<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.job API documentation</title>
<meta name="description" content="Main file for performing operations on a job …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.job</code></h1>
</header>
<section id="section-intro">
<p>Main file for performing operations on a job.</p>
<p>JobController:
Class for managing jobs on this commcell</p>
<p>JobManagement:
Class for performing Job Management operations</p>
<p>Job:
Class for keeping track of a job and perform various operations on it.</p>
<h1 id="jobcontroller">JobController</h1>
<pre><code>__init__(commcell_object)   --  initializes the instance of JobController class associated
with the specified commcell

__str__()                   --  returns the string representation of the active jobs
on this commcell

__repr__()                  --  returns the string representation of the object of this class,
with the commcell it is associated with

_get_jobs_list()            --  executes the request, and parses and returns the jobs response

_get_jobs_request_json(**options)
                            --  Returns the request json for the jobs request

_modify_all_jobs(operation_type=None)
                            --  executes a request on the server to suspend/resume/kill all
                                    the jobs on the commserver.

all_jobs()                  --  returns all the jobs on this commcell

active_jobs()               --  returns the dict of active jobs and their details

finished_jobs()             --  retutns the dict of finished jobs and their details

get()                       --  returns the Job class instance for the given job id

kill_all_jobs()             -- Kills all jobs on the commcell

resume_all_jobs()           -- Resumes all jobs on the commcell

suspend_all_jobs()          -- Suspends all jobs on the commcell
</code></pre>
<h1 id="jobmanagement">JobManagement</h1>
<pre><code>__init__(commcell_object)                                       --  initialise object of the JobManagement class

_set_jobmanagement_settings()                                   --  sets the jobmanagement settings

_refresh()                                                      --  refresh the job management settings

 set_general_settings(settings)                                 --  sets the general settings of job management

 set_priority_settings(settings)                                --  sets the priority settings of job management

 set_restart_settings(settings)                                 --  sets the restart settings of job management

 set_update_settings(settings)                                  --  sets the update settings of job management

 job_priority_precedence                                        --  gets the job priority precedence

 job_priority_precedence(priority_type)                         --  sets the job priority precedence property

 start_phase_retry_interval                                     --  gets the start phase retry interval in
                                                                    (minutes)

 start_phase_retry_interval(minutes)                            --  sets the start phase retry interval property

 state_update_interval_for_continuous_data_replicator           --  gets the start phase retry interval in
                                                                    (minutes)

 state_update_interval_for_continuous_data_replicator(minutes)  --  sets the state update interval for continuous
                                                                    data replicator

 allow_running_jobs_to_complete_past_operation_window           --  gets the allow running jobs to complete past
                                                                    operation window(True/False)

 allow_running_jobs_to_complete_past_operation_window(flag)     --  sets the allow running jobs to complete past
                                                                    operation window

 job_alive_check_interval_in_minutes                            --  gets the job alive check interval in (minutes)

 job_alive_check_interval_in_minutes(minutes)                   --  sets the job alive check interval in minutes

 queue_scheduled_jobs                                           --  gets the queue scheduled jobs(True/False)

 queue_scheduled_jobs(flags)                                    --  sets the queue scheduled jobs

 enable_job_throttle_at_client_level                            --  gets the enable job throttle at client level
                                                                    (True/False)

 enable_job_throttle_at_client_level(flag)                      --  sets the enable job throttle at client level

 enable_multiplexing_for_db_agents                              --  gets the enable multiplexing for db agents
                                                                    (True/False)

 enable_multiplexing_for_db_agents(flag)                        --  sets the enable multiplexing for db agents

 queue_jobs_if_conflicting_jobs_active                          --  gets the queue jobs if conflicting jobs active
                                                                    (True/False)

 queue_jobs_if_conflicting_jobs_active(flag)                    --  sets the queue jobs if conflicting jobs active

 queue_jobs_if_activity_disabled                                --  gets the queue jobs if activity disabled
                                                                    (True/False)

 queue_jobs_if_activity_disabled(flag)                          --  sets the queue jobs if activity disabled

 backups_preempts_auxilary_copy                                 --  gets the backups preempts auxilary copy
                                                                    (True/False)

 backups_preempts_auxilary_copy(flag)                           --  sets the backups preempts auxilary copy

 restore_preempts_other_jobs                                    --  gets the restore preempts other jobs
                                                                    (True/False)

 restore_preempts_other_jobs(flag)                               --  sets the restore preempts other jobs

 enable_multiplexing_for_oracle                                  --  gets the enable multiplexing for oracle
                                                                    (True/False)

 enable_multiplexing_for_oracle(flag)                            --  sets the enable multiplexing for oracle

 job_stream_high_water_mark_level                                --  gets the job stream high water mark level

 job_stream_high_water_mark_level(level)                         --  sets the job stream high water mark level

 backups_preempts_other_backups                                  --  gets the backups preempts other backups
                                                                    (True/False)

 backups_preempts_other_backups(flag)                            --  sets the backups preempts other backups

 do_not_start_backups_on_disabled_client                         --  gets the do not start backups on
                                                                     disabled client(True/False)

 do_not_start_backups_on_disabled_client(flag)                   --  sets the do not start backups
                                                                     on disabled client

 get_restart_setting(jobtype)                                    --  gets the restart settings of a specific
                                                                     jobtype

 get_priority_setting(jobtype)                                   --  gets the priority setting of a specific
                                                                     jobtype

 get_update_setting(jobtype)                                     --   gets the update settings of a specific
                                                                      jobtype

 get_restart_settings                                            --  gets the restart settings of job management

 get_priority_settings                                           --  gets the priority settings of job management

 get_update_settings                                             --  gets the update settings of job management
</code></pre>
<h1 id="job">Job</h1>
<pre><code>__init__()                  --  initializes the instance of Job class associated with the
specified commcell of job with id: 'job_id'

__repr__()                  --  returns the string representation of the object of this class,
with the job id it is associated with

_is_valid_job()             --  checks if the job with the given id is a valid job or not

_get_job_summary()          --  gets the summary of the job with the given job id

_get_job_details()          --  gets the details of the job with the given job id

_initialize_job_properties()--  initializes the properties of the job

_wait_for_status()          --  waits for 6 minutes or till the job status is changed
to given status, whichever is earlier

wait_for_completion()       --  waits for the job to finish, (job.is_finished == True)

is_finished()               --  checks for the status of the job.

                                    Returns True if finished, else False

pause()                     --  suspend the job

resume()                    --  resumes the job

resubmit()                  --  to resubmit the job

kill()                      --  kills the job

refresh()                   --  refresh the properties of the Job

advanced_job_details()      --  Returns advanced properties for the job

get_events()                --  returns the commserv events for the job

get_child_jobs()            --  Returns the child jobs
</code></pre>
<h2 id="job-instance-attributes">Job Instance Attributes</h2>
<p><strong>job.is_finished</strong>
&ndash;
specifies whether the job is finished or not
(True / False)</p>
<p><strong>job.client_name</strong>
&ndash;
returns the name of the client, job is running for</p>
<p><strong>job.agent_name</strong>
&ndash;
returns the name of the agent, job is running for</p>
<p><strong>job.instance_name</strong>
&ndash;
returns the name of the instance, job is running for</p>
<p><strong>job.backupset_name</strong>
&ndash;
returns the name of the backupset, job is running for</p>
<p><strong>job.subclient_name</strong>
&ndash;
returns the name of the subclient, job is running for</p>
<p><strong>job.status</strong>
&ndash;
returns the current status of the job
(Completed / Suspended / Waiting / &hellip; / etc.)
<a href="http://documentation.commvault.com/commvault/v11/article?p=features/rest_api/operations/get_job.htm">http://documentation.commvault.com/commvault/v11/article?p=features/rest_api/operations/get_job.htm</a>
please refer status section in above doc link for complete list of status available</p>
<p><strong>job.job_id</strong>
&ndash;
returns the id of the job</p>
<p><strong>job.job_type</strong>
&ndash;
returns the type of the job</p>
<p><strong>job.backup_level</strong>
&ndash;
returns the backup level (if applicable), otherwise None</p>
<p><strong>job.start_time</strong>
&ndash;
returns the start time of the job</p>
<p><strong>job.end_time</strong>
&ndash;
returns the end time of the job</p>
<p><strong>job.delay_reason</strong>
&ndash;
reason why the job was delayed</p>
<p><strong>job.pending_reason</strong>
&ndash;
reason if job went into pending state</p>
<p><strong>job.phase</strong>
&ndash;
returns the current phase of the job</p>
<p><strong>job.summary</strong>
&ndash;
returns the dictionary consisting of the full summary of the job</p>
<p><strong>job.attempts</strong>
&ndash;
returns the dictionary consisting of the attempt details of the job</p>
<p><strong>job.username</strong>
&ndash;
returns the username with which the job started</p>
<p><strong>job.userid</strong>
&ndash;
returns the userid with which the job started</p>
<p><strong>job.details</strong>
&ndash;
returns the dictionary consisting of the full details of the job</p>
<p><strong>job.num_of_files_transferred</strong>
&ndash; returns the current number of files transferred for the job.</p>
<p><strong>job.state</strong>
&ndash; returns the current state of the job.</p>
<h1 id="errorrule">ErrorRule</h1>
<pre><code>_get_xml_for_rule()             --  Returns the XML for a given rule's dictionary of key value pairs.

add_error_rule()                --  Add new error rules as well as update existing rules.

_modify_job_status_on_errors()  --  Internally used to enable or disable job status on errors.

enable()                        --  Enable an error rule for a specific iDA using _modify_job_status_on_errors.

disable()                       --  Disable an error rule for a specific iDA using _modify_job_status_on_errors.
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L1-L3093" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-
# pylint: disable=W0104, R0205, R1710

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Main file for performing operations on a job.

JobController:  Class for managing jobs on this commcell

JobManagement:  Class for performing Job Management operations

Job:            Class for keeping track of a job and perform various operations on it.


JobController
=============

    __init__(commcell_object)   --  initializes the instance of JobController class associated
    with the specified commcell

    __str__()                   --  returns the string representation of the active jobs
    on this commcell

    __repr__()                  --  returns the string representation of the object of this class,
    with the commcell it is associated with

    _get_jobs_list()            --  executes the request, and parses and returns the jobs response

    _get_jobs_request_json(**options)
                                --  Returns the request json for the jobs request

    _modify_all_jobs(operation_type=None)
                                --  executes a request on the server to suspend/resume/kill all
                                        the jobs on the commserver.

    all_jobs()                  --  returns all the jobs on this commcell

    active_jobs()               --  returns the dict of active jobs and their details

    finished_jobs()             --  retutns the dict of finished jobs and their details

    get()                       --  returns the Job class instance for the given job id

    kill_all_jobs()             -- Kills all jobs on the commcell

    resume_all_jobs()           -- Resumes all jobs on the commcell

    suspend_all_jobs()          -- Suspends all jobs on the commcell


JobManagement
==============

    __init__(commcell_object)                                       --  initialise object of the JobManagement class

    _set_jobmanagement_settings()                                   --  sets the jobmanagement settings

    _refresh()                                                      --  refresh the job management settings

     set_general_settings(settings)                                 --  sets the general settings of job management

     set_priority_settings(settings)                                --  sets the priority settings of job management

     set_restart_settings(settings)                                 --  sets the restart settings of job management

     set_update_settings(settings)                                  --  sets the update settings of job management

     job_priority_precedence                                        --  gets the job priority precedence

     job_priority_precedence(priority_type)                         --  sets the job priority precedence property

     start_phase_retry_interval                                     --  gets the start phase retry interval in
                                                                        (minutes)

     start_phase_retry_interval(minutes)                            --  sets the start phase retry interval property

     state_update_interval_for_continuous_data_replicator           --  gets the start phase retry interval in
                                                                        (minutes)

     state_update_interval_for_continuous_data_replicator(minutes)  --  sets the state update interval for continuous
                                                                        data replicator

     allow_running_jobs_to_complete_past_operation_window           --  gets the allow running jobs to complete past
                                                                        operation window(True/False)

     allow_running_jobs_to_complete_past_operation_window(flag)     --  sets the allow running jobs to complete past
                                                                        operation window

     job_alive_check_interval_in_minutes                            --  gets the job alive check interval in (minutes)

     job_alive_check_interval_in_minutes(minutes)                   --  sets the job alive check interval in minutes

     queue_scheduled_jobs                                           --  gets the queue scheduled jobs(True/False)

     queue_scheduled_jobs(flags)                                    --  sets the queue scheduled jobs

     enable_job_throttle_at_client_level                            --  gets the enable job throttle at client level
                                                                        (True/False)

     enable_job_throttle_at_client_level(flag)                      --  sets the enable job throttle at client level

     enable_multiplexing_for_db_agents                              --  gets the enable multiplexing for db agents
                                                                        (True/False)

     enable_multiplexing_for_db_agents(flag)                        --  sets the enable multiplexing for db agents

     queue_jobs_if_conflicting_jobs_active                          --  gets the queue jobs if conflicting jobs active
                                                                        (True/False)

     queue_jobs_if_conflicting_jobs_active(flag)                    --  sets the queue jobs if conflicting jobs active

     queue_jobs_if_activity_disabled                                --  gets the queue jobs if activity disabled
                                                                        (True/False)

     queue_jobs_if_activity_disabled(flag)                          --  sets the queue jobs if activity disabled

     backups_preempts_auxilary_copy                                 --  gets the backups preempts auxilary copy
                                                                        (True/False)

     backups_preempts_auxilary_copy(flag)                           --  sets the backups preempts auxilary copy

     restore_preempts_other_jobs                                    --  gets the restore preempts other jobs
                                                                        (True/False)

     restore_preempts_other_jobs(flag)                               --  sets the restore preempts other jobs

     enable_multiplexing_for_oracle                                  --  gets the enable multiplexing for oracle
                                                                        (True/False)

     enable_multiplexing_for_oracle(flag)                            --  sets the enable multiplexing for oracle

     job_stream_high_water_mark_level                                --  gets the job stream high water mark level

     job_stream_high_water_mark_level(level)                         --  sets the job stream high water mark level

     backups_preempts_other_backups                                  --  gets the backups preempts other backups
                                                                        (True/False)

     backups_preempts_other_backups(flag)                            --  sets the backups preempts other backups

     do_not_start_backups_on_disabled_client                         --  gets the do not start backups on
                                                                         disabled client(True/False)

     do_not_start_backups_on_disabled_client(flag)                   --  sets the do not start backups
                                                                         on disabled client

     get_restart_setting(jobtype)                                    --  gets the restart settings of a specific
                                                                         jobtype

     get_priority_setting(jobtype)                                   --  gets the priority setting of a specific
                                                                         jobtype

     get_update_setting(jobtype)                                     --   gets the update settings of a specific
                                                                          jobtype

     get_restart_settings                                            --  gets the restart settings of job management

     get_priority_settings                                           --  gets the priority settings of job management

     get_update_settings                                             --  gets the update settings of job management


Job
===

    __init__()                  --  initializes the instance of Job class associated with the
    specified commcell of job with id: &#39;job_id&#39;

    __repr__()                  --  returns the string representation of the object of this class,
    with the job id it is associated with

    _is_valid_job()             --  checks if the job with the given id is a valid job or not

    _get_job_summary()          --  gets the summary of the job with the given job id

    _get_job_details()          --  gets the details of the job with the given job id

    _initialize_job_properties()--  initializes the properties of the job

    _wait_for_status()          --  waits for 6 minutes or till the job status is changed
    to given status, whichever is earlier

    wait_for_completion()       --  waits for the job to finish, (job.is_finished == True)

    is_finished()               --  checks for the status of the job.

                                        Returns True if finished, else False

    pause()                     --  suspend the job

    resume()                    --  resumes the job

    resubmit()                  --  to resubmit the job

    kill()                      --  kills the job

    refresh()                   --  refresh the properties of the Job

    advanced_job_details()      --  Returns advanced properties for the job

    get_events()                --  returns the commserv events for the job

    get_child_jobs()            --  Returns the child jobs


Job instance Attributes
-----------------------

**job.is_finished**                 --  specifies whether the job is finished or not    (True / False)

**job.client_name**                 --  returns the name of the client, job is running for

**job.agent_name**                  --  returns the name of the agent, job is running for

**job.instance_name**               --  returns the name of the instance, job is running for

**job.backupset_name**              --  returns the name of the backupset, job is running for

**job.subclient_name**              --  returns the name of the subclient, job is running for

**job.status**                      --  returns the current status of the job
                                        (Completed / Suspended / Waiting / ... / etc.)
        http://documentation.commvault.com/commvault/v11/article?p=features/rest_api/operations/get_job.htm
        please refer status section in above doc link for complete list of status available

**job.job_id**                      --  returns the id of the job

**job.job_type**                    --  returns the type of the job

**job.backup_level**                --  returns the backup level (if applicable), otherwise None

**job.start_time**                  --  returns the start time of the job

**job.end_time**                    --  returns the end time of the job

**job.delay_reason**                --  reason why the job was delayed

**job.pending_reason**              --  reason if job went into pending state

**job.phase**                       --  returns the current phase of the job

**job.summary**                     --  returns the dictionary consisting of the full summary of the job

**job.attempts**                    --  returns the dictionary consisting of the attempt details of the job

**job.username**                    --  returns the username with which the job started

**job.userid**                      --  returns the userid with which the job started

**job.details**                     --  returns the dictionary consisting of the full details of the job

**job.num_of_files_transferred**    -- returns the current number of files transferred for the job.

**job.state**                       -- returns the current state of the job.

ErrorRule
=========

    _get_xml_for_rule()             --  Returns the XML for a given rule&#39;s dictionary of key value pairs.

    add_error_rule()                --  Add new error rules as well as update existing rules.

    _modify_job_status_on_errors()  --  Internally used to enable or disable job status on errors.

    enable()                        --  Enable an error rule for a specific iDA using _modify_job_status_on_errors.

    disable()                       --  Disable an error rule for a specific iDA using _modify_job_status_on_errors.

&#34;&#34;&#34;

from __future__ import absolute_import
from __future__ import unicode_literals

import time
import copy

from .exception import SDKException
from .constants import AdvancedJobDetailType, ApplicationGroup


class JobController(object):
    &#34;&#34;&#34;Class for controlling all the jobs associated with the commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize instance of the JobController class to get the details of Commcell Jobs.

            Args:
                commcell_object     (object)    --  instance of Commcell class to get the jobs of

            Returns:
                None

        &#34;&#34;&#34;
        self._commcell_object = commcell_object

        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all active jobs on this commcell.

            Returns:
                str     -   string of all the active jobs on this commcell

        &#34;&#34;&#34;
        jobs_dict = self.active_jobs()

        representation_string = &#39;{:^5}\t{:^25}\t{:^20}\t{:^20}\t{:^20}\t{:^20}\t{:^20}\n\n&#39;.format(
            &#39;Job ID&#39;, &#39;Operation&#39;, &#39;Status&#39;, &#39;Agent type&#39;, &#39;Job type&#39;, &#39;Progress&#39;, &#39;Pending Reason&#39;
        )

        for job in jobs_dict:
            sub_str = &#39;{:^5}\t{:25}\t{:20}\t{:20}\t{:20}\t{:20}%\t{:^20}\n&#39;.format(
                job,
                jobs_dict[job][&#39;operation&#39;],
                jobs_dict[job][&#39;status&#39;],
                jobs_dict[job][&#39;app_type&#39;],
                jobs_dict[job][&#39;job_type&#39;],
                jobs_dict[job][&#39;percent_complete&#39;],
                jobs_dict[job][&#39;pending_reason&#39;]
            )
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the JobController class.&#34;&#34;&#34;
        return &#34;JobController class instance for Commcell&#34;

    def _get_jobs_request_json(self, **options):
        &#34;&#34;&#34;Returns the request json for the jobs request

            Args:
                options     (dict)  --  dict of key-word arguments

                Available Options:

                    category        (str)   --  category name for which the list of jobs
                    are to be retrieved

                        Valid Values:

                            - ALL

                            - ACTIVE

                            - FINISHED

                        default: ALL

                    limit           (int)   --  total number of jobs list that are to be returned

                            default: 20

                    offset           (int)  --  value from which starting job to be returned is counted

                            default: 0

                    lookup_time     (int)   --  list of jobs to be retrieved which are specified
                    hours older

                            default: 5 hours

                    show_aged_job   (bool)  --  boolean specifying whether to include aged jobs in
                    the result or not

                            default: False
                    
                    hide_admin_jobs (bool)  --  boolean specifying whether to exclude admin jobs from
                    the result or not

                            default: False

                    clients_list    (list)  --  list of clients to return the jobs for

                            default: []

                    job_type_list   (list)  --  list of job operation types

                            default: []

                    entity          (dict)  --  dict containing entity details to which associated jobs has to be fetched

                            Example : To fetch job details of particular data source id

                                &#34;entity&#34;: {
                                            &#34;dataSourceId&#34;: 2575
                                            }


            Returns:
                dict    -   request json that is to be sent to server

        &#34;&#34;&#34;
        job_list_category = {
            &#39;ALL&#39;: 0,
            &#39;ACTIVE&#39;: 1,
            &#39;FINISHED&#39;: 2
        }

        for client in options.get(&#39;clients_list&#39;, []):
            if not self._commcell_object.clients.has_client(client):
                raise SDKException(&#39;Job&#39;, &#39;102&#39;, &#39;No client with name {0} exists.&#39;.format(client))

        client_list = []
        for client in options.get(&#39;clients_list&#39;, []):
            try:
                _client_id = int(self._commcell_object.clients.all_clients[client.lower()][&#39;id&#39;])
            except KeyError:
                _client_id = int(self._commcell_object.clients.hidden_clients[client.lower()][&#39;id&#39;])
            client_list.append({&#34;clientId&#34;: _client_id})

        request_json = {
            &#34;scope&#34;: 1,
            &#34;category&#34;: job_list_category[options.get(&#39;category&#39;, &#39;ALL&#39;)],
            &#34;pagingConfig&#34;: {
                &#34;sortDirection&#34;: 1,
                &#34;offset&#34;: options.get(&#39;offset&#39;, 0),
                &#34;sortField&#34;: &#34;jobId&#34;,
                &#34;limit&#34;: options.get(&#39;limit&#39;, 20)
            },
            &#34;jobFilter&#34;: {
                &#34;completedJobLookupTime&#34;: int(options.get(&#39;lookup_time&#39;, 5) * 60 * 60),
                &#34;showAgedJobs&#34;: options.get(&#39;show_aged_jobs&#39;, False),
                &#34;hideAdminJobs&#34;: options.get(&#39;hide_admin_jobs&#39;, False),
                &#34;clientList&#34;: client_list,
                &#34;jobTypeList&#34;: [
                    job_type for job_type in options.get(&#39;job_type_list&#39;, [])
                ]
            }
        }

        if &#34;entity&#34; in options:
            request_json[&#39;jobFilter&#39;][&#39;entity&#39;] = options.get(&#34;entity&#34;)

        return request_json

    def _get_jobs_list(self, **options):
        &#34;&#34;&#34;Executes a request on the server to get the list of jobs.

            Args:
                request_json    (dict)  --  request that is to be sent to server

            Returns:
                dict    -   dict containing details about all the retrieved jobs

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        request_json = self._get_jobs_request_json(**options)

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;ALL_JOBS&#39;], request_json
        )

        jobs_dict = {}

        if flag:
            try:
                if response.json():
                    if &#39;jobs&#39; in response.json():
                        all_jobs = response.json()[&#39;jobs&#39;]

                        for job in all_jobs:
                            if &#39;jobSummary&#39; in job and job[&#39;jobSummary&#39;][&#39;isVisible&#39;] is True:

                                job_summary = job[&#39;jobSummary&#39;]
                                job_id = job_summary[&#39;jobId&#39;]

                                if options.get(&#39;job_summary&#39;, &#39;&#39;).lower() == &#39;full&#39;:
                                    jobs_dict[job_id] = job_summary
                                else:
                                    status = job_summary[&#39;status&#39;]
                                    operation = job_summary.get(&#39;localizedOperationName&#39;, &#39;&#39;)
                                    percent_complete = job_summary[&#39;percentComplete&#39;]
                                    backup_level = job_summary.get(&#39;backupLevelName&#39;)

                                    app_type = &#39;&#39;
                                    job_type = &#39;&#39;
                                    pending_reason = &#39;&#39;
                                    subclient_id = &#39;&#39;
                                    client_id = &#39;&#39;
                                    client_name = &#39;&#39;
                                    job_elapsed_time = 0
                                    job_start_time = 0

                                    if &#39;jobElapsedTime&#39; in job_summary:
                                        job_elapsed_time = job_summary[&#39;jobElapsedTime&#39;]

                                    if &#39;jobStartTime&#39; in job_summary:
                                        job_start_time = job_summary[&#39;jobStartTime&#39;]

                                    if &#39;appTypeName&#39; in job_summary:
                                        app_type = job_summary[&#39;appTypeName&#39;]

                                    if &#39;jobType&#39; in job_summary:
                                        job_type = job_summary[&#39;jobType&#39;]

                                    if &#39;pendingReason&#39; in job_summary:
                                        pending_reason = job_summary[&#39;pendingReason&#39;]

                                    if &#39;subclient&#39; in job_summary:
                                        job_subclient = job_summary[&#39;subclient&#39;]
                                        if &#39;subclientId&#39; in job_subclient:
                                            subclient_id = job_subclient[&#39;subclientId&#39;]
                                        if &#39;clientId&#39; in job_subclient:
                                            client_id = job_subclient[&#39;clientId&#39;]
                                        if &#39;clientName&#39; in job_subclient:
                                            client_name = job_subclient[&#39;clientName&#39;]

                                    jobs_dict[job_id] = {
                                        &#39;operation&#39;: operation,
                                        &#39;status&#39;: status,
                                        &#39;app_type&#39;: app_type,
                                        &#39;job_type&#39;: job_type,
                                        &#39;percent_complete&#39;: percent_complete,
                                        &#39;pending_reason&#39;: pending_reason,
                                        &#39;client_id&#39;: client_id,
                                        &#39;client_name&#39;: client_name,
                                        &#39;subclient_id&#39;: subclient_id,
                                        &#39;backup_level&#39;: backup_level,
                                        &#39;job_start_time&#39;: job_start_time,
                                        &#39;job_elapsed_time&#39;: job_elapsed_time

                                    }

                    return jobs_dict

                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)

            except ValueError:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;, &#39;Please check the inputs.&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _modify_all_jobs(self, operation_type=None):
        &#34;&#34;&#34; Executes a request on the server to suspend/resume/kill all the jobs on the commserver

            Args:
                operation_type     (str)   --  All jobs on commcell will be changed to this
                                                    state.
                                                    Options:
                                                        suspend/resume/kill

            Returns:
                None

            Raises:
                SDKException:
                    - Invalid input is passed to the module

                    - Failed to execute the api to modify jobs

                    - Response is incorrect
        &#34;&#34;&#34;

        job_map = {
            &#39;suspend&#39;: &#39;JOB_SUSPEND&#39;,
            &#39;resume&#39;: &#39;JOB_RESUME&#39;,
            &#39;kill&#39;: &#39;JOB_KILL&#39;
        }

        if operation_type not in job_map:
            raise SDKException(&#39;Job&#39;, &#39;102&#39;, &#39;Invalid input&#39;)

        request_json = {
            &#34;JobManager_PerformMultiCellJobOpReq&#34;: {
                &#34;jobOpReq&#34;: {
                    &#34;operationType&#34;: job_map[operation_type]
                },
                &#34;message&#34;: &#34;ALL_JOBS&#34;,
                &#34;operationDescription&#34;: &#34;All jobs&#34;
            }
        }

        response = self._commcell_object._qoperation_execute(request_json)

        if &#39;error&#39; in response:
            error_code = response[&#39;error&#39;].get(&#39;errorCode&#39;)
            if error_code != 0:
                if &#39;errLogMessage&#39; in response[&#39;error&#39;]:
                    error_message = &#34;Failed to {0} all jobs with error: [{1}]&#34;.format(
                        operation_type, response[&#39;error&#39;][&#39;errLogMessage&#39;]
                    )

                    raise SDKException(
                        &#39;Job&#39;,
                        &#39;102&#39;,
                        &#39;Error Code:&#34;{0}&#34;\nError Message: &#34;{1}&#34;&#39;.format(error_code, error_message)
                    )
                else:
                    raise SDKException(&#39;Job&#39;,
                                       &#39;102&#39;,
                                       &#34;Failed to {0} all jobs&#34;.format(operation_type))
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

    def all_jobs(self, client_name=None, lookup_time=5, job_filter=None, **options):
        &#34;&#34;&#34;Returns the dict consisting of all the jobs executed on the Commcell within the number
            of hours specified in lookup time value.

            Args:
                client_name     (str)   --  name of the client to filter out the jobs for

                    default: None, get all the jobs


                lookup_time     (int)   --  get all the jobs executed within the number of hours

                    default: 5 Hours


                job_filter      (str)   --  type of jobs to filter

                        for multiple filters, give the values **comma(,)** separated

                        List of Possible Values:

                            Backup

                            Restore

                            AUXCOPY

                            WORKFLOW

                            etc..

                    http://documentation.commvault.com/commvault/v11/article?p=features/rest_api/operations/get_job.htm
                        to get the complete list of filters available

                    default: None

                options         (dict)  --  dict of key-word arguments

                Available Options:

                    limit           (int)   --  total number of jobs list that are to be returned
                        default: 20

                    offset           (int)  --  value from which starting job to be returned is counted

                        default: 0

                    show_aged_job   (bool)  --  boolean specifying whether to include aged jobs in
                    the result or not

                        default: False

                    hide_admin_jobs (bool)  --  boolean specifying whether to exclude admin jobs from
                    the result or not

                        default: False

                    clients_list    (list)  --  list of clients to return the jobs for

                        default: []

                    job_type_list   (list)  --  list of job operation types

                        default: []

                    job_summary     (str)   --  To return the basic job summary or full job summary

                        default: basic

                        accepted values: [&#39;basic&#39;, &#39;full&#39;]

            Returns:
                dict    -   dictionary consisting of the job IDs matching the given criteria
                as the key, and their details as its value

            Raises:
                SDKException:
                    if client name is given, and no client exists with the given name

        &#34;&#34;&#34;
        options[&#39;category&#39;] = &#39;ALL&#39;
        options[&#39;lookup_time&#39;] = lookup_time

        if job_filter:
            options[&#39;job_type_list&#39;] = options.get(&#39;job_type_list&#39;, []) + job_filter.split(&#39;,&#39;)

        if client_name:
            options[&#39;clients_list&#39;] = options.get(&#39;clients_list&#39;, []) + [client_name]

        return self._get_jobs_list(**options)

    def active_jobs(self, client_name=None, lookup_time=1, job_filter=None, **options):
        &#34;&#34;&#34;Returns the dict consisting of all the active jobs currently being executed on the
            Commcell within the number of hours specified in lookup time value.

            Args:
                client_name     (str)   --  name of the client to filter out the jobs for

                    default: None, get all the jobs


                lookup_time     (int)   --  get all the jobs executed within the number of hours

                    default: 1 Hour(s)


                job_filter      (str)   --  type of jobs to filter

                        for multiple filters, give the values **comma(,)** separated

                        List of Possible Values:

                            Backup

                            Restore

                            AUXCOPY

                            WORKFLOW

                            etc..

                    http://documentation.commvault.com/commvault/v11/article?p=features/rest_api/operations/get_job.htm
                        to get the complete list of filters available

                    default: None

                options         (dict)  --  dict of key-word arguments

                Available Options:

                    limit           (int)   --  total number of jobs list that are to be returned

                        default: 20

                    offset          (int)   --  value from which starting job to be returned is counted

                        default: 0

                    show_aged_job   (bool)  --  boolean specifying whether to include aged jobs in
                    the result or not

                        default: False

                    hide_admin_jobs (bool)  --  boolean specifying whether to exclude admin jobs from
                    the result or not

                        default: False

                    clients_list    (list)  --  list of clients to return the jobs for

                        default: []

                    job_type_list   (list)  --  list of job operation types

                        default: []

                    job_summary     (str)   --  To return the basic job summary or full job summary

                        default: basic

                        accepted values: [&#39;basic&#39;, &#39;full&#39;]

                    entity          (dict)  --  dict containing entity details to which associated jobs has to be fetched

                        Example : To fetch job details of particular data source id

                                &#34;entity&#34;: {
                                            &#34;dataSourceId&#34;: 2575
                                            }

            Returns:
                dict    -   dictionary consisting of the job IDs matching the given criteria
                as the key, and their details as its value

            Raises:
                SDKException:
                    if client name is given, and no client exists with the given name

        &#34;&#34;&#34;
        options[&#39;category&#39;] = &#39;ACTIVE&#39;
        options[&#39;lookup_time&#39;] = lookup_time

        if job_filter:
            options[&#39;job_type_list&#39;] = options.get(&#39;job_type_list&#39;, []) + job_filter.split(&#39;,&#39;)

        if client_name:
            options[&#39;clients_list&#39;] = options.get(&#39;clients_list&#39;, []) + [client_name]

        return self._get_jobs_list(**options)

    def finished_jobs(self, client_name=None, lookup_time=24, job_filter=None, **options):
        &#34;&#34;&#34;Returns the dict consisting of all the finished jobs on the Commcell within the number
            of hours specified in lookup time value.

            Args:
                client_name     (str)   --  name of the client to filter out the jobs for

                    default: None, get all the jobs ir-respective of client


                lookup_time     (int)   --  get all the jobs executed within the number of hours

                    default: 24 Hours


                job_filter      (str)   --  type of jobs to filter

                        for multiple filters, give the values **comma(,)** separated

                        List of Possible Values:

                            Backup

                            Restore

                            AUXCOPY

                            WORKFLOW

                            etc..

                    http://documentation.commvault.com/commvault/v11/article?p=features/rest_api/operations/get_job.htm
                        to get the complete list of filters available

                    default: None


                options         (dict)  --  dict of key-word arguments

                Available Options:

                    limit           (int)   --  total number of jobs list that are to be returned

                        default: 20

                    offset          (int)   --  value from which starting job to be returned is counted

                            default: 0

                    show_aged_job   (bool)  --  boolean specifying whether to include aged jobs in
                    the result or not

                        default: False

                    hide_admin_jobs (bool)  --  boolean specifying whether to exclude admin jobs from
                    the result or not

                        default: False

                    clients_list    (list)  --  list of clients to return the jobs for

                        default: []

                    job_type_list   (list)  --  list of job operation types

                        default: []

                    job_summary     (str)   --  To return the basic job summary or full job summary

                        default: basic

                        accepted values: [&#39;basic&#39;, &#39;full&#39;]

                    entity          (dict)  --  dict containing entity details to which associated jobs has to be fetched

                        Example : To fetch job details of particular data source id

                                &#34;entity&#34;: {
                                            &#34;dataSourceId&#34;: 2575
                                            }

            Returns:
                dict    -   dictionary consisting of the job IDs matching the given criteria
                as the key, and their details as its value

            Raises:
                SDKException:
                    if client name is given, and no client exists with the given name

        &#34;&#34;&#34;
        options[&#39;category&#39;] = &#39;FINISHED&#39;
        options[&#39;lookup_time&#39;] = lookup_time

        if job_filter:
            options[&#39;job_type_list&#39;] = options.get(&#39;job_type_list&#39;, []) + job_filter.split(&#39;,&#39;)

        if client_name:
            options[&#39;clients_list&#39;] = options.get(&#39;clients_list&#39;, []) + [client_name]

        return self._get_jobs_list(**options)

    def suspend_all_jobs(self):
        &#34;&#34;&#34; Suspends all the jobs on the commserver &#34;&#34;&#34;
        self._modify_all_jobs(&#39;suspend&#39;)

    def resume_all_jobs(self):
        &#34;&#34;&#34; Resumes all the jobs on the commserver &#34;&#34;&#34;
        self._modify_all_jobs(&#39;resume&#39;)

    def kill_all_jobs(self):
        &#34;&#34;&#34; Kills all the jobs on the commserver &#34;&#34;&#34;
        self._modify_all_jobs(&#39;kill&#39;)

    def get(self, job_id):
        &#34;&#34;&#34;Returns the job object for the given job id.

            Args:
                job_id  (int)   --  id of the job to create Job class instance for

            Returns:
                object  -   Job class object for the given job id

            Raises:
                SDKException:
                    if no job with specified job id exists

        &#34;&#34;&#34;
        return Job(self._commcell_object, job_id)


class JobManagement(object):
    &#34;&#34;&#34;Class for performing job management operations. &#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;
        Initialize instance of JobManagement class for performing operations on jon management settings.

            Args:
                commcell_object         (object)        --  instance of Commcell class.

            Returns:
                None

        &#34;&#34;&#34;
        self._comcell = commcell_object
        self._service = commcell_object._services.get(&#39;JOB_MANAGEMENT_SETTINGS&#39;)
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._error_rules = None
        self.refresh()

    @property
    def error_rules(self):
        if not self._error_rules:
            self._error_rules = _ErrorRule(self._comcell)
        return self._error_rules

    def _set_jobmanagement_settings(self):
        &#34;&#34;&#34;
        Executes a request on the server, to set the job management settings.

         Returns:
               None

         Raises:
              SDKException:
                    if given inputs are invalid

        &#34;&#34;&#34;

        flag, response = self._cvpysdk_object.make_request(method=&#39;POST&#39;, url=self._service,
                                                           payload=self._settings_dict)
        if flag:
            if response and response.json():
                if response.json().get(&#39;errorCode&#39;, 0) != 0:
                    raise SDKException(&#39;Job&#39;, &#39;102&#39;, &#39;Failed to set job management properties. \nError: {0}&#39;.format(
                        response.json().get(&#39;errorMessage&#39;, &#39;&#39;)))
                self.refresh()
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response.json()[&#34;errorMessage&#34;])

    def _get_jobmanagement_settings(self):
        &#34;&#34;&#34;
         Executes a request on the server to get the settings of job management.

            Returns:
                None

            Raises:
                SDKException
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(method=&#39;GET&#39;, url=self._service)
        if flag:
            if response and response.json():
                self._settings_dict = response.json()
                if self._settings_dict.get(&#39;errorCode&#39;, 0) != 0:
                    raise SDKException(&#39;Job&#39;, &#39;102&#39;, &#39;Failed to get job management properties. \nError: {0}&#39;.format(
                        self._settings_dict.get(&#39;errorMessage&#39;, &#39;&#39;)))
                if &#39;jobManagementSettings&#39; in self._settings_dict:
                    self._restart_settings = {&#39;jobRestartSettings&#39;: self._settings_dict.get(
                        &#39;jobManagementSettings&#39;).get(&#39;jobRestartSettings&#39;, {})}
                    self._priority_settings = {&#39;jobPrioritySettings&#39;: self._settings_dict.get(
                        &#39;jobManagementSettings&#39;).get(&#39;jobPrioritySettings&#39;, {})}
                    self._general_settings = {&#39;generalSettings&#39;: self._settings_dict.get(
                        &#39;jobManagementSettings&#39;).get(&#39;generalSettings&#39;, {})}
                    self._update_settings = {&#39;jobUpdatesSettings&#39;: self._settings_dict.get(
                        &#39;jobManagementSettings&#39;).get(&#39;jobUpdatesSettings&#39;, {})}
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._comcell._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def refresh(self):
        &#34;&#34;&#34;
        calls the private method _get_jobmanagement_settings()
        &#34;&#34;&#34;
        self._restart_settings = None
        self._general_settings = None
        self._update_settings = None
        self._priority_settings = None
        self._get_jobmanagement_settings()

    def set_general_settings(self, settings):
        &#34;&#34;&#34;
        sets general settings of job management.

        Note : dedicated setters and getters are provided for general settings.
            Args:
                settings (dict)  --       Following key/value pairs can be set.
                                            {
                                                &#34;allowRunningJobsToCompletePastOperationWindow&#34;: False,
                                                &#34;jobAliveCheckIntervalInMinutes&#34;: 5,
                                                &#34;queueScheduledJobs&#34;: False,
                                                &#34;enableJobThrottleAtClientLevel&#34;: False,
                                                &#34;enableMultiplexingForDBAgents&#34;: False,
                                                &#34;queueJobsIfConflictingJobsActive&#34;: False,
                                                &#34;queueJobsIfActivityDisabled&#34;: False,
                                                &#34;backupsPreemptsAuxilaryCopy&#34;: False,
                                                &#34;restorePreemptsOtherJobs&#34;: False,
                                                &#34;enableMultiplexingForOracle&#34;: False,
                                                &#34;jobStreamHighWaterMarkLevel&#34;: 500,
                                                &#34;backupsPreemptsOtherBackups&#34;: False,
                                                &#34;doNotStartBackupsOnDisabledClient&#34;: False

                                            }
            Returns:
                None

            Raises:
                SDKException:
                    if input is not valid type
        &#34;&#34;&#34;
        if isinstance(settings, dict):
            self._general_settings.get(&#39;generalSettings&#39;).update(settings)
            self._set_jobmanagement_settings()
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    def set_priority_settings(self, settings):
        &#34;&#34;&#34;
        sets priority settings for jobs and agents type.

            Args:
                settings  (list)    --  list of dictionaries with following format.
                                         [
                                            {
                                                &#34;type_of_operation&#34;: 1,
                                                &#34;combinedPriority&#34;: 10,
                                                &#34;jobTypeName&#34;: &#34;Information Management&#34;
                                            },
                                            {
                                                &#34;type_of_operation&#34;: 2,
                                                &#34;combinedPriority&#34;: 10,
                                                &#34;appTypeName&#34;: &#34;Windows File System&#34;
                                            },
                                            {
                                            &#34;type_of_operation&#34;: 1,
                                            &#34;combinedPriority&#34;: 10,
                                            &#34;jobTypeName&#34;: &#34;Auxiliary Copy&#34;
                                             }
                                        ]

            We have priority settings fro jobtype and agenttype

            NOTE : for setting, priority for jobtype the &#39;type_of_operation&#39; must be set to 1 and name of the job type
                   must be specified as below format.

                       ex :-  &#34;jobTypeName&#34;: &#34;Information Management&#34;

            NOTE : for setting, priority for agenttype the &#39;type_of_operation&#39; must be set to 2 and name of the job
             type must be specified as below format

                        ex :- &#34;appTypeName&#34;: &#34;Windows File System&#34;

            Returns:
                None

            Raises:
                SDKException:
                    if input is not valid type

        &#34;&#34;&#34;
        if isinstance(settings, list):
            for job in settings:
                if job[&#34;type_of_operation&#34;] == 1:
                    for job_type in self._priority_settings[&#39;jobPrioritySettings&#39;][&#39;jobTypePriorityList&#39;]:
                        if job_type[&#39;jobTypeName&#39;] == job.get(&#34;jobTypeName&#34;):
                            job.pop(&#34;jobTypeName&#34;)
                            job.pop(&#34;type_of_operation&#34;)
                            job_type.update(job)
                            break
                elif job[&#34;type_of_operation&#34;] == 2:
                    for job_type in self._priority_settings[&#39;jobPrioritySettings&#39;][&#39;agentTypePriorityList&#39;]:
                        if job_type[&#39;agentTypeEntity&#39;][&#39;appTypeName&#39;] == job.get(&#34;appTypeName&#34;):
                            job.pop(&#34;appTypeName&#34;)
                            job.pop(&#34;type_of_operation&#34;)
                            job_type.update(job)
                            break
            self._set_jobmanagement_settings()
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    def set_restart_settings(self, settings):
        &#34;&#34;&#34;
        sets restart settings for jobs.

            Args:
                settings    (list)      --  list of dictionaries with following format
                                            [
                                                {
                                                    &#34;killRunningJobWhenTotalRunningTimeExpires&#34;: False,
                                                    &#34;maxRestarts&#34;: 10,
                                                    &#34;enableTotalRunningTime&#34;: False,
                                                    &#34;restartable&#34;: False,
                                                    &#34;jobTypeName&#34;: &#34;File System and Indexing Based (Data Protection)&#34;,
                                                    &#34;restartIntervalInMinutes&#34;: 20,
                                                    &#34;preemptable&#34;: True,
                                                    &#34;totalRunningTime&#34;: 21600,
                                                    &#34;jobType&#34;: 6
                                                },
                                                {
                                                    &#34;killRunningJobWhenTotalRunningTimeExpires&#34;: False,
                                                    &#34;maxRestarts&#34;: 144,
                                                    &#34;enableTotalRunningTime&#34;: False,
                                                    &#34;restartable&#34;: False,
                                                    &#34;jobTypeName&#34;: &#34;File System and Indexing Based (Data Recovery)&#34;,
                                                    &#34;restartIntervalInMinutes&#34;: 20,
                                                    &#34;preemptable&#34;: False,
                                                    &#34;totalRunningTime&#34;: 21600,
                                                    &#34;jobType&#34;: 7
                                                }
                                            ]

            Returns:
                None

            Raises:
                SDKException:
                    if input is not valid type

        &#34;&#34;&#34;

        if isinstance(settings, list):
            for job in settings:
                target = {&#39;target&#39;: job_type for job_type in
                          self._restart_settings[&#39;jobRestartSettings&#39;][&#39;jobTypeRestartSettingList&#39;]
                          if job_type[&#39;jobTypeName&#39;] == job.get(&#34;jobTypeName&#34;)}
                target.get(&#39;target&#39;).update(job)
            self._set_jobmanagement_settings()
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    def set_update_settings(self, settings):
        &#34;&#34;&#34;
        sets update settings for jobs

            Args:
                settings    (list)      --      list of dictionaries with following format
                                                [
                                                    {
                                                        &#34;appTypeName&#34;: &#34;Windows File System&#34;,
                                                        &#34;recoveryTimeInMinutes&#34;: 20,
                                                        &#34;protectionTimeInMinutes&#34;: 20
                                                    },
                                                    {
                                                        &#34;appTypeName&#34;: &#34;Windows XP 64-bit File System&#34;,
                                                        &#34;recoveryTimeInMinutes&#34;: 20,
                                                        &#34;protectionTimeInMinutes&#34;: 20,
                                                    }
                                                ]
            Returns:
                None

            Raises:
                SDKException:
                    if input is not valid type

        &#34;&#34;&#34;

        if isinstance(settings, list):
            for job in settings:
                for job_type in self._update_settings[&#39;jobUpdatesSettings&#39;][&#39;agentTypeJobUpdateIntervalList&#39;]:
                    if job_type[&#39;agentTypeEntity&#39;][&#39;appTypeName&#39;] == job.get(&#34;appTypeName&#34;):
                        job.pop(&#34;appTypeName&#34;)
                        job_type.update(job)
                        break
            self._set_jobmanagement_settings()
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    @property
    def job_priority_precedence(self):
        &#34;&#34;&#34;
        gets the job priority precedence
            Returns:
                 (str)  --   type of job priority precedence is set.
        &#34;&#34;&#34;

        available_priorities = {
            1: &#34;client&#34;,
            2: &#34;agentType&#34;
        }
        return available_priorities.get(self._priority_settings[&#34;jobPrioritySettings&#34;][&#34;priorityPrecedence&#34;])

    @job_priority_precedence.setter
    def job_priority_precedence(self, priority_type):
        &#34;&#34;&#34;
        sets job priority precedence

                Args:
                    priority_type   (str)   --      type of priority to be set

                    Values:
                        &#34;client&#34;
                        &#34;agentType&#34;

        &#34;&#34;&#34;
        if isinstance(priority_type, str):
            available_priorities = {
                &#34;client&#34;: 1,
                &#34;agentType&#34;: 2
            }
            self._priority_settings[&#34;jobPrioritySettings&#34;][&#34;priorityPrecedence&#34;] = available_priorities[priority_type]
            self._set_jobmanagement_settings()
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    @property
    def start_phase_retry_interval(self):
        &#34;&#34;&#34;
        gets the start phase retry interval in (minutes)
            Returns:
                 (int)      --      interval in minutes.
        &#34;&#34;&#34;
        return self._restart_settings[&#34;jobRestartSettings&#34;][&#34;startPhaseRetryIntervalInMinutes&#34;]

    @start_phase_retry_interval.setter
    def start_phase_retry_interval(self, minutes):
        &#34;&#34;&#34;
        sets start phase retry interval for jobs

            Args:
                minutes     (int)       --      minutes to be set.

            Raises:
                SDKException:
                    if input is not valid type.
        &#34;&#34;&#34;

        if isinstance(minutes, int):
            self._restart_settings[&#34;jobRestartSettings&#34;][&#34;startPhaseRetryIntervalInMinutes&#34;] = minutes
            self._set_jobmanagement_settings()
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    @property
    def state_update_interval_for_continuous_data_replicator(self):
        &#34;&#34;&#34;
        gets the state update interval for continuous data replicator in (minutes)
            Returns:
                 (int)      --      interval in minutes
        &#34;&#34;&#34;
        return self._update_settings[&#34;jobUpdatesSettings&#34;][&#34;stateUpdateIntervalForContinuousDataReplicator&#34;]

    @state_update_interval_for_continuous_data_replicator.setter
    def state_update_interval_for_continuous_data_replicator(self, minutes):
        &#34;&#34;&#34;
        sets state update interval for continuous data replicator

            Args:
                 minutes       (int)        --      minutes to be set.

            Raises:
                SDKException:
                    if input is not valid type
        &#34;&#34;&#34;
        if isinstance(minutes, int):
            self._update_settings[&#34;jobUpdatesSettings&#34;][&#34;stateUpdateIntervalForContinuousDataReplicator&#34;] = minutes
            self._set_jobmanagement_settings()
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    @property
    def allow_running_jobs_to_complete_past_operation_window(self):
        &#34;&#34;&#34;
        Returns True if option is enabled
        else returns false
        &#34;&#34;&#34;
        return self._general_settings.get(&#39;generalSettings&#39;).get(&#34;allowRunningJobsToCompletePastOperationWindow&#34;)

    @allow_running_jobs_to_complete_past_operation_window.setter
    def allow_running_jobs_to_complete_past_operation_window(self, flag):
        &#34;&#34;&#34;
        enable/disable, allow running jobs to complete past operation window.
            Args:
                flag    (bool)    --        (True/False) to be set.

            Raises:
                SDKException:
                    if input is not valid type
        &#34;&#34;&#34;
        if isinstance(flag, bool):
            settings = {
                &#34;allowRunningJobsToCompletePastOperationWindow&#34;: flag
            }
            self.set_general_settings(settings)
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    @property
    def job_alive_check_interval_in_minutes(self):
        &#34;&#34;&#34;
        gets the job alive check interval in (minutes)
            Returns:
                (int)       --      interval in minutes
        &#34;&#34;&#34;
        return self._general_settings.get(&#39;generalSettings&#39;).get(&#34;jobAliveCheckIntervalInMinutes&#34;)

    @job_alive_check_interval_in_minutes.setter
    def job_alive_check_interval_in_minutes(self, minutes):
        &#34;&#34;&#34;
        sets the job alive check interval in (minutes)
            Args:
                  minutes       --      minutes to be set.

            Raises:
                  SDKException:
                        if input is not valid type
        &#34;&#34;&#34;
        if isinstance(minutes, int):
            settings = {
                &#34;jobAliveCheckIntervalInMinutes&#34;: minutes
            }
            self.set_general_settings(settings)
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    @property
    def queue_scheduled_jobs(self):
        &#34;&#34;&#34;
        Returns True if option is enabled
        else returns false
        &#34;&#34;&#34;
        return self._general_settings.get(&#39;generalSettings&#39;).get(&#34;queueScheduledJobs&#34;)

    @queue_scheduled_jobs.setter
    def queue_scheduled_jobs(self, flag):
        &#34;&#34;&#34;
        enable/disable, queue scheduled jobs

            Args:
                flag   (bool)      --       (True/False to be set)

            Raises:
                SDKException:
                    if input is not valid type
        &#34;&#34;&#34;
        if isinstance(flag, bool):
            settings = {
                &#34;queueScheduledJobs&#34;: flag
            }
            self.set_general_settings(settings)
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    @property
    def enable_job_throttle_at_client_level(self):
        &#34;&#34;&#34;
        Returns True if option is enabled
        else returns false
        &#34;&#34;&#34;
        return self._general_settings.get(&#39;generalSettings&#39;).get(&#34;enableJobThrottleAtClientLevel&#34;)

    @enable_job_throttle_at_client_level.setter
    def enable_job_throttle_at_client_level(self, flag):
        &#34;&#34;&#34;
        enable/disable, job throttle at client level
            Args:
                flag    (bool)      --      (True/False) to be set

            Raises:
                SDKException:
                    if input is not valid type
        &#34;&#34;&#34;
        if isinstance(flag, bool):
            settings = {
                &#34;enableJobThrottleAtClientLevel&#34;: flag
            }
            self.set_general_settings(settings)
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    @property
    def enable_multiplexing_for_db_agents(self):
        &#34;&#34;&#34;
        Returns True if option is enabled
        else returns False
        &#34;&#34;&#34;
        return self._general_settings.get(&#39;generalSettings&#39;).get(&#34;enableMultiplexingForDBAgents&#34;)

    @enable_multiplexing_for_db_agents.setter
    def enable_multiplexing_for_db_agents(self, flag):
        &#34;&#34;&#34;
        enable/disable, multiplexing for db agents
            Args:
                flag    (bool)      --      (True/False) to be set

            Raises:
                SDKException:
                    if input is not valid type
        &#34;&#34;&#34;
        if isinstance(flag, bool):
            settings = {
                &#34;enableMultiplexingForDBAgents&#34;: flag
            }
            self.set_general_settings(settings)
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    @property
    def queue_jobs_if_conflicting_jobs_active(self):
        &#34;&#34;&#34;
        Returns True if option is enabled
        else returns false
        &#34;&#34;&#34;
        return self._general_settings.get(&#39;generalSettings&#39;).get(&#34;queueJobsIfConflictingJobsActive&#34;)

    @queue_jobs_if_conflicting_jobs_active.setter
    def queue_jobs_if_conflicting_jobs_active(self, flag):
        &#34;&#34;&#34;
        enable/disable, queue jobs if conflicting jobs active
            Args;
                flag    (bool)      --      (True/False) to be set

            Raises:
                SDKException:
                    if input is not valid type
        &#34;&#34;&#34;
        if isinstance(flag, bool):
            settings = {
                &#34;queueJobsIfConflictingJobsActive&#34;: flag
            }
            self.set_general_settings(settings)
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    @property
    def queue_jobs_if_activity_disabled(self):
        &#34;&#34;&#34;
        Returns True if option is enabled
        else returns False
        &#34;&#34;&#34;
        return self._general_settings.get(&#39;generalSettings&#39;).get(&#34;queueJobsIfActivityDisabled&#34;)

    @queue_jobs_if_activity_disabled.setter
    def queue_jobs_if_activity_disabled(self, flag):
        &#34;&#34;&#34;
        enable/disable, queue jobs if activity disabled
            Args;
                flag    (bool)      --      (True/False) to be set

            Raises:
                SDKException:
                    if input is not valid type
        &#34;&#34;&#34;
        if isinstance(flag, bool):
            settings = {
                &#34;queueJobsIfActivityDisabled&#34;: flag
            }
            self.set_general_settings(settings)
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    @property
    def backups_preempts_auxilary_copy(self):
        &#34;&#34;&#34;
        Returns True if option is enabled
        else returns False
        &#34;&#34;&#34;
        return self._general_settings.get(&#39;generalSettings&#39;).get(&#34;backupsPreemptsAuxilaryCopy&#34;)

    @backups_preempts_auxilary_copy.setter
    def backups_preempts_auxilary_copy(self, flag):
        &#34;&#34;&#34;
        enable/disable, backups preempts auxiliary copy
            Args:
                flag    (bool)      --      (True/False) to be set

            Raises:
                SDKException:
                    if input is not valid type
        &#34;&#34;&#34;
        if isinstance(flag, bool):
            settings = {
                &#34;backupsPreemptsAuxilaryCopy&#34;: flag
            }
            self.set_general_settings(settings)
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    @property
    def restore_preempts_other_jobs(self):
        &#34;&#34;&#34;
        Returns True if option is enabled
        else returns False
        &#34;&#34;&#34;
        return self._general_settings.get(&#39;generalSettings&#39;).get(&#34;restorePreemptsOtherJobs&#34;)

    @restore_preempts_other_jobs.setter
    def restore_preempts_other_jobs(self, flag):
        &#34;&#34;&#34;
        enable/disable, restore preempts other jobs
            Args:
                flag    (bool)      --      (True/False) to be set

            Raises:
                SDKException:
                    if input is not valid type
        &#34;&#34;&#34;
        if isinstance(flag, bool):
            settings = {
                &#34;restorePreemptsOtherJobs&#34;: flag
            }
            self.set_general_settings(settings)
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    @property
    def enable_multiplexing_for_oracle(self):
        &#34;&#34;&#34;
        Returns True if option is enabled
        else returns False
        &#34;&#34;&#34;
        return self._general_settings.get(&#39;generalSettings&#39;).get(&#34;enableMultiplexingForOracle&#34;)

    @enable_multiplexing_for_oracle.setter
    def enable_multiplexing_for_oracle(self, flag):
        &#34;&#34;&#34;
        enable/disable, enable multiplexing for oracle
            Args:
                 flag   (bool)  --      (True/False) to be set

            Raises:
                SDKException:
                    if input is not valid type
        &#34;&#34;&#34;
        if isinstance(flag, bool):
            settings = {
                &#34;enableMultiplexingForOracle&#34;: flag
            }
            self.set_general_settings(settings)
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    @property
    def job_stream_high_water_mark_level(self):
        &#34;&#34;&#34;
        gets the job stream high water mark level
        &#34;&#34;&#34;
        return self._general_settings.get(&#39;generalSettings&#39;).get(&#34;jobStreamHighWaterMarkLevel&#34;)

    @job_stream_high_water_mark_level.setter
    def job_stream_high_water_mark_level(self, level):
        &#34;&#34;&#34;
        sets, job stream high water mak level
            Args:
                level   (int)       --      number of jobs to be performed at a time

            Raises:
                SDKException:
                    if input is not valid type
        &#34;&#34;&#34;
        if isinstance(level, int):
            settings = {
                &#34;jobStreamHighWaterMarkLevel&#34;: level
            }
            self.set_general_settings(settings)
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    @property
    def backups_preempts_other_backups(self):
        &#34;&#34;&#34;
        Returns True if option is enabled
        else returns False
        &#34;&#34;&#34;
        return self._general_settings.get(&#39;generalSettings&#39;).get(&#34;backupsPreemptsOtherBackups&#34;)

    @backups_preempts_other_backups.setter
    def backups_preempts_other_backups(self, flag):
        &#34;&#34;&#34;
        enable/disable, backups preempts other backups
            Args:
                 flag   (bool)      --      (True/False) to be set

            Raises:
                SDKException:
                    if input is not a valid type
        &#34;&#34;&#34;
        if isinstance(flag, bool):
            settings = {
                &#34;backupsPreemptsOtherBackups&#34;: flag
            }
            self.set_general_settings(settings)
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    @property
    def do_not_start_backups_on_disabled_client(self):
        &#34;&#34;&#34;
        Returns True if option is enabled
        else returns False
        &#34;&#34;&#34;
        return self._general_settings.get(&#39;generalSettings&#39;).get(&#34;doNotStartBackupsOnDisabledClient&#34;)

    @do_not_start_backups_on_disabled_client.setter
    def do_not_start_backups_on_disabled_client(self, flag):
        &#34;&#34;&#34;
         enable/disable, do not start backups on disabled client
            Args:
                 flag   (bool)      --      (True/False) to be set

            Raises:
                SDKException:
                    if input is not a valid type
        &#34;&#34;&#34;
        if isinstance(flag, bool):
            settings = {
                &#34;doNotStartBackupsOnDisabledClient&#34;: flag
            }
            self.set_general_settings(settings)
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    def get_restart_setting(self, jobtype):
        &#34;&#34;&#34;
        restart settings associated to particular jobtype can be obtained
            Args:
                jobtype     (str)       --      settings of the jobtype to get

                Available jobtypes:

                        &#34;Disaster Recovery backup&#34;
                        &#34;Auxiliary Copy&#34;
                        &#34;Data Aging&#34;
                        &#34;Download/Copy Updates&#34;
                        &#34;Offline Content Indexing&#34;
                        &#34;Information Management&#34;
                        &#34;File System and Indexing Based (Data Protection)&#34;
                        &#34;File System and Indexing Based (Data Recovery)&#34;
                        &#34;Exchange DB (Data Protection)&#34;
                        &#34;Exchange DB (Data Recovery)&#34;
                        &#34;Informix DB (Data Protection)&#34;
                        &#34;Informix DB (Data Recovery)&#34;
                        &#34;Lotus Notes DB (Data Protection)&#34;
                        &#34;Lotus Notes DB (Data Recovery)&#34;
                        &#34;Oracle DB (Data Protection)&#34;
                        &#34;Oracle DB (Data Recovery)&#34;
                        &#34;SQL DB (Data Protection)&#34;
                        &#34;SQL DB (Data Recovery)&#34;
                        &#34;MYSQL (Data Protection)&#34;
        `               &#34;MYSQL (Data Recovery)&#34;
                        &#34;Sybase DB (Data Protection)&#34;
                        &#34;Sybase DB (Data Recovery)&#34;
                        &#34;DB2 (Data Protection)&#34;
                        &#34;DB2 (Data Recovery)&#34;
                        &#34;CDR (Data Management)&#34;
                        &#34;Media Refresh&#34;
                        &#34;Documentum (Data Protection)&#34;
                        &#34;Documentum (Data Recovery)&#34;
                        &#34;SAP for Oracle (Data Protection)&#34;
                        &#34;SAP for Oracle (Data Recovery)&#34;
                        &#34;PostgreSQL (Data Protection)&#34;
                        &#34;PostgreSQL (Data Recovery)&#34;
                        &#34;Other (Data Protection)&#34;
                        &#34;Other (Data Recovery)&#34;
                        &#34;Workflow&#34;
                        &#34;DeDup DB Reconstruction&#34;
                        &#34;CommCell Migration Export&#34;
                        &#34;CommCell Migration Import&#34;
                        &#34;Install Software&#34;
                        &#34;Uninstall Software&#34;
                        &#34;Data Verification&#34;
                        &#34;Big Data Apps (Data Protection)&#34;
                        &#34;Big Data Apps (Data Recovery)&#34;
                        &#34;Cloud Apps (Data Protection)&#34;
                        &#34;Cloud Apps (Data Recovery)&#34;
                        &#34;Virtual Server (Data Protection)&#34;
                        &#34;Virtual Server (Data Recovery)&#34;
                        &#34;SAP for Hana (Data Protection)&#34;
                        &#34;SAP for Hana (Data Recovery)&#34;



            Returns:
                dict          --        settings of the specific job type as follows
                                        {
                                            &#34;jobTypeName&#34;: &#34;File System and Indexing Based (Data Protection)&#34;,
                                            &#34;restartable&#34;: true,
                                            &#34;maxRestarts&#34;: 10,
                                            &#34;restartIntervalInMinutes&#34;: 20,
                                            &#34;enableTotalRunningTime&#34;: false,
                                            &#34;totalRunningTime&#34;: 25200,
                                            &#34;killRunningJobWhenTotalRunningTimeExpires&#34;: false,
                                            &#34;preemptable&#34;: true,

                                        }

            Raises:
                SDKException:
                    if input is not valid type

        &#34;&#34;&#34;
        if isinstance(jobtype, str):
            for job_type in self._restart_settings[&#39;jobRestartSettings&#39;][&#39;jobTypeRestartSettingList&#39;]:
                if job_type[&#39;jobTypeName&#39;] == jobtype:
                    settings = copy.deepcopy(job_type)
                    return settings
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    def get_priority_setting(self, jobtype):
        &#34;&#34;&#34;
        priority settings associated to particular jobtype can be obtained
            Args:
                jobtype     (str)       --      settings of jobtype to get

                Available values:

                    jobtypename:
                        &#34;Information Management&#34;
                        &#34;Auxiliary Copy&#34;
                        &#34;Media Refresh&#34;
                        &#34;Data Verification&#34;
                        &#34;Persistent Recovery&#34;
                        &#34;Synth Full&#34;

                    apptypename:
                        &#34;Windows File System&#34;
                        &#34;Windows XP 64-bit File System&#34;
                        &#34;Windows 2003 32-bit File System&#34;
                        &#34;Windows 2003 64-bit File System&#34;
                        &#34;Active Directory&#34;
                        &#34;Windows File Archiver&#34;
                        &#34;File Share Archiver&#34;
                        &#34;Image Level&#34;
                        &#34;Exchange Mailbox (Classic)&#34;
                        &#34;Exchange Mailbox Archiver&#34;
                        &#34;Exchange Compliance Archiver&#34;
                        &#34;Exchange Public Folder&#34;
                        &#34;Exchange Database&#34;
                        &#34;SharePoint Database&#34;
                        &#34;SharePoint Server Database&#34;
                        &#34;SharePoint Document&#34;
                        &#34;SharePoint Server&#34;
                        &#34;Novell Directory Services&#34;
                        &#34;GroupWise DB&#34;
                        &#34;NDMP&#34;
                        &#34;Notes Document&#34;
                        &#34;Unix Notes Database&#34;
                        &#34;MAC FileSystem&#34;
                        &#34;Big Data Apps&#34;
                        &#34;Solaris File System&#34;
                        &#34;Solaris 64bit File System&#34;
                        &#34;FreeBSD&#34;
                        &#34;HP-UX File System&#34;
                        &#34;HP-UX 64bit File System&#34;
                        &#34;AIX File System&#34;
                        &#34;Unix Tru64 64-bit File System&#34;
                        &#34;Linux File System&#34;
                        &#34;Sybase Database&#34;
                        &#34;Oracle Database&#34;
                        &#34;Oracle RAC&#34;
                        &#34;Informix Database&#34;
                        &#34;DB2&#34;
                        &#34;DB2 on Unix&#34;
                        &#34;SAP for Oracle&#34;
                        &#34;SAP for MAX DB&#34;
                        &#34;ProxyHost on Unix&#34;
                        &#34;ProxyHost&#34;
                        &#34;Image Level On Unix&#34;
                        &#34;OSSV Plug-in on Windows&#34;
                        &#34;OSSV Plug-in on Unix&#34;
                        &#34;Unix File Archiver&#34;
                        &#34;SQL Server&#34;
                        &#34;Data Classification&#34;
                        &#34;OES File System on Linux&#34;
                        &#34;Centera&#34;
                        &#34;Exchange PF Archiver&#34;
                        &#34;Domino Mailbox Archiver&#34;
                        &#34;MS SharePoint Archiver&#34;
                        &#34;Content Indexing Agent&#34;
                        &#34;SRM Agent For Windows File Systems&#34;
                        &#34;SRM Agent For UNIX File Systems&#34;
                        &#34;DB2 MultiNode&#34;
                        &#34;MySQL&#34;
                        &#34;Virtual Server&#34;
                        &#34;SharePoint Search Connector&#34;
                        &#34;Object Link&#34;
                        &#34;PostgreSQL&#34;
                        &#34;Sybase IQ&#34;
                        &#34;External Data Connector&#34;
                        &#34;Documentum&#34;
                        &#34;Object Store&#34;
                        &#34;SAP HANA&#34;
                        &#34;Cloud Apps&#34;
                        &#34;Exchange Mailbox&#34;

            Returns:
                dict        --          settings of a specific jobtype
                                        ex:
                                        {
                                            &#34;jobTypeName&#34;: &#34;Information Management&#34;,
                                            &#34;combinedPriority&#34;: 0,
                                            &#34;type_of_operation&#34;: 1
                                        }

                                        or

                                        settings of a specific apptype
                                        ex:
                                        {
                                            &#34;appTypeName&#34;: &#34;Windows File System&#34;,
                                            &#34;combinedPriority&#34;: 6,
                                            &#34;type_of_operation&#34;: 2
                                        }
            Raises:
                SDKException:
                    if input is not valid type

        &#34;&#34;&#34;
        if isinstance(jobtype, str):
            for job_type in self._priority_settings[&#39;jobPrioritySettings&#39;][&#39;jobTypePriorityList&#39;]:
                if job_type[&#39;jobTypeName&#39;] == jobtype:
                    settings = {
                        &#39;jobTypeName&#39;: job_type.get(&#39;jobTypeName&#39;),
                        &#39;combinedPriority&#39;: job_type.get(&#39;combinedPriority&#39;),
                        &#39;type_of_operation&#39;: 1
                    }
                    return settings
            for job_type in self._priority_settings[&#39;jobPrioritySettings&#39;][&#39;agentTypePriorityList&#39;]:
                if job_type[&#39;agentTypeEntity&#39;][&#39;appTypeName&#39;] == jobtype:
                    settings = {
                        &#39;appTypeName&#39;: job_type.get(&#39;agentTypeEntity&#39;).get(&#39;appTypeName&#39;),
                        &#39;combinedPriority&#39;: job_type.get(&#39;combinedPriority&#39;),
                        &#39;type_of_operation&#39;: 2
                    }
                    return settings
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    def get_update_setting(self, jobtype):
        &#34;&#34;&#34;
        update settings associated to particular jobtype can be obtained
            Args:
                jobtype     (str)       --      settings of jobtype to get

                Available jobtype

                    Check get_priority_setting(self, jobtype) method documentation.

            Returns:
                dict        -           settings of a jobtype
                                        {
                                            &#34;appTypeName&#34;: &#34;Windows File System&#34;,
                                            &#34;recoveryTimeInMinutes&#34;: 20,
                                            &#34;protectionTimeInMinutes&#34;: 20
                                        }
            Raises:
                SDKException:
                    if input is not valid type

        &#34;&#34;&#34;
        if isinstance(jobtype, str):
            for job_type in self._update_settings[&#39;jobUpdatesSettings&#39;][&#39;agentTypeJobUpdateIntervalList&#39;]:
                if job_type[&#39;agentTypeEntity&#39;][&#39;appTypeName&#39;] == jobtype:
                    settings = {
                        &#39;appTypeName&#39;: job_type.get(&#39;agentTypeEntity&#39;).get(&#39;appTypeName&#39;),
                        &#39;recoveryTimeInMinutes&#39;: job_type.get(&#39;recoveryTimeInMinutes&#39;),
                        &#39;protectionTimeInMinutes&#39;: job_type.get(&#39;protectionTimeInMinutes&#39;)
                    }
                    return settings
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    @property
    def general_settings(self):
        &#34;&#34;&#34;
        gets the general settings.
             Returns:   (dict)      --  The general settings
        &#34;&#34;&#34;
        return self._general_settings

    @property
    def restart_settings(self):
        &#34;&#34;&#34;
        gets the restart settings.
                Returns:    (dict)    --  The restart settings.
        &#34;&#34;&#34;

        return self._restart_settings

    @property
    def priority_settings(self):
        &#34;&#34;&#34;
        gets the priority settings.
                Returns:    (dict)    --  The priority settings.
        &#34;&#34;&#34;

        return self._priority_settings

    @property
    def update_settings(self):
        &#34;&#34;&#34;
        gets the update settings.
                Returns:    (dict)    --  The update settings.
        &#34;&#34;&#34;

        return self._update_settings

    def set_job_error_threshold(self, error_threshold_dict):
        &#34;&#34;&#34;

        Args:
            error_threshold_dict  (dict)  :   A dictionary of following  key/value pairs can be set.

        Returns:
            None

        &#34;&#34;&#34;
        raise NotImplementedError(&#34;Yet To Be Implemented&#34;)


class Job(object):
    &#34;&#34;&#34;Class for performing client operations for a specific client.&#34;&#34;&#34;

    def __init__(self, commcell_object, job_id):
        &#34;&#34;&#34;Initialise the Job class instance.

            Args:
                commcell_object     (object)        --  instance of the Commcell class

                job_id              (str / int)     --  id of the job

            Returns:
                object  -   instance of the Job class

            Raises:
                SDKException:
                    if job id is not an integer

                    if job is not a valid job, i.e., does not exist in the Commcell

        &#34;&#34;&#34;
        try:
            int(job_id)
        except ValueError:
            raise SDKException(&#39;Job&#39;, &#39;101&#39;)

        self._commcell_object = commcell_object

        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_

        self._job_id = str(job_id)

        self._JOB = self._services[&#39;JOB&#39;] % (self.job_id)

        if not self._is_valid_job():
            raise SDKException(&#39;Job&#39;, &#39;102&#39;, f&#39;No job exists with the specified Job ID: {self.job_id}&#39;)

        self._JOB_DETAILS = self._services[&#39;JOB_DETAILS&#39;]
        self.ADVANCED_JOB_DETAILS = AdvancedJobDetailType
        self._SUSPEND = self._services[&#39;SUSPEND_JOB&#39;] % self.job_id
        self._RESUME = self._services[&#39;RESUME_JOB&#39;] % self.job_id
        self._KILL = self._services[&#39;KILL_JOB&#39;] % self.job_id
        self._RESUBMIT = self._services[&#39;RESUBMIT_JOB&#39;] % self.job_id
        self._JOB_EVENTS = self._services[&#39;JOB_EVENTS&#39;] % self.job_id
        self._JOB_TASK_DETAILS = self._services[&#39;JOB_TASK_DETAILS&#39;]

        self._client_name = None
        self._agent_name = None
        self._instance_name = None
        self._backupset_name = None
        self._subclient_name = None
        self._job_type = None
        self._backup_level = None
        self._start_time = None
        self._end_time = None
        self._delay_reason = None
        self._pending_reason = None
        self._status = None
        self._phase = None
        self._summary = None
        self._details = None
        self._task_details = None

        self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.

            Returns:
                str     -   string for instance of this class

        &#34;&#34;&#34;
        representation_string = &#39;Job class instance for job id: &#34;{0}&#34;&#39;
        return representation_string.format(self.job_id)

    def _is_valid_job(self):
        &#34;&#34;&#34;Checks if the job submitted with the job id is a valid job or not.

            Returns:
                bool    -   boolean that represents whether the job is valid or not

        &#34;&#34;&#34;
        for _ in range(10):
            try:
                self._get_job_summary()
                return True
            except SDKException as excp:
                if excp.exception_module == &#39;Job&#39; and excp.exception_id == &#39;104&#39;:
                    time.sleep(1.5)
                    continue
                else:
                    raise excp

        return False

    def _get_job_summary(self):
        &#34;&#34;&#34;Gets the properties of this job.

            Returns:
                dict    -   dict that contains the summary of this job

            Raises:
                SDKException:
                    if no record found for this job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        attempts = 0
        while attempts &lt; 5:  # Retrying to ignore the transient case when no jobs are found
            flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._JOB)
            attempts += 1

            if flag:
                if response.json():
                    if response.json().get(&#39;totalRecordsWithoutPaging&#39;, 0) == 0:
                        time.sleep(2**attempts)
                        continue

                    if &#39;jobs&#39; in response.json():
                        for job in response.json()[&#39;jobs&#39;]:
                            return job[&#39;jobSummary&#39;]
                else:
                    if attempts &gt; 4:
                        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
                    time.sleep(20)

            else:
                if attempts &gt; 4:
                    response_string = self._update_response_(response.text)
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
                time.sleep(20)

        raise SDKException(&#39;Job&#39;, &#39;104&#39;)

    def _get_job_details(self):
        &#34;&#34;&#34;Gets the detailed properties of this job.

            Returns:
                dict    -   dict consisting of the detailed properties of the job

            Raises:
                SDKException:
                    if failed to get the job details

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        payload = {
            &#34;jobId&#34;: int(self.job_id),
            &#34;showAttempt&#34;: True
        }

        retry_count = 0

        while retry_count &lt; 5:  # Retrying to ignore the transient case when job details are not found
            flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._JOB_DETAILS, payload)
            retry_count += 1

            if flag:
                if response.json():
                    if &#39;job&#39; in response.json():
                        return response.json()[&#39;job&#39;]
                    elif &#39;error&#39; in response.json():
                        error_code = response.json()[&#39;error&#39;][&#39;errList&#39;][0][&#39;errorCode&#39;]
                        error_message = response.json()[&#39;error&#39;][&#39;errList&#39;][0][&#39;errLogMessage&#39;]

                        raise SDKException(
                            &#39;Job&#39;,
                            &#39;105&#39;,
                            &#39;Error Code: &#34;{0}&#34;\nError Message: &#34;{1}&#34;&#39;.format(error_code, error_message)
                        )
                    else:
                        raise SDKException(&#39;Job&#39;, &#39;106&#39;, &#39;Response JSON: {0}&#39;.format(response.json()))
                else:
                    if retry_count &gt; 4:
                        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
                    time.sleep(20)
            else:
                if retry_count &gt; 4:
                    response_string = self._update_response_(response.text)
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
                time.sleep(20)

        raise SDKException(&#39;Response&#39;, &#39;102&#39;)

    def _get_job_task_details(self):
        &#34;&#34;&#34;Gets the task details of this job.

            Returns:
                dict    -   dict consisting of the task details of the job

            Raises:
                SDKException:
                    if failed to get the job task details

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        retry_count = 0

        while retry_count &lt; 5:  # Retrying to ignore the transient case when job task details are not found
            flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._JOB_TASK_DETAILS % self.job_id)
            retry_count += 1

            if flag:
                if response.json():
                    if &#39;taskInfo&#39; in response.json():
                        return response.json()[&#39;taskInfo&#39;]
                    elif &#39;error&#39; in response.json():
                        error_code = response.json()[&#39;error&#39;][&#39;errList&#39;][0][&#39;errorCode&#39;]
                        error_message = response.json()[&#39;error&#39;][&#39;errList&#39;][0][&#39;errorMessage&#39;]

                        raise SDKException(
                            &#39;Job&#39;,
                            &#39;105&#39;,
                            &#39;Error Code: &#34;{0}&#34;\nError Message: &#34;{1}&#34;&#39;.format(error_code, error_message)
                        )
                    else:
                        raise SDKException(&#39;Job&#39;, &#39;106&#39;, &#39;Response JSON: {0}&#39;.format(response.json()))
                else:
                    if retry_count &gt; 4:
                        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
                    time.sleep(20)
            else:
                if retry_count &gt; 4:
                    response_string = self._update_response_(response.text)
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
                time.sleep(20)

        raise SDKException(&#39;Response&#39;, &#39;102&#39;)

    def _initialize_job_properties(self):
        &#34;&#34;&#34;Initializes the common properties for the job.
            Adds the client, agent, backupset, subclient name to the job object.

        &#34;&#34;&#34;
        self._summary = self._get_job_summary()
        self._details = self._get_job_details()

        self._status = self._summary[&#39;status&#39;]

        self._start_time = time.strftime(
            &#39;%Y-%m-%d %H:%M:%S&#39;, time.gmtime(self._summary[&#39;jobStartTime&#39;])
        )

    def _wait_for_status(self, status, timeout=6):
        &#34;&#34;&#34;Waits for 6 minutes or till the job status is changed to given status,
            whichever is earlier.

            Args:
                status  (str)   --  Job Status

                timeout (int)   --  timeout interval in mins

            Returns:
                None

        &#34;&#34;&#34;
        start_time = time.time()
        current_job_status = self.status
        current_job_status = current_job_status if current_job_status else self.state
        while current_job_status.lower() != status.lower():
            if (self.is_finished is True) or (time.time() - start_time &gt; (timeout * 60)):
                break

            time.sleep(3)
            current_job_status = self.status

    def wait_for_completion(self, timeout=30, **kwargs):
        &#34;&#34;&#34;Waits till the job is not finished; i.e.; till the value of job.is_finished is not True.
            Kills the job and exits, if the job has been in Pending / Waiting state for more than
            the timeout value.

            In case of job failure job status and failure reason can be obtained
                using status and delay_reason property

            Args:
                timeout     (int)   --  minutes after which the job should be killed and exited,
                        if the job has been in Pending / Waiting state
                    default: 30

                **kwargs    (str)   --  accepted optional arguments

                    return_timeout  (int)   -- minutes after which the method will return False.

            Returns:
                bool    -   boolean specifying whether the job had finished or not
                    True    -   if the job had finished successfully

                    False   -   if the job was killed/failed

        &#34;&#34;&#34;
        start_time = actual_start_time = time.time()
        pending_time = 0
        waiting_time = 0
        previous_status = None
        return_timeout = kwargs.get(&#39;return_timeout&#39;)

        status_list = [&#39;pending&#39;, &#39;waiting&#39;]

        while not self.is_finished:
            time.sleep(30)

            if return_timeout and ((time.time() - actual_start_time) / 60) &gt; return_timeout:
                return False

            # get the current status of the job
            status = self.status
            status = status.lower() if status else self.state.lower()

            # set the value of start time as current time
            # if the current status is pending / waiting but the previous status was not
            # also if the current status is pending / waiting and same as previous,
            # then don&#39;t update the value of start time
            if status in status_list and previous_status not in status_list:
                start_time = time.time()

            if status == &#39;pending&#39;:
                pending_time = (time.time() - start_time) / 60
            else:
                pending_time = 0

            if status == &#39;waiting&#39;:
                waiting_time = (time.time() - start_time) / 60
            else:
                waiting_time = 0

            if pending_time &gt; timeout or waiting_time &gt; timeout:
                self.kill()
                break

            # set the value of previous status as the value of current status
            previous_status = status
        else:
            return self._status.lower() not in [&#34;failed&#34;, &#34;killed&#34;, &#34;failed to start&#34;]

        return False

    @property
    def is_finished(self):
        &#34;&#34;&#34;Checks whether the job has finished or not.

            Returns:
                bool    -   boolean that represents whether the job has finished or not

        &#34;&#34;&#34;
        self._summary = self._get_job_summary()
        self._details = self._get_job_details()

        self._status = self._summary[&#39;status&#39;]

        if self._summary[&#39;lastUpdateTime&#39;] != 0:
            self._end_time = time.strftime(
                &#39;%Y-%m-%d %H:%M:%S&#39;, time.gmtime(self._summary[&#39;lastUpdateTime&#39;])
            )

        return (&#39;completed&#39; in self._status.lower() or
                &#39;killed&#39; in self._status.lower() or
                &#39;committed&#39; in self._status.lower() or
                &#39;failed&#39; in self._status.lower())

    @property
    def client_name(self):
        &#34;&#34;&#34;Treats the client name as a read-only attribute.&#34;&#34;&#34;
        if &#39;clientName&#39; in self._summary[&#39;subclient&#39;]:
            return self._summary[&#39;subclient&#39;][&#39;clientName&#39;]

    @property
    def agent_name(self):
        &#34;&#34;&#34;Treats the agent name as a read-only attribute.&#34;&#34;&#34;
        if &#39;appName&#39; in self._summary[&#39;subclient&#39;]:
            return self._summary[&#39;subclient&#39;][&#39;appName&#39;]

    @property
    def instance_name(self):
        &#34;&#34;&#34;Treats the instance name as a read-only attribute.&#34;&#34;&#34;
        if &#39;instanceName&#39; in self._summary[&#39;subclient&#39;]:
            return self._summary[&#39;subclient&#39;][&#39;instanceName&#39;]

    @property
    def backupset_name(self):
        &#34;&#34;&#34;Treats the backupset name as a read-only attribute.&#34;&#34;&#34;
        if &#39;backupsetName&#39; in self._summary[&#39;subclient&#39;]:
            return self._summary[&#39;subclient&#39;][&#39;backupsetName&#39;]

    @property
    def subclient_name(self):
        &#34;&#34;&#34;Treats the subclient name as a read-only attribute.&#34;&#34;&#34;
        if &#39;subclientName&#39; in self._summary[&#39;subclient&#39;]:
            return self._summary[&#39;subclient&#39;][&#39;subclientName&#39;]

    @property
    def status(self):
        &#34;&#34;&#34;Treats the job status as a read-only attribute.
           http://documentation.commvault.com/commvault/v11/article?p=features/rest_api/operations/get_job.htm
           please refer status section in above doc link for complete list of status available&#34;&#34;&#34;
        self.is_finished
        return self._status

    @property
    def job_id(self):
        &#34;&#34;&#34;Treats the job id as a read-only attribute.&#34;&#34;&#34;
        return self._job_id

    @property
    def job_type(self):
        &#34;&#34;&#34;Treats the job type as a read-only attribute.&#34;&#34;&#34;
        return self._summary[&#39;jobType&#39;]

    @property
    def backup_level(self):
        &#34;&#34;&#34;Treats the backup level as a read-only attribute.&#34;&#34;&#34;
        if &#39;backupLevelName&#39; in self._summary:
            return self._summary[&#39;backupLevelName&#39;]

    @property
    def start_time(self):
        &#34;&#34;&#34;Treats the start time as a read-only attribute.&#34;&#34;&#34;
        return self._start_time
    
    @property
    def start_timestamp(self):
        &#34;&#34;&#34;Treats the unix start time as a read-only attribute.&#34;&#34;&#34;
        return self._summary[&#39;jobStartTime&#39;]

    @property
    def end_timestamp(self):
        &#34;&#34;&#34;Treats the unix end time as a read-only attribute&#34;&#34;&#34;
        return self._summary[&#39;jobEndTime&#39;]

    @property
    def end_time(self):
        &#34;&#34;&#34;Treats the end time as a read-only attribute.&#34;&#34;&#34;
        return self._end_time

    @property
    def delay_reason(self):
        &#34;&#34;&#34;Treats the job delay reason as a read-only attribute.&#34;&#34;&#34;
        self.is_finished
        progress_info = self._details[&#39;jobDetail&#39;][&#39;progressInfo&#39;]
        if &#39;reasonForJobDelay&#39; in progress_info and progress_info[&#39;reasonForJobDelay&#39;]:
            return progress_info[&#39;reasonForJobDelay&#39;]

    @property
    def pending_reason(self):
        &#34;&#34;&#34;Treats the job pending reason as a read-only attribute.&#34;&#34;&#34;
        self.is_finished
        if &#39;pendingReason&#39; in self._summary and self._summary[&#39;pendingReason&#39;]:
            return self._summary[&#39;pendingReason&#39;]

    @property
    def phase(self):
        &#34;&#34;&#34;Treats the job current phase as a read-only attribute.&#34;&#34;&#34;
        self.is_finished
        if &#39;currentPhaseName&#39; in self._summary:
            return self._summary[&#39;currentPhaseName&#39;]

    @property
    def attempts(self):
        &#34;&#34;&#34;Returns job attempts data as read-only attribute&#34;&#34;&#34;
        self.is_finished
        return self._details.get(&#39;jobDetail&#39;, {}).get(&#39;attemptsInfo&#39;, {})

    @property
    def summary(self):
        &#34;&#34;&#34;Treats the job full summary as a read-only attribute.&#34;&#34;&#34;
        self.is_finished
        return self._summary

    @property
    def username(self):
        &#34;&#34;&#34;Treats the username as a read-only attribute.&#34;&#34;&#34;
        return self._summary[&#39;userName&#39;][&#39;userName&#39;]

    @property
    def userid(self):
        &#34;&#34;&#34;Treats the userid as a read-only attribute.&#34;&#34;&#34;
        return self._summary[&#39;userName&#39;][&#39;userId&#39;]

    @property
    def details(self):
        &#34;&#34;&#34;Treats the job full details as a read-only attribute.&#34;&#34;&#34;
        self.is_finished
        return self._details

    @property
    def size_of_application(self):
        &#34;&#34;&#34;Treats the size of application as a read-only attribute.&#34;&#34;&#34;
        if &#39;sizeOfApplication&#39; in self._summary:
            return self._summary[&#39;sizeOfApplication&#39;]

    @property
    def media_size(self):
        &#34;&#34;&#34;
        Treats the size of media as a read-only attribute
        Returns:
            integer - size of media or data written
        &#34;&#34;&#34;
        return self._summary.get(&#39;sizeOfMediaOnDisk&#39;, 0)

    @property
    def num_of_files_transferred(self):
        &#34;&#34;&#34;Treats the number of files transferred as a read-only attribute.&#34;&#34;&#34;
        self.is_finished
        return self._details[&#39;jobDetail&#39;][&#39;progressInfo&#39;][&#39;numOfFilesTransferred&#39;]

    @property
    def state(self):
        &#34;&#34;&#34;Treats the job state as a read-only attribute.&#34;&#34;&#34;
        self.is_finished
        return self._details[&#39;jobDetail&#39;][&#39;progressInfo&#39;][&#39;state&#39;]

    @property
    def task_details(self):
        &#34;&#34;&#34;Returns: (dict) A dictionary of job task details&#34;&#34;&#34;
        if not self._task_details:
            self._task_details = self._get_job_task_details()
        return self._task_details

    def pause(self, wait_for_job_to_pause=False, timeout=6):
        &#34;&#34;&#34;Suspends the job.

            Args:
                wait_for_job_to_pause   (bool)  --  wait till job status is changed to Suspended

                    default: False

                timeout (int)                   --  timeout interval to wait for job to move to suspend state

            Raises:
                SDKException:
                    if failed to suspend job

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._SUSPEND)

        self.is_finished

        if flag:
            if response.json():
                if &#39;errors&#39; in response.json():
                    error_list = response.json()[&#39;errors&#39;][0][&#39;errList&#39;][0]
                    error_code = error_list[&#39;errorCode&#39;]
                    error_message = error_list[&#39;errLogMessage&#39;].strip()
                else:
                    error_code = response.json().get(&#39;errorCode&#39;, 0)
                    error_message = response.json().get(&#39;errorMessage&#39;, &#39;nil&#39;)

                if error_code != 0:
                    raise SDKException(
                        &#39;Job&#39;, &#39;102&#39;, &#39;Job suspend failed\nError: &#34;{0}&#34;&#39;.format(error_message)
                    )
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        if wait_for_job_to_pause is True:
            self._wait_for_status(&#34;SUSPENDED&#34;, timeout=timeout)

    def resume(self, wait_for_job_to_resume=False):
        &#34;&#34;&#34;Resumes the job.

            Args:
                wait_for_job_to_resume  (bool)  --  wait till job status is changed to Running

                    default: False

            Raises:
                SDKException:
                    if failed to resume job

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._RESUME)

        self.is_finished

        if flag:
            if response.json():
                if &#39;errors&#39; in response.json():
                    error_list = response.json()[&#39;errors&#39;][0][&#39;errList&#39;][0]
                    error_code = error_list[&#39;errorCode&#39;]
                    error_message = error_list[&#39;errLogMessage&#39;].strip()
                else:
                    error_code = response.json().get(&#39;errorCode&#39;, 0)
                    error_message = response.json().get(&#39;errorMessage&#39;, &#39;nil&#39;)

                if error_code != 0:
                    raise SDKException(
                        &#39;Job&#39;, &#39;102&#39;, &#39;Job resume failed\nError: &#34;{0}&#34;&#39;.format(error_message)
                    )
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        if wait_for_job_to_resume is True:
            self._wait_for_status(&#34;RUNNING&#34;)

    def resubmit(self, start_suspended=None):
        &#34;&#34;&#34;Resubmits the job

        Args:
            start_suspended (bool)  -   whether to start the new job in suspended state or not
                                        default: None, the new job starts same as this job started

        Returns:
            object  -   Job class object for the given job id

        Raises:
                SDKException:
                    if job is already running

                    if response is not success

        &#34;&#34;&#34;
        if start_suspended not in [True, False, None]:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

        if not self.is_finished:
            raise SDKException(&#39;Job&#39;, &#39;102&#39;, &#39;Cannot resubmit the Job, the Job is still running&#39;)

        url = self._RESUBMIT
        if start_suspended is not None:
            url += f&#39;?startInSuspendedState={start_suspended}&#39;
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, url)

        if flag:
            if response.json():
                if &#39;errors&#39; in response.json():
                    error_list = response.json()[&#39;errors&#39;][0][&#39;errList&#39;][0]
                    error_code = error_list[&#39;errorCode&#39;]
                    error_message = error_list[&#39;errLogMessage&#39;].strip()
                else:
                    error_code = response.json().get(&#39;errorCode&#39;, 0)
                    error_message = response.json().get(&#39;errorMessage&#39;, &#39;nil&#39;)

                if error_code != 0:
                    raise SDKException(
                        &#39;Job&#39;, &#39;102&#39;, &#39;Resubmitting job failed\nError: &#34;{0}&#34;&#39;.format(error_message)
                    )
            return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def kill(self, wait_for_job_to_kill=False):
        &#34;&#34;&#34;Kills the job.

            Args:
                wait_for_job_to_kill    (bool)  --  wait till job status is changed to Killed

                    default: False

            Raises:
                SDKException:
                    if failed to kill job

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._KILL)

        self.is_finished

        if flag:
            if response.json():
                if &#39;errors&#39; in response.json():
                    error_list = response.json()[&#39;errors&#39;][0][&#39;errList&#39;][0]
                    error_code = error_list[&#39;errorCode&#39;]
                    error_message = error_list[&#39;errLogMessage&#39;].strip()
                else:
                    error_code = response.json().get(&#39;errorCode&#39;, 0)
                    error_message = response.json().get(&#39;errorMessage&#39;, &#39;nil&#39;)

                if error_code != 0:
                    raise SDKException(
                        &#39;Job&#39;, &#39;102&#39;, &#39;Job kill failed\nError: &#34;{0}&#34;&#39;.format(error_message)
                    )
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        if wait_for_job_to_kill is True:
            self._wait_for_status(&#34;KILLED&#34;)

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the Job.&#34;&#34;&#34;
        self._initialize_job_properties()
        self.is_finished

    def advanced_job_details(self, info_type):
        &#34;&#34;&#34;Returns advanced properties for the job

            Args:
                infoType    (object)  --  job detail type to be passed from AdvancedJobDetailType
                enum from the constants

            Returns:
                dict -  dictionary with advanced details of the job info type given

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        if not isinstance(info_type, AdvancedJobDetailType):
            raise SDKException(&#39;Response&#39;, &#39;107&#39;)
        url = self._services[&#39;ADVANCED_JOB_DETAIL_TYPE&#39;] % (self.job_id, info_type.value)
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, url)

        if flag:
            if response.json():
                response = response.json()

                if response.get(&#39;errorCode&#39;, 0) != 0:
                    error_message = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Failed to fetch details.\nError: &#34;{0}&#34;&#39;.format(error_message)
                    raise SDKException(&#39;Job&#39;, &#39;102&#39;, o_str)

                return response
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def get_events(self):
        &#34;&#34;&#34; gets the commserv events associated with this job

            Args:

                None

            Returns:

                list - list of job events

                    Example : [
                        {
                            &#34;severity&#34;: 3,
                            &#34;eventCode&#34;: &#34;318769020&#34;,
                            &#34;jobId&#34;: 4547,
                            &#34;acknowledge&#34;: 0,
                            &#34;eventCodeString&#34;: &#34;19:1916&#34;,
                            &#34;subsystem&#34;: &#34;JobManager&#34;,
                            &#34;description&#34;: &#34;Data Analytics operation has completed with one or more errors.&#34;,
                            &#34;id&#34;: 25245,
                            &#34;timeSource&#34;: 1600919001,
                            &#34;type&#34;: 0,
                            &#34;clientEntity&#34;: {
                                &#34;clientId&#34;: 2,
                                &#34;clientName&#34;: &#34;xyz&#34;,
                                &#34;displayName&#34;: &#34;xyz&#34;
                            }
                        },
                        {
                            &#34;severity&#34;: 6,
                            &#34;eventCode&#34;: &#34;318767961&#34;,
                            &#34;jobId&#34;: 4547,
                            &#34;acknowledge&#34;: 0,
                            &#34;eventCodeString&#34;: &#34;19:857&#34;,
                            &#34;subsystem&#34;: &#34;clBackup&#34;,
                            &#34;description&#34;: &#34;Failed to send some items to Index Engine&#34;,
                            &#34;id&#34;: 25244,
                            &#34;timeSource&#34;: 1600918999,
                            &#34;type&#34;: 0,
                            &#34;clientEntity&#34;: {
                                &#34;clientId&#34;: 33,
                                &#34;clientName&#34;: &#34;xyz&#34;,
                                &#34;displayName&#34;: &#34;xyz&#34;
                            }
                        }
                    ]

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._JOB_EVENTS)
        if flag:
            if response.json() and &#39;commservEvents&#39; in response.json():
                    return response.json()[&#39;commservEvents&#39;]
            raise SDKException(&#39;Job&#39;, &#39;104&#39;)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def get_vm_list(self):
        &#34;&#34;&#34;
        Gets the list of all VMs associated to the job
        Returns: list of VM dictionaries
            VM: {
               &#34;Size&#34;:0,
               &#34;AverageThroughput&#34;:0,
               &#34;UsedSpace&#34;:0,
               &#34;ArchivedByCurrentJob&#34;:false,
               &#34;jobID&#34;:0,
               &#34;CBTStatus&#34;:&#34;&#34;,
               &#34;BackupType&#34;:0,
               &#34;totalFiles&#34;:0,
               &#34;Status&#34;:2,
               &#34;CurrentThroughput&#34;:0,
               &#34;Agent&#34;:&#34;proxy&#34;,
               &#34;lastSyncedBkpJob&#34;:0,
               &#34;GUID&#34;:&#34;live sync pair guid&#34;,
               &#34;HardwareVersion&#34;:&#34;vm h/w&#34;,
               &#34;restoredSize&#34;:1361912,
               &#34;FailureReason&#34;:&#34;&#34;,
               &#34;BackupStartTime&#34;:0,
               &#34;TransportMode&#34;:&#34;nbd&#34;,
               &#34;projectId&#34;:&#34;&#34;,
               &#34;syncStatus&#34;:3,
               &#34;PoweredOffSince&#34;:0,
               &#34;OperatingSystem&#34;:&#34;Microsoft Windows Server 2012 (64-bit)&#34;,
               &#34;backupLevel&#34;:0,
               &#34;destinationVMName&#34;:&#34;drvm1&#34;,
               &#34;successfulCIedFiles&#34;:0,
               &#34;GuestSize&#34;:0,
               &#34;failedCIedFiles&#34;:0,
               &#34;vmName&#34;:&#34;vm1&#34;,
               &#34;ToolsVersion&#34;:&#34;Not running&#34;,
               &#34;clientId&#34;:3280,
               &#34;Host&#34;:&#34;*******&#34;,
               &#34;StubStatus&#34;:0,
               &#34;BackupEndTime&#34;:0,
               &#34;PoweredOffByCurrentJob&#34;:false
            }
        &#34;&#34;&#34;
        return self.details.get(&#39;jobDetail&#39;, {}).get(&#39;clientStatusInfo&#39;, {}).get(&#39;vmStatus&#39;, [])

    def get_child_jobs(self):
        &#34;&#34;&#34; Get the child jobs details for the current job
        Returns:
                _jobs_list          (list):     List of child jobs

        &#34;&#34;&#34;
        _jobs_list = []
        if self.details.get(&#39;jobDetail&#39;, {}).get(&#39;clientStatusInfo&#39;, {}).get(&#39;vmStatus&#39;):
            for _job in self.details[&#39;jobDetail&#39;][&#39;clientStatusInfo&#39;][&#39;vmStatus&#39;]:
                _jobs_list.append(_job)
            return _jobs_list
        else:
            return None


class _ErrorRule:
    &#34;&#34;&#34;Class for enabling, disabling, adding, getting and deleting error rules.&#34;&#34;&#34;

    def __init__(self, commcell):
        self.commcell = commcell
        self.rule_dict = {}
        self.xml_body = &#34;&#34;&#34;
        &lt;App_SetJobErrorDecision&gt;
        &lt;entity _type_=&#34;1&#34; commCellId=&#34;{commcell_id}&#34; commCellName=&#34;{commserv_name}&#34; /&gt;
        &lt;jobErrorRuleList&gt;
        &lt;idaRuleList isEnabled=&#34;{enable_flag_ida}&#34;&gt;
        &lt;ida _type_=&#34;78&#34; appGroupId=&#34;57&#34; appGroupName=&#34;{app_group_name}&#34; /&gt;
        &lt;ruleList&gt;{final_str}&lt;srcEntity _type_=&#34;1&#34; commCellId=&#34;{commcell_id}&#34; /&gt;&lt;/ruleList&gt;
        &lt;osEntity _type_=&#34;161&#34; /&gt;
        &lt;/idaRuleList&gt;
        &lt;/jobErrorRuleList&gt;
        &lt;/App_SetJobErrorDecision&gt;
        &#34;&#34;&#34;

        self.error_rule_str = &#34;&#34;&#34;
        &lt;ruleList blockedFileTypes=&#34;0&#34; isEnabled=&#34;{is_enabled}&#34; jobDecision=&#34;{job_decision}&#34; pattern=&#34;{pattern}&#34; skipTLbackups=&#34;0&#34; skipofflineDBs=&#34;0&#34; skippedFiles=&#34;0&#34;&gt;
        &lt;errorCode allErrorCodes=&#34;{all_error_codes}&#34; fromValue=&#34;{from_error_code}&#34; skipReportingError=&#34;{skip_reporting_error}&#34; toValue=&#34;{to_error_code}&#34; /&gt;
        &lt;/ruleList&gt;
        &#34;&#34;&#34;

    def _get_xml_for_rule(self, rule_dict):
        &#34;&#34;&#34;
        Returns the XML for a given rule&#39;s dictionary of key value pairs. The XML output is used internally when
        when adding new or updating existing rules.

        Args:
            rule_dict   (dict)  -   Dictionary of a rule&#39;s key value pairs.

        Returns:
            str -   The XML output formatted as a string.

        Raises:
            None

        &#34;&#34;&#34;

        return self.error_rule_str.format(
            pattern=rule_dict[&#39;pattern&#39;],
            all_error_codes=rule_dict[&#39;all_error_codes&#39;],
            from_error_code=rule_dict[&#39;from_error_code&#39;],
            to_error_code=rule_dict[&#39;to_error_code&#39;],
            job_decision=rule_dict[&#39;job_decision&#39;],
            is_enabled=rule_dict[&#39;is_enabled&#39;],
            skip_reporting_error=rule_dict[&#39;skip_reporting_error&#39;])

    def add_error_rule(self, rules_arg):
        &#34;&#34;&#34;
        Add new error rules as well as update existing rules, each rule is identified by its rule name denoted by key
        rule_name.

            Args:
                rules_arg   (dict)  --  A dictionary whose key is the application group name and value is a rules list.

                    Supported value(s) for key is all constants under ApplicationGroup(Enum)

                    The value for above key is a list
                    where each item of the list is a dictionary of the following key value pairs.

                        is_enabled              (str)   --  Specifies whether the rule should be enabled or not.

                        pattern                 (str)   --  Specifies the file pattern for the error rule.

                        all_error_codes         (bool)  --  Specifies whether all error codes should be enabled.

                        from_error_code         (int)   --  Error code range&#39;s lower value.
                        Valid values are all non negative integers.

                        to_error_code           (int)   --  Error code range&#39;s upper value.
                        Valid values are all non negative integers higher larger the from_ec value.

                        skip_reporting_error    (bool)  --  Specifies if error codes need to be skipped from being reported.

                    Example:
                            {
                             WINDOWS : { &#39;rule_1&#39;: { &#39;appGroupName&#39;: WINDOWS,
                                                     &#39;pattern&#39;: &#34;*&#34;,
                                                     &#39;all_error_codes&#39;: False,
                                                     &#39;from_error_code&#39;: 1,
                                                     &#39;to_error_code&#39;: 2,
                                                     &#39;job_decision&#39;: 0,
                                                     &#39;is_enabled&#39;: True,
                                                     &#39;skip_reporting_error&#39;: False
                                                   },
                                         &#39;rule_2&#39; : { ......}
                                       }
                            }

            Returns:
                None

            Raises:
                Exception in case of invalid key/value pair(s).
        &#34;&#34;&#34;

        final_str = &#34;&#34;
        old_values = []

        for app_group, rules_dict in rules_arg.items():
            assert (app_group.name in [i.name for i in ApplicationGroup])

            # FETCH ALL EXISTING RULES ON THE COMMCELL FOR THE APPLICATION
            # GROUP IN QUESTION
            existing_error_rules = self._get_error_rules(app_group)

            for rule_name, rule in rules_dict.items():
                assert isinstance(
                    rule[&#39;pattern&#39;], str) and isinstance(
                    rule[&#39;all_error_codes&#39;], bool) and isinstance(
                    rule[&#39;skip_reporting_error&#39;], int) and isinstance(
                    rule[&#39;from_error_code&#39;], int) and isinstance(
                    rule[&#39;to_error_code&#39;], int) and isinstance(
                    rule[&#39;job_decision&#39;], int) and rule[&#39;job_decision&#39;] in range(
                    0, 3) and isinstance(
                    rule[&#39;is_enabled&#39;], bool), &#34;Invalid key value pairs provided.&#34;

                rule_dict = {k:v for k,v in rule.items() if k != &#39;appGroupName&#39;}

                # GET RULE STRING FOR EACH RULE DICTIONARY PROVIDED IN THE ARGUMENT
                new_rule_str = self._get_xml_for_rule(rule_dict)

                # IF RULE NAME NOT PRESENT IN OUR INTERNAL STRUCTURE, IT MEANS USER IS ADDING NEW RULE
                if rule_name not in list(self.rule_dict.keys()):
                    self.rule_dict[rule_name] = {&#39;new_value&#39;: new_rule_str, &#39;old_value&#39;: new_rule_str}
                    final_str = &#39;&#39;.join((final_str, new_rule_str))

                # ELSE CHECK IF THE RULE&#39;S VALUE REMAINS SAME AND IF IT DOES, WE SIMPLY CONTINUE AND STORE EXISTING VALUE
                elif new_rule_str == self.rule_dict[rule_name][&#39;old_value&#39;]:
                    final_str = &#39;&#39;.join((final_str, self.rule_dict[rule_name][&#39;old_value&#39;]))

                # ELSE RULE IS BEING UPDATED, STORE NEW VALUE IN FINAL STRING AND PRESERVE OLD VALUE AS WELL
                else:
                    self.rule_dict[rule_name][&#39;old_value&#39;] = self.rule_dict[rule_name][&#39;new_value&#39;]
                    self.rule_dict[rule_name][&#39;new_value&#39;] = new_rule_str
                    final_str = &#39;&#39;.join((final_str, new_rule_str))

            # NOW GO THROUGH ALL EXISTING RULES ON CS AND EITHER PRESERVE OR UPDATE IT
            # PREPARE A LIST OF ALL OLD VALUES FIRST
            for rule_name, values in self.rule_dict.items():
                old_values.extend([value for value_type, value in values.items() if value_type == &#39;old_value&#39;])
            for existing_error_rule in existing_error_rules:
                existing_rule_dict = {&#39;pattern&#39;: existing_error_rule[&#39;pattern&#39;],
                                      &#39;all_error_codes&#39;: existing_error_rule[&#39;errorCode&#39;][&#39;allErrorCodes&#39;],
                                      &#39;skip_reporting_error&#39;: existing_error_rule[&#39;errorCode&#39;][&#39;skipReportingError&#39;],
                                      &#39;from_error_code&#39;: existing_error_rule[&#39;errorCode&#39;][&#39;fromValue&#39;],
                                      &#39;to_error_code&#39;: existing_error_rule[&#39;errorCode&#39;][&#39;toValue&#39;],
                                      &#39;job_decision&#39;: existing_error_rule[&#39;jobDecision&#39;],
                                      &#39;is_enabled&#39;: existing_error_rule[&#39;isEnabled&#39;]}

                existing_rule_str = self._get_xml_for_rule(existing_rule_dict)
                # AN EXISTING RULE THAT HAS NOT BEEN UPDATED AND IS NOT ADDED BY THE TEST CASE OR THROUGH AUTOMATION.
                # IN OTHER WORDS, AN EXISTING RULE THAT WAS ADDED OUTSIDE OF THE SCOPE OF THE TEST CASE
                if existing_rule_str not in old_values:
                    final_str = &#39;&#39;.join((final_str, existing_rule_str))

        # NEED TO ADD SUPPORT FOR UPDATION OF ERROR RULES FOR MULTIPLE iDAs SIMULTANEOUSLY
        xml_body = self.xml_body.format(commcell_id=self.commcell.commcell_id,
                                        commserv_name=self.commcell.commserv_name,
                                        enable_flag_ida=1,
                                        app_group_name=app_group,
                                        final_str=final_str)

        xml_body = &#39;&#39;.join(i.lstrip().rstrip() for i in xml_body.split(&#34;\n&#34;))
        self.commcell.qoperation_execute(xml_body)

    def enable(self, app_group):
        &#34;&#34;&#34;Enables the job error control rules for the specified Application Group Type.
            Args:
                app_group   (str)   --  The iDA for which the enable flag needs to be set.
                Currently supported values are APPGRP_WindowsFileSystemIDA.

            Returns:
                None

            Raises:
                None

        &#34;&#34;&#34;
        return self._modify_job_status_on_errors(app_group, enable_flag=True)

    def disable(self, app_group):
        &#34;&#34;&#34;Disables the job error control rules for the specified Application Group Type.
            Args:
                app_group   (str)   --  The iDA for which the enable flag needs to be set.
                Currently supported values are APPGRP_WindowsFileSystemIDA.

            Returns:
                None

            Raises:
                None
        &#34;&#34;&#34;
        return self._modify_job_status_on_errors(app_group, enable_flag=False)

    def _modify_job_status_on_errors(self, app_group, enable_flag):
        &#34;&#34;&#34;To enable or disable job status on errors.
            Args:
                app_group   (str)   --  The iDA for which the enable flag needs to be set.
                Currently supported values are APPGRP_WindowsFileSystemIDA.

                enable_flag (bool)  --  Enables and disables job status on errors.
            Returns:
                None

            Raises:
                None
        &#34;&#34;&#34;

        # FETCHING ALL EXISTING RULES
        error_rules = self._get_error_rules(app_group)

        # FOR EVERY RULE IN RULE LIST
        for rule in error_rules:
            rule_str = self.error_rule_str.format(pattern=rule[&#39;pattern&#39;],
                                                  all_error_codes=rule[&#39;errorCode&#39;][&#39;allErrorCodes&#39;],
                                                  from_error_code=rule[&#39;errorCode&#39;][&#39;fromValue&#39;],
                                                  to_error_code=rule[&#39;errorCode&#39;][&#39;toValue&#39;],
                                                  job_decision=rule[&#39;jobDecision&#39;],
                                                  is_enabled=rule[&#39;isEnabled&#39;],
                                                  skip_reporting_error=rule[&#39;errorCode&#39;][&#39;skipReportingError&#39;])

            final_str = &#39;&#39;.join((final_str, rule_str))

        xml_body = self.xml_body.format(commcell_id=self.commcell.commcell_id,
                                        commserv_name=self.commcell.commserv_name,
                                        enable_flag_ida=1 if enable_flag else 0,
                                        final_str=final_str)

        xml_body = &#39;&#39;.join(i.lstrip().rstrip() for i in xml_body.split(&#34;\n&#34;))
        return self.commcell.qoperation_execute(xml_body)

    def _get_error_rules(self, app_group):
        &#34;&#34;&#34;
        Returns the error rules set on the CS in the form of a dictionary.

        Args:
            app_group   (str)   --  The iDA for which the enable flag needs to be set.
                Currently supported values are APPGRP_WindowsFileSystemIDA.

        Returns:
            list    -   A list of error rules. Each rule will be a dictionary of key value pairs for pattern,
            error code from value, error code to value etc.

        Raises:
            None
        &#34;&#34;&#34;

        rule_list = []

        xml_body = f&#34;&#34;&#34;
        &lt;App_GetJobErrorDecisionReq&gt;
        &lt;entity _type_=&#34;1&#34; commCellId=&#34;{self.commcell.commcell_id}&#34; commCellName=&#34;{self.commcell.commserv_name}&#34;/&gt;
        &lt;/App_GetJobErrorDecisionReq&gt;&#34;&#34;&#34;

        xml_body = &#39;&#39;.join(i.lstrip().rstrip() for i in xml_body.split(&#34;\n&#34;))
        error_rules = self.commcell.qoperation_execute(xml_body)

        if any(error_rules):

            ida_rule_lists = error_rules[&#39;jobErrorRuleList&#39;][&#39;idaRuleList&#39;]
            for ida_rule_list in ida_rule_lists:
                # HARD CODED FOR WINDOWS SUPPORT ONLY
                if ida_rule_list[&#39;ida&#39;][&#39;appGroupName&#39;] == app_group:
                    try:
                        rule_list = ida_rule_list[&#39;ruleList&#39;][&#39;ruleList&#39;]
                    except Exception:
                        pass

        return rule_list</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.job.Job"><code class="flex name class">
<span>class <span class="ident">Job</span></span>
<span>(</span><span>commcell_object, job_id)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for performing client operations for a specific client.</p>
<p>Initialise the Job class instance.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the Commcell class</p>
<p>job_id
(str / int)
&ndash;
id of the job</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the Job class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if job id is not an integer</p>
<pre><code>if job is not a valid job, i.e., does not exist in the Commcell
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L1968-L2818" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Job(object):
    &#34;&#34;&#34;Class for performing client operations for a specific client.&#34;&#34;&#34;

    def __init__(self, commcell_object, job_id):
        &#34;&#34;&#34;Initialise the Job class instance.

            Args:
                commcell_object     (object)        --  instance of the Commcell class

                job_id              (str / int)     --  id of the job

            Returns:
                object  -   instance of the Job class

            Raises:
                SDKException:
                    if job id is not an integer

                    if job is not a valid job, i.e., does not exist in the Commcell

        &#34;&#34;&#34;
        try:
            int(job_id)
        except ValueError:
            raise SDKException(&#39;Job&#39;, &#39;101&#39;)

        self._commcell_object = commcell_object

        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_

        self._job_id = str(job_id)

        self._JOB = self._services[&#39;JOB&#39;] % (self.job_id)

        if not self._is_valid_job():
            raise SDKException(&#39;Job&#39;, &#39;102&#39;, f&#39;No job exists with the specified Job ID: {self.job_id}&#39;)

        self._JOB_DETAILS = self._services[&#39;JOB_DETAILS&#39;]
        self.ADVANCED_JOB_DETAILS = AdvancedJobDetailType
        self._SUSPEND = self._services[&#39;SUSPEND_JOB&#39;] % self.job_id
        self._RESUME = self._services[&#39;RESUME_JOB&#39;] % self.job_id
        self._KILL = self._services[&#39;KILL_JOB&#39;] % self.job_id
        self._RESUBMIT = self._services[&#39;RESUBMIT_JOB&#39;] % self.job_id
        self._JOB_EVENTS = self._services[&#39;JOB_EVENTS&#39;] % self.job_id
        self._JOB_TASK_DETAILS = self._services[&#39;JOB_TASK_DETAILS&#39;]

        self._client_name = None
        self._agent_name = None
        self._instance_name = None
        self._backupset_name = None
        self._subclient_name = None
        self._job_type = None
        self._backup_level = None
        self._start_time = None
        self._end_time = None
        self._delay_reason = None
        self._pending_reason = None
        self._status = None
        self._phase = None
        self._summary = None
        self._details = None
        self._task_details = None

        self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.

            Returns:
                str     -   string for instance of this class

        &#34;&#34;&#34;
        representation_string = &#39;Job class instance for job id: &#34;{0}&#34;&#39;
        return representation_string.format(self.job_id)

    def _is_valid_job(self):
        &#34;&#34;&#34;Checks if the job submitted with the job id is a valid job or not.

            Returns:
                bool    -   boolean that represents whether the job is valid or not

        &#34;&#34;&#34;
        for _ in range(10):
            try:
                self._get_job_summary()
                return True
            except SDKException as excp:
                if excp.exception_module == &#39;Job&#39; and excp.exception_id == &#39;104&#39;:
                    time.sleep(1.5)
                    continue
                else:
                    raise excp

        return False

    def _get_job_summary(self):
        &#34;&#34;&#34;Gets the properties of this job.

            Returns:
                dict    -   dict that contains the summary of this job

            Raises:
                SDKException:
                    if no record found for this job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        attempts = 0
        while attempts &lt; 5:  # Retrying to ignore the transient case when no jobs are found
            flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._JOB)
            attempts += 1

            if flag:
                if response.json():
                    if response.json().get(&#39;totalRecordsWithoutPaging&#39;, 0) == 0:
                        time.sleep(2**attempts)
                        continue

                    if &#39;jobs&#39; in response.json():
                        for job in response.json()[&#39;jobs&#39;]:
                            return job[&#39;jobSummary&#39;]
                else:
                    if attempts &gt; 4:
                        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
                    time.sleep(20)

            else:
                if attempts &gt; 4:
                    response_string = self._update_response_(response.text)
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
                time.sleep(20)

        raise SDKException(&#39;Job&#39;, &#39;104&#39;)

    def _get_job_details(self):
        &#34;&#34;&#34;Gets the detailed properties of this job.

            Returns:
                dict    -   dict consisting of the detailed properties of the job

            Raises:
                SDKException:
                    if failed to get the job details

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        payload = {
            &#34;jobId&#34;: int(self.job_id),
            &#34;showAttempt&#34;: True
        }

        retry_count = 0

        while retry_count &lt; 5:  # Retrying to ignore the transient case when job details are not found
            flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._JOB_DETAILS, payload)
            retry_count += 1

            if flag:
                if response.json():
                    if &#39;job&#39; in response.json():
                        return response.json()[&#39;job&#39;]
                    elif &#39;error&#39; in response.json():
                        error_code = response.json()[&#39;error&#39;][&#39;errList&#39;][0][&#39;errorCode&#39;]
                        error_message = response.json()[&#39;error&#39;][&#39;errList&#39;][0][&#39;errLogMessage&#39;]

                        raise SDKException(
                            &#39;Job&#39;,
                            &#39;105&#39;,
                            &#39;Error Code: &#34;{0}&#34;\nError Message: &#34;{1}&#34;&#39;.format(error_code, error_message)
                        )
                    else:
                        raise SDKException(&#39;Job&#39;, &#39;106&#39;, &#39;Response JSON: {0}&#39;.format(response.json()))
                else:
                    if retry_count &gt; 4:
                        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
                    time.sleep(20)
            else:
                if retry_count &gt; 4:
                    response_string = self._update_response_(response.text)
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
                time.sleep(20)

        raise SDKException(&#39;Response&#39;, &#39;102&#39;)

    def _get_job_task_details(self):
        &#34;&#34;&#34;Gets the task details of this job.

            Returns:
                dict    -   dict consisting of the task details of the job

            Raises:
                SDKException:
                    if failed to get the job task details

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        retry_count = 0

        while retry_count &lt; 5:  # Retrying to ignore the transient case when job task details are not found
            flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._JOB_TASK_DETAILS % self.job_id)
            retry_count += 1

            if flag:
                if response.json():
                    if &#39;taskInfo&#39; in response.json():
                        return response.json()[&#39;taskInfo&#39;]
                    elif &#39;error&#39; in response.json():
                        error_code = response.json()[&#39;error&#39;][&#39;errList&#39;][0][&#39;errorCode&#39;]
                        error_message = response.json()[&#39;error&#39;][&#39;errList&#39;][0][&#39;errorMessage&#39;]

                        raise SDKException(
                            &#39;Job&#39;,
                            &#39;105&#39;,
                            &#39;Error Code: &#34;{0}&#34;\nError Message: &#34;{1}&#34;&#39;.format(error_code, error_message)
                        )
                    else:
                        raise SDKException(&#39;Job&#39;, &#39;106&#39;, &#39;Response JSON: {0}&#39;.format(response.json()))
                else:
                    if retry_count &gt; 4:
                        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
                    time.sleep(20)
            else:
                if retry_count &gt; 4:
                    response_string = self._update_response_(response.text)
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
                time.sleep(20)

        raise SDKException(&#39;Response&#39;, &#39;102&#39;)

    def _initialize_job_properties(self):
        &#34;&#34;&#34;Initializes the common properties for the job.
            Adds the client, agent, backupset, subclient name to the job object.

        &#34;&#34;&#34;
        self._summary = self._get_job_summary()
        self._details = self._get_job_details()

        self._status = self._summary[&#39;status&#39;]

        self._start_time = time.strftime(
            &#39;%Y-%m-%d %H:%M:%S&#39;, time.gmtime(self._summary[&#39;jobStartTime&#39;])
        )

    def _wait_for_status(self, status, timeout=6):
        &#34;&#34;&#34;Waits for 6 minutes or till the job status is changed to given status,
            whichever is earlier.

            Args:
                status  (str)   --  Job Status

                timeout (int)   --  timeout interval in mins

            Returns:
                None

        &#34;&#34;&#34;
        start_time = time.time()
        current_job_status = self.status
        current_job_status = current_job_status if current_job_status else self.state
        while current_job_status.lower() != status.lower():
            if (self.is_finished is True) or (time.time() - start_time &gt; (timeout * 60)):
                break

            time.sleep(3)
            current_job_status = self.status

    def wait_for_completion(self, timeout=30, **kwargs):
        &#34;&#34;&#34;Waits till the job is not finished; i.e.; till the value of job.is_finished is not True.
            Kills the job and exits, if the job has been in Pending / Waiting state for more than
            the timeout value.

            In case of job failure job status and failure reason can be obtained
                using status and delay_reason property

            Args:
                timeout     (int)   --  minutes after which the job should be killed and exited,
                        if the job has been in Pending / Waiting state
                    default: 30

                **kwargs    (str)   --  accepted optional arguments

                    return_timeout  (int)   -- minutes after which the method will return False.

            Returns:
                bool    -   boolean specifying whether the job had finished or not
                    True    -   if the job had finished successfully

                    False   -   if the job was killed/failed

        &#34;&#34;&#34;
        start_time = actual_start_time = time.time()
        pending_time = 0
        waiting_time = 0
        previous_status = None
        return_timeout = kwargs.get(&#39;return_timeout&#39;)

        status_list = [&#39;pending&#39;, &#39;waiting&#39;]

        while not self.is_finished:
            time.sleep(30)

            if return_timeout and ((time.time() - actual_start_time) / 60) &gt; return_timeout:
                return False

            # get the current status of the job
            status = self.status
            status = status.lower() if status else self.state.lower()

            # set the value of start time as current time
            # if the current status is pending / waiting but the previous status was not
            # also if the current status is pending / waiting and same as previous,
            # then don&#39;t update the value of start time
            if status in status_list and previous_status not in status_list:
                start_time = time.time()

            if status == &#39;pending&#39;:
                pending_time = (time.time() - start_time) / 60
            else:
                pending_time = 0

            if status == &#39;waiting&#39;:
                waiting_time = (time.time() - start_time) / 60
            else:
                waiting_time = 0

            if pending_time &gt; timeout or waiting_time &gt; timeout:
                self.kill()
                break

            # set the value of previous status as the value of current status
            previous_status = status
        else:
            return self._status.lower() not in [&#34;failed&#34;, &#34;killed&#34;, &#34;failed to start&#34;]

        return False

    @property
    def is_finished(self):
        &#34;&#34;&#34;Checks whether the job has finished or not.

            Returns:
                bool    -   boolean that represents whether the job has finished or not

        &#34;&#34;&#34;
        self._summary = self._get_job_summary()
        self._details = self._get_job_details()

        self._status = self._summary[&#39;status&#39;]

        if self._summary[&#39;lastUpdateTime&#39;] != 0:
            self._end_time = time.strftime(
                &#39;%Y-%m-%d %H:%M:%S&#39;, time.gmtime(self._summary[&#39;lastUpdateTime&#39;])
            )

        return (&#39;completed&#39; in self._status.lower() or
                &#39;killed&#39; in self._status.lower() or
                &#39;committed&#39; in self._status.lower() or
                &#39;failed&#39; in self._status.lower())

    @property
    def client_name(self):
        &#34;&#34;&#34;Treats the client name as a read-only attribute.&#34;&#34;&#34;
        if &#39;clientName&#39; in self._summary[&#39;subclient&#39;]:
            return self._summary[&#39;subclient&#39;][&#39;clientName&#39;]

    @property
    def agent_name(self):
        &#34;&#34;&#34;Treats the agent name as a read-only attribute.&#34;&#34;&#34;
        if &#39;appName&#39; in self._summary[&#39;subclient&#39;]:
            return self._summary[&#39;subclient&#39;][&#39;appName&#39;]

    @property
    def instance_name(self):
        &#34;&#34;&#34;Treats the instance name as a read-only attribute.&#34;&#34;&#34;
        if &#39;instanceName&#39; in self._summary[&#39;subclient&#39;]:
            return self._summary[&#39;subclient&#39;][&#39;instanceName&#39;]

    @property
    def backupset_name(self):
        &#34;&#34;&#34;Treats the backupset name as a read-only attribute.&#34;&#34;&#34;
        if &#39;backupsetName&#39; in self._summary[&#39;subclient&#39;]:
            return self._summary[&#39;subclient&#39;][&#39;backupsetName&#39;]

    @property
    def subclient_name(self):
        &#34;&#34;&#34;Treats the subclient name as a read-only attribute.&#34;&#34;&#34;
        if &#39;subclientName&#39; in self._summary[&#39;subclient&#39;]:
            return self._summary[&#39;subclient&#39;][&#39;subclientName&#39;]

    @property
    def status(self):
        &#34;&#34;&#34;Treats the job status as a read-only attribute.
           http://documentation.commvault.com/commvault/v11/article?p=features/rest_api/operations/get_job.htm
           please refer status section in above doc link for complete list of status available&#34;&#34;&#34;
        self.is_finished
        return self._status

    @property
    def job_id(self):
        &#34;&#34;&#34;Treats the job id as a read-only attribute.&#34;&#34;&#34;
        return self._job_id

    @property
    def job_type(self):
        &#34;&#34;&#34;Treats the job type as a read-only attribute.&#34;&#34;&#34;
        return self._summary[&#39;jobType&#39;]

    @property
    def backup_level(self):
        &#34;&#34;&#34;Treats the backup level as a read-only attribute.&#34;&#34;&#34;
        if &#39;backupLevelName&#39; in self._summary:
            return self._summary[&#39;backupLevelName&#39;]

    @property
    def start_time(self):
        &#34;&#34;&#34;Treats the start time as a read-only attribute.&#34;&#34;&#34;
        return self._start_time
    
    @property
    def start_timestamp(self):
        &#34;&#34;&#34;Treats the unix start time as a read-only attribute.&#34;&#34;&#34;
        return self._summary[&#39;jobStartTime&#39;]

    @property
    def end_timestamp(self):
        &#34;&#34;&#34;Treats the unix end time as a read-only attribute&#34;&#34;&#34;
        return self._summary[&#39;jobEndTime&#39;]

    @property
    def end_time(self):
        &#34;&#34;&#34;Treats the end time as a read-only attribute.&#34;&#34;&#34;
        return self._end_time

    @property
    def delay_reason(self):
        &#34;&#34;&#34;Treats the job delay reason as a read-only attribute.&#34;&#34;&#34;
        self.is_finished
        progress_info = self._details[&#39;jobDetail&#39;][&#39;progressInfo&#39;]
        if &#39;reasonForJobDelay&#39; in progress_info and progress_info[&#39;reasonForJobDelay&#39;]:
            return progress_info[&#39;reasonForJobDelay&#39;]

    @property
    def pending_reason(self):
        &#34;&#34;&#34;Treats the job pending reason as a read-only attribute.&#34;&#34;&#34;
        self.is_finished
        if &#39;pendingReason&#39; in self._summary and self._summary[&#39;pendingReason&#39;]:
            return self._summary[&#39;pendingReason&#39;]

    @property
    def phase(self):
        &#34;&#34;&#34;Treats the job current phase as a read-only attribute.&#34;&#34;&#34;
        self.is_finished
        if &#39;currentPhaseName&#39; in self._summary:
            return self._summary[&#39;currentPhaseName&#39;]

    @property
    def attempts(self):
        &#34;&#34;&#34;Returns job attempts data as read-only attribute&#34;&#34;&#34;
        self.is_finished
        return self._details.get(&#39;jobDetail&#39;, {}).get(&#39;attemptsInfo&#39;, {})

    @property
    def summary(self):
        &#34;&#34;&#34;Treats the job full summary as a read-only attribute.&#34;&#34;&#34;
        self.is_finished
        return self._summary

    @property
    def username(self):
        &#34;&#34;&#34;Treats the username as a read-only attribute.&#34;&#34;&#34;
        return self._summary[&#39;userName&#39;][&#39;userName&#39;]

    @property
    def userid(self):
        &#34;&#34;&#34;Treats the userid as a read-only attribute.&#34;&#34;&#34;
        return self._summary[&#39;userName&#39;][&#39;userId&#39;]

    @property
    def details(self):
        &#34;&#34;&#34;Treats the job full details as a read-only attribute.&#34;&#34;&#34;
        self.is_finished
        return self._details

    @property
    def size_of_application(self):
        &#34;&#34;&#34;Treats the size of application as a read-only attribute.&#34;&#34;&#34;
        if &#39;sizeOfApplication&#39; in self._summary:
            return self._summary[&#39;sizeOfApplication&#39;]

    @property
    def media_size(self):
        &#34;&#34;&#34;
        Treats the size of media as a read-only attribute
        Returns:
            integer - size of media or data written
        &#34;&#34;&#34;
        return self._summary.get(&#39;sizeOfMediaOnDisk&#39;, 0)

    @property
    def num_of_files_transferred(self):
        &#34;&#34;&#34;Treats the number of files transferred as a read-only attribute.&#34;&#34;&#34;
        self.is_finished
        return self._details[&#39;jobDetail&#39;][&#39;progressInfo&#39;][&#39;numOfFilesTransferred&#39;]

    @property
    def state(self):
        &#34;&#34;&#34;Treats the job state as a read-only attribute.&#34;&#34;&#34;
        self.is_finished
        return self._details[&#39;jobDetail&#39;][&#39;progressInfo&#39;][&#39;state&#39;]

    @property
    def task_details(self):
        &#34;&#34;&#34;Returns: (dict) A dictionary of job task details&#34;&#34;&#34;
        if not self._task_details:
            self._task_details = self._get_job_task_details()
        return self._task_details

    def pause(self, wait_for_job_to_pause=False, timeout=6):
        &#34;&#34;&#34;Suspends the job.

            Args:
                wait_for_job_to_pause   (bool)  --  wait till job status is changed to Suspended

                    default: False

                timeout (int)                   --  timeout interval to wait for job to move to suspend state

            Raises:
                SDKException:
                    if failed to suspend job

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._SUSPEND)

        self.is_finished

        if flag:
            if response.json():
                if &#39;errors&#39; in response.json():
                    error_list = response.json()[&#39;errors&#39;][0][&#39;errList&#39;][0]
                    error_code = error_list[&#39;errorCode&#39;]
                    error_message = error_list[&#39;errLogMessage&#39;].strip()
                else:
                    error_code = response.json().get(&#39;errorCode&#39;, 0)
                    error_message = response.json().get(&#39;errorMessage&#39;, &#39;nil&#39;)

                if error_code != 0:
                    raise SDKException(
                        &#39;Job&#39;, &#39;102&#39;, &#39;Job suspend failed\nError: &#34;{0}&#34;&#39;.format(error_message)
                    )
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        if wait_for_job_to_pause is True:
            self._wait_for_status(&#34;SUSPENDED&#34;, timeout=timeout)

    def resume(self, wait_for_job_to_resume=False):
        &#34;&#34;&#34;Resumes the job.

            Args:
                wait_for_job_to_resume  (bool)  --  wait till job status is changed to Running

                    default: False

            Raises:
                SDKException:
                    if failed to resume job

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._RESUME)

        self.is_finished

        if flag:
            if response.json():
                if &#39;errors&#39; in response.json():
                    error_list = response.json()[&#39;errors&#39;][0][&#39;errList&#39;][0]
                    error_code = error_list[&#39;errorCode&#39;]
                    error_message = error_list[&#39;errLogMessage&#39;].strip()
                else:
                    error_code = response.json().get(&#39;errorCode&#39;, 0)
                    error_message = response.json().get(&#39;errorMessage&#39;, &#39;nil&#39;)

                if error_code != 0:
                    raise SDKException(
                        &#39;Job&#39;, &#39;102&#39;, &#39;Job resume failed\nError: &#34;{0}&#34;&#39;.format(error_message)
                    )
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        if wait_for_job_to_resume is True:
            self._wait_for_status(&#34;RUNNING&#34;)

    def resubmit(self, start_suspended=None):
        &#34;&#34;&#34;Resubmits the job

        Args:
            start_suspended (bool)  -   whether to start the new job in suspended state or not
                                        default: None, the new job starts same as this job started

        Returns:
            object  -   Job class object for the given job id

        Raises:
                SDKException:
                    if job is already running

                    if response is not success

        &#34;&#34;&#34;
        if start_suspended not in [True, False, None]:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

        if not self.is_finished:
            raise SDKException(&#39;Job&#39;, &#39;102&#39;, &#39;Cannot resubmit the Job, the Job is still running&#39;)

        url = self._RESUBMIT
        if start_suspended is not None:
            url += f&#39;?startInSuspendedState={start_suspended}&#39;
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, url)

        if flag:
            if response.json():
                if &#39;errors&#39; in response.json():
                    error_list = response.json()[&#39;errors&#39;][0][&#39;errList&#39;][0]
                    error_code = error_list[&#39;errorCode&#39;]
                    error_message = error_list[&#39;errLogMessage&#39;].strip()
                else:
                    error_code = response.json().get(&#39;errorCode&#39;, 0)
                    error_message = response.json().get(&#39;errorMessage&#39;, &#39;nil&#39;)

                if error_code != 0:
                    raise SDKException(
                        &#39;Job&#39;, &#39;102&#39;, &#39;Resubmitting job failed\nError: &#34;{0}&#34;&#39;.format(error_message)
                    )
            return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def kill(self, wait_for_job_to_kill=False):
        &#34;&#34;&#34;Kills the job.

            Args:
                wait_for_job_to_kill    (bool)  --  wait till job status is changed to Killed

                    default: False

            Raises:
                SDKException:
                    if failed to kill job

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._KILL)

        self.is_finished

        if flag:
            if response.json():
                if &#39;errors&#39; in response.json():
                    error_list = response.json()[&#39;errors&#39;][0][&#39;errList&#39;][0]
                    error_code = error_list[&#39;errorCode&#39;]
                    error_message = error_list[&#39;errLogMessage&#39;].strip()
                else:
                    error_code = response.json().get(&#39;errorCode&#39;, 0)
                    error_message = response.json().get(&#39;errorMessage&#39;, &#39;nil&#39;)

                if error_code != 0:
                    raise SDKException(
                        &#39;Job&#39;, &#39;102&#39;, &#39;Job kill failed\nError: &#34;{0}&#34;&#39;.format(error_message)
                    )
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        if wait_for_job_to_kill is True:
            self._wait_for_status(&#34;KILLED&#34;)

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the Job.&#34;&#34;&#34;
        self._initialize_job_properties()
        self.is_finished

    def advanced_job_details(self, info_type):
        &#34;&#34;&#34;Returns advanced properties for the job

            Args:
                infoType    (object)  --  job detail type to be passed from AdvancedJobDetailType
                enum from the constants

            Returns:
                dict -  dictionary with advanced details of the job info type given

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        if not isinstance(info_type, AdvancedJobDetailType):
            raise SDKException(&#39;Response&#39;, &#39;107&#39;)
        url = self._services[&#39;ADVANCED_JOB_DETAIL_TYPE&#39;] % (self.job_id, info_type.value)
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, url)

        if flag:
            if response.json():
                response = response.json()

                if response.get(&#39;errorCode&#39;, 0) != 0:
                    error_message = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Failed to fetch details.\nError: &#34;{0}&#34;&#39;.format(error_message)
                    raise SDKException(&#39;Job&#39;, &#39;102&#39;, o_str)

                return response
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def get_events(self):
        &#34;&#34;&#34; gets the commserv events associated with this job

            Args:

                None

            Returns:

                list - list of job events

                    Example : [
                        {
                            &#34;severity&#34;: 3,
                            &#34;eventCode&#34;: &#34;318769020&#34;,
                            &#34;jobId&#34;: 4547,
                            &#34;acknowledge&#34;: 0,
                            &#34;eventCodeString&#34;: &#34;19:1916&#34;,
                            &#34;subsystem&#34;: &#34;JobManager&#34;,
                            &#34;description&#34;: &#34;Data Analytics operation has completed with one or more errors.&#34;,
                            &#34;id&#34;: 25245,
                            &#34;timeSource&#34;: 1600919001,
                            &#34;type&#34;: 0,
                            &#34;clientEntity&#34;: {
                                &#34;clientId&#34;: 2,
                                &#34;clientName&#34;: &#34;xyz&#34;,
                                &#34;displayName&#34;: &#34;xyz&#34;
                            }
                        },
                        {
                            &#34;severity&#34;: 6,
                            &#34;eventCode&#34;: &#34;318767961&#34;,
                            &#34;jobId&#34;: 4547,
                            &#34;acknowledge&#34;: 0,
                            &#34;eventCodeString&#34;: &#34;19:857&#34;,
                            &#34;subsystem&#34;: &#34;clBackup&#34;,
                            &#34;description&#34;: &#34;Failed to send some items to Index Engine&#34;,
                            &#34;id&#34;: 25244,
                            &#34;timeSource&#34;: 1600918999,
                            &#34;type&#34;: 0,
                            &#34;clientEntity&#34;: {
                                &#34;clientId&#34;: 33,
                                &#34;clientName&#34;: &#34;xyz&#34;,
                                &#34;displayName&#34;: &#34;xyz&#34;
                            }
                        }
                    ]

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._JOB_EVENTS)
        if flag:
            if response.json() and &#39;commservEvents&#39; in response.json():
                    return response.json()[&#39;commservEvents&#39;]
            raise SDKException(&#39;Job&#39;, &#39;104&#39;)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def get_vm_list(self):
        &#34;&#34;&#34;
        Gets the list of all VMs associated to the job
        Returns: list of VM dictionaries
            VM: {
               &#34;Size&#34;:0,
               &#34;AverageThroughput&#34;:0,
               &#34;UsedSpace&#34;:0,
               &#34;ArchivedByCurrentJob&#34;:false,
               &#34;jobID&#34;:0,
               &#34;CBTStatus&#34;:&#34;&#34;,
               &#34;BackupType&#34;:0,
               &#34;totalFiles&#34;:0,
               &#34;Status&#34;:2,
               &#34;CurrentThroughput&#34;:0,
               &#34;Agent&#34;:&#34;proxy&#34;,
               &#34;lastSyncedBkpJob&#34;:0,
               &#34;GUID&#34;:&#34;live sync pair guid&#34;,
               &#34;HardwareVersion&#34;:&#34;vm h/w&#34;,
               &#34;restoredSize&#34;:1361912,
               &#34;FailureReason&#34;:&#34;&#34;,
               &#34;BackupStartTime&#34;:0,
               &#34;TransportMode&#34;:&#34;nbd&#34;,
               &#34;projectId&#34;:&#34;&#34;,
               &#34;syncStatus&#34;:3,
               &#34;PoweredOffSince&#34;:0,
               &#34;OperatingSystem&#34;:&#34;Microsoft Windows Server 2012 (64-bit)&#34;,
               &#34;backupLevel&#34;:0,
               &#34;destinationVMName&#34;:&#34;drvm1&#34;,
               &#34;successfulCIedFiles&#34;:0,
               &#34;GuestSize&#34;:0,
               &#34;failedCIedFiles&#34;:0,
               &#34;vmName&#34;:&#34;vm1&#34;,
               &#34;ToolsVersion&#34;:&#34;Not running&#34;,
               &#34;clientId&#34;:3280,
               &#34;Host&#34;:&#34;*******&#34;,
               &#34;StubStatus&#34;:0,
               &#34;BackupEndTime&#34;:0,
               &#34;PoweredOffByCurrentJob&#34;:false
            }
        &#34;&#34;&#34;
        return self.details.get(&#39;jobDetail&#39;, {}).get(&#39;clientStatusInfo&#39;, {}).get(&#39;vmStatus&#39;, [])

    def get_child_jobs(self):
        &#34;&#34;&#34; Get the child jobs details for the current job
        Returns:
                _jobs_list          (list):     List of child jobs

        &#34;&#34;&#34;
        _jobs_list = []
        if self.details.get(&#39;jobDetail&#39;, {}).get(&#39;clientStatusInfo&#39;, {}).get(&#39;vmStatus&#39;):
            for _job in self.details[&#39;jobDetail&#39;][&#39;clientStatusInfo&#39;][&#39;vmStatus&#39;]:
                _jobs_list.append(_job)
            return _jobs_list
        else:
            return None</code></pre>
</details>
<h3>Subclasses</h3>
<ul class="hlist">
<li><a title="cvpysdk.drorchestration.drjob.DRJob" href="drorchestration/drjob.html#cvpysdk.drorchestration.drjob.DRJob">DRJob</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.job.Job.agent_name"><code class="name">var <span class="ident">agent_name</span></code></dt>
<dd>
<div class="desc"><p>Treats the agent name as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L2344-L2348" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def agent_name(self):
    &#34;&#34;&#34;Treats the agent name as a read-only attribute.&#34;&#34;&#34;
    if &#39;appName&#39; in self._summary[&#39;subclient&#39;]:
        return self._summary[&#39;subclient&#39;][&#39;appName&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.Job.attempts"><code class="name">var <span class="ident">attempts</span></code></dt>
<dd>
<div class="desc"><p>Returns job attempts data as read-only attribute</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L2434-L2438" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def attempts(self):
    &#34;&#34;&#34;Returns job attempts data as read-only attribute&#34;&#34;&#34;
    self.is_finished
    return self._details.get(&#39;jobDetail&#39;, {}).get(&#39;attemptsInfo&#39;, {})</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.Job.backup_level"><code class="name">var <span class="ident">backup_level</span></code></dt>
<dd>
<div class="desc"><p>Treats the backup level as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L2386-L2390" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def backup_level(self):
    &#34;&#34;&#34;Treats the backup level as a read-only attribute.&#34;&#34;&#34;
    if &#39;backupLevelName&#39; in self._summary:
        return self._summary[&#39;backupLevelName&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.Job.backupset_name"><code class="name">var <span class="ident">backupset_name</span></code></dt>
<dd>
<div class="desc"><p>Treats the backupset name as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L2356-L2360" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def backupset_name(self):
    &#34;&#34;&#34;Treats the backupset name as a read-only attribute.&#34;&#34;&#34;
    if &#39;backupsetName&#39; in self._summary[&#39;subclient&#39;]:
        return self._summary[&#39;subclient&#39;][&#39;backupsetName&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.Job.client_name"><code class="name">var <span class="ident">client_name</span></code></dt>
<dd>
<div class="desc"><p>Treats the client name as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L2338-L2342" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def client_name(self):
    &#34;&#34;&#34;Treats the client name as a read-only attribute.&#34;&#34;&#34;
    if &#39;clientName&#39; in self._summary[&#39;subclient&#39;]:
        return self._summary[&#39;subclient&#39;][&#39;clientName&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.Job.delay_reason"><code class="name">var <span class="ident">delay_reason</span></code></dt>
<dd>
<div class="desc"><p>Treats the job delay reason as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L2412-L2418" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def delay_reason(self):
    &#34;&#34;&#34;Treats the job delay reason as a read-only attribute.&#34;&#34;&#34;
    self.is_finished
    progress_info = self._details[&#39;jobDetail&#39;][&#39;progressInfo&#39;]
    if &#39;reasonForJobDelay&#39; in progress_info and progress_info[&#39;reasonForJobDelay&#39;]:
        return progress_info[&#39;reasonForJobDelay&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.Job.details"><code class="name">var <span class="ident">details</span></code></dt>
<dd>
<div class="desc"><p>Treats the job full details as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L2456-L2460" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def details(self):
    &#34;&#34;&#34;Treats the job full details as a read-only attribute.&#34;&#34;&#34;
    self.is_finished
    return self._details</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.Job.end_time"><code class="name">var <span class="ident">end_time</span></code></dt>
<dd>
<div class="desc"><p>Treats the end time as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L2407-L2410" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def end_time(self):
    &#34;&#34;&#34;Treats the end time as a read-only attribute.&#34;&#34;&#34;
    return self._end_time</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.Job.end_timestamp"><code class="name">var <span class="ident">end_timestamp</span></code></dt>
<dd>
<div class="desc"><p>Treats the unix end time as a read-only attribute</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L2402-L2405" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def end_timestamp(self):
    &#34;&#34;&#34;Treats the unix end time as a read-only attribute&#34;&#34;&#34;
    return self._summary[&#39;jobEndTime&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.Job.instance_name"><code class="name">var <span class="ident">instance_name</span></code></dt>
<dd>
<div class="desc"><p>Treats the instance name as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L2350-L2354" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def instance_name(self):
    &#34;&#34;&#34;Treats the instance name as a read-only attribute.&#34;&#34;&#34;
    if &#39;instanceName&#39; in self._summary[&#39;subclient&#39;]:
        return self._summary[&#39;subclient&#39;][&#39;instanceName&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.Job.is_finished"><code class="name">var <span class="ident">is_finished</span></code></dt>
<dd>
<div class="desc"><p>Checks whether the job has finished or not.</p>
<h2 id="returns">Returns</h2>
<p>bool
-
boolean that represents whether the job has finished or not</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L2315-L2336" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_finished(self):
    &#34;&#34;&#34;Checks whether the job has finished or not.

        Returns:
            bool    -   boolean that represents whether the job has finished or not

    &#34;&#34;&#34;
    self._summary = self._get_job_summary()
    self._details = self._get_job_details()

    self._status = self._summary[&#39;status&#39;]

    if self._summary[&#39;lastUpdateTime&#39;] != 0:
        self._end_time = time.strftime(
            &#39;%Y-%m-%d %H:%M:%S&#39;, time.gmtime(self._summary[&#39;lastUpdateTime&#39;])
        )

    return (&#39;completed&#39; in self._status.lower() or
            &#39;killed&#39; in self._status.lower() or
            &#39;committed&#39; in self._status.lower() or
            &#39;failed&#39; in self._status.lower())</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.Job.job_id"><code class="name">var <span class="ident">job_id</span></code></dt>
<dd>
<div class="desc"><p>Treats the job id as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L2376-L2379" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def job_id(self):
    &#34;&#34;&#34;Treats the job id as a read-only attribute.&#34;&#34;&#34;
    return self._job_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.Job.job_type"><code class="name">var <span class="ident">job_type</span></code></dt>
<dd>
<div class="desc"><p>Treats the job type as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L2381-L2384" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def job_type(self):
    &#34;&#34;&#34;Treats the job type as a read-only attribute.&#34;&#34;&#34;
    return self._summary[&#39;jobType&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.Job.media_size"><code class="name">var <span class="ident">media_size</span></code></dt>
<dd>
<div class="desc"><p>Treats the size of media as a read-only attribute</p>
<h2 id="returns">Returns</h2>
<p>integer - size of media or data written</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L2468-L2475" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def media_size(self):
    &#34;&#34;&#34;
    Treats the size of media as a read-only attribute
    Returns:
        integer - size of media or data written
    &#34;&#34;&#34;
    return self._summary.get(&#39;sizeOfMediaOnDisk&#39;, 0)</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.Job.num_of_files_transferred"><code class="name">var <span class="ident">num_of_files_transferred</span></code></dt>
<dd>
<div class="desc"><p>Treats the number of files transferred as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L2477-L2481" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def num_of_files_transferred(self):
    &#34;&#34;&#34;Treats the number of files transferred as a read-only attribute.&#34;&#34;&#34;
    self.is_finished
    return self._details[&#39;jobDetail&#39;][&#39;progressInfo&#39;][&#39;numOfFilesTransferred&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.Job.pending_reason"><code class="name">var <span class="ident">pending_reason</span></code></dt>
<dd>
<div class="desc"><p>Treats the job pending reason as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L2420-L2425" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def pending_reason(self):
    &#34;&#34;&#34;Treats the job pending reason as a read-only attribute.&#34;&#34;&#34;
    self.is_finished
    if &#39;pendingReason&#39; in self._summary and self._summary[&#39;pendingReason&#39;]:
        return self._summary[&#39;pendingReason&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.Job.phase"><code class="name">var <span class="ident">phase</span></code></dt>
<dd>
<div class="desc"><p>Treats the job current phase as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L2427-L2432" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def phase(self):
    &#34;&#34;&#34;Treats the job current phase as a read-only attribute.&#34;&#34;&#34;
    self.is_finished
    if &#39;currentPhaseName&#39; in self._summary:
        return self._summary[&#39;currentPhaseName&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.Job.size_of_application"><code class="name">var <span class="ident">size_of_application</span></code></dt>
<dd>
<div class="desc"><p>Treats the size of application as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L2462-L2466" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def size_of_application(self):
    &#34;&#34;&#34;Treats the size of application as a read-only attribute.&#34;&#34;&#34;
    if &#39;sizeOfApplication&#39; in self._summary:
        return self._summary[&#39;sizeOfApplication&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.Job.start_time"><code class="name">var <span class="ident">start_time</span></code></dt>
<dd>
<div class="desc"><p>Treats the start time as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L2392-L2395" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def start_time(self):
    &#34;&#34;&#34;Treats the start time as a read-only attribute.&#34;&#34;&#34;
    return self._start_time</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.Job.start_timestamp"><code class="name">var <span class="ident">start_timestamp</span></code></dt>
<dd>
<div class="desc"><p>Treats the unix start time as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L2397-L2400" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def start_timestamp(self):
    &#34;&#34;&#34;Treats the unix start time as a read-only attribute.&#34;&#34;&#34;
    return self._summary[&#39;jobStartTime&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.Job.state"><code class="name">var <span class="ident">state</span></code></dt>
<dd>
<div class="desc"><p>Treats the job state as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L2483-L2487" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def state(self):
    &#34;&#34;&#34;Treats the job state as a read-only attribute.&#34;&#34;&#34;
    self.is_finished
    return self._details[&#39;jobDetail&#39;][&#39;progressInfo&#39;][&#39;state&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.Job.status"><code class="name">var <span class="ident">status</span></code></dt>
<dd>
<div class="desc"><p>Treats the job status as a read-only attribute.
<a href="http://documentation.commvault.com/commvault/v11/article?p=features/rest_api/operations/get_job.htm">http://documentation.commvault.com/commvault/v11/article?p=features/rest_api/operations/get_job.htm</a>
please refer status section in above doc link for complete list of status available</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L2368-L2374" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def status(self):
    &#34;&#34;&#34;Treats the job status as a read-only attribute.
       http://documentation.commvault.com/commvault/v11/article?p=features/rest_api/operations/get_job.htm
       please refer status section in above doc link for complete list of status available&#34;&#34;&#34;
    self.is_finished
    return self._status</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.Job.subclient_name"><code class="name">var <span class="ident">subclient_name</span></code></dt>
<dd>
<div class="desc"><p>Treats the subclient name as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L2362-L2366" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def subclient_name(self):
    &#34;&#34;&#34;Treats the subclient name as a read-only attribute.&#34;&#34;&#34;
    if &#39;subclientName&#39; in self._summary[&#39;subclient&#39;]:
        return self._summary[&#39;subclient&#39;][&#39;subclientName&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.Job.summary"><code class="name">var <span class="ident">summary</span></code></dt>
<dd>
<div class="desc"><p>Treats the job full summary as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L2440-L2444" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def summary(self):
    &#34;&#34;&#34;Treats the job full summary as a read-only attribute.&#34;&#34;&#34;
    self.is_finished
    return self._summary</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.Job.task_details"><code class="name">var <span class="ident">task_details</span></code></dt>
<dd>
<div class="desc"><p>Returns: (dict) A dictionary of job task details</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L2489-L2494" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def task_details(self):
    &#34;&#34;&#34;Returns: (dict) A dictionary of job task details&#34;&#34;&#34;
    if not self._task_details:
        self._task_details = self._get_job_task_details()
    return self._task_details</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.Job.userid"><code class="name">var <span class="ident">userid</span></code></dt>
<dd>
<div class="desc"><p>Treats the userid as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L2451-L2454" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def userid(self):
    &#34;&#34;&#34;Treats the userid as a read-only attribute.&#34;&#34;&#34;
    return self._summary[&#39;userName&#39;][&#39;userId&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.Job.username"><code class="name">var <span class="ident">username</span></code></dt>
<dd>
<div class="desc"><p>Treats the username as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L2446-L2449" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def username(self):
    &#34;&#34;&#34;Treats the username as a read-only attribute.&#34;&#34;&#34;
    return self._summary[&#39;userName&#39;][&#39;userName&#39;]</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.job.Job.advanced_job_details"><code class="name flex">
<span>def <span class="ident">advanced_job_details</span></span>(<span>self, info_type)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns advanced properties for the job</p>
<h2 id="args">Args</h2>
<p>infoType
(object)
&ndash;
job detail type to be passed from AdvancedJobDetailType
enum from the constants</p>
<h2 id="returns">Returns</h2>
<p>dict -
dictionary with advanced details of the job info type given</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if response is empty</p>
<pre><code>if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L2670-L2705" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def advanced_job_details(self, info_type):
    &#34;&#34;&#34;Returns advanced properties for the job

        Args:
            infoType    (object)  --  job detail type to be passed from AdvancedJobDetailType
            enum from the constants

        Returns:
            dict -  dictionary with advanced details of the job info type given

        Raises:
            SDKException:
                if response is empty

                if response is not success

    &#34;&#34;&#34;
    if not isinstance(info_type, AdvancedJobDetailType):
        raise SDKException(&#39;Response&#39;, &#39;107&#39;)
    url = self._services[&#39;ADVANCED_JOB_DETAIL_TYPE&#39;] % (self.job_id, info_type.value)
    flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, url)

    if flag:
        if response.json():
            response = response.json()

            if response.get(&#39;errorCode&#39;, 0) != 0:
                error_message = response.json()[&#39;errorMessage&#39;]
                o_str = &#39;Failed to fetch details.\nError: &#34;{0}&#34;&#39;.format(error_message)
                raise SDKException(&#39;Job&#39;, &#39;102&#39;, o_str)

            return response
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.Job.get_child_jobs"><code class="name flex">
<span>def <span class="ident">get_child_jobs</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Get the child jobs details for the current job</p>
<h2 id="returns">Returns</h2>
<p>_jobs_list
(list):
List of child jobs</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L2806-L2818" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_child_jobs(self):
    &#34;&#34;&#34; Get the child jobs details for the current job
    Returns:
            _jobs_list          (list):     List of child jobs

    &#34;&#34;&#34;
    _jobs_list = []
    if self.details.get(&#39;jobDetail&#39;, {}).get(&#39;clientStatusInfo&#39;, {}).get(&#39;vmStatus&#39;):
        for _job in self.details[&#39;jobDetail&#39;][&#39;clientStatusInfo&#39;][&#39;vmStatus&#39;]:
            _jobs_list.append(_job)
        return _jobs_list
    else:
        return None</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.Job.get_events"><code class="name flex">
<span>def <span class="ident">get_events</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>gets the commserv events associated with this job</p>
<h2 id="args">Args</h2>
<p>None</p>
<h2 id="returns">Returns</h2>
<p>list - list of job events</p>
<dl>
<dt><code>
Example </code></dt>
<dd>[
{
"severity": 3,
"eventCode": "318769020",
"jobId": 4547,
"acknowledge": 0,
"eventCodeString": "19:1916",
"subsystem": "JobManager",
"description": "Data Analytics operation has completed with one or more errors.",
"id": 25245,
"timeSource": 1600919001,
"type": 0,
"clientEntity": {
"clientId": 2,
"clientName": "xyz",
"displayName": "xyz"
}
},
{
"severity": 6,
"eventCode": "318767961",
"jobId": 4547,
"acknowledge": 0,
"eventCodeString": "19:857",
"subsystem": "clBackup",
"description": "Failed to send some items to Index Engine",
"id": 25244,
"timeSource": 1600918999,
"type": 0,
"clientEntity": {
"clientId": 33,
"clientName": "xyz",
"displayName": "xyz"
}
}
]</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L2707-L2761" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_events(self):
    &#34;&#34;&#34; gets the commserv events associated with this job

        Args:

            None

        Returns:

            list - list of job events

                Example : [
                    {
                        &#34;severity&#34;: 3,
                        &#34;eventCode&#34;: &#34;318769020&#34;,
                        &#34;jobId&#34;: 4547,
                        &#34;acknowledge&#34;: 0,
                        &#34;eventCodeString&#34;: &#34;19:1916&#34;,
                        &#34;subsystem&#34;: &#34;JobManager&#34;,
                        &#34;description&#34;: &#34;Data Analytics operation has completed with one or more errors.&#34;,
                        &#34;id&#34;: 25245,
                        &#34;timeSource&#34;: 1600919001,
                        &#34;type&#34;: 0,
                        &#34;clientEntity&#34;: {
                            &#34;clientId&#34;: 2,
                            &#34;clientName&#34;: &#34;xyz&#34;,
                            &#34;displayName&#34;: &#34;xyz&#34;
                        }
                    },
                    {
                        &#34;severity&#34;: 6,
                        &#34;eventCode&#34;: &#34;318767961&#34;,
                        &#34;jobId&#34;: 4547,
                        &#34;acknowledge&#34;: 0,
                        &#34;eventCodeString&#34;: &#34;19:857&#34;,
                        &#34;subsystem&#34;: &#34;clBackup&#34;,
                        &#34;description&#34;: &#34;Failed to send some items to Index Engine&#34;,
                        &#34;id&#34;: 25244,
                        &#34;timeSource&#34;: 1600918999,
                        &#34;type&#34;: 0,
                        &#34;clientEntity&#34;: {
                            &#34;clientId&#34;: 33,
                            &#34;clientName&#34;: &#34;xyz&#34;,
                            &#34;displayName&#34;: &#34;xyz&#34;
                        }
                    }
                ]

    &#34;&#34;&#34;
    flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._JOB_EVENTS)
    if flag:
        if response.json() and &#39;commservEvents&#39; in response.json():
                return response.json()[&#39;commservEvents&#39;]
        raise SDKException(&#39;Job&#39;, &#39;104&#39;)
    raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.Job.get_vm_list"><code class="name flex">
<span>def <span class="ident">get_vm_list</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Gets the list of all VMs associated to the job
Returns: list of VM dictionaries
VM: {
"Size":0,
"AverageThroughput":0,
"UsedSpace":0,
"ArchivedByCurrentJob":false,
"jobID":0,
"CBTStatus":"",
"BackupType":0,
"totalFiles":0,
"Status":2,
"CurrentThroughput":0,
"Agent":"proxy",
"lastSyncedBkpJob":0,
"GUID":"live sync pair guid",
"HardwareVersion":"vm h/w",
"restoredSize":1361912,
"FailureReason":"",
"BackupStartTime":0,
"TransportMode":"nbd",
"projectId":"",
"syncStatus":3,
"PoweredOffSince":0,
"OperatingSystem":"Microsoft Windows Server 2012 (64-bit)",
"backupLevel":0,
"destinationVMName":"drvm1",
"successfulCIedFiles":0,
"GuestSize":0,
"failedCIedFiles":0,
"vmName":"vm1",
"ToolsVersion":"Not running",
"clientId":3280,
"Host":"*******",
"StubStatus":0,
"BackupEndTime":0,
"PoweredOffByCurrentJob":false
}</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L2763-L2804" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_vm_list(self):
    &#34;&#34;&#34;
    Gets the list of all VMs associated to the job
    Returns: list of VM dictionaries
        VM: {
           &#34;Size&#34;:0,
           &#34;AverageThroughput&#34;:0,
           &#34;UsedSpace&#34;:0,
           &#34;ArchivedByCurrentJob&#34;:false,
           &#34;jobID&#34;:0,
           &#34;CBTStatus&#34;:&#34;&#34;,
           &#34;BackupType&#34;:0,
           &#34;totalFiles&#34;:0,
           &#34;Status&#34;:2,
           &#34;CurrentThroughput&#34;:0,
           &#34;Agent&#34;:&#34;proxy&#34;,
           &#34;lastSyncedBkpJob&#34;:0,
           &#34;GUID&#34;:&#34;live sync pair guid&#34;,
           &#34;HardwareVersion&#34;:&#34;vm h/w&#34;,
           &#34;restoredSize&#34;:1361912,
           &#34;FailureReason&#34;:&#34;&#34;,
           &#34;BackupStartTime&#34;:0,
           &#34;TransportMode&#34;:&#34;nbd&#34;,
           &#34;projectId&#34;:&#34;&#34;,
           &#34;syncStatus&#34;:3,
           &#34;PoweredOffSince&#34;:0,
           &#34;OperatingSystem&#34;:&#34;Microsoft Windows Server 2012 (64-bit)&#34;,
           &#34;backupLevel&#34;:0,
           &#34;destinationVMName&#34;:&#34;drvm1&#34;,
           &#34;successfulCIedFiles&#34;:0,
           &#34;GuestSize&#34;:0,
           &#34;failedCIedFiles&#34;:0,
           &#34;vmName&#34;:&#34;vm1&#34;,
           &#34;ToolsVersion&#34;:&#34;Not running&#34;,
           &#34;clientId&#34;:3280,
           &#34;Host&#34;:&#34;*******&#34;,
           &#34;StubStatus&#34;:0,
           &#34;BackupEndTime&#34;:0,
           &#34;PoweredOffByCurrentJob&#34;:false
        }
    &#34;&#34;&#34;
    return self.details.get(&#39;jobDetail&#39;, {}).get(&#39;clientStatusInfo&#39;, {}).get(&#39;vmStatus&#39;, [])</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.Job.kill"><code class="name flex">
<span>def <span class="ident">kill</span></span>(<span>self, wait_for_job_to_kill=False)</span>
</code></dt>
<dd>
<div class="desc"><p>Kills the job.</p>
<h2 id="args">Args</h2>
<p>wait_for_job_to_kill
(bool)
&ndash;
wait till job status is changed to Killed</p>
<pre><code>default: False
</code></pre>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to kill job</p>
<pre><code>if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L2625-L2663" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def kill(self, wait_for_job_to_kill=False):
    &#34;&#34;&#34;Kills the job.

        Args:
            wait_for_job_to_kill    (bool)  --  wait till job status is changed to Killed

                default: False

        Raises:
            SDKException:
                if failed to kill job

                if response is not success

    &#34;&#34;&#34;
    flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._KILL)

    self.is_finished

    if flag:
        if response.json():
            if &#39;errors&#39; in response.json():
                error_list = response.json()[&#39;errors&#39;][0][&#39;errList&#39;][0]
                error_code = error_list[&#39;errorCode&#39;]
                error_message = error_list[&#39;errLogMessage&#39;].strip()
            else:
                error_code = response.json().get(&#39;errorCode&#39;, 0)
                error_message = response.json().get(&#39;errorMessage&#39;, &#39;nil&#39;)

            if error_code != 0:
                raise SDKException(
                    &#39;Job&#39;, &#39;102&#39;, &#39;Job kill failed\nError: &#34;{0}&#34;&#39;.format(error_message)
                )
    else:
        response_string = self._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    if wait_for_job_to_kill is True:
        self._wait_for_status(&#34;KILLED&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.Job.pause"><code class="name flex">
<span>def <span class="ident">pause</span></span>(<span>self, wait_for_job_to_pause=False, timeout=6)</span>
</code></dt>
<dd>
<div class="desc"><p>Suspends the job.</p>
<h2 id="args">Args</h2>
<p>wait_for_job_to_pause
(bool)
&ndash;
wait till job status is changed to Suspended</p>
<pre><code>default: False
</code></pre>
<p>timeout (int)
&ndash;
timeout interval to wait for job to move to suspend state</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to suspend job</p>
<pre><code>if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L2496-L2536" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def pause(self, wait_for_job_to_pause=False, timeout=6):
    &#34;&#34;&#34;Suspends the job.

        Args:
            wait_for_job_to_pause   (bool)  --  wait till job status is changed to Suspended

                default: False

            timeout (int)                   --  timeout interval to wait for job to move to suspend state

        Raises:
            SDKException:
                if failed to suspend job

                if response is not success

    &#34;&#34;&#34;
    flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._SUSPEND)

    self.is_finished

    if flag:
        if response.json():
            if &#39;errors&#39; in response.json():
                error_list = response.json()[&#39;errors&#39;][0][&#39;errList&#39;][0]
                error_code = error_list[&#39;errorCode&#39;]
                error_message = error_list[&#39;errLogMessage&#39;].strip()
            else:
                error_code = response.json().get(&#39;errorCode&#39;, 0)
                error_message = response.json().get(&#39;errorMessage&#39;, &#39;nil&#39;)

            if error_code != 0:
                raise SDKException(
                    &#39;Job&#39;, &#39;102&#39;, &#39;Job suspend failed\nError: &#34;{0}&#34;&#39;.format(error_message)
                )
    else:
        response_string = self._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    if wait_for_job_to_pause is True:
        self._wait_for_status(&#34;SUSPENDED&#34;, timeout=timeout)</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.Job.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the properties of the Job.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L2665-L2668" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the properties of the Job.&#34;&#34;&#34;
    self._initialize_job_properties()
    self.is_finished</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.Job.resubmit"><code class="name flex">
<span>def <span class="ident">resubmit</span></span>(<span>self, start_suspended=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Resubmits the job</p>
<h2 id="args">Args</h2>
<p>start_suspended (bool)
-
whether to start the new job in suspended state or not
default: None, the new job starts same as this job started</p>
<h2 id="returns">Returns</h2>
<p>object
-
Job class object for the given job id</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if job is already running</p>
<pre><code>if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L2578-L2623" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def resubmit(self, start_suspended=None):
    &#34;&#34;&#34;Resubmits the job

    Args:
        start_suspended (bool)  -   whether to start the new job in suspended state or not
                                    default: None, the new job starts same as this job started

    Returns:
        object  -   Job class object for the given job id

    Raises:
            SDKException:
                if job is already running

                if response is not success

    &#34;&#34;&#34;
    if start_suspended not in [True, False, None]:
        raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    if not self.is_finished:
        raise SDKException(&#39;Job&#39;, &#39;102&#39;, &#39;Cannot resubmit the Job, the Job is still running&#39;)

    url = self._RESUBMIT
    if start_suspended is not None:
        url += f&#39;?startInSuspendedState={start_suspended}&#39;
    flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, url)

    if flag:
        if response.json():
            if &#39;errors&#39; in response.json():
                error_list = response.json()[&#39;errors&#39;][0][&#39;errList&#39;][0]
                error_code = error_list[&#39;errorCode&#39;]
                error_message = error_list[&#39;errLogMessage&#39;].strip()
            else:
                error_code = response.json().get(&#39;errorCode&#39;, 0)
                error_message = response.json().get(&#39;errorMessage&#39;, &#39;nil&#39;)

            if error_code != 0:
                raise SDKException(
                    &#39;Job&#39;, &#39;102&#39;, &#39;Resubmitting job failed\nError: &#34;{0}&#34;&#39;.format(error_message)
                )
        return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])
    else:
        response_string = self._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.Job.resume"><code class="name flex">
<span>def <span class="ident">resume</span></span>(<span>self, wait_for_job_to_resume=False)</span>
</code></dt>
<dd>
<div class="desc"><p>Resumes the job.</p>
<h2 id="args">Args</h2>
<p>wait_for_job_to_resume
(bool)
&ndash;
wait till job status is changed to Running</p>
<pre><code>default: False
</code></pre>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to resume job</p>
<pre><code>if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L2538-L2576" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def resume(self, wait_for_job_to_resume=False):
    &#34;&#34;&#34;Resumes the job.

        Args:
            wait_for_job_to_resume  (bool)  --  wait till job status is changed to Running

                default: False

        Raises:
            SDKException:
                if failed to resume job

                if response is not success

    &#34;&#34;&#34;
    flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._RESUME)

    self.is_finished

    if flag:
        if response.json():
            if &#39;errors&#39; in response.json():
                error_list = response.json()[&#39;errors&#39;][0][&#39;errList&#39;][0]
                error_code = error_list[&#39;errorCode&#39;]
                error_message = error_list[&#39;errLogMessage&#39;].strip()
            else:
                error_code = response.json().get(&#39;errorCode&#39;, 0)
                error_message = response.json().get(&#39;errorMessage&#39;, &#39;nil&#39;)

            if error_code != 0:
                raise SDKException(
                    &#39;Job&#39;, &#39;102&#39;, &#39;Job resume failed\nError: &#34;{0}&#34;&#39;.format(error_message)
                )
    else:
        response_string = self._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    if wait_for_job_to_resume is True:
        self._wait_for_status(&#34;RUNNING&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.Job.wait_for_completion"><code class="name flex">
<span>def <span class="ident">wait_for_completion</span></span>(<span>self, timeout=30, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Waits till the job is not finished; i.e.; till the value of job.is_finished is not True.
Kills the job and exits, if the job has been in Pending / Waiting state for more than
the timeout value.</p>
<p>In case of job failure job status and failure reason can be obtained
using status and delay_reason property</p>
<h2 id="args">Args</h2>
<p>timeout
(int)
&ndash;
minutes after which the job should be killed and exited,
if the job has been in Pending / Waiting state
default: 30</p>
<p>**kwargs
(str)
&ndash;
accepted optional arguments</p>
<pre><code>return_timeout  (int)   -- minutes after which the method will return False.
</code></pre>
<h2 id="returns">Returns</h2>
<p>bool
-
boolean specifying whether the job had finished or not
True
-
if the job had finished successfully</p>
<pre><code>False   -   if the job was killed/failed
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L2245-L2313" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def wait_for_completion(self, timeout=30, **kwargs):
    &#34;&#34;&#34;Waits till the job is not finished; i.e.; till the value of job.is_finished is not True.
        Kills the job and exits, if the job has been in Pending / Waiting state for more than
        the timeout value.

        In case of job failure job status and failure reason can be obtained
            using status and delay_reason property

        Args:
            timeout     (int)   --  minutes after which the job should be killed and exited,
                    if the job has been in Pending / Waiting state
                default: 30

            **kwargs    (str)   --  accepted optional arguments

                return_timeout  (int)   -- minutes after which the method will return False.

        Returns:
            bool    -   boolean specifying whether the job had finished or not
                True    -   if the job had finished successfully

                False   -   if the job was killed/failed

    &#34;&#34;&#34;
    start_time = actual_start_time = time.time()
    pending_time = 0
    waiting_time = 0
    previous_status = None
    return_timeout = kwargs.get(&#39;return_timeout&#39;)

    status_list = [&#39;pending&#39;, &#39;waiting&#39;]

    while not self.is_finished:
        time.sleep(30)

        if return_timeout and ((time.time() - actual_start_time) / 60) &gt; return_timeout:
            return False

        # get the current status of the job
        status = self.status
        status = status.lower() if status else self.state.lower()

        # set the value of start time as current time
        # if the current status is pending / waiting but the previous status was not
        # also if the current status is pending / waiting and same as previous,
        # then don&#39;t update the value of start time
        if status in status_list and previous_status not in status_list:
            start_time = time.time()

        if status == &#39;pending&#39;:
            pending_time = (time.time() - start_time) / 60
        else:
            pending_time = 0

        if status == &#39;waiting&#39;:
            waiting_time = (time.time() - start_time) / 60
        else:
            waiting_time = 0

        if pending_time &gt; timeout or waiting_time &gt; timeout:
            self.kill()
            break

        # set the value of previous status as the value of current status
        previous_status = status
    else:
        return self._status.lower() not in [&#34;failed&#34;, &#34;killed&#34;, &#34;failed to start&#34;]

    return False</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.job.JobController"><code class="flex name class">
<span>class <span class="ident">JobController</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for controlling all the jobs associated with the commcell.</p>
<p>Initialize instance of the JobController class to get the details of Commcell Jobs.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of Commcell class to get the jobs of</p>
<h2 id="returns">Returns</h2>
<p>None</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L295-L937" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class JobController(object):
    &#34;&#34;&#34;Class for controlling all the jobs associated with the commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize instance of the JobController class to get the details of Commcell Jobs.

            Args:
                commcell_object     (object)    --  instance of Commcell class to get the jobs of

            Returns:
                None

        &#34;&#34;&#34;
        self._commcell_object = commcell_object

        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all active jobs on this commcell.

            Returns:
                str     -   string of all the active jobs on this commcell

        &#34;&#34;&#34;
        jobs_dict = self.active_jobs()

        representation_string = &#39;{:^5}\t{:^25}\t{:^20}\t{:^20}\t{:^20}\t{:^20}\t{:^20}\n\n&#39;.format(
            &#39;Job ID&#39;, &#39;Operation&#39;, &#39;Status&#39;, &#39;Agent type&#39;, &#39;Job type&#39;, &#39;Progress&#39;, &#39;Pending Reason&#39;
        )

        for job in jobs_dict:
            sub_str = &#39;{:^5}\t{:25}\t{:20}\t{:20}\t{:20}\t{:20}%\t{:^20}\n&#39;.format(
                job,
                jobs_dict[job][&#39;operation&#39;],
                jobs_dict[job][&#39;status&#39;],
                jobs_dict[job][&#39;app_type&#39;],
                jobs_dict[job][&#39;job_type&#39;],
                jobs_dict[job][&#39;percent_complete&#39;],
                jobs_dict[job][&#39;pending_reason&#39;]
            )
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the JobController class.&#34;&#34;&#34;
        return &#34;JobController class instance for Commcell&#34;

    def _get_jobs_request_json(self, **options):
        &#34;&#34;&#34;Returns the request json for the jobs request

            Args:
                options     (dict)  --  dict of key-word arguments

                Available Options:

                    category        (str)   --  category name for which the list of jobs
                    are to be retrieved

                        Valid Values:

                            - ALL

                            - ACTIVE

                            - FINISHED

                        default: ALL

                    limit           (int)   --  total number of jobs list that are to be returned

                            default: 20

                    offset           (int)  --  value from which starting job to be returned is counted

                            default: 0

                    lookup_time     (int)   --  list of jobs to be retrieved which are specified
                    hours older

                            default: 5 hours

                    show_aged_job   (bool)  --  boolean specifying whether to include aged jobs in
                    the result or not

                            default: False
                    
                    hide_admin_jobs (bool)  --  boolean specifying whether to exclude admin jobs from
                    the result or not

                            default: False

                    clients_list    (list)  --  list of clients to return the jobs for

                            default: []

                    job_type_list   (list)  --  list of job operation types

                            default: []

                    entity          (dict)  --  dict containing entity details to which associated jobs has to be fetched

                            Example : To fetch job details of particular data source id

                                &#34;entity&#34;: {
                                            &#34;dataSourceId&#34;: 2575
                                            }


            Returns:
                dict    -   request json that is to be sent to server

        &#34;&#34;&#34;
        job_list_category = {
            &#39;ALL&#39;: 0,
            &#39;ACTIVE&#39;: 1,
            &#39;FINISHED&#39;: 2
        }

        for client in options.get(&#39;clients_list&#39;, []):
            if not self._commcell_object.clients.has_client(client):
                raise SDKException(&#39;Job&#39;, &#39;102&#39;, &#39;No client with name {0} exists.&#39;.format(client))

        client_list = []
        for client in options.get(&#39;clients_list&#39;, []):
            try:
                _client_id = int(self._commcell_object.clients.all_clients[client.lower()][&#39;id&#39;])
            except KeyError:
                _client_id = int(self._commcell_object.clients.hidden_clients[client.lower()][&#39;id&#39;])
            client_list.append({&#34;clientId&#34;: _client_id})

        request_json = {
            &#34;scope&#34;: 1,
            &#34;category&#34;: job_list_category[options.get(&#39;category&#39;, &#39;ALL&#39;)],
            &#34;pagingConfig&#34;: {
                &#34;sortDirection&#34;: 1,
                &#34;offset&#34;: options.get(&#39;offset&#39;, 0),
                &#34;sortField&#34;: &#34;jobId&#34;,
                &#34;limit&#34;: options.get(&#39;limit&#39;, 20)
            },
            &#34;jobFilter&#34;: {
                &#34;completedJobLookupTime&#34;: int(options.get(&#39;lookup_time&#39;, 5) * 60 * 60),
                &#34;showAgedJobs&#34;: options.get(&#39;show_aged_jobs&#39;, False),
                &#34;hideAdminJobs&#34;: options.get(&#39;hide_admin_jobs&#39;, False),
                &#34;clientList&#34;: client_list,
                &#34;jobTypeList&#34;: [
                    job_type for job_type in options.get(&#39;job_type_list&#39;, [])
                ]
            }
        }

        if &#34;entity&#34; in options:
            request_json[&#39;jobFilter&#39;][&#39;entity&#39;] = options.get(&#34;entity&#34;)

        return request_json

    def _get_jobs_list(self, **options):
        &#34;&#34;&#34;Executes a request on the server to get the list of jobs.

            Args:
                request_json    (dict)  --  request that is to be sent to server

            Returns:
                dict    -   dict containing details about all the retrieved jobs

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        request_json = self._get_jobs_request_json(**options)

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;ALL_JOBS&#39;], request_json
        )

        jobs_dict = {}

        if flag:
            try:
                if response.json():
                    if &#39;jobs&#39; in response.json():
                        all_jobs = response.json()[&#39;jobs&#39;]

                        for job in all_jobs:
                            if &#39;jobSummary&#39; in job and job[&#39;jobSummary&#39;][&#39;isVisible&#39;] is True:

                                job_summary = job[&#39;jobSummary&#39;]
                                job_id = job_summary[&#39;jobId&#39;]

                                if options.get(&#39;job_summary&#39;, &#39;&#39;).lower() == &#39;full&#39;:
                                    jobs_dict[job_id] = job_summary
                                else:
                                    status = job_summary[&#39;status&#39;]
                                    operation = job_summary.get(&#39;localizedOperationName&#39;, &#39;&#39;)
                                    percent_complete = job_summary[&#39;percentComplete&#39;]
                                    backup_level = job_summary.get(&#39;backupLevelName&#39;)

                                    app_type = &#39;&#39;
                                    job_type = &#39;&#39;
                                    pending_reason = &#39;&#39;
                                    subclient_id = &#39;&#39;
                                    client_id = &#39;&#39;
                                    client_name = &#39;&#39;
                                    job_elapsed_time = 0
                                    job_start_time = 0

                                    if &#39;jobElapsedTime&#39; in job_summary:
                                        job_elapsed_time = job_summary[&#39;jobElapsedTime&#39;]

                                    if &#39;jobStartTime&#39; in job_summary:
                                        job_start_time = job_summary[&#39;jobStartTime&#39;]

                                    if &#39;appTypeName&#39; in job_summary:
                                        app_type = job_summary[&#39;appTypeName&#39;]

                                    if &#39;jobType&#39; in job_summary:
                                        job_type = job_summary[&#39;jobType&#39;]

                                    if &#39;pendingReason&#39; in job_summary:
                                        pending_reason = job_summary[&#39;pendingReason&#39;]

                                    if &#39;subclient&#39; in job_summary:
                                        job_subclient = job_summary[&#39;subclient&#39;]
                                        if &#39;subclientId&#39; in job_subclient:
                                            subclient_id = job_subclient[&#39;subclientId&#39;]
                                        if &#39;clientId&#39; in job_subclient:
                                            client_id = job_subclient[&#39;clientId&#39;]
                                        if &#39;clientName&#39; in job_subclient:
                                            client_name = job_subclient[&#39;clientName&#39;]

                                    jobs_dict[job_id] = {
                                        &#39;operation&#39;: operation,
                                        &#39;status&#39;: status,
                                        &#39;app_type&#39;: app_type,
                                        &#39;job_type&#39;: job_type,
                                        &#39;percent_complete&#39;: percent_complete,
                                        &#39;pending_reason&#39;: pending_reason,
                                        &#39;client_id&#39;: client_id,
                                        &#39;client_name&#39;: client_name,
                                        &#39;subclient_id&#39;: subclient_id,
                                        &#39;backup_level&#39;: backup_level,
                                        &#39;job_start_time&#39;: job_start_time,
                                        &#39;job_elapsed_time&#39;: job_elapsed_time

                                    }

                    return jobs_dict

                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)

            except ValueError:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;, &#39;Please check the inputs.&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _modify_all_jobs(self, operation_type=None):
        &#34;&#34;&#34; Executes a request on the server to suspend/resume/kill all the jobs on the commserver

            Args:
                operation_type     (str)   --  All jobs on commcell will be changed to this
                                                    state.
                                                    Options:
                                                        suspend/resume/kill

            Returns:
                None

            Raises:
                SDKException:
                    - Invalid input is passed to the module

                    - Failed to execute the api to modify jobs

                    - Response is incorrect
        &#34;&#34;&#34;

        job_map = {
            &#39;suspend&#39;: &#39;JOB_SUSPEND&#39;,
            &#39;resume&#39;: &#39;JOB_RESUME&#39;,
            &#39;kill&#39;: &#39;JOB_KILL&#39;
        }

        if operation_type not in job_map:
            raise SDKException(&#39;Job&#39;, &#39;102&#39;, &#39;Invalid input&#39;)

        request_json = {
            &#34;JobManager_PerformMultiCellJobOpReq&#34;: {
                &#34;jobOpReq&#34;: {
                    &#34;operationType&#34;: job_map[operation_type]
                },
                &#34;message&#34;: &#34;ALL_JOBS&#34;,
                &#34;operationDescription&#34;: &#34;All jobs&#34;
            }
        }

        response = self._commcell_object._qoperation_execute(request_json)

        if &#39;error&#39; in response:
            error_code = response[&#39;error&#39;].get(&#39;errorCode&#39;)
            if error_code != 0:
                if &#39;errLogMessage&#39; in response[&#39;error&#39;]:
                    error_message = &#34;Failed to {0} all jobs with error: [{1}]&#34;.format(
                        operation_type, response[&#39;error&#39;][&#39;errLogMessage&#39;]
                    )

                    raise SDKException(
                        &#39;Job&#39;,
                        &#39;102&#39;,
                        &#39;Error Code:&#34;{0}&#34;\nError Message: &#34;{1}&#34;&#39;.format(error_code, error_message)
                    )
                else:
                    raise SDKException(&#39;Job&#39;,
                                       &#39;102&#39;,
                                       &#34;Failed to {0} all jobs&#34;.format(operation_type))
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

    def all_jobs(self, client_name=None, lookup_time=5, job_filter=None, **options):
        &#34;&#34;&#34;Returns the dict consisting of all the jobs executed on the Commcell within the number
            of hours specified in lookup time value.

            Args:
                client_name     (str)   --  name of the client to filter out the jobs for

                    default: None, get all the jobs


                lookup_time     (int)   --  get all the jobs executed within the number of hours

                    default: 5 Hours


                job_filter      (str)   --  type of jobs to filter

                        for multiple filters, give the values **comma(,)** separated

                        List of Possible Values:

                            Backup

                            Restore

                            AUXCOPY

                            WORKFLOW

                            etc..

                    http://documentation.commvault.com/commvault/v11/article?p=features/rest_api/operations/get_job.htm
                        to get the complete list of filters available

                    default: None

                options         (dict)  --  dict of key-word arguments

                Available Options:

                    limit           (int)   --  total number of jobs list that are to be returned
                        default: 20

                    offset           (int)  --  value from which starting job to be returned is counted

                        default: 0

                    show_aged_job   (bool)  --  boolean specifying whether to include aged jobs in
                    the result or not

                        default: False

                    hide_admin_jobs (bool)  --  boolean specifying whether to exclude admin jobs from
                    the result or not

                        default: False

                    clients_list    (list)  --  list of clients to return the jobs for

                        default: []

                    job_type_list   (list)  --  list of job operation types

                        default: []

                    job_summary     (str)   --  To return the basic job summary or full job summary

                        default: basic

                        accepted values: [&#39;basic&#39;, &#39;full&#39;]

            Returns:
                dict    -   dictionary consisting of the job IDs matching the given criteria
                as the key, and their details as its value

            Raises:
                SDKException:
                    if client name is given, and no client exists with the given name

        &#34;&#34;&#34;
        options[&#39;category&#39;] = &#39;ALL&#39;
        options[&#39;lookup_time&#39;] = lookup_time

        if job_filter:
            options[&#39;job_type_list&#39;] = options.get(&#39;job_type_list&#39;, []) + job_filter.split(&#39;,&#39;)

        if client_name:
            options[&#39;clients_list&#39;] = options.get(&#39;clients_list&#39;, []) + [client_name]

        return self._get_jobs_list(**options)

    def active_jobs(self, client_name=None, lookup_time=1, job_filter=None, **options):
        &#34;&#34;&#34;Returns the dict consisting of all the active jobs currently being executed on the
            Commcell within the number of hours specified in lookup time value.

            Args:
                client_name     (str)   --  name of the client to filter out the jobs for

                    default: None, get all the jobs


                lookup_time     (int)   --  get all the jobs executed within the number of hours

                    default: 1 Hour(s)


                job_filter      (str)   --  type of jobs to filter

                        for multiple filters, give the values **comma(,)** separated

                        List of Possible Values:

                            Backup

                            Restore

                            AUXCOPY

                            WORKFLOW

                            etc..

                    http://documentation.commvault.com/commvault/v11/article?p=features/rest_api/operations/get_job.htm
                        to get the complete list of filters available

                    default: None

                options         (dict)  --  dict of key-word arguments

                Available Options:

                    limit           (int)   --  total number of jobs list that are to be returned

                        default: 20

                    offset          (int)   --  value from which starting job to be returned is counted

                        default: 0

                    show_aged_job   (bool)  --  boolean specifying whether to include aged jobs in
                    the result or not

                        default: False

                    hide_admin_jobs (bool)  --  boolean specifying whether to exclude admin jobs from
                    the result or not

                        default: False

                    clients_list    (list)  --  list of clients to return the jobs for

                        default: []

                    job_type_list   (list)  --  list of job operation types

                        default: []

                    job_summary     (str)   --  To return the basic job summary or full job summary

                        default: basic

                        accepted values: [&#39;basic&#39;, &#39;full&#39;]

                    entity          (dict)  --  dict containing entity details to which associated jobs has to be fetched

                        Example : To fetch job details of particular data source id

                                &#34;entity&#34;: {
                                            &#34;dataSourceId&#34;: 2575
                                            }

            Returns:
                dict    -   dictionary consisting of the job IDs matching the given criteria
                as the key, and their details as its value

            Raises:
                SDKException:
                    if client name is given, and no client exists with the given name

        &#34;&#34;&#34;
        options[&#39;category&#39;] = &#39;ACTIVE&#39;
        options[&#39;lookup_time&#39;] = lookup_time

        if job_filter:
            options[&#39;job_type_list&#39;] = options.get(&#39;job_type_list&#39;, []) + job_filter.split(&#39;,&#39;)

        if client_name:
            options[&#39;clients_list&#39;] = options.get(&#39;clients_list&#39;, []) + [client_name]

        return self._get_jobs_list(**options)

    def finished_jobs(self, client_name=None, lookup_time=24, job_filter=None, **options):
        &#34;&#34;&#34;Returns the dict consisting of all the finished jobs on the Commcell within the number
            of hours specified in lookup time value.

            Args:
                client_name     (str)   --  name of the client to filter out the jobs for

                    default: None, get all the jobs ir-respective of client


                lookup_time     (int)   --  get all the jobs executed within the number of hours

                    default: 24 Hours


                job_filter      (str)   --  type of jobs to filter

                        for multiple filters, give the values **comma(,)** separated

                        List of Possible Values:

                            Backup

                            Restore

                            AUXCOPY

                            WORKFLOW

                            etc..

                    http://documentation.commvault.com/commvault/v11/article?p=features/rest_api/operations/get_job.htm
                        to get the complete list of filters available

                    default: None


                options         (dict)  --  dict of key-word arguments

                Available Options:

                    limit           (int)   --  total number of jobs list that are to be returned

                        default: 20

                    offset          (int)   --  value from which starting job to be returned is counted

                            default: 0

                    show_aged_job   (bool)  --  boolean specifying whether to include aged jobs in
                    the result or not

                        default: False

                    hide_admin_jobs (bool)  --  boolean specifying whether to exclude admin jobs from
                    the result or not

                        default: False

                    clients_list    (list)  --  list of clients to return the jobs for

                        default: []

                    job_type_list   (list)  --  list of job operation types

                        default: []

                    job_summary     (str)   --  To return the basic job summary or full job summary

                        default: basic

                        accepted values: [&#39;basic&#39;, &#39;full&#39;]

                    entity          (dict)  --  dict containing entity details to which associated jobs has to be fetched

                        Example : To fetch job details of particular data source id

                                &#34;entity&#34;: {
                                            &#34;dataSourceId&#34;: 2575
                                            }

            Returns:
                dict    -   dictionary consisting of the job IDs matching the given criteria
                as the key, and their details as its value

            Raises:
                SDKException:
                    if client name is given, and no client exists with the given name

        &#34;&#34;&#34;
        options[&#39;category&#39;] = &#39;FINISHED&#39;
        options[&#39;lookup_time&#39;] = lookup_time

        if job_filter:
            options[&#39;job_type_list&#39;] = options.get(&#39;job_type_list&#39;, []) + job_filter.split(&#39;,&#39;)

        if client_name:
            options[&#39;clients_list&#39;] = options.get(&#39;clients_list&#39;, []) + [client_name]

        return self._get_jobs_list(**options)

    def suspend_all_jobs(self):
        &#34;&#34;&#34; Suspends all the jobs on the commserver &#34;&#34;&#34;
        self._modify_all_jobs(&#39;suspend&#39;)

    def resume_all_jobs(self):
        &#34;&#34;&#34; Resumes all the jobs on the commserver &#34;&#34;&#34;
        self._modify_all_jobs(&#39;resume&#39;)

    def kill_all_jobs(self):
        &#34;&#34;&#34; Kills all the jobs on the commserver &#34;&#34;&#34;
        self._modify_all_jobs(&#39;kill&#39;)

    def get(self, job_id):
        &#34;&#34;&#34;Returns the job object for the given job id.

            Args:
                job_id  (int)   --  id of the job to create Job class instance for

            Returns:
                object  -   Job class object for the given job id

            Raises:
                SDKException:
                    if no job with specified job id exists

        &#34;&#34;&#34;
        return Job(self._commcell_object, job_id)</code></pre>
</details>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.job.JobController.active_jobs"><code class="name flex">
<span>def <span class="ident">active_jobs</span></span>(<span>self, client_name=None, lookup_time=1, job_filter=None, **options)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the dict consisting of all the active jobs currently being executed on the
Commcell within the number of hours specified in lookup time value.</p>
<h2 id="args">Args</h2>
<p>client_name
(str)
&ndash;
name of the client to filter out the jobs for</p>
<pre><code>default: None, get all the jobs
</code></pre>
<p>lookup_time
(int)
&ndash;
get all the jobs executed within the number of hours</p>
<pre><code>default: 1 Hour(s)
</code></pre>
<p>job_filter
(str)
&ndash;
type of jobs to filter</p>
<pre><code>    for multiple filters, give the values **comma(,)** separated

    List of Possible Values:

        Backup

        Restore

        AUXCOPY

        WORKFLOW

        etc..

&lt;http://documentation.commvault.com/commvault/v11/article?p=features/rest_api/operations/get_job.htm&gt;
    to get the complete list of filters available

default: None
</code></pre>
<p>options
(dict)
&ndash;
dict of key-word arguments</p>
<p>Available Options:</p>
<pre><code>limit           (int)   --  total number of jobs list that are to be returned

    default: 20

offset          (int)   --  value from which starting job to be returned is counted

    default: 0

show_aged_job   (bool)  --  boolean specifying whether to include aged jobs in
the result or not

    default: False

hide_admin_jobs (bool)  --  boolean specifying whether to exclude admin jobs from
the result or not

    default: False

clients_list    (list)  --  list of clients to return the jobs for

    default: []

job_type_list   (list)  --  list of job operation types

    default: []

job_summary     (str)   --  To return the basic job summary or full job summary

    default: basic

    accepted values: ['basic', 'full']

entity          (dict)  --  dict containing entity details to which associated jobs has to be fetched

    Example : To fetch job details of particular data source id

            "entity": {
                        "dataSourceId": 2575
                        }
</code></pre>
<h2 id="returns">Returns</h2>
<p>dict
-
dictionary consisting of the job IDs matching the given criteria
as the key, and their details as its value</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if client name is given, and no client exists with the given name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L710-L808" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def active_jobs(self, client_name=None, lookup_time=1, job_filter=None, **options):
    &#34;&#34;&#34;Returns the dict consisting of all the active jobs currently being executed on the
        Commcell within the number of hours specified in lookup time value.

        Args:
            client_name     (str)   --  name of the client to filter out the jobs for

                default: None, get all the jobs


            lookup_time     (int)   --  get all the jobs executed within the number of hours

                default: 1 Hour(s)


            job_filter      (str)   --  type of jobs to filter

                    for multiple filters, give the values **comma(,)** separated

                    List of Possible Values:

                        Backup

                        Restore

                        AUXCOPY

                        WORKFLOW

                        etc..

                http://documentation.commvault.com/commvault/v11/article?p=features/rest_api/operations/get_job.htm
                    to get the complete list of filters available

                default: None

            options         (dict)  --  dict of key-word arguments

            Available Options:

                limit           (int)   --  total number of jobs list that are to be returned

                    default: 20

                offset          (int)   --  value from which starting job to be returned is counted

                    default: 0

                show_aged_job   (bool)  --  boolean specifying whether to include aged jobs in
                the result or not

                    default: False

                hide_admin_jobs (bool)  --  boolean specifying whether to exclude admin jobs from
                the result or not

                    default: False

                clients_list    (list)  --  list of clients to return the jobs for

                    default: []

                job_type_list   (list)  --  list of job operation types

                    default: []

                job_summary     (str)   --  To return the basic job summary or full job summary

                    default: basic

                    accepted values: [&#39;basic&#39;, &#39;full&#39;]

                entity          (dict)  --  dict containing entity details to which associated jobs has to be fetched

                    Example : To fetch job details of particular data source id

                            &#34;entity&#34;: {
                                        &#34;dataSourceId&#34;: 2575
                                        }

        Returns:
            dict    -   dictionary consisting of the job IDs matching the given criteria
            as the key, and their details as its value

        Raises:
            SDKException:
                if client name is given, and no client exists with the given name

    &#34;&#34;&#34;
    options[&#39;category&#39;] = &#39;ACTIVE&#39;
    options[&#39;lookup_time&#39;] = lookup_time

    if job_filter:
        options[&#39;job_type_list&#39;] = options.get(&#39;job_type_list&#39;, []) + job_filter.split(&#39;,&#39;)

    if client_name:
        options[&#39;clients_list&#39;] = options.get(&#39;clients_list&#39;, []) + [client_name]

    return self._get_jobs_list(**options)</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.JobController.all_jobs"><code class="name flex">
<span>def <span class="ident">all_jobs</span></span>(<span>self, client_name=None, lookup_time=5, job_filter=None, **options)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the dict consisting of all the jobs executed on the Commcell within the number
of hours specified in lookup time value.</p>
<h2 id="args">Args</h2>
<p>client_name
(str)
&ndash;
name of the client to filter out the jobs for</p>
<pre><code>default: None, get all the jobs
</code></pre>
<p>lookup_time
(int)
&ndash;
get all the jobs executed within the number of hours</p>
<pre><code>default: 5 Hours
</code></pre>
<p>job_filter
(str)
&ndash;
type of jobs to filter</p>
<pre><code>    for multiple filters, give the values **comma(,)** separated

    List of Possible Values:

        Backup

        Restore

        AUXCOPY

        WORKFLOW

        etc..

&lt;http://documentation.commvault.com/commvault/v11/article?p=features/rest_api/operations/get_job.htm&gt;
    to get the complete list of filters available

default: None
</code></pre>
<p>options
(dict)
&ndash;
dict of key-word arguments</p>
<p>Available Options:</p>
<pre><code>limit           (int)   --  total number of jobs list that are to be returned
    default: 20

offset           (int)  --  value from which starting job to be returned is counted

    default: 0

show_aged_job   (bool)  --  boolean specifying whether to include aged jobs in
the result or not

    default: False

hide_admin_jobs (bool)  --  boolean specifying whether to exclude admin jobs from
the result or not

    default: False

clients_list    (list)  --  list of clients to return the jobs for

    default: []

job_type_list   (list)  --  list of job operation types

    default: []

job_summary     (str)   --  To return the basic job summary or full job summary

    default: basic

    accepted values: ['basic', 'full']
</code></pre>
<h2 id="returns">Returns</h2>
<p>dict
-
dictionary consisting of the job IDs matching the given criteria
as the key, and their details as its value</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if client name is given, and no client exists with the given name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L619-L708" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def all_jobs(self, client_name=None, lookup_time=5, job_filter=None, **options):
    &#34;&#34;&#34;Returns the dict consisting of all the jobs executed on the Commcell within the number
        of hours specified in lookup time value.

        Args:
            client_name     (str)   --  name of the client to filter out the jobs for

                default: None, get all the jobs


            lookup_time     (int)   --  get all the jobs executed within the number of hours

                default: 5 Hours


            job_filter      (str)   --  type of jobs to filter

                    for multiple filters, give the values **comma(,)** separated

                    List of Possible Values:

                        Backup

                        Restore

                        AUXCOPY

                        WORKFLOW

                        etc..

                http://documentation.commvault.com/commvault/v11/article?p=features/rest_api/operations/get_job.htm
                    to get the complete list of filters available

                default: None

            options         (dict)  --  dict of key-word arguments

            Available Options:

                limit           (int)   --  total number of jobs list that are to be returned
                    default: 20

                offset           (int)  --  value from which starting job to be returned is counted

                    default: 0

                show_aged_job   (bool)  --  boolean specifying whether to include aged jobs in
                the result or not

                    default: False

                hide_admin_jobs (bool)  --  boolean specifying whether to exclude admin jobs from
                the result or not

                    default: False

                clients_list    (list)  --  list of clients to return the jobs for

                    default: []

                job_type_list   (list)  --  list of job operation types

                    default: []

                job_summary     (str)   --  To return the basic job summary or full job summary

                    default: basic

                    accepted values: [&#39;basic&#39;, &#39;full&#39;]

        Returns:
            dict    -   dictionary consisting of the job IDs matching the given criteria
            as the key, and their details as its value

        Raises:
            SDKException:
                if client name is given, and no client exists with the given name

    &#34;&#34;&#34;
    options[&#39;category&#39;] = &#39;ALL&#39;
    options[&#39;lookup_time&#39;] = lookup_time

    if job_filter:
        options[&#39;job_type_list&#39;] = options.get(&#39;job_type_list&#39;, []) + job_filter.split(&#39;,&#39;)

    if client_name:
        options[&#39;clients_list&#39;] = options.get(&#39;clients_list&#39;, []) + [client_name]

    return self._get_jobs_list(**options)</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.JobController.finished_jobs"><code class="name flex">
<span>def <span class="ident">finished_jobs</span></span>(<span>self, client_name=None, lookup_time=24, job_filter=None, **options)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the dict consisting of all the finished jobs on the Commcell within the number
of hours specified in lookup time value.</p>
<h2 id="args">Args</h2>
<p>client_name
(str)
&ndash;
name of the client to filter out the jobs for</p>
<pre><code>default: None, get all the jobs ir-respective of client
</code></pre>
<p>lookup_time
(int)
&ndash;
get all the jobs executed within the number of hours</p>
<pre><code>default: 24 Hours
</code></pre>
<p>job_filter
(str)
&ndash;
type of jobs to filter</p>
<pre><code>    for multiple filters, give the values **comma(,)** separated

    List of Possible Values:

        Backup

        Restore

        AUXCOPY

        WORKFLOW

        etc..

&lt;http://documentation.commvault.com/commvault/v11/article?p=features/rest_api/operations/get_job.htm&gt;
    to get the complete list of filters available

default: None
</code></pre>
<p>options
(dict)
&ndash;
dict of key-word arguments</p>
<p>Available Options:</p>
<pre><code>limit           (int)   --  total number of jobs list that are to be returned

    default: 20

offset          (int)   --  value from which starting job to be returned is counted

        default: 0

show_aged_job   (bool)  --  boolean specifying whether to include aged jobs in
the result or not

    default: False

hide_admin_jobs (bool)  --  boolean specifying whether to exclude admin jobs from
the result or not

    default: False

clients_list    (list)  --  list of clients to return the jobs for

    default: []

job_type_list   (list)  --  list of job operation types

    default: []

job_summary     (str)   --  To return the basic job summary or full job summary

    default: basic

    accepted values: ['basic', 'full']

entity          (dict)  --  dict containing entity details to which associated jobs has to be fetched

    Example : To fetch job details of particular data source id

            "entity": {
                        "dataSourceId": 2575
                        }
</code></pre>
<h2 id="returns">Returns</h2>
<p>dict
-
dictionary consisting of the job IDs matching the given criteria
as the key, and their details as its value</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if client name is given, and no client exists with the given name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L810-L909" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def finished_jobs(self, client_name=None, lookup_time=24, job_filter=None, **options):
    &#34;&#34;&#34;Returns the dict consisting of all the finished jobs on the Commcell within the number
        of hours specified in lookup time value.

        Args:
            client_name     (str)   --  name of the client to filter out the jobs for

                default: None, get all the jobs ir-respective of client


            lookup_time     (int)   --  get all the jobs executed within the number of hours

                default: 24 Hours


            job_filter      (str)   --  type of jobs to filter

                    for multiple filters, give the values **comma(,)** separated

                    List of Possible Values:

                        Backup

                        Restore

                        AUXCOPY

                        WORKFLOW

                        etc..

                http://documentation.commvault.com/commvault/v11/article?p=features/rest_api/operations/get_job.htm
                    to get the complete list of filters available

                default: None


            options         (dict)  --  dict of key-word arguments

            Available Options:

                limit           (int)   --  total number of jobs list that are to be returned

                    default: 20

                offset          (int)   --  value from which starting job to be returned is counted

                        default: 0

                show_aged_job   (bool)  --  boolean specifying whether to include aged jobs in
                the result or not

                    default: False

                hide_admin_jobs (bool)  --  boolean specifying whether to exclude admin jobs from
                the result or not

                    default: False

                clients_list    (list)  --  list of clients to return the jobs for

                    default: []

                job_type_list   (list)  --  list of job operation types

                    default: []

                job_summary     (str)   --  To return the basic job summary or full job summary

                    default: basic

                    accepted values: [&#39;basic&#39;, &#39;full&#39;]

                entity          (dict)  --  dict containing entity details to which associated jobs has to be fetched

                    Example : To fetch job details of particular data source id

                            &#34;entity&#34;: {
                                        &#34;dataSourceId&#34;: 2575
                                        }

        Returns:
            dict    -   dictionary consisting of the job IDs matching the given criteria
            as the key, and their details as its value

        Raises:
            SDKException:
                if client name is given, and no client exists with the given name

    &#34;&#34;&#34;
    options[&#39;category&#39;] = &#39;FINISHED&#39;
    options[&#39;lookup_time&#39;] = lookup_time

    if job_filter:
        options[&#39;job_type_list&#39;] = options.get(&#39;job_type_list&#39;, []) + job_filter.split(&#39;,&#39;)

    if client_name:
        options[&#39;clients_list&#39;] = options.get(&#39;clients_list&#39;, []) + [client_name]

    return self._get_jobs_list(**options)</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.JobController.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, job_id)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the job object for the given job id.</p>
<h2 id="args">Args</h2>
<p>job_id
(int)
&ndash;
id of the job to create Job class instance for</p>
<h2 id="returns">Returns</h2>
<p>object
-
Job class object for the given job id</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if no job with specified job id exists</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L923-L937" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, job_id):
    &#34;&#34;&#34;Returns the job object for the given job id.

        Args:
            job_id  (int)   --  id of the job to create Job class instance for

        Returns:
            object  -   Job class object for the given job id

        Raises:
            SDKException:
                if no job with specified job id exists

    &#34;&#34;&#34;
    return Job(self._commcell_object, job_id)</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.JobController.kill_all_jobs"><code class="name flex">
<span>def <span class="ident">kill_all_jobs</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Kills all the jobs on the commserver</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L919-L921" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def kill_all_jobs(self):
    &#34;&#34;&#34; Kills all the jobs on the commserver &#34;&#34;&#34;
    self._modify_all_jobs(&#39;kill&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.JobController.resume_all_jobs"><code class="name flex">
<span>def <span class="ident">resume_all_jobs</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Resumes all the jobs on the commserver</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L915-L917" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def resume_all_jobs(self):
    &#34;&#34;&#34; Resumes all the jobs on the commserver &#34;&#34;&#34;
    self._modify_all_jobs(&#39;resume&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.JobController.suspend_all_jobs"><code class="name flex">
<span>def <span class="ident">suspend_all_jobs</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Suspends all the jobs on the commserver</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L911-L913" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def suspend_all_jobs(self):
    &#34;&#34;&#34; Suspends all the jobs on the commserver &#34;&#34;&#34;
    self._modify_all_jobs(&#39;suspend&#39;)</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.job.JobManagement"><code class="flex name class">
<span>class <span class="ident">JobManagement</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for performing job management operations. </p>
<p>Initialize instance of JobManagement class for performing operations on jon management settings.</p>
<pre><code>Args:
    commcell_object         (object)        --  instance of Commcell class.

Returns:
    None
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L940-L1965" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class JobManagement(object):
    &#34;&#34;&#34;Class for performing job management operations. &#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;
        Initialize instance of JobManagement class for performing operations on jon management settings.

            Args:
                commcell_object         (object)        --  instance of Commcell class.

            Returns:
                None

        &#34;&#34;&#34;
        self._comcell = commcell_object
        self._service = commcell_object._services.get(&#39;JOB_MANAGEMENT_SETTINGS&#39;)
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._error_rules = None
        self.refresh()

    @property
    def error_rules(self):
        if not self._error_rules:
            self._error_rules = _ErrorRule(self._comcell)
        return self._error_rules

    def _set_jobmanagement_settings(self):
        &#34;&#34;&#34;
        Executes a request on the server, to set the job management settings.

         Returns:
               None

         Raises:
              SDKException:
                    if given inputs are invalid

        &#34;&#34;&#34;

        flag, response = self._cvpysdk_object.make_request(method=&#39;POST&#39;, url=self._service,
                                                           payload=self._settings_dict)
        if flag:
            if response and response.json():
                if response.json().get(&#39;errorCode&#39;, 0) != 0:
                    raise SDKException(&#39;Job&#39;, &#39;102&#39;, &#39;Failed to set job management properties. \nError: {0}&#39;.format(
                        response.json().get(&#39;errorMessage&#39;, &#39;&#39;)))
                self.refresh()
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response.json()[&#34;errorMessage&#34;])

    def _get_jobmanagement_settings(self):
        &#34;&#34;&#34;
         Executes a request on the server to get the settings of job management.

            Returns:
                None

            Raises:
                SDKException
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(method=&#39;GET&#39;, url=self._service)
        if flag:
            if response and response.json():
                self._settings_dict = response.json()
                if self._settings_dict.get(&#39;errorCode&#39;, 0) != 0:
                    raise SDKException(&#39;Job&#39;, &#39;102&#39;, &#39;Failed to get job management properties. \nError: {0}&#39;.format(
                        self._settings_dict.get(&#39;errorMessage&#39;, &#39;&#39;)))
                if &#39;jobManagementSettings&#39; in self._settings_dict:
                    self._restart_settings = {&#39;jobRestartSettings&#39;: self._settings_dict.get(
                        &#39;jobManagementSettings&#39;).get(&#39;jobRestartSettings&#39;, {})}
                    self._priority_settings = {&#39;jobPrioritySettings&#39;: self._settings_dict.get(
                        &#39;jobManagementSettings&#39;).get(&#39;jobPrioritySettings&#39;, {})}
                    self._general_settings = {&#39;generalSettings&#39;: self._settings_dict.get(
                        &#39;jobManagementSettings&#39;).get(&#39;generalSettings&#39;, {})}
                    self._update_settings = {&#39;jobUpdatesSettings&#39;: self._settings_dict.get(
                        &#39;jobManagementSettings&#39;).get(&#39;jobUpdatesSettings&#39;, {})}
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._comcell._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def refresh(self):
        &#34;&#34;&#34;
        calls the private method _get_jobmanagement_settings()
        &#34;&#34;&#34;
        self._restart_settings = None
        self._general_settings = None
        self._update_settings = None
        self._priority_settings = None
        self._get_jobmanagement_settings()

    def set_general_settings(self, settings):
        &#34;&#34;&#34;
        sets general settings of job management.

        Note : dedicated setters and getters are provided for general settings.
            Args:
                settings (dict)  --       Following key/value pairs can be set.
                                            {
                                                &#34;allowRunningJobsToCompletePastOperationWindow&#34;: False,
                                                &#34;jobAliveCheckIntervalInMinutes&#34;: 5,
                                                &#34;queueScheduledJobs&#34;: False,
                                                &#34;enableJobThrottleAtClientLevel&#34;: False,
                                                &#34;enableMultiplexingForDBAgents&#34;: False,
                                                &#34;queueJobsIfConflictingJobsActive&#34;: False,
                                                &#34;queueJobsIfActivityDisabled&#34;: False,
                                                &#34;backupsPreemptsAuxilaryCopy&#34;: False,
                                                &#34;restorePreemptsOtherJobs&#34;: False,
                                                &#34;enableMultiplexingForOracle&#34;: False,
                                                &#34;jobStreamHighWaterMarkLevel&#34;: 500,
                                                &#34;backupsPreemptsOtherBackups&#34;: False,
                                                &#34;doNotStartBackupsOnDisabledClient&#34;: False

                                            }
            Returns:
                None

            Raises:
                SDKException:
                    if input is not valid type
        &#34;&#34;&#34;
        if isinstance(settings, dict):
            self._general_settings.get(&#39;generalSettings&#39;).update(settings)
            self._set_jobmanagement_settings()
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    def set_priority_settings(self, settings):
        &#34;&#34;&#34;
        sets priority settings for jobs and agents type.

            Args:
                settings  (list)    --  list of dictionaries with following format.
                                         [
                                            {
                                                &#34;type_of_operation&#34;: 1,
                                                &#34;combinedPriority&#34;: 10,
                                                &#34;jobTypeName&#34;: &#34;Information Management&#34;
                                            },
                                            {
                                                &#34;type_of_operation&#34;: 2,
                                                &#34;combinedPriority&#34;: 10,
                                                &#34;appTypeName&#34;: &#34;Windows File System&#34;
                                            },
                                            {
                                            &#34;type_of_operation&#34;: 1,
                                            &#34;combinedPriority&#34;: 10,
                                            &#34;jobTypeName&#34;: &#34;Auxiliary Copy&#34;
                                             }
                                        ]

            We have priority settings fro jobtype and agenttype

            NOTE : for setting, priority for jobtype the &#39;type_of_operation&#39; must be set to 1 and name of the job type
                   must be specified as below format.

                       ex :-  &#34;jobTypeName&#34;: &#34;Information Management&#34;

            NOTE : for setting, priority for agenttype the &#39;type_of_operation&#39; must be set to 2 and name of the job
             type must be specified as below format

                        ex :- &#34;appTypeName&#34;: &#34;Windows File System&#34;

            Returns:
                None

            Raises:
                SDKException:
                    if input is not valid type

        &#34;&#34;&#34;
        if isinstance(settings, list):
            for job in settings:
                if job[&#34;type_of_operation&#34;] == 1:
                    for job_type in self._priority_settings[&#39;jobPrioritySettings&#39;][&#39;jobTypePriorityList&#39;]:
                        if job_type[&#39;jobTypeName&#39;] == job.get(&#34;jobTypeName&#34;):
                            job.pop(&#34;jobTypeName&#34;)
                            job.pop(&#34;type_of_operation&#34;)
                            job_type.update(job)
                            break
                elif job[&#34;type_of_operation&#34;] == 2:
                    for job_type in self._priority_settings[&#39;jobPrioritySettings&#39;][&#39;agentTypePriorityList&#39;]:
                        if job_type[&#39;agentTypeEntity&#39;][&#39;appTypeName&#39;] == job.get(&#34;appTypeName&#34;):
                            job.pop(&#34;appTypeName&#34;)
                            job.pop(&#34;type_of_operation&#34;)
                            job_type.update(job)
                            break
            self._set_jobmanagement_settings()
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    def set_restart_settings(self, settings):
        &#34;&#34;&#34;
        sets restart settings for jobs.

            Args:
                settings    (list)      --  list of dictionaries with following format
                                            [
                                                {
                                                    &#34;killRunningJobWhenTotalRunningTimeExpires&#34;: False,
                                                    &#34;maxRestarts&#34;: 10,
                                                    &#34;enableTotalRunningTime&#34;: False,
                                                    &#34;restartable&#34;: False,
                                                    &#34;jobTypeName&#34;: &#34;File System and Indexing Based (Data Protection)&#34;,
                                                    &#34;restartIntervalInMinutes&#34;: 20,
                                                    &#34;preemptable&#34;: True,
                                                    &#34;totalRunningTime&#34;: 21600,
                                                    &#34;jobType&#34;: 6
                                                },
                                                {
                                                    &#34;killRunningJobWhenTotalRunningTimeExpires&#34;: False,
                                                    &#34;maxRestarts&#34;: 144,
                                                    &#34;enableTotalRunningTime&#34;: False,
                                                    &#34;restartable&#34;: False,
                                                    &#34;jobTypeName&#34;: &#34;File System and Indexing Based (Data Recovery)&#34;,
                                                    &#34;restartIntervalInMinutes&#34;: 20,
                                                    &#34;preemptable&#34;: False,
                                                    &#34;totalRunningTime&#34;: 21600,
                                                    &#34;jobType&#34;: 7
                                                }
                                            ]

            Returns:
                None

            Raises:
                SDKException:
                    if input is not valid type

        &#34;&#34;&#34;

        if isinstance(settings, list):
            for job in settings:
                target = {&#39;target&#39;: job_type for job_type in
                          self._restart_settings[&#39;jobRestartSettings&#39;][&#39;jobTypeRestartSettingList&#39;]
                          if job_type[&#39;jobTypeName&#39;] == job.get(&#34;jobTypeName&#34;)}
                target.get(&#39;target&#39;).update(job)
            self._set_jobmanagement_settings()
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    def set_update_settings(self, settings):
        &#34;&#34;&#34;
        sets update settings for jobs

            Args:
                settings    (list)      --      list of dictionaries with following format
                                                [
                                                    {
                                                        &#34;appTypeName&#34;: &#34;Windows File System&#34;,
                                                        &#34;recoveryTimeInMinutes&#34;: 20,
                                                        &#34;protectionTimeInMinutes&#34;: 20
                                                    },
                                                    {
                                                        &#34;appTypeName&#34;: &#34;Windows XP 64-bit File System&#34;,
                                                        &#34;recoveryTimeInMinutes&#34;: 20,
                                                        &#34;protectionTimeInMinutes&#34;: 20,
                                                    }
                                                ]
            Returns:
                None

            Raises:
                SDKException:
                    if input is not valid type

        &#34;&#34;&#34;

        if isinstance(settings, list):
            for job in settings:
                for job_type in self._update_settings[&#39;jobUpdatesSettings&#39;][&#39;agentTypeJobUpdateIntervalList&#39;]:
                    if job_type[&#39;agentTypeEntity&#39;][&#39;appTypeName&#39;] == job.get(&#34;appTypeName&#34;):
                        job.pop(&#34;appTypeName&#34;)
                        job_type.update(job)
                        break
            self._set_jobmanagement_settings()
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    @property
    def job_priority_precedence(self):
        &#34;&#34;&#34;
        gets the job priority precedence
            Returns:
                 (str)  --   type of job priority precedence is set.
        &#34;&#34;&#34;

        available_priorities = {
            1: &#34;client&#34;,
            2: &#34;agentType&#34;
        }
        return available_priorities.get(self._priority_settings[&#34;jobPrioritySettings&#34;][&#34;priorityPrecedence&#34;])

    @job_priority_precedence.setter
    def job_priority_precedence(self, priority_type):
        &#34;&#34;&#34;
        sets job priority precedence

                Args:
                    priority_type   (str)   --      type of priority to be set

                    Values:
                        &#34;client&#34;
                        &#34;agentType&#34;

        &#34;&#34;&#34;
        if isinstance(priority_type, str):
            available_priorities = {
                &#34;client&#34;: 1,
                &#34;agentType&#34;: 2
            }
            self._priority_settings[&#34;jobPrioritySettings&#34;][&#34;priorityPrecedence&#34;] = available_priorities[priority_type]
            self._set_jobmanagement_settings()
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    @property
    def start_phase_retry_interval(self):
        &#34;&#34;&#34;
        gets the start phase retry interval in (minutes)
            Returns:
                 (int)      --      interval in minutes.
        &#34;&#34;&#34;
        return self._restart_settings[&#34;jobRestartSettings&#34;][&#34;startPhaseRetryIntervalInMinutes&#34;]

    @start_phase_retry_interval.setter
    def start_phase_retry_interval(self, minutes):
        &#34;&#34;&#34;
        sets start phase retry interval for jobs

            Args:
                minutes     (int)       --      minutes to be set.

            Raises:
                SDKException:
                    if input is not valid type.
        &#34;&#34;&#34;

        if isinstance(minutes, int):
            self._restart_settings[&#34;jobRestartSettings&#34;][&#34;startPhaseRetryIntervalInMinutes&#34;] = minutes
            self._set_jobmanagement_settings()
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    @property
    def state_update_interval_for_continuous_data_replicator(self):
        &#34;&#34;&#34;
        gets the state update interval for continuous data replicator in (minutes)
            Returns:
                 (int)      --      interval in minutes
        &#34;&#34;&#34;
        return self._update_settings[&#34;jobUpdatesSettings&#34;][&#34;stateUpdateIntervalForContinuousDataReplicator&#34;]

    @state_update_interval_for_continuous_data_replicator.setter
    def state_update_interval_for_continuous_data_replicator(self, minutes):
        &#34;&#34;&#34;
        sets state update interval for continuous data replicator

            Args:
                 minutes       (int)        --      minutes to be set.

            Raises:
                SDKException:
                    if input is not valid type
        &#34;&#34;&#34;
        if isinstance(minutes, int):
            self._update_settings[&#34;jobUpdatesSettings&#34;][&#34;stateUpdateIntervalForContinuousDataReplicator&#34;] = minutes
            self._set_jobmanagement_settings()
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    @property
    def allow_running_jobs_to_complete_past_operation_window(self):
        &#34;&#34;&#34;
        Returns True if option is enabled
        else returns false
        &#34;&#34;&#34;
        return self._general_settings.get(&#39;generalSettings&#39;).get(&#34;allowRunningJobsToCompletePastOperationWindow&#34;)

    @allow_running_jobs_to_complete_past_operation_window.setter
    def allow_running_jobs_to_complete_past_operation_window(self, flag):
        &#34;&#34;&#34;
        enable/disable, allow running jobs to complete past operation window.
            Args:
                flag    (bool)    --        (True/False) to be set.

            Raises:
                SDKException:
                    if input is not valid type
        &#34;&#34;&#34;
        if isinstance(flag, bool):
            settings = {
                &#34;allowRunningJobsToCompletePastOperationWindow&#34;: flag
            }
            self.set_general_settings(settings)
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    @property
    def job_alive_check_interval_in_minutes(self):
        &#34;&#34;&#34;
        gets the job alive check interval in (minutes)
            Returns:
                (int)       --      interval in minutes
        &#34;&#34;&#34;
        return self._general_settings.get(&#39;generalSettings&#39;).get(&#34;jobAliveCheckIntervalInMinutes&#34;)

    @job_alive_check_interval_in_minutes.setter
    def job_alive_check_interval_in_minutes(self, minutes):
        &#34;&#34;&#34;
        sets the job alive check interval in (minutes)
            Args:
                  minutes       --      minutes to be set.

            Raises:
                  SDKException:
                        if input is not valid type
        &#34;&#34;&#34;
        if isinstance(minutes, int):
            settings = {
                &#34;jobAliveCheckIntervalInMinutes&#34;: minutes
            }
            self.set_general_settings(settings)
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    @property
    def queue_scheduled_jobs(self):
        &#34;&#34;&#34;
        Returns True if option is enabled
        else returns false
        &#34;&#34;&#34;
        return self._general_settings.get(&#39;generalSettings&#39;).get(&#34;queueScheduledJobs&#34;)

    @queue_scheduled_jobs.setter
    def queue_scheduled_jobs(self, flag):
        &#34;&#34;&#34;
        enable/disable, queue scheduled jobs

            Args:
                flag   (bool)      --       (True/False to be set)

            Raises:
                SDKException:
                    if input is not valid type
        &#34;&#34;&#34;
        if isinstance(flag, bool):
            settings = {
                &#34;queueScheduledJobs&#34;: flag
            }
            self.set_general_settings(settings)
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    @property
    def enable_job_throttle_at_client_level(self):
        &#34;&#34;&#34;
        Returns True if option is enabled
        else returns false
        &#34;&#34;&#34;
        return self._general_settings.get(&#39;generalSettings&#39;).get(&#34;enableJobThrottleAtClientLevel&#34;)

    @enable_job_throttle_at_client_level.setter
    def enable_job_throttle_at_client_level(self, flag):
        &#34;&#34;&#34;
        enable/disable, job throttle at client level
            Args:
                flag    (bool)      --      (True/False) to be set

            Raises:
                SDKException:
                    if input is not valid type
        &#34;&#34;&#34;
        if isinstance(flag, bool):
            settings = {
                &#34;enableJobThrottleAtClientLevel&#34;: flag
            }
            self.set_general_settings(settings)
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    @property
    def enable_multiplexing_for_db_agents(self):
        &#34;&#34;&#34;
        Returns True if option is enabled
        else returns False
        &#34;&#34;&#34;
        return self._general_settings.get(&#39;generalSettings&#39;).get(&#34;enableMultiplexingForDBAgents&#34;)

    @enable_multiplexing_for_db_agents.setter
    def enable_multiplexing_for_db_agents(self, flag):
        &#34;&#34;&#34;
        enable/disable, multiplexing for db agents
            Args:
                flag    (bool)      --      (True/False) to be set

            Raises:
                SDKException:
                    if input is not valid type
        &#34;&#34;&#34;
        if isinstance(flag, bool):
            settings = {
                &#34;enableMultiplexingForDBAgents&#34;: flag
            }
            self.set_general_settings(settings)
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    @property
    def queue_jobs_if_conflicting_jobs_active(self):
        &#34;&#34;&#34;
        Returns True if option is enabled
        else returns false
        &#34;&#34;&#34;
        return self._general_settings.get(&#39;generalSettings&#39;).get(&#34;queueJobsIfConflictingJobsActive&#34;)

    @queue_jobs_if_conflicting_jobs_active.setter
    def queue_jobs_if_conflicting_jobs_active(self, flag):
        &#34;&#34;&#34;
        enable/disable, queue jobs if conflicting jobs active
            Args;
                flag    (bool)      --      (True/False) to be set

            Raises:
                SDKException:
                    if input is not valid type
        &#34;&#34;&#34;
        if isinstance(flag, bool):
            settings = {
                &#34;queueJobsIfConflictingJobsActive&#34;: flag
            }
            self.set_general_settings(settings)
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    @property
    def queue_jobs_if_activity_disabled(self):
        &#34;&#34;&#34;
        Returns True if option is enabled
        else returns False
        &#34;&#34;&#34;
        return self._general_settings.get(&#39;generalSettings&#39;).get(&#34;queueJobsIfActivityDisabled&#34;)

    @queue_jobs_if_activity_disabled.setter
    def queue_jobs_if_activity_disabled(self, flag):
        &#34;&#34;&#34;
        enable/disable, queue jobs if activity disabled
            Args;
                flag    (bool)      --      (True/False) to be set

            Raises:
                SDKException:
                    if input is not valid type
        &#34;&#34;&#34;
        if isinstance(flag, bool):
            settings = {
                &#34;queueJobsIfActivityDisabled&#34;: flag
            }
            self.set_general_settings(settings)
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    @property
    def backups_preempts_auxilary_copy(self):
        &#34;&#34;&#34;
        Returns True if option is enabled
        else returns False
        &#34;&#34;&#34;
        return self._general_settings.get(&#39;generalSettings&#39;).get(&#34;backupsPreemptsAuxilaryCopy&#34;)

    @backups_preempts_auxilary_copy.setter
    def backups_preempts_auxilary_copy(self, flag):
        &#34;&#34;&#34;
        enable/disable, backups preempts auxiliary copy
            Args:
                flag    (bool)      --      (True/False) to be set

            Raises:
                SDKException:
                    if input is not valid type
        &#34;&#34;&#34;
        if isinstance(flag, bool):
            settings = {
                &#34;backupsPreemptsAuxilaryCopy&#34;: flag
            }
            self.set_general_settings(settings)
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    @property
    def restore_preempts_other_jobs(self):
        &#34;&#34;&#34;
        Returns True if option is enabled
        else returns False
        &#34;&#34;&#34;
        return self._general_settings.get(&#39;generalSettings&#39;).get(&#34;restorePreemptsOtherJobs&#34;)

    @restore_preempts_other_jobs.setter
    def restore_preempts_other_jobs(self, flag):
        &#34;&#34;&#34;
        enable/disable, restore preempts other jobs
            Args:
                flag    (bool)      --      (True/False) to be set

            Raises:
                SDKException:
                    if input is not valid type
        &#34;&#34;&#34;
        if isinstance(flag, bool):
            settings = {
                &#34;restorePreemptsOtherJobs&#34;: flag
            }
            self.set_general_settings(settings)
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    @property
    def enable_multiplexing_for_oracle(self):
        &#34;&#34;&#34;
        Returns True if option is enabled
        else returns False
        &#34;&#34;&#34;
        return self._general_settings.get(&#39;generalSettings&#39;).get(&#34;enableMultiplexingForOracle&#34;)

    @enable_multiplexing_for_oracle.setter
    def enable_multiplexing_for_oracle(self, flag):
        &#34;&#34;&#34;
        enable/disable, enable multiplexing for oracle
            Args:
                 flag   (bool)  --      (True/False) to be set

            Raises:
                SDKException:
                    if input is not valid type
        &#34;&#34;&#34;
        if isinstance(flag, bool):
            settings = {
                &#34;enableMultiplexingForOracle&#34;: flag
            }
            self.set_general_settings(settings)
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    @property
    def job_stream_high_water_mark_level(self):
        &#34;&#34;&#34;
        gets the job stream high water mark level
        &#34;&#34;&#34;
        return self._general_settings.get(&#39;generalSettings&#39;).get(&#34;jobStreamHighWaterMarkLevel&#34;)

    @job_stream_high_water_mark_level.setter
    def job_stream_high_water_mark_level(self, level):
        &#34;&#34;&#34;
        sets, job stream high water mak level
            Args:
                level   (int)       --      number of jobs to be performed at a time

            Raises:
                SDKException:
                    if input is not valid type
        &#34;&#34;&#34;
        if isinstance(level, int):
            settings = {
                &#34;jobStreamHighWaterMarkLevel&#34;: level
            }
            self.set_general_settings(settings)
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    @property
    def backups_preempts_other_backups(self):
        &#34;&#34;&#34;
        Returns True if option is enabled
        else returns False
        &#34;&#34;&#34;
        return self._general_settings.get(&#39;generalSettings&#39;).get(&#34;backupsPreemptsOtherBackups&#34;)

    @backups_preempts_other_backups.setter
    def backups_preempts_other_backups(self, flag):
        &#34;&#34;&#34;
        enable/disable, backups preempts other backups
            Args:
                 flag   (bool)      --      (True/False) to be set

            Raises:
                SDKException:
                    if input is not a valid type
        &#34;&#34;&#34;
        if isinstance(flag, bool):
            settings = {
                &#34;backupsPreemptsOtherBackups&#34;: flag
            }
            self.set_general_settings(settings)
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    @property
    def do_not_start_backups_on_disabled_client(self):
        &#34;&#34;&#34;
        Returns True if option is enabled
        else returns False
        &#34;&#34;&#34;
        return self._general_settings.get(&#39;generalSettings&#39;).get(&#34;doNotStartBackupsOnDisabledClient&#34;)

    @do_not_start_backups_on_disabled_client.setter
    def do_not_start_backups_on_disabled_client(self, flag):
        &#34;&#34;&#34;
         enable/disable, do not start backups on disabled client
            Args:
                 flag   (bool)      --      (True/False) to be set

            Raises:
                SDKException:
                    if input is not a valid type
        &#34;&#34;&#34;
        if isinstance(flag, bool):
            settings = {
                &#34;doNotStartBackupsOnDisabledClient&#34;: flag
            }
            self.set_general_settings(settings)
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    def get_restart_setting(self, jobtype):
        &#34;&#34;&#34;
        restart settings associated to particular jobtype can be obtained
            Args:
                jobtype     (str)       --      settings of the jobtype to get

                Available jobtypes:

                        &#34;Disaster Recovery backup&#34;
                        &#34;Auxiliary Copy&#34;
                        &#34;Data Aging&#34;
                        &#34;Download/Copy Updates&#34;
                        &#34;Offline Content Indexing&#34;
                        &#34;Information Management&#34;
                        &#34;File System and Indexing Based (Data Protection)&#34;
                        &#34;File System and Indexing Based (Data Recovery)&#34;
                        &#34;Exchange DB (Data Protection)&#34;
                        &#34;Exchange DB (Data Recovery)&#34;
                        &#34;Informix DB (Data Protection)&#34;
                        &#34;Informix DB (Data Recovery)&#34;
                        &#34;Lotus Notes DB (Data Protection)&#34;
                        &#34;Lotus Notes DB (Data Recovery)&#34;
                        &#34;Oracle DB (Data Protection)&#34;
                        &#34;Oracle DB (Data Recovery)&#34;
                        &#34;SQL DB (Data Protection)&#34;
                        &#34;SQL DB (Data Recovery)&#34;
                        &#34;MYSQL (Data Protection)&#34;
        `               &#34;MYSQL (Data Recovery)&#34;
                        &#34;Sybase DB (Data Protection)&#34;
                        &#34;Sybase DB (Data Recovery)&#34;
                        &#34;DB2 (Data Protection)&#34;
                        &#34;DB2 (Data Recovery)&#34;
                        &#34;CDR (Data Management)&#34;
                        &#34;Media Refresh&#34;
                        &#34;Documentum (Data Protection)&#34;
                        &#34;Documentum (Data Recovery)&#34;
                        &#34;SAP for Oracle (Data Protection)&#34;
                        &#34;SAP for Oracle (Data Recovery)&#34;
                        &#34;PostgreSQL (Data Protection)&#34;
                        &#34;PostgreSQL (Data Recovery)&#34;
                        &#34;Other (Data Protection)&#34;
                        &#34;Other (Data Recovery)&#34;
                        &#34;Workflow&#34;
                        &#34;DeDup DB Reconstruction&#34;
                        &#34;CommCell Migration Export&#34;
                        &#34;CommCell Migration Import&#34;
                        &#34;Install Software&#34;
                        &#34;Uninstall Software&#34;
                        &#34;Data Verification&#34;
                        &#34;Big Data Apps (Data Protection)&#34;
                        &#34;Big Data Apps (Data Recovery)&#34;
                        &#34;Cloud Apps (Data Protection)&#34;
                        &#34;Cloud Apps (Data Recovery)&#34;
                        &#34;Virtual Server (Data Protection)&#34;
                        &#34;Virtual Server (Data Recovery)&#34;
                        &#34;SAP for Hana (Data Protection)&#34;
                        &#34;SAP for Hana (Data Recovery)&#34;



            Returns:
                dict          --        settings of the specific job type as follows
                                        {
                                            &#34;jobTypeName&#34;: &#34;File System and Indexing Based (Data Protection)&#34;,
                                            &#34;restartable&#34;: true,
                                            &#34;maxRestarts&#34;: 10,
                                            &#34;restartIntervalInMinutes&#34;: 20,
                                            &#34;enableTotalRunningTime&#34;: false,
                                            &#34;totalRunningTime&#34;: 25200,
                                            &#34;killRunningJobWhenTotalRunningTimeExpires&#34;: false,
                                            &#34;preemptable&#34;: true,

                                        }

            Raises:
                SDKException:
                    if input is not valid type

        &#34;&#34;&#34;
        if isinstance(jobtype, str):
            for job_type in self._restart_settings[&#39;jobRestartSettings&#39;][&#39;jobTypeRestartSettingList&#39;]:
                if job_type[&#39;jobTypeName&#39;] == jobtype:
                    settings = copy.deepcopy(job_type)
                    return settings
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    def get_priority_setting(self, jobtype):
        &#34;&#34;&#34;
        priority settings associated to particular jobtype can be obtained
            Args:
                jobtype     (str)       --      settings of jobtype to get

                Available values:

                    jobtypename:
                        &#34;Information Management&#34;
                        &#34;Auxiliary Copy&#34;
                        &#34;Media Refresh&#34;
                        &#34;Data Verification&#34;
                        &#34;Persistent Recovery&#34;
                        &#34;Synth Full&#34;

                    apptypename:
                        &#34;Windows File System&#34;
                        &#34;Windows XP 64-bit File System&#34;
                        &#34;Windows 2003 32-bit File System&#34;
                        &#34;Windows 2003 64-bit File System&#34;
                        &#34;Active Directory&#34;
                        &#34;Windows File Archiver&#34;
                        &#34;File Share Archiver&#34;
                        &#34;Image Level&#34;
                        &#34;Exchange Mailbox (Classic)&#34;
                        &#34;Exchange Mailbox Archiver&#34;
                        &#34;Exchange Compliance Archiver&#34;
                        &#34;Exchange Public Folder&#34;
                        &#34;Exchange Database&#34;
                        &#34;SharePoint Database&#34;
                        &#34;SharePoint Server Database&#34;
                        &#34;SharePoint Document&#34;
                        &#34;SharePoint Server&#34;
                        &#34;Novell Directory Services&#34;
                        &#34;GroupWise DB&#34;
                        &#34;NDMP&#34;
                        &#34;Notes Document&#34;
                        &#34;Unix Notes Database&#34;
                        &#34;MAC FileSystem&#34;
                        &#34;Big Data Apps&#34;
                        &#34;Solaris File System&#34;
                        &#34;Solaris 64bit File System&#34;
                        &#34;FreeBSD&#34;
                        &#34;HP-UX File System&#34;
                        &#34;HP-UX 64bit File System&#34;
                        &#34;AIX File System&#34;
                        &#34;Unix Tru64 64-bit File System&#34;
                        &#34;Linux File System&#34;
                        &#34;Sybase Database&#34;
                        &#34;Oracle Database&#34;
                        &#34;Oracle RAC&#34;
                        &#34;Informix Database&#34;
                        &#34;DB2&#34;
                        &#34;DB2 on Unix&#34;
                        &#34;SAP for Oracle&#34;
                        &#34;SAP for MAX DB&#34;
                        &#34;ProxyHost on Unix&#34;
                        &#34;ProxyHost&#34;
                        &#34;Image Level On Unix&#34;
                        &#34;OSSV Plug-in on Windows&#34;
                        &#34;OSSV Plug-in on Unix&#34;
                        &#34;Unix File Archiver&#34;
                        &#34;SQL Server&#34;
                        &#34;Data Classification&#34;
                        &#34;OES File System on Linux&#34;
                        &#34;Centera&#34;
                        &#34;Exchange PF Archiver&#34;
                        &#34;Domino Mailbox Archiver&#34;
                        &#34;MS SharePoint Archiver&#34;
                        &#34;Content Indexing Agent&#34;
                        &#34;SRM Agent For Windows File Systems&#34;
                        &#34;SRM Agent For UNIX File Systems&#34;
                        &#34;DB2 MultiNode&#34;
                        &#34;MySQL&#34;
                        &#34;Virtual Server&#34;
                        &#34;SharePoint Search Connector&#34;
                        &#34;Object Link&#34;
                        &#34;PostgreSQL&#34;
                        &#34;Sybase IQ&#34;
                        &#34;External Data Connector&#34;
                        &#34;Documentum&#34;
                        &#34;Object Store&#34;
                        &#34;SAP HANA&#34;
                        &#34;Cloud Apps&#34;
                        &#34;Exchange Mailbox&#34;

            Returns:
                dict        --          settings of a specific jobtype
                                        ex:
                                        {
                                            &#34;jobTypeName&#34;: &#34;Information Management&#34;,
                                            &#34;combinedPriority&#34;: 0,
                                            &#34;type_of_operation&#34;: 1
                                        }

                                        or

                                        settings of a specific apptype
                                        ex:
                                        {
                                            &#34;appTypeName&#34;: &#34;Windows File System&#34;,
                                            &#34;combinedPriority&#34;: 6,
                                            &#34;type_of_operation&#34;: 2
                                        }
            Raises:
                SDKException:
                    if input is not valid type

        &#34;&#34;&#34;
        if isinstance(jobtype, str):
            for job_type in self._priority_settings[&#39;jobPrioritySettings&#39;][&#39;jobTypePriorityList&#39;]:
                if job_type[&#39;jobTypeName&#39;] == jobtype:
                    settings = {
                        &#39;jobTypeName&#39;: job_type.get(&#39;jobTypeName&#39;),
                        &#39;combinedPriority&#39;: job_type.get(&#39;combinedPriority&#39;),
                        &#39;type_of_operation&#39;: 1
                    }
                    return settings
            for job_type in self._priority_settings[&#39;jobPrioritySettings&#39;][&#39;agentTypePriorityList&#39;]:
                if job_type[&#39;agentTypeEntity&#39;][&#39;appTypeName&#39;] == jobtype:
                    settings = {
                        &#39;appTypeName&#39;: job_type.get(&#39;agentTypeEntity&#39;).get(&#39;appTypeName&#39;),
                        &#39;combinedPriority&#39;: job_type.get(&#39;combinedPriority&#39;),
                        &#39;type_of_operation&#39;: 2
                    }
                    return settings
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    def get_update_setting(self, jobtype):
        &#34;&#34;&#34;
        update settings associated to particular jobtype can be obtained
            Args:
                jobtype     (str)       --      settings of jobtype to get

                Available jobtype

                    Check get_priority_setting(self, jobtype) method documentation.

            Returns:
                dict        -           settings of a jobtype
                                        {
                                            &#34;appTypeName&#34;: &#34;Windows File System&#34;,
                                            &#34;recoveryTimeInMinutes&#34;: 20,
                                            &#34;protectionTimeInMinutes&#34;: 20
                                        }
            Raises:
                SDKException:
                    if input is not valid type

        &#34;&#34;&#34;
        if isinstance(jobtype, str):
            for job_type in self._update_settings[&#39;jobUpdatesSettings&#39;][&#39;agentTypeJobUpdateIntervalList&#39;]:
                if job_type[&#39;agentTypeEntity&#39;][&#39;appTypeName&#39;] == jobtype:
                    settings = {
                        &#39;appTypeName&#39;: job_type.get(&#39;agentTypeEntity&#39;).get(&#39;appTypeName&#39;),
                        &#39;recoveryTimeInMinutes&#39;: job_type.get(&#39;recoveryTimeInMinutes&#39;),
                        &#39;protectionTimeInMinutes&#39;: job_type.get(&#39;protectionTimeInMinutes&#39;)
                    }
                    return settings
        else:
            raise SDKException(&#39;Job&#39;, &#39;108&#39;)

    @property
    def general_settings(self):
        &#34;&#34;&#34;
        gets the general settings.
             Returns:   (dict)      --  The general settings
        &#34;&#34;&#34;
        return self._general_settings

    @property
    def restart_settings(self):
        &#34;&#34;&#34;
        gets the restart settings.
                Returns:    (dict)    --  The restart settings.
        &#34;&#34;&#34;

        return self._restart_settings

    @property
    def priority_settings(self):
        &#34;&#34;&#34;
        gets the priority settings.
                Returns:    (dict)    --  The priority settings.
        &#34;&#34;&#34;

        return self._priority_settings

    @property
    def update_settings(self):
        &#34;&#34;&#34;
        gets the update settings.
                Returns:    (dict)    --  The update settings.
        &#34;&#34;&#34;

        return self._update_settings

    def set_job_error_threshold(self, error_threshold_dict):
        &#34;&#34;&#34;

        Args:
            error_threshold_dict  (dict)  :   A dictionary of following  key/value pairs can be set.

        Returns:
            None

        &#34;&#34;&#34;
        raise NotImplementedError(&#34;Yet To Be Implemented&#34;)</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.job.JobManagement.allow_running_jobs_to_complete_past_operation_window"><code class="name">var <span class="ident">allow_running_jobs_to_complete_past_operation_window</span></code></dt>
<dd>
<div class="desc"><p>Returns True if option is enabled
else returns false</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L1317-L1323" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def allow_running_jobs_to_complete_past_operation_window(self):
    &#34;&#34;&#34;
    Returns True if option is enabled
    else returns false
    &#34;&#34;&#34;
    return self._general_settings.get(&#39;generalSettings&#39;).get(&#34;allowRunningJobsToCompletePastOperationWindow&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.JobManagement.backups_preempts_auxilary_copy"><code class="name">var <span class="ident">backups_preempts_auxilary_copy</span></code></dt>
<dd>
<div class="desc"><p>Returns True if option is enabled
else returns False</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L1508-L1514" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def backups_preempts_auxilary_copy(self):
    &#34;&#34;&#34;
    Returns True if option is enabled
    else returns False
    &#34;&#34;&#34;
    return self._general_settings.get(&#39;generalSettings&#39;).get(&#34;backupsPreemptsAuxilaryCopy&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.JobManagement.backups_preempts_other_backups"><code class="name">var <span class="ident">backups_preempts_other_backups</span></code></dt>
<dd>
<div class="desc"><p>Returns True if option is enabled
else returns False</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L1615-L1621" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def backups_preempts_other_backups(self):
    &#34;&#34;&#34;
    Returns True if option is enabled
    else returns False
    &#34;&#34;&#34;
    return self._general_settings.get(&#39;generalSettings&#39;).get(&#34;backupsPreemptsOtherBackups&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.JobManagement.do_not_start_backups_on_disabled_client"><code class="name">var <span class="ident">do_not_start_backups_on_disabled_client</span></code></dt>
<dd>
<div class="desc"><p>Returns True if option is enabled
else returns False</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L1642-L1648" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def do_not_start_backups_on_disabled_client(self):
    &#34;&#34;&#34;
    Returns True if option is enabled
    else returns False
    &#34;&#34;&#34;
    return self._general_settings.get(&#39;generalSettings&#39;).get(&#34;doNotStartBackupsOnDisabledClient&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.JobManagement.enable_job_throttle_at_client_level"><code class="name">var <span class="ident">enable_job_throttle_at_client_level</span></code></dt>
<dd>
<div class="desc"><p>Returns True if option is enabled
else returns false</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L1400-L1406" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def enable_job_throttle_at_client_level(self):
    &#34;&#34;&#34;
    Returns True if option is enabled
    else returns false
    &#34;&#34;&#34;
    return self._general_settings.get(&#39;generalSettings&#39;).get(&#34;enableJobThrottleAtClientLevel&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.JobManagement.enable_multiplexing_for_db_agents"><code class="name">var <span class="ident">enable_multiplexing_for_db_agents</span></code></dt>
<dd>
<div class="desc"><p>Returns True if option is enabled
else returns False</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L1427-L1433" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def enable_multiplexing_for_db_agents(self):
    &#34;&#34;&#34;
    Returns True if option is enabled
    else returns False
    &#34;&#34;&#34;
    return self._general_settings.get(&#39;generalSettings&#39;).get(&#34;enableMultiplexingForDBAgents&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.JobManagement.enable_multiplexing_for_oracle"><code class="name">var <span class="ident">enable_multiplexing_for_oracle</span></code></dt>
<dd>
<div class="desc"><p>Returns True if option is enabled
else returns False</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L1562-L1568" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def enable_multiplexing_for_oracle(self):
    &#34;&#34;&#34;
    Returns True if option is enabled
    else returns False
    &#34;&#34;&#34;
    return self._general_settings.get(&#39;generalSettings&#39;).get(&#34;enableMultiplexingForOracle&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.JobManagement.error_rules"><code class="name">var <span class="ident">error_rules</span></code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L960-L964" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def error_rules(self):
    if not self._error_rules:
        self._error_rules = _ErrorRule(self._comcell)
    return self._error_rules</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.JobManagement.general_settings"><code class="name">var <span class="ident">general_settings</span></code></dt>
<dd>
<div class="desc"><p>gets the general settings.
Returns:
(dict)
&ndash;
The general settings</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L1920-L1926" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def general_settings(self):
    &#34;&#34;&#34;
    gets the general settings.
         Returns:   (dict)      --  The general settings
    &#34;&#34;&#34;
    return self._general_settings</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.JobManagement.job_alive_check_interval_in_minutes"><code class="name">var <span class="ident">job_alive_check_interval_in_minutes</span></code></dt>
<dd>
<div class="desc"><p>gets the job alive check interval in (minutes)
Returns:
(int)
&ndash;
interval in minutes</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L1344-L1351" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def job_alive_check_interval_in_minutes(self):
    &#34;&#34;&#34;
    gets the job alive check interval in (minutes)
        Returns:
            (int)       --      interval in minutes
    &#34;&#34;&#34;
    return self._general_settings.get(&#39;generalSettings&#39;).get(&#34;jobAliveCheckIntervalInMinutes&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.JobManagement.job_priority_precedence"><code class="name">var <span class="ident">job_priority_precedence</span></code></dt>
<dd>
<div class="desc"><p>gets the job priority precedence
Returns:
(str)
&ndash;
type of job priority precedence is set.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L1225-L1237" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def job_priority_precedence(self):
    &#34;&#34;&#34;
    gets the job priority precedence
        Returns:
             (str)  --   type of job priority precedence is set.
    &#34;&#34;&#34;

    available_priorities = {
        1: &#34;client&#34;,
        2: &#34;agentType&#34;
    }
    return available_priorities.get(self._priority_settings[&#34;jobPrioritySettings&#34;][&#34;priorityPrecedence&#34;])</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.JobManagement.job_stream_high_water_mark_level"><code class="name">var <span class="ident">job_stream_high_water_mark_level</span></code></dt>
<dd>
<div class="desc"><p>gets the job stream high water mark level</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L1589-L1594" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def job_stream_high_water_mark_level(self):
    &#34;&#34;&#34;
    gets the job stream high water mark level
    &#34;&#34;&#34;
    return self._general_settings.get(&#39;generalSettings&#39;).get(&#34;jobStreamHighWaterMarkLevel&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.JobManagement.priority_settings"><code class="name">var <span class="ident">priority_settings</span></code></dt>
<dd>
<div class="desc"><p>gets the priority settings.
Returns:
(dict)
&ndash;
The priority settings.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L1937-L1944" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def priority_settings(self):
    &#34;&#34;&#34;
    gets the priority settings.
            Returns:    (dict)    --  The priority settings.
    &#34;&#34;&#34;

    return self._priority_settings</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.JobManagement.queue_jobs_if_activity_disabled"><code class="name">var <span class="ident">queue_jobs_if_activity_disabled</span></code></dt>
<dd>
<div class="desc"><p>Returns True if option is enabled
else returns False</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L1481-L1487" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def queue_jobs_if_activity_disabled(self):
    &#34;&#34;&#34;
    Returns True if option is enabled
    else returns False
    &#34;&#34;&#34;
    return self._general_settings.get(&#39;generalSettings&#39;).get(&#34;queueJobsIfActivityDisabled&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.JobManagement.queue_jobs_if_conflicting_jobs_active"><code class="name">var <span class="ident">queue_jobs_if_conflicting_jobs_active</span></code></dt>
<dd>
<div class="desc"><p>Returns True if option is enabled
else returns false</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L1454-L1460" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def queue_jobs_if_conflicting_jobs_active(self):
    &#34;&#34;&#34;
    Returns True if option is enabled
    else returns false
    &#34;&#34;&#34;
    return self._general_settings.get(&#39;generalSettings&#39;).get(&#34;queueJobsIfConflictingJobsActive&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.JobManagement.queue_scheduled_jobs"><code class="name">var <span class="ident">queue_scheduled_jobs</span></code></dt>
<dd>
<div class="desc"><p>Returns True if option is enabled
else returns false</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L1372-L1378" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def queue_scheduled_jobs(self):
    &#34;&#34;&#34;
    Returns True if option is enabled
    else returns false
    &#34;&#34;&#34;
    return self._general_settings.get(&#39;generalSettings&#39;).get(&#34;queueScheduledJobs&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.JobManagement.restart_settings"><code class="name">var <span class="ident">restart_settings</span></code></dt>
<dd>
<div class="desc"><p>gets the restart settings.
Returns:
(dict)
&ndash;
The restart settings.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L1928-L1935" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def restart_settings(self):
    &#34;&#34;&#34;
    gets the restart settings.
            Returns:    (dict)    --  The restart settings.
    &#34;&#34;&#34;

    return self._restart_settings</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.JobManagement.restore_preempts_other_jobs"><code class="name">var <span class="ident">restore_preempts_other_jobs</span></code></dt>
<dd>
<div class="desc"><p>Returns True if option is enabled
else returns False</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L1535-L1541" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def restore_preempts_other_jobs(self):
    &#34;&#34;&#34;
    Returns True if option is enabled
    else returns False
    &#34;&#34;&#34;
    return self._general_settings.get(&#39;generalSettings&#39;).get(&#34;restorePreemptsOtherJobs&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.JobManagement.start_phase_retry_interval"><code class="name">var <span class="ident">start_phase_retry_interval</span></code></dt>
<dd>
<div class="desc"><p>gets the start phase retry interval in (minutes)
Returns:
(int)
&ndash;
interval in minutes.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L1262-L1269" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def start_phase_retry_interval(self):
    &#34;&#34;&#34;
    gets the start phase retry interval in (minutes)
        Returns:
             (int)      --      interval in minutes.
    &#34;&#34;&#34;
    return self._restart_settings[&#34;jobRestartSettings&#34;][&#34;startPhaseRetryIntervalInMinutes&#34;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.JobManagement.state_update_interval_for_continuous_data_replicator"><code class="name">var <span class="ident">state_update_interval_for_continuous_data_replicator</span></code></dt>
<dd>
<div class="desc"><p>gets the state update interval for continuous data replicator in (minutes)
Returns:
(int)
&ndash;
interval in minutes</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L1290-L1297" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def state_update_interval_for_continuous_data_replicator(self):
    &#34;&#34;&#34;
    gets the state update interval for continuous data replicator in (minutes)
        Returns:
             (int)      --      interval in minutes
    &#34;&#34;&#34;
    return self._update_settings[&#34;jobUpdatesSettings&#34;][&#34;stateUpdateIntervalForContinuousDataReplicator&#34;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.JobManagement.update_settings"><code class="name">var <span class="ident">update_settings</span></code></dt>
<dd>
<div class="desc"><p>gets the update settings.
Returns:
(dict)
&ndash;
The update settings.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L1946-L1953" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def update_settings(self):
    &#34;&#34;&#34;
    gets the update settings.
            Returns:    (dict)    --  The update settings.
    &#34;&#34;&#34;

    return self._update_settings</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.job.JobManagement.get_priority_setting"><code class="name flex">
<span>def <span class="ident">get_priority_setting</span></span>(<span>self, jobtype)</span>
</code></dt>
<dd>
<div class="desc"><p>priority settings associated to particular jobtype can be obtained
Args:
jobtype
(str)
&ndash;
settings of jobtype to get</p>
<pre><code>    Available values:

        jobtypename:
            "Information Management"
            "Auxiliary Copy"
            "Media Refresh"
            "Data Verification"
            "Persistent Recovery"
            "Synth Full"

        apptypename:
            "Windows File System"
            "Windows XP 64-bit File System"
            "Windows 2003 32-bit File System"
            "Windows 2003 64-bit File System"
            "Active Directory"
            "Windows File Archiver"
            "File Share Archiver"
            "Image Level"
            "Exchange Mailbox (Classic)"
            "Exchange Mailbox Archiver"
            "Exchange Compliance Archiver"
            "Exchange Public Folder"
            "Exchange Database"
            "SharePoint Database"
            "SharePoint Server Database"
            "SharePoint Document"
            "SharePoint Server"
            "Novell Directory Services"
            "GroupWise DB"
            "NDMP"
            "Notes Document"
            "Unix Notes Database"
            "MAC FileSystem"
            "Big Data Apps"
            "Solaris File System"
            "Solaris 64bit File System"
            "FreeBSD"
            "HP-UX File System"
            "HP-UX 64bit File System"
            "AIX File System"
            "Unix Tru64 64-bit File System"
            "Linux File System"
            "Sybase Database"
            "Oracle Database"
            "Oracle RAC"
            "Informix Database"
            "DB2"
            "DB2 on Unix"
            "SAP for Oracle"
            "SAP for MAX DB"
            "ProxyHost on Unix"
            "ProxyHost"
            "Image Level On Unix"
            "OSSV Plug-in on Windows"
            "OSSV Plug-in on Unix"
            "Unix File Archiver"
            "SQL Server"
            "Data Classification"
            "OES File System on Linux"
            "Centera"
            "Exchange PF Archiver"
            "Domino Mailbox Archiver"
            "MS SharePoint Archiver"
            "Content Indexing Agent"
            "SRM Agent For Windows File Systems"
            "SRM Agent For UNIX File Systems"
            "DB2 MultiNode"
            "MySQL"
            "Virtual Server"
            "SharePoint Search Connector"
            "Object Link"
            "PostgreSQL"
            "Sybase IQ"
            "External Data Connector"
            "Documentum"
            "Object Store"
            "SAP HANA"
            "Cloud Apps"
            "Exchange Mailbox"

Returns:
    dict        --          settings of a specific jobtype
                            ex:
                            {
                                "jobTypeName": "Information Management",
                                "combinedPriority": 0,
                                "type_of_operation": 1
                            }

                            or

                            settings of a specific apptype
                            ex:
                            {
                                "appTypeName": "Windows File System",
                                "combinedPriority": 6,
                                "type_of_operation": 2
                            }
Raises:
    SDKException:
        if input is not valid type
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L1756-L1884" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_priority_setting(self, jobtype):
    &#34;&#34;&#34;
    priority settings associated to particular jobtype can be obtained
        Args:
            jobtype     (str)       --      settings of jobtype to get

            Available values:

                jobtypename:
                    &#34;Information Management&#34;
                    &#34;Auxiliary Copy&#34;
                    &#34;Media Refresh&#34;
                    &#34;Data Verification&#34;
                    &#34;Persistent Recovery&#34;
                    &#34;Synth Full&#34;

                apptypename:
                    &#34;Windows File System&#34;
                    &#34;Windows XP 64-bit File System&#34;
                    &#34;Windows 2003 32-bit File System&#34;
                    &#34;Windows 2003 64-bit File System&#34;
                    &#34;Active Directory&#34;
                    &#34;Windows File Archiver&#34;
                    &#34;File Share Archiver&#34;
                    &#34;Image Level&#34;
                    &#34;Exchange Mailbox (Classic)&#34;
                    &#34;Exchange Mailbox Archiver&#34;
                    &#34;Exchange Compliance Archiver&#34;
                    &#34;Exchange Public Folder&#34;
                    &#34;Exchange Database&#34;
                    &#34;SharePoint Database&#34;
                    &#34;SharePoint Server Database&#34;
                    &#34;SharePoint Document&#34;
                    &#34;SharePoint Server&#34;
                    &#34;Novell Directory Services&#34;
                    &#34;GroupWise DB&#34;
                    &#34;NDMP&#34;
                    &#34;Notes Document&#34;
                    &#34;Unix Notes Database&#34;
                    &#34;MAC FileSystem&#34;
                    &#34;Big Data Apps&#34;
                    &#34;Solaris File System&#34;
                    &#34;Solaris 64bit File System&#34;
                    &#34;FreeBSD&#34;
                    &#34;HP-UX File System&#34;
                    &#34;HP-UX 64bit File System&#34;
                    &#34;AIX File System&#34;
                    &#34;Unix Tru64 64-bit File System&#34;
                    &#34;Linux File System&#34;
                    &#34;Sybase Database&#34;
                    &#34;Oracle Database&#34;
                    &#34;Oracle RAC&#34;
                    &#34;Informix Database&#34;
                    &#34;DB2&#34;
                    &#34;DB2 on Unix&#34;
                    &#34;SAP for Oracle&#34;
                    &#34;SAP for MAX DB&#34;
                    &#34;ProxyHost on Unix&#34;
                    &#34;ProxyHost&#34;
                    &#34;Image Level On Unix&#34;
                    &#34;OSSV Plug-in on Windows&#34;
                    &#34;OSSV Plug-in on Unix&#34;
                    &#34;Unix File Archiver&#34;
                    &#34;SQL Server&#34;
                    &#34;Data Classification&#34;
                    &#34;OES File System on Linux&#34;
                    &#34;Centera&#34;
                    &#34;Exchange PF Archiver&#34;
                    &#34;Domino Mailbox Archiver&#34;
                    &#34;MS SharePoint Archiver&#34;
                    &#34;Content Indexing Agent&#34;
                    &#34;SRM Agent For Windows File Systems&#34;
                    &#34;SRM Agent For UNIX File Systems&#34;
                    &#34;DB2 MultiNode&#34;
                    &#34;MySQL&#34;
                    &#34;Virtual Server&#34;
                    &#34;SharePoint Search Connector&#34;
                    &#34;Object Link&#34;
                    &#34;PostgreSQL&#34;
                    &#34;Sybase IQ&#34;
                    &#34;External Data Connector&#34;
                    &#34;Documentum&#34;
                    &#34;Object Store&#34;
                    &#34;SAP HANA&#34;
                    &#34;Cloud Apps&#34;
                    &#34;Exchange Mailbox&#34;

        Returns:
            dict        --          settings of a specific jobtype
                                    ex:
                                    {
                                        &#34;jobTypeName&#34;: &#34;Information Management&#34;,
                                        &#34;combinedPriority&#34;: 0,
                                        &#34;type_of_operation&#34;: 1
                                    }

                                    or

                                    settings of a specific apptype
                                    ex:
                                    {
                                        &#34;appTypeName&#34;: &#34;Windows File System&#34;,
                                        &#34;combinedPriority&#34;: 6,
                                        &#34;type_of_operation&#34;: 2
                                    }
        Raises:
            SDKException:
                if input is not valid type

    &#34;&#34;&#34;
    if isinstance(jobtype, str):
        for job_type in self._priority_settings[&#39;jobPrioritySettings&#39;][&#39;jobTypePriorityList&#39;]:
            if job_type[&#39;jobTypeName&#39;] == jobtype:
                settings = {
                    &#39;jobTypeName&#39;: job_type.get(&#39;jobTypeName&#39;),
                    &#39;combinedPriority&#39;: job_type.get(&#39;combinedPriority&#39;),
                    &#39;type_of_operation&#39;: 1
                }
                return settings
        for job_type in self._priority_settings[&#39;jobPrioritySettings&#39;][&#39;agentTypePriorityList&#39;]:
            if job_type[&#39;agentTypeEntity&#39;][&#39;appTypeName&#39;] == jobtype:
                settings = {
                    &#39;appTypeName&#39;: job_type.get(&#39;agentTypeEntity&#39;).get(&#39;appTypeName&#39;),
                    &#39;combinedPriority&#39;: job_type.get(&#39;combinedPriority&#39;),
                    &#39;type_of_operation&#39;: 2
                }
                return settings
    else:
        raise SDKException(&#39;Job&#39;, &#39;108&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.JobManagement.get_restart_setting"><code class="name flex">
<span>def <span class="ident">get_restart_setting</span></span>(<span>self, jobtype)</span>
</code></dt>
<dd>
<div class="desc"><p>restart settings associated to particular jobtype can be obtained
Args:
jobtype
(str)
&ndash;
settings of the jobtype to get</p>
<pre><code>    Available jobtypes:

            "Disaster Recovery backup"
            "Auxiliary Copy"
            "Data Aging"
            "Download/Copy Updates"
            "Offline Content Indexing"
            "Information Management"
            "File System and Indexing Based (Data Protection)"
            "File System and Indexing Based (Data Recovery)"
            "Exchange DB (Data Protection)"
            "Exchange DB (Data Recovery)"
            "Informix DB (Data Protection)"
            "Informix DB (Data Recovery)"
            "Lotus Notes DB (Data Protection)"
            "Lotus Notes DB (Data Recovery)"
            "Oracle DB (Data Protection)"
            "Oracle DB (Data Recovery)"
            "SQL DB (Data Protection)"
            "SQL DB (Data Recovery)"
            "MYSQL (Data Protection)"
</code></pre>
<p>`
"MYSQL (Data Recovery)"
"Sybase DB (Data Protection)"
"Sybase DB (Data Recovery)"
"DB2 (Data Protection)"
"DB2 (Data Recovery)"
"CDR (Data Management)"
"Media Refresh"
"Documentum (Data Protection)"
"Documentum (Data Recovery)"
"SAP for Oracle (Data Protection)"
"SAP for Oracle (Data Recovery)"
"PostgreSQL (Data Protection)"
"PostgreSQL (Data Recovery)"
"Other (Data Protection)"
"Other (Data Recovery)"
"Workflow"
"DeDup DB Reconstruction"
"CommCell Migration Export"
"CommCell Migration Import"
"Install Software"
"Uninstall Software"
"Data Verification"
"Big Data Apps (Data Protection)"
"Big Data Apps (Data Recovery)"
"Cloud Apps (Data Protection)"
"Cloud Apps (Data Recovery)"
"Virtual Server (Data Protection)"
"Virtual Server (Data Recovery)"
"SAP for Hana (Data Protection)"
"SAP for Hana (Data Recovery)"</p>
<pre><code>Returns:
    dict          --        settings of the specific job type as follows
                            {
                                "jobTypeName": "File System and Indexing Based (Data Protection)",
                                "restartable": true,
                                "maxRestarts": 10,
                                "restartIntervalInMinutes": 20,
                                "enableTotalRunningTime": false,
                                "totalRunningTime": 25200,
                                "killRunningJobWhenTotalRunningTimeExpires": false,
                                "preemptable": true,

                            }

Raises:
    SDKException:
        if input is not valid type
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L1669-L1754" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_restart_setting(self, jobtype):
    &#34;&#34;&#34;
    restart settings associated to particular jobtype can be obtained
        Args:
            jobtype     (str)       --      settings of the jobtype to get

            Available jobtypes:

                    &#34;Disaster Recovery backup&#34;
                    &#34;Auxiliary Copy&#34;
                    &#34;Data Aging&#34;
                    &#34;Download/Copy Updates&#34;
                    &#34;Offline Content Indexing&#34;
                    &#34;Information Management&#34;
                    &#34;File System and Indexing Based (Data Protection)&#34;
                    &#34;File System and Indexing Based (Data Recovery)&#34;
                    &#34;Exchange DB (Data Protection)&#34;
                    &#34;Exchange DB (Data Recovery)&#34;
                    &#34;Informix DB (Data Protection)&#34;
                    &#34;Informix DB (Data Recovery)&#34;
                    &#34;Lotus Notes DB (Data Protection)&#34;
                    &#34;Lotus Notes DB (Data Recovery)&#34;
                    &#34;Oracle DB (Data Protection)&#34;
                    &#34;Oracle DB (Data Recovery)&#34;
                    &#34;SQL DB (Data Protection)&#34;
                    &#34;SQL DB (Data Recovery)&#34;
                    &#34;MYSQL (Data Protection)&#34;
    `               &#34;MYSQL (Data Recovery)&#34;
                    &#34;Sybase DB (Data Protection)&#34;
                    &#34;Sybase DB (Data Recovery)&#34;
                    &#34;DB2 (Data Protection)&#34;
                    &#34;DB2 (Data Recovery)&#34;
                    &#34;CDR (Data Management)&#34;
                    &#34;Media Refresh&#34;
                    &#34;Documentum (Data Protection)&#34;
                    &#34;Documentum (Data Recovery)&#34;
                    &#34;SAP for Oracle (Data Protection)&#34;
                    &#34;SAP for Oracle (Data Recovery)&#34;
                    &#34;PostgreSQL (Data Protection)&#34;
                    &#34;PostgreSQL (Data Recovery)&#34;
                    &#34;Other (Data Protection)&#34;
                    &#34;Other (Data Recovery)&#34;
                    &#34;Workflow&#34;
                    &#34;DeDup DB Reconstruction&#34;
                    &#34;CommCell Migration Export&#34;
                    &#34;CommCell Migration Import&#34;
                    &#34;Install Software&#34;
                    &#34;Uninstall Software&#34;
                    &#34;Data Verification&#34;
                    &#34;Big Data Apps (Data Protection)&#34;
                    &#34;Big Data Apps (Data Recovery)&#34;
                    &#34;Cloud Apps (Data Protection)&#34;
                    &#34;Cloud Apps (Data Recovery)&#34;
                    &#34;Virtual Server (Data Protection)&#34;
                    &#34;Virtual Server (Data Recovery)&#34;
                    &#34;SAP for Hana (Data Protection)&#34;
                    &#34;SAP for Hana (Data Recovery)&#34;



        Returns:
            dict          --        settings of the specific job type as follows
                                    {
                                        &#34;jobTypeName&#34;: &#34;File System and Indexing Based (Data Protection)&#34;,
                                        &#34;restartable&#34;: true,
                                        &#34;maxRestarts&#34;: 10,
                                        &#34;restartIntervalInMinutes&#34;: 20,
                                        &#34;enableTotalRunningTime&#34;: false,
                                        &#34;totalRunningTime&#34;: 25200,
                                        &#34;killRunningJobWhenTotalRunningTimeExpires&#34;: false,
                                        &#34;preemptable&#34;: true,

                                    }

        Raises:
            SDKException:
                if input is not valid type

    &#34;&#34;&#34;
    if isinstance(jobtype, str):
        for job_type in self._restart_settings[&#39;jobRestartSettings&#39;][&#39;jobTypeRestartSettingList&#39;]:
            if job_type[&#39;jobTypeName&#39;] == jobtype:
                settings = copy.deepcopy(job_type)
                return settings
    else:
        raise SDKException(&#39;Job&#39;, &#39;108&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.JobManagement.get_update_setting"><code class="name flex">
<span>def <span class="ident">get_update_setting</span></span>(<span>self, jobtype)</span>
</code></dt>
<dd>
<div class="desc"><p>update settings associated to particular jobtype can be obtained
Args:
jobtype
(str)
&ndash;
settings of jobtype to get</p>
<pre><code>    Available jobtype

        Check get_priority_setting(self, jobtype) method documentation.

Returns:
    dict        -           settings of a jobtype
                            {
                                "appTypeName": "Windows File System",
                                "recoveryTimeInMinutes": 20,
                                "protectionTimeInMinutes": 20
                            }
Raises:
    SDKException:
        if input is not valid type
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L1886-L1918" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_update_setting(self, jobtype):
    &#34;&#34;&#34;
    update settings associated to particular jobtype can be obtained
        Args:
            jobtype     (str)       --      settings of jobtype to get

            Available jobtype

                Check get_priority_setting(self, jobtype) method documentation.

        Returns:
            dict        -           settings of a jobtype
                                    {
                                        &#34;appTypeName&#34;: &#34;Windows File System&#34;,
                                        &#34;recoveryTimeInMinutes&#34;: 20,
                                        &#34;protectionTimeInMinutes&#34;: 20
                                    }
        Raises:
            SDKException:
                if input is not valid type

    &#34;&#34;&#34;
    if isinstance(jobtype, str):
        for job_type in self._update_settings[&#39;jobUpdatesSettings&#39;][&#39;agentTypeJobUpdateIntervalList&#39;]:
            if job_type[&#39;agentTypeEntity&#39;][&#39;appTypeName&#39;] == jobtype:
                settings = {
                    &#39;appTypeName&#39;: job_type.get(&#39;agentTypeEntity&#39;).get(&#39;appTypeName&#39;),
                    &#39;recoveryTimeInMinutes&#39;: job_type.get(&#39;recoveryTimeInMinutes&#39;),
                    &#39;protectionTimeInMinutes&#39;: job_type.get(&#39;protectionTimeInMinutes&#39;)
                }
                return settings
    else:
        raise SDKException(&#39;Job&#39;, &#39;108&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.JobManagement.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>calls the private method _get_jobmanagement_settings()</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L1027-L1035" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;
    calls the private method _get_jobmanagement_settings()
    &#34;&#34;&#34;
    self._restart_settings = None
    self._general_settings = None
    self._update_settings = None
    self._priority_settings = None
    self._get_jobmanagement_settings()</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.JobManagement.set_general_settings"><code class="name flex">
<span>def <span class="ident">set_general_settings</span></span>(<span>self, settings)</span>
</code></dt>
<dd>
<div class="desc"><p>sets general settings of job management.</p>
<p>Note : dedicated setters and getters are provided for general settings.
Args:
settings (dict)
&ndash;
Following key/value pairs can be set.
{
"allowRunningJobsToCompletePastOperationWindow": False,
"jobAliveCheckIntervalInMinutes": 5,
"queueScheduledJobs": False,
"enableJobThrottleAtClientLevel": False,
"enableMultiplexingForDBAgents": False,
"queueJobsIfConflictingJobsActive": False,
"queueJobsIfActivityDisabled": False,
"backupsPreemptsAuxilaryCopy": False,
"restorePreemptsOtherJobs": False,
"enableMultiplexingForOracle": False,
"jobStreamHighWaterMarkLevel": 500,
"backupsPreemptsOtherBackups": False,
"doNotStartBackupsOnDisabledClient": False</p>
<pre><code>                                }
Returns:
    None

Raises:
    SDKException:
        if input is not valid type
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L1037-L1071" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_general_settings(self, settings):
    &#34;&#34;&#34;
    sets general settings of job management.

    Note : dedicated setters and getters are provided for general settings.
        Args:
            settings (dict)  --       Following key/value pairs can be set.
                                        {
                                            &#34;allowRunningJobsToCompletePastOperationWindow&#34;: False,
                                            &#34;jobAliveCheckIntervalInMinutes&#34;: 5,
                                            &#34;queueScheduledJobs&#34;: False,
                                            &#34;enableJobThrottleAtClientLevel&#34;: False,
                                            &#34;enableMultiplexingForDBAgents&#34;: False,
                                            &#34;queueJobsIfConflictingJobsActive&#34;: False,
                                            &#34;queueJobsIfActivityDisabled&#34;: False,
                                            &#34;backupsPreemptsAuxilaryCopy&#34;: False,
                                            &#34;restorePreemptsOtherJobs&#34;: False,
                                            &#34;enableMultiplexingForOracle&#34;: False,
                                            &#34;jobStreamHighWaterMarkLevel&#34;: 500,
                                            &#34;backupsPreemptsOtherBackups&#34;: False,
                                            &#34;doNotStartBackupsOnDisabledClient&#34;: False

                                        }
        Returns:
            None

        Raises:
            SDKException:
                if input is not valid type
    &#34;&#34;&#34;
    if isinstance(settings, dict):
        self._general_settings.get(&#39;generalSettings&#39;).update(settings)
        self._set_jobmanagement_settings()
    else:
        raise SDKException(&#39;Job&#39;, &#39;108&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.JobManagement.set_job_error_threshold"><code class="name flex">
<span>def <span class="ident">set_job_error_threshold</span></span>(<span>self, error_threshold_dict)</span>
</code></dt>
<dd>
<div class="desc"><h2 id="args">Args</h2>
<p>error_threshold_dict
(dict)
:
A dictionary of following
key/value pairs can be set.</p>
<h2 id="returns">Returns</h2>
<p>None</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L1955-L1965" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_job_error_threshold(self, error_threshold_dict):
    &#34;&#34;&#34;

    Args:
        error_threshold_dict  (dict)  :   A dictionary of following  key/value pairs can be set.

    Returns:
        None

    &#34;&#34;&#34;
    raise NotImplementedError(&#34;Yet To Be Implemented&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.JobManagement.set_priority_settings"><code class="name flex">
<span>def <span class="ident">set_priority_settings</span></span>(<span>self, settings)</span>
</code></dt>
<dd>
<div class="desc"><p>sets priority settings for jobs and agents type.</p>
<pre><code>Args:
    settings  (list)    --  list of dictionaries with following format.
                             [
                                {
                                    "type_of_operation": 1,
                                    "combinedPriority": 10,
                                    "jobTypeName": "Information Management"
                                },
                                {
                                    "type_of_operation": 2,
                                    "combinedPriority": 10,
                                    "appTypeName": "Windows File System"
                                },
                                {
                                "type_of_operation": 1,
                                "combinedPriority": 10,
                                "jobTypeName": "Auxiliary Copy"
                                 }
                            ]

We have priority settings fro jobtype and agenttype

NOTE : for setting, priority for jobtype the 'type_of_operation' must be set to 1 and name of the job type
       must be specified as below format.

           ex :-  "jobTypeName": "Information Management"

NOTE : for setting, priority for agenttype the 'type_of_operation' must be set to 2 and name of the job
 type must be specified as below format

            ex :- "appTypeName": "Windows File System"

Returns:
    None

Raises:
    SDKException:
        if input is not valid type
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L1073-L1135" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_priority_settings(self, settings):
    &#34;&#34;&#34;
    sets priority settings for jobs and agents type.

        Args:
            settings  (list)    --  list of dictionaries with following format.
                                     [
                                        {
                                            &#34;type_of_operation&#34;: 1,
                                            &#34;combinedPriority&#34;: 10,
                                            &#34;jobTypeName&#34;: &#34;Information Management&#34;
                                        },
                                        {
                                            &#34;type_of_operation&#34;: 2,
                                            &#34;combinedPriority&#34;: 10,
                                            &#34;appTypeName&#34;: &#34;Windows File System&#34;
                                        },
                                        {
                                        &#34;type_of_operation&#34;: 1,
                                        &#34;combinedPriority&#34;: 10,
                                        &#34;jobTypeName&#34;: &#34;Auxiliary Copy&#34;
                                         }
                                    ]

        We have priority settings fro jobtype and agenttype

        NOTE : for setting, priority for jobtype the &#39;type_of_operation&#39; must be set to 1 and name of the job type
               must be specified as below format.

                   ex :-  &#34;jobTypeName&#34;: &#34;Information Management&#34;

        NOTE : for setting, priority for agenttype the &#39;type_of_operation&#39; must be set to 2 and name of the job
         type must be specified as below format

                    ex :- &#34;appTypeName&#34;: &#34;Windows File System&#34;

        Returns:
            None

        Raises:
            SDKException:
                if input is not valid type

    &#34;&#34;&#34;
    if isinstance(settings, list):
        for job in settings:
            if job[&#34;type_of_operation&#34;] == 1:
                for job_type in self._priority_settings[&#39;jobPrioritySettings&#39;][&#39;jobTypePriorityList&#39;]:
                    if job_type[&#39;jobTypeName&#39;] == job.get(&#34;jobTypeName&#34;):
                        job.pop(&#34;jobTypeName&#34;)
                        job.pop(&#34;type_of_operation&#34;)
                        job_type.update(job)
                        break
            elif job[&#34;type_of_operation&#34;] == 2:
                for job_type in self._priority_settings[&#39;jobPrioritySettings&#39;][&#39;agentTypePriorityList&#39;]:
                    if job_type[&#39;agentTypeEntity&#39;][&#39;appTypeName&#39;] == job.get(&#34;appTypeName&#34;):
                        job.pop(&#34;appTypeName&#34;)
                        job.pop(&#34;type_of_operation&#34;)
                        job_type.update(job)
                        break
        self._set_jobmanagement_settings()
    else:
        raise SDKException(&#39;Job&#39;, &#39;108&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.JobManagement.set_restart_settings"><code class="name flex">
<span>def <span class="ident">set_restart_settings</span></span>(<span>self, settings)</span>
</code></dt>
<dd>
<div class="desc"><p>sets restart settings for jobs.</p>
<pre><code>Args:
    settings    (list)      --  list of dictionaries with following format
                                [
                                    {
                                        "killRunningJobWhenTotalRunningTimeExpires": False,
                                        "maxRestarts": 10,
                                        "enableTotalRunningTime": False,
                                        "restartable": False,
                                        "jobTypeName": "File System and Indexing Based (Data Protection)",
                                        "restartIntervalInMinutes": 20,
                                        "preemptable": True,
                                        "totalRunningTime": 21600,
                                        "jobType": 6
                                    },
                                    {
                                        "killRunningJobWhenTotalRunningTimeExpires": False,
                                        "maxRestarts": 144,
                                        "enableTotalRunningTime": False,
                                        "restartable": False,
                                        "jobTypeName": "File System and Indexing Based (Data Recovery)",
                                        "restartIntervalInMinutes": 20,
                                        "preemptable": False,
                                        "totalRunningTime": 21600,
                                        "jobType": 7
                                    }
                                ]

Returns:
    None

Raises:
    SDKException:
        if input is not valid type
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L1137-L1185" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_restart_settings(self, settings):
    &#34;&#34;&#34;
    sets restart settings for jobs.

        Args:
            settings    (list)      --  list of dictionaries with following format
                                        [
                                            {
                                                &#34;killRunningJobWhenTotalRunningTimeExpires&#34;: False,
                                                &#34;maxRestarts&#34;: 10,
                                                &#34;enableTotalRunningTime&#34;: False,
                                                &#34;restartable&#34;: False,
                                                &#34;jobTypeName&#34;: &#34;File System and Indexing Based (Data Protection)&#34;,
                                                &#34;restartIntervalInMinutes&#34;: 20,
                                                &#34;preemptable&#34;: True,
                                                &#34;totalRunningTime&#34;: 21600,
                                                &#34;jobType&#34;: 6
                                            },
                                            {
                                                &#34;killRunningJobWhenTotalRunningTimeExpires&#34;: False,
                                                &#34;maxRestarts&#34;: 144,
                                                &#34;enableTotalRunningTime&#34;: False,
                                                &#34;restartable&#34;: False,
                                                &#34;jobTypeName&#34;: &#34;File System and Indexing Based (Data Recovery)&#34;,
                                                &#34;restartIntervalInMinutes&#34;: 20,
                                                &#34;preemptable&#34;: False,
                                                &#34;totalRunningTime&#34;: 21600,
                                                &#34;jobType&#34;: 7
                                            }
                                        ]

        Returns:
            None

        Raises:
            SDKException:
                if input is not valid type

    &#34;&#34;&#34;

    if isinstance(settings, list):
        for job in settings:
            target = {&#39;target&#39;: job_type for job_type in
                      self._restart_settings[&#39;jobRestartSettings&#39;][&#39;jobTypeRestartSettingList&#39;]
                      if job_type[&#39;jobTypeName&#39;] == job.get(&#34;jobTypeName&#34;)}
            target.get(&#39;target&#39;).update(job)
        self._set_jobmanagement_settings()
    else:
        raise SDKException(&#39;Job&#39;, &#39;108&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.job.JobManagement.set_update_settings"><code class="name flex">
<span>def <span class="ident">set_update_settings</span></span>(<span>self, settings)</span>
</code></dt>
<dd>
<div class="desc"><p>sets update settings for jobs</p>
<pre><code>Args:
    settings    (list)      --      list of dictionaries with following format
                                    [
                                        {
                                            "appTypeName": "Windows File System",
                                            "recoveryTimeInMinutes": 20,
                                            "protectionTimeInMinutes": 20
                                        },
                                        {
                                            "appTypeName": "Windows XP 64-bit File System",
                                            "recoveryTimeInMinutes": 20,
                                            "protectionTimeInMinutes": 20,
                                        }
                                    ]
Returns:
    None

Raises:
    SDKException:
        if input is not valid type
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/job.py#L1187-L1223" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_update_settings(self, settings):
    &#34;&#34;&#34;
    sets update settings for jobs

        Args:
            settings    (list)      --      list of dictionaries with following format
                                            [
                                                {
                                                    &#34;appTypeName&#34;: &#34;Windows File System&#34;,
                                                    &#34;recoveryTimeInMinutes&#34;: 20,
                                                    &#34;protectionTimeInMinutes&#34;: 20
                                                },
                                                {
                                                    &#34;appTypeName&#34;: &#34;Windows XP 64-bit File System&#34;,
                                                    &#34;recoveryTimeInMinutes&#34;: 20,
                                                    &#34;protectionTimeInMinutes&#34;: 20,
                                                }
                                            ]
        Returns:
            None

        Raises:
            SDKException:
                if input is not valid type

    &#34;&#34;&#34;

    if isinstance(settings, list):
        for job in settings:
            for job_type in self._update_settings[&#39;jobUpdatesSettings&#39;][&#39;agentTypeJobUpdateIntervalList&#39;]:
                if job_type[&#39;agentTypeEntity&#39;][&#39;appTypeName&#39;] == job.get(&#34;appTypeName&#34;):
                    job.pop(&#34;appTypeName&#34;)
                    job_type.update(job)
                    break
        self._set_jobmanagement_settings()
    else:
        raise SDKException(&#39;Job&#39;, &#39;108&#39;)</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#jobcontroller">JobController</a></li>
<li><a href="#jobmanagement">JobManagement</a></li>
<li><a href="#job">Job</a><ul>
<li><a href="#job-instance-attributes">Job instance Attributes</a></li>
</ul>
</li>
<li><a href="#errorrule">ErrorRule</a></li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.job.Job" href="#cvpysdk.job.Job">Job</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.job.Job.advanced_job_details" href="#cvpysdk.job.Job.advanced_job_details">advanced_job_details</a></code></li>
<li><code><a title="cvpysdk.job.Job.agent_name" href="#cvpysdk.job.Job.agent_name">agent_name</a></code></li>
<li><code><a title="cvpysdk.job.Job.attempts" href="#cvpysdk.job.Job.attempts">attempts</a></code></li>
<li><code><a title="cvpysdk.job.Job.backup_level" href="#cvpysdk.job.Job.backup_level">backup_level</a></code></li>
<li><code><a title="cvpysdk.job.Job.backupset_name" href="#cvpysdk.job.Job.backupset_name">backupset_name</a></code></li>
<li><code><a title="cvpysdk.job.Job.client_name" href="#cvpysdk.job.Job.client_name">client_name</a></code></li>
<li><code><a title="cvpysdk.job.Job.delay_reason" href="#cvpysdk.job.Job.delay_reason">delay_reason</a></code></li>
<li><code><a title="cvpysdk.job.Job.details" href="#cvpysdk.job.Job.details">details</a></code></li>
<li><code><a title="cvpysdk.job.Job.end_time" href="#cvpysdk.job.Job.end_time">end_time</a></code></li>
<li><code><a title="cvpysdk.job.Job.end_timestamp" href="#cvpysdk.job.Job.end_timestamp">end_timestamp</a></code></li>
<li><code><a title="cvpysdk.job.Job.get_child_jobs" href="#cvpysdk.job.Job.get_child_jobs">get_child_jobs</a></code></li>
<li><code><a title="cvpysdk.job.Job.get_events" href="#cvpysdk.job.Job.get_events">get_events</a></code></li>
<li><code><a title="cvpysdk.job.Job.get_vm_list" href="#cvpysdk.job.Job.get_vm_list">get_vm_list</a></code></li>
<li><code><a title="cvpysdk.job.Job.instance_name" href="#cvpysdk.job.Job.instance_name">instance_name</a></code></li>
<li><code><a title="cvpysdk.job.Job.is_finished" href="#cvpysdk.job.Job.is_finished">is_finished</a></code></li>
<li><code><a title="cvpysdk.job.Job.job_id" href="#cvpysdk.job.Job.job_id">job_id</a></code></li>
<li><code><a title="cvpysdk.job.Job.job_type" href="#cvpysdk.job.Job.job_type">job_type</a></code></li>
<li><code><a title="cvpysdk.job.Job.kill" href="#cvpysdk.job.Job.kill">kill</a></code></li>
<li><code><a title="cvpysdk.job.Job.media_size" href="#cvpysdk.job.Job.media_size">media_size</a></code></li>
<li><code><a title="cvpysdk.job.Job.num_of_files_transferred" href="#cvpysdk.job.Job.num_of_files_transferred">num_of_files_transferred</a></code></li>
<li><code><a title="cvpysdk.job.Job.pause" href="#cvpysdk.job.Job.pause">pause</a></code></li>
<li><code><a title="cvpysdk.job.Job.pending_reason" href="#cvpysdk.job.Job.pending_reason">pending_reason</a></code></li>
<li><code><a title="cvpysdk.job.Job.phase" href="#cvpysdk.job.Job.phase">phase</a></code></li>
<li><code><a title="cvpysdk.job.Job.refresh" href="#cvpysdk.job.Job.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.job.Job.resubmit" href="#cvpysdk.job.Job.resubmit">resubmit</a></code></li>
<li><code><a title="cvpysdk.job.Job.resume" href="#cvpysdk.job.Job.resume">resume</a></code></li>
<li><code><a title="cvpysdk.job.Job.size_of_application" href="#cvpysdk.job.Job.size_of_application">size_of_application</a></code></li>
<li><code><a title="cvpysdk.job.Job.start_time" href="#cvpysdk.job.Job.start_time">start_time</a></code></li>
<li><code><a title="cvpysdk.job.Job.start_timestamp" href="#cvpysdk.job.Job.start_timestamp">start_timestamp</a></code></li>
<li><code><a title="cvpysdk.job.Job.state" href="#cvpysdk.job.Job.state">state</a></code></li>
<li><code><a title="cvpysdk.job.Job.status" href="#cvpysdk.job.Job.status">status</a></code></li>
<li><code><a title="cvpysdk.job.Job.subclient_name" href="#cvpysdk.job.Job.subclient_name">subclient_name</a></code></li>
<li><code><a title="cvpysdk.job.Job.summary" href="#cvpysdk.job.Job.summary">summary</a></code></li>
<li><code><a title="cvpysdk.job.Job.task_details" href="#cvpysdk.job.Job.task_details">task_details</a></code></li>
<li><code><a title="cvpysdk.job.Job.userid" href="#cvpysdk.job.Job.userid">userid</a></code></li>
<li><code><a title="cvpysdk.job.Job.username" href="#cvpysdk.job.Job.username">username</a></code></li>
<li><code><a title="cvpysdk.job.Job.wait_for_completion" href="#cvpysdk.job.Job.wait_for_completion">wait_for_completion</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.job.JobController" href="#cvpysdk.job.JobController">JobController</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.job.JobController.active_jobs" href="#cvpysdk.job.JobController.active_jobs">active_jobs</a></code></li>
<li><code><a title="cvpysdk.job.JobController.all_jobs" href="#cvpysdk.job.JobController.all_jobs">all_jobs</a></code></li>
<li><code><a title="cvpysdk.job.JobController.finished_jobs" href="#cvpysdk.job.JobController.finished_jobs">finished_jobs</a></code></li>
<li><code><a title="cvpysdk.job.JobController.get" href="#cvpysdk.job.JobController.get">get</a></code></li>
<li><code><a title="cvpysdk.job.JobController.kill_all_jobs" href="#cvpysdk.job.JobController.kill_all_jobs">kill_all_jobs</a></code></li>
<li><code><a title="cvpysdk.job.JobController.resume_all_jobs" href="#cvpysdk.job.JobController.resume_all_jobs">resume_all_jobs</a></code></li>
<li><code><a title="cvpysdk.job.JobController.suspend_all_jobs" href="#cvpysdk.job.JobController.suspend_all_jobs">suspend_all_jobs</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.job.JobManagement" href="#cvpysdk.job.JobManagement">JobManagement</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.job.JobManagement.allow_running_jobs_to_complete_past_operation_window" href="#cvpysdk.job.JobManagement.allow_running_jobs_to_complete_past_operation_window">allow_running_jobs_to_complete_past_operation_window</a></code></li>
<li><code><a title="cvpysdk.job.JobManagement.backups_preempts_auxilary_copy" href="#cvpysdk.job.JobManagement.backups_preempts_auxilary_copy">backups_preempts_auxilary_copy</a></code></li>
<li><code><a title="cvpysdk.job.JobManagement.backups_preempts_other_backups" href="#cvpysdk.job.JobManagement.backups_preempts_other_backups">backups_preempts_other_backups</a></code></li>
<li><code><a title="cvpysdk.job.JobManagement.do_not_start_backups_on_disabled_client" href="#cvpysdk.job.JobManagement.do_not_start_backups_on_disabled_client">do_not_start_backups_on_disabled_client</a></code></li>
<li><code><a title="cvpysdk.job.JobManagement.enable_job_throttle_at_client_level" href="#cvpysdk.job.JobManagement.enable_job_throttle_at_client_level">enable_job_throttle_at_client_level</a></code></li>
<li><code><a title="cvpysdk.job.JobManagement.enable_multiplexing_for_db_agents" href="#cvpysdk.job.JobManagement.enable_multiplexing_for_db_agents">enable_multiplexing_for_db_agents</a></code></li>
<li><code><a title="cvpysdk.job.JobManagement.enable_multiplexing_for_oracle" href="#cvpysdk.job.JobManagement.enable_multiplexing_for_oracle">enable_multiplexing_for_oracle</a></code></li>
<li><code><a title="cvpysdk.job.JobManagement.error_rules" href="#cvpysdk.job.JobManagement.error_rules">error_rules</a></code></li>
<li><code><a title="cvpysdk.job.JobManagement.general_settings" href="#cvpysdk.job.JobManagement.general_settings">general_settings</a></code></li>
<li><code><a title="cvpysdk.job.JobManagement.get_priority_setting" href="#cvpysdk.job.JobManagement.get_priority_setting">get_priority_setting</a></code></li>
<li><code><a title="cvpysdk.job.JobManagement.get_restart_setting" href="#cvpysdk.job.JobManagement.get_restart_setting">get_restart_setting</a></code></li>
<li><code><a title="cvpysdk.job.JobManagement.get_update_setting" href="#cvpysdk.job.JobManagement.get_update_setting">get_update_setting</a></code></li>
<li><code><a title="cvpysdk.job.JobManagement.job_alive_check_interval_in_minutes" href="#cvpysdk.job.JobManagement.job_alive_check_interval_in_minutes">job_alive_check_interval_in_minutes</a></code></li>
<li><code><a title="cvpysdk.job.JobManagement.job_priority_precedence" href="#cvpysdk.job.JobManagement.job_priority_precedence">job_priority_precedence</a></code></li>
<li><code><a title="cvpysdk.job.JobManagement.job_stream_high_water_mark_level" href="#cvpysdk.job.JobManagement.job_stream_high_water_mark_level">job_stream_high_water_mark_level</a></code></li>
<li><code><a title="cvpysdk.job.JobManagement.priority_settings" href="#cvpysdk.job.JobManagement.priority_settings">priority_settings</a></code></li>
<li><code><a title="cvpysdk.job.JobManagement.queue_jobs_if_activity_disabled" href="#cvpysdk.job.JobManagement.queue_jobs_if_activity_disabled">queue_jobs_if_activity_disabled</a></code></li>
<li><code><a title="cvpysdk.job.JobManagement.queue_jobs_if_conflicting_jobs_active" href="#cvpysdk.job.JobManagement.queue_jobs_if_conflicting_jobs_active">queue_jobs_if_conflicting_jobs_active</a></code></li>
<li><code><a title="cvpysdk.job.JobManagement.queue_scheduled_jobs" href="#cvpysdk.job.JobManagement.queue_scheduled_jobs">queue_scheduled_jobs</a></code></li>
<li><code><a title="cvpysdk.job.JobManagement.refresh" href="#cvpysdk.job.JobManagement.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.job.JobManagement.restart_settings" href="#cvpysdk.job.JobManagement.restart_settings">restart_settings</a></code></li>
<li><code><a title="cvpysdk.job.JobManagement.restore_preempts_other_jobs" href="#cvpysdk.job.JobManagement.restore_preempts_other_jobs">restore_preempts_other_jobs</a></code></li>
<li><code><a title="cvpysdk.job.JobManagement.set_general_settings" href="#cvpysdk.job.JobManagement.set_general_settings">set_general_settings</a></code></li>
<li><code><a title="cvpysdk.job.JobManagement.set_job_error_threshold" href="#cvpysdk.job.JobManagement.set_job_error_threshold">set_job_error_threshold</a></code></li>
<li><code><a title="cvpysdk.job.JobManagement.set_priority_settings" href="#cvpysdk.job.JobManagement.set_priority_settings">set_priority_settings</a></code></li>
<li><code><a title="cvpysdk.job.JobManagement.set_restart_settings" href="#cvpysdk.job.JobManagement.set_restart_settings">set_restart_settings</a></code></li>
<li><code><a title="cvpysdk.job.JobManagement.set_update_settings" href="#cvpysdk.job.JobManagement.set_update_settings">set_update_settings</a></code></li>
<li><code><a title="cvpysdk.job.JobManagement.start_phase_retry_interval" href="#cvpysdk.job.JobManagement.start_phase_retry_interval">start_phase_retry_interval</a></code></li>
<li><code><a title="cvpysdk.job.JobManagement.state_update_interval_for_continuous_data_replicator" href="#cvpysdk.job.JobManagement.state_update_interval_for_continuous_data_replicator">state_update_interval_for_continuous_data_replicator</a></code></li>
<li><code><a title="cvpysdk.job.JobManagement.update_settings" href="#cvpysdk.job.JobManagement.update_settings">update_settings</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>