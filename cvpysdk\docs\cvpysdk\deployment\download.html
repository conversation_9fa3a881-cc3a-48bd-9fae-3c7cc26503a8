<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.deployment.download API documentation</title>
<meta name="description" content="&#34; Main file for performing the download operation …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.deployment.download</code></h1>
</header>
<section id="section-intro">
<p>" Main file for performing the download operation</p>
<h1 id="download">Download</h1>
<pre><code>__init__(commcell_object)             --  initialize commcell_object of Download class
associated with the commcell

download_software()                   --  downloads software packages in the commcell

sync_remote_cache()                   --  syncs remote cache
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/deployment/download.py#L1-L585" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;&#34; Main file for performing the download operation

Download
========

    __init__(commcell_object)             --  initialize commcell_object of Download class
    associated with the commcell

    download_software()                   --  downloads software packages in the commcell

    sync_remote_cache()                   --  syncs remote cache

&#34;&#34;&#34;

from ..job import Job
from ..exception import SDKException
from .deploymentconstants import DownloadOptions
from cvpysdk.schedules import Schedules, SchedulePattern


class Download(object):
    &#34;&#34;&#34;&#34;class for downloading software packages&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize commcell_object of the Download class.

            Args:
                commcell_object (object)  --  instance of the Commcell class

            Returns:
                object - instance of the Download class

        &#34;&#34;&#34;

        self.commcell_object = commcell_object
        self._services = commcell_object._services
        self._cvpysdkcommcell_object = commcell_object._cvpysdk_object
        self.update_option = {}

    def download_software(
            self,
            options=None,
            os_list=None,
            service_pack=None,
            cu_number=0,
            sync_cache=True,
            sync_cache_list=None,
            schedule_pattern=None):
        &#34;&#34;&#34;Downloads the os packages on the commcell

            Args:

                options      (enum)            --  Download option to download software

                os_list      (list of enum)    --  list of windows/unix packages to be downloaded

                service_pack (int)             --  service pack to be downloaded

                cu_number (int)                --  maintenance release number

                sync_cache (bool)              --  True if download and sync
                                                   False only download
                
                sync_cache_list (list)         --  list of names of remote caches to sync
                                                   use None to sync all caches

                schedule_pattern (dict)        --  pattern for schedule task
                                                   

            Returns:
                object - instance of the Job class for this download job

            Raises:
                SDKException:
                    if Download job failed

                    if response is empty

                    if response is not success

                    if another download job is running

            Usage:

            -   if download_software is not given any parameters it takes default value of latest
                service pack for options and downloads WINDOWS_64 package

                &gt;&gt;&gt; commcell_obj.download_software()

            -   DownloadOptions and DownloadPackages enum is used for providing input to the
                download software method, it can be imported by

                &gt;&gt;&gt; from cvpysdk.deployment.deploymentconstants import DownloadOptions
                    from cvpysdk.deployment.deploymentconstants import DownloadPackages

            -   sample method calls for different options, for latest service pack

                &gt;&gt;&gt; commcell_obj.download_software(
                        options=DownloadOptions.lATEST_SERVICEPACK.value,
                        os_list=[DownloadPackages.WINDOWS_64.value]
                        )

            -   For Latest hotfixes for the installed service pack

                &gt;&gt;&gt; commcell_obj.download_software(
                        options=&#39;DownloadOptions.LATEST_HOTFIXES.value&#39;,
                        os_list=[DownloadPackages.WINDOWS_64.value,
                                DownloadPackages.UNIX_LINUX64.value]
                        )

            -   For service pack and hotfixes

                &gt;&gt;&gt; commcell_obj.download_software(
                        options=&#39;DownloadOptions.SERVICEPACK_AND_HOTFIXES.value&#39;,
                        os_list=[DownloadPackages.UNIX_MAC.value],
                        service_pack=13,
                        cu_number=42
                        )

                    **NOTE:** service_pack parameter must be specified for third option

        &#34;&#34;&#34;

        # To set the default value if option is none
        version = self.commcell_object.commserv_version
        if options is None:
            options = &#39;latest service pack&#39;

        if DownloadOptions.LATEST_SERVICEPACK.value == options:
            self.update_option = {
                &#39;upgradeToLatestRelease&#39;: True,
                &#39;latestFixesForCurrentRelease&#39;: False,
                &#34;featureRelease&#34;: self.commcell_object.version,
                &#39;SPName&#39;: &#39;latest&#39;,
                &#39;IsSPName&#39;: False,
                &#39;isSpDelayedDays&#39;: True,
                &#39;isHotfixesDownload&#39;: False
            }
        elif DownloadOptions.LATEST_HOTFIXES.value == options:
            self.update_option = {
                &#39;upgradeToLatestRelease&#39;: False,
                &#39;latestFixesForCurrentRelease&#39;: True,
                &#39;SPName&#39;: &#39;hotfix&#39;,
                &#39;IsSPName&#39;: False,
                &#39;isSpDelayedDays&#39;: True,
                &#39;isHotfixesDownload&#39;: True
            }
        elif DownloadOptions.SERVICEPACK_AND_HOTFIXES.value == options:
            if service_pack is None:
                raise SDKException(&#39;Download&#39;, &#39;102&#39;)
            self.update_option = {
                &#39;upgradeToLatestRelease&#39;: False,
                &#39;latestFixesForCurrentRelease&#39;: False,
                &#34;featureRelease&#34;: &#34;11.&#34; + str(service_pack) + &#34;.&#34; + str(cu_number),
                &#39;SPName&#39;: str(service_pack),
                &#39;IsSPName&#39;: True,
                &#39;isSpDelayedDays&#39;: False,
                &#39;isHotfixesDownload&#39;: False
            }

        # to set the default value if os_list is none
        if os_list is None:
            os_list = [&#39;WINDOWS_X64&#39;]

        client_groups = []
        if sync_cache and sync_cache_list:
            for cache in sync_cache_list:
                client_groups.append({&#34;type&#34;: &#34;CLIENT_ENTITY&#34;,
                                      &#34;clientName&#34;: cache,
                                      &#34;id&#34;: self.commcell_object.clients.get(cache).client_id
                                      }) if version &gt;= 36 else client_groups.append({&#34;clientName&#34;: cache})
        elif sync_cache and not sync_cache_list:
            if version &lt; 36:
                client_groups = [{&#34;_type_&#34;: 2}]
            else:
                flag_1, response_1 = self._cvpysdkcommcell_object.make_request(&#39;GET&#39;, self._services[&#39;CREATE_RC&#39;])
                cache_list = []
                for obj in response_1.json()[&#39;softwareCacheDetailList&#39;]:
                    if obj[&#39;cache&#39;][&#39;id&#39;] != 2:
                        cache_list.append(obj[&#39;cache&#39;][&#39;name&#39;])
                for client in cache_list:
                    client_groups.append({&#34;type&#34;: &#34;CLIENT_ENTITY&#34;,
                                          &#34;clientName&#34;: client,
                                          &#34;id&#34;: int(self.commcell_object.clients.all_clients[client][&#39;id&#39;])})

        if version &gt;= 36:
            win_os_list = []
            unix_os_list = []
            temp_win = [&#39;WINDOWS_X64&#39;, &#39;WINDOWS_X32&#39;, &#39;WINDOWS_ARM64&#39;]

            for os in os_list:
                if os in temp_win:
                    win_os_list.append(os)
                else:
                    unix_os_list.append(os)
            request_json = {
                &#34;downloadConfiguration&#34;: {
                    &#34;upgradeToLatestRelease&#34;: self.update_option[&#39;upgradeToLatestRelease&#39;],
                    &#34;latestFixesForCurrentRelease&#34;: self.update_option[&#39;latestFixesForCurrentRelease&#39;],
                    &#34;windowsDownloadOptions&#34;: win_os_list,
                    &#34;unixDownloadOptions&#34;: unix_os_list,
                },
                &#34;notifyWhenJobCompletes&#34;: False,
                &#34;entities&#34;: client_groups

            }
            if DownloadOptions.SERVICEPACK_AND_HOTFIXES.value == options:
                request_json[&#34;downloadConfiguration&#34;][&#34;featureRelease&#34;] = self.update_option[&#39;featureRelease&#39;]
        else:
            request_json = {
                &#34;taskInfo&#34;: {
                    &#34;task&#34;: {
                        &#34;taskType&#34;: 1,
                        &#34;initiatedFrom&#34;: 2,
                        &#34;policyType&#34;: 0,
                        &#34;alert&#34;: {
                            &#34;alertName&#34;: &#34;&#34;
                        },
                        &#34;taskFlags&#34;: {
                            &#34;isEdgeDrive&#34;: False,
                            &#34;disabled&#34;: False
                        }
                    },
                    &#34;subTasks&#34;: [
                        {
                            &#34;subTaskOperation&#34;: 1,
                            &#34;subTask&#34;: {
                                &#34;subTaskType&#34;: 1,
                                &#34;operationType&#34;: 4019
                            },
                            &#34;options&#34;: {
                                &#34;adminOpts&#34;: {
                                    &#34;updateOption&#34;: {
                                        &#34;syncUpdateCaches&#34;: sync_cache,
                                        &#34;spName&#34;: self.update_option[&#39;SPName&#39;],
                                        &#34;CUNumber&#34;: cu_number,
                                        &#34;isWindows&#34;: True,
                                        &#34;majorOnly&#34;: False,
                                        &#34;isSpName&#34;: self.update_option[&#39;IsSPName&#39;],
                                        &#34;copyUpdates&#34;: True,
                                        &#34;isHotfixesDownload&#34;: self.update_option[&#39;isHotfixesDownload&#39;],
                                        &#34;isSpDelayedDays&#34;: self.update_option[&#39;isSpDelayedDays&#39;],
                                        &#34;copySoftwareAndUpdates&#34;: False,
                                        &#34;isUnix&#34;: True,
                                        &#34;unixDownloadPackages&#34;: {
                                            &#34;linuxosX64&#34;: &#39;LINUX_X86_64&#39; in os_list,
                                            &#34;solarisosX64&#34;: &#39;SOLARIS_X86_64&#39; in os_list,
                                            &#34;solsparcos&#34;: &#39;Solaris-SPARC-X86&#39; in os_list,
                                            &#34;freeBSDos&#34;: &#39;FREEBSD_X86&#39; in os_list,
                                            &#34;linuxos&#34;: &#39;LINUX_X86&#39; in os_list,
                                            &#34;linuxosPPC64le&#34;: &#39;LINUX_PPC64LE&#39; in os_list,
                                            &#34;freeBSDosX64&#34;: &#39;FREEBSD_X86_64&#39; in os_list,
                                            &#34;solarisos&#34;: &#39;SOLARIS_SPARC&#39; in os_list,
                                            &#34;linuxs390os&#34;: &#39;Linux-S390-31&#39; in os_list,
                                            &#34;darwinos&#34;: &#39;MAC_OS&#39; in os_list,
                                            &#34;linuxosS390&#34;: &#39;Linux-S390&#39; in os_list,
                                            &#34;aixppcos&#34;: &#39;Aix-PPC-32&#39; in os_list,
                                            &#34;linuxosPPC64&#34;: &#39;LINUX_PPC64&#39; in os_list,
                                            &#34;aixos&#34;: &#39;AIX_PPC&#39; in os_list,
                                            &#34;hpos&#34;: &#39;HP_IA64&#39; in os_list,
                                            &#34;solos&#34;: &#39;Solaris X86&#39; in os_list
                                        },
                                        &#34;windowsDownloadPackages&#34;: {
                                            &#34;windowsX64&#34;: &#39;WINDOWS_X64&#39; in os_list,
                                            &#34;windows32&#34;: &#39;WINDOWS_X32&#39; in os_list
                                        },
                                        &#34;clientAndClientGroups&#34;: client_groups,
                                        &#34;downloadUpdatesJobOptions&#34;: {
                                            &#34;downloadSoftware&#34;: True
                                        }
                                    }
                                }
                            }
                        }
                    ]
                }
            }

        if schedule_pattern:
            request_json = SchedulePattern().create_schedule(request_json, schedule_pattern)

        method = &#39;PUT&#39; if version &gt;= 36 else &#39;POST&#39;
        url = self._services[&#39;DOWNLOAD_SOFTWARE&#39;] if version &gt;= 36 else self._services[&#39;CREATE_TASK&#39;]

        flag, response = self._cvpysdkcommcell_object.make_request(
            method, url, request_json
        )

        if flag:
            if response.json():
                if &#34;jobId&#34; in response.json() or &#34;jobIds&#34; in response.json():
                    return Job(self.commcell_object, response.json()[&#39;jobId&#39;]) if version &gt;= 36 \
                        else Job(self.commcell_object, response.json()[&#39;jobIds&#39;][0])

                elif &#34;taskId&#34; in response.json():
                    return Schedules(self.commcell_object).get(task_id=response.json()[&#39;taskId&#39;])

                else:
                    raise SDKException(&#39;Download&#39;, &#39;101&#39;)

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)

    def copy_software(self, media_loc, username=None, password=None, sync_cache=True, sync_cache_list=None, schedule_pattern=None):
        &#34;&#34;&#34;copies media from the specified location on the commcell

                    Args:

                        media_loc      (str)           --  Media Location to be used for copy software

                        username       (str)           --  username to authenticate to external location

                        password       (str)           --  password to authenticate to external location

                        sync_cache (bool)              --  True if download and sync
                                                           False only download

                        sync_cache_list (list)         --  list of names of remote caches to sync
                                                            use None to sync all caches

                        schedule_pattern(dict)         --  pattern for schedule task


                    Returns:
                        object - instance of the Job class for this copy software job

                    Raises:
                        SDKException:
                            if Download job failed

                            if response is empty

                            if response is not success

                            if another download job is running
                    Usage:

                        -   if media_location directory is local to the machine - username and password is not needed

                            &gt;&gt;&gt; commcell_obj.copy_software(media_loc = &#34;C:\\Downloads\\Media&#34;)

                        -   if Media_location directory is remote- username and passsword(base 64 encoded) are needed
                            to authenticate the cache

                            &gt;&gt;&gt; commcell_obj.copy_software(
                            media_loc = &#34;\\\\subdomain.company.com\\Media&#34;,
                            username = &#34;domainone\\userone&#34;,
                            password = &#34;base64encoded password&#34;
                            )
                &#34;&#34;&#34;
        version = self.commcell_object.commserv_version
        client_auth = {}
        if username:
            if password is None:
                raise Exception(f&#34;Password missing for remote location {media_loc}&#34;)
            client_auth = {
                &#34;userName&#34;: username,
                &#34;password&#34;: password
            }

        if version &gt;= 36:
            client_groups = []
            if sync_cache:
                if sync_cache_list:
                    for cache in sync_cache_list:
                        client_groups.append({&#34;type&#34;: &#34;CLIENT_ENTITY&#34;,
                                              &#34;clientName&#34;: cache,
                                              &#34;id&#34;: self.commcell_object.clients.get(cache).client_id
                                              })
                else:
                    flag_1, response_1 = self._cvpysdkcommcell_object.make_request(&#39;GET&#39;, self._services[&#39;CREATE_RC&#39;])
                    cache_list = []
                    for obj in response_1.json()[&#39;softwareCacheDetailList&#39;]:
                        if obj[&#39;cache&#39;][&#39;id&#39;] != 2:
                            cache_list.append(obj[&#39;cache&#39;][&#39;name&#39;])
                    for client in cache_list:
                        client_groups.append({&#34;type&#34;: &#34;CLIENT_ENTITY&#34;,
                                              &#34;clientName&#34;: client,
                                              &#34;id&#34;: int(self.commcell_object.clients.all_clients[client][&#39;id&#39;])})
            request_json = {
                &#34;copyConfiguration&#34;: {
                    &#34;downloadPath&#34;: media_loc
                },
                &#34;notifyWhenJobCompletes&#34;: False,
                &#34;entities&#34;: client_groups
            }
            if client_auth:
                request_json[&#39;copyConfiguration&#39;][&#39;username&#39;] = username
                request_json[&#39;copyConfiguration&#39;][&#39;password&#39;] = password
        else:
            request_json = {
                &#34;taskInfo&#34;: {
                    &#34;task&#34;: {
                        &#34;taskType&#34;: 1,
                        &#34;initiatedFrom&#34;: 2,
                        &#34;policyType&#34;: 0,
                        &#34;alert&#34;: {
                            &#34;alertName&#34;: &#34;&#34;
                        },
                        &#34;taskFlags&#34;: {
                            &#34;isEdgeDrive&#34;: False,
                            &#34;disabled&#34;: False
                        }
                    },
                    &#34;subTasks&#34;: [
                        {
                            &#34;subTaskOperation&#34;: 1,
                            &#34;subTask&#34;: {
                                &#34;subTaskType&#34;: 1,
                                &#34;operationType&#34;: 4019
                            },
                            &#34;options&#34;: {
                                &#34;adminOpts&#34;: {
                                    &#34;updateOption&#34;: {
                                        &#34;syncUpdateCaches&#34;: sync_cache,
                                        &#34;copyUpdates&#34;: True,
                                        &#34;copySoftwareAndUpdates&#34;: True,
                                        &#34;clientAndClientGroups&#34;: [
                                            {
                                                &#34;_type_&#34;: 2
                                            }
                                        ],
                                        &#34;downloadUpdatesJobOptions&#34;: {
                                            &#34;downloadSoftware&#34;: True,
                                            &#34;updateCachePath&#34;: media_loc,
                                            &#34;clientAuth&#34;: client_auth
                                        }
                                    }
                                }
                            }
                        }
                    ]
                }
            }

        if schedule_pattern:
            request_json = SchedulePattern().create_schedule(request_json, schedule_pattern)

        method = &#39;PUT&#39; if version &gt;= 36 else &#39;POST&#39;
        url = self._services[&#39;DOWNLOAD_SOFTWARE&#39;] if version &gt;= 36 else self._services[&#39;CREATE_TASK&#39;]

        flag, response = self._cvpysdkcommcell_object.make_request(
            method, url, request_json
        )

        if flag:
            if response.json():
                if &#34;jobId&#34; in response.json() or &#34;jobIds&#34; in response.json():
                    return Job(self.commcell_object, response.json()[&#39;jobId&#39;]) if version &gt;= 36 \
                        else Job(self.commcell_object, response.json()[&#39;jobIds&#39;][0])

                elif &#34;taskId&#34; in response.json():
                    return Schedules(self.commcell_object).get(task_id=response.json()[&#39;taskId&#39;])

                else:
                    raise SDKException(&#39;Download&#39;, &#39;101&#39;)

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)

    def sync_remote_cache(self, client_list=None, schedule_pattern=None):
        &#34;&#34;&#34;Syncs remote cache

            Args:

                client_list  --  list of client names
                Default is None. By default all remote cache clients are synced

                schedule_pattern (dict)        --  Pattern to schedule Sync Job

            Returns:
                object - instance of the Job class for sync job

            Raises:
                SDKException:
                    if sync job failed

                    if response is empty

                    if response is not success

                    if another sync job is running with the given client

        &#34;&#34;&#34;

        clients = []
        if client_list is None:

            clients = [{
                &#34;_type_&#34;: 2
            }]
        else:
            for each in client_list:
                clients.append({
                    &#34;_type_&#34;: 3,
                    &#34;clientName&#34;: each})

        request_json = {
            &#34;taskInfo&#34;: {
                &#34;task&#34;: {
                    &#34;ownerId&#34;: 1,
                    &#34;taskType&#34;: 1,
                    &#34;ownerName&#34;: &#34;admin&#34;,
                    &#34;initiatedFrom&#34;: 1,
                    &#34;policyType&#34;: 0,
                    &#34;taskFlags&#34;: {
                        &#34;disabled&#34;: False
                    }
                },
                &#34;subTasks&#34;: [
                    {
                        &#34;subTaskOperation&#34;: 1,
                        &#34;subTask&#34;: {
                            &#34;subTaskType&#34;: 1,
                            &#34;operationType&#34;: 4019
                        },
                        &#34;options&#34;: {
                            &#34;adminOpts&#34;: {
                                &#34;updateOption&#34;: {
                                    &#34;syncUpdateCaches&#34;: True,
                                    &#34;invokeLevel&#34;: 1,
                                    &#34;isWindows&#34;: False,
                                    &#34;majorOnly&#34;: False,
                                    &#34;isSpName&#34;: False,
                                    &#34;copyUpdates&#34;: True,
                                    &#34;isHotfixesDownload&#34;: False,
                                    &#34;isSpDelayedDays&#34;: True,
                                    &#34;copySoftwareAndUpdates&#34;: False,
                                    &#34;isUnix&#34;: False,
                                    &#34;clientAndClientGroups&#34;: clients,
                                    &#34;downloadUpdatesJobOptions&#34;: {
                                        &#34;downloadSoftware&#34;: False
                                    }
                                }
                            }
                        }
                    }
                ]
            }
        }

        if schedule_pattern:
            request_json = SchedulePattern().create_schedule(request_json, schedule_pattern)

        flag, response = self._cvpysdkcommcell_object.make_request(
            &#39;POST&#39;, self._services[&#39;CREATE_TASK&#39;], request_json
        )

        if flag:
            if response.json():
                if &#34;jobIds&#34; in response.json():
                    return Job(self.commcell_object, response.json()[&#39;jobIds&#39;][0])

                elif &#34;taskId&#34; in response.json():
                    return Schedules(self.commcell_object).get(task_id=response.json()[&#39;taskId&#39;])

                else:
                    raise SDKException(&#39;Download&#39;, &#39;101&#39;)

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.deployment.download.Download"><code class="flex name class">
<span>class <span class="ident">Download</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>"class for downloading software packages</p>
<p>Initialize commcell_object of the Download class.</p>
<h2 id="args">Args</h2>
<p>commcell_object (object)
&ndash;
instance of the Commcell class</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Download class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/deployment/download.py#L39-L585" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Download(object):
    &#34;&#34;&#34;&#34;class for downloading software packages&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize commcell_object of the Download class.

            Args:
                commcell_object (object)  --  instance of the Commcell class

            Returns:
                object - instance of the Download class

        &#34;&#34;&#34;

        self.commcell_object = commcell_object
        self._services = commcell_object._services
        self._cvpysdkcommcell_object = commcell_object._cvpysdk_object
        self.update_option = {}

    def download_software(
            self,
            options=None,
            os_list=None,
            service_pack=None,
            cu_number=0,
            sync_cache=True,
            sync_cache_list=None,
            schedule_pattern=None):
        &#34;&#34;&#34;Downloads the os packages on the commcell

            Args:

                options      (enum)            --  Download option to download software

                os_list      (list of enum)    --  list of windows/unix packages to be downloaded

                service_pack (int)             --  service pack to be downloaded

                cu_number (int)                --  maintenance release number

                sync_cache (bool)              --  True if download and sync
                                                   False only download
                
                sync_cache_list (list)         --  list of names of remote caches to sync
                                                   use None to sync all caches

                schedule_pattern (dict)        --  pattern for schedule task
                                                   

            Returns:
                object - instance of the Job class for this download job

            Raises:
                SDKException:
                    if Download job failed

                    if response is empty

                    if response is not success

                    if another download job is running

            Usage:

            -   if download_software is not given any parameters it takes default value of latest
                service pack for options and downloads WINDOWS_64 package

                &gt;&gt;&gt; commcell_obj.download_software()

            -   DownloadOptions and DownloadPackages enum is used for providing input to the
                download software method, it can be imported by

                &gt;&gt;&gt; from cvpysdk.deployment.deploymentconstants import DownloadOptions
                    from cvpysdk.deployment.deploymentconstants import DownloadPackages

            -   sample method calls for different options, for latest service pack

                &gt;&gt;&gt; commcell_obj.download_software(
                        options=DownloadOptions.lATEST_SERVICEPACK.value,
                        os_list=[DownloadPackages.WINDOWS_64.value]
                        )

            -   For Latest hotfixes for the installed service pack

                &gt;&gt;&gt; commcell_obj.download_software(
                        options=&#39;DownloadOptions.LATEST_HOTFIXES.value&#39;,
                        os_list=[DownloadPackages.WINDOWS_64.value,
                                DownloadPackages.UNIX_LINUX64.value]
                        )

            -   For service pack and hotfixes

                &gt;&gt;&gt; commcell_obj.download_software(
                        options=&#39;DownloadOptions.SERVICEPACK_AND_HOTFIXES.value&#39;,
                        os_list=[DownloadPackages.UNIX_MAC.value],
                        service_pack=13,
                        cu_number=42
                        )

                    **NOTE:** service_pack parameter must be specified for third option

        &#34;&#34;&#34;

        # To set the default value if option is none
        version = self.commcell_object.commserv_version
        if options is None:
            options = &#39;latest service pack&#39;

        if DownloadOptions.LATEST_SERVICEPACK.value == options:
            self.update_option = {
                &#39;upgradeToLatestRelease&#39;: True,
                &#39;latestFixesForCurrentRelease&#39;: False,
                &#34;featureRelease&#34;: self.commcell_object.version,
                &#39;SPName&#39;: &#39;latest&#39;,
                &#39;IsSPName&#39;: False,
                &#39;isSpDelayedDays&#39;: True,
                &#39;isHotfixesDownload&#39;: False
            }
        elif DownloadOptions.LATEST_HOTFIXES.value == options:
            self.update_option = {
                &#39;upgradeToLatestRelease&#39;: False,
                &#39;latestFixesForCurrentRelease&#39;: True,
                &#39;SPName&#39;: &#39;hotfix&#39;,
                &#39;IsSPName&#39;: False,
                &#39;isSpDelayedDays&#39;: True,
                &#39;isHotfixesDownload&#39;: True
            }
        elif DownloadOptions.SERVICEPACK_AND_HOTFIXES.value == options:
            if service_pack is None:
                raise SDKException(&#39;Download&#39;, &#39;102&#39;)
            self.update_option = {
                &#39;upgradeToLatestRelease&#39;: False,
                &#39;latestFixesForCurrentRelease&#39;: False,
                &#34;featureRelease&#34;: &#34;11.&#34; + str(service_pack) + &#34;.&#34; + str(cu_number),
                &#39;SPName&#39;: str(service_pack),
                &#39;IsSPName&#39;: True,
                &#39;isSpDelayedDays&#39;: False,
                &#39;isHotfixesDownload&#39;: False
            }

        # to set the default value if os_list is none
        if os_list is None:
            os_list = [&#39;WINDOWS_X64&#39;]

        client_groups = []
        if sync_cache and sync_cache_list:
            for cache in sync_cache_list:
                client_groups.append({&#34;type&#34;: &#34;CLIENT_ENTITY&#34;,
                                      &#34;clientName&#34;: cache,
                                      &#34;id&#34;: self.commcell_object.clients.get(cache).client_id
                                      }) if version &gt;= 36 else client_groups.append({&#34;clientName&#34;: cache})
        elif sync_cache and not sync_cache_list:
            if version &lt; 36:
                client_groups = [{&#34;_type_&#34;: 2}]
            else:
                flag_1, response_1 = self._cvpysdkcommcell_object.make_request(&#39;GET&#39;, self._services[&#39;CREATE_RC&#39;])
                cache_list = []
                for obj in response_1.json()[&#39;softwareCacheDetailList&#39;]:
                    if obj[&#39;cache&#39;][&#39;id&#39;] != 2:
                        cache_list.append(obj[&#39;cache&#39;][&#39;name&#39;])
                for client in cache_list:
                    client_groups.append({&#34;type&#34;: &#34;CLIENT_ENTITY&#34;,
                                          &#34;clientName&#34;: client,
                                          &#34;id&#34;: int(self.commcell_object.clients.all_clients[client][&#39;id&#39;])})

        if version &gt;= 36:
            win_os_list = []
            unix_os_list = []
            temp_win = [&#39;WINDOWS_X64&#39;, &#39;WINDOWS_X32&#39;, &#39;WINDOWS_ARM64&#39;]

            for os in os_list:
                if os in temp_win:
                    win_os_list.append(os)
                else:
                    unix_os_list.append(os)
            request_json = {
                &#34;downloadConfiguration&#34;: {
                    &#34;upgradeToLatestRelease&#34;: self.update_option[&#39;upgradeToLatestRelease&#39;],
                    &#34;latestFixesForCurrentRelease&#34;: self.update_option[&#39;latestFixesForCurrentRelease&#39;],
                    &#34;windowsDownloadOptions&#34;: win_os_list,
                    &#34;unixDownloadOptions&#34;: unix_os_list,
                },
                &#34;notifyWhenJobCompletes&#34;: False,
                &#34;entities&#34;: client_groups

            }
            if DownloadOptions.SERVICEPACK_AND_HOTFIXES.value == options:
                request_json[&#34;downloadConfiguration&#34;][&#34;featureRelease&#34;] = self.update_option[&#39;featureRelease&#39;]
        else:
            request_json = {
                &#34;taskInfo&#34;: {
                    &#34;task&#34;: {
                        &#34;taskType&#34;: 1,
                        &#34;initiatedFrom&#34;: 2,
                        &#34;policyType&#34;: 0,
                        &#34;alert&#34;: {
                            &#34;alertName&#34;: &#34;&#34;
                        },
                        &#34;taskFlags&#34;: {
                            &#34;isEdgeDrive&#34;: False,
                            &#34;disabled&#34;: False
                        }
                    },
                    &#34;subTasks&#34;: [
                        {
                            &#34;subTaskOperation&#34;: 1,
                            &#34;subTask&#34;: {
                                &#34;subTaskType&#34;: 1,
                                &#34;operationType&#34;: 4019
                            },
                            &#34;options&#34;: {
                                &#34;adminOpts&#34;: {
                                    &#34;updateOption&#34;: {
                                        &#34;syncUpdateCaches&#34;: sync_cache,
                                        &#34;spName&#34;: self.update_option[&#39;SPName&#39;],
                                        &#34;CUNumber&#34;: cu_number,
                                        &#34;isWindows&#34;: True,
                                        &#34;majorOnly&#34;: False,
                                        &#34;isSpName&#34;: self.update_option[&#39;IsSPName&#39;],
                                        &#34;copyUpdates&#34;: True,
                                        &#34;isHotfixesDownload&#34;: self.update_option[&#39;isHotfixesDownload&#39;],
                                        &#34;isSpDelayedDays&#34;: self.update_option[&#39;isSpDelayedDays&#39;],
                                        &#34;copySoftwareAndUpdates&#34;: False,
                                        &#34;isUnix&#34;: True,
                                        &#34;unixDownloadPackages&#34;: {
                                            &#34;linuxosX64&#34;: &#39;LINUX_X86_64&#39; in os_list,
                                            &#34;solarisosX64&#34;: &#39;SOLARIS_X86_64&#39; in os_list,
                                            &#34;solsparcos&#34;: &#39;Solaris-SPARC-X86&#39; in os_list,
                                            &#34;freeBSDos&#34;: &#39;FREEBSD_X86&#39; in os_list,
                                            &#34;linuxos&#34;: &#39;LINUX_X86&#39; in os_list,
                                            &#34;linuxosPPC64le&#34;: &#39;LINUX_PPC64LE&#39; in os_list,
                                            &#34;freeBSDosX64&#34;: &#39;FREEBSD_X86_64&#39; in os_list,
                                            &#34;solarisos&#34;: &#39;SOLARIS_SPARC&#39; in os_list,
                                            &#34;linuxs390os&#34;: &#39;Linux-S390-31&#39; in os_list,
                                            &#34;darwinos&#34;: &#39;MAC_OS&#39; in os_list,
                                            &#34;linuxosS390&#34;: &#39;Linux-S390&#39; in os_list,
                                            &#34;aixppcos&#34;: &#39;Aix-PPC-32&#39; in os_list,
                                            &#34;linuxosPPC64&#34;: &#39;LINUX_PPC64&#39; in os_list,
                                            &#34;aixos&#34;: &#39;AIX_PPC&#39; in os_list,
                                            &#34;hpos&#34;: &#39;HP_IA64&#39; in os_list,
                                            &#34;solos&#34;: &#39;Solaris X86&#39; in os_list
                                        },
                                        &#34;windowsDownloadPackages&#34;: {
                                            &#34;windowsX64&#34;: &#39;WINDOWS_X64&#39; in os_list,
                                            &#34;windows32&#34;: &#39;WINDOWS_X32&#39; in os_list
                                        },
                                        &#34;clientAndClientGroups&#34;: client_groups,
                                        &#34;downloadUpdatesJobOptions&#34;: {
                                            &#34;downloadSoftware&#34;: True
                                        }
                                    }
                                }
                            }
                        }
                    ]
                }
            }

        if schedule_pattern:
            request_json = SchedulePattern().create_schedule(request_json, schedule_pattern)

        method = &#39;PUT&#39; if version &gt;= 36 else &#39;POST&#39;
        url = self._services[&#39;DOWNLOAD_SOFTWARE&#39;] if version &gt;= 36 else self._services[&#39;CREATE_TASK&#39;]

        flag, response = self._cvpysdkcommcell_object.make_request(
            method, url, request_json
        )

        if flag:
            if response.json():
                if &#34;jobId&#34; in response.json() or &#34;jobIds&#34; in response.json():
                    return Job(self.commcell_object, response.json()[&#39;jobId&#39;]) if version &gt;= 36 \
                        else Job(self.commcell_object, response.json()[&#39;jobIds&#39;][0])

                elif &#34;taskId&#34; in response.json():
                    return Schedules(self.commcell_object).get(task_id=response.json()[&#39;taskId&#39;])

                else:
                    raise SDKException(&#39;Download&#39;, &#39;101&#39;)

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)

    def copy_software(self, media_loc, username=None, password=None, sync_cache=True, sync_cache_list=None, schedule_pattern=None):
        &#34;&#34;&#34;copies media from the specified location on the commcell

                    Args:

                        media_loc      (str)           --  Media Location to be used for copy software

                        username       (str)           --  username to authenticate to external location

                        password       (str)           --  password to authenticate to external location

                        sync_cache (bool)              --  True if download and sync
                                                           False only download

                        sync_cache_list (list)         --  list of names of remote caches to sync
                                                            use None to sync all caches

                        schedule_pattern(dict)         --  pattern for schedule task


                    Returns:
                        object - instance of the Job class for this copy software job

                    Raises:
                        SDKException:
                            if Download job failed

                            if response is empty

                            if response is not success

                            if another download job is running
                    Usage:

                        -   if media_location directory is local to the machine - username and password is not needed

                            &gt;&gt;&gt; commcell_obj.copy_software(media_loc = &#34;C:\\Downloads\\Media&#34;)

                        -   if Media_location directory is remote- username and passsword(base 64 encoded) are needed
                            to authenticate the cache

                            &gt;&gt;&gt; commcell_obj.copy_software(
                            media_loc = &#34;\\\\subdomain.company.com\\Media&#34;,
                            username = &#34;domainone\\userone&#34;,
                            password = &#34;base64encoded password&#34;
                            )
                &#34;&#34;&#34;
        version = self.commcell_object.commserv_version
        client_auth = {}
        if username:
            if password is None:
                raise Exception(f&#34;Password missing for remote location {media_loc}&#34;)
            client_auth = {
                &#34;userName&#34;: username,
                &#34;password&#34;: password
            }

        if version &gt;= 36:
            client_groups = []
            if sync_cache:
                if sync_cache_list:
                    for cache in sync_cache_list:
                        client_groups.append({&#34;type&#34;: &#34;CLIENT_ENTITY&#34;,
                                              &#34;clientName&#34;: cache,
                                              &#34;id&#34;: self.commcell_object.clients.get(cache).client_id
                                              })
                else:
                    flag_1, response_1 = self._cvpysdkcommcell_object.make_request(&#39;GET&#39;, self._services[&#39;CREATE_RC&#39;])
                    cache_list = []
                    for obj in response_1.json()[&#39;softwareCacheDetailList&#39;]:
                        if obj[&#39;cache&#39;][&#39;id&#39;] != 2:
                            cache_list.append(obj[&#39;cache&#39;][&#39;name&#39;])
                    for client in cache_list:
                        client_groups.append({&#34;type&#34;: &#34;CLIENT_ENTITY&#34;,
                                              &#34;clientName&#34;: client,
                                              &#34;id&#34;: int(self.commcell_object.clients.all_clients[client][&#39;id&#39;])})
            request_json = {
                &#34;copyConfiguration&#34;: {
                    &#34;downloadPath&#34;: media_loc
                },
                &#34;notifyWhenJobCompletes&#34;: False,
                &#34;entities&#34;: client_groups
            }
            if client_auth:
                request_json[&#39;copyConfiguration&#39;][&#39;username&#39;] = username
                request_json[&#39;copyConfiguration&#39;][&#39;password&#39;] = password
        else:
            request_json = {
                &#34;taskInfo&#34;: {
                    &#34;task&#34;: {
                        &#34;taskType&#34;: 1,
                        &#34;initiatedFrom&#34;: 2,
                        &#34;policyType&#34;: 0,
                        &#34;alert&#34;: {
                            &#34;alertName&#34;: &#34;&#34;
                        },
                        &#34;taskFlags&#34;: {
                            &#34;isEdgeDrive&#34;: False,
                            &#34;disabled&#34;: False
                        }
                    },
                    &#34;subTasks&#34;: [
                        {
                            &#34;subTaskOperation&#34;: 1,
                            &#34;subTask&#34;: {
                                &#34;subTaskType&#34;: 1,
                                &#34;operationType&#34;: 4019
                            },
                            &#34;options&#34;: {
                                &#34;adminOpts&#34;: {
                                    &#34;updateOption&#34;: {
                                        &#34;syncUpdateCaches&#34;: sync_cache,
                                        &#34;copyUpdates&#34;: True,
                                        &#34;copySoftwareAndUpdates&#34;: True,
                                        &#34;clientAndClientGroups&#34;: [
                                            {
                                                &#34;_type_&#34;: 2
                                            }
                                        ],
                                        &#34;downloadUpdatesJobOptions&#34;: {
                                            &#34;downloadSoftware&#34;: True,
                                            &#34;updateCachePath&#34;: media_loc,
                                            &#34;clientAuth&#34;: client_auth
                                        }
                                    }
                                }
                            }
                        }
                    ]
                }
            }

        if schedule_pattern:
            request_json = SchedulePattern().create_schedule(request_json, schedule_pattern)

        method = &#39;PUT&#39; if version &gt;= 36 else &#39;POST&#39;
        url = self._services[&#39;DOWNLOAD_SOFTWARE&#39;] if version &gt;= 36 else self._services[&#39;CREATE_TASK&#39;]

        flag, response = self._cvpysdkcommcell_object.make_request(
            method, url, request_json
        )

        if flag:
            if response.json():
                if &#34;jobId&#34; in response.json() or &#34;jobIds&#34; in response.json():
                    return Job(self.commcell_object, response.json()[&#39;jobId&#39;]) if version &gt;= 36 \
                        else Job(self.commcell_object, response.json()[&#39;jobIds&#39;][0])

                elif &#34;taskId&#34; in response.json():
                    return Schedules(self.commcell_object).get(task_id=response.json()[&#39;taskId&#39;])

                else:
                    raise SDKException(&#39;Download&#39;, &#39;101&#39;)

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)

    def sync_remote_cache(self, client_list=None, schedule_pattern=None):
        &#34;&#34;&#34;Syncs remote cache

            Args:

                client_list  --  list of client names
                Default is None. By default all remote cache clients are synced

                schedule_pattern (dict)        --  Pattern to schedule Sync Job

            Returns:
                object - instance of the Job class for sync job

            Raises:
                SDKException:
                    if sync job failed

                    if response is empty

                    if response is not success

                    if another sync job is running with the given client

        &#34;&#34;&#34;

        clients = []
        if client_list is None:

            clients = [{
                &#34;_type_&#34;: 2
            }]
        else:
            for each in client_list:
                clients.append({
                    &#34;_type_&#34;: 3,
                    &#34;clientName&#34;: each})

        request_json = {
            &#34;taskInfo&#34;: {
                &#34;task&#34;: {
                    &#34;ownerId&#34;: 1,
                    &#34;taskType&#34;: 1,
                    &#34;ownerName&#34;: &#34;admin&#34;,
                    &#34;initiatedFrom&#34;: 1,
                    &#34;policyType&#34;: 0,
                    &#34;taskFlags&#34;: {
                        &#34;disabled&#34;: False
                    }
                },
                &#34;subTasks&#34;: [
                    {
                        &#34;subTaskOperation&#34;: 1,
                        &#34;subTask&#34;: {
                            &#34;subTaskType&#34;: 1,
                            &#34;operationType&#34;: 4019
                        },
                        &#34;options&#34;: {
                            &#34;adminOpts&#34;: {
                                &#34;updateOption&#34;: {
                                    &#34;syncUpdateCaches&#34;: True,
                                    &#34;invokeLevel&#34;: 1,
                                    &#34;isWindows&#34;: False,
                                    &#34;majorOnly&#34;: False,
                                    &#34;isSpName&#34;: False,
                                    &#34;copyUpdates&#34;: True,
                                    &#34;isHotfixesDownload&#34;: False,
                                    &#34;isSpDelayedDays&#34;: True,
                                    &#34;copySoftwareAndUpdates&#34;: False,
                                    &#34;isUnix&#34;: False,
                                    &#34;clientAndClientGroups&#34;: clients,
                                    &#34;downloadUpdatesJobOptions&#34;: {
                                        &#34;downloadSoftware&#34;: False
                                    }
                                }
                            }
                        }
                    }
                ]
            }
        }

        if schedule_pattern:
            request_json = SchedulePattern().create_schedule(request_json, schedule_pattern)

        flag, response = self._cvpysdkcommcell_object.make_request(
            &#39;POST&#39;, self._services[&#39;CREATE_TASK&#39;], request_json
        )

        if flag:
            if response.json():
                if &#34;jobIds&#34; in response.json():
                    return Job(self.commcell_object, response.json()[&#39;jobIds&#39;][0])

                elif &#34;taskId&#34; in response.json():
                    return Schedules(self.commcell_object).get(task_id=response.json()[&#39;taskId&#39;])

                else:
                    raise SDKException(&#39;Download&#39;, &#39;101&#39;)

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)</code></pre>
</details>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.deployment.download.Download.copy_software"><code class="name flex">
<span>def <span class="ident">copy_software</span></span>(<span>self, media_loc, username=None, password=None, sync_cache=True, sync_cache_list=None, schedule_pattern=None)</span>
</code></dt>
<dd>
<div class="desc"><p>copies media from the specified location on the commcell</p>
<h2 id="args">Args</h2>
<p>media_loc
(str)
&ndash;
Media Location to be used for copy software</p>
<p>username
(str)
&ndash;
username to authenticate to external location</p>
<p>password
(str)
&ndash;
password to authenticate to external location</p>
<p>sync_cache (bool)
&ndash;
True if download and sync
False only download</p>
<p>sync_cache_list (list)
&ndash;
list of names of remote caches to sync
use None to sync all caches</p>
<p>schedule_pattern(dict)
&ndash;
pattern for schedule task</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this copy software job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if Download job failed</p>
<pre><code>if response is empty

if response is not success

if another download job is running
</code></pre>
<h2 id="usage">Usage</h2>
<ul>
<li>
<p>if media_location directory is local to the machine - username and password is not needed</p>
<blockquote>
<blockquote>
<blockquote>
<p>commcell_obj.copy_software(media_loc = "C:\Downloads\Media")</p>
</blockquote>
</blockquote>
</blockquote>
</li>
<li>
<p>if Media_location directory is remote- username and passsword(base 64 encoded) are needed
to authenticate the cache</p>
<blockquote>
<blockquote>
<blockquote>
<p>commcell_obj.copy_software(
media_loc = "\subdomain.company.com\Media",
username = "domainone\userone",
password = "base64encoded password"
)</p>
</blockquote>
</blockquote>
</blockquote>
</li>
</ul></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/deployment/download.py#L324-L481" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def copy_software(self, media_loc, username=None, password=None, sync_cache=True, sync_cache_list=None, schedule_pattern=None):
    &#34;&#34;&#34;copies media from the specified location on the commcell

                Args:

                    media_loc      (str)           --  Media Location to be used for copy software

                    username       (str)           --  username to authenticate to external location

                    password       (str)           --  password to authenticate to external location

                    sync_cache (bool)              --  True if download and sync
                                                       False only download

                    sync_cache_list (list)         --  list of names of remote caches to sync
                                                        use None to sync all caches

                    schedule_pattern(dict)         --  pattern for schedule task


                Returns:
                    object - instance of the Job class for this copy software job

                Raises:
                    SDKException:
                        if Download job failed

                        if response is empty

                        if response is not success

                        if another download job is running
                Usage:

                    -   if media_location directory is local to the machine - username and password is not needed

                        &gt;&gt;&gt; commcell_obj.copy_software(media_loc = &#34;C:\\Downloads\\Media&#34;)

                    -   if Media_location directory is remote- username and passsword(base 64 encoded) are needed
                        to authenticate the cache

                        &gt;&gt;&gt; commcell_obj.copy_software(
                        media_loc = &#34;\\\\subdomain.company.com\\Media&#34;,
                        username = &#34;domainone\\userone&#34;,
                        password = &#34;base64encoded password&#34;
                        )
            &#34;&#34;&#34;
    version = self.commcell_object.commserv_version
    client_auth = {}
    if username:
        if password is None:
            raise Exception(f&#34;Password missing for remote location {media_loc}&#34;)
        client_auth = {
            &#34;userName&#34;: username,
            &#34;password&#34;: password
        }

    if version &gt;= 36:
        client_groups = []
        if sync_cache:
            if sync_cache_list:
                for cache in sync_cache_list:
                    client_groups.append({&#34;type&#34;: &#34;CLIENT_ENTITY&#34;,
                                          &#34;clientName&#34;: cache,
                                          &#34;id&#34;: self.commcell_object.clients.get(cache).client_id
                                          })
            else:
                flag_1, response_1 = self._cvpysdkcommcell_object.make_request(&#39;GET&#39;, self._services[&#39;CREATE_RC&#39;])
                cache_list = []
                for obj in response_1.json()[&#39;softwareCacheDetailList&#39;]:
                    if obj[&#39;cache&#39;][&#39;id&#39;] != 2:
                        cache_list.append(obj[&#39;cache&#39;][&#39;name&#39;])
                for client in cache_list:
                    client_groups.append({&#34;type&#34;: &#34;CLIENT_ENTITY&#34;,
                                          &#34;clientName&#34;: client,
                                          &#34;id&#34;: int(self.commcell_object.clients.all_clients[client][&#39;id&#39;])})
        request_json = {
            &#34;copyConfiguration&#34;: {
                &#34;downloadPath&#34;: media_loc
            },
            &#34;notifyWhenJobCompletes&#34;: False,
            &#34;entities&#34;: client_groups
        }
        if client_auth:
            request_json[&#39;copyConfiguration&#39;][&#39;username&#39;] = username
            request_json[&#39;copyConfiguration&#39;][&#39;password&#39;] = password
    else:
        request_json = {
            &#34;taskInfo&#34;: {
                &#34;task&#34;: {
                    &#34;taskType&#34;: 1,
                    &#34;initiatedFrom&#34;: 2,
                    &#34;policyType&#34;: 0,
                    &#34;alert&#34;: {
                        &#34;alertName&#34;: &#34;&#34;
                    },
                    &#34;taskFlags&#34;: {
                        &#34;isEdgeDrive&#34;: False,
                        &#34;disabled&#34;: False
                    }
                },
                &#34;subTasks&#34;: [
                    {
                        &#34;subTaskOperation&#34;: 1,
                        &#34;subTask&#34;: {
                            &#34;subTaskType&#34;: 1,
                            &#34;operationType&#34;: 4019
                        },
                        &#34;options&#34;: {
                            &#34;adminOpts&#34;: {
                                &#34;updateOption&#34;: {
                                    &#34;syncUpdateCaches&#34;: sync_cache,
                                    &#34;copyUpdates&#34;: True,
                                    &#34;copySoftwareAndUpdates&#34;: True,
                                    &#34;clientAndClientGroups&#34;: [
                                        {
                                            &#34;_type_&#34;: 2
                                        }
                                    ],
                                    &#34;downloadUpdatesJobOptions&#34;: {
                                        &#34;downloadSoftware&#34;: True,
                                        &#34;updateCachePath&#34;: media_loc,
                                        &#34;clientAuth&#34;: client_auth
                                    }
                                }
                            }
                        }
                    }
                ]
            }
        }

    if schedule_pattern:
        request_json = SchedulePattern().create_schedule(request_json, schedule_pattern)

    method = &#39;PUT&#39; if version &gt;= 36 else &#39;POST&#39;
    url = self._services[&#39;DOWNLOAD_SOFTWARE&#39;] if version &gt;= 36 else self._services[&#39;CREATE_TASK&#39;]

    flag, response = self._cvpysdkcommcell_object.make_request(
        method, url, request_json
    )

    if flag:
        if response.json():
            if &#34;jobId&#34; in response.json() or &#34;jobIds&#34; in response.json():
                return Job(self.commcell_object, response.json()[&#39;jobId&#39;]) if version &gt;= 36 \
                    else Job(self.commcell_object, response.json()[&#39;jobIds&#39;][0])

            elif &#34;taskId&#34; in response.json():
                return Schedules(self.commcell_object).get(task_id=response.json()[&#39;taskId&#39;])

            else:
                raise SDKException(&#39;Download&#39;, &#39;101&#39;)

        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.deployment.download.Download.download_software"><code class="name flex">
<span>def <span class="ident">download_software</span></span>(<span>self, options=None, os_list=None, service_pack=None, cu_number=0, sync_cache=True, sync_cache_list=None, schedule_pattern=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Downloads the os packages on the commcell</p>
<h2 id="args">Args</h2>
<p>options
(enum)
&ndash;
Download option to download software</p>
<p>os_list
(list of enum)
&ndash;
list of windows/unix packages to be downloaded</p>
<p>service_pack (int)
&ndash;
service pack to be downloaded</p>
<p>cu_number (int)
&ndash;
maintenance release number</p>
<p>sync_cache (bool)
&ndash;
True if download and sync
False only download</p>
<p>sync_cache_list (list)
&ndash;
list of names of remote caches to sync
use None to sync all caches</p>
<p>schedule_pattern (dict)
&ndash;
pattern for schedule task</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this download job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if Download job failed</p>
<pre><code>if response is empty

if response is not success

if another download job is running
</code></pre>
<p>Usage:</p>
<ul>
<li>
<p>if download_software is not given any parameters it takes default value of latest
service pack for options and downloads WINDOWS_64 package</p>
<blockquote>
<blockquote>
<blockquote>
<p>commcell_obj.download_software()</p>
</blockquote>
</blockquote>
</blockquote>
</li>
<li>
<p>DownloadOptions and DownloadPackages enum is used for providing input to the
download software method, it can be imported by</p>
<blockquote>
<blockquote>
<blockquote>
<p>from cvpysdk.deployment.deploymentconstants import DownloadOptions
from cvpysdk.deployment.deploymentconstants import DownloadPackages</p>
</blockquote>
</blockquote>
</blockquote>
</li>
<li>
<p>sample method calls for different options, for latest service pack</p>
<blockquote>
<blockquote>
<blockquote>
<p>commcell_obj.download_software(
options=DownloadOptions.lATEST_SERVICEPACK.value,
os_list=[DownloadPackages.WINDOWS_64.value]
)</p>
</blockquote>
</blockquote>
</blockquote>
</li>
<li>
<p>For Latest hotfixes for the installed service pack</p>
<blockquote>
<blockquote>
<blockquote>
<p>commcell_obj.download_software(
options='DownloadOptions.LATEST_HOTFIXES.value',
os_list=[DownloadPackages.WINDOWS_64.value,
DownloadPackages.UNIX_LINUX64.value]
)</p>
</blockquote>
</blockquote>
</blockquote>
</li>
<li>
<p>For service pack and hotfixes</p>
<blockquote>
<blockquote>
<blockquote>
<p>commcell_obj.download_software(
options='DownloadOptions.SERVICEPACK_AND_HOTFIXES.value',
os_list=[DownloadPackages.UNIX_MAC.value],
service_pack=13,
cu_number=42
)</p>
</blockquote>
</blockquote>
</blockquote>
<pre><code>**NOTE:** service_pack parameter must be specified for third option
</code></pre>
</li>
</ul></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/deployment/download.py#L58-L322" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def download_software(
        self,
        options=None,
        os_list=None,
        service_pack=None,
        cu_number=0,
        sync_cache=True,
        sync_cache_list=None,
        schedule_pattern=None):
    &#34;&#34;&#34;Downloads the os packages on the commcell

        Args:

            options      (enum)            --  Download option to download software

            os_list      (list of enum)    --  list of windows/unix packages to be downloaded

            service_pack (int)             --  service pack to be downloaded

            cu_number (int)                --  maintenance release number

            sync_cache (bool)              --  True if download and sync
                                               False only download
            
            sync_cache_list (list)         --  list of names of remote caches to sync
                                               use None to sync all caches

            schedule_pattern (dict)        --  pattern for schedule task
                                               

        Returns:
            object - instance of the Job class for this download job

        Raises:
            SDKException:
                if Download job failed

                if response is empty

                if response is not success

                if another download job is running

        Usage:

        -   if download_software is not given any parameters it takes default value of latest
            service pack for options and downloads WINDOWS_64 package

            &gt;&gt;&gt; commcell_obj.download_software()

        -   DownloadOptions and DownloadPackages enum is used for providing input to the
            download software method, it can be imported by

            &gt;&gt;&gt; from cvpysdk.deployment.deploymentconstants import DownloadOptions
                from cvpysdk.deployment.deploymentconstants import DownloadPackages

        -   sample method calls for different options, for latest service pack

            &gt;&gt;&gt; commcell_obj.download_software(
                    options=DownloadOptions.lATEST_SERVICEPACK.value,
                    os_list=[DownloadPackages.WINDOWS_64.value]
                    )

        -   For Latest hotfixes for the installed service pack

            &gt;&gt;&gt; commcell_obj.download_software(
                    options=&#39;DownloadOptions.LATEST_HOTFIXES.value&#39;,
                    os_list=[DownloadPackages.WINDOWS_64.value,
                            DownloadPackages.UNIX_LINUX64.value]
                    )

        -   For service pack and hotfixes

            &gt;&gt;&gt; commcell_obj.download_software(
                    options=&#39;DownloadOptions.SERVICEPACK_AND_HOTFIXES.value&#39;,
                    os_list=[DownloadPackages.UNIX_MAC.value],
                    service_pack=13,
                    cu_number=42
                    )

                **NOTE:** service_pack parameter must be specified for third option

    &#34;&#34;&#34;

    # To set the default value if option is none
    version = self.commcell_object.commserv_version
    if options is None:
        options = &#39;latest service pack&#39;

    if DownloadOptions.LATEST_SERVICEPACK.value == options:
        self.update_option = {
            &#39;upgradeToLatestRelease&#39;: True,
            &#39;latestFixesForCurrentRelease&#39;: False,
            &#34;featureRelease&#34;: self.commcell_object.version,
            &#39;SPName&#39;: &#39;latest&#39;,
            &#39;IsSPName&#39;: False,
            &#39;isSpDelayedDays&#39;: True,
            &#39;isHotfixesDownload&#39;: False
        }
    elif DownloadOptions.LATEST_HOTFIXES.value == options:
        self.update_option = {
            &#39;upgradeToLatestRelease&#39;: False,
            &#39;latestFixesForCurrentRelease&#39;: True,
            &#39;SPName&#39;: &#39;hotfix&#39;,
            &#39;IsSPName&#39;: False,
            &#39;isSpDelayedDays&#39;: True,
            &#39;isHotfixesDownload&#39;: True
        }
    elif DownloadOptions.SERVICEPACK_AND_HOTFIXES.value == options:
        if service_pack is None:
            raise SDKException(&#39;Download&#39;, &#39;102&#39;)
        self.update_option = {
            &#39;upgradeToLatestRelease&#39;: False,
            &#39;latestFixesForCurrentRelease&#39;: False,
            &#34;featureRelease&#34;: &#34;11.&#34; + str(service_pack) + &#34;.&#34; + str(cu_number),
            &#39;SPName&#39;: str(service_pack),
            &#39;IsSPName&#39;: True,
            &#39;isSpDelayedDays&#39;: False,
            &#39;isHotfixesDownload&#39;: False
        }

    # to set the default value if os_list is none
    if os_list is None:
        os_list = [&#39;WINDOWS_X64&#39;]

    client_groups = []
    if sync_cache and sync_cache_list:
        for cache in sync_cache_list:
            client_groups.append({&#34;type&#34;: &#34;CLIENT_ENTITY&#34;,
                                  &#34;clientName&#34;: cache,
                                  &#34;id&#34;: self.commcell_object.clients.get(cache).client_id
                                  }) if version &gt;= 36 else client_groups.append({&#34;clientName&#34;: cache})
    elif sync_cache and not sync_cache_list:
        if version &lt; 36:
            client_groups = [{&#34;_type_&#34;: 2}]
        else:
            flag_1, response_1 = self._cvpysdkcommcell_object.make_request(&#39;GET&#39;, self._services[&#39;CREATE_RC&#39;])
            cache_list = []
            for obj in response_1.json()[&#39;softwareCacheDetailList&#39;]:
                if obj[&#39;cache&#39;][&#39;id&#39;] != 2:
                    cache_list.append(obj[&#39;cache&#39;][&#39;name&#39;])
            for client in cache_list:
                client_groups.append({&#34;type&#34;: &#34;CLIENT_ENTITY&#34;,
                                      &#34;clientName&#34;: client,
                                      &#34;id&#34;: int(self.commcell_object.clients.all_clients[client][&#39;id&#39;])})

    if version &gt;= 36:
        win_os_list = []
        unix_os_list = []
        temp_win = [&#39;WINDOWS_X64&#39;, &#39;WINDOWS_X32&#39;, &#39;WINDOWS_ARM64&#39;]

        for os in os_list:
            if os in temp_win:
                win_os_list.append(os)
            else:
                unix_os_list.append(os)
        request_json = {
            &#34;downloadConfiguration&#34;: {
                &#34;upgradeToLatestRelease&#34;: self.update_option[&#39;upgradeToLatestRelease&#39;],
                &#34;latestFixesForCurrentRelease&#34;: self.update_option[&#39;latestFixesForCurrentRelease&#39;],
                &#34;windowsDownloadOptions&#34;: win_os_list,
                &#34;unixDownloadOptions&#34;: unix_os_list,
            },
            &#34;notifyWhenJobCompletes&#34;: False,
            &#34;entities&#34;: client_groups

        }
        if DownloadOptions.SERVICEPACK_AND_HOTFIXES.value == options:
            request_json[&#34;downloadConfiguration&#34;][&#34;featureRelease&#34;] = self.update_option[&#39;featureRelease&#39;]
    else:
        request_json = {
            &#34;taskInfo&#34;: {
                &#34;task&#34;: {
                    &#34;taskType&#34;: 1,
                    &#34;initiatedFrom&#34;: 2,
                    &#34;policyType&#34;: 0,
                    &#34;alert&#34;: {
                        &#34;alertName&#34;: &#34;&#34;
                    },
                    &#34;taskFlags&#34;: {
                        &#34;isEdgeDrive&#34;: False,
                        &#34;disabled&#34;: False
                    }
                },
                &#34;subTasks&#34;: [
                    {
                        &#34;subTaskOperation&#34;: 1,
                        &#34;subTask&#34;: {
                            &#34;subTaskType&#34;: 1,
                            &#34;operationType&#34;: 4019
                        },
                        &#34;options&#34;: {
                            &#34;adminOpts&#34;: {
                                &#34;updateOption&#34;: {
                                    &#34;syncUpdateCaches&#34;: sync_cache,
                                    &#34;spName&#34;: self.update_option[&#39;SPName&#39;],
                                    &#34;CUNumber&#34;: cu_number,
                                    &#34;isWindows&#34;: True,
                                    &#34;majorOnly&#34;: False,
                                    &#34;isSpName&#34;: self.update_option[&#39;IsSPName&#39;],
                                    &#34;copyUpdates&#34;: True,
                                    &#34;isHotfixesDownload&#34;: self.update_option[&#39;isHotfixesDownload&#39;],
                                    &#34;isSpDelayedDays&#34;: self.update_option[&#39;isSpDelayedDays&#39;],
                                    &#34;copySoftwareAndUpdates&#34;: False,
                                    &#34;isUnix&#34;: True,
                                    &#34;unixDownloadPackages&#34;: {
                                        &#34;linuxosX64&#34;: &#39;LINUX_X86_64&#39; in os_list,
                                        &#34;solarisosX64&#34;: &#39;SOLARIS_X86_64&#39; in os_list,
                                        &#34;solsparcos&#34;: &#39;Solaris-SPARC-X86&#39; in os_list,
                                        &#34;freeBSDos&#34;: &#39;FREEBSD_X86&#39; in os_list,
                                        &#34;linuxos&#34;: &#39;LINUX_X86&#39; in os_list,
                                        &#34;linuxosPPC64le&#34;: &#39;LINUX_PPC64LE&#39; in os_list,
                                        &#34;freeBSDosX64&#34;: &#39;FREEBSD_X86_64&#39; in os_list,
                                        &#34;solarisos&#34;: &#39;SOLARIS_SPARC&#39; in os_list,
                                        &#34;linuxs390os&#34;: &#39;Linux-S390-31&#39; in os_list,
                                        &#34;darwinos&#34;: &#39;MAC_OS&#39; in os_list,
                                        &#34;linuxosS390&#34;: &#39;Linux-S390&#39; in os_list,
                                        &#34;aixppcos&#34;: &#39;Aix-PPC-32&#39; in os_list,
                                        &#34;linuxosPPC64&#34;: &#39;LINUX_PPC64&#39; in os_list,
                                        &#34;aixos&#34;: &#39;AIX_PPC&#39; in os_list,
                                        &#34;hpos&#34;: &#39;HP_IA64&#39; in os_list,
                                        &#34;solos&#34;: &#39;Solaris X86&#39; in os_list
                                    },
                                    &#34;windowsDownloadPackages&#34;: {
                                        &#34;windowsX64&#34;: &#39;WINDOWS_X64&#39; in os_list,
                                        &#34;windows32&#34;: &#39;WINDOWS_X32&#39; in os_list
                                    },
                                    &#34;clientAndClientGroups&#34;: client_groups,
                                    &#34;downloadUpdatesJobOptions&#34;: {
                                        &#34;downloadSoftware&#34;: True
                                    }
                                }
                            }
                        }
                    }
                ]
            }
        }

    if schedule_pattern:
        request_json = SchedulePattern().create_schedule(request_json, schedule_pattern)

    method = &#39;PUT&#39; if version &gt;= 36 else &#39;POST&#39;
    url = self._services[&#39;DOWNLOAD_SOFTWARE&#39;] if version &gt;= 36 else self._services[&#39;CREATE_TASK&#39;]

    flag, response = self._cvpysdkcommcell_object.make_request(
        method, url, request_json
    )

    if flag:
        if response.json():
            if &#34;jobId&#34; in response.json() or &#34;jobIds&#34; in response.json():
                return Job(self.commcell_object, response.json()[&#39;jobId&#39;]) if version &gt;= 36 \
                    else Job(self.commcell_object, response.json()[&#39;jobIds&#39;][0])

            elif &#34;taskId&#34; in response.json():
                return Schedules(self.commcell_object).get(task_id=response.json()[&#39;taskId&#39;])

            else:
                raise SDKException(&#39;Download&#39;, &#39;101&#39;)

        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.deployment.download.Download.sync_remote_cache"><code class="name flex">
<span>def <span class="ident">sync_remote_cache</span></span>(<span>self, client_list=None, schedule_pattern=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Syncs remote cache</p>
<h2 id="args">Args</h2>
<p>client_list
&ndash;
list of client names
Default is None. By default all remote cache clients are synced</p>
<p>schedule_pattern (dict)
&ndash;
Pattern to schedule Sync Job</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for sync job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if sync job failed</p>
<pre><code>if response is empty

if response is not success

if another sync job is running with the given client
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/deployment/download.py#L483-L585" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def sync_remote_cache(self, client_list=None, schedule_pattern=None):
    &#34;&#34;&#34;Syncs remote cache

        Args:

            client_list  --  list of client names
            Default is None. By default all remote cache clients are synced

            schedule_pattern (dict)        --  Pattern to schedule Sync Job

        Returns:
            object - instance of the Job class for sync job

        Raises:
            SDKException:
                if sync job failed

                if response is empty

                if response is not success

                if another sync job is running with the given client

    &#34;&#34;&#34;

    clients = []
    if client_list is None:

        clients = [{
            &#34;_type_&#34;: 2
        }]
    else:
        for each in client_list:
            clients.append({
                &#34;_type_&#34;: 3,
                &#34;clientName&#34;: each})

    request_json = {
        &#34;taskInfo&#34;: {
            &#34;task&#34;: {
                &#34;ownerId&#34;: 1,
                &#34;taskType&#34;: 1,
                &#34;ownerName&#34;: &#34;admin&#34;,
                &#34;initiatedFrom&#34;: 1,
                &#34;policyType&#34;: 0,
                &#34;taskFlags&#34;: {
                    &#34;disabled&#34;: False
                }
            },
            &#34;subTasks&#34;: [
                {
                    &#34;subTaskOperation&#34;: 1,
                    &#34;subTask&#34;: {
                        &#34;subTaskType&#34;: 1,
                        &#34;operationType&#34;: 4019
                    },
                    &#34;options&#34;: {
                        &#34;adminOpts&#34;: {
                            &#34;updateOption&#34;: {
                                &#34;syncUpdateCaches&#34;: True,
                                &#34;invokeLevel&#34;: 1,
                                &#34;isWindows&#34;: False,
                                &#34;majorOnly&#34;: False,
                                &#34;isSpName&#34;: False,
                                &#34;copyUpdates&#34;: True,
                                &#34;isHotfixesDownload&#34;: False,
                                &#34;isSpDelayedDays&#34;: True,
                                &#34;copySoftwareAndUpdates&#34;: False,
                                &#34;isUnix&#34;: False,
                                &#34;clientAndClientGroups&#34;: clients,
                                &#34;downloadUpdatesJobOptions&#34;: {
                                    &#34;downloadSoftware&#34;: False
                                }
                            }
                        }
                    }
                }
            ]
        }
    }

    if schedule_pattern:
        request_json = SchedulePattern().create_schedule(request_json, schedule_pattern)

    flag, response = self._cvpysdkcommcell_object.make_request(
        &#39;POST&#39;, self._services[&#39;CREATE_TASK&#39;], request_json
    )

    if flag:
        if response.json():
            if &#34;jobIds&#34; in response.json():
                return Job(self.commcell_object, response.json()[&#39;jobIds&#39;][0])

            elif &#34;taskId&#34; in response.json():
                return Schedules(self.commcell_object).get(task_id=response.json()[&#39;taskId&#39;])

            else:
                raise SDKException(&#39;Download&#39;, &#39;101&#39;)

        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;)</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#download">Download</a></li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.deployment" href="index.html">cvpysdk.deployment</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.deployment.download.Download" href="#cvpysdk.deployment.download.Download">Download</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.deployment.download.Download.copy_software" href="#cvpysdk.deployment.download.Download.copy_software">copy_software</a></code></li>
<li><code><a title="cvpysdk.deployment.download.Download.download_software" href="#cvpysdk.deployment.download.Download.download_software">download_software</a></code></li>
<li><code><a title="cvpysdk.deployment.download.Download.sync_remote_cache" href="#cvpysdk.deployment.download.Download.sync_remote_cache">sync_remote_cache</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>