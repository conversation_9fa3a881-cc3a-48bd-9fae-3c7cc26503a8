<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.instances.cloudapps.salesforce_instance API documentation</title>
<meta name="description" content="File for operating on a Salesforce Instance …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.instances.cloudapps.salesforce_instance</code></h1>
</header>
<section id="section-intro">
<p>File for operating on a Salesforce Instance.</p>
<p>SalesforceInstance is the only class defined in this file.</p>
<p>SalesforceeInstance:
Derived class from CloudAppsInstance Base class, representing a
Salesforce instance, and to perform operations on that instance</p>
<h2 id="salesforceinstance">Salesforceinstance</h2>
<p>_restore_json()
&ndash;
Returns the JSON request to pass to the API as per the options selected by
the user</p>
<p>restore_to_file_system()
&ndash;
Runs object level restore to file system and returns object of Job or
Schedule class</p>
<p>restore_to_database()
&ndash;
Runs object level restore to database and returns object of Job or Schedule
class</p>
<p>restore_to_salesforce_from_database() &ndash;
Runs restore to Salesforce from database and returns object of Job or
Schedule class</p>
<p>restore_to_salesforce_from_media()
&ndash;
Runs restore to Salesforce from database and returns object of Job or
Schedule class</p>
<p>metadata_restore_to_file_system()
&ndash;
Runs metadata restore to file system and returns object of Job or Schedule
class</p>
<p>metadata_restore_to_salesforce()
&ndash;
Runs metadata restore to Salesforce and returns object of Job or Schedule
class</p>
<p>SalesforceInstance Attributes:</p>
<pre><code>**ca_instance_type**            --  Returns the instance type of this cloud apps instance

**organization_id**             --  Returns the Salesforce organization id

**login_url**                   --  Returns the login url of Salesforce organization

**consumer_id**                 --  Returns the Consumer Id of the Salesforce connected app used to authenticate
                                    with Salesforce by this instance

**proxy_client**                --  Returns the name of the access node. Returns None if client group is configured
                                    as access node
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/salesforce_instance.py#L1-L738" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;File for operating on a Salesforce Instance.

SalesforceInstance is the only class defined in this file.

SalesforceeInstance:    Derived class from CloudAppsInstance Base class, representing a
                            Salesforce instance, and to perform operations on that instance

SalesforceInstance:

    _restore_json()                     --  Returns the JSON request to pass to the API as per the options selected by
                                            the user

    restore_to_file_system()            --  Runs object level restore to file system and returns object of Job or
                                            Schedule class

    restore_to_database()               --  Runs object level restore to database and returns object of Job or Schedule
                                            class

    restore_to_salesforce_from_database() --  Runs restore to Salesforce from database and returns object of Job or
                                            Schedule class

    restore_to_salesforce_from_media()  --  Runs restore to Salesforce from database and returns object of Job or
                                            Schedule class

    metadata_restore_to_file_system()   --  Runs metadata restore to file system and returns object of Job or Schedule
                                            class

    metadata_restore_to_salesforce()    --  Runs metadata restore to Salesforce and returns object of Job or Schedule
                                            class

SalesforceInstance Attributes:

    **ca_instance_type**            --  Returns the instance type of this cloud apps instance

    **organization_id**             --  Returns the Salesforce organization id

    **login_url**                   --  Returns the login url of Salesforce organization

    **consumer_id**                 --  Returns the Consumer Id of the Salesforce connected app used to authenticate
                                        with Salesforce by this instance

    **proxy_client**                --  Returns the name of the access node. Returns None if client group is configured
                                        as access node

&#34;&#34;&#34;
from __future__ import unicode_literals
from base64 import b64encode
from ..cainstance import CloudAppsInstance
from ...exception import SDKException


class SalesforceInstance(CloudAppsInstance):
    &#34;&#34;&#34;Class for representing an Instance of the Salesforce instance type.&#34;&#34;&#34;

    @property
    def ca_instance_type(self):
        &#34;&#34;&#34;
        Returns the instance type of this cloud apps instance

        Returns:
            (str): Instance Type
        &#34;&#34;&#34;
        return &#39;SALESFORCE&#39;

    @property
    def organization_id(self):
        &#34;&#34;&#34;
        Returns the Salesforce organization id

        Returns:
            (str): Organization Id

        Raises:
            SDKException: if attribute could not be fetched
        &#34;&#34;&#34;
        try:
            return self._properties[&#39;cloudAppsInstance&#39;][&#39;salesforceInstance&#39;][&#39;sfOrgID&#39;]
        except KeyError:
            raise SDKException(&#39;Instance&#39;, &#39;105&#39;, &#39;Could not fetch organization ID&#39;)

    @property
    def login_url(self):
        &#34;&#34;&#34;
        Returns the login url of Salesforce organization

        Returns:
            (str): Login URL

        Raises:
            SDKException: if attribute could not be fetched
        &#34;&#34;&#34;
        try:
            return self._properties[&#39;cloudAppsInstance&#39;][&#39;salesforceInstance&#39;][&#39;endpoint&#39;]
        except KeyError:
            raise SDKException(&#39;Instance&#39;, &#39;105&#39;, &#39;Could not fetch login url&#39;)

    @property
    def consumer_id(self):
        &#34;&#34;&#34;
        Returns the Consumer Id of the Salesforce connected app used to authenticate with Salesforce by this instance

        Returns:
            (str): Consumer Id

        Raises:
            SDKException: if attribute could not be fetched
        &#34;&#34;&#34;
        try:
            return self._properties[&#39;cloudAppsInstance&#39;][&#39;salesforceInstance&#39;][&#39;consumerId&#39;]
        except KeyError:
            raise SDKException(&#39;Instance&#39;, &#39;105&#39;, &#39;Could not fetch login url&#39;)

    @property
    def proxy_client(self):
        &#34;&#34;&#34;
        Returns the name of the access node.

        Returns:
            (str): Access Node

        Raises:
            SDKException:
                if attribute could not be fetched

                if access node is a client group
        &#34;&#34;&#34;
        try:
            general_cloud_properties = self._properties[&#39;cloudAppsInstance&#39;][&#39;generalCloudProperties&#39;]
            if &#39;clientName&#39; in general_cloud_properties[&#39;proxyServers&#39;][0].keys():
                return general_cloud_properties[&#39;proxyServers&#39;][0][&#39;clientName&#39;]
            if &#39;clientName&#39; in general_cloud_properties[&#39;accessNodes&#39;][&#39;memberServers&#39;][0][&#39;client&#39;].keys():
                return general_cloud_properties[&#39;accessNodes&#39;][&#39;memberServers&#39;][0][&#39;client&#39;][&#39;clientName&#39;]
            if &#39;clientGroupName&#39; in general_cloud_properties[&#39;accessNodes&#39;][&#39;memberServers&#39;][0][&#39;client&#39;].keys():
                raise SDKException(
                    &#39;Instance&#39;,
                    &#39;102&#39;,
                    &#39;This instance uses a client group as access node. Use access_node attribute instead.&#39;
                )
        except KeyError:
            raise SDKException(&#39;Instance&#39;, &#39;105&#39;, &#39;Could not fetch proxy client&#39;)

    @property
    def access_node(self):
        &#34;&#34;&#34;
        Returns a dictionary containing clientName and clientId or clientGroupName and clientGroupId depending on
        whether a single client or a client group is configured as access node.

        Returns:
            (dict): Dictionary containing access node name and id

        Raises:
            SDKException: if attribute could not be fetched
        &#34;&#34;&#34;
        try:
            access_node = self._properties[&#39;cloudAppsInstance&#39;][&#39;generalCloudProperties&#39;][&#39;accessNodes&#39;] \
                [&#39;memberServers&#39;][0][&#39;client&#39;].copy()
            if &#39;entityInfo&#39; in access_node:
                del access_node[&#39;entityInfo&#39;]
            return access_node
        except KeyError:
            raise SDKException(&#39;Instance&#39;, &#39;105&#39;, &#39;Could not fetch access node&#39;)

    def _restore_json(self, **kwargs):
        &#34;&#34;&#34;
        Returns the JSON request to pass to the API as per the options selected by the user

        Args:
            **kwargs (dict): Dict of named parameters to set for restore

        Returns:
            (dict): Request JSON
        &#34;&#34;&#34;
        if len(self.backupsets.all_backupsets) &gt; 1 or len(self.subclients.all_subclients) &gt; 1:
            raise SDKException(
                &#39;Instance&#39;,
                &#39;102&#39;,
                &#39;More than one backupset/subclient configured in this instance. Run restore from subclient&#39;
            )

        if not kwargs.get(&#39;no_of_streams&#39;, None):
            kwargs[&#39;no_of_streams&#39;] = 2
        kwargs[&#39;client&#39;] = kwargs.get(&#39;client&#39;, None) or self._agent_object._client_object
        request_json = super()._restore_json(**kwargs)

        backupset = self.backupsets.get(list(self.backupsets.all_backupsets.keys())[0])
        subclient = list(self.subclients.all_subclients.items())[0]
        request_json[&#39;taskInfo&#39;][&#39;associations&#39;][0].update({
            &#39;backupsetName&#39;: backupset.name,
            &#39;backupsetId&#39;: int(backupset.backupset_id),
            &#39;subclientName&#39;: subclient[0],
            &#39;subclientId&#39;: int(subclient[1][&#39;id&#39;])
        })

        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;cloudAppsRestoreOptions&#39;] = {
            &#39;instanceType&#39;: self.ca_instance_type,
            &#39;salesforceRestoreOptions&#39;: {
                &#39;restoreToFileSystem&#39;: kwargs.get(&#39;restore_to_file_system&#39;, False),
                &#39;restoreToSalesforce&#39;: kwargs.get(&#39;restore_to_salesforce&#39;, False),
                &#39;restoreFromDatabase&#39;: kwargs.get(&#39;restore_from_database&#39;, False),
                &#39;isMetadataRestore&#39;: kwargs.get(&#39;is_metadata_restore&#39;, False),
                &#39;pathToStoreCsv&#39;: kwargs.get(&#39;path_to_store_csv&#39;, None) or backupset.download_cache_path,
                &#39;dependentRestoreLevel&#39;: kwargs.get(&#39;dependent_restore_level&#39;, 0),
                &#39;restoreParentType&#39;: kwargs.get(&#39;restore_parent_type&#39;, &#39;NONE&#39;),
                &#39;isSaaSRestore&#39;: False
            }
        }

        if &#39;restore_to_salesforce&#39; in kwargs and kwargs[&#39;restore_to_salesforce&#39;]:
            if kwargs.get(&#39;instance&#39;, None) and kwargs.get(&#39;backupset&#39;, None):
                destination_client = self._commcell_object.clients.get(kwargs[&#39;client&#39;])
                destination_instance = destination_client.agents.get(&#39;Cloud Apps&#39;).instances.get(kwargs[&#39;instance&#39;])
                destination_backupset = destination_instance.backupsets.get(kwargs[&#39;backupset&#39;])
            else:
                destination_instance = self
                destination_backupset = backupset
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;destination&#39;].update({
                &#39;destinationInstance&#39;: {
                    &#39;instanceId&#39;: int(destination_instance.instance_id),
                    &#39;instanceName&#39;: destination_instance.name,
                },
                &#39;destinationBackupset&#39;: {
                    &#39;backupsetId&#39;: int(destination_backupset.backupset_id),
                    &#39;backupsetName&#39;: destination_backupset.backupset_name
                }
            })

        if kwargs.get(&#39;db_enabled&#39;, False):
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;cloudAppsRestoreOptions&#39;] \
                [&#39;salesforceRestoreOptions&#39;].update({
                &#39;syncDatabase&#39;: {
                    &#39;dbEnabled&#39;: True,
                    &#39;dbType&#39;: kwargs[&#39;db_type&#39;],
                    &#39;dbHost&#39;: kwargs[&#39;db_host&#39;],
                    &#39;dbInstance&#39;: kwargs.get(&#39;db_instance&#39;, &#39;&#39;),
                    &#39;dbPort&#39;: str(kwargs.get(&#39;db_port&#39;, 5432 if kwargs[&#39;db_type&#39;] == &#39;POSTGRESQL&#39; else 1433)),
                    &#39;dbName&#39;: kwargs[&#39;db_name&#39;],
                    &#39;dbUserPassword&#39;: {
                        &#39;userName&#39;: kwargs[&#39;db_user_name&#39;],
                        &#39;password&#39;: b64encode(kwargs[&#39;db_password&#39;].encode()).decode()
                    }
                },
                &#39;overrideTable&#39;: kwargs.get(&#39;override_table&#39;, True),
                &#39;restoreCatalogDatabase&#39;: kwargs.get(&#39;restore_catalog_database&#39;, False)
            })

        return request_json

    def restore_to_file_system(self, **kwargs):
        &#34;&#34;&#34;
        Runs object level restore to file system and returns object of Job or Schedule class. For out of place restore,
        pass both client and path_to_store_csv parameters. By default, will restore to access node and download cache
        path.

        Args:
            **kwargs (dict): Restore options including
                {
                    paths (list[str]): List of files and objects to restore like
                                [&#39;/Files/filename&#39;, &#39;/Objects/object_name&#39;]
                                (Default is [&#39;/Files/&#39;, &#39;/Objects/&#39;] which selects all files and objects for restore),

                    client (str): Name of destination client (Default is access node),

                    path_to_store_csv (str): path on destination client to restore to (Default is download cache path),

                    from_time (str): time to restore contents after like YYYY-MM-DD HH:MM:SS (Default is None),

                    to_time (str): time to restore contents before like YYYY-MM-DD HH:MM:SS (Default is None),

                    no_of_streams (int): Number of streams to use for restore (Default is 2),

                    dependent_restore_level (int): restore children option (Default is 0)
                                                    0  -- No Children
                                                    1  -- Immediate Children
                                                    -1 -- All Children,

                    restore_parent_type (str): restore parents option (Default is &#39;NONE&#39;)
                                                    &#39;NONE&#39; -- No Parents
                                                    &#39;ALL&#39;  -- All Parents
                }

        Returns:
            object: Object of Job or Schedule class

        Raises:
            SDKException:
                if paths is given but is not a list
    
                if client parameter is not given and the access node configured with this instance is a client group
                
                if either client or path_to_store_csv is given but not both are present
                
                if client or path_to_store_csv are not strings
        &#34;&#34;&#34;
        PARAMS = (&#39;client&#39;, &#39;path_to_store_csv&#39;)

        if not isinstance(kwargs.get(&#39;paths&#39;, list()), list):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if any(param in kwargs for param in PARAMS) and \
                not all(isinstance(kwargs.get(param, None), str) for param in PARAMS):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if not &#39;paths&#39; in kwargs:
            kwargs[&#39;paths&#39;] = [&#39;/Files/&#39;, &#39;/Objects/&#39;]

        request_json = self._restore_json(
            client=kwargs.get(&#39;client&#39;, self.proxy_client),
            restore_to_file_system=True,
            **kwargs
        )
        
        return self._process_restore_response(request_json)

    def restore_to_database(
            self,
            db_type,
            db_host_name,
            db_name,
            db_user_name,
            db_password,
            **kwargs
        ):
        &#34;&#34;&#34;
        Runs object level restore to database and returns object of Job or Schedule class

        Args:
            db_type (str): Type of database out of &#39;POSTGRESQL&#39; or &#39;SQLSERVER&#39;

            db_host_name (str): Hostname of database server

            db_name (str): Database name where objects will be restored

            db_user_name (str): Username of database user

            db_password (str): Password of database user

            **kwargs (dict): Other restore options including
                {
                    paths (list[str]): List of files and objects to restore like
                                [&#39;/Files/filename&#39;, &#39;/Objects/object_name&#39;]
                                (Default is [&#39;/Objects/&#39;] which selects all objects for restore),

                    db_instance (str): Database instance for SQL Server,

                    db_port (int): Port of database server (Default is 5432 for POSTGRESQL and 1433 for SQLSERVER),

                    from_time (str): time to restore contents after like YYYY-MM-DD HH:MM:SS (Default is None),

                    to_time (str): time to restore contents before like YYYY-MM-DD HH:MM:SS (Default is None),

                    no_of_streams (int): Number of streams to use for restore (Default is 2),

                    path_to_store_csv (str): path to use as staging folder (Default is download cache path),

                    dependent_restore_level (int): restore children option (Default is 0)
                                                    0  -- No Children
                                                    1  -- Immediate Children
                                                    -1 -- All Children,

                    restore_parent_type (str): restore parents option (Default is &#39;NONE&#39;)
                                                    &#39;NONE&#39; -- No Parents
                                                    &#39;ALL&#39;  -- All Parents
                }

        Returns:
            object: Object of Job or Schedule class

        Raises:
            SDKException:
                if required parameters are not of the correct type

                if db_type is &#39;SQLSERVER&#39; but db_instance is not given/ is not a string

                if paths is given but is not a list
        &#34;&#34;&#34;
        PARAMS = (db_type, db_host_name,  db_name, db_user_name, db_password)

        if not isinstance(kwargs.get(&#39;paths&#39;, list()), list):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if not all(isinstance(val, str) for val in PARAMS) and \
                (isinstance(kwargs.get(&#39;db_instance&#39;, None), str) != (db_type == &#39;SQLSERVER&#39;)):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if not &#39;paths&#39; in kwargs:
            kwargs[&#39;paths&#39;] = [&#39;/Objects/&#39;]

        request_json = self._restore_json(
            db_enabled=True,
            db_type=db_type,
            db_host=db_host_name,
            db_name=db_name,
            db_user_name=db_user_name,
            db_password=db_password,
            **kwargs
        )

        return self._process_restore_response(request_json)

    def restore_to_salesforce_from_database(self, **kwargs):
        &#34;&#34;&#34;
        Runs restore to Salesforce from database and returns object of Job or Schedule class. For out of place restore,
        pass the client, instance and backupset parameters. If database parameters are not passed, sync db will be used.

        Args:
            **kwargs (dict): Other restore options including
                {
                    paths (list[str]): List of files and objects to restore like
                                [&#39;/Files/filename&#39;, &#39;/Objects/object_name&#39;]
                                (Default is [&#39;/Files/&#39;, &#39;/Objects/&#39;] which selects all files and objects for restore),

                    client (str): Name of destination client (Default is source client),

                    instance (str): Name of destination instance (Default is source instance),

                    backupset (str): Name of destination backupset (Default is source backupset),

                    db_type (str): Type of database out of &#39;POSTGRESQL&#39; or &#39;SQLSERVER&#39;,

                    db_host (str): Hostname of database server,

                    db_name (str): Database name where objects will be restored,

                    db_user_name (str): Username of database user,

                    db_password (str): Password of database user,

                    db_instance (str): Database instance for SQL Server,

                    db_port (int): Port of database server (Default is 5432 for POSTGRESQL and 1433 for SQLSERVER),

                    from_time (str): time to restore contents after like YYYY-MM-DD HH:MM:SS (Default is None),

                    to_time (str): time to restore contents before like YYYY-MM-DD HH:MM:SS (Default is None),

                    no_of_streams (int): Number of streams to use for restore (Default is 2),

                    path_to_store_csv (str): path to use as staging folder (Default is download cache path),

                    dependent_restore_level (int): restore children option (Default is 0)
                                                    0  -- No Children
                                                    1  -- Immediate Children
                                                    -1 -- All Children,

                    restore_parent_type (str): restore parents option (Default is &#39;NONE&#39;)
                                                    &#39;NONE&#39; -- No Parents
                                                    &#39;ALL&#39;  -- All Parents
                }

        Returns:
            object: Object of Job or Schedule class

        Raises:
            SDKException:
                if paths is given but is not a list

                if any database parameters are given but not all are present

                if database parameters are not all strings

                if db_type is &#39;SQLSERVER&#39; but db_instance is not given/ is not a string

                if either client, instance or backupset are given but not all three are present

                if client, instance and backupset are not strings
        &#34;&#34;&#34;
        DB_PARAMS = (&#39;db_type&#39;, &#39;db_host&#39;, &#39;db_name&#39;, &#39;db_user_name&#39;, &#39;db_password&#39;)
        DEST_PARAMS = (&#39;client&#39;, &#39;instance&#39;, &#39;backupset&#39;)

        if not isinstance(kwargs.get(&#39;paths&#39;, list()), list):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if any(param in kwargs for param in DEST_PARAMS) and \
                not all(isinstance(kwargs.get(param, None), str) for param in DEST_PARAMS):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if any(param in kwargs for param in DB_PARAMS):
            if not all(isinstance(kwargs.get(param, None), str) for param in DB_PARAMS) and \
                    (isinstance(kwargs.get(&#39;db_instance&#39;, None), str) != (kwargs[&#39;db_type&#39;] == &#39;SQLSERVER&#39;)):
                raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
            kwargs[&#39;db_enabled&#39;] = True

        if not &#39;paths&#39; in kwargs:
            kwargs[&#39;paths&#39;] = [&#39;/Files/&#39;, &#39;/Objects/&#39;]

        request_json = self._restore_json(
            restore_to_salesforce=True,
            restore_from_database=True,
            **kwargs
        )

        return self._process_restore_response(request_json)

    def restore_to_salesforce_from_media(self, **kwargs):
        &#34;&#34;&#34;
        Runs restore to Salesforce from database and returns object of Job or Schedule class. For out of place restore,
        pass the client, instance and backupset parameters. If database parameters are not passed, sync db will be used
        as staging db.

        Args:
            **kwargs (dict): Other restore options including
                {
                    paths (list[str]): List of files and objects to restore like
                                [&#39;/Files/filename&#39;, &#39;/Objects/object_name&#39;]
                                (Default is [&#39;/Files/&#39;, &#39;/Objects/&#39;] which selects all files and objects for restore),

                    client (str): Name of destination client (Default is source client),

                    instance (str): Name of destination instance (Default is source instance),

                    backupset (str): Name of destination backupset (Default is source backupset),

                    db_type (str): Type of database out of &#39;POSTGRESQL&#39; or &#39;SQLSERVER&#39;,

                    db_host (str): Hostname of database server,

                    db_name (str): Database name where objects will be restored,

                    db_user_name (str): Username of database user,

                    db_password (str): Password of database user,

                    db_instance (str): Database instance for SQL Server,

                    db_port (int): Port of database server (Default is 5432 for POSTGRESQL and 1433 for SQLSERVER),

                    from_time (str): time to restore contents after like YYYY-MM-DD HH:MM:SS (Default is None),

                    to_time (str): time to restore contents before like YYYY-MM-DD HH:MM:SS (Default is None),

                    no_of_streams (int): Number of streams to use for restore (Default is 2),

                    path_to_store_csv (str): path to use as staging folder (Default is download cache path),

                    dependent_restore_level (int): restore children option (Default is 0)
                                                    0  -- No Children
                                                    1  -- Immediate Children
                                                    -1 -- All Children,

                    restore_parent_type (str): restore parents option (Default is &#39;NONE&#39;)
                                                    &#39;NONE&#39; -- No Parents
                                                    &#39;ALL&#39;  -- All Parents
                }

        Returns:
            object: Object of Job or Schedule class

        Raises:
            SDKException:
                if paths is given but is not a list

                if any database parameters are given but not all are present

                if database parameters are not all strings

                if db_type is &#39;SQLSERVER&#39; but db_instance is not given/ is not a string

                if either client, instance or backupset are given but not all three are present

                if client, instance and backupset are not strings
        &#34;&#34;&#34;
        DB_PARAMS = (&#39;db_type&#39;, &#39;db_host&#39;, &#39;db_name&#39;, &#39;db_user_name&#39;, &#39;db_password&#39;)
        DEST_PARAMS = (&#39;client&#39;, &#39;instance&#39;, &#39;backupset&#39;)

        if not isinstance(kwargs.get(&#39;paths&#39;, list()), list):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if any(param in kwargs for param in DEST_PARAMS) and \
                not all(isinstance(kwargs.get(param, None), str) for param in DEST_PARAMS):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if any(keyword in kwargs for keyword in DB_PARAMS):
            if not all(isinstance(kwargs.get(param, None), str) for param in DB_PARAMS) and \
                    (isinstance(kwargs.get(&#39;db_instance&#39;, None), str) != (kwargs[&#39;db_type&#39;] == &#39;SQLSERVER&#39;)):
                raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
            kwargs[&#39;db_enabled&#39;] = True

        if not &#39;paths&#39; in kwargs:
            kwargs[&#39;paths&#39;] = [&#39;/Files/&#39;, &#39;/Objects/&#39;]

        request_json = self._restore_json(
            restore_to_salesforce=True,
            **kwargs
        )

        return self._process_restore_response(request_json)

    def metadata_restore_to_file_system(self, **kwargs):
        &#34;&#34;&#34;
        Runs metadata restore to file system and returns object of Job or Schedule class. For out of place restore,
        pass both client and path_to_store_csv parameters. By default, will restore to access node and download cache
        path.

        Args:
            **kwargs (dict): Other restore options including
                {                    
                    paths (list[str]): List of metadata components to restore like
                                [&#39;/Metadata/unpackaged/objects/Account.object&#39;,
                                 &#39;/Metadata/unpackaged/profiles/Admin.profile&#39;]
                                (Default is [&#39;/Metadata/unpackaged/&#39;] which selects all metdata components for restore),
                                
                    client (str): Name of destination client (Default is access node),
                    
                    path_to_store_csv (str): path on destination client to restore to (Default is download cache path),
                    
                    from_time (str): time to restore contents after like YYYY-MM-DD HH:MM:SS (Default is None),
                    
                    to_time (str): time to restore contents before like YYYY-MM-DD HH:MM:SS (Default is None),
                    
                    no_of_streams (int): Number of streams to use for restore (Default is 2),
                    
                    dependent_restore_level (int): restore children option (Default is 0)
                                                    0  -- No Children
                                                    1  -- Immediate Children
                                                    -1 -- All Children,
                                                    
                    restore_parent_type (str): restore parents option (Default is &#39;NONE&#39;)
                                                    &#39;NONE&#39; -- No Parents
                                                    &#39;ALL&#39;  -- All Parents
                }

        Returns:
            object: Object of Job or Schedule class

        Raises:
            SDKException:
                if paths is given but is not a list
    
                if client parameter is not given and the access node configured with this instance is a client group
                
                if either client or path_to_store_csv is given but not both are present
                
                if client or path_to_store_csv are not strings
        &#34;&#34;&#34;
        PARAMS = (&#39;client&#39;, &#39;path_to_store_csv&#39;)

        if not isinstance(kwargs.get(&#39;paths&#39;, list()), list):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if any(param in kwargs for param in PARAMS) and \
                not all(isinstance(kwargs.get(param, None), str) for param in PARAMS):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if not &#39;paths&#39; in kwargs:
            kwargs[&#39;paths&#39;] = [&#39;/Metadata/unpackaged/&#39;]
        
        request_json = self._restore_json(
            restore_to_file_system=True,
            is_metadata_restore=True,
            **kwargs
        )
        return self._process_restore_response(request_json)

    def metadata_restore_to_salesforce(self, **kwargs):
        &#34;&#34;&#34;
        Runs metadata restore to Salesforce and returns object of Job or Schedule class. For out of place restore,
        pass client, instance and backupset parameters.

        Args:
            **kwargs (dict): Other restore options including
                {
                    paths (list[str]): List of metadata components to restore like
                                [&#39;/Metadata/unpackaged/objects/Account.object&#39;,
                                 &#39;/Metadata/unpackaged/profiles/Admin.profile&#39;]
                                (Default is [&#39;/Metadata/unpackaged/&#39;] which selects all metdata components for restore),
                                
                    client (str): Name of destination client (Default is source client),
                    
                    instance (str): Name of destination instance (Default is source instance),
                    
                    backupset (str): Name of destination backupset (Default is source backupset),
                    
                    from_time (str): time to restore contents after like YYYY-MM-DD HH:MM:SS (Default is None),
                    
                    to_time (str): time to restore contents before like YYYY-MM-DD HH:MM:SS (Default is None),
                    
                    no_of_streams (int): Number of streams to use for restore (Default is 2),
                    
                    path_to_store_csv (str): path to use as staging folder (Default is download cache path),
                    
                    dependent_restore_level (int): restore children option (Default is 0)
                                                    0  -- No Children
                                                    1  -- Immediate Children
                                                    -1 -- All Children,
                                                    
                    restore_parent_type (str): restore parents option (Default is &#39;NONE&#39;)
                                                    &#39;NONE&#39; -- No Parents
                                                    &#39;ALL&#39;  -- All Parents
                }

        Returns:
            object: Object of Job or Schedule class

        Raises:
            SDKException:
                if paths is given but is not a list
                
                if either client, instance or backupset are given but not all three are present

                if client, instance and backupset are not strings
        &#34;&#34;&#34;
        DEST_PARAMS = (&#39;client&#39;, &#39;instance&#39;, &#39;backupset&#39;)

        if not isinstance(kwargs.get(&#39;paths&#39;, list()), list):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if any(param in kwargs for param in DEST_PARAMS) and \
                not all(isinstance(kwargs.get(param, None), str) for param in DEST_PARAMS):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if not &#39;paths&#39; in kwargs:
            kwargs[&#39;paths&#39;] = [&#39;/Metadata/unpackaged/&#39;]
        
        request_json = self._restore_json(
            restore_to_salesforce=True,
            is_metadata_restore=True,
            **kwargs
        )
        
        return self._process_restore_response(request_json)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.instances.cloudapps.salesforce_instance.SalesforceInstance"><code class="flex name class">
<span>class <span class="ident">SalesforceInstance</span></span>
<span>(</span><span>agent_object, instance_name, instance_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing an Instance of the Salesforce instance type.</p>
<p>Initialise the instance object.</p>
<h2 id="args">Args</h2>
<p>agent_object
(object)
&ndash;
instance of the Agent class</p>
<p>instance_name
(str)
&ndash;
name of the instance</p>
<p>instance_id
(str)
&ndash;
id of the instance
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Instance class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/salesforce_instance.py#L70-L738" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class SalesforceInstance(CloudAppsInstance):
    &#34;&#34;&#34;Class for representing an Instance of the Salesforce instance type.&#34;&#34;&#34;

    @property
    def ca_instance_type(self):
        &#34;&#34;&#34;
        Returns the instance type of this cloud apps instance

        Returns:
            (str): Instance Type
        &#34;&#34;&#34;
        return &#39;SALESFORCE&#39;

    @property
    def organization_id(self):
        &#34;&#34;&#34;
        Returns the Salesforce organization id

        Returns:
            (str): Organization Id

        Raises:
            SDKException: if attribute could not be fetched
        &#34;&#34;&#34;
        try:
            return self._properties[&#39;cloudAppsInstance&#39;][&#39;salesforceInstance&#39;][&#39;sfOrgID&#39;]
        except KeyError:
            raise SDKException(&#39;Instance&#39;, &#39;105&#39;, &#39;Could not fetch organization ID&#39;)

    @property
    def login_url(self):
        &#34;&#34;&#34;
        Returns the login url of Salesforce organization

        Returns:
            (str): Login URL

        Raises:
            SDKException: if attribute could not be fetched
        &#34;&#34;&#34;
        try:
            return self._properties[&#39;cloudAppsInstance&#39;][&#39;salesforceInstance&#39;][&#39;endpoint&#39;]
        except KeyError:
            raise SDKException(&#39;Instance&#39;, &#39;105&#39;, &#39;Could not fetch login url&#39;)

    @property
    def consumer_id(self):
        &#34;&#34;&#34;
        Returns the Consumer Id of the Salesforce connected app used to authenticate with Salesforce by this instance

        Returns:
            (str): Consumer Id

        Raises:
            SDKException: if attribute could not be fetched
        &#34;&#34;&#34;
        try:
            return self._properties[&#39;cloudAppsInstance&#39;][&#39;salesforceInstance&#39;][&#39;consumerId&#39;]
        except KeyError:
            raise SDKException(&#39;Instance&#39;, &#39;105&#39;, &#39;Could not fetch login url&#39;)

    @property
    def proxy_client(self):
        &#34;&#34;&#34;
        Returns the name of the access node.

        Returns:
            (str): Access Node

        Raises:
            SDKException:
                if attribute could not be fetched

                if access node is a client group
        &#34;&#34;&#34;
        try:
            general_cloud_properties = self._properties[&#39;cloudAppsInstance&#39;][&#39;generalCloudProperties&#39;]
            if &#39;clientName&#39; in general_cloud_properties[&#39;proxyServers&#39;][0].keys():
                return general_cloud_properties[&#39;proxyServers&#39;][0][&#39;clientName&#39;]
            if &#39;clientName&#39; in general_cloud_properties[&#39;accessNodes&#39;][&#39;memberServers&#39;][0][&#39;client&#39;].keys():
                return general_cloud_properties[&#39;accessNodes&#39;][&#39;memberServers&#39;][0][&#39;client&#39;][&#39;clientName&#39;]
            if &#39;clientGroupName&#39; in general_cloud_properties[&#39;accessNodes&#39;][&#39;memberServers&#39;][0][&#39;client&#39;].keys():
                raise SDKException(
                    &#39;Instance&#39;,
                    &#39;102&#39;,
                    &#39;This instance uses a client group as access node. Use access_node attribute instead.&#39;
                )
        except KeyError:
            raise SDKException(&#39;Instance&#39;, &#39;105&#39;, &#39;Could not fetch proxy client&#39;)

    @property
    def access_node(self):
        &#34;&#34;&#34;
        Returns a dictionary containing clientName and clientId or clientGroupName and clientGroupId depending on
        whether a single client or a client group is configured as access node.

        Returns:
            (dict): Dictionary containing access node name and id

        Raises:
            SDKException: if attribute could not be fetched
        &#34;&#34;&#34;
        try:
            access_node = self._properties[&#39;cloudAppsInstance&#39;][&#39;generalCloudProperties&#39;][&#39;accessNodes&#39;] \
                [&#39;memberServers&#39;][0][&#39;client&#39;].copy()
            if &#39;entityInfo&#39; in access_node:
                del access_node[&#39;entityInfo&#39;]
            return access_node
        except KeyError:
            raise SDKException(&#39;Instance&#39;, &#39;105&#39;, &#39;Could not fetch access node&#39;)

    def _restore_json(self, **kwargs):
        &#34;&#34;&#34;
        Returns the JSON request to pass to the API as per the options selected by the user

        Args:
            **kwargs (dict): Dict of named parameters to set for restore

        Returns:
            (dict): Request JSON
        &#34;&#34;&#34;
        if len(self.backupsets.all_backupsets) &gt; 1 or len(self.subclients.all_subclients) &gt; 1:
            raise SDKException(
                &#39;Instance&#39;,
                &#39;102&#39;,
                &#39;More than one backupset/subclient configured in this instance. Run restore from subclient&#39;
            )

        if not kwargs.get(&#39;no_of_streams&#39;, None):
            kwargs[&#39;no_of_streams&#39;] = 2
        kwargs[&#39;client&#39;] = kwargs.get(&#39;client&#39;, None) or self._agent_object._client_object
        request_json = super()._restore_json(**kwargs)

        backupset = self.backupsets.get(list(self.backupsets.all_backupsets.keys())[0])
        subclient = list(self.subclients.all_subclients.items())[0]
        request_json[&#39;taskInfo&#39;][&#39;associations&#39;][0].update({
            &#39;backupsetName&#39;: backupset.name,
            &#39;backupsetId&#39;: int(backupset.backupset_id),
            &#39;subclientName&#39;: subclient[0],
            &#39;subclientId&#39;: int(subclient[1][&#39;id&#39;])
        })

        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;cloudAppsRestoreOptions&#39;] = {
            &#39;instanceType&#39;: self.ca_instance_type,
            &#39;salesforceRestoreOptions&#39;: {
                &#39;restoreToFileSystem&#39;: kwargs.get(&#39;restore_to_file_system&#39;, False),
                &#39;restoreToSalesforce&#39;: kwargs.get(&#39;restore_to_salesforce&#39;, False),
                &#39;restoreFromDatabase&#39;: kwargs.get(&#39;restore_from_database&#39;, False),
                &#39;isMetadataRestore&#39;: kwargs.get(&#39;is_metadata_restore&#39;, False),
                &#39;pathToStoreCsv&#39;: kwargs.get(&#39;path_to_store_csv&#39;, None) or backupset.download_cache_path,
                &#39;dependentRestoreLevel&#39;: kwargs.get(&#39;dependent_restore_level&#39;, 0),
                &#39;restoreParentType&#39;: kwargs.get(&#39;restore_parent_type&#39;, &#39;NONE&#39;),
                &#39;isSaaSRestore&#39;: False
            }
        }

        if &#39;restore_to_salesforce&#39; in kwargs and kwargs[&#39;restore_to_salesforce&#39;]:
            if kwargs.get(&#39;instance&#39;, None) and kwargs.get(&#39;backupset&#39;, None):
                destination_client = self._commcell_object.clients.get(kwargs[&#39;client&#39;])
                destination_instance = destination_client.agents.get(&#39;Cloud Apps&#39;).instances.get(kwargs[&#39;instance&#39;])
                destination_backupset = destination_instance.backupsets.get(kwargs[&#39;backupset&#39;])
            else:
                destination_instance = self
                destination_backupset = backupset
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;destination&#39;].update({
                &#39;destinationInstance&#39;: {
                    &#39;instanceId&#39;: int(destination_instance.instance_id),
                    &#39;instanceName&#39;: destination_instance.name,
                },
                &#39;destinationBackupset&#39;: {
                    &#39;backupsetId&#39;: int(destination_backupset.backupset_id),
                    &#39;backupsetName&#39;: destination_backupset.backupset_name
                }
            })

        if kwargs.get(&#39;db_enabled&#39;, False):
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;cloudAppsRestoreOptions&#39;] \
                [&#39;salesforceRestoreOptions&#39;].update({
                &#39;syncDatabase&#39;: {
                    &#39;dbEnabled&#39;: True,
                    &#39;dbType&#39;: kwargs[&#39;db_type&#39;],
                    &#39;dbHost&#39;: kwargs[&#39;db_host&#39;],
                    &#39;dbInstance&#39;: kwargs.get(&#39;db_instance&#39;, &#39;&#39;),
                    &#39;dbPort&#39;: str(kwargs.get(&#39;db_port&#39;, 5432 if kwargs[&#39;db_type&#39;] == &#39;POSTGRESQL&#39; else 1433)),
                    &#39;dbName&#39;: kwargs[&#39;db_name&#39;],
                    &#39;dbUserPassword&#39;: {
                        &#39;userName&#39;: kwargs[&#39;db_user_name&#39;],
                        &#39;password&#39;: b64encode(kwargs[&#39;db_password&#39;].encode()).decode()
                    }
                },
                &#39;overrideTable&#39;: kwargs.get(&#39;override_table&#39;, True),
                &#39;restoreCatalogDatabase&#39;: kwargs.get(&#39;restore_catalog_database&#39;, False)
            })

        return request_json

    def restore_to_file_system(self, **kwargs):
        &#34;&#34;&#34;
        Runs object level restore to file system and returns object of Job or Schedule class. For out of place restore,
        pass both client and path_to_store_csv parameters. By default, will restore to access node and download cache
        path.

        Args:
            **kwargs (dict): Restore options including
                {
                    paths (list[str]): List of files and objects to restore like
                                [&#39;/Files/filename&#39;, &#39;/Objects/object_name&#39;]
                                (Default is [&#39;/Files/&#39;, &#39;/Objects/&#39;] which selects all files and objects for restore),

                    client (str): Name of destination client (Default is access node),

                    path_to_store_csv (str): path on destination client to restore to (Default is download cache path),

                    from_time (str): time to restore contents after like YYYY-MM-DD HH:MM:SS (Default is None),

                    to_time (str): time to restore contents before like YYYY-MM-DD HH:MM:SS (Default is None),

                    no_of_streams (int): Number of streams to use for restore (Default is 2),

                    dependent_restore_level (int): restore children option (Default is 0)
                                                    0  -- No Children
                                                    1  -- Immediate Children
                                                    -1 -- All Children,

                    restore_parent_type (str): restore parents option (Default is &#39;NONE&#39;)
                                                    &#39;NONE&#39; -- No Parents
                                                    &#39;ALL&#39;  -- All Parents
                }

        Returns:
            object: Object of Job or Schedule class

        Raises:
            SDKException:
                if paths is given but is not a list
    
                if client parameter is not given and the access node configured with this instance is a client group
                
                if either client or path_to_store_csv is given but not both are present
                
                if client or path_to_store_csv are not strings
        &#34;&#34;&#34;
        PARAMS = (&#39;client&#39;, &#39;path_to_store_csv&#39;)

        if not isinstance(kwargs.get(&#39;paths&#39;, list()), list):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if any(param in kwargs for param in PARAMS) and \
                not all(isinstance(kwargs.get(param, None), str) for param in PARAMS):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if not &#39;paths&#39; in kwargs:
            kwargs[&#39;paths&#39;] = [&#39;/Files/&#39;, &#39;/Objects/&#39;]

        request_json = self._restore_json(
            client=kwargs.get(&#39;client&#39;, self.proxy_client),
            restore_to_file_system=True,
            **kwargs
        )
        
        return self._process_restore_response(request_json)

    def restore_to_database(
            self,
            db_type,
            db_host_name,
            db_name,
            db_user_name,
            db_password,
            **kwargs
        ):
        &#34;&#34;&#34;
        Runs object level restore to database and returns object of Job or Schedule class

        Args:
            db_type (str): Type of database out of &#39;POSTGRESQL&#39; or &#39;SQLSERVER&#39;

            db_host_name (str): Hostname of database server

            db_name (str): Database name where objects will be restored

            db_user_name (str): Username of database user

            db_password (str): Password of database user

            **kwargs (dict): Other restore options including
                {
                    paths (list[str]): List of files and objects to restore like
                                [&#39;/Files/filename&#39;, &#39;/Objects/object_name&#39;]
                                (Default is [&#39;/Objects/&#39;] which selects all objects for restore),

                    db_instance (str): Database instance for SQL Server,

                    db_port (int): Port of database server (Default is 5432 for POSTGRESQL and 1433 for SQLSERVER),

                    from_time (str): time to restore contents after like YYYY-MM-DD HH:MM:SS (Default is None),

                    to_time (str): time to restore contents before like YYYY-MM-DD HH:MM:SS (Default is None),

                    no_of_streams (int): Number of streams to use for restore (Default is 2),

                    path_to_store_csv (str): path to use as staging folder (Default is download cache path),

                    dependent_restore_level (int): restore children option (Default is 0)
                                                    0  -- No Children
                                                    1  -- Immediate Children
                                                    -1 -- All Children,

                    restore_parent_type (str): restore parents option (Default is &#39;NONE&#39;)
                                                    &#39;NONE&#39; -- No Parents
                                                    &#39;ALL&#39;  -- All Parents
                }

        Returns:
            object: Object of Job or Schedule class

        Raises:
            SDKException:
                if required parameters are not of the correct type

                if db_type is &#39;SQLSERVER&#39; but db_instance is not given/ is not a string

                if paths is given but is not a list
        &#34;&#34;&#34;
        PARAMS = (db_type, db_host_name,  db_name, db_user_name, db_password)

        if not isinstance(kwargs.get(&#39;paths&#39;, list()), list):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if not all(isinstance(val, str) for val in PARAMS) and \
                (isinstance(kwargs.get(&#39;db_instance&#39;, None), str) != (db_type == &#39;SQLSERVER&#39;)):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if not &#39;paths&#39; in kwargs:
            kwargs[&#39;paths&#39;] = [&#39;/Objects/&#39;]

        request_json = self._restore_json(
            db_enabled=True,
            db_type=db_type,
            db_host=db_host_name,
            db_name=db_name,
            db_user_name=db_user_name,
            db_password=db_password,
            **kwargs
        )

        return self._process_restore_response(request_json)

    def restore_to_salesforce_from_database(self, **kwargs):
        &#34;&#34;&#34;
        Runs restore to Salesforce from database and returns object of Job or Schedule class. For out of place restore,
        pass the client, instance and backupset parameters. If database parameters are not passed, sync db will be used.

        Args:
            **kwargs (dict): Other restore options including
                {
                    paths (list[str]): List of files and objects to restore like
                                [&#39;/Files/filename&#39;, &#39;/Objects/object_name&#39;]
                                (Default is [&#39;/Files/&#39;, &#39;/Objects/&#39;] which selects all files and objects for restore),

                    client (str): Name of destination client (Default is source client),

                    instance (str): Name of destination instance (Default is source instance),

                    backupset (str): Name of destination backupset (Default is source backupset),

                    db_type (str): Type of database out of &#39;POSTGRESQL&#39; or &#39;SQLSERVER&#39;,

                    db_host (str): Hostname of database server,

                    db_name (str): Database name where objects will be restored,

                    db_user_name (str): Username of database user,

                    db_password (str): Password of database user,

                    db_instance (str): Database instance for SQL Server,

                    db_port (int): Port of database server (Default is 5432 for POSTGRESQL and 1433 for SQLSERVER),

                    from_time (str): time to restore contents after like YYYY-MM-DD HH:MM:SS (Default is None),

                    to_time (str): time to restore contents before like YYYY-MM-DD HH:MM:SS (Default is None),

                    no_of_streams (int): Number of streams to use for restore (Default is 2),

                    path_to_store_csv (str): path to use as staging folder (Default is download cache path),

                    dependent_restore_level (int): restore children option (Default is 0)
                                                    0  -- No Children
                                                    1  -- Immediate Children
                                                    -1 -- All Children,

                    restore_parent_type (str): restore parents option (Default is &#39;NONE&#39;)
                                                    &#39;NONE&#39; -- No Parents
                                                    &#39;ALL&#39;  -- All Parents
                }

        Returns:
            object: Object of Job or Schedule class

        Raises:
            SDKException:
                if paths is given but is not a list

                if any database parameters are given but not all are present

                if database parameters are not all strings

                if db_type is &#39;SQLSERVER&#39; but db_instance is not given/ is not a string

                if either client, instance or backupset are given but not all three are present

                if client, instance and backupset are not strings
        &#34;&#34;&#34;
        DB_PARAMS = (&#39;db_type&#39;, &#39;db_host&#39;, &#39;db_name&#39;, &#39;db_user_name&#39;, &#39;db_password&#39;)
        DEST_PARAMS = (&#39;client&#39;, &#39;instance&#39;, &#39;backupset&#39;)

        if not isinstance(kwargs.get(&#39;paths&#39;, list()), list):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if any(param in kwargs for param in DEST_PARAMS) and \
                not all(isinstance(kwargs.get(param, None), str) for param in DEST_PARAMS):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if any(param in kwargs for param in DB_PARAMS):
            if not all(isinstance(kwargs.get(param, None), str) for param in DB_PARAMS) and \
                    (isinstance(kwargs.get(&#39;db_instance&#39;, None), str) != (kwargs[&#39;db_type&#39;] == &#39;SQLSERVER&#39;)):
                raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
            kwargs[&#39;db_enabled&#39;] = True

        if not &#39;paths&#39; in kwargs:
            kwargs[&#39;paths&#39;] = [&#39;/Files/&#39;, &#39;/Objects/&#39;]

        request_json = self._restore_json(
            restore_to_salesforce=True,
            restore_from_database=True,
            **kwargs
        )

        return self._process_restore_response(request_json)

    def restore_to_salesforce_from_media(self, **kwargs):
        &#34;&#34;&#34;
        Runs restore to Salesforce from database and returns object of Job or Schedule class. For out of place restore,
        pass the client, instance and backupset parameters. If database parameters are not passed, sync db will be used
        as staging db.

        Args:
            **kwargs (dict): Other restore options including
                {
                    paths (list[str]): List of files and objects to restore like
                                [&#39;/Files/filename&#39;, &#39;/Objects/object_name&#39;]
                                (Default is [&#39;/Files/&#39;, &#39;/Objects/&#39;] which selects all files and objects for restore),

                    client (str): Name of destination client (Default is source client),

                    instance (str): Name of destination instance (Default is source instance),

                    backupset (str): Name of destination backupset (Default is source backupset),

                    db_type (str): Type of database out of &#39;POSTGRESQL&#39; or &#39;SQLSERVER&#39;,

                    db_host (str): Hostname of database server,

                    db_name (str): Database name where objects will be restored,

                    db_user_name (str): Username of database user,

                    db_password (str): Password of database user,

                    db_instance (str): Database instance for SQL Server,

                    db_port (int): Port of database server (Default is 5432 for POSTGRESQL and 1433 for SQLSERVER),

                    from_time (str): time to restore contents after like YYYY-MM-DD HH:MM:SS (Default is None),

                    to_time (str): time to restore contents before like YYYY-MM-DD HH:MM:SS (Default is None),

                    no_of_streams (int): Number of streams to use for restore (Default is 2),

                    path_to_store_csv (str): path to use as staging folder (Default is download cache path),

                    dependent_restore_level (int): restore children option (Default is 0)
                                                    0  -- No Children
                                                    1  -- Immediate Children
                                                    -1 -- All Children,

                    restore_parent_type (str): restore parents option (Default is &#39;NONE&#39;)
                                                    &#39;NONE&#39; -- No Parents
                                                    &#39;ALL&#39;  -- All Parents
                }

        Returns:
            object: Object of Job or Schedule class

        Raises:
            SDKException:
                if paths is given but is not a list

                if any database parameters are given but not all are present

                if database parameters are not all strings

                if db_type is &#39;SQLSERVER&#39; but db_instance is not given/ is not a string

                if either client, instance or backupset are given but not all three are present

                if client, instance and backupset are not strings
        &#34;&#34;&#34;
        DB_PARAMS = (&#39;db_type&#39;, &#39;db_host&#39;, &#39;db_name&#39;, &#39;db_user_name&#39;, &#39;db_password&#39;)
        DEST_PARAMS = (&#39;client&#39;, &#39;instance&#39;, &#39;backupset&#39;)

        if not isinstance(kwargs.get(&#39;paths&#39;, list()), list):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if any(param in kwargs for param in DEST_PARAMS) and \
                not all(isinstance(kwargs.get(param, None), str) for param in DEST_PARAMS):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if any(keyword in kwargs for keyword in DB_PARAMS):
            if not all(isinstance(kwargs.get(param, None), str) for param in DB_PARAMS) and \
                    (isinstance(kwargs.get(&#39;db_instance&#39;, None), str) != (kwargs[&#39;db_type&#39;] == &#39;SQLSERVER&#39;)):
                raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
            kwargs[&#39;db_enabled&#39;] = True

        if not &#39;paths&#39; in kwargs:
            kwargs[&#39;paths&#39;] = [&#39;/Files/&#39;, &#39;/Objects/&#39;]

        request_json = self._restore_json(
            restore_to_salesforce=True,
            **kwargs
        )

        return self._process_restore_response(request_json)

    def metadata_restore_to_file_system(self, **kwargs):
        &#34;&#34;&#34;
        Runs metadata restore to file system and returns object of Job or Schedule class. For out of place restore,
        pass both client and path_to_store_csv parameters. By default, will restore to access node and download cache
        path.

        Args:
            **kwargs (dict): Other restore options including
                {                    
                    paths (list[str]): List of metadata components to restore like
                                [&#39;/Metadata/unpackaged/objects/Account.object&#39;,
                                 &#39;/Metadata/unpackaged/profiles/Admin.profile&#39;]
                                (Default is [&#39;/Metadata/unpackaged/&#39;] which selects all metdata components for restore),
                                
                    client (str): Name of destination client (Default is access node),
                    
                    path_to_store_csv (str): path on destination client to restore to (Default is download cache path),
                    
                    from_time (str): time to restore contents after like YYYY-MM-DD HH:MM:SS (Default is None),
                    
                    to_time (str): time to restore contents before like YYYY-MM-DD HH:MM:SS (Default is None),
                    
                    no_of_streams (int): Number of streams to use for restore (Default is 2),
                    
                    dependent_restore_level (int): restore children option (Default is 0)
                                                    0  -- No Children
                                                    1  -- Immediate Children
                                                    -1 -- All Children,
                                                    
                    restore_parent_type (str): restore parents option (Default is &#39;NONE&#39;)
                                                    &#39;NONE&#39; -- No Parents
                                                    &#39;ALL&#39;  -- All Parents
                }

        Returns:
            object: Object of Job or Schedule class

        Raises:
            SDKException:
                if paths is given but is not a list
    
                if client parameter is not given and the access node configured with this instance is a client group
                
                if either client or path_to_store_csv is given but not both are present
                
                if client or path_to_store_csv are not strings
        &#34;&#34;&#34;
        PARAMS = (&#39;client&#39;, &#39;path_to_store_csv&#39;)

        if not isinstance(kwargs.get(&#39;paths&#39;, list()), list):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if any(param in kwargs for param in PARAMS) and \
                not all(isinstance(kwargs.get(param, None), str) for param in PARAMS):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if not &#39;paths&#39; in kwargs:
            kwargs[&#39;paths&#39;] = [&#39;/Metadata/unpackaged/&#39;]
        
        request_json = self._restore_json(
            restore_to_file_system=True,
            is_metadata_restore=True,
            **kwargs
        )
        return self._process_restore_response(request_json)

    def metadata_restore_to_salesforce(self, **kwargs):
        &#34;&#34;&#34;
        Runs metadata restore to Salesforce and returns object of Job or Schedule class. For out of place restore,
        pass client, instance and backupset parameters.

        Args:
            **kwargs (dict): Other restore options including
                {
                    paths (list[str]): List of metadata components to restore like
                                [&#39;/Metadata/unpackaged/objects/Account.object&#39;,
                                 &#39;/Metadata/unpackaged/profiles/Admin.profile&#39;]
                                (Default is [&#39;/Metadata/unpackaged/&#39;] which selects all metdata components for restore),
                                
                    client (str): Name of destination client (Default is source client),
                    
                    instance (str): Name of destination instance (Default is source instance),
                    
                    backupset (str): Name of destination backupset (Default is source backupset),
                    
                    from_time (str): time to restore contents after like YYYY-MM-DD HH:MM:SS (Default is None),
                    
                    to_time (str): time to restore contents before like YYYY-MM-DD HH:MM:SS (Default is None),
                    
                    no_of_streams (int): Number of streams to use for restore (Default is 2),
                    
                    path_to_store_csv (str): path to use as staging folder (Default is download cache path),
                    
                    dependent_restore_level (int): restore children option (Default is 0)
                                                    0  -- No Children
                                                    1  -- Immediate Children
                                                    -1 -- All Children,
                                                    
                    restore_parent_type (str): restore parents option (Default is &#39;NONE&#39;)
                                                    &#39;NONE&#39; -- No Parents
                                                    &#39;ALL&#39;  -- All Parents
                }

        Returns:
            object: Object of Job or Schedule class

        Raises:
            SDKException:
                if paths is given but is not a list
                
                if either client, instance or backupset are given but not all three are present

                if client, instance and backupset are not strings
        &#34;&#34;&#34;
        DEST_PARAMS = (&#39;client&#39;, &#39;instance&#39;, &#39;backupset&#39;)

        if not isinstance(kwargs.get(&#39;paths&#39;, list()), list):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if any(param in kwargs for param in DEST_PARAMS) and \
                not all(isinstance(kwargs.get(param, None), str) for param in DEST_PARAMS):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if not &#39;paths&#39; in kwargs:
            kwargs[&#39;paths&#39;] = [&#39;/Metadata/unpackaged/&#39;]
        
        request_json = self._restore_json(
            restore_to_salesforce=True,
            is_metadata_restore=True,
            **kwargs
        )
        
        return self._process_restore_response(request_json)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.instances.cainstance.CloudAppsInstance" href="../cainstance.html#cvpysdk.instances.cainstance.CloudAppsInstance">CloudAppsInstance</a></li>
<li><a title="cvpysdk.instance.Instance" href="../../instance.html#cvpysdk.instance.Instance">Instance</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.instances.cloudapps.salesforce_instance.SalesforceInstance.access_node"><code class="name">var <span class="ident">access_node</span></code></dt>
<dd>
<div class="desc"><p>Returns a dictionary containing clientName and clientId or clientGroupName and clientGroupId depending on
whether a single client or a client group is configured as access node.</p>
<h2 id="returns">Returns</h2>
<p>(dict): Dictionary containing access node name and id</p>
<h2 id="raises">Raises</h2>
<dl>
<dt><code>SDKException</code></dt>
<dd>if attribute could not be fetched</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/salesforce_instance.py#L160-L179" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def access_node(self):
    &#34;&#34;&#34;
    Returns a dictionary containing clientName and clientId or clientGroupName and clientGroupId depending on
    whether a single client or a client group is configured as access node.

    Returns:
        (dict): Dictionary containing access node name and id

    Raises:
        SDKException: if attribute could not be fetched
    &#34;&#34;&#34;
    try:
        access_node = self._properties[&#39;cloudAppsInstance&#39;][&#39;generalCloudProperties&#39;][&#39;accessNodes&#39;] \
            [&#39;memberServers&#39;][0][&#39;client&#39;].copy()
        if &#39;entityInfo&#39; in access_node:
            del access_node[&#39;entityInfo&#39;]
        return access_node
    except KeyError:
        raise SDKException(&#39;Instance&#39;, &#39;105&#39;, &#39;Could not fetch access node&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.cloudapps.salesforce_instance.SalesforceInstance.ca_instance_type"><code class="name">var <span class="ident">ca_instance_type</span></code></dt>
<dd>
<div class="desc"><p>Returns the instance type of this cloud apps instance</p>
<h2 id="returns">Returns</h2>
<p>(str): Instance Type</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/salesforce_instance.py#L73-L81" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def ca_instance_type(self):
    &#34;&#34;&#34;
    Returns the instance type of this cloud apps instance

    Returns:
        (str): Instance Type
    &#34;&#34;&#34;
    return &#39;SALESFORCE&#39;</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.cloudapps.salesforce_instance.SalesforceInstance.consumer_id"><code class="name">var <span class="ident">consumer_id</span></code></dt>
<dd>
<div class="desc"><p>Returns the Consumer Id of the Salesforce connected app used to authenticate with Salesforce by this instance</p>
<h2 id="returns">Returns</h2>
<p>(str): Consumer Id</p>
<h2 id="raises">Raises</h2>
<dl>
<dt><code>SDKException</code></dt>
<dd>if attribute could not be fetched</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/salesforce_instance.py#L115-L129" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def consumer_id(self):
    &#34;&#34;&#34;
    Returns the Consumer Id of the Salesforce connected app used to authenticate with Salesforce by this instance

    Returns:
        (str): Consumer Id

    Raises:
        SDKException: if attribute could not be fetched
    &#34;&#34;&#34;
    try:
        return self._properties[&#39;cloudAppsInstance&#39;][&#39;salesforceInstance&#39;][&#39;consumerId&#39;]
    except KeyError:
        raise SDKException(&#39;Instance&#39;, &#39;105&#39;, &#39;Could not fetch login url&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.cloudapps.salesforce_instance.SalesforceInstance.login_url"><code class="name">var <span class="ident">login_url</span></code></dt>
<dd>
<div class="desc"><p>Returns the login url of Salesforce organization</p>
<h2 id="returns">Returns</h2>
<p>(str): Login URL</p>
<h2 id="raises">Raises</h2>
<dl>
<dt><code>SDKException</code></dt>
<dd>if attribute could not be fetched</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/salesforce_instance.py#L99-L113" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def login_url(self):
    &#34;&#34;&#34;
    Returns the login url of Salesforce organization

    Returns:
        (str): Login URL

    Raises:
        SDKException: if attribute could not be fetched
    &#34;&#34;&#34;
    try:
        return self._properties[&#39;cloudAppsInstance&#39;][&#39;salesforceInstance&#39;][&#39;endpoint&#39;]
    except KeyError:
        raise SDKException(&#39;Instance&#39;, &#39;105&#39;, &#39;Could not fetch login url&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.cloudapps.salesforce_instance.SalesforceInstance.organization_id"><code class="name">var <span class="ident">organization_id</span></code></dt>
<dd>
<div class="desc"><p>Returns the Salesforce organization id</p>
<h2 id="returns">Returns</h2>
<p>(str): Organization Id</p>
<h2 id="raises">Raises</h2>
<dl>
<dt><code>SDKException</code></dt>
<dd>if attribute could not be fetched</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/salesforce_instance.py#L83-L97" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def organization_id(self):
    &#34;&#34;&#34;
    Returns the Salesforce organization id

    Returns:
        (str): Organization Id

    Raises:
        SDKException: if attribute could not be fetched
    &#34;&#34;&#34;
    try:
        return self._properties[&#39;cloudAppsInstance&#39;][&#39;salesforceInstance&#39;][&#39;sfOrgID&#39;]
    except KeyError:
        raise SDKException(&#39;Instance&#39;, &#39;105&#39;, &#39;Could not fetch organization ID&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.cloudapps.salesforce_instance.SalesforceInstance.proxy_client"><code class="name">var <span class="ident">proxy_client</span></code></dt>
<dd>
<div class="desc"><p>Returns the name of the access node.</p>
<h2 id="returns">Returns</h2>
<p>(str): Access Node</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if attribute could not be fetched</p>
<pre><code>if access node is a client group
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/salesforce_instance.py#L131-L158" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def proxy_client(self):
    &#34;&#34;&#34;
    Returns the name of the access node.

    Returns:
        (str): Access Node

    Raises:
        SDKException:
            if attribute could not be fetched

            if access node is a client group
    &#34;&#34;&#34;
    try:
        general_cloud_properties = self._properties[&#39;cloudAppsInstance&#39;][&#39;generalCloudProperties&#39;]
        if &#39;clientName&#39; in general_cloud_properties[&#39;proxyServers&#39;][0].keys():
            return general_cloud_properties[&#39;proxyServers&#39;][0][&#39;clientName&#39;]
        if &#39;clientName&#39; in general_cloud_properties[&#39;accessNodes&#39;][&#39;memberServers&#39;][0][&#39;client&#39;].keys():
            return general_cloud_properties[&#39;accessNodes&#39;][&#39;memberServers&#39;][0][&#39;client&#39;][&#39;clientName&#39;]
        if &#39;clientGroupName&#39; in general_cloud_properties[&#39;accessNodes&#39;][&#39;memberServers&#39;][0][&#39;client&#39;].keys():
            raise SDKException(
                &#39;Instance&#39;,
                &#39;102&#39;,
                &#39;This instance uses a client group as access node. Use access_node attribute instead.&#39;
            )
    except KeyError:
        raise SDKException(&#39;Instance&#39;, &#39;105&#39;, &#39;Could not fetch proxy client&#39;)</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.instances.cloudapps.salesforce_instance.SalesforceInstance.metadata_restore_to_file_system"><code class="name flex">
<span>def <span class="ident">metadata_restore_to_file_system</span></span>(<span>self, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Runs metadata restore to file system and returns object of Job or Schedule class. For out of place restore,
pass both client and path_to_store_csv parameters. By default, will restore to access node and download cache
path.</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>**kwargs</code></strong> :&ensp;<code>dict</code></dt>
<dd>Other restore options including
{
<br>
paths (list[str]): List of metadata components to restore like
['/Metadata/unpackaged/objects/Account.object',
'/Metadata/unpackaged/profiles/Admin.profile']
(Default is ['/Metadata/unpackaged/'] which selects all metdata components for restore),<pre><code>client (str): Name of destination client (Default is access node),

path_to_store_csv (str): path on destination client to restore to (Default is download cache path),

from_time (str): time to restore contents after like YYYY-MM-DD HH:MM:SS (Default is None),

to_time (str): time to restore contents before like YYYY-MM-DD HH:MM:SS (Default is None),

no_of_streams (int): Number of streams to use for restore (Default is 2),

dependent_restore_level (int): restore children option (Default is 0)
                                0  -- No Children
                                1  -- Immediate Children
                                -1 -- All Children,

restore_parent_type (str): restore parents option (Default is 'NONE')
                                'NONE' -- No Parents
                                'ALL'  -- All Parents
</code></pre>
<p>}</p>
</dd>
</dl>
<h2 id="returns">Returns</h2>
<dl>
<dt><code>object</code></dt>
<dd>Object of Job or Schedule class</dd>
</dl>
<h2 id="raises">Raises</h2>
<p>SDKException:
if paths is given but is not a list</p>
<pre><code>if client parameter is not given and the access node configured with this instance is a client group

if either client or path_to_store_csv is given but not both are present

if client or path_to_store_csv are not strings
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/salesforce_instance.py#L606-L670" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def metadata_restore_to_file_system(self, **kwargs):
    &#34;&#34;&#34;
    Runs metadata restore to file system and returns object of Job or Schedule class. For out of place restore,
    pass both client and path_to_store_csv parameters. By default, will restore to access node and download cache
    path.

    Args:
        **kwargs (dict): Other restore options including
            {                    
                paths (list[str]): List of metadata components to restore like
                            [&#39;/Metadata/unpackaged/objects/Account.object&#39;,
                             &#39;/Metadata/unpackaged/profiles/Admin.profile&#39;]
                            (Default is [&#39;/Metadata/unpackaged/&#39;] which selects all metdata components for restore),
                            
                client (str): Name of destination client (Default is access node),
                
                path_to_store_csv (str): path on destination client to restore to (Default is download cache path),
                
                from_time (str): time to restore contents after like YYYY-MM-DD HH:MM:SS (Default is None),
                
                to_time (str): time to restore contents before like YYYY-MM-DD HH:MM:SS (Default is None),
                
                no_of_streams (int): Number of streams to use for restore (Default is 2),
                
                dependent_restore_level (int): restore children option (Default is 0)
                                                0  -- No Children
                                                1  -- Immediate Children
                                                -1 -- All Children,
                                                
                restore_parent_type (str): restore parents option (Default is &#39;NONE&#39;)
                                                &#39;NONE&#39; -- No Parents
                                                &#39;ALL&#39;  -- All Parents
            }

    Returns:
        object: Object of Job or Schedule class

    Raises:
        SDKException:
            if paths is given but is not a list

            if client parameter is not given and the access node configured with this instance is a client group
            
            if either client or path_to_store_csv is given but not both are present
            
            if client or path_to_store_csv are not strings
    &#34;&#34;&#34;
    PARAMS = (&#39;client&#39;, &#39;path_to_store_csv&#39;)

    if not isinstance(kwargs.get(&#39;paths&#39;, list()), list):
        raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

    if any(param in kwargs for param in PARAMS) and \
            not all(isinstance(kwargs.get(param, None), str) for param in PARAMS):
        raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

    if not &#39;paths&#39; in kwargs:
        kwargs[&#39;paths&#39;] = [&#39;/Metadata/unpackaged/&#39;]
    
    request_json = self._restore_json(
        restore_to_file_system=True,
        is_metadata_restore=True,
        **kwargs
    )
    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.cloudapps.salesforce_instance.SalesforceInstance.metadata_restore_to_salesforce"><code class="name flex">
<span>def <span class="ident">metadata_restore_to_salesforce</span></span>(<span>self, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Runs metadata restore to Salesforce and returns object of Job or Schedule class. For out of place restore,
pass client, instance and backupset parameters.</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>**kwargs</code></strong> :&ensp;<code>dict</code></dt>
<dd>Other restore options including
{
paths (list[str]): List of metadata components to restore like
['/Metadata/unpackaged/objects/Account.object',
'/Metadata/unpackaged/profiles/Admin.profile']
(Default is ['/Metadata/unpackaged/'] which selects all metdata components for restore),<pre><code>client (str): Name of destination client (Default is source client),

instance (str): Name of destination instance (Default is source instance),

backupset (str): Name of destination backupset (Default is source backupset),

from_time (str): time to restore contents after like YYYY-MM-DD HH:MM:SS (Default is None),

to_time (str): time to restore contents before like YYYY-MM-DD HH:MM:SS (Default is None),

no_of_streams (int): Number of streams to use for restore (Default is 2),

path_to_store_csv (str): path to use as staging folder (Default is download cache path),

dependent_restore_level (int): restore children option (Default is 0)
                                0  -- No Children
                                1  -- Immediate Children
                                -1 -- All Children,

restore_parent_type (str): restore parents option (Default is 'NONE')
                                'NONE' -- No Parents
                                'ALL'  -- All Parents
</code></pre>
<p>}</p>
</dd>
</dl>
<h2 id="returns">Returns</h2>
<dl>
<dt><code>object</code></dt>
<dd>Object of Job or Schedule class</dd>
</dl>
<h2 id="raises">Raises</h2>
<p>SDKException:
if paths is given but is not a list</p>
<pre><code>if either client, instance or backupset are given but not all three are present

if client, instance and backupset are not strings
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/salesforce_instance.py#L672-L738" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def metadata_restore_to_salesforce(self, **kwargs):
    &#34;&#34;&#34;
    Runs metadata restore to Salesforce and returns object of Job or Schedule class. For out of place restore,
    pass client, instance and backupset parameters.

    Args:
        **kwargs (dict): Other restore options including
            {
                paths (list[str]): List of metadata components to restore like
                            [&#39;/Metadata/unpackaged/objects/Account.object&#39;,
                             &#39;/Metadata/unpackaged/profiles/Admin.profile&#39;]
                            (Default is [&#39;/Metadata/unpackaged/&#39;] which selects all metdata components for restore),
                            
                client (str): Name of destination client (Default is source client),
                
                instance (str): Name of destination instance (Default is source instance),
                
                backupset (str): Name of destination backupset (Default is source backupset),
                
                from_time (str): time to restore contents after like YYYY-MM-DD HH:MM:SS (Default is None),
                
                to_time (str): time to restore contents before like YYYY-MM-DD HH:MM:SS (Default is None),
                
                no_of_streams (int): Number of streams to use for restore (Default is 2),
                
                path_to_store_csv (str): path to use as staging folder (Default is download cache path),
                
                dependent_restore_level (int): restore children option (Default is 0)
                                                0  -- No Children
                                                1  -- Immediate Children
                                                -1 -- All Children,
                                                
                restore_parent_type (str): restore parents option (Default is &#39;NONE&#39;)
                                                &#39;NONE&#39; -- No Parents
                                                &#39;ALL&#39;  -- All Parents
            }

    Returns:
        object: Object of Job or Schedule class

    Raises:
        SDKException:
            if paths is given but is not a list
            
            if either client, instance or backupset are given but not all three are present

            if client, instance and backupset are not strings
    &#34;&#34;&#34;
    DEST_PARAMS = (&#39;client&#39;, &#39;instance&#39;, &#39;backupset&#39;)

    if not isinstance(kwargs.get(&#39;paths&#39;, list()), list):
        raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

    if any(param in kwargs for param in DEST_PARAMS) and \
            not all(isinstance(kwargs.get(param, None), str) for param in DEST_PARAMS):
        raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

    if not &#39;paths&#39; in kwargs:
        kwargs[&#39;paths&#39;] = [&#39;/Metadata/unpackaged/&#39;]
    
    request_json = self._restore_json(
        restore_to_salesforce=True,
        is_metadata_restore=True,
        **kwargs
    )
    
    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.cloudapps.salesforce_instance.SalesforceInstance.restore_to_database"><code class="name flex">
<span>def <span class="ident">restore_to_database</span></span>(<span>self, db_type, db_host_name, db_name, db_user_name, db_password, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Runs object level restore to database and returns object of Job or Schedule class</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>db_type</code></strong> :&ensp;<code>str</code></dt>
<dd>Type of database out of 'POSTGRESQL' or 'SQLSERVER'</dd>
<dt><strong><code>db_host_name</code></strong> :&ensp;<code>str</code></dt>
<dd>Hostname of database server</dd>
<dt><strong><code>db_name</code></strong> :&ensp;<code>str</code></dt>
<dd>Database name where objects will be restored</dd>
<dt><strong><code>db_user_name</code></strong> :&ensp;<code>str</code></dt>
<dd>Username of database user</dd>
<dt><strong><code>db_password</code></strong> :&ensp;<code>str</code></dt>
<dd>Password of database user</dd>
<dt><strong><code>**kwargs</code></strong> :&ensp;<code>dict</code></dt>
<dd>Other restore options including
{
paths (list[str]): List of files and objects to restore like
['/Files/filename', '/Objects/object_name']
(Default is ['/Objects/'] which selects all objects for restore),<pre><code>db_instance (str): Database instance for SQL Server,

db_port (int): Port of database server (Default is 5432 for POSTGRESQL and 1433 for SQLSERVER),

from_time (str): time to restore contents after like YYYY-MM-DD HH:MM:SS (Default is None),

to_time (str): time to restore contents before like YYYY-MM-DD HH:MM:SS (Default is None),

no_of_streams (int): Number of streams to use for restore (Default is 2),

path_to_store_csv (str): path to use as staging folder (Default is download cache path),

dependent_restore_level (int): restore children option (Default is 0)
                                0  -- No Children
                                1  -- Immediate Children
                                -1 -- All Children,

restore_parent_type (str): restore parents option (Default is 'NONE')
                                'NONE' -- No Parents
                                'ALL'  -- All Parents
</code></pre>
<p>}</p>
</dd>
</dl>
<h2 id="returns">Returns</h2>
<dl>
<dt><code>object</code></dt>
<dd>Object of Job or Schedule class</dd>
</dl>
<h2 id="raises">Raises</h2>
<p>SDKException:
if required parameters are not of the correct type</p>
<pre><code>if db_type is 'SQLSERVER' but db_instance is not given/ is not a string

if paths is given but is not a list
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/salesforce_instance.py#L332-L416" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_to_database(
        self,
        db_type,
        db_host_name,
        db_name,
        db_user_name,
        db_password,
        **kwargs
    ):
    &#34;&#34;&#34;
    Runs object level restore to database and returns object of Job or Schedule class

    Args:
        db_type (str): Type of database out of &#39;POSTGRESQL&#39; or &#39;SQLSERVER&#39;

        db_host_name (str): Hostname of database server

        db_name (str): Database name where objects will be restored

        db_user_name (str): Username of database user

        db_password (str): Password of database user

        **kwargs (dict): Other restore options including
            {
                paths (list[str]): List of files and objects to restore like
                            [&#39;/Files/filename&#39;, &#39;/Objects/object_name&#39;]
                            (Default is [&#39;/Objects/&#39;] which selects all objects for restore),

                db_instance (str): Database instance for SQL Server,

                db_port (int): Port of database server (Default is 5432 for POSTGRESQL and 1433 for SQLSERVER),

                from_time (str): time to restore contents after like YYYY-MM-DD HH:MM:SS (Default is None),

                to_time (str): time to restore contents before like YYYY-MM-DD HH:MM:SS (Default is None),

                no_of_streams (int): Number of streams to use for restore (Default is 2),

                path_to_store_csv (str): path to use as staging folder (Default is download cache path),

                dependent_restore_level (int): restore children option (Default is 0)
                                                0  -- No Children
                                                1  -- Immediate Children
                                                -1 -- All Children,

                restore_parent_type (str): restore parents option (Default is &#39;NONE&#39;)
                                                &#39;NONE&#39; -- No Parents
                                                &#39;ALL&#39;  -- All Parents
            }

    Returns:
        object: Object of Job or Schedule class

    Raises:
        SDKException:
            if required parameters are not of the correct type

            if db_type is &#39;SQLSERVER&#39; but db_instance is not given/ is not a string

            if paths is given but is not a list
    &#34;&#34;&#34;
    PARAMS = (db_type, db_host_name,  db_name, db_user_name, db_password)

    if not isinstance(kwargs.get(&#39;paths&#39;, list()), list):
        raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

    if not all(isinstance(val, str) for val in PARAMS) and \
            (isinstance(kwargs.get(&#39;db_instance&#39;, None), str) != (db_type == &#39;SQLSERVER&#39;)):
        raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

    if not &#39;paths&#39; in kwargs:
        kwargs[&#39;paths&#39;] = [&#39;/Objects/&#39;]

    request_json = self._restore_json(
        db_enabled=True,
        db_type=db_type,
        db_host=db_host_name,
        db_name=db_name,
        db_user_name=db_user_name,
        db_password=db_password,
        **kwargs
    )

    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.cloudapps.salesforce_instance.SalesforceInstance.restore_to_file_system"><code class="name flex">
<span>def <span class="ident">restore_to_file_system</span></span>(<span>self, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Runs object level restore to file system and returns object of Job or Schedule class. For out of place restore,
pass both client and path_to_store_csv parameters. By default, will restore to access node and download cache
path.</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>**kwargs</code></strong> :&ensp;<code>dict</code></dt>
<dd>Restore options including
{
paths (list[str]): List of files and objects to restore like
['/Files/filename', '/Objects/object_name']
(Default is ['/Files/', '/Objects/'] which selects all files and objects for restore),<pre><code>client (str): Name of destination client (Default is access node),

path_to_store_csv (str): path on destination client to restore to (Default is download cache path),

from_time (str): time to restore contents after like YYYY-MM-DD HH:MM:SS (Default is None),

to_time (str): time to restore contents before like YYYY-MM-DD HH:MM:SS (Default is None),

no_of_streams (int): Number of streams to use for restore (Default is 2),

dependent_restore_level (int): restore children option (Default is 0)
                                0  -- No Children
                                1  -- Immediate Children
                                -1 -- All Children,

restore_parent_type (str): restore parents option (Default is 'NONE')
                                'NONE' -- No Parents
                                'ALL'  -- All Parents
</code></pre>
<p>}</p>
</dd>
</dl>
<h2 id="returns">Returns</h2>
<dl>
<dt><code>object</code></dt>
<dd>Object of Job or Schedule class</dd>
</dl>
<h2 id="raises">Raises</h2>
<p>SDKException:
if paths is given but is not a list</p>
<pre><code>if client parameter is not given and the access node configured with this instance is a client group

if either client or path_to_store_csv is given but not both are present

if client or path_to_store_csv are not strings
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/salesforce_instance.py#L266-L330" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_to_file_system(self, **kwargs):
    &#34;&#34;&#34;
    Runs object level restore to file system and returns object of Job or Schedule class. For out of place restore,
    pass both client and path_to_store_csv parameters. By default, will restore to access node and download cache
    path.

    Args:
        **kwargs (dict): Restore options including
            {
                paths (list[str]): List of files and objects to restore like
                            [&#39;/Files/filename&#39;, &#39;/Objects/object_name&#39;]
                            (Default is [&#39;/Files/&#39;, &#39;/Objects/&#39;] which selects all files and objects for restore),

                client (str): Name of destination client (Default is access node),

                path_to_store_csv (str): path on destination client to restore to (Default is download cache path),

                from_time (str): time to restore contents after like YYYY-MM-DD HH:MM:SS (Default is None),

                to_time (str): time to restore contents before like YYYY-MM-DD HH:MM:SS (Default is None),

                no_of_streams (int): Number of streams to use for restore (Default is 2),

                dependent_restore_level (int): restore children option (Default is 0)
                                                0  -- No Children
                                                1  -- Immediate Children
                                                -1 -- All Children,

                restore_parent_type (str): restore parents option (Default is &#39;NONE&#39;)
                                                &#39;NONE&#39; -- No Parents
                                                &#39;ALL&#39;  -- All Parents
            }

    Returns:
        object: Object of Job or Schedule class

    Raises:
        SDKException:
            if paths is given but is not a list

            if client parameter is not given and the access node configured with this instance is a client group
            
            if either client or path_to_store_csv is given but not both are present
            
            if client or path_to_store_csv are not strings
    &#34;&#34;&#34;
    PARAMS = (&#39;client&#39;, &#39;path_to_store_csv&#39;)

    if not isinstance(kwargs.get(&#39;paths&#39;, list()), list):
        raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

    if any(param in kwargs for param in PARAMS) and \
            not all(isinstance(kwargs.get(param, None), str) for param in PARAMS):
        raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

    if not &#39;paths&#39; in kwargs:
        kwargs[&#39;paths&#39;] = [&#39;/Files/&#39;, &#39;/Objects/&#39;]

    request_json = self._restore_json(
        client=kwargs.get(&#39;client&#39;, self.proxy_client),
        restore_to_file_system=True,
        **kwargs
    )
    
    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.cloudapps.salesforce_instance.SalesforceInstance.restore_to_salesforce_from_database"><code class="name flex">
<span>def <span class="ident">restore_to_salesforce_from_database</span></span>(<span>self, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Runs restore to Salesforce from database and returns object of Job or Schedule class. For out of place restore,
pass the client, instance and backupset parameters. If database parameters are not passed, sync db will be used.</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>**kwargs</code></strong> :&ensp;<code>dict</code></dt>
<dd>Other restore options including
{
paths (list[str]): List of files and objects to restore like
['/Files/filename', '/Objects/object_name']
(Default is ['/Files/', '/Objects/'] which selects all files and objects for restore),<pre><code>client (str): Name of destination client (Default is source client),

instance (str): Name of destination instance (Default is source instance),

backupset (str): Name of destination backupset (Default is source backupset),

db_type (str): Type of database out of 'POSTGRESQL' or 'SQLSERVER',

db_host (str): Hostname of database server,

db_name (str): Database name where objects will be restored,

db_user_name (str): Username of database user,

db_password (str): Password of database user,

db_instance (str): Database instance for SQL Server,

db_port (int): Port of database server (Default is 5432 for POSTGRESQL and 1433 for SQLSERVER),

from_time (str): time to restore contents after like YYYY-MM-DD HH:MM:SS (Default is None),

to_time (str): time to restore contents before like YYYY-MM-DD HH:MM:SS (Default is None),

no_of_streams (int): Number of streams to use for restore (Default is 2),

path_to_store_csv (str): path to use as staging folder (Default is download cache path),

dependent_restore_level (int): restore children option (Default is 0)
                                0  -- No Children
                                1  -- Immediate Children
                                -1 -- All Children,

restore_parent_type (str): restore parents option (Default is 'NONE')
                                'NONE' -- No Parents
                                'ALL'  -- All Parents
</code></pre>
<p>}</p>
</dd>
</dl>
<h2 id="returns">Returns</h2>
<dl>
<dt><code>object</code></dt>
<dd>Object of Job or Schedule class</dd>
</dl>
<h2 id="raises">Raises</h2>
<p>SDKException:
if paths is given but is not a list</p>
<pre><code>if any database parameters are given but not all are present

if database parameters are not all strings

if db_type is 'SQLSERVER' but db_instance is not given/ is not a string

if either client, instance or backupset are given but not all three are present

if client, instance and backupset are not strings
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/salesforce_instance.py#L418-L510" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_to_salesforce_from_database(self, **kwargs):
    &#34;&#34;&#34;
    Runs restore to Salesforce from database and returns object of Job or Schedule class. For out of place restore,
    pass the client, instance and backupset parameters. If database parameters are not passed, sync db will be used.

    Args:
        **kwargs (dict): Other restore options including
            {
                paths (list[str]): List of files and objects to restore like
                            [&#39;/Files/filename&#39;, &#39;/Objects/object_name&#39;]
                            (Default is [&#39;/Files/&#39;, &#39;/Objects/&#39;] which selects all files and objects for restore),

                client (str): Name of destination client (Default is source client),

                instance (str): Name of destination instance (Default is source instance),

                backupset (str): Name of destination backupset (Default is source backupset),

                db_type (str): Type of database out of &#39;POSTGRESQL&#39; or &#39;SQLSERVER&#39;,

                db_host (str): Hostname of database server,

                db_name (str): Database name where objects will be restored,

                db_user_name (str): Username of database user,

                db_password (str): Password of database user,

                db_instance (str): Database instance for SQL Server,

                db_port (int): Port of database server (Default is 5432 for POSTGRESQL and 1433 for SQLSERVER),

                from_time (str): time to restore contents after like YYYY-MM-DD HH:MM:SS (Default is None),

                to_time (str): time to restore contents before like YYYY-MM-DD HH:MM:SS (Default is None),

                no_of_streams (int): Number of streams to use for restore (Default is 2),

                path_to_store_csv (str): path to use as staging folder (Default is download cache path),

                dependent_restore_level (int): restore children option (Default is 0)
                                                0  -- No Children
                                                1  -- Immediate Children
                                                -1 -- All Children,

                restore_parent_type (str): restore parents option (Default is &#39;NONE&#39;)
                                                &#39;NONE&#39; -- No Parents
                                                &#39;ALL&#39;  -- All Parents
            }

    Returns:
        object: Object of Job or Schedule class

    Raises:
        SDKException:
            if paths is given but is not a list

            if any database parameters are given but not all are present

            if database parameters are not all strings

            if db_type is &#39;SQLSERVER&#39; but db_instance is not given/ is not a string

            if either client, instance or backupset are given but not all three are present

            if client, instance and backupset are not strings
    &#34;&#34;&#34;
    DB_PARAMS = (&#39;db_type&#39;, &#39;db_host&#39;, &#39;db_name&#39;, &#39;db_user_name&#39;, &#39;db_password&#39;)
    DEST_PARAMS = (&#39;client&#39;, &#39;instance&#39;, &#39;backupset&#39;)

    if not isinstance(kwargs.get(&#39;paths&#39;, list()), list):
        raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

    if any(param in kwargs for param in DEST_PARAMS) and \
            not all(isinstance(kwargs.get(param, None), str) for param in DEST_PARAMS):
        raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

    if any(param in kwargs for param in DB_PARAMS):
        if not all(isinstance(kwargs.get(param, None), str) for param in DB_PARAMS) and \
                (isinstance(kwargs.get(&#39;db_instance&#39;, None), str) != (kwargs[&#39;db_type&#39;] == &#39;SQLSERVER&#39;)):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        kwargs[&#39;db_enabled&#39;] = True

    if not &#39;paths&#39; in kwargs:
        kwargs[&#39;paths&#39;] = [&#39;/Files/&#39;, &#39;/Objects/&#39;]

    request_json = self._restore_json(
        restore_to_salesforce=True,
        restore_from_database=True,
        **kwargs
    )

    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.cloudapps.salesforce_instance.SalesforceInstance.restore_to_salesforce_from_media"><code class="name flex">
<span>def <span class="ident">restore_to_salesforce_from_media</span></span>(<span>self, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Runs restore to Salesforce from database and returns object of Job or Schedule class. For out of place restore,
pass the client, instance and backupset parameters. If database parameters are not passed, sync db will be used
as staging db.</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>**kwargs</code></strong> :&ensp;<code>dict</code></dt>
<dd>Other restore options including
{
paths (list[str]): List of files and objects to restore like
['/Files/filename', '/Objects/object_name']
(Default is ['/Files/', '/Objects/'] which selects all files and objects for restore),<pre><code>client (str): Name of destination client (Default is source client),

instance (str): Name of destination instance (Default is source instance),

backupset (str): Name of destination backupset (Default is source backupset),

db_type (str): Type of database out of 'POSTGRESQL' or 'SQLSERVER',

db_host (str): Hostname of database server,

db_name (str): Database name where objects will be restored,

db_user_name (str): Username of database user,

db_password (str): Password of database user,

db_instance (str): Database instance for SQL Server,

db_port (int): Port of database server (Default is 5432 for POSTGRESQL and 1433 for SQLSERVER),

from_time (str): time to restore contents after like YYYY-MM-DD HH:MM:SS (Default is None),

to_time (str): time to restore contents before like YYYY-MM-DD HH:MM:SS (Default is None),

no_of_streams (int): Number of streams to use for restore (Default is 2),

path_to_store_csv (str): path to use as staging folder (Default is download cache path),

dependent_restore_level (int): restore children option (Default is 0)
                                0  -- No Children
                                1  -- Immediate Children
                                -1 -- All Children,

restore_parent_type (str): restore parents option (Default is 'NONE')
                                'NONE' -- No Parents
                                'ALL'  -- All Parents
</code></pre>
<p>}</p>
</dd>
</dl>
<h2 id="returns">Returns</h2>
<dl>
<dt><code>object</code></dt>
<dd>Object of Job or Schedule class</dd>
</dl>
<h2 id="raises">Raises</h2>
<p>SDKException:
if paths is given but is not a list</p>
<pre><code>if any database parameters are given but not all are present

if database parameters are not all strings

if db_type is 'SQLSERVER' but db_instance is not given/ is not a string

if either client, instance or backupset are given but not all three are present

if client, instance and backupset are not strings
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/salesforce_instance.py#L512-L604" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_to_salesforce_from_media(self, **kwargs):
    &#34;&#34;&#34;
    Runs restore to Salesforce from database and returns object of Job or Schedule class. For out of place restore,
    pass the client, instance and backupset parameters. If database parameters are not passed, sync db will be used
    as staging db.

    Args:
        **kwargs (dict): Other restore options including
            {
                paths (list[str]): List of files and objects to restore like
                            [&#39;/Files/filename&#39;, &#39;/Objects/object_name&#39;]
                            (Default is [&#39;/Files/&#39;, &#39;/Objects/&#39;] which selects all files and objects for restore),

                client (str): Name of destination client (Default is source client),

                instance (str): Name of destination instance (Default is source instance),

                backupset (str): Name of destination backupset (Default is source backupset),

                db_type (str): Type of database out of &#39;POSTGRESQL&#39; or &#39;SQLSERVER&#39;,

                db_host (str): Hostname of database server,

                db_name (str): Database name where objects will be restored,

                db_user_name (str): Username of database user,

                db_password (str): Password of database user,

                db_instance (str): Database instance for SQL Server,

                db_port (int): Port of database server (Default is 5432 for POSTGRESQL and 1433 for SQLSERVER),

                from_time (str): time to restore contents after like YYYY-MM-DD HH:MM:SS (Default is None),

                to_time (str): time to restore contents before like YYYY-MM-DD HH:MM:SS (Default is None),

                no_of_streams (int): Number of streams to use for restore (Default is 2),

                path_to_store_csv (str): path to use as staging folder (Default is download cache path),

                dependent_restore_level (int): restore children option (Default is 0)
                                                0  -- No Children
                                                1  -- Immediate Children
                                                -1 -- All Children,

                restore_parent_type (str): restore parents option (Default is &#39;NONE&#39;)
                                                &#39;NONE&#39; -- No Parents
                                                &#39;ALL&#39;  -- All Parents
            }

    Returns:
        object: Object of Job or Schedule class

    Raises:
        SDKException:
            if paths is given but is not a list

            if any database parameters are given but not all are present

            if database parameters are not all strings

            if db_type is &#39;SQLSERVER&#39; but db_instance is not given/ is not a string

            if either client, instance or backupset are given but not all three are present

            if client, instance and backupset are not strings
    &#34;&#34;&#34;
    DB_PARAMS = (&#39;db_type&#39;, &#39;db_host&#39;, &#39;db_name&#39;, &#39;db_user_name&#39;, &#39;db_password&#39;)
    DEST_PARAMS = (&#39;client&#39;, &#39;instance&#39;, &#39;backupset&#39;)

    if not isinstance(kwargs.get(&#39;paths&#39;, list()), list):
        raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

    if any(param in kwargs for param in DEST_PARAMS) and \
            not all(isinstance(kwargs.get(param, None), str) for param in DEST_PARAMS):
        raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

    if any(keyword in kwargs for keyword in DB_PARAMS):
        if not all(isinstance(kwargs.get(param, None), str) for param in DB_PARAMS) and \
                (isinstance(kwargs.get(&#39;db_instance&#39;, None), str) != (kwargs[&#39;db_type&#39;] == &#39;SQLSERVER&#39;)):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        kwargs[&#39;db_enabled&#39;] = True

    if not &#39;paths&#39; in kwargs:
        kwargs[&#39;paths&#39;] = [&#39;/Files/&#39;, &#39;/Objects/&#39;]

    request_json = self._restore_json(
        restore_to_salesforce=True,
        **kwargs
    )

    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.instances.cainstance.CloudAppsInstance" href="../cainstance.html#cvpysdk.instances.cainstance.CloudAppsInstance">CloudAppsInstance</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.backupsets" href="../../instance.html#cvpysdk.instance.Instance.backupsets">backupsets</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.browse" href="../../instance.html#cvpysdk.instance.Instance.browse">browse</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.credentials" href="../../instance.html#cvpysdk.instance.Instance.credentials">credentials</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.find" href="../../instance.html#cvpysdk.instance.Instance.find">find</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.instance_id" href="../../instance.html#cvpysdk.instance.Instance.instance_id">instance_id</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.instance_name" href="../../instance.html#cvpysdk.instance.Instance.instance_name">instance_name</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.name" href="../../instance.html#cvpysdk.instance.Instance.name">name</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.properties" href="../../instance.html#cvpysdk.instance.Instance.properties">properties</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.refresh" href="../../instance.html#cvpysdk.instance.Instance.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.subclients" href="../../instance.html#cvpysdk.instance.Instance.subclients">subclients</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.update_properties" href="../../instance.html#cvpysdk.instance.Instance.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.instances.cloudapps" href="index.html">cvpysdk.instances.cloudapps</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.instances.cloudapps.salesforce_instance.SalesforceInstance" href="#cvpysdk.instances.cloudapps.salesforce_instance.SalesforceInstance">SalesforceInstance</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.instances.cloudapps.salesforce_instance.SalesforceInstance.access_node" href="#cvpysdk.instances.cloudapps.salesforce_instance.SalesforceInstance.access_node">access_node</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.salesforce_instance.SalesforceInstance.ca_instance_type" href="#cvpysdk.instances.cloudapps.salesforce_instance.SalesforceInstance.ca_instance_type">ca_instance_type</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.salesforce_instance.SalesforceInstance.consumer_id" href="#cvpysdk.instances.cloudapps.salesforce_instance.SalesforceInstance.consumer_id">consumer_id</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.salesforce_instance.SalesforceInstance.login_url" href="#cvpysdk.instances.cloudapps.salesforce_instance.SalesforceInstance.login_url">login_url</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.salesforce_instance.SalesforceInstance.metadata_restore_to_file_system" href="#cvpysdk.instances.cloudapps.salesforce_instance.SalesforceInstance.metadata_restore_to_file_system">metadata_restore_to_file_system</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.salesforce_instance.SalesforceInstance.metadata_restore_to_salesforce" href="#cvpysdk.instances.cloudapps.salesforce_instance.SalesforceInstance.metadata_restore_to_salesforce">metadata_restore_to_salesforce</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.salesforce_instance.SalesforceInstance.organization_id" href="#cvpysdk.instances.cloudapps.salesforce_instance.SalesforceInstance.organization_id">organization_id</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.salesforce_instance.SalesforceInstance.proxy_client" href="#cvpysdk.instances.cloudapps.salesforce_instance.SalesforceInstance.proxy_client">proxy_client</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.salesforce_instance.SalesforceInstance.restore_to_database" href="#cvpysdk.instances.cloudapps.salesforce_instance.SalesforceInstance.restore_to_database">restore_to_database</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.salesforce_instance.SalesforceInstance.restore_to_file_system" href="#cvpysdk.instances.cloudapps.salesforce_instance.SalesforceInstance.restore_to_file_system">restore_to_file_system</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.salesforce_instance.SalesforceInstance.restore_to_salesforce_from_database" href="#cvpysdk.instances.cloudapps.salesforce_instance.SalesforceInstance.restore_to_salesforce_from_database">restore_to_salesforce_from_database</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.salesforce_instance.SalesforceInstance.restore_to_salesforce_from_media" href="#cvpysdk.instances.cloudapps.salesforce_instance.SalesforceInstance.restore_to_salesforce_from_media">restore_to_salesforce_from_media</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>