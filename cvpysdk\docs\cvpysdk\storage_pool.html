<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.storage_pool API documentation</title>
<meta name="description" content="File for doing operations on an Storage Pools …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.storage_pool</code></h1>
</header>
<section id="section-intro">
<p>File for doing operations on an Storage Pools.</p>
<p>This module has classes defined for doing operations for Storage Pools:</p>
<p>StoragePools, StoragePoolType, StorageType, WORMLockType and StoragePool are the classes defined in this file.</p>
<p>StoragePools: Class for representing all the StoragePools in the commcell</p>
<p>StoragePoolType : Class for representing storage pool types like deduplication, secondary copy, non-dedupe, scale out</p>
<p>StorageType : Class for representing storage types like disk, cloud, tape of a storage pool</p>
<p>WORMLockType : Class for representing different WORM lock types flag values of a WORM enable storage pool</p>
<p>StoragePool: Class for representing a single StoragePool of the commcell</p>
<h1 id="storagepools">StoragePools</h1>
<pre><code>__init__(commcell_object)   --  initializes object of the StoragePools class associated with the commcell

__str__()                   --  returns all the storage pools associated with the commcell

__repr__()                  --  returns the string representation of an instance of this class

__len__()                   --  returns the number of storage pools added to the Commcell

__getitem__()               --  returns the name of the storage pool for the given storage
pool Id or the details for the given storage pool name

_get_storage_pools()        --  returns all storage pools added to the commcell

has_storage_pool()          --  checks whether the storage pool  with given name exists or not

get()                       --  returns StoragePool object of the storage pool for the
                                specified input name

add()                       --  Adds a storage pool, according to given input and returns
                                StoragePool object

delete()                    --  deletes the specified storage pool

refresh()                   --  refresh the list of storage pools associated with the commcell

add_air_gap_protect()       --  Adds a new air gap protect storage pool to commcell
</code></pre>
<h2 id="attributes">Attributes</h2>
<pre><code>**all_storage_pools**   --  returns dict of all the storage pools on commcell
</code></pre>
<dl>
<dt><strong><code>StoragePool</code></strong></dt>
<dd>&nbsp;</dd>
</dl>
<p>===========</p>
<pre><code>__init__()                  --  initialize the instance of StoragePool class for specific storage pool of commcell

__repr__()                  --  returns a string representation of the StoragePool instance

 _get_storage_pool_properties() --  returns the properties of this storage pool

refresh()                   --      Refresh the properties of the StoragePool

get_copy()                  --  Returns the StoragePolicyCopy object of Storage Pool copy

enable_compliance_lock()    --  Enables compliance lock on Storage Pool Copy

enable_worm_storage_lock()  --  Enables WORM storage lock on storage pool

hyperscale_add_nodes()      --  Add 3 new nodes to an existing storage pool
</code></pre>
<h1 id="storagepool-instance-attributes">StoragePool instance attributes</h1>
<pre><code>**storage_pool_name**           --  returns the name of the storage pool

**storage_pool_id**             --  returns the storage pool id

**storage_pool_properties**     --  returns the properties of the storage pool

**global_policy_name**          --  returns the global policy corresponding to the storage pool

**copy_name**                   --  returns the copy name of the storage pool

**copy_id**                     --  returns the copy id of the storage pool

**storage_pool_type**           --  returns the storage pool type

**storage_type**                --  returns the storage type of the storage pool

**storage_vendor**              --  returns the storage vendor id of the storage pool

**is_worm_storage_lock_enabled**--  returns whether WORM storage lock is enabled

**is_object_level_worm_lock_enabled** --  returns whether object level WORM lock is enabled

**is_bucket_level_worm_lock_enabled** --  returns whether bucket level WORM lock is enabled

**is_compliance_lock_enabled**  --  returns whether compliance lock is enabled
</code></pre>
<h1 id="todo-check-with-mm-api-team-to-get-the-response-in-json">TODO: check with MM API team to get the response in JSON</h1>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage_pool.py#L1-L1341" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;File for doing operations on an Storage Pools.

This module has classes defined for doing operations for Storage Pools:

StoragePools, StoragePoolType, StorageType, WORMLockType and StoragePool are the classes defined in this file.

StoragePools: Class for representing all the StoragePools in the commcell

StoragePoolType : Class for representing storage pool types like deduplication, secondary copy, non-dedupe, scale out

StorageType : Class for representing storage types like disk, cloud, tape of a storage pool

WORMLockType : Class for representing different WORM lock types flag values of a WORM enable storage pool

StoragePool: Class for representing a single StoragePool of the commcell


StoragePools
============

    __init__(commcell_object)   --  initializes object of the StoragePools class associated with the commcell

    __str__()                   --  returns all the storage pools associated with the commcell

    __repr__()                  --  returns the string representation of an instance of this class

    __len__()                   --  returns the number of storage pools added to the Commcell

    __getitem__()               --  returns the name of the storage pool for the given storage
    pool Id or the details for the given storage pool name

    _get_storage_pools()        --  returns all storage pools added to the commcell

    has_storage_pool()          --  checks whether the storage pool  with given name exists or not

    get()                       --  returns StoragePool object of the storage pool for the
                                    specified input name

    add()                       --  Adds a storage pool, according to given input and returns
                                    StoragePool object

    delete()                    --  deletes the specified storage pool

    refresh()                   --  refresh the list of storage pools associated with the commcell

    add_air_gap_protect()       --  Adds a new air gap protect storage pool to commcell

Attributes
----------

    **all_storage_pools**   --  returns dict of all the storage pools on commcell


StoragePool
===========

    __init__()                  --  initialize the instance of StoragePool class for specific storage pool of commcell

    __repr__()                  --  returns a string representation of the StoragePool instance

     _get_storage_pool_properties() --  returns the properties of this storage pool

    refresh()                   --      Refresh the properties of the StoragePool

    get_copy()                  --  Returns the StoragePolicyCopy object of Storage Pool copy

    enable_compliance_lock()    --  Enables compliance lock on Storage Pool Copy

    enable_worm_storage_lock()  --  Enables WORM storage lock on storage pool

    hyperscale_add_nodes()      --  Add 3 new nodes to an existing storage pool

StoragePool instance attributes
================================

    **storage_pool_name**           --  returns the name of the storage pool

    **storage_pool_id**             --  returns the storage pool id

    **storage_pool_properties**     --  returns the properties of the storage pool

    **global_policy_name**          --  returns the global policy corresponding to the storage pool

    **copy_name**                   --  returns the copy name of the storage pool

    **copy_id**                     --  returns the copy id of the storage pool

    **storage_pool_type**           --  returns the storage pool type

    **storage_type**                --  returns the storage type of the storage pool

    **storage_vendor**              --  returns the storage vendor id of the storage pool

    **is_worm_storage_lock_enabled**--  returns whether WORM storage lock is enabled

    **is_object_level_worm_lock_enabled** --  returns whether object level WORM lock is enabled

    **is_bucket_level_worm_lock_enabled** --  returns whether bucket level WORM lock is enabled

    **is_compliance_lock_enabled**  --  returns whether compliance lock is enabled

# TODO: check with MM API team to get the response in JSON

&#34;&#34;&#34;
import copy

import xmltodict
from base64 import b64encode
from enum import IntFlag, IntEnum

from .exception import SDKException

from .storage import MediaAgent
from .security.security_association import SecurityAssociation
from .constants import StoragePoolConstants
from .policies.storage_policies import StoragePolicyCopy

class StorageType(IntEnum):
    &#34;&#34;&#34;Class IntEnum to represent different storage types&#34;&#34;&#34;
    DISK = 1,
    CLOUD = 2,
    HYPERSCALE = 3,
    TAPE = 4

class StoragePools:
    &#34;&#34;&#34;Class for doing operations on Storage Pools, like get storage poo ID.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initializes instance of the StoragePools class to perform operations on a storage pool.

            Args:
                commcell_object     (object)    --  instance of the Commcell class

            Returns:
                object  -   instance of the StoragePools class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object

        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_
        self._add_storage_pool_api = self._services[&#39;ADD_STORAGE_POOL&#39;]

        self._storage_pools_api = self._services[&#39;STORAGE_POOL&#39;]

        self._metallic_storage_api = self._services[&#39;GET_METALLIC_STORAGE_DETAILS&#39;]
        self.__get_agp_storage_api = self._services[&#39;GET_AGP_STORAGE&#39;]
        self._storage_pools = None

        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all storage pools present in the Commcell.

            Returns:
                str     -   string of all the storage pools associated with the commcell

        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^40}\n\n&#39;.format(&#39;S. No.&#39;, &#39;Storage Pool&#39;)

        for index, storage_pool in enumerate(self._storage_pools):
            sub_str = &#39;{:^5}\t{:40}\n&#39;.format(index + 1, storage_pool)
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Returns the string representation of an instance of this class.&#34;&#34;&#34;
        return &#34;StoragePools class instance for Commcell&#34;

    def __len__(self):
        &#34;&#34;&#34;Returns the number of the storage pools added to the Commcell.&#34;&#34;&#34;
        return len(self.all_storage_pools)

    def __getitem__(self, value):
        &#34;&#34;&#34;Returns the name of the storage pool for the given storage pool ID or
            the details of the storage pool for given storage pool Name.

            Args:
                value   (str / int)     --  Name or ID of the storage pool

            Returns:
                str     -   name of the storage pool, if the storage pool id was given

                dict    -   dict of details of the storage pool, if storage pool name was given

            Raises:
                IndexError:
                    no storage pool exists with the given Name / Id

        &#34;&#34;&#34;
        value = str(value)

        if value in self.all_storage_pools:
            return self.all_storage_pools[value]
        else:
            try:
                return list(filter(lambda x: x[1][&#39;id&#39;] == value, self.all_storage_pools.items()))[0][0]
            except IndexError:
                raise IndexError(&#39;No storage pool exists with the given Name / Id&#39;)

    def _get_storage_pools(self):
        &#34;&#34;&#34;Gets all the storage pools associated with the Commcell environment.

            Returns:
                dict    -   consists of all storage pools added to the commcell

                    {
                        &#34;storage_pool1_name&#34;: storage_pool1_id,

                        &#34;storage_pool2_name&#34;: storage_pool2_id
                    }

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        headers = self._commcell_object._headers.copy()
        headers[&#39;Accept&#39;] = &#39;application/xml&#39;

        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._storage_pools_api, headers=headers
        )

        if flag:
            storage_pools = {}

            response = xmltodict.parse(response.text)[&#39;Api_GetStoragePoolListResp&#39;]

            if response is None or response.get(&#39;storagePoolList&#39;) is None:
                storage_pool_list = []
            else:
                storage_pool_list = response[&#39;storagePoolList&#39;]

            if not isinstance(storage_pool_list, list):
                storage_pool_list = [storage_pool_list]

            if response:
                for pool in storage_pool_list:
                    name = pool[&#39;storagePoolEntity&#39;][&#39;@storagePoolName&#39;].lower()
                    storage_pool_id = pool[&#39;storagePoolEntity&#39;][&#39;@storagePoolId&#39;]

                    storage_pools[name] = storage_pool_id

                return storage_pools
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
    
    def get_storage_pools_for_a_company(self, company_id, storage_type: StorageType = None):
        &#34;&#34;&#34;Gets all the storage pools associated with the Commcell environment.

            Args:
                company_id - id of the company for which the associated storge pools are to be fetched

            Returns:
                dict    -   consists of all storage pools added to the commcell

                    {
                        &#34;storage_pool1_name&#34;: storage_pool1_id,

                        &#34;storage_pool2_name&#34;: storage_pool2_id
                    }

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        headers = self._commcell_object._headers.copy()
        headers[&#39;Accept&#39;] = &#39;application/json&#39;
        headers[&#39;onlygetcompanyownedentities&#39;] = &#39;1&#39;
        headers[&#39;operatorcompanyid&#39;] = f&#39;{company_id}&#39;

        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._storage_pools_api, headers=headers
        )

        if flag:
            storage_pools = {}
            response = response.json()
            if response is None or response.get(&#39;storagePoolList&#39;) is None:
                storage_pool_list = []
            else:
                storage_pool_list = response[&#39;storagePoolList&#39;]
            if not isinstance(storage_pool_list, list):
                storage_pool_list = [storage_pool_list]
            if response:
                for pool in storage_pool_list:
                    if storage_type and pool[&#39;storageType&#39;] != storage_type:
                        continue
                    # skip agp pools for cloud storage type
                    if storage_type == StorageType.CLOUD and 401 &lt;= pool[&#39;libraryVendorType&#39;] &lt;= 499:
                        continue
                    name = pool[&#39;storagePoolEntity&#39;][&#39;storagePoolName&#39;]
                    storage_pool_id = pool[&#39;storagePoolEntity&#39;][&#39;storagePoolId&#39;]

                    storage_pools[name] = storage_pool_id

            return storage_pools
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def all_storage_pools(self):
        &#34;&#34;&#34;Returns dict of all the storage pools on this commcell

            dict    -   consists of all storage pools added to the commcell

                {

                    &#34;storage_pool1_name&#34;: storage_pool1_id,

                    &#34;storage_pool2_name&#34;: storage_pool2_id
                }

        &#34;&#34;&#34;
        return self._storage_pools

    def has_storage_pool(self, name):
        &#34;&#34;&#34;Checks if a storage pool exists in the Commcell with the input storage pool name.

            Args:
                name    (str)   --  name of the storage pool

            Returns:
                bool    -   boolean output whether the storage pool exists in the commcell or not

        &#34;&#34;&#34;
        return self._storage_pools and name.lower() in self._storage_pools

    def get(self, name):
        &#34;&#34;&#34;Returns the id of the storage pool for the given storage pool name.

            Args:
                name    (str)   --  name of the storage pool to get the id of

            Returns:
                str     -   id of the storage pool for the given storage pool name

            Raises:
                SDKException:
                    if no storage pool exists with the given name

        &#34;&#34;&#34;
        self.refresh()
        name = name.lower()

        if self.has_storage_pool(name):
            return StoragePool(self._commcell_object, name, storage_pool_id=self._storage_pools[name])
        else:
            raise SDKException(&#39;StoragePool&#39;, &#39;103&#39;)

    def hyperscale_create_storage_pool(self, storage_pool_name, media_agents):
        &#34;&#34;&#34;
            Create new storage pool for hyperscale
            Args:
                storage_pool_name (string) -- Name of the storage pools to create

                media_agents      (List)   -- List of 3 media agents with name&#39;s(str)
                                                or instance of media agent&#39;s(object)

                Example: [&#34;ma1&#34;,&#34;ma2&#34;,&#34;ma3&#34;]

            Return:
                 flag, response -- response returned by the REST API call
        &#34;&#34;&#34;

        if not isinstance(media_agents, list):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)
        if not isinstance(storage_pool_name, str):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        mediagent_obj = []
        for media_agent in media_agents:
            if isinstance(media_agent, MediaAgent):
                mediagent_obj.append(media_agent)
            elif isinstance(media_agent, str):
                mediagent_obj.append(self._commcell_object.media_agents.get(media_agent))
            else:
                raise SDKException(&#39;Storage&#39;, &#39;103&#39;)
        if len(mediagent_obj) &lt;= 2:
            raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#34;minimum 3 media agents are required&#34;)

        request_xml = &#34;&#34;&#34;&lt;App_CreateStoragePolicyReq storagePolicyName=&#34;{0}&#34; copyName=&#34;{0}_Primary&#34; type=&#34;1&#34;
                                     numberOfCopies=&#34;1&#34;&gt;
                                    &lt;storagePolicyCopyInfo&gt;
                                        &lt;storagePolicyFlags scaleOutStoragePolicy=&#34;1&#34;/&gt;
                                    &lt;/storagePolicyCopyInfo&gt;
                                    &lt;storage&gt;
                                        &lt;mediaAgent mediaAgentId=&#34;{4}&#34; mediaAgentName=&#34;{1}&#34; displayName=&#34;{1}&#34;/&gt;
                                    &lt;/storage&gt;
                                    &lt;storage&gt;
                                        &lt;mediaAgent mediaAgentId=&#34;{5}&#34; mediaAgentName=&#34;{2}&#34; displayName=&#34;{2}&#34;/&gt;
                                    &lt;/storage&gt;
                                    &lt;storage&gt;
                                        &lt;mediaAgent mediaAgentId=&#34;{6}&#34; mediaAgentName=&#34;{3}&#34; displayName=&#34;{3}&#34;/&gt;
                                    &lt;/storage&gt;
                                    &lt;scaleoutConfiguration configurationType=&#34;1&#34;/&gt;
                                &lt;/App_CreateStoragePolicyReq&gt;
                                &#34;&#34;&#34;.format(storage_pool_name, mediagent_obj[0].media_agent_name,
                                           mediagent_obj[1].media_agent_name, mediagent_obj[2].media_agent_name,
                                           mediagent_obj[0].media_agent_id, mediagent_obj[1].media_agent_id,
                                           mediagent_obj[2].media_agent_id)

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._add_storage_pool_api, request_xml
        )
        if flag:
            if response.json():
                error_code = response.json()[&#39;error&#39;][&#39;errorCode&#39;]

                if int(error_code) != 0:
                    error_message = response.json()[&#39;error&#39;][&#39;errorMessage&#39;]
                    o_str = &#39;Failed to create storage pool\nError: &#34;{0}&#34;&#39;

                    raise SDKException(&#39;StoragePool&#39;, &#39;102&#39;, o_str.format(error_message))
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        self.refresh()
        return self.get(storage_pool_name)

    def add_air_gap_protect(self, storage_pool_name, media_agent, storage_type, storage_class, region_name,
                            ddb_ma=None, dedup_path=None):
        &#34;&#34;&#34;
            Adds a new air gap protect storage pool to commcell

                Args:
                    storage_pool_name   (str)       --  name of new storage pool to add

                    media_agent         (str/object)--  name or instance of media agent

                    storage_type        (str)        -- name of the cloud vendor (str, eg - &#34;Microsoft Azure storage&#34;) (same as UI)

                    storage_class       (str)        -- storage class (str, eg - &#34;Hot&#34;,&#34;Cool&#34;) (same as UI)

                    region_name (str)      --  name of the geographical region for storage (same as UI)

                    ddb_ma              (list&lt;str/object&gt;/str/object)   --  list of (name of name or instance)
                                                                            or name or instance of dedupe media agent

                    dedup_path          (list&lt;str&gt;/str)       --  list of paths or path where the DDB should be stored

                Returns:
                    StoragePool object if creation is successful

                Raises:
                    SDKException, if invalid parameters provided

        &#34;&#34;&#34;
        license_type_dict = StoragePoolConstants.AIR_GAP_PROTECT_STORAGE_TYPES
        error_message = &#34;&#34;
        if storage_type.upper() in license_type_dict:
            available_storage_classes = license_type_dict[storage_type.upper()]
            if storage_class.upper() in available_storage_classes:
                vendor_id = available_storage_classes[storage_class.upper()][&#34;vendorId&#34;]
                display_vendor_id = available_storage_classes[storage_class.upper()][&#34;displayVendorId&#34;]
            else:
                error_message += f&#34;Invalid storage class provided. Valid storage class {list(available_storage_classes.keys())}&#34;
        else:
            error_message += f&#34;  Invalid storage type provided. {list(license_type_dict.keys())}&#34;

        if error_message:
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;, error_message)

        region = None
        available_regions = []

        #  API call to fetch the region name - sourced directly from the vendor
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;GET&#39;, self._metallic_storage_api)
        if flag:
            if response.json():
                if &#34;storageInformation&#34; in response.json():
                    for storage_info in response.json()[&#34;storageInformation&#34;]:
                        if (int(storage_info[&#34;vendorId&#34;]) == int(vendor_id)) and (int(storage_info[&#34;displayVendorId&#34;]) == int(display_vendor_id)):
                            for region_dict in storage_info[&#34;region&#34;]:
                                available_regions.append(region_dict[&#34;displayName&#34;])
                                if region_dict[&#34;displayName&#34;] == region_name:
                                    region = region_dict[&#34;regionName&#34;]
                                    break

                        if region:
                            break

                    if region is None:
                        if not available_regions:
                            raise SDKException(&#39;Storage&#39;, &#39;101&#39;,
                                               f&#34;Active license is required to configure {storage_type} - {storage_class} Air Gap Protect storage&#34;)
                        else:
                            raise SDKException(&#39;Storage&#39;, &#39;101&#39;,
                                               f&#34;Invalid region: {region_name} ,\nValid regions: {available_regions}&#34;)
                else:
                    raise SDKException(&#39;Storage&#39;, &#39;101&#39;, &#34;Unexpected response returned while fetching Air Gap Protect storage details&#34;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        # cloud server type for air gap protect is 400
        cloud_server_type = 400

        return self.add(storage_pool_name=storage_pool_name, mountpath=None, media_agent=media_agent, ddb_ma=ddb_ma,
                        dedup_path=dedup_path, cloud_server_type=cloud_server_type, region=region, vendor_id=vendor_id,
                        display_vendor_id=display_vendor_id)
        
    def get_air_gap_protect(self, company_id = None):
        &#34;&#34;&#34;
        Returns the list of air gap protect storage pools in the commcell.
        
        Args:
            company_id (int) -- id of the company to get the air gap protect storage pools for
                                (optional, default is None which returns all air gap protect storage pools)
        
        Returns:
            dict - dictionary of air gap protect storage pools with name as key and id as value
                
                    {
                        &#34;storage_pool1_name&#34;: storage_pool1_id,
                        &#34;storage_pool2_name&#34;: storage_pool2_id
                    }  
        
        Raises:
            SDKException:
                if response is empty

                if response is not success
        &#34;&#34;&#34;
        headers = self._commcell_object._headers.copy()
        headers[&#39;Accept&#39;] = &#39;application/json&#39;
        if company_id:
            headers[&#39;onlygetcompanyownedentities&#39;] = &#39;1&#39;
            headers[&#39;operatorcompanyid&#39;] = f&#39;{company_id}&#39;

        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self.__get_agp_storage_api, headers=headers
        )

        if flag:
            storage_pools = {}
            response = response.json()
            if response is None or response.get(&#39;cloudStorage&#39;) is None:
                storage_pool_list = []
            else:
                storage_pool_list = response[&#39;cloudStorage&#39;]
            if not isinstance(storage_pool_list, list):
                storage_pool_list = [storage_pool_list]
            if response:
                for pool in storage_pool_list:
                    name = pool[&#39;name&#39;]
                    storage_pool_id = pool[&#39;id&#39;]

                    storage_pools[name] = storage_pool_id

            return storage_pools
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def add(self, storage_pool_name, mountpath, media_agent, ddb_ma=None, dedup_path=None, **kwargs):
        &#34;&#34;&#34;
        Adds a new storage pool to commcell

        Args:
            storage_pool_name   (str)       --  name of new storage pool to add

            mountpath           (str)       --  mount path for the storage pool

            media_agent         (str/object)--  name or instance of media agent

            ddb_ma              (list&lt;str/object&gt;/str/object)   --  list of (name of name or instance)
                                                                        or name or instance of dedupe media agent

            dedup_path          (list&lt;str&gt;/str)       --  list of paths or path where the DDB should be stored

            **kwargs:
                username        (str)       --  username to access the mountpath

                password        (str)       --  password to access the mountpath

                credential_name (str)       --  name of the credential as in credential manager

                cloud_server_type (int)     --  cloud server type of the cloud vendor (required)

                region (str)                --  name of geographical region for storage (required for air gap protect)

                vendor_id (int)             -- id for the cloud_vendor (eg - 3 for azure) (required for air gap protect pool)

                display_vendor_id (int)     -- storage Class id for that vendor (eg - 401 for azure hot) (required for air gap protect pool)

                region_id        (int)      --  Cloud Hypervisor specific region ID

                tape_storage (boolean)      -- if library passed is tape library. 

        Returns:
            StoragePool object if creation is successful

        Raises:
            Exception if creation is unsuccessful
        &#34;&#34;&#34;
        username = kwargs.get(&#39;username&#39;, None)
        password = kwargs.get(&#39;password&#39;, None)
        credential_name = kwargs.get(&#39;credential_name&#39;, None)
        cloud_server_type = kwargs.get(&#39;cloud_server_type&#39;, None)
        library_name = kwargs.get(&#39;library_name&#39;, None)
        tape_storage = False

        region = kwargs.get(&#39;region&#39;, None)
        vendor_id = kwargs.get(&#39;vendor_id&#39;, None)
        display_vendor_id = kwargs.get(&#39;display_vendor_id&#39;, None)
        region_id = kwargs.get(&#39;region_id&#39;, None)

        if library_name:
            library_object = self._commcell_object.disk_libraries.get(library_name)
            library_type = library_object.library_properties.get(&#39;libraryType&#39;, None)
            tape_storage = True if library_type == 1 else tape_storage


        if ((ddb_ma is not None and not (isinstance(dedup_path, str) or isinstance(dedup_path, list))) or
                not (isinstance(storage_pool_name, str) or not isinstance(mountpath, str))):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        if isinstance(media_agent, MediaAgent):
            media_agent = media_agent
        elif isinstance(media_agent, str):
            media_agent = MediaAgent(self._commcell_object, media_agent)
        else:
            raise SDKException(&#39;Storage&#39;, &#39;103&#39;)

        if (isinstance(ddb_ma, str) or isinstance(ddb_ma, MediaAgent)) and isinstance(dedup_path, str):
            ddb_ma = [ddb_ma]
            dedup_path = [dedup_path]

        if isinstance(ddb_ma, list) and isinstance(dedup_path, list):
            if len(ddb_ma) != len(dedup_path):
                raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        if library_name is not None and mountpath != &#39;&#39;:
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        if ddb_ma is not None and (len(ddb_ma) &gt; 6 or len(dedup_path) &gt; 6):
            raise SDKException(&#39;Storage&#39;, &#39;110&#39;)

        if ddb_ma is not None:
            for i in range(len(ddb_ma)):
                if isinstance(ddb_ma[i], MediaAgent):
                    ddb_ma[i] = ddb_ma[i]
                elif isinstance(ddb_ma[i], str):
                    ddb_ma[i] = MediaAgent(self._commcell_object, ddb_ma[i])
                else:
                    raise SDKException(&#39;Storage&#39;, &#39;103&#39;)

        request_json = {
            &#34;storagePolicyName&#34;: storage_pool_name,
            &#34;type&#34;: &#34;CVA_REGULAR_SP&#34;,
            &#34;copyName&#34;: &#34;Primary&#34;,
            &#34;numberOfCopies&#34;: 1,
            &#34;storage&#34;: [
                {
                    &#34;path&#34;: mountpath,
                    &#34;mediaAgent&#34;: {
                        &#34;mediaAgentId&#34;: int(media_agent.media_agent_id),
                        &#34;mediaAgentName&#34;: media_agent.media_agent_name
                    }
                }
            ],
            &#34;storagePolicyCopyInfo&#34;: {
                &#34;copyType&#34;: &#34;SYNCHRONOUS&#34;,
                &#34;isFromGui&#34;: True,
                &#34;active&#34;: &#34;SET_TRUE&#34;,
                &#34;isDefault&#34;: &#34;SET_TRUE&#34;,
                &#34;numberOfStreamsToCombine&#34;: 1,
                &#34;retentionRules&#34;: {
                    &#34;retentionFlags&#34;: {
                        &#34;enableDataAging&#34;: &#34;SET_TRUE&#34;
                    },
                    &#34;retainBackupDataForDays&#34;: -1,
                    &#34;retainBackupDataForCycles&#34;: -1,
                    &#34;retainArchiverDataForDays&#34;: -1
                },
                &#34;library&#34;: {
                    &#34;libraryId&#34;: 0,
                },
                &#34;mediaAgent&#34;: {
                    &#34;mediaAgentId&#34;: int(media_agent.media_agent_id),
                    &#34;mediaAgentName&#34;: media_agent.media_agent_name
                }
            }
        }

        if cloud_server_type and int(cloud_server_type) &gt; 0:
            request_json[&#34;storage&#34;][0][&#34;deviceType&#34;] = cloud_server_type

        if region_id is not None:
            request_json[&#34;storage&#34;][0][&#34;metallicStorageInfo&#34;] = {
                &#34;region&#34;: [
                    {
                        &#34;regionId&#34;: region_id
                    }
                ],
                &#34;storageClass&#34;: [
                    &#34;CONTAINER_DEFAULT&#34;
                ],
                &#34;replication&#34;: [
                    &#34;NONE&#34;
                ]
            }
            request_json[&#34;region&#34;] = {&#34;regionId&#34;: region_id}

        if username is not None:
            request_json[&#34;storage&#34;][0][&#34;credentials&#34;] = {&#34;userName&#34;: username}

        if password is not None:
            request_json[&#34;storage&#34;][0][&#34;credentials&#34;][&#34;password&#34;] = b64encode(password.encode()).decode()

        if credential_name is not None:
            request_json[&#34;storage&#34;][0][&#34;savedCredential&#34;] = {&#34;credentialName&#34;: credential_name}

        if library_name is not None:
            request_json[&#34;storage&#34;] = []
            request_json[&#34;storagePolicyCopyInfo&#34;][&#34;library&#34;][&#34;libraryName&#34;] = library_name

        if ddb_ma is not None or dedup_path is not None:
            maInfoList = []
            for ma, path in zip(ddb_ma, dedup_path):
                maInfoList.append({
                    &#34;mediaAgent&#34;: {
                        &#34;mediaAgentId&#34;: int(ma.media_agent_id),
                        &#34;mediaAgentName&#34;: ma.media_agent_name
                    },
                    &#34;subStoreList&#34;: [
                        {
                            &#34;accessPath&#34;: {
                                &#34;path&#34;: path
                            },
                            &#34;diskFreeThresholdMB&#34;: 5120,
                            &#34;diskFreeWarningThreshholdMB&#34;: 10240
                        }]
                })

            request_json[&#34;storagePolicyCopyInfo&#34;].update({
                &#34;storagePolicyFlags&#34;: {
                    &#34;blockLevelDedup&#34;: &#34;SET_TRUE&#34;,
                    &#34;enableGlobalDeduplication&#34;: &#34;SET_TRUE&#34;
                },
                &#34;dedupeFlags&#34;: {
                    &#34;enableDeduplication&#34;: &#34;SET_TRUE&#34;,
                    &#34;enableDASHFull&#34;: &#34;SET_TRUE&#34;,
                    &#34;hostGlobalDedupStore&#34;: &#34;SET_TRUE&#34;
                },
                &#34;DDBPartitionInfo&#34;: {
                    &#34;maInfoList&#34;: maInfoList
                }
            })
        elif tape_storage:
            request_json[&#34;storagePolicyCopyInfo&#34;].update({
                &#34;storagePolicyFlags&#34;: {
                    &#34;globalAuxCopyPolicy&#34;: &#34;SET_TRUE&#34;
                },
                &#34;copyFlags&#34;: {
                    &#34;preserveEncryptionModeAsInSource&#34;: &#34;SET_TRUE&#34;
                },
                &#34;extendedFlags&#34;: {
                    &#34;globalAuxCopyPolicy&#34;: &#34;SET_TRUE&#34;
                }
            })
        else:
            request_json[&#34;storagePolicyCopyInfo&#34;].update({
                &#34;storagePolicyFlags&#34;: {
                    &#34;globalStoragePolicy&#34;: &#34;SET_TRUE&#34;
                },
                &#34;copyFlags&#34;: {
                    &#34;preserveEncryptionModeAsInSource&#34;: &#34;SET_TRUE&#34;
                },
                &#34;extendedFlags&#34;: {
                    &#34;globalStoragePolicy&#34;: &#34;SET_TRUE&#34;
                }
            })

        # air gap protect storage
        if cloud_server_type == 400:
            del request_json[&#34;storage&#34;][0][&#34;path&#34;]
            request_json[&#34;storage&#34;][0][&#34;savedCredential&#34;] = {&#34;credentialId&#34;: 0}

            metallic_Storage = {
                &#34;region&#34;: [
                    {
                        &#34;regionName&#34;: region
                    }
                ],
                &#34;displayVendorId&#34;: display_vendor_id,
                &#34;vendorId&#34;: vendor_id
            }
            request_json[&#34;storage&#34;][0][&#34;metallicStorageInfo&#34;] = metallic_Storage

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._add_storage_pool_api, request_json
        )

        if flag:
            if response.json():
                error_code = response.json()[&#39;error&#39;][&#39;errorCode&#39;]

                if int(error_code) != 0:
                    error_message = response.json()[&#39;error&#39;][&#39;errorMessage&#39;]
                    o_str = &#39;Failed to create storage pool\nError: &#34;{0}&#34;&#39;

                    raise SDKException(&#39;StoragePool&#39;, &#39;102&#39;, o_str.format(error_message))
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        self.refresh()
        self._commcell_object.disk_libraries.refresh()
        return self.get(storage_pool_name)

    def delete(self, storage_pool_name):
        &#34;&#34;&#34;deletes the specified storage pool.

            Args:
                storage_pool_name (str)  --  name of the storage pool to delete

            Raises:
                SDKException:
                    if type of the storage pool name is not string

                    if failed to delete storage pool

                    if no storage pool exists with the given name

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;

        if not isinstance(storage_pool_name, str):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)
        else:
            storage_pool_name = storage_pool_name.lower()

            if self.has_storage_pool(storage_pool_name):
                storage_pool_id = self._storage_pools[storage_pool_name]

                delete_storage_pool = self._services[&#39;DELETE_STORAGE_POOL&#39;] % (storage_pool_id)

                flag, response = self._cvpysdk_object.make_request(&#39;DELETE&#39;, delete_storage_pool)

                if flag:
                    error_code = response.json()[&#39;error&#39;][&#39;errorCode&#39;]
                    if int(error_code) != 0:
                        error_message = response.json()[&#39;error&#39;][&#39;errorMessage&#39;]
                        o_str = f&#39;Failed to delete storage pools {storage_pool_name}&#39;
                        o_str += &#39;\nError: &#34;{0}&#34;&#39;.format(error_message)
                        raise SDKException(&#39;Storage&#39;, &#39;102&#39;, o_str)
                    else:
                        # initialize the storage pool again
                        # so the storage pool object has all the storage pools
                        self.refresh()
                        # as part of storage pool we might delete library so initialize the libraries again
                        self._commcell_object.disk_libraries.refresh()
                else:
                    response_string = self._update_response_(response.text)
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
            else:
                raise SDKException(
                    &#39;Storage&#39;,
                    &#39;102&#39;,
                    &#39;No storage pool exists with name: {0}&#39;.format(storage_pool_name)
                )
    def refresh(self):
        &#34;&#34;&#34;Refresh the list of storage pools associated to the Commcell.&#34;&#34;&#34;
        self._storage_pools = self._get_storage_pools()

class StoragePoolType(IntEnum):
    &#34;&#34;&#34;Class IntEnum to represent different storage pool types&#34;&#34;&#34;
    DEDUPLICATION = 1,
    SECONDARY_COPY = 2,
    NON_DEDUPLICATION = 3,
    SCALE_OUT = 4


class WORMLockType(IntFlag):
    &#34;&#34;&#34;Class IntFlag to represent different WORM lock types flag values&#34;&#34;&#34;
    COPY = 1,  # copy level software WORM (compliance lock)
    STORAGE = 2,  # storage level hardware WORM
    OBJECT = 4,  # object level storage WORM
    BUCKET = 8  # bucket level storage WORM


class StoragePool(object):
    &#34;&#34;&#34;Class for individual storage pools&#34;&#34;&#34;

    def __init__(self, commcell_object, storage_pool_name, storage_pool_id=None):
        &#34;&#34;&#34;
        Intitalise the Storage Pool classs instance

        Args:
            commcell_object     (object)        --instance of the Commcell class

            storage_pool_name   (string)    -- Name of the storage pool

            storage_pool_id     (int)       -- Storage pool id
        Returns:
            object - Instance of the StoragePool class

        &#34;&#34;&#34;
        self._storage_pool_name = storage_pool_name.lower()
        self._commcell_object = commcell_object
        self._storage_pool_properties = None
        self._storage_pool_id = None
        self._copy_id = None
        self._copy_name = None

        if storage_pool_id:
            self._storage_pool_id = str(storage_pool_id)
        else:
            self._storage_pool_id = self._commcell_object.storage_pools.get(self._storage_pool_name).storage_pool_id

        self._STORAGE_POOL = self._commcell_object._services[&#39;GET_STORAGE_POOL&#39;] % (self.storage_pool_id)
        self.refresh()

        self._copy_id = self._storage_pool_properties.get(&#34;storagePoolDetails&#34;, {}).get(&#34;copyInfo&#34;, {}).get(
            &#34;StoragePolicyCopy&#34;, {}).get(&#34;copyId&#34;)
        self._copy_name = self._storage_pool_properties.get(&#34;storagePoolDetails&#34;, {}).get(&#34;copyInfo&#34;, {}).get(
            &#34;StoragePolicyCopy&#34;, {}).get(&#34;copyName&#34;)

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class&#34;&#34;&#34;
        representation_string = &#34;Storage Pool class Instance for {0}&#34;.format(self._storage_pool_name)
        return representation_string

    def _get_storage_pool_properties(self):
        &#34;&#34;&#34;
        Gets StoragePool properties

            Raises:
                SDKException:
                    if repsonse is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;GET&#39;, self._STORAGE_POOL)

        if flag:
            if response.json():
                self._storage_pool_properties = response.json()
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def storage_pool_name(self):
        &#34;&#34;&#34;Treats the storage_policy_name as a read only attribute&#34;&#34;&#34;
        return self._storage_pool_name

    @property
    def storage_pool_id(self):
        &#34;&#34;&#34;Treats id as a read only attribute&#34;&#34;&#34;
        return self._storage_pool_id

    @property
    def storage_pool_properties(self):
        &#34;&#34;&#34;Treats the storage_pool_properties as a read only attribute&#34;&#34;&#34;
        return self._storage_pool_properties

    @property
    def global_policy_name(self):
        &#34;&#34;&#34;Returns the global policy corresponding to the storage pool&#34;&#34;&#34;
        return self._storage_pool_properties[&#34;storagePoolDetails&#34;][&#34;copyInfo&#34;][&#34;StoragePolicyCopy&#34;][&#34;storagePolicyName&#34;]

    @property
    def copy_name(self):
        &#34;&#34;&#34;Treats copy name as a read only attribute&#34;&#34;&#34;
        return self._copy_name

    @property
    def copy_id(self):
        &#34;&#34;&#34;Treats copy ID as a read only attribute&#34;&#34;&#34;
        return self._copy_id

    @property
    def storage_pool_type(self):
        &#34;&#34;&#34;Treats storage type as a read only attribute&#34;&#34;&#34;
        return self._storage_pool_properties[&#34;storagePoolDetails&#34;][&#34;storagePoolType&#34;]

    @property
    def storage_type(self):
        &#34;&#34;&#34;Treats storage type as a read only attribute&#34;&#34;&#34;
        return self._storage_pool_properties[&#34;storagePoolDetails&#34;][&#34;storageType&#34;]

    @property
    def storage_vendor(self):
        &#34;&#34;&#34;Treats library vendor like cloud storage provider as a read only attribute&#34;&#34;&#34;
        return self._storage_pool_properties[&#34;storagePoolDetails&#34;][&#34;libraryVendorId&#34;]

    @property
    def is_worm_storage_lock_enabled(self):
        &#34;&#34;&#34;Treats is worm enabled as a read only attribute&#34;&#34;&#34;
        return self._storage_pool_properties[&#34;storagePoolDetails&#34;][&#34;isWormStorage&#34;]

    @property
    def is_object_level_worm_lock_enabled(self):
        &#34;&#34;&#34;Treats is object WORM enabled as a read only attribute&#34;&#34;&#34;
        worm_flag = int(self._storage_pool_properties[&#34;storagePoolDetails&#34;][&#34;copyInfo&#34;][&#34;wormStorageFlag&#34;])
        return worm_flag &amp; WORMLockType.OBJECT == WORMLockType.OBJECT

    @property
    def is_bucket_level_worm_lock_enabled(self):
        &#34;&#34;&#34;Treats is bucket WORM enabled as a read only attribute&#34;&#34;&#34;
        worm_flag = int(self._storage_pool_properties[&#34;storagePoolDetails&#34;][&#34;copyInfo&#34;][&#34;wormStorageFlag&#34;])
        return worm_flag &amp; WORMLockType.BUCKET == WORMLockType.BUCKET

    @property
    def is_compliance_lock_enabled(self):
        &#34;&#34;&#34;Treats is compliance lock enabled as a read only attribute&#34;&#34;&#34;
        return self._storage_pool_properties[&#34;storagePoolDetails&#34;][&#34;copyInfo&#34;][&#34;copyFlags&#34;][&#34;wormCopy&#34;] == 1

    def get_copy(self):
        &#34;&#34;&#34; Returns the StoragePolicyCopy object of Storage Pool copy&#34;&#34;&#34;
        return StoragePolicyCopy(self._commcell_object, self.storage_pool_name, self.copy_name)

    def enable_compliance_lock(self):
        &#34;&#34;&#34; Enables compliance lock on Storage Pool Copy &#34;&#34;&#34;
        self.get_copy().enable_compliance_lock()
        self.refresh()

    def enable_worm_storage_lock(self, retain_days):
        &#34;&#34;&#34;
        Enable storage WORM lock on storage pool

        Args:
            retain_days    (int)   -- number of days of retention on WORM copy.

        Raises:
            SDKException:
                if response is not success.

                if reponse is empty.
        &#34;&#34;&#34;

        request_json = {
            &#34;storagePolicyCopyInfo&#34;: {
                &#34;copyFlags&#34;: {
                    &#34;wormCopy&#34;: 1
                },
                &#34;retentionRules&#34;: {
                    &#34;retainBackupDataForDays&#34;: retain_days
                }
            },
            &#34;isWormStorage&#34;: True,
            &#34;forceCopyToFollowPoolRetention&#34;: True
        }

        _STORAGE_POOL_COPY = self._commcell_object._services[&#39;STORAGE_POLICY_COPY&#39;] % (
            self._storage_pool_id, str(self.copy_id))
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;PUT&#39;, _STORAGE_POOL_COPY, request_json)

        if flag:
            if response.json():
                response = response.json()
                if &#34;error&#34; in response and response.get(&#34;error&#34;, {}).get(&#34;errorCode&#34;) != 0:
                    error_message = response.get(&#34;error&#34;, {}).get(&#34;errorMessage&#34;)
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;, error_message)
                else:
                    self.refresh()
            else:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def hyperscale_add_nodes(self, media_agents):
        &#34;&#34;&#34;
        Add 3 new nodes to an existing storage pool

        args:
            media_agents      (List)   -- List of 3 media agents with name&#39;s(str)
                                            or instance of media agent&#39;s(object)

            Example: [&#34;ma1&#34;,&#34;ma2&#34;,&#34;ma3&#34;]

        Raises:
                SDKException:
                    if add nodes to an existing storage pool fails
        &#34;&#34;&#34;
        if not isinstance(media_agents, list):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        mediagent_obj = []
        for media_agent in media_agents:
            if isinstance(media_agent, MediaAgent):
                mediagent_obj.append(media_agent)
            elif isinstance(media_agent, str):
                mediagent_obj.append(self._commcell_object.media_agents.get(media_agent))
            else:
                raise SDKException(&#39;Storage&#39;, &#39;103&#39;)

        if len(mediagent_obj) &lt;= 2:
            raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#34;Minimum 3 MediaAgents required&#34;)

        request_json = {
            &#34;scaleoutOperationType&#34;: 2,
            &#34;StoragePolicy&#34;: {
                &#34;storagePolicyName&#34;: &#34;{0}&#34;.format(self.storage_pool_name),
            },
            &#34;storage&#34;: [
                {
                    &#34;mediaAgent&#34;: {
                        &#34;displayName&#34;: &#34;{0}&#34;.format(mediagent_obj[0].media_agent_id),
                        &#34;mediaAgentName&#34;: &#34;{0}&#34;.format(mediagent_obj[0].media_agent_name)
                    }
                },
                {
                    &#34;mediaAgent&#34;: {
                        &#34;displayName&#34;: &#34;{0}&#34;.format(mediagent_obj[1].media_agent_id),
                        &#34;mediaAgentName&#34;: &#34;{0}&#34;.format(mediagent_obj[1].media_agent_name)
                    }
                },
                {
                    &#34;mediaAgent&#34;: {
                        &#34;displayName&#34;: &#34;{0}&#34;.format(mediagent_obj[2].media_agent_id),
                        &#34;mediaAgentName&#34;: &#34;{0}&#34;.format(mediagent_obj[2].media_agent_name)
                    }
                }
            ],
            &#34;scaleoutConfiguration&#34;: {
                &#34;configurationType&#34;: 1
            }
        }

        self._edit_storage_pool_api = self._commcell_object._services[
            &#39;EDIT_STORAGE_POOL&#39;]

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._edit_storage_pool_api, request_json
        )

        if flag:
            if response.json():
                error_code = response.json()[&#39;errorCode&#39;]

                if int(error_code) != 0:
                    error_message = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Failed to add nodes to storage pool\nError: &#34;{0}&#34;&#39;

                    raise SDKException(&#39;StoragePool&#39;, &#39;102&#39;, o_str.format(error_message))
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        self.refresh()

    def hyperscale_reconfigure_storage_pool(self, storage_pool_name):
        &#34;&#34;&#34;
        Reconfigures storage pool, for any failure during creation and expansion

        args:
          storage_pool_name (string) -- Name of the storage pools to reconfigure
        Raises:
                SDKException:
                    if reconfigure fails
        &#34;&#34;&#34;
        if not isinstance(storage_pool_name, str):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        request_json = {

            &#34;scaleoutOperationType&#34;: 4,
            &#34;StoragePolicy&#34;:
                {
                    &#34;storagePolicyName&#34;: &#34;{0}&#34;.format(storage_pool_name),
                    &#34;storagePolicyId&#34;: int(&#34;{0}&#34;.format(self.storage_pool_id))

                }
        }

        self._edit_storage_pool_api = self._commcell_object._services[
            &#39;EDIT_STORAGE_POOL&#39;]

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._edit_storage_pool_api, request_json
        )

        if flag:
            if response.json():
                error_code = response.json()[&#39;errorCode&#39;]

                if int(error_code) != 0:
                    error_message = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Failed to reconfigure storage pool\nError: &#34;{0}&#34;&#39;

                    raise SDKException(&#39;StoragePool&#39;, &#39;102&#39;, o_str.format(error_message))
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        self.refresh()

    def hyperscale_replace_disk(self, disk_id, media_agent, storage_pool_name):
        &#34;&#34;&#34;
              Replace disk action, over a media agent which is part of storage pool
               args:
                    disk_id (int) --&gt; disk id for the disk to replace
                    media_agent (string/object) --&gt; media agent name/ object
                    storage_pool_name (string) --&gt; Name of the storage pools for replacemnet of disk
               Raises:
                       SDKException:
                           if replace fails
               &#34;&#34;&#34;
        if isinstance(disk_id, str):
            disk_id = int(disk_id)
        elif not isinstance(disk_id, int):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        media_agent_obj = None
        if isinstance(media_agent, str):
            media_agent_obj = self._commcell_object.media_agents.get(media_agent)
        elif isinstance(media_agent, MediaAgent):
            media_agent_obj = media_agent
        else:
            raise SDKException(&#39;Storage&#39;, &#39;103&#39;)

        if not isinstance(storage_pool_name, str):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        request_json = {

            &#34;driveId&#34;: int(&#34;{0}&#34;.format(disk_id)),
            &#34;operationType&#34;: 1,
            &#34;mediaAgent&#34;: {
                &#34;_type_&#34;: 11,
                &#34;mediaAgentId&#34;: int(&#34;{0}&#34;.format(media_agent_obj.media_agent_id)),
                &#34;mediaAgentName&#34;: &#34;{0}&#34;.format(media_agent_obj.media_agent_name)
            },
            &#34;scaleoutStoragePool&#34;: {
                &#34;_type_&#34;: 160,
                &#34;storagePoolId&#34;: int(&#34;{0}&#34;.format(self.storage_pool_id)),
                &#34;storagePoolName&#34;: &#34;{0}&#34;.format(self.storage_pool_name)
            }
        }

        self._replace_disk_storage_pool_api = self._commcell_object._services[
            &#39;REPLACE_DISK_STORAGE_POOL&#39;]

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._replace_disk_storage_pool_api, request_json
        )

        if flag:
            if response.json():
                error_code = response.json()[&#39;errorCode&#39;]

                if int(error_code) != 0:
                    error_message = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Failed to replace disk\nError: &#34;{0}&#34;&#39;

                    raise SDKException(&#39;StoragePool&#39;, &#39;102&#39;, o_str.format(error_message))
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        self.refresh()

    def refresh(self):
        &#34;&#34;&#34;Refreshes propery of the class object&#34;&#34;&#34;
        self._get_storage_pool_properties()

    def update_security_associations(self, associations_list, isUser=True, request_type=None, externalGroup=False):
        &#34;&#34;&#34;
        Adds the security association on the storage pool object

        Args:
            associations_list   (list)  --  list of users to be associated
                Example:
                    associations_list = [
                        {
                            &#39;user_name&#39;: user1,
                            &#39;role_name&#39;: role1
                        },
                        {
                            &#39;user_name&#39;: user2,
                            &#39;role_name&#39;: role2
                        }
                    ]

            isUser (bool)           --    True or False. set isUser = False, If associations_list made up of user groups
            request_type (str)      --    eg : &#39;OVERWRITE&#39; or &#39;UPDATE&#39; or &#39;DELETE&#39;, Default will be OVERWRITE operation
            externalGroup (bool)    --    True or False, set externalGroup = True. If Security associations is being done on External User Groups

        Raises:
            SDKException:
                if association is not of List type
        &#34;&#34;&#34;
        if not isinstance(associations_list, list):
            raise SDKException(&#39;StoragePool&#39;, &#39;101&#39;)

        SecurityAssociation(self._commcell_object, self)._add_security_association(associations_list,
                                                                                   user=isUser,
                                                                                   request_type=request_type,
                                                                                   externalGroup=externalGroup)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.storage_pool.StoragePool"><code class="flex name class">
<span>class <span class="ident">StoragePool</span></span>
<span>(</span><span>commcell_object, storage_pool_name, storage_pool_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for individual storage pools</p>
<p>Intitalise the Storage Pool classs instance</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;instance of the Commcell class</p>
<p>storage_pool_name
(string)
&ndash; Name of the storage pool</p>
<p>storage_pool_id
(int)
&ndash; Storage pool id</p>
<h2 id="returns">Returns</h2>
<p>object - Instance of the StoragePool class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage_pool.py#L922-L1341" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class StoragePool(object):
    &#34;&#34;&#34;Class for individual storage pools&#34;&#34;&#34;

    def __init__(self, commcell_object, storage_pool_name, storage_pool_id=None):
        &#34;&#34;&#34;
        Intitalise the Storage Pool classs instance

        Args:
            commcell_object     (object)        --instance of the Commcell class

            storage_pool_name   (string)    -- Name of the storage pool

            storage_pool_id     (int)       -- Storage pool id
        Returns:
            object - Instance of the StoragePool class

        &#34;&#34;&#34;
        self._storage_pool_name = storage_pool_name.lower()
        self._commcell_object = commcell_object
        self._storage_pool_properties = None
        self._storage_pool_id = None
        self._copy_id = None
        self._copy_name = None

        if storage_pool_id:
            self._storage_pool_id = str(storage_pool_id)
        else:
            self._storage_pool_id = self._commcell_object.storage_pools.get(self._storage_pool_name).storage_pool_id

        self._STORAGE_POOL = self._commcell_object._services[&#39;GET_STORAGE_POOL&#39;] % (self.storage_pool_id)
        self.refresh()

        self._copy_id = self._storage_pool_properties.get(&#34;storagePoolDetails&#34;, {}).get(&#34;copyInfo&#34;, {}).get(
            &#34;StoragePolicyCopy&#34;, {}).get(&#34;copyId&#34;)
        self._copy_name = self._storage_pool_properties.get(&#34;storagePoolDetails&#34;, {}).get(&#34;copyInfo&#34;, {}).get(
            &#34;StoragePolicyCopy&#34;, {}).get(&#34;copyName&#34;)

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class&#34;&#34;&#34;
        representation_string = &#34;Storage Pool class Instance for {0}&#34;.format(self._storage_pool_name)
        return representation_string

    def _get_storage_pool_properties(self):
        &#34;&#34;&#34;
        Gets StoragePool properties

            Raises:
                SDKException:
                    if repsonse is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;GET&#39;, self._STORAGE_POOL)

        if flag:
            if response.json():
                self._storage_pool_properties = response.json()
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def storage_pool_name(self):
        &#34;&#34;&#34;Treats the storage_policy_name as a read only attribute&#34;&#34;&#34;
        return self._storage_pool_name

    @property
    def storage_pool_id(self):
        &#34;&#34;&#34;Treats id as a read only attribute&#34;&#34;&#34;
        return self._storage_pool_id

    @property
    def storage_pool_properties(self):
        &#34;&#34;&#34;Treats the storage_pool_properties as a read only attribute&#34;&#34;&#34;
        return self._storage_pool_properties

    @property
    def global_policy_name(self):
        &#34;&#34;&#34;Returns the global policy corresponding to the storage pool&#34;&#34;&#34;
        return self._storage_pool_properties[&#34;storagePoolDetails&#34;][&#34;copyInfo&#34;][&#34;StoragePolicyCopy&#34;][&#34;storagePolicyName&#34;]

    @property
    def copy_name(self):
        &#34;&#34;&#34;Treats copy name as a read only attribute&#34;&#34;&#34;
        return self._copy_name

    @property
    def copy_id(self):
        &#34;&#34;&#34;Treats copy ID as a read only attribute&#34;&#34;&#34;
        return self._copy_id

    @property
    def storage_pool_type(self):
        &#34;&#34;&#34;Treats storage type as a read only attribute&#34;&#34;&#34;
        return self._storage_pool_properties[&#34;storagePoolDetails&#34;][&#34;storagePoolType&#34;]

    @property
    def storage_type(self):
        &#34;&#34;&#34;Treats storage type as a read only attribute&#34;&#34;&#34;
        return self._storage_pool_properties[&#34;storagePoolDetails&#34;][&#34;storageType&#34;]

    @property
    def storage_vendor(self):
        &#34;&#34;&#34;Treats library vendor like cloud storage provider as a read only attribute&#34;&#34;&#34;
        return self._storage_pool_properties[&#34;storagePoolDetails&#34;][&#34;libraryVendorId&#34;]

    @property
    def is_worm_storage_lock_enabled(self):
        &#34;&#34;&#34;Treats is worm enabled as a read only attribute&#34;&#34;&#34;
        return self._storage_pool_properties[&#34;storagePoolDetails&#34;][&#34;isWormStorage&#34;]

    @property
    def is_object_level_worm_lock_enabled(self):
        &#34;&#34;&#34;Treats is object WORM enabled as a read only attribute&#34;&#34;&#34;
        worm_flag = int(self._storage_pool_properties[&#34;storagePoolDetails&#34;][&#34;copyInfo&#34;][&#34;wormStorageFlag&#34;])
        return worm_flag &amp; WORMLockType.OBJECT == WORMLockType.OBJECT

    @property
    def is_bucket_level_worm_lock_enabled(self):
        &#34;&#34;&#34;Treats is bucket WORM enabled as a read only attribute&#34;&#34;&#34;
        worm_flag = int(self._storage_pool_properties[&#34;storagePoolDetails&#34;][&#34;copyInfo&#34;][&#34;wormStorageFlag&#34;])
        return worm_flag &amp; WORMLockType.BUCKET == WORMLockType.BUCKET

    @property
    def is_compliance_lock_enabled(self):
        &#34;&#34;&#34;Treats is compliance lock enabled as a read only attribute&#34;&#34;&#34;
        return self._storage_pool_properties[&#34;storagePoolDetails&#34;][&#34;copyInfo&#34;][&#34;copyFlags&#34;][&#34;wormCopy&#34;] == 1

    def get_copy(self):
        &#34;&#34;&#34; Returns the StoragePolicyCopy object of Storage Pool copy&#34;&#34;&#34;
        return StoragePolicyCopy(self._commcell_object, self.storage_pool_name, self.copy_name)

    def enable_compliance_lock(self):
        &#34;&#34;&#34; Enables compliance lock on Storage Pool Copy &#34;&#34;&#34;
        self.get_copy().enable_compliance_lock()
        self.refresh()

    def enable_worm_storage_lock(self, retain_days):
        &#34;&#34;&#34;
        Enable storage WORM lock on storage pool

        Args:
            retain_days    (int)   -- number of days of retention on WORM copy.

        Raises:
            SDKException:
                if response is not success.

                if reponse is empty.
        &#34;&#34;&#34;

        request_json = {
            &#34;storagePolicyCopyInfo&#34;: {
                &#34;copyFlags&#34;: {
                    &#34;wormCopy&#34;: 1
                },
                &#34;retentionRules&#34;: {
                    &#34;retainBackupDataForDays&#34;: retain_days
                }
            },
            &#34;isWormStorage&#34;: True,
            &#34;forceCopyToFollowPoolRetention&#34;: True
        }

        _STORAGE_POOL_COPY = self._commcell_object._services[&#39;STORAGE_POLICY_COPY&#39;] % (
            self._storage_pool_id, str(self.copy_id))
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;PUT&#39;, _STORAGE_POOL_COPY, request_json)

        if flag:
            if response.json():
                response = response.json()
                if &#34;error&#34; in response and response.get(&#34;error&#34;, {}).get(&#34;errorCode&#34;) != 0:
                    error_message = response.get(&#34;error&#34;, {}).get(&#34;errorMessage&#34;)
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;, error_message)
                else:
                    self.refresh()
            else:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def hyperscale_add_nodes(self, media_agents):
        &#34;&#34;&#34;
        Add 3 new nodes to an existing storage pool

        args:
            media_agents      (List)   -- List of 3 media agents with name&#39;s(str)
                                            or instance of media agent&#39;s(object)

            Example: [&#34;ma1&#34;,&#34;ma2&#34;,&#34;ma3&#34;]

        Raises:
                SDKException:
                    if add nodes to an existing storage pool fails
        &#34;&#34;&#34;
        if not isinstance(media_agents, list):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        mediagent_obj = []
        for media_agent in media_agents:
            if isinstance(media_agent, MediaAgent):
                mediagent_obj.append(media_agent)
            elif isinstance(media_agent, str):
                mediagent_obj.append(self._commcell_object.media_agents.get(media_agent))
            else:
                raise SDKException(&#39;Storage&#39;, &#39;103&#39;)

        if len(mediagent_obj) &lt;= 2:
            raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#34;Minimum 3 MediaAgents required&#34;)

        request_json = {
            &#34;scaleoutOperationType&#34;: 2,
            &#34;StoragePolicy&#34;: {
                &#34;storagePolicyName&#34;: &#34;{0}&#34;.format(self.storage_pool_name),
            },
            &#34;storage&#34;: [
                {
                    &#34;mediaAgent&#34;: {
                        &#34;displayName&#34;: &#34;{0}&#34;.format(mediagent_obj[0].media_agent_id),
                        &#34;mediaAgentName&#34;: &#34;{0}&#34;.format(mediagent_obj[0].media_agent_name)
                    }
                },
                {
                    &#34;mediaAgent&#34;: {
                        &#34;displayName&#34;: &#34;{0}&#34;.format(mediagent_obj[1].media_agent_id),
                        &#34;mediaAgentName&#34;: &#34;{0}&#34;.format(mediagent_obj[1].media_agent_name)
                    }
                },
                {
                    &#34;mediaAgent&#34;: {
                        &#34;displayName&#34;: &#34;{0}&#34;.format(mediagent_obj[2].media_agent_id),
                        &#34;mediaAgentName&#34;: &#34;{0}&#34;.format(mediagent_obj[2].media_agent_name)
                    }
                }
            ],
            &#34;scaleoutConfiguration&#34;: {
                &#34;configurationType&#34;: 1
            }
        }

        self._edit_storage_pool_api = self._commcell_object._services[
            &#39;EDIT_STORAGE_POOL&#39;]

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._edit_storage_pool_api, request_json
        )

        if flag:
            if response.json():
                error_code = response.json()[&#39;errorCode&#39;]

                if int(error_code) != 0:
                    error_message = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Failed to add nodes to storage pool\nError: &#34;{0}&#34;&#39;

                    raise SDKException(&#39;StoragePool&#39;, &#39;102&#39;, o_str.format(error_message))
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        self.refresh()

    def hyperscale_reconfigure_storage_pool(self, storage_pool_name):
        &#34;&#34;&#34;
        Reconfigures storage pool, for any failure during creation and expansion

        args:
          storage_pool_name (string) -- Name of the storage pools to reconfigure
        Raises:
                SDKException:
                    if reconfigure fails
        &#34;&#34;&#34;
        if not isinstance(storage_pool_name, str):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        request_json = {

            &#34;scaleoutOperationType&#34;: 4,
            &#34;StoragePolicy&#34;:
                {
                    &#34;storagePolicyName&#34;: &#34;{0}&#34;.format(storage_pool_name),
                    &#34;storagePolicyId&#34;: int(&#34;{0}&#34;.format(self.storage_pool_id))

                }
        }

        self._edit_storage_pool_api = self._commcell_object._services[
            &#39;EDIT_STORAGE_POOL&#39;]

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._edit_storage_pool_api, request_json
        )

        if flag:
            if response.json():
                error_code = response.json()[&#39;errorCode&#39;]

                if int(error_code) != 0:
                    error_message = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Failed to reconfigure storage pool\nError: &#34;{0}&#34;&#39;

                    raise SDKException(&#39;StoragePool&#39;, &#39;102&#39;, o_str.format(error_message))
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        self.refresh()

    def hyperscale_replace_disk(self, disk_id, media_agent, storage_pool_name):
        &#34;&#34;&#34;
              Replace disk action, over a media agent which is part of storage pool
               args:
                    disk_id (int) --&gt; disk id for the disk to replace
                    media_agent (string/object) --&gt; media agent name/ object
                    storage_pool_name (string) --&gt; Name of the storage pools for replacemnet of disk
               Raises:
                       SDKException:
                           if replace fails
               &#34;&#34;&#34;
        if isinstance(disk_id, str):
            disk_id = int(disk_id)
        elif not isinstance(disk_id, int):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        media_agent_obj = None
        if isinstance(media_agent, str):
            media_agent_obj = self._commcell_object.media_agents.get(media_agent)
        elif isinstance(media_agent, MediaAgent):
            media_agent_obj = media_agent
        else:
            raise SDKException(&#39;Storage&#39;, &#39;103&#39;)

        if not isinstance(storage_pool_name, str):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        request_json = {

            &#34;driveId&#34;: int(&#34;{0}&#34;.format(disk_id)),
            &#34;operationType&#34;: 1,
            &#34;mediaAgent&#34;: {
                &#34;_type_&#34;: 11,
                &#34;mediaAgentId&#34;: int(&#34;{0}&#34;.format(media_agent_obj.media_agent_id)),
                &#34;mediaAgentName&#34;: &#34;{0}&#34;.format(media_agent_obj.media_agent_name)
            },
            &#34;scaleoutStoragePool&#34;: {
                &#34;_type_&#34;: 160,
                &#34;storagePoolId&#34;: int(&#34;{0}&#34;.format(self.storage_pool_id)),
                &#34;storagePoolName&#34;: &#34;{0}&#34;.format(self.storage_pool_name)
            }
        }

        self._replace_disk_storage_pool_api = self._commcell_object._services[
            &#39;REPLACE_DISK_STORAGE_POOL&#39;]

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._replace_disk_storage_pool_api, request_json
        )

        if flag:
            if response.json():
                error_code = response.json()[&#39;errorCode&#39;]

                if int(error_code) != 0:
                    error_message = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Failed to replace disk\nError: &#34;{0}&#34;&#39;

                    raise SDKException(&#39;StoragePool&#39;, &#39;102&#39;, o_str.format(error_message))
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        self.refresh()

    def refresh(self):
        &#34;&#34;&#34;Refreshes propery of the class object&#34;&#34;&#34;
        self._get_storage_pool_properties()

    def update_security_associations(self, associations_list, isUser=True, request_type=None, externalGroup=False):
        &#34;&#34;&#34;
        Adds the security association on the storage pool object

        Args:
            associations_list   (list)  --  list of users to be associated
                Example:
                    associations_list = [
                        {
                            &#39;user_name&#39;: user1,
                            &#39;role_name&#39;: role1
                        },
                        {
                            &#39;user_name&#39;: user2,
                            &#39;role_name&#39;: role2
                        }
                    ]

            isUser (bool)           --    True or False. set isUser = False, If associations_list made up of user groups
            request_type (str)      --    eg : &#39;OVERWRITE&#39; or &#39;UPDATE&#39; or &#39;DELETE&#39;, Default will be OVERWRITE operation
            externalGroup (bool)    --    True or False, set externalGroup = True. If Security associations is being done on External User Groups

        Raises:
            SDKException:
                if association is not of List type
        &#34;&#34;&#34;
        if not isinstance(associations_list, list):
            raise SDKException(&#39;StoragePool&#39;, &#39;101&#39;)

        SecurityAssociation(self._commcell_object, self)._add_security_association(associations_list,
                                                                                   user=isUser,
                                                                                   request_type=request_type,
                                                                                   externalGroup=externalGroup)</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.storage_pool.StoragePool.copy_id"><code class="name">var <span class="ident">copy_id</span></code></dt>
<dd>
<div class="desc"><p>Treats copy ID as a read only attribute</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage_pool.py#L1011-L1014" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def copy_id(self):
    &#34;&#34;&#34;Treats copy ID as a read only attribute&#34;&#34;&#34;
    return self._copy_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage_pool.StoragePool.copy_name"><code class="name">var <span class="ident">copy_name</span></code></dt>
<dd>
<div class="desc"><p>Treats copy name as a read only attribute</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage_pool.py#L1006-L1009" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def copy_name(self):
    &#34;&#34;&#34;Treats copy name as a read only attribute&#34;&#34;&#34;
    return self._copy_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage_pool.StoragePool.global_policy_name"><code class="name">var <span class="ident">global_policy_name</span></code></dt>
<dd>
<div class="desc"><p>Returns the global policy corresponding to the storage pool</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage_pool.py#L1001-L1004" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def global_policy_name(self):
    &#34;&#34;&#34;Returns the global policy corresponding to the storage pool&#34;&#34;&#34;
    return self._storage_pool_properties[&#34;storagePoolDetails&#34;][&#34;copyInfo&#34;][&#34;StoragePolicyCopy&#34;][&#34;storagePolicyName&#34;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage_pool.StoragePool.is_bucket_level_worm_lock_enabled"><code class="name">var <span class="ident">is_bucket_level_worm_lock_enabled</span></code></dt>
<dd>
<div class="desc"><p>Treats is bucket WORM enabled as a read only attribute</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage_pool.py#L1042-L1046" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_bucket_level_worm_lock_enabled(self):
    &#34;&#34;&#34;Treats is bucket WORM enabled as a read only attribute&#34;&#34;&#34;
    worm_flag = int(self._storage_pool_properties[&#34;storagePoolDetails&#34;][&#34;copyInfo&#34;][&#34;wormStorageFlag&#34;])
    return worm_flag &amp; WORMLockType.BUCKET == WORMLockType.BUCKET</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage_pool.StoragePool.is_compliance_lock_enabled"><code class="name">var <span class="ident">is_compliance_lock_enabled</span></code></dt>
<dd>
<div class="desc"><p>Treats is compliance lock enabled as a read only attribute</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage_pool.py#L1048-L1051" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_compliance_lock_enabled(self):
    &#34;&#34;&#34;Treats is compliance lock enabled as a read only attribute&#34;&#34;&#34;
    return self._storage_pool_properties[&#34;storagePoolDetails&#34;][&#34;copyInfo&#34;][&#34;copyFlags&#34;][&#34;wormCopy&#34;] == 1</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage_pool.StoragePool.is_object_level_worm_lock_enabled"><code class="name">var <span class="ident">is_object_level_worm_lock_enabled</span></code></dt>
<dd>
<div class="desc"><p>Treats is object WORM enabled as a read only attribute</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage_pool.py#L1036-L1040" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_object_level_worm_lock_enabled(self):
    &#34;&#34;&#34;Treats is object WORM enabled as a read only attribute&#34;&#34;&#34;
    worm_flag = int(self._storage_pool_properties[&#34;storagePoolDetails&#34;][&#34;copyInfo&#34;][&#34;wormStorageFlag&#34;])
    return worm_flag &amp; WORMLockType.OBJECT == WORMLockType.OBJECT</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage_pool.StoragePool.is_worm_storage_lock_enabled"><code class="name">var <span class="ident">is_worm_storage_lock_enabled</span></code></dt>
<dd>
<div class="desc"><p>Treats is worm enabled as a read only attribute</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage_pool.py#L1031-L1034" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_worm_storage_lock_enabled(self):
    &#34;&#34;&#34;Treats is worm enabled as a read only attribute&#34;&#34;&#34;
    return self._storage_pool_properties[&#34;storagePoolDetails&#34;][&#34;isWormStorage&#34;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage_pool.StoragePool.storage_pool_id"><code class="name">var <span class="ident">storage_pool_id</span></code></dt>
<dd>
<div class="desc"><p>Treats id as a read only attribute</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage_pool.py#L991-L994" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def storage_pool_id(self):
    &#34;&#34;&#34;Treats id as a read only attribute&#34;&#34;&#34;
    return self._storage_pool_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage_pool.StoragePool.storage_pool_name"><code class="name">var <span class="ident">storage_pool_name</span></code></dt>
<dd>
<div class="desc"><p>Treats the storage_policy_name as a read only attribute</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage_pool.py#L986-L989" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def storage_pool_name(self):
    &#34;&#34;&#34;Treats the storage_policy_name as a read only attribute&#34;&#34;&#34;
    return self._storage_pool_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage_pool.StoragePool.storage_pool_properties"><code class="name">var <span class="ident">storage_pool_properties</span></code></dt>
<dd>
<div class="desc"><p>Treats the storage_pool_properties as a read only attribute</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage_pool.py#L996-L999" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def storage_pool_properties(self):
    &#34;&#34;&#34;Treats the storage_pool_properties as a read only attribute&#34;&#34;&#34;
    return self._storage_pool_properties</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage_pool.StoragePool.storage_pool_type"><code class="name">var <span class="ident">storage_pool_type</span></code></dt>
<dd>
<div class="desc"><p>Treats storage type as a read only attribute</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage_pool.py#L1016-L1019" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def storage_pool_type(self):
    &#34;&#34;&#34;Treats storage type as a read only attribute&#34;&#34;&#34;
    return self._storage_pool_properties[&#34;storagePoolDetails&#34;][&#34;storagePoolType&#34;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage_pool.StoragePool.storage_type"><code class="name">var <span class="ident">storage_type</span></code></dt>
<dd>
<div class="desc"><p>Treats storage type as a read only attribute</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage_pool.py#L1021-L1024" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def storage_type(self):
    &#34;&#34;&#34;Treats storage type as a read only attribute&#34;&#34;&#34;
    return self._storage_pool_properties[&#34;storagePoolDetails&#34;][&#34;storageType&#34;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage_pool.StoragePool.storage_vendor"><code class="name">var <span class="ident">storage_vendor</span></code></dt>
<dd>
<div class="desc"><p>Treats library vendor like cloud storage provider as a read only attribute</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage_pool.py#L1026-L1029" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def storage_vendor(self):
    &#34;&#34;&#34;Treats library vendor like cloud storage provider as a read only attribute&#34;&#34;&#34;
    return self._storage_pool_properties[&#34;storagePoolDetails&#34;][&#34;libraryVendorId&#34;]</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.storage_pool.StoragePool.enable_compliance_lock"><code class="name flex">
<span>def <span class="ident">enable_compliance_lock</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Enables compliance lock on Storage Pool Copy</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage_pool.py#L1057-L1060" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable_compliance_lock(self):
    &#34;&#34;&#34; Enables compliance lock on Storage Pool Copy &#34;&#34;&#34;
    self.get_copy().enable_compliance_lock()
    self.refresh()</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage_pool.StoragePool.enable_worm_storage_lock"><code class="name flex">
<span>def <span class="ident">enable_worm_storage_lock</span></span>(<span>self, retain_days)</span>
</code></dt>
<dd>
<div class="desc"><p>Enable storage WORM lock on storage pool</p>
<h2 id="args">Args</h2>
<p>retain_days
(int)
&ndash; number of days of retention on WORM copy.</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if response is not success.</p>
<pre><code>if reponse is empty.
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage_pool.py#L1062-L1105" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable_worm_storage_lock(self, retain_days):
    &#34;&#34;&#34;
    Enable storage WORM lock on storage pool

    Args:
        retain_days    (int)   -- number of days of retention on WORM copy.

    Raises:
        SDKException:
            if response is not success.

            if reponse is empty.
    &#34;&#34;&#34;

    request_json = {
        &#34;storagePolicyCopyInfo&#34;: {
            &#34;copyFlags&#34;: {
                &#34;wormCopy&#34;: 1
            },
            &#34;retentionRules&#34;: {
                &#34;retainBackupDataForDays&#34;: retain_days
            }
        },
        &#34;isWormStorage&#34;: True,
        &#34;forceCopyToFollowPoolRetention&#34;: True
    }

    _STORAGE_POOL_COPY = self._commcell_object._services[&#39;STORAGE_POLICY_COPY&#39;] % (
        self._storage_pool_id, str(self.copy_id))
    flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;PUT&#39;, _STORAGE_POOL_COPY, request_json)

    if flag:
        if response.json():
            response = response.json()
            if &#34;error&#34; in response and response.get(&#34;error&#34;, {}).get(&#34;errorCode&#34;) != 0:
                error_message = response.get(&#34;error&#34;, {}).get(&#34;errorMessage&#34;)
                raise SDKException(&#39;Response&#39;, &#39;102&#39;, error_message)
            else:
                self.refresh()
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage_pool.StoragePool.get_copy"><code class="name flex">
<span>def <span class="ident">get_copy</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the StoragePolicyCopy object of Storage Pool copy</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage_pool.py#L1053-L1055" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_copy(self):
    &#34;&#34;&#34; Returns the StoragePolicyCopy object of Storage Pool copy&#34;&#34;&#34;
    return StoragePolicyCopy(self._commcell_object, self.storage_pool_name, self.copy_name)</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage_pool.StoragePool.hyperscale_add_nodes"><code class="name flex">
<span>def <span class="ident">hyperscale_add_nodes</span></span>(<span>self, media_agents)</span>
</code></dt>
<dd>
<div class="desc"><p>Add 3 new nodes to an existing storage pool</p>
<p>args:
media_agents
(List)
&ndash; List of 3 media agents with name's(str)
or instance of media agent's(object)</p>
<pre><code>Example: ["ma1","ma2","ma3"]
</code></pre>
<h2 id="raises">Raises</h2>
<p>SDKException:
if add nodes to an existing storage pool fails</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage_pool.py#L1107-L1188" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def hyperscale_add_nodes(self, media_agents):
    &#34;&#34;&#34;
    Add 3 new nodes to an existing storage pool

    args:
        media_agents      (List)   -- List of 3 media agents with name&#39;s(str)
                                        or instance of media agent&#39;s(object)

        Example: [&#34;ma1&#34;,&#34;ma2&#34;,&#34;ma3&#34;]

    Raises:
            SDKException:
                if add nodes to an existing storage pool fails
    &#34;&#34;&#34;
    if not isinstance(media_agents, list):
        raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

    mediagent_obj = []
    for media_agent in media_agents:
        if isinstance(media_agent, MediaAgent):
            mediagent_obj.append(media_agent)
        elif isinstance(media_agent, str):
            mediagent_obj.append(self._commcell_object.media_agents.get(media_agent))
        else:
            raise SDKException(&#39;Storage&#39;, &#39;103&#39;)

    if len(mediagent_obj) &lt;= 2:
        raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#34;Minimum 3 MediaAgents required&#34;)

    request_json = {
        &#34;scaleoutOperationType&#34;: 2,
        &#34;StoragePolicy&#34;: {
            &#34;storagePolicyName&#34;: &#34;{0}&#34;.format(self.storage_pool_name),
        },
        &#34;storage&#34;: [
            {
                &#34;mediaAgent&#34;: {
                    &#34;displayName&#34;: &#34;{0}&#34;.format(mediagent_obj[0].media_agent_id),
                    &#34;mediaAgentName&#34;: &#34;{0}&#34;.format(mediagent_obj[0].media_agent_name)
                }
            },
            {
                &#34;mediaAgent&#34;: {
                    &#34;displayName&#34;: &#34;{0}&#34;.format(mediagent_obj[1].media_agent_id),
                    &#34;mediaAgentName&#34;: &#34;{0}&#34;.format(mediagent_obj[1].media_agent_name)
                }
            },
            {
                &#34;mediaAgent&#34;: {
                    &#34;displayName&#34;: &#34;{0}&#34;.format(mediagent_obj[2].media_agent_id),
                    &#34;mediaAgentName&#34;: &#34;{0}&#34;.format(mediagent_obj[2].media_agent_name)
                }
            }
        ],
        &#34;scaleoutConfiguration&#34;: {
            &#34;configurationType&#34;: 1
        }
    }

    self._edit_storage_pool_api = self._commcell_object._services[
        &#39;EDIT_STORAGE_POOL&#39;]

    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, self._edit_storage_pool_api, request_json
    )

    if flag:
        if response.json():
            error_code = response.json()[&#39;errorCode&#39;]

            if int(error_code) != 0:
                error_message = response.json()[&#39;errorMessage&#39;]
                o_str = &#39;Failed to add nodes to storage pool\nError: &#34;{0}&#34;&#39;

                raise SDKException(&#39;StoragePool&#39;, &#39;102&#39;, o_str.format(error_message))
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    self.refresh()</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage_pool.StoragePool.hyperscale_reconfigure_storage_pool"><code class="name flex">
<span>def <span class="ident">hyperscale_reconfigure_storage_pool</span></span>(<span>self, storage_pool_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Reconfigures storage pool, for any failure during creation and expansion</p>
<p>args:
storage_pool_name (string) &ndash; Name of the storage pools to reconfigure</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if reconfigure fails</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage_pool.py#L1190-L1236" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def hyperscale_reconfigure_storage_pool(self, storage_pool_name):
    &#34;&#34;&#34;
    Reconfigures storage pool, for any failure during creation and expansion

    args:
      storage_pool_name (string) -- Name of the storage pools to reconfigure
    Raises:
            SDKException:
                if reconfigure fails
    &#34;&#34;&#34;
    if not isinstance(storage_pool_name, str):
        raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

    request_json = {

        &#34;scaleoutOperationType&#34;: 4,
        &#34;StoragePolicy&#34;:
            {
                &#34;storagePolicyName&#34;: &#34;{0}&#34;.format(storage_pool_name),
                &#34;storagePolicyId&#34;: int(&#34;{0}&#34;.format(self.storage_pool_id))

            }
    }

    self._edit_storage_pool_api = self._commcell_object._services[
        &#39;EDIT_STORAGE_POOL&#39;]

    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, self._edit_storage_pool_api, request_json
    )

    if flag:
        if response.json():
            error_code = response.json()[&#39;errorCode&#39;]

            if int(error_code) != 0:
                error_message = response.json()[&#39;errorMessage&#39;]
                o_str = &#39;Failed to reconfigure storage pool\nError: &#34;{0}&#34;&#39;

                raise SDKException(&#39;StoragePool&#39;, &#39;102&#39;, o_str.format(error_message))
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    self.refresh()</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage_pool.StoragePool.hyperscale_replace_disk"><code class="name flex">
<span>def <span class="ident">hyperscale_replace_disk</span></span>(<span>self, disk_id, media_agent, storage_pool_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Replace disk action, over a media agent which is part of storage pool
args:
disk_id (int) &ndash;&gt; disk id for the disk to replace
media_agent (string/object) &ndash;&gt; media agent name/ object
storage_pool_name (string) &ndash;&gt; Name of the storage pools for replacemnet of disk
Raises:
SDKException:
if replace fails</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage_pool.py#L1238-L1303" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def hyperscale_replace_disk(self, disk_id, media_agent, storage_pool_name):
    &#34;&#34;&#34;
          Replace disk action, over a media agent which is part of storage pool
           args:
                disk_id (int) --&gt; disk id for the disk to replace
                media_agent (string/object) --&gt; media agent name/ object
                storage_pool_name (string) --&gt; Name of the storage pools for replacemnet of disk
           Raises:
                   SDKException:
                       if replace fails
           &#34;&#34;&#34;
    if isinstance(disk_id, str):
        disk_id = int(disk_id)
    elif not isinstance(disk_id, int):
        raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

    media_agent_obj = None
    if isinstance(media_agent, str):
        media_agent_obj = self._commcell_object.media_agents.get(media_agent)
    elif isinstance(media_agent, MediaAgent):
        media_agent_obj = media_agent
    else:
        raise SDKException(&#39;Storage&#39;, &#39;103&#39;)

    if not isinstance(storage_pool_name, str):
        raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

    request_json = {

        &#34;driveId&#34;: int(&#34;{0}&#34;.format(disk_id)),
        &#34;operationType&#34;: 1,
        &#34;mediaAgent&#34;: {
            &#34;_type_&#34;: 11,
            &#34;mediaAgentId&#34;: int(&#34;{0}&#34;.format(media_agent_obj.media_agent_id)),
            &#34;mediaAgentName&#34;: &#34;{0}&#34;.format(media_agent_obj.media_agent_name)
        },
        &#34;scaleoutStoragePool&#34;: {
            &#34;_type_&#34;: 160,
            &#34;storagePoolId&#34;: int(&#34;{0}&#34;.format(self.storage_pool_id)),
            &#34;storagePoolName&#34;: &#34;{0}&#34;.format(self.storage_pool_name)
        }
    }

    self._replace_disk_storage_pool_api = self._commcell_object._services[
        &#39;REPLACE_DISK_STORAGE_POOL&#39;]

    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, self._replace_disk_storage_pool_api, request_json
    )

    if flag:
        if response.json():
            error_code = response.json()[&#39;errorCode&#39;]

            if int(error_code) != 0:
                error_message = response.json()[&#39;errorMessage&#39;]
                o_str = &#39;Failed to replace disk\nError: &#34;{0}&#34;&#39;

                raise SDKException(&#39;StoragePool&#39;, &#39;102&#39;, o_str.format(error_message))
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    self.refresh()</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage_pool.StoragePool.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refreshes propery of the class object</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage_pool.py#L1305-L1307" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refreshes propery of the class object&#34;&#34;&#34;
    self._get_storage_pool_properties()</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage_pool.StoragePool.update_security_associations"><code class="name flex">
<span>def <span class="ident">update_security_associations</span></span>(<span>self, associations_list, isUser=True, request_type=None, externalGroup=False)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds the security association on the storage pool object</p>
<h2 id="args">Args</h2>
<p>associations_list
(list)
&ndash;
list of users to be associated
Example:
associations_list = [
{
'user_name': user1,
'role_name': role1
},
{
'user_name': user2,
'role_name': role2
}
]</p>
<p>isUser (bool)
&ndash;
True or False. set isUser = False, If associations_list made up of user groups
request_type (str)
&ndash;
eg : 'OVERWRITE' or 'UPDATE' or 'DELETE', Default will be OVERWRITE operation
externalGroup (bool)
&ndash;
True or False, set externalGroup = True. If Security associations is being done on External User Groups</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if association is not of List type</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage_pool.py#L1309-L1341" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def update_security_associations(self, associations_list, isUser=True, request_type=None, externalGroup=False):
    &#34;&#34;&#34;
    Adds the security association on the storage pool object

    Args:
        associations_list   (list)  --  list of users to be associated
            Example:
                associations_list = [
                    {
                        &#39;user_name&#39;: user1,
                        &#39;role_name&#39;: role1
                    },
                    {
                        &#39;user_name&#39;: user2,
                        &#39;role_name&#39;: role2
                    }
                ]

        isUser (bool)           --    True or False. set isUser = False, If associations_list made up of user groups
        request_type (str)      --    eg : &#39;OVERWRITE&#39; or &#39;UPDATE&#39; or &#39;DELETE&#39;, Default will be OVERWRITE operation
        externalGroup (bool)    --    True or False, set externalGroup = True. If Security associations is being done on External User Groups

    Raises:
        SDKException:
            if association is not of List type
    &#34;&#34;&#34;
    if not isinstance(associations_list, list):
        raise SDKException(&#39;StoragePool&#39;, &#39;101&#39;)

    SecurityAssociation(self._commcell_object, self)._add_security_association(associations_list,
                                                                               user=isUser,
                                                                               request_type=request_type,
                                                                               externalGroup=externalGroup)</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.storage_pool.StoragePoolType"><code class="flex name class">
<span>class <span class="ident">StoragePoolType</span></span>
<span>(</span><span>value, names=None, *, module=None, qualname=None, type=None, start=1)</span>
</code></dt>
<dd>
<div class="desc"><p>Class IntEnum to represent different storage pool types</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage_pool.py#L906-L911" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class StoragePoolType(IntEnum):
    &#34;&#34;&#34;Class IntEnum to represent different storage pool types&#34;&#34;&#34;
    DEDUPLICATION = 1,
    SECONDARY_COPY = 2,
    NON_DEDUPLICATION = 3,
    SCALE_OUT = 4</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li>enum.IntEnum</li>
<li>builtins.int</li>
<li>enum.Enum</li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.storage_pool.StoragePoolType.DEDUPLICATION"><code class="name">var <span class="ident">DEDUPLICATION</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.storage_pool.StoragePoolType.NON_DEDUPLICATION"><code class="name">var <span class="ident">NON_DEDUPLICATION</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.storage_pool.StoragePoolType.SCALE_OUT"><code class="name">var <span class="ident">SCALE_OUT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.storage_pool.StoragePoolType.SECONDARY_COPY"><code class="name">var <span class="ident">SECONDARY_COPY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.storage_pool.StoragePools"><code class="flex name class">
<span>class <span class="ident">StoragePools</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for doing operations on Storage Pools, like get storage poo ID.</p>
<p>Initializes instance of the StoragePools class to perform operations on a storage pool.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the Commcell class</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the StoragePools class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage_pool.py#L143-L904" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class StoragePools:
    &#34;&#34;&#34;Class for doing operations on Storage Pools, like get storage poo ID.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initializes instance of the StoragePools class to perform operations on a storage pool.

            Args:
                commcell_object     (object)    --  instance of the Commcell class

            Returns:
                object  -   instance of the StoragePools class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object

        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_
        self._add_storage_pool_api = self._services[&#39;ADD_STORAGE_POOL&#39;]

        self._storage_pools_api = self._services[&#39;STORAGE_POOL&#39;]

        self._metallic_storage_api = self._services[&#39;GET_METALLIC_STORAGE_DETAILS&#39;]
        self.__get_agp_storage_api = self._services[&#39;GET_AGP_STORAGE&#39;]
        self._storage_pools = None

        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all storage pools present in the Commcell.

            Returns:
                str     -   string of all the storage pools associated with the commcell

        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^40}\n\n&#39;.format(&#39;S. No.&#39;, &#39;Storage Pool&#39;)

        for index, storage_pool in enumerate(self._storage_pools):
            sub_str = &#39;{:^5}\t{:40}\n&#39;.format(index + 1, storage_pool)
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Returns the string representation of an instance of this class.&#34;&#34;&#34;
        return &#34;StoragePools class instance for Commcell&#34;

    def __len__(self):
        &#34;&#34;&#34;Returns the number of the storage pools added to the Commcell.&#34;&#34;&#34;
        return len(self.all_storage_pools)

    def __getitem__(self, value):
        &#34;&#34;&#34;Returns the name of the storage pool for the given storage pool ID or
            the details of the storage pool for given storage pool Name.

            Args:
                value   (str / int)     --  Name or ID of the storage pool

            Returns:
                str     -   name of the storage pool, if the storage pool id was given

                dict    -   dict of details of the storage pool, if storage pool name was given

            Raises:
                IndexError:
                    no storage pool exists with the given Name / Id

        &#34;&#34;&#34;
        value = str(value)

        if value in self.all_storage_pools:
            return self.all_storage_pools[value]
        else:
            try:
                return list(filter(lambda x: x[1][&#39;id&#39;] == value, self.all_storage_pools.items()))[0][0]
            except IndexError:
                raise IndexError(&#39;No storage pool exists with the given Name / Id&#39;)

    def _get_storage_pools(self):
        &#34;&#34;&#34;Gets all the storage pools associated with the Commcell environment.

            Returns:
                dict    -   consists of all storage pools added to the commcell

                    {
                        &#34;storage_pool1_name&#34;: storage_pool1_id,

                        &#34;storage_pool2_name&#34;: storage_pool2_id
                    }

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        headers = self._commcell_object._headers.copy()
        headers[&#39;Accept&#39;] = &#39;application/xml&#39;

        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._storage_pools_api, headers=headers
        )

        if flag:
            storage_pools = {}

            response = xmltodict.parse(response.text)[&#39;Api_GetStoragePoolListResp&#39;]

            if response is None or response.get(&#39;storagePoolList&#39;) is None:
                storage_pool_list = []
            else:
                storage_pool_list = response[&#39;storagePoolList&#39;]

            if not isinstance(storage_pool_list, list):
                storage_pool_list = [storage_pool_list]

            if response:
                for pool in storage_pool_list:
                    name = pool[&#39;storagePoolEntity&#39;][&#39;@storagePoolName&#39;].lower()
                    storage_pool_id = pool[&#39;storagePoolEntity&#39;][&#39;@storagePoolId&#39;]

                    storage_pools[name] = storage_pool_id

                return storage_pools
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
    
    def get_storage_pools_for_a_company(self, company_id, storage_type: StorageType = None):
        &#34;&#34;&#34;Gets all the storage pools associated with the Commcell environment.

            Args:
                company_id - id of the company for which the associated storge pools are to be fetched

            Returns:
                dict    -   consists of all storage pools added to the commcell

                    {
                        &#34;storage_pool1_name&#34;: storage_pool1_id,

                        &#34;storage_pool2_name&#34;: storage_pool2_id
                    }

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        headers = self._commcell_object._headers.copy()
        headers[&#39;Accept&#39;] = &#39;application/json&#39;
        headers[&#39;onlygetcompanyownedentities&#39;] = &#39;1&#39;
        headers[&#39;operatorcompanyid&#39;] = f&#39;{company_id}&#39;

        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._storage_pools_api, headers=headers
        )

        if flag:
            storage_pools = {}
            response = response.json()
            if response is None or response.get(&#39;storagePoolList&#39;) is None:
                storage_pool_list = []
            else:
                storage_pool_list = response[&#39;storagePoolList&#39;]
            if not isinstance(storage_pool_list, list):
                storage_pool_list = [storage_pool_list]
            if response:
                for pool in storage_pool_list:
                    if storage_type and pool[&#39;storageType&#39;] != storage_type:
                        continue
                    # skip agp pools for cloud storage type
                    if storage_type == StorageType.CLOUD and 401 &lt;= pool[&#39;libraryVendorType&#39;] &lt;= 499:
                        continue
                    name = pool[&#39;storagePoolEntity&#39;][&#39;storagePoolName&#39;]
                    storage_pool_id = pool[&#39;storagePoolEntity&#39;][&#39;storagePoolId&#39;]

                    storage_pools[name] = storage_pool_id

            return storage_pools
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def all_storage_pools(self):
        &#34;&#34;&#34;Returns dict of all the storage pools on this commcell

            dict    -   consists of all storage pools added to the commcell

                {

                    &#34;storage_pool1_name&#34;: storage_pool1_id,

                    &#34;storage_pool2_name&#34;: storage_pool2_id
                }

        &#34;&#34;&#34;
        return self._storage_pools

    def has_storage_pool(self, name):
        &#34;&#34;&#34;Checks if a storage pool exists in the Commcell with the input storage pool name.

            Args:
                name    (str)   --  name of the storage pool

            Returns:
                bool    -   boolean output whether the storage pool exists in the commcell or not

        &#34;&#34;&#34;
        return self._storage_pools and name.lower() in self._storage_pools

    def get(self, name):
        &#34;&#34;&#34;Returns the id of the storage pool for the given storage pool name.

            Args:
                name    (str)   --  name of the storage pool to get the id of

            Returns:
                str     -   id of the storage pool for the given storage pool name

            Raises:
                SDKException:
                    if no storage pool exists with the given name

        &#34;&#34;&#34;
        self.refresh()
        name = name.lower()

        if self.has_storage_pool(name):
            return StoragePool(self._commcell_object, name, storage_pool_id=self._storage_pools[name])
        else:
            raise SDKException(&#39;StoragePool&#39;, &#39;103&#39;)

    def hyperscale_create_storage_pool(self, storage_pool_name, media_agents):
        &#34;&#34;&#34;
            Create new storage pool for hyperscale
            Args:
                storage_pool_name (string) -- Name of the storage pools to create

                media_agents      (List)   -- List of 3 media agents with name&#39;s(str)
                                                or instance of media agent&#39;s(object)

                Example: [&#34;ma1&#34;,&#34;ma2&#34;,&#34;ma3&#34;]

            Return:
                 flag, response -- response returned by the REST API call
        &#34;&#34;&#34;

        if not isinstance(media_agents, list):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)
        if not isinstance(storage_pool_name, str):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        mediagent_obj = []
        for media_agent in media_agents:
            if isinstance(media_agent, MediaAgent):
                mediagent_obj.append(media_agent)
            elif isinstance(media_agent, str):
                mediagent_obj.append(self._commcell_object.media_agents.get(media_agent))
            else:
                raise SDKException(&#39;Storage&#39;, &#39;103&#39;)
        if len(mediagent_obj) &lt;= 2:
            raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#34;minimum 3 media agents are required&#34;)

        request_xml = &#34;&#34;&#34;&lt;App_CreateStoragePolicyReq storagePolicyName=&#34;{0}&#34; copyName=&#34;{0}_Primary&#34; type=&#34;1&#34;
                                     numberOfCopies=&#34;1&#34;&gt;
                                    &lt;storagePolicyCopyInfo&gt;
                                        &lt;storagePolicyFlags scaleOutStoragePolicy=&#34;1&#34;/&gt;
                                    &lt;/storagePolicyCopyInfo&gt;
                                    &lt;storage&gt;
                                        &lt;mediaAgent mediaAgentId=&#34;{4}&#34; mediaAgentName=&#34;{1}&#34; displayName=&#34;{1}&#34;/&gt;
                                    &lt;/storage&gt;
                                    &lt;storage&gt;
                                        &lt;mediaAgent mediaAgentId=&#34;{5}&#34; mediaAgentName=&#34;{2}&#34; displayName=&#34;{2}&#34;/&gt;
                                    &lt;/storage&gt;
                                    &lt;storage&gt;
                                        &lt;mediaAgent mediaAgentId=&#34;{6}&#34; mediaAgentName=&#34;{3}&#34; displayName=&#34;{3}&#34;/&gt;
                                    &lt;/storage&gt;
                                    &lt;scaleoutConfiguration configurationType=&#34;1&#34;/&gt;
                                &lt;/App_CreateStoragePolicyReq&gt;
                                &#34;&#34;&#34;.format(storage_pool_name, mediagent_obj[0].media_agent_name,
                                           mediagent_obj[1].media_agent_name, mediagent_obj[2].media_agent_name,
                                           mediagent_obj[0].media_agent_id, mediagent_obj[1].media_agent_id,
                                           mediagent_obj[2].media_agent_id)

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._add_storage_pool_api, request_xml
        )
        if flag:
            if response.json():
                error_code = response.json()[&#39;error&#39;][&#39;errorCode&#39;]

                if int(error_code) != 0:
                    error_message = response.json()[&#39;error&#39;][&#39;errorMessage&#39;]
                    o_str = &#39;Failed to create storage pool\nError: &#34;{0}&#34;&#39;

                    raise SDKException(&#39;StoragePool&#39;, &#39;102&#39;, o_str.format(error_message))
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        self.refresh()
        return self.get(storage_pool_name)

    def add_air_gap_protect(self, storage_pool_name, media_agent, storage_type, storage_class, region_name,
                            ddb_ma=None, dedup_path=None):
        &#34;&#34;&#34;
            Adds a new air gap protect storage pool to commcell

                Args:
                    storage_pool_name   (str)       --  name of new storage pool to add

                    media_agent         (str/object)--  name or instance of media agent

                    storage_type        (str)        -- name of the cloud vendor (str, eg - &#34;Microsoft Azure storage&#34;) (same as UI)

                    storage_class       (str)        -- storage class (str, eg - &#34;Hot&#34;,&#34;Cool&#34;) (same as UI)

                    region_name (str)      --  name of the geographical region for storage (same as UI)

                    ddb_ma              (list&lt;str/object&gt;/str/object)   --  list of (name of name or instance)
                                                                            or name or instance of dedupe media agent

                    dedup_path          (list&lt;str&gt;/str)       --  list of paths or path where the DDB should be stored

                Returns:
                    StoragePool object if creation is successful

                Raises:
                    SDKException, if invalid parameters provided

        &#34;&#34;&#34;
        license_type_dict = StoragePoolConstants.AIR_GAP_PROTECT_STORAGE_TYPES
        error_message = &#34;&#34;
        if storage_type.upper() in license_type_dict:
            available_storage_classes = license_type_dict[storage_type.upper()]
            if storage_class.upper() in available_storage_classes:
                vendor_id = available_storage_classes[storage_class.upper()][&#34;vendorId&#34;]
                display_vendor_id = available_storage_classes[storage_class.upper()][&#34;displayVendorId&#34;]
            else:
                error_message += f&#34;Invalid storage class provided. Valid storage class {list(available_storage_classes.keys())}&#34;
        else:
            error_message += f&#34;  Invalid storage type provided. {list(license_type_dict.keys())}&#34;

        if error_message:
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;, error_message)

        region = None
        available_regions = []

        #  API call to fetch the region name - sourced directly from the vendor
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;GET&#39;, self._metallic_storage_api)
        if flag:
            if response.json():
                if &#34;storageInformation&#34; in response.json():
                    for storage_info in response.json()[&#34;storageInformation&#34;]:
                        if (int(storage_info[&#34;vendorId&#34;]) == int(vendor_id)) and (int(storage_info[&#34;displayVendorId&#34;]) == int(display_vendor_id)):
                            for region_dict in storage_info[&#34;region&#34;]:
                                available_regions.append(region_dict[&#34;displayName&#34;])
                                if region_dict[&#34;displayName&#34;] == region_name:
                                    region = region_dict[&#34;regionName&#34;]
                                    break

                        if region:
                            break

                    if region is None:
                        if not available_regions:
                            raise SDKException(&#39;Storage&#39;, &#39;101&#39;,
                                               f&#34;Active license is required to configure {storage_type} - {storage_class} Air Gap Protect storage&#34;)
                        else:
                            raise SDKException(&#39;Storage&#39;, &#39;101&#39;,
                                               f&#34;Invalid region: {region_name} ,\nValid regions: {available_regions}&#34;)
                else:
                    raise SDKException(&#39;Storage&#39;, &#39;101&#39;, &#34;Unexpected response returned while fetching Air Gap Protect storage details&#34;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        # cloud server type for air gap protect is 400
        cloud_server_type = 400

        return self.add(storage_pool_name=storage_pool_name, mountpath=None, media_agent=media_agent, ddb_ma=ddb_ma,
                        dedup_path=dedup_path, cloud_server_type=cloud_server_type, region=region, vendor_id=vendor_id,
                        display_vendor_id=display_vendor_id)
        
    def get_air_gap_protect(self, company_id = None):
        &#34;&#34;&#34;
        Returns the list of air gap protect storage pools in the commcell.
        
        Args:
            company_id (int) -- id of the company to get the air gap protect storage pools for
                                (optional, default is None which returns all air gap protect storage pools)
        
        Returns:
            dict - dictionary of air gap protect storage pools with name as key and id as value
                
                    {
                        &#34;storage_pool1_name&#34;: storage_pool1_id,
                        &#34;storage_pool2_name&#34;: storage_pool2_id
                    }  
        
        Raises:
            SDKException:
                if response is empty

                if response is not success
        &#34;&#34;&#34;
        headers = self._commcell_object._headers.copy()
        headers[&#39;Accept&#39;] = &#39;application/json&#39;
        if company_id:
            headers[&#39;onlygetcompanyownedentities&#39;] = &#39;1&#39;
            headers[&#39;operatorcompanyid&#39;] = f&#39;{company_id}&#39;

        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self.__get_agp_storage_api, headers=headers
        )

        if flag:
            storage_pools = {}
            response = response.json()
            if response is None or response.get(&#39;cloudStorage&#39;) is None:
                storage_pool_list = []
            else:
                storage_pool_list = response[&#39;cloudStorage&#39;]
            if not isinstance(storage_pool_list, list):
                storage_pool_list = [storage_pool_list]
            if response:
                for pool in storage_pool_list:
                    name = pool[&#39;name&#39;]
                    storage_pool_id = pool[&#39;id&#39;]

                    storage_pools[name] = storage_pool_id

            return storage_pools
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def add(self, storage_pool_name, mountpath, media_agent, ddb_ma=None, dedup_path=None, **kwargs):
        &#34;&#34;&#34;
        Adds a new storage pool to commcell

        Args:
            storage_pool_name   (str)       --  name of new storage pool to add

            mountpath           (str)       --  mount path for the storage pool

            media_agent         (str/object)--  name or instance of media agent

            ddb_ma              (list&lt;str/object&gt;/str/object)   --  list of (name of name or instance)
                                                                        or name or instance of dedupe media agent

            dedup_path          (list&lt;str&gt;/str)       --  list of paths or path where the DDB should be stored

            **kwargs:
                username        (str)       --  username to access the mountpath

                password        (str)       --  password to access the mountpath

                credential_name (str)       --  name of the credential as in credential manager

                cloud_server_type (int)     --  cloud server type of the cloud vendor (required)

                region (str)                --  name of geographical region for storage (required for air gap protect)

                vendor_id (int)             -- id for the cloud_vendor (eg - 3 for azure) (required for air gap protect pool)

                display_vendor_id (int)     -- storage Class id for that vendor (eg - 401 for azure hot) (required for air gap protect pool)

                region_id        (int)      --  Cloud Hypervisor specific region ID

                tape_storage (boolean)      -- if library passed is tape library. 

        Returns:
            StoragePool object if creation is successful

        Raises:
            Exception if creation is unsuccessful
        &#34;&#34;&#34;
        username = kwargs.get(&#39;username&#39;, None)
        password = kwargs.get(&#39;password&#39;, None)
        credential_name = kwargs.get(&#39;credential_name&#39;, None)
        cloud_server_type = kwargs.get(&#39;cloud_server_type&#39;, None)
        library_name = kwargs.get(&#39;library_name&#39;, None)
        tape_storage = False

        region = kwargs.get(&#39;region&#39;, None)
        vendor_id = kwargs.get(&#39;vendor_id&#39;, None)
        display_vendor_id = kwargs.get(&#39;display_vendor_id&#39;, None)
        region_id = kwargs.get(&#39;region_id&#39;, None)

        if library_name:
            library_object = self._commcell_object.disk_libraries.get(library_name)
            library_type = library_object.library_properties.get(&#39;libraryType&#39;, None)
            tape_storage = True if library_type == 1 else tape_storage


        if ((ddb_ma is not None and not (isinstance(dedup_path, str) or isinstance(dedup_path, list))) or
                not (isinstance(storage_pool_name, str) or not isinstance(mountpath, str))):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        if isinstance(media_agent, MediaAgent):
            media_agent = media_agent
        elif isinstance(media_agent, str):
            media_agent = MediaAgent(self._commcell_object, media_agent)
        else:
            raise SDKException(&#39;Storage&#39;, &#39;103&#39;)

        if (isinstance(ddb_ma, str) or isinstance(ddb_ma, MediaAgent)) and isinstance(dedup_path, str):
            ddb_ma = [ddb_ma]
            dedup_path = [dedup_path]

        if isinstance(ddb_ma, list) and isinstance(dedup_path, list):
            if len(ddb_ma) != len(dedup_path):
                raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        if library_name is not None and mountpath != &#39;&#39;:
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

        if ddb_ma is not None and (len(ddb_ma) &gt; 6 or len(dedup_path) &gt; 6):
            raise SDKException(&#39;Storage&#39;, &#39;110&#39;)

        if ddb_ma is not None:
            for i in range(len(ddb_ma)):
                if isinstance(ddb_ma[i], MediaAgent):
                    ddb_ma[i] = ddb_ma[i]
                elif isinstance(ddb_ma[i], str):
                    ddb_ma[i] = MediaAgent(self._commcell_object, ddb_ma[i])
                else:
                    raise SDKException(&#39;Storage&#39;, &#39;103&#39;)

        request_json = {
            &#34;storagePolicyName&#34;: storage_pool_name,
            &#34;type&#34;: &#34;CVA_REGULAR_SP&#34;,
            &#34;copyName&#34;: &#34;Primary&#34;,
            &#34;numberOfCopies&#34;: 1,
            &#34;storage&#34;: [
                {
                    &#34;path&#34;: mountpath,
                    &#34;mediaAgent&#34;: {
                        &#34;mediaAgentId&#34;: int(media_agent.media_agent_id),
                        &#34;mediaAgentName&#34;: media_agent.media_agent_name
                    }
                }
            ],
            &#34;storagePolicyCopyInfo&#34;: {
                &#34;copyType&#34;: &#34;SYNCHRONOUS&#34;,
                &#34;isFromGui&#34;: True,
                &#34;active&#34;: &#34;SET_TRUE&#34;,
                &#34;isDefault&#34;: &#34;SET_TRUE&#34;,
                &#34;numberOfStreamsToCombine&#34;: 1,
                &#34;retentionRules&#34;: {
                    &#34;retentionFlags&#34;: {
                        &#34;enableDataAging&#34;: &#34;SET_TRUE&#34;
                    },
                    &#34;retainBackupDataForDays&#34;: -1,
                    &#34;retainBackupDataForCycles&#34;: -1,
                    &#34;retainArchiverDataForDays&#34;: -1
                },
                &#34;library&#34;: {
                    &#34;libraryId&#34;: 0,
                },
                &#34;mediaAgent&#34;: {
                    &#34;mediaAgentId&#34;: int(media_agent.media_agent_id),
                    &#34;mediaAgentName&#34;: media_agent.media_agent_name
                }
            }
        }

        if cloud_server_type and int(cloud_server_type) &gt; 0:
            request_json[&#34;storage&#34;][0][&#34;deviceType&#34;] = cloud_server_type

        if region_id is not None:
            request_json[&#34;storage&#34;][0][&#34;metallicStorageInfo&#34;] = {
                &#34;region&#34;: [
                    {
                        &#34;regionId&#34;: region_id
                    }
                ],
                &#34;storageClass&#34;: [
                    &#34;CONTAINER_DEFAULT&#34;
                ],
                &#34;replication&#34;: [
                    &#34;NONE&#34;
                ]
            }
            request_json[&#34;region&#34;] = {&#34;regionId&#34;: region_id}

        if username is not None:
            request_json[&#34;storage&#34;][0][&#34;credentials&#34;] = {&#34;userName&#34;: username}

        if password is not None:
            request_json[&#34;storage&#34;][0][&#34;credentials&#34;][&#34;password&#34;] = b64encode(password.encode()).decode()

        if credential_name is not None:
            request_json[&#34;storage&#34;][0][&#34;savedCredential&#34;] = {&#34;credentialName&#34;: credential_name}

        if library_name is not None:
            request_json[&#34;storage&#34;] = []
            request_json[&#34;storagePolicyCopyInfo&#34;][&#34;library&#34;][&#34;libraryName&#34;] = library_name

        if ddb_ma is not None or dedup_path is not None:
            maInfoList = []
            for ma, path in zip(ddb_ma, dedup_path):
                maInfoList.append({
                    &#34;mediaAgent&#34;: {
                        &#34;mediaAgentId&#34;: int(ma.media_agent_id),
                        &#34;mediaAgentName&#34;: ma.media_agent_name
                    },
                    &#34;subStoreList&#34;: [
                        {
                            &#34;accessPath&#34;: {
                                &#34;path&#34;: path
                            },
                            &#34;diskFreeThresholdMB&#34;: 5120,
                            &#34;diskFreeWarningThreshholdMB&#34;: 10240
                        }]
                })

            request_json[&#34;storagePolicyCopyInfo&#34;].update({
                &#34;storagePolicyFlags&#34;: {
                    &#34;blockLevelDedup&#34;: &#34;SET_TRUE&#34;,
                    &#34;enableGlobalDeduplication&#34;: &#34;SET_TRUE&#34;
                },
                &#34;dedupeFlags&#34;: {
                    &#34;enableDeduplication&#34;: &#34;SET_TRUE&#34;,
                    &#34;enableDASHFull&#34;: &#34;SET_TRUE&#34;,
                    &#34;hostGlobalDedupStore&#34;: &#34;SET_TRUE&#34;
                },
                &#34;DDBPartitionInfo&#34;: {
                    &#34;maInfoList&#34;: maInfoList
                }
            })
        elif tape_storage:
            request_json[&#34;storagePolicyCopyInfo&#34;].update({
                &#34;storagePolicyFlags&#34;: {
                    &#34;globalAuxCopyPolicy&#34;: &#34;SET_TRUE&#34;
                },
                &#34;copyFlags&#34;: {
                    &#34;preserveEncryptionModeAsInSource&#34;: &#34;SET_TRUE&#34;
                },
                &#34;extendedFlags&#34;: {
                    &#34;globalAuxCopyPolicy&#34;: &#34;SET_TRUE&#34;
                }
            })
        else:
            request_json[&#34;storagePolicyCopyInfo&#34;].update({
                &#34;storagePolicyFlags&#34;: {
                    &#34;globalStoragePolicy&#34;: &#34;SET_TRUE&#34;
                },
                &#34;copyFlags&#34;: {
                    &#34;preserveEncryptionModeAsInSource&#34;: &#34;SET_TRUE&#34;
                },
                &#34;extendedFlags&#34;: {
                    &#34;globalStoragePolicy&#34;: &#34;SET_TRUE&#34;
                }
            })

        # air gap protect storage
        if cloud_server_type == 400:
            del request_json[&#34;storage&#34;][0][&#34;path&#34;]
            request_json[&#34;storage&#34;][0][&#34;savedCredential&#34;] = {&#34;credentialId&#34;: 0}

            metallic_Storage = {
                &#34;region&#34;: [
                    {
                        &#34;regionName&#34;: region
                    }
                ],
                &#34;displayVendorId&#34;: display_vendor_id,
                &#34;vendorId&#34;: vendor_id
            }
            request_json[&#34;storage&#34;][0][&#34;metallicStorageInfo&#34;] = metallic_Storage

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._add_storage_pool_api, request_json
        )

        if flag:
            if response.json():
                error_code = response.json()[&#39;error&#39;][&#39;errorCode&#39;]

                if int(error_code) != 0:
                    error_message = response.json()[&#39;error&#39;][&#39;errorMessage&#39;]
                    o_str = &#39;Failed to create storage pool\nError: &#34;{0}&#34;&#39;

                    raise SDKException(&#39;StoragePool&#39;, &#39;102&#39;, o_str.format(error_message))
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        self.refresh()
        self._commcell_object.disk_libraries.refresh()
        return self.get(storage_pool_name)

    def delete(self, storage_pool_name):
        &#34;&#34;&#34;deletes the specified storage pool.

            Args:
                storage_pool_name (str)  --  name of the storage pool to delete

            Raises:
                SDKException:
                    if type of the storage pool name is not string

                    if failed to delete storage pool

                    if no storage pool exists with the given name

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;

        if not isinstance(storage_pool_name, str):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)
        else:
            storage_pool_name = storage_pool_name.lower()

            if self.has_storage_pool(storage_pool_name):
                storage_pool_id = self._storage_pools[storage_pool_name]

                delete_storage_pool = self._services[&#39;DELETE_STORAGE_POOL&#39;] % (storage_pool_id)

                flag, response = self._cvpysdk_object.make_request(&#39;DELETE&#39;, delete_storage_pool)

                if flag:
                    error_code = response.json()[&#39;error&#39;][&#39;errorCode&#39;]
                    if int(error_code) != 0:
                        error_message = response.json()[&#39;error&#39;][&#39;errorMessage&#39;]
                        o_str = f&#39;Failed to delete storage pools {storage_pool_name}&#39;
                        o_str += &#39;\nError: &#34;{0}&#34;&#39;.format(error_message)
                        raise SDKException(&#39;Storage&#39;, &#39;102&#39;, o_str)
                    else:
                        # initialize the storage pool again
                        # so the storage pool object has all the storage pools
                        self.refresh()
                        # as part of storage pool we might delete library so initialize the libraries again
                        self._commcell_object.disk_libraries.refresh()
                else:
                    response_string = self._update_response_(response.text)
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
            else:
                raise SDKException(
                    &#39;Storage&#39;,
                    &#39;102&#39;,
                    &#39;No storage pool exists with name: {0}&#39;.format(storage_pool_name)
                )
    def refresh(self):
        &#34;&#34;&#34;Refresh the list of storage pools associated to the Commcell.&#34;&#34;&#34;
        self._storage_pools = self._get_storage_pools()</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.storage_pool.StoragePools.all_storage_pools"><code class="name">var <span class="ident">all_storage_pools</span></code></dt>
<dd>
<div class="desc"><p>Returns dict of all the storage pools on this commcell</p>
<p>dict
-
consists of all storage pools added to the commcell</p>
<pre><code>{

    "storage_pool1_name": storage_pool1_id,

    "storage_pool2_name": storage_pool2_id
}
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage_pool.py#L329-L343" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def all_storage_pools(self):
    &#34;&#34;&#34;Returns dict of all the storage pools on this commcell

        dict    -   consists of all storage pools added to the commcell

            {

                &#34;storage_pool1_name&#34;: storage_pool1_id,

                &#34;storage_pool2_name&#34;: storage_pool2_id
            }

    &#34;&#34;&#34;
    return self._storage_pools</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.storage_pool.StoragePools.add"><code class="name flex">
<span>def <span class="ident">add</span></span>(<span>self, storage_pool_name, mountpath, media_agent, ddb_ma=None, dedup_path=None, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds a new storage pool to commcell</p>
<h2 id="args">Args</h2>
<p>storage_pool_name
(str)
&ndash;
name of new storage pool to add</p>
<p>mountpath
(str)
&ndash;
mount path for the storage pool</p>
<p>media_agent
(str/object)&ndash;
name or instance of media agent</p>
<p>ddb_ma
(list<str/object>/str/object)
&ndash;
list of (name of name or instance)
or name or instance of dedupe media agent</p>
<p>dedup_path
(list<str>/str)
&ndash;
list of paths or path where the DDB should be stored</p>
<p>**kwargs:
username
(str)
&ndash;
username to access the mountpath</p>
<pre><code>password        (str)       --  password to access the mountpath

credential_name (str)       --  name of the credential as in credential manager

cloud_server_type (int)     --  cloud server type of the cloud vendor (required)

region (str)                --  name of geographical region for storage (required for air gap protect)

vendor_id (int)             -- id for the cloud_vendor (eg - 3 for azure) (required for air gap protect pool)

display_vendor_id (int)     -- storage Class id for that vendor (eg - 401 for azure hot) (required for air gap protect pool)

region_id        (int)      --  Cloud Hypervisor specific region ID

tape_storage (boolean)      -- if library passed is tape library.
</code></pre>
<h2 id="returns">Returns</h2>
<p>StoragePool object if creation is successful</p>
<h2 id="raises">Raises</h2>
<p>Exception if creation is unsuccessful</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage_pool.py#L589-L846" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add(self, storage_pool_name, mountpath, media_agent, ddb_ma=None, dedup_path=None, **kwargs):
    &#34;&#34;&#34;
    Adds a new storage pool to commcell

    Args:
        storage_pool_name   (str)       --  name of new storage pool to add

        mountpath           (str)       --  mount path for the storage pool

        media_agent         (str/object)--  name or instance of media agent

        ddb_ma              (list&lt;str/object&gt;/str/object)   --  list of (name of name or instance)
                                                                    or name or instance of dedupe media agent

        dedup_path          (list&lt;str&gt;/str)       --  list of paths or path where the DDB should be stored

        **kwargs:
            username        (str)       --  username to access the mountpath

            password        (str)       --  password to access the mountpath

            credential_name (str)       --  name of the credential as in credential manager

            cloud_server_type (int)     --  cloud server type of the cloud vendor (required)

            region (str)                --  name of geographical region for storage (required for air gap protect)

            vendor_id (int)             -- id for the cloud_vendor (eg - 3 for azure) (required for air gap protect pool)

            display_vendor_id (int)     -- storage Class id for that vendor (eg - 401 for azure hot) (required for air gap protect pool)

            region_id        (int)      --  Cloud Hypervisor specific region ID

            tape_storage (boolean)      -- if library passed is tape library. 

    Returns:
        StoragePool object if creation is successful

    Raises:
        Exception if creation is unsuccessful
    &#34;&#34;&#34;
    username = kwargs.get(&#39;username&#39;, None)
    password = kwargs.get(&#39;password&#39;, None)
    credential_name = kwargs.get(&#39;credential_name&#39;, None)
    cloud_server_type = kwargs.get(&#39;cloud_server_type&#39;, None)
    library_name = kwargs.get(&#39;library_name&#39;, None)
    tape_storage = False

    region = kwargs.get(&#39;region&#39;, None)
    vendor_id = kwargs.get(&#39;vendor_id&#39;, None)
    display_vendor_id = kwargs.get(&#39;display_vendor_id&#39;, None)
    region_id = kwargs.get(&#39;region_id&#39;, None)

    if library_name:
        library_object = self._commcell_object.disk_libraries.get(library_name)
        library_type = library_object.library_properties.get(&#39;libraryType&#39;, None)
        tape_storage = True if library_type == 1 else tape_storage


    if ((ddb_ma is not None and not (isinstance(dedup_path, str) or isinstance(dedup_path, list))) or
            not (isinstance(storage_pool_name, str) or not isinstance(mountpath, str))):
        raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

    if isinstance(media_agent, MediaAgent):
        media_agent = media_agent
    elif isinstance(media_agent, str):
        media_agent = MediaAgent(self._commcell_object, media_agent)
    else:
        raise SDKException(&#39;Storage&#39;, &#39;103&#39;)

    if (isinstance(ddb_ma, str) or isinstance(ddb_ma, MediaAgent)) and isinstance(dedup_path, str):
        ddb_ma = [ddb_ma]
        dedup_path = [dedup_path]

    if isinstance(ddb_ma, list) and isinstance(dedup_path, list):
        if len(ddb_ma) != len(dedup_path):
            raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

    if library_name is not None and mountpath != &#39;&#39;:
        raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

    if ddb_ma is not None and (len(ddb_ma) &gt; 6 or len(dedup_path) &gt; 6):
        raise SDKException(&#39;Storage&#39;, &#39;110&#39;)

    if ddb_ma is not None:
        for i in range(len(ddb_ma)):
            if isinstance(ddb_ma[i], MediaAgent):
                ddb_ma[i] = ddb_ma[i]
            elif isinstance(ddb_ma[i], str):
                ddb_ma[i] = MediaAgent(self._commcell_object, ddb_ma[i])
            else:
                raise SDKException(&#39;Storage&#39;, &#39;103&#39;)

    request_json = {
        &#34;storagePolicyName&#34;: storage_pool_name,
        &#34;type&#34;: &#34;CVA_REGULAR_SP&#34;,
        &#34;copyName&#34;: &#34;Primary&#34;,
        &#34;numberOfCopies&#34;: 1,
        &#34;storage&#34;: [
            {
                &#34;path&#34;: mountpath,
                &#34;mediaAgent&#34;: {
                    &#34;mediaAgentId&#34;: int(media_agent.media_agent_id),
                    &#34;mediaAgentName&#34;: media_agent.media_agent_name
                }
            }
        ],
        &#34;storagePolicyCopyInfo&#34;: {
            &#34;copyType&#34;: &#34;SYNCHRONOUS&#34;,
            &#34;isFromGui&#34;: True,
            &#34;active&#34;: &#34;SET_TRUE&#34;,
            &#34;isDefault&#34;: &#34;SET_TRUE&#34;,
            &#34;numberOfStreamsToCombine&#34;: 1,
            &#34;retentionRules&#34;: {
                &#34;retentionFlags&#34;: {
                    &#34;enableDataAging&#34;: &#34;SET_TRUE&#34;
                },
                &#34;retainBackupDataForDays&#34;: -1,
                &#34;retainBackupDataForCycles&#34;: -1,
                &#34;retainArchiverDataForDays&#34;: -1
            },
            &#34;library&#34;: {
                &#34;libraryId&#34;: 0,
            },
            &#34;mediaAgent&#34;: {
                &#34;mediaAgentId&#34;: int(media_agent.media_agent_id),
                &#34;mediaAgentName&#34;: media_agent.media_agent_name
            }
        }
    }

    if cloud_server_type and int(cloud_server_type) &gt; 0:
        request_json[&#34;storage&#34;][0][&#34;deviceType&#34;] = cloud_server_type

    if region_id is not None:
        request_json[&#34;storage&#34;][0][&#34;metallicStorageInfo&#34;] = {
            &#34;region&#34;: [
                {
                    &#34;regionId&#34;: region_id
                }
            ],
            &#34;storageClass&#34;: [
                &#34;CONTAINER_DEFAULT&#34;
            ],
            &#34;replication&#34;: [
                &#34;NONE&#34;
            ]
        }
        request_json[&#34;region&#34;] = {&#34;regionId&#34;: region_id}

    if username is not None:
        request_json[&#34;storage&#34;][0][&#34;credentials&#34;] = {&#34;userName&#34;: username}

    if password is not None:
        request_json[&#34;storage&#34;][0][&#34;credentials&#34;][&#34;password&#34;] = b64encode(password.encode()).decode()

    if credential_name is not None:
        request_json[&#34;storage&#34;][0][&#34;savedCredential&#34;] = {&#34;credentialName&#34;: credential_name}

    if library_name is not None:
        request_json[&#34;storage&#34;] = []
        request_json[&#34;storagePolicyCopyInfo&#34;][&#34;library&#34;][&#34;libraryName&#34;] = library_name

    if ddb_ma is not None or dedup_path is not None:
        maInfoList = []
        for ma, path in zip(ddb_ma, dedup_path):
            maInfoList.append({
                &#34;mediaAgent&#34;: {
                    &#34;mediaAgentId&#34;: int(ma.media_agent_id),
                    &#34;mediaAgentName&#34;: ma.media_agent_name
                },
                &#34;subStoreList&#34;: [
                    {
                        &#34;accessPath&#34;: {
                            &#34;path&#34;: path
                        },
                        &#34;diskFreeThresholdMB&#34;: 5120,
                        &#34;diskFreeWarningThreshholdMB&#34;: 10240
                    }]
            })

        request_json[&#34;storagePolicyCopyInfo&#34;].update({
            &#34;storagePolicyFlags&#34;: {
                &#34;blockLevelDedup&#34;: &#34;SET_TRUE&#34;,
                &#34;enableGlobalDeduplication&#34;: &#34;SET_TRUE&#34;
            },
            &#34;dedupeFlags&#34;: {
                &#34;enableDeduplication&#34;: &#34;SET_TRUE&#34;,
                &#34;enableDASHFull&#34;: &#34;SET_TRUE&#34;,
                &#34;hostGlobalDedupStore&#34;: &#34;SET_TRUE&#34;
            },
            &#34;DDBPartitionInfo&#34;: {
                &#34;maInfoList&#34;: maInfoList
            }
        })
    elif tape_storage:
        request_json[&#34;storagePolicyCopyInfo&#34;].update({
            &#34;storagePolicyFlags&#34;: {
                &#34;globalAuxCopyPolicy&#34;: &#34;SET_TRUE&#34;
            },
            &#34;copyFlags&#34;: {
                &#34;preserveEncryptionModeAsInSource&#34;: &#34;SET_TRUE&#34;
            },
            &#34;extendedFlags&#34;: {
                &#34;globalAuxCopyPolicy&#34;: &#34;SET_TRUE&#34;
            }
        })
    else:
        request_json[&#34;storagePolicyCopyInfo&#34;].update({
            &#34;storagePolicyFlags&#34;: {
                &#34;globalStoragePolicy&#34;: &#34;SET_TRUE&#34;
            },
            &#34;copyFlags&#34;: {
                &#34;preserveEncryptionModeAsInSource&#34;: &#34;SET_TRUE&#34;
            },
            &#34;extendedFlags&#34;: {
                &#34;globalStoragePolicy&#34;: &#34;SET_TRUE&#34;
            }
        })

    # air gap protect storage
    if cloud_server_type == 400:
        del request_json[&#34;storage&#34;][0][&#34;path&#34;]
        request_json[&#34;storage&#34;][0][&#34;savedCredential&#34;] = {&#34;credentialId&#34;: 0}

        metallic_Storage = {
            &#34;region&#34;: [
                {
                    &#34;regionName&#34;: region
                }
            ],
            &#34;displayVendorId&#34;: display_vendor_id,
            &#34;vendorId&#34;: vendor_id
        }
        request_json[&#34;storage&#34;][0][&#34;metallicStorageInfo&#34;] = metallic_Storage

    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, self._add_storage_pool_api, request_json
    )

    if flag:
        if response.json():
            error_code = response.json()[&#39;error&#39;][&#39;errorCode&#39;]

            if int(error_code) != 0:
                error_message = response.json()[&#39;error&#39;][&#39;errorMessage&#39;]
                o_str = &#39;Failed to create storage pool\nError: &#34;{0}&#34;&#39;

                raise SDKException(&#39;StoragePool&#39;, &#39;102&#39;, o_str.format(error_message))
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    self.refresh()
    self._commcell_object.disk_libraries.refresh()
    return self.get(storage_pool_name)</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage_pool.StoragePools.add_air_gap_protect"><code class="name flex">
<span>def <span class="ident">add_air_gap_protect</span></span>(<span>self, storage_pool_name, media_agent, storage_type, storage_class, region_name, ddb_ma=None, dedup_path=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds a new air gap protect storage pool to commcell</p>
<pre><code>Args:
    storage_pool_name   (str)       --  name of new storage pool to add

    media_agent         (str/object)--  name or instance of media agent

    storage_type        (str)        -- name of the cloud vendor (str, eg - "Microsoft Azure storage") (same as UI)

    storage_class       (str)        -- storage class (str, eg - "Hot","Cool") (same as UI)

    region_name (str)      --  name of the geographical region for storage (same as UI)

    ddb_ma              (list&lt;str/object&gt;/str/object)   --  list of (name of name or instance)
                                                            or name or instance of dedupe media agent

    dedup_path          (list&lt;str&gt;/str)       --  list of paths or path where the DDB should be stored

Returns:
    StoragePool object if creation is successful

Raises:
    SDKException, if invalid parameters provided
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage_pool.py#L452-L534" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_air_gap_protect(self, storage_pool_name, media_agent, storage_type, storage_class, region_name,
                        ddb_ma=None, dedup_path=None):
    &#34;&#34;&#34;
        Adds a new air gap protect storage pool to commcell

            Args:
                storage_pool_name   (str)       --  name of new storage pool to add

                media_agent         (str/object)--  name or instance of media agent

                storage_type        (str)        -- name of the cloud vendor (str, eg - &#34;Microsoft Azure storage&#34;) (same as UI)

                storage_class       (str)        -- storage class (str, eg - &#34;Hot&#34;,&#34;Cool&#34;) (same as UI)

                region_name (str)      --  name of the geographical region for storage (same as UI)

                ddb_ma              (list&lt;str/object&gt;/str/object)   --  list of (name of name or instance)
                                                                        or name or instance of dedupe media agent

                dedup_path          (list&lt;str&gt;/str)       --  list of paths or path where the DDB should be stored

            Returns:
                StoragePool object if creation is successful

            Raises:
                SDKException, if invalid parameters provided

    &#34;&#34;&#34;
    license_type_dict = StoragePoolConstants.AIR_GAP_PROTECT_STORAGE_TYPES
    error_message = &#34;&#34;
    if storage_type.upper() in license_type_dict:
        available_storage_classes = license_type_dict[storage_type.upper()]
        if storage_class.upper() in available_storage_classes:
            vendor_id = available_storage_classes[storage_class.upper()][&#34;vendorId&#34;]
            display_vendor_id = available_storage_classes[storage_class.upper()][&#34;displayVendorId&#34;]
        else:
            error_message += f&#34;Invalid storage class provided. Valid storage class {list(available_storage_classes.keys())}&#34;
    else:
        error_message += f&#34;  Invalid storage type provided. {list(license_type_dict.keys())}&#34;

    if error_message:
        raise SDKException(&#39;Storage&#39;, &#39;101&#39;, error_message)

    region = None
    available_regions = []

    #  API call to fetch the region name - sourced directly from the vendor
    flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;GET&#39;, self._metallic_storage_api)
    if flag:
        if response.json():
            if &#34;storageInformation&#34; in response.json():
                for storage_info in response.json()[&#34;storageInformation&#34;]:
                    if (int(storage_info[&#34;vendorId&#34;]) == int(vendor_id)) and (int(storage_info[&#34;displayVendorId&#34;]) == int(display_vendor_id)):
                        for region_dict in storage_info[&#34;region&#34;]:
                            available_regions.append(region_dict[&#34;displayName&#34;])
                            if region_dict[&#34;displayName&#34;] == region_name:
                                region = region_dict[&#34;regionName&#34;]
                                break

                    if region:
                        break

                if region is None:
                    if not available_regions:
                        raise SDKException(&#39;Storage&#39;, &#39;101&#39;,
                                           f&#34;Active license is required to configure {storage_type} - {storage_class} Air Gap Protect storage&#34;)
                    else:
                        raise SDKException(&#39;Storage&#39;, &#39;101&#39;,
                                           f&#34;Invalid region: {region_name} ,\nValid regions: {available_regions}&#34;)
            else:
                raise SDKException(&#39;Storage&#39;, &#39;101&#39;, &#34;Unexpected response returned while fetching Air Gap Protect storage details&#34;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    # cloud server type for air gap protect is 400
    cloud_server_type = 400

    return self.add(storage_pool_name=storage_pool_name, mountpath=None, media_agent=media_agent, ddb_ma=ddb_ma,
                    dedup_path=dedup_path, cloud_server_type=cloud_server_type, region=region, vendor_id=vendor_id,
                    display_vendor_id=display_vendor_id)</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage_pool.StoragePools.delete"><code class="name flex">
<span>def <span class="ident">delete</span></span>(<span>self, storage_pool_name)</span>
</code></dt>
<dd>
<div class="desc"><p>deletes the specified storage pool.</p>
<h2 id="args">Args</h2>
<p>storage_pool_name (str)
&ndash;
name of the storage pool to delete</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the storage pool name is not string</p>
<pre><code>if failed to delete storage pool

if no storage pool exists with the given name

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage_pool.py#L848-L901" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete(self, storage_pool_name):
    &#34;&#34;&#34;deletes the specified storage pool.

        Args:
            storage_pool_name (str)  --  name of the storage pool to delete

        Raises:
            SDKException:
                if type of the storage pool name is not string

                if failed to delete storage pool

                if no storage pool exists with the given name

                if response is empty

                if response is not success

    &#34;&#34;&#34;

    if not isinstance(storage_pool_name, str):
        raise SDKException(&#39;Storage&#39;, &#39;101&#39;)
    else:
        storage_pool_name = storage_pool_name.lower()

        if self.has_storage_pool(storage_pool_name):
            storage_pool_id = self._storage_pools[storage_pool_name]

            delete_storage_pool = self._services[&#39;DELETE_STORAGE_POOL&#39;] % (storage_pool_id)

            flag, response = self._cvpysdk_object.make_request(&#39;DELETE&#39;, delete_storage_pool)

            if flag:
                error_code = response.json()[&#39;error&#39;][&#39;errorCode&#39;]
                if int(error_code) != 0:
                    error_message = response.json()[&#39;error&#39;][&#39;errorMessage&#39;]
                    o_str = f&#39;Failed to delete storage pools {storage_pool_name}&#39;
                    o_str += &#39;\nError: &#34;{0}&#34;&#39;.format(error_message)
                    raise SDKException(&#39;Storage&#39;, &#39;102&#39;, o_str)
                else:
                    # initialize the storage pool again
                    # so the storage pool object has all the storage pools
                    self.refresh()
                    # as part of storage pool we might delete library so initialize the libraries again
                    self._commcell_object.disk_libraries.refresh()
            else:
                response_string = self._update_response_(response.text)
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        else:
            raise SDKException(
                &#39;Storage&#39;,
                &#39;102&#39;,
                &#39;No storage pool exists with name: {0}&#39;.format(storage_pool_name)
            )</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage_pool.StoragePools.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the id of the storage pool for the given storage pool name.</p>
<h2 id="args">Args</h2>
<p>name
(str)
&ndash;
name of the storage pool to get the id of</p>
<h2 id="returns">Returns</h2>
<p>str
-
id of the storage pool for the given storage pool name</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if no storage pool exists with the given name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage_pool.py#L357-L377" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, name):
    &#34;&#34;&#34;Returns the id of the storage pool for the given storage pool name.

        Args:
            name    (str)   --  name of the storage pool to get the id of

        Returns:
            str     -   id of the storage pool for the given storage pool name

        Raises:
            SDKException:
                if no storage pool exists with the given name

    &#34;&#34;&#34;
    self.refresh()
    name = name.lower()

    if self.has_storage_pool(name):
        return StoragePool(self._commcell_object, name, storage_pool_id=self._storage_pools[name])
    else:
        raise SDKException(&#39;StoragePool&#39;, &#39;103&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage_pool.StoragePools.get_air_gap_protect"><code class="name flex">
<span>def <span class="ident">get_air_gap_protect</span></span>(<span>self, company_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the list of air gap protect storage pools in the commcell.</p>
<h2 id="args">Args</h2>
<p>company_id (int) &ndash; id of the company to get the air gap protect storage pools for
(optional, default is None which returns all air gap protect storage pools)</p>
<h2 id="returns">Returns</h2>
<p>dict - dictionary of air gap protect storage pools with name as key and id as value</p>
<pre><code>    {
        "storage_pool1_name": storage_pool1_id,
        "storage_pool2_name": storage_pool2_id
    }
</code></pre>
<h2 id="raises">Raises</h2>
<p>SDKException:
if response is empty</p>
<pre><code>if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage_pool.py#L536-L587" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_air_gap_protect(self, company_id = None):
    &#34;&#34;&#34;
    Returns the list of air gap protect storage pools in the commcell.
    
    Args:
        company_id (int) -- id of the company to get the air gap protect storage pools for
                            (optional, default is None which returns all air gap protect storage pools)
    
    Returns:
        dict - dictionary of air gap protect storage pools with name as key and id as value
            
                {
                    &#34;storage_pool1_name&#34;: storage_pool1_id,
                    &#34;storage_pool2_name&#34;: storage_pool2_id
                }  
    
    Raises:
        SDKException:
            if response is empty

            if response is not success
    &#34;&#34;&#34;
    headers = self._commcell_object._headers.copy()
    headers[&#39;Accept&#39;] = &#39;application/json&#39;
    if company_id:
        headers[&#39;onlygetcompanyownedentities&#39;] = &#39;1&#39;
        headers[&#39;operatorcompanyid&#39;] = f&#39;{company_id}&#39;

    flag, response = self._cvpysdk_object.make_request(
        &#39;GET&#39;, self.__get_agp_storage_api, headers=headers
    )

    if flag:
        storage_pools = {}
        response = response.json()
        if response is None or response.get(&#39;cloudStorage&#39;) is None:
            storage_pool_list = []
        else:
            storage_pool_list = response[&#39;cloudStorage&#39;]
        if not isinstance(storage_pool_list, list):
            storage_pool_list = [storage_pool_list]
        if response:
            for pool in storage_pool_list:
                name = pool[&#39;name&#39;]
                storage_pool_id = pool[&#39;id&#39;]

                storage_pools[name] = storage_pool_id

        return storage_pools
    else:
        response_string = self._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage_pool.StoragePools.get_storage_pools_for_a_company"><code class="name flex">
<span>def <span class="ident">get_storage_pools_for_a_company</span></span>(<span>self, company_id, storage_type: <a title="cvpysdk.storage_pool.StorageType" href="#cvpysdk.storage_pool.StorageType">StorageType</a> = None)</span>
</code></dt>
<dd>
<div class="desc"><p>Gets all the storage pools associated with the Commcell environment.</p>
<h2 id="args">Args</h2>
<p>company_id - id of the company for which the associated storge pools are to be fetched</p>
<h2 id="returns">Returns</h2>
<p>dict
-
consists of all storage pools added to the commcell</p>
<pre><code>{
    "storage_pool1_name": storage_pool1_id,

    "storage_pool2_name": storage_pool2_id
}
</code></pre>
<h2 id="raises">Raises</h2>
<p>SDKException:
if response is empty</p>
<pre><code>if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage_pool.py#L272-L327" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_storage_pools_for_a_company(self, company_id, storage_type: StorageType = None):
    &#34;&#34;&#34;Gets all the storage pools associated with the Commcell environment.

        Args:
            company_id - id of the company for which the associated storge pools are to be fetched

        Returns:
            dict    -   consists of all storage pools added to the commcell

                {
                    &#34;storage_pool1_name&#34;: storage_pool1_id,

                    &#34;storage_pool2_name&#34;: storage_pool2_id
                }

        Raises:
            SDKException:
                if response is empty

                if response is not success

    &#34;&#34;&#34;
    headers = self._commcell_object._headers.copy()
    headers[&#39;Accept&#39;] = &#39;application/json&#39;
    headers[&#39;onlygetcompanyownedentities&#39;] = &#39;1&#39;
    headers[&#39;operatorcompanyid&#39;] = f&#39;{company_id}&#39;

    flag, response = self._cvpysdk_object.make_request(
        &#39;GET&#39;, self._storage_pools_api, headers=headers
    )

    if flag:
        storage_pools = {}
        response = response.json()
        if response is None or response.get(&#39;storagePoolList&#39;) is None:
            storage_pool_list = []
        else:
            storage_pool_list = response[&#39;storagePoolList&#39;]
        if not isinstance(storage_pool_list, list):
            storage_pool_list = [storage_pool_list]
        if response:
            for pool in storage_pool_list:
                if storage_type and pool[&#39;storageType&#39;] != storage_type:
                    continue
                # skip agp pools for cloud storage type
                if storage_type == StorageType.CLOUD and 401 &lt;= pool[&#39;libraryVendorType&#39;] &lt;= 499:
                    continue
                name = pool[&#39;storagePoolEntity&#39;][&#39;storagePoolName&#39;]
                storage_pool_id = pool[&#39;storagePoolEntity&#39;][&#39;storagePoolId&#39;]

                storage_pools[name] = storage_pool_id

        return storage_pools
    else:
        response_string = self._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage_pool.StoragePools.has_storage_pool"><code class="name flex">
<span>def <span class="ident">has_storage_pool</span></span>(<span>self, name)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks if a storage pool exists in the Commcell with the input storage pool name.</p>
<h2 id="args">Args</h2>
<p>name
(str)
&ndash;
name of the storage pool</p>
<h2 id="returns">Returns</h2>
<p>bool
-
boolean output whether the storage pool exists in the commcell or not</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage_pool.py#L345-L355" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_storage_pool(self, name):
    &#34;&#34;&#34;Checks if a storage pool exists in the Commcell with the input storage pool name.

        Args:
            name    (str)   --  name of the storage pool

        Returns:
            bool    -   boolean output whether the storage pool exists in the commcell or not

    &#34;&#34;&#34;
    return self._storage_pools and name.lower() in self._storage_pools</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage_pool.StoragePools.hyperscale_create_storage_pool"><code class="name flex">
<span>def <span class="ident">hyperscale_create_storage_pool</span></span>(<span>self, storage_pool_name, media_agents)</span>
</code></dt>
<dd>
<div class="desc"><p>Create new storage pool for hyperscale</p>
<h2 id="args">Args</h2>
<p>storage_pool_name (string) &ndash; Name of the storage pools to create</p>
<p>media_agents
(List)
&ndash; List of 3 media agents with name's(str)
or instance of media agent's(object)</p>
<dl>
<dt><strong><code>Example</code></strong></dt>
<dd>["ma1","ma2","ma3"]</dd>
</dl>
<h2 id="return">Return</h2>
<p>flag, response &ndash; response returned by the REST API call</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage_pool.py#L379-L450" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def hyperscale_create_storage_pool(self, storage_pool_name, media_agents):
    &#34;&#34;&#34;
        Create new storage pool for hyperscale
        Args:
            storage_pool_name (string) -- Name of the storage pools to create

            media_agents      (List)   -- List of 3 media agents with name&#39;s(str)
                                            or instance of media agent&#39;s(object)

            Example: [&#34;ma1&#34;,&#34;ma2&#34;,&#34;ma3&#34;]

        Return:
             flag, response -- response returned by the REST API call
    &#34;&#34;&#34;

    if not isinstance(media_agents, list):
        raise SDKException(&#39;Storage&#39;, &#39;101&#39;)
    if not isinstance(storage_pool_name, str):
        raise SDKException(&#39;Storage&#39;, &#39;101&#39;)

    mediagent_obj = []
    for media_agent in media_agents:
        if isinstance(media_agent, MediaAgent):
            mediagent_obj.append(media_agent)
        elif isinstance(media_agent, str):
            mediagent_obj.append(self._commcell_object.media_agents.get(media_agent))
        else:
            raise SDKException(&#39;Storage&#39;, &#39;103&#39;)
    if len(mediagent_obj) &lt;= 2:
        raise SDKException(&#39;Storage&#39;, &#39;102&#39;, &#34;minimum 3 media agents are required&#34;)

    request_xml = &#34;&#34;&#34;&lt;App_CreateStoragePolicyReq storagePolicyName=&#34;{0}&#34; copyName=&#34;{0}_Primary&#34; type=&#34;1&#34;
                                 numberOfCopies=&#34;1&#34;&gt;
                                &lt;storagePolicyCopyInfo&gt;
                                    &lt;storagePolicyFlags scaleOutStoragePolicy=&#34;1&#34;/&gt;
                                &lt;/storagePolicyCopyInfo&gt;
                                &lt;storage&gt;
                                    &lt;mediaAgent mediaAgentId=&#34;{4}&#34; mediaAgentName=&#34;{1}&#34; displayName=&#34;{1}&#34;/&gt;
                                &lt;/storage&gt;
                                &lt;storage&gt;
                                    &lt;mediaAgent mediaAgentId=&#34;{5}&#34; mediaAgentName=&#34;{2}&#34; displayName=&#34;{2}&#34;/&gt;
                                &lt;/storage&gt;
                                &lt;storage&gt;
                                    &lt;mediaAgent mediaAgentId=&#34;{6}&#34; mediaAgentName=&#34;{3}&#34; displayName=&#34;{3}&#34;/&gt;
                                &lt;/storage&gt;
                                &lt;scaleoutConfiguration configurationType=&#34;1&#34;/&gt;
                            &lt;/App_CreateStoragePolicyReq&gt;
                            &#34;&#34;&#34;.format(storage_pool_name, mediagent_obj[0].media_agent_name,
                                       mediagent_obj[1].media_agent_name, mediagent_obj[2].media_agent_name,
                                       mediagent_obj[0].media_agent_id, mediagent_obj[1].media_agent_id,
                                       mediagent_obj[2].media_agent_id)

    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, self._add_storage_pool_api, request_xml
    )
    if flag:
        if response.json():
            error_code = response.json()[&#39;error&#39;][&#39;errorCode&#39;]

            if int(error_code) != 0:
                error_message = response.json()[&#39;error&#39;][&#39;errorMessage&#39;]
                o_str = &#39;Failed to create storage pool\nError: &#34;{0}&#34;&#39;

                raise SDKException(&#39;StoragePool&#39;, &#39;102&#39;, o_str.format(error_message))
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    self.refresh()
    return self.get(storage_pool_name)</code></pre>
</details>
</dd>
<dt id="cvpysdk.storage_pool.StoragePools.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the list of storage pools associated to the Commcell.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage_pool.py#L902-L904" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the list of storage pools associated to the Commcell.&#34;&#34;&#34;
    self._storage_pools = self._get_storage_pools()</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.storage_pool.StorageType"><code class="flex name class">
<span>class <span class="ident">StorageType</span></span>
<span>(</span><span>value, names=None, *, module=None, qualname=None, type=None, start=1)</span>
</code></dt>
<dd>
<div class="desc"><p>Class IntEnum to represent different storage types</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage_pool.py#L136-L141" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class StorageType(IntEnum):
    &#34;&#34;&#34;Class IntEnum to represent different storage types&#34;&#34;&#34;
    DISK = 1,
    CLOUD = 2,
    HYPERSCALE = 3,
    TAPE = 4</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li>enum.IntEnum</li>
<li>builtins.int</li>
<li>enum.Enum</li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.storage_pool.StorageType.CLOUD"><code class="name">var <span class="ident">CLOUD</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.storage_pool.StorageType.DISK"><code class="name">var <span class="ident">DISK</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.storage_pool.StorageType.HYPERSCALE"><code class="name">var <span class="ident">HYPERSCALE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.storage_pool.StorageType.TAPE"><code class="name">var <span class="ident">TAPE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.storage_pool.WORMLockType"><code class="flex name class">
<span>class <span class="ident">WORMLockType</span></span>
<span>(</span><span>value, names=None, *, module=None, qualname=None, type=None, start=1)</span>
</code></dt>
<dd>
<div class="desc"><p>Class IntFlag to represent different WORM lock types flag values</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/storage_pool.py#L914-L919" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class WORMLockType(IntFlag):
    &#34;&#34;&#34;Class IntFlag to represent different WORM lock types flag values&#34;&#34;&#34;
    COPY = 1,  # copy level software WORM (compliance lock)
    STORAGE = 2,  # storage level hardware WORM
    OBJECT = 4,  # object level storage WORM
    BUCKET = 8  # bucket level storage WORM</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li>enum.IntFlag</li>
<li>builtins.int</li>
<li>enum.Flag</li>
<li>enum.Enum</li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.storage_pool.WORMLockType.BUCKET"><code class="name">var <span class="ident">BUCKET</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.storage_pool.WORMLockType.COPY"><code class="name">var <span class="ident">COPY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.storage_pool.WORMLockType.OBJECT"><code class="name">var <span class="ident">OBJECT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.storage_pool.WORMLockType.STORAGE"><code class="name">var <span class="ident">STORAGE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#storagepools">StoragePools</a><ul>
<li><a href="#attributes">Attributes</a></li>
</ul>
</li>
<li><a href="#storagepool">StoragePool</a></li>
<li><a href="#storagepool-instance-attributes">StoragePool instance attributes</a></li>
<li><a href="#todo-check-with-mm-api-team-to-get-the-response-in-json">TODO: check with MM API team to get the response in JSON</a></li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.storage_pool.StoragePool" href="#cvpysdk.storage_pool.StoragePool">StoragePool</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.storage_pool.StoragePool.copy_id" href="#cvpysdk.storage_pool.StoragePool.copy_id">copy_id</a></code></li>
<li><code><a title="cvpysdk.storage_pool.StoragePool.copy_name" href="#cvpysdk.storage_pool.StoragePool.copy_name">copy_name</a></code></li>
<li><code><a title="cvpysdk.storage_pool.StoragePool.enable_compliance_lock" href="#cvpysdk.storage_pool.StoragePool.enable_compliance_lock">enable_compliance_lock</a></code></li>
<li><code><a title="cvpysdk.storage_pool.StoragePool.enable_worm_storage_lock" href="#cvpysdk.storage_pool.StoragePool.enable_worm_storage_lock">enable_worm_storage_lock</a></code></li>
<li><code><a title="cvpysdk.storage_pool.StoragePool.get_copy" href="#cvpysdk.storage_pool.StoragePool.get_copy">get_copy</a></code></li>
<li><code><a title="cvpysdk.storage_pool.StoragePool.global_policy_name" href="#cvpysdk.storage_pool.StoragePool.global_policy_name">global_policy_name</a></code></li>
<li><code><a title="cvpysdk.storage_pool.StoragePool.hyperscale_add_nodes" href="#cvpysdk.storage_pool.StoragePool.hyperscale_add_nodes">hyperscale_add_nodes</a></code></li>
<li><code><a title="cvpysdk.storage_pool.StoragePool.hyperscale_reconfigure_storage_pool" href="#cvpysdk.storage_pool.StoragePool.hyperscale_reconfigure_storage_pool">hyperscale_reconfigure_storage_pool</a></code></li>
<li><code><a title="cvpysdk.storage_pool.StoragePool.hyperscale_replace_disk" href="#cvpysdk.storage_pool.StoragePool.hyperscale_replace_disk">hyperscale_replace_disk</a></code></li>
<li><code><a title="cvpysdk.storage_pool.StoragePool.is_bucket_level_worm_lock_enabled" href="#cvpysdk.storage_pool.StoragePool.is_bucket_level_worm_lock_enabled">is_bucket_level_worm_lock_enabled</a></code></li>
<li><code><a title="cvpysdk.storage_pool.StoragePool.is_compliance_lock_enabled" href="#cvpysdk.storage_pool.StoragePool.is_compliance_lock_enabled">is_compliance_lock_enabled</a></code></li>
<li><code><a title="cvpysdk.storage_pool.StoragePool.is_object_level_worm_lock_enabled" href="#cvpysdk.storage_pool.StoragePool.is_object_level_worm_lock_enabled">is_object_level_worm_lock_enabled</a></code></li>
<li><code><a title="cvpysdk.storage_pool.StoragePool.is_worm_storage_lock_enabled" href="#cvpysdk.storage_pool.StoragePool.is_worm_storage_lock_enabled">is_worm_storage_lock_enabled</a></code></li>
<li><code><a title="cvpysdk.storage_pool.StoragePool.refresh" href="#cvpysdk.storage_pool.StoragePool.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.storage_pool.StoragePool.storage_pool_id" href="#cvpysdk.storage_pool.StoragePool.storage_pool_id">storage_pool_id</a></code></li>
<li><code><a title="cvpysdk.storage_pool.StoragePool.storage_pool_name" href="#cvpysdk.storage_pool.StoragePool.storage_pool_name">storage_pool_name</a></code></li>
<li><code><a title="cvpysdk.storage_pool.StoragePool.storage_pool_properties" href="#cvpysdk.storage_pool.StoragePool.storage_pool_properties">storage_pool_properties</a></code></li>
<li><code><a title="cvpysdk.storage_pool.StoragePool.storage_pool_type" href="#cvpysdk.storage_pool.StoragePool.storage_pool_type">storage_pool_type</a></code></li>
<li><code><a title="cvpysdk.storage_pool.StoragePool.storage_type" href="#cvpysdk.storage_pool.StoragePool.storage_type">storage_type</a></code></li>
<li><code><a title="cvpysdk.storage_pool.StoragePool.storage_vendor" href="#cvpysdk.storage_pool.StoragePool.storage_vendor">storage_vendor</a></code></li>
<li><code><a title="cvpysdk.storage_pool.StoragePool.update_security_associations" href="#cvpysdk.storage_pool.StoragePool.update_security_associations">update_security_associations</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.storage_pool.StoragePoolType" href="#cvpysdk.storage_pool.StoragePoolType">StoragePoolType</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.storage_pool.StoragePoolType.DEDUPLICATION" href="#cvpysdk.storage_pool.StoragePoolType.DEDUPLICATION">DEDUPLICATION</a></code></li>
<li><code><a title="cvpysdk.storage_pool.StoragePoolType.NON_DEDUPLICATION" href="#cvpysdk.storage_pool.StoragePoolType.NON_DEDUPLICATION">NON_DEDUPLICATION</a></code></li>
<li><code><a title="cvpysdk.storage_pool.StoragePoolType.SCALE_OUT" href="#cvpysdk.storage_pool.StoragePoolType.SCALE_OUT">SCALE_OUT</a></code></li>
<li><code><a title="cvpysdk.storage_pool.StoragePoolType.SECONDARY_COPY" href="#cvpysdk.storage_pool.StoragePoolType.SECONDARY_COPY">SECONDARY_COPY</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.storage_pool.StoragePools" href="#cvpysdk.storage_pool.StoragePools">StoragePools</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.storage_pool.StoragePools.add" href="#cvpysdk.storage_pool.StoragePools.add">add</a></code></li>
<li><code><a title="cvpysdk.storage_pool.StoragePools.add_air_gap_protect" href="#cvpysdk.storage_pool.StoragePools.add_air_gap_protect">add_air_gap_protect</a></code></li>
<li><code><a title="cvpysdk.storage_pool.StoragePools.all_storage_pools" href="#cvpysdk.storage_pool.StoragePools.all_storage_pools">all_storage_pools</a></code></li>
<li><code><a title="cvpysdk.storage_pool.StoragePools.delete" href="#cvpysdk.storage_pool.StoragePools.delete">delete</a></code></li>
<li><code><a title="cvpysdk.storage_pool.StoragePools.get" href="#cvpysdk.storage_pool.StoragePools.get">get</a></code></li>
<li><code><a title="cvpysdk.storage_pool.StoragePools.get_air_gap_protect" href="#cvpysdk.storage_pool.StoragePools.get_air_gap_protect">get_air_gap_protect</a></code></li>
<li><code><a title="cvpysdk.storage_pool.StoragePools.get_storage_pools_for_a_company" href="#cvpysdk.storage_pool.StoragePools.get_storage_pools_for_a_company">get_storage_pools_for_a_company</a></code></li>
<li><code><a title="cvpysdk.storage_pool.StoragePools.has_storage_pool" href="#cvpysdk.storage_pool.StoragePools.has_storage_pool">has_storage_pool</a></code></li>
<li><code><a title="cvpysdk.storage_pool.StoragePools.hyperscale_create_storage_pool" href="#cvpysdk.storage_pool.StoragePools.hyperscale_create_storage_pool">hyperscale_create_storage_pool</a></code></li>
<li><code><a title="cvpysdk.storage_pool.StoragePools.refresh" href="#cvpysdk.storage_pool.StoragePools.refresh">refresh</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.storage_pool.StorageType" href="#cvpysdk.storage_pool.StorageType">StorageType</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.storage_pool.StorageType.CLOUD" href="#cvpysdk.storage_pool.StorageType.CLOUD">CLOUD</a></code></li>
<li><code><a title="cvpysdk.storage_pool.StorageType.DISK" href="#cvpysdk.storage_pool.StorageType.DISK">DISK</a></code></li>
<li><code><a title="cvpysdk.storage_pool.StorageType.HYPERSCALE" href="#cvpysdk.storage_pool.StorageType.HYPERSCALE">HYPERSCALE</a></code></li>
<li><code><a title="cvpysdk.storage_pool.StorageType.TAPE" href="#cvpysdk.storage_pool.StorageType.TAPE">TAPE</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.storage_pool.WORMLockType" href="#cvpysdk.storage_pool.WORMLockType">WORMLockType</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.storage_pool.WORMLockType.BUCKET" href="#cvpysdk.storage_pool.WORMLockType.BUCKET">BUCKET</a></code></li>
<li><code><a title="cvpysdk.storage_pool.WORMLockType.COPY" href="#cvpysdk.storage_pool.WORMLockType.COPY">COPY</a></code></li>
<li><code><a title="cvpysdk.storage_pool.WORMLockType.OBJECT" href="#cvpysdk.storage_pool.WORMLockType.OBJECT">OBJECT</a></code></li>
<li><code><a title="cvpysdk.storage_pool.WORMLockType.STORAGE" href="#cvpysdk.storage_pool.WORMLockType.STORAGE">STORAGE</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>