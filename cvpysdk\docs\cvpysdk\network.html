<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.network API documentation</title>
<meta name="description" content="Main file for performing network related operations on a client/client group …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.network</code></h1>
</header>
<section id="section-intro">
<p>Main file for performing network related operations on a client/client group</p>
<h2 id="network">Network</h2>
<p><strong>init</strong>(class_object)
&ndash;
initialize object of the Network class</p>
<p>_get_network_properties()
&ndash;
returns all the existing network properties on a
client/client group</p>
<p>configure_network_settings
&ndash;
get the value
for configureFirewallSettings</p>
<p>configure_network_settings(val)
&ndash;
set the value
for configureFirewallSettings</p>
<p>trivial_config
&ndash;
get the value for isTrivialConfig</p>
<p>trivial_config(val)
&ndash;
set the value for isTrivialConfig</p>
<p>roaming_client
&ndash;
get the value for isRoamingClient</p>
<p>roaming_client(val)
&ndash;
set the value for isRoamingClient</p>
<p>tunnel_connection_port
&ndash;
get the tunnel connection port on the
client/client group</p>
<p>tunnel_connection_port(val)
&ndash;
set the tunnel connection port on the
client/client group</p>
<p>force_ssl
&ndash;
get the value for foreceSSL</p>
<p>force_ssl(val)
&ndash;
set the value for foreceSSL</p>
<p>tunnel_init_seconds
&ndash;
get the value for tunnelInitSeconds</p>
<p>tunnel_init_seconds(val)
&ndash;
set the value for tunnelInitSeconds</p>
<p>lockdown
&ndash;
get the value for lockdown</p>
<p>lockdown(val)
&ndash;
set the value for lockdown</p>
<p>bind_open_ports
&ndash;
get the value for bindOpenPortsOnly</p>
<p>bind_open_ports(val)
&ndash;
set the value for bindOpenPortsOnly</p>
<p>proxy
&ndash;
get the value for isDMZ</p>
<p>proxy(val)
&ndash;
set the value for isDMZ</p>
<p>keep_alive_seconds
&ndash;
get the value for keepAliveSeconds</p>
<p>keep_alive_seconds(val)
&ndash;
set the value for keepAliveSeconds</p>
<p>incoming_connections
&ndash;
get the list of incoming connections on the
client/client group</p>
<p>set_incoming_connections()
&ndash;
sets the incoming connections on the client/client
group with the list of values provided</p>
<p>additional_ports
&ndash;
get the list of additional ports on the
client/client group</p>
<p>set_additional_ports()
&ndash;
sets the range of additional ports on the client/client
group provided as list and tunnel port</p>
<p>outgoing_routes
&ndash;
get the list of outgoing routes on the
client/client group</p>
<p>set_outgoing_routes()
&ndash;
sets the outgoing routes on the client/client group
with the list of values provided</p>
<p>tppm_settings
&ndash;
get the list of tppm settings on the client</p>
<p>set_tppm_settings(tppm_settings)
&ndash;
set the tppm on the client with the list of
values provided</p>
<p>_advanced_network_config()
&ndash;
set advanced network configuration on the
client/client group</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/network.py#L1-L922" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Main file for performing network related operations on a client/client group

Network:

    __init__(class_object)            --    initialize object of the Network class

    _get_network_properties()         --    returns all the existing network properties on a
                                            client/client group

    configure_network_settings        --    get the value  for configureFirewallSettings

    configure_network_settings(val)   --    set the value  for configureFirewallSettings

    trivial_config                    --    get the value for isTrivialConfig

    trivial_config(val)               --    set the value for isTrivialConfig

    roaming_client                    --    get the value for isRoamingClient

    roaming_client(val)               --    set the value for isRoamingClient

    tunnel_connection_port            --    get the tunnel connection port on the
                                            client/client group

    tunnel_connection_port(val)       --    set the tunnel connection port on the
                                            client/client group

    force_ssl                         --    get the value for foreceSSL

    force_ssl(val)                    --    set the value for foreceSSL

    tunnel_init_seconds               --    get the value for tunnelInitSeconds

    tunnel_init_seconds(val)          --    set the value for tunnelInitSeconds

    lockdown                          --    get the value for lockdown

    lockdown(val)                     --    set the value for lockdown

    bind_open_ports                   --    get the value for bindOpenPortsOnly

    bind_open_ports(val)              --    set the value for bindOpenPortsOnly

    proxy                             --    get the value for isDMZ

    proxy(val)                        --    set the value for isDMZ

    keep_alive_seconds                --    get the value for keepAliveSeconds

    keep_alive_seconds(val)           --    set the value for keepAliveSeconds

    incoming_connections              --    get the list of incoming connections on the
                                            client/client group

    set_incoming_connections()        --    sets the incoming connections on the client/client
                                            group with the list of values provided

    additional_ports                  --    get the list of additional ports on the
                                            client/client group

    set_additional_ports()            --    sets the range of additional ports on the client/client
                                            group provided as list and tunnel port

    outgoing_routes                   --    get the list of outgoing routes on the
                                            client/client group

    set_outgoing_routes()             --    sets the outgoing routes on the client/client group
                                            with the list of values provided

    tppm_settings                     --    get the list of tppm settings on the client

    set_tppm_settings(tppm_settings)  --    set the tppm on the client with the list of
                                            values provided

    _advanced_network_config()        --    set advanced network configuration on the
                                            client/client group


&#34;&#34;&#34;

from __future__ import absolute_import
from __future__ import unicode_literals

from .exception import SDKException


class Network(object):
    &#34;&#34;&#34;Class for performing network related operations on a client or client group&#34;&#34;&#34;

    def __init__(self, class_object):
        &#34;&#34;&#34;Initialize the Network class object

            Args:
                class_object (object)  --  instance of the client/client group class



        &#34;&#34;&#34;
        from .client import Client
        from .clientgroup import ClientGroup

        self._class_object = class_object
        self._commcell_object = self._class_object._commcell_object
        self.flag = &#34;&#34;
        if isinstance(class_object, Client):
            self._client_object = class_object
            self.flag = &#34;CLIENT&#34;

        elif isinstance(class_object, ClientGroup):
            self._clientgroup_object = class_object
            self.flag = &#34;CLIENTGROUP&#34;

        self._config_network_settings = None
        self._is_trivial_config = False
        self._proxy_entities = []
        self._port_range = []
        self._network_outgoing_routes = []
        self._restriction_to = []
        self._tppm_settings = []
        self._is_roaming_client = False
        self._tunnel_connection_port = 8403
        self._force_ssl = False
        self._tunnel_init_seconds = 30
        self._lockdown = False
        self._bind_open_ports_only = False
        self._is_dmz = False
        self._keep_alive_seconds = 300
        self.enable_network_settings = None

        self._incoming_connection_type = {
            0: &#39;RESTRICTED&#39;,
            1: &#39;BLOCKED&#39;
        }

        self._firewall_outgoing_route_type = {
            0: &#39;DIRECT&#39;,
            1: &#39;VIA_GATEWAY&#39;,
            2: &#39;VIA_PROXY&#39;
        }

        self._firewall_outgoing_connection_protocol = {
            0: &#39;HTTP&#39;,
            1: &#39;HTTPS&#39;,
            2: &#39;HTTPS_AuthOnly&#39;,
            3: &#39;RAW_PROTOCOL&#39;
        }

        self._tppm_type = {
            2: &#39;WEB_SERVER_FOR_IIS_SERVER&#39;,
            3: &#39;COMMSERVE&#39;,
            5: &#39;REPORTS&#39;,
            6: &#39;CUSTOM_REPORT_ENGINE&#39;
        }

        self._get_network_properties()

    def _get_network_properties(self):
        &#34;&#34;&#34;Get all the existing network properties on a client/client group and retain each of them


        &#34;&#34;&#34;
        if self.flag == &#34;CLIENT&#34;:
            network_prop = self._client_object._properties[&#39;clientProps&#39;]

        elif self.flag == &#34;CLIENTGROUP&#34;:
            network_prop = self._clientgroup_object._properties

        if &#39;firewallConfiguration&#39; in network_prop:
            self._config_network_settings = (network_prop[&#39;firewallConfiguration&#39;][
                &#39;configureFirewallSettings&#39;])

            self._is_trivial_config = network_prop[&#39;firewallConfiguration&#39;][&#39;isTrivialConfig&#39;]

            if &#39;portRange&#39; in network_prop[&#39;firewallConfiguration&#39;]:
                self._port_range = network_prop[&#39;firewallConfiguration&#39;][&#39;portRange&#39;]

            if &#39;proxyEntities&#39; in network_prop[&#39;firewallConfiguration&#39;]:
                self._proxy_entities = network_prop[&#39;firewallConfiguration&#39;][&#39;proxyEntities&#39;]

            if &#39;firewallOutGoingRoutes&#39; in network_prop[&#39;firewallConfiguration&#39;]:
                self._network_outgoing_routes = (network_prop[&#39;firewallConfiguration&#39;][
                    &#39;firewallOutGoingRoutes&#39;])

            if &#39;restrictionTo&#39; in network_prop[&#39;firewallConfiguration&#39;]:
                self._restriction_to = network_prop[&#39;firewallConfiguration&#39;][&#39;restrictionTo&#39;]

            if &#39;firewallOptions&#39; in network_prop[&#39;firewallConfiguration&#39;]:
                self._network_options = network_prop[&#39;firewallConfiguration&#39;][&#39;firewallOptions&#39;]

            if &#39;isRoamingClient&#39; in network_prop[&#39;firewallConfiguration&#39;]:
                self._is_roaming_client = (network_prop[&#39;firewallConfiguration&#39;][
                    &#39;firewallOptions&#39;][&#39;isRoamingClient&#39;])

            self._tunnel_connection_port = network_prop[&#39;firewallConfiguration&#39;][&#39;firewallOptions&#39;][
                &#39;tunnelconnectionPort&#39;]

            self._force_ssl = network_prop[&#39;firewallConfiguration&#39;][&#39;firewallOptions&#39;][&#39;foreceSSL&#39;]

            self._tunnel_init_seconds = (network_prop[&#39;firewallConfiguration&#39;][
                &#39;firewallOptions&#39;][&#39;tunnelInitSeconds&#39;])

            self._lockdown = network_prop[&#39;firewallConfiguration&#39;][&#39;firewallOptions&#39;][&#39;lockdown&#39;]

            self._bind_open_ports_only = (network_prop[&#39;firewallConfiguration&#39;][
                &#39;firewallOptions&#39;][&#39;bindOpenPortsOnly&#39;])

            self._is_dmz = network_prop[&#39;firewallConfiguration&#39;][&#39;firewallOptions&#39;][&#39;isDMZ&#39;]

            self._keep_alive_seconds = (network_prop[&#39;firewallConfiguration&#39;][
                &#39;firewallOptions&#39;][&#39;keepAliveSeconds&#39;])

            if &#39;tppm&#39; in self._network_options:
                self._tppm_settings = (network_prop[&#39;firewallConfiguration&#39;][
                    &#39;firewallOptions&#39;][&#39;tppm&#39;])

    @property
    def configure_network_settings(self):
        &#34;&#34;&#34;Gets the value for configure firewall settings

        :return:
            boolean - configureFirewallSettings
        &#34;&#34;&#34;
        return self._config_network_settings

    @configure_network_settings.setter
    def configure_network_settings(self, val):
        &#34;&#34;&#34;Sets the value for configureFirewallSettings with the parameter provided


        &#34;&#34;&#34;
        self._config_network_settings = val
        self._advanced_network_config()

    @property
    def trivial_config(self):
        &#34;&#34;&#34;Gets the value for isTrivialConfig

        :return:
            boolean - isTrivialConfig
        &#34;&#34;&#34;
        return self._is_trivial_config

    @trivial_config.setter
    def trivial_config(self, val):
        &#34;&#34;&#34;Sets the value for isTrivialConfig with the parameter provided


        &#34;&#34;&#34;
        self._is_trivial_config = val
        self.enable_network_settings = True

    @property
    def roaming_client(self):
        &#34;&#34;&#34;Gets the value for isRoamingClient

        :return:
            boolen - isRoamingClient
        &#34;&#34;&#34;
        return self._is_roaming_client

    @roaming_client.setter
    def roaming_client(self, val):
        &#34;&#34;&#34;Sets the value for isRoamingClient with the parameter provided

        &#34;&#34;&#34;
        self._is_roaming_client = val
        self.configure_network_settings = True

    @property
    def tunnel_connection_port(self):
        &#34;&#34;&#34;Gets the value for tunnel port on the client/client group

        :return:
            int - tunnelConnectionPort
        &#34;&#34;&#34;
        return self._tunnel_connection_port

    @tunnel_connection_port.setter
    def tunnel_connection_port(self, val):
        &#34;&#34;&#34;Sets the value for tunnelConnectionPort with the parameter provided

        &#34;&#34;&#34;
        self._tunnel_connection_port = val
        self.configure_network_settings = True

    @property
    def force_ssl(self):
        &#34;&#34;&#34;Gets the value for forceSSL

        :return:
            boolean - forceSSL
        &#34;&#34;&#34;
        return self._force_ssl

    @force_ssl.setter
    def force_ssl(self, val):
        &#34;&#34;&#34;Sets the value for forceSSL with the parameter provided


        &#34;&#34;&#34;
        self._force_ssl = val
        self.configure_network_settings = True

    @property
    def tunnel_init_seconds(self):
        &#34;&#34;&#34;Gets the tunnel init seconds

        :return:
            int - tunnelInitSeconds
        &#34;&#34;&#34;
        return self._tunnel_init_seconds

    @tunnel_init_seconds.setter
    def tunnel_init_seconds(self, val):
        &#34;&#34;&#34;Sets the tunnelInitSeconds with the parameter provided


        &#34;&#34;&#34;
        self._tunnel_init_seconds = val
        self.configure_network_settings = True

    @property
    def lockdown(self):
        &#34;&#34;&#34;Gets the value for lockdown

        :return:
            boolean - lockdown
        &#34;&#34;&#34;
        return self._lockdown

    @lockdown.setter
    def lockdown(self, val):
        &#34;&#34;&#34;Sets the lockdown with the parameter provided


        &#34;&#34;&#34;
        self._lockdown = val
        self.configure_network_settings = True

    @property
    def bind_open_ports(self):
        &#34;&#34;&#34;Gets the value for bindOpenports only

        :return:
            boolean - bindOpenPortsOnly
        &#34;&#34;&#34;
        return self._bind_open_ports_only

    @bind_open_ports.setter
    def bind_open_ports(self, val):
        &#34;&#34;&#34;Sets bindopenportsonly with the parameter provided


        &#34;&#34;&#34;
        self._bind_open_ports_only = val
        self.configure_network_settings = True

    @property
    def proxy(self):
        &#34;&#34;&#34;Gets the value for isDMZ

        :return:
            boolean - isDMZ
        &#34;&#34;&#34;
        return self._is_dmz

    @proxy.setter
    def proxy(self, val):
        &#34;&#34;&#34;Sets the value for isDMZ with the parameter provided


        &#34;&#34;&#34;
        self._is_dmz = val
        self.configure_network_settings = True

    @property
    def keep_alive_seconds(self):
        &#34;&#34;&#34;Gets the value set for keep alive

        :return:
            int - keepAliveSeconds
        &#34;&#34;&#34;
        return self._keep_alive_seconds

    @keep_alive_seconds.setter
    def keep_alive_seconds(self, val):
        &#34;&#34;&#34;Sets the value for keep alive seconds with the parameter provided

        &#34;&#34;&#34;
        self._keep_alive_seconds = val
        self.configure_network_settings = True

    @property
    def incoming_connections(self):
        &#34;&#34;&#34;Gets all the incoming connections on a client

        :return:
            list - incoming connections
        &#34;&#34;&#34;

        for incoming_connection in self._restriction_to:
            if incoming_connection[&#39;state&#39;] in self._incoming_connection_type.keys():
                incoming_connection[&#39;state&#39;] = (self._incoming_connection_type[
                    incoming_connection[&#39;state&#39;]])

        return self._restriction_to

    def set_incoming_connections(self, incoming_connections):
        &#34;&#34;&#34;Sets the incoming connections on a client/client group with the list of values provided

         Args:
                incoming_connections(list)  -- list of incoming connections should be
                a list of dict containing incoming connection type, entity name and entity type.
                [{&#39;state&#39;:val,&#39;entity&#39;:val,&#39;isClient&#39;:val}]

            Example:
            [
                {
                &#39;state&#39;: &#39;RESTRICTED&#39;,
                &#39;entity&#39;: &#39;centOS&#39;,
                &#39;isClient&#39; : True
                },

                {
                &#39;state&#39;: &#39;BLOCKED&#39;,
                &#39;entity&#39;:  &#39;Edge Clients&#39;,
                &#39;isClient&#39; : False
                }
            ]

        Raises:
                SDKException:
                    if the required key is missing in the input value passed

        &#34;&#34;&#34;
        try:

            for incoming_connection in incoming_connections:

                if incoming_connection[&#39;isClient&#39;]:
                    restriction_to_dict = {
                        &#34;state&#34;: incoming_connection[&#39;state&#39;],
                        &#34;entity&#34;: {
                            &#34;clientName&#34;: incoming_connection[&#39;entity&#39;]
                        }
                    }

                else:
                    restriction_to_dict = {
                        &#34;state&#34;: incoming_connection[&#39;state&#39;],
                        &#34;entity&#34;: {
                            &#34;clientGroupName&#34;: incoming_connection[&#39;entity&#39;]
                        }
                    }

                self._restriction_to.append(restriction_to_dict)
            self.configure_network_settings = True

        except KeyError as err:
            raise SDKException(&#39;Client&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))

    @property
    def additional_ports(self):
        &#34;&#34;&#34;Gets the additional ports

        :return:
            list - ports
        &#34;&#34;&#34;
        return self._port_range

    def set_additional_ports(self, ports, tunnel_port=8403):
        &#34;&#34;&#34;Sets additional incoming ports and tunnel port with the values provided as parameter

            Args:
                tunnel_port (int) -- value to be set for tunnel port
                ports(list)  -- list of ports should be a list of dict containing
                start port and end port
                [{&#39;startPort&#39;:val,&#39;endPort&#39;:val}]

            Example:
            [
                {
                &#39;startPort&#39;: 1024,
                &#39;endPort&#39;: 1030
                },
                {
                &#39;startPort&#39;: 2000,
                &#39;endPort&#39;:4000
                }
            ]

            Raises:
                SDKException:
                    if the required key is missing in the input value passed
        &#34;&#34;&#34;
        try:
            self._tunnel_connection_port = tunnel_port
            for port in ports:
                additional_port_dict = {
                    &#34;startPort&#34;: port[&#39;startPort&#39;],
                    &#34;endPort&#34;: port[&#39;endPort&#39;]
                }

                self._port_range.append(additional_port_dict)

            self.configure_network_settings = True

        except KeyError as err:
            raise SDKException(&#39;Client&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))

    @property
    def outgoing_routes(self):
        &#34;&#34;&#34;Gets the list of all outgoing routes

            :return:
                list - outgoing routes

        &#34;&#34;&#34;

        for outgoing_route in self._network_outgoing_routes:
            if (outgoing_route[&#39;fireWallOutGoingRouteOptions&#39;][&#39;connectionProtocol&#39;]
                    in self._firewall_outgoing_connection_protocol.keys()):
                (outgoing_route[&#39;fireWallOutGoingRouteOptions&#39;][
                    &#39;connectionProtocol&#39;]) = (self._firewall_outgoing_connection_protocol[
                    outgoing_route[&#39;fireWallOutGoingRouteOptions&#39;][&#39;connectionProtocol&#39;]])
            if (outgoing_route[&#39;fireWallOutGoingRouteOptions&#39;][&#39;routeType&#39;]
                    in self._firewall_outgoing_route_type.keys()):
                outgoing_route[&#39;fireWallOutGoingRouteOptions&#39;][&#39;routeType&#39;] = (self._firewall_outgoing_route_type[
                    outgoing_route[&#39;fireWallOutGoingRouteOptions&#39;][&#39;routeType&#39;]])

        return self._network_outgoing_routes

    def set_outgoing_routes(self, outgoing_routes):
        &#34;&#34;&#34;Sets outgoing routes on the client with the list of values provided as parameter

            Args:
                outgoing_routes(list)  -- list of outgoing routes should be a list of dict
                containing route type, entity name, entity type, streams, gateway host,
                gateway port, tunnel connection protocol and remote proxy based on route type.

                For routeType: DIRECT
                [{&#39;routeType&#39;:&#39;DIRECT&#39;,
                &#39;remoteEntity&#39;:val ,
                &#39;streams&#39;:val,
                &#39;isClient&#39;:val,
                &#39;forceAllDataTraffic&#39;: True,
                &#39;connectionProtocol&#39; : 0}]

                For routeType: VIA_GATEWAY
                [{&#39;routeType&#39;:&#39;VIA_GATEWAY&#39;,
                &#39;remoteEntity&#39;:val,
                &#39;streams&#39;:val,
                &#39;gatewayPort&#39;:val,
                &#39;gatewayHost&#39;: val,
                &#39;isClient&#39;:val,
                &#39;forceAllDataTraffic&#39;: False,
                &#39;connectionProtocol&#39; : 3}]

                For routeType: VIA_PROXY
                [{&#39;routeType&#39;:&#39;VIA_PROXY&#39;,
                &#39;remoteEntity&#39;:val,
                &#39;remoteProxy&#39;:val,
                &#39;isClient&#39;:val}]


                Valid values for connectionProtocol:
                0: &#39;HTTP&#39;,
                1: &#39;HTTPS&#39;,
                2: &#39;HTTPS_AuthOnly&#39;,
                3: &#39;RAW_PROTOCOL&#39;

            Example:
            [
                {
                &#39;routeType&#39;: &#39;DIRECT&#39;,
                &#39;remoteEntity&#39;:&#39;Testcs&#39; ,
                &#39;streams&#39;: 1,
                &#39;isClient&#39;: True,
                &#39;forceAllDataTraffic&#39; : True
                &#39;connectionProtocol&#39; : 0
                },
                {
                &#39;routeType&#39;: &#39;VIA_GATEWAY&#39;,
                &#39;remoteEntity&#39;: &#39;centOS&#39;,
                &#39;streams&#39;: 2,
                &#39;gatewayPort&#39;: 443,
                &#39;gatewayHost&#39;: &#39;*******&#39;,
                &#39;isClient&#39;: True,
                &#39;forceAllDataTraffic&#39; :False
                &#39;connectionProtocol&#39; : 1
                },
                {
                &#39;routeType&#39;: &#39;VIA_PROXY&#39;,
                &#39;remoteEntity&#39;: &#39;Laptop Clients&#39;,
                &#39;remoteProxy&#39;: &#39;TemplateRHEL65_4&#39;,
                &#39;isClient&#39;: False
                }
            ]

            Raises:
                SDKException:
                    if routeType is invalid in the input value passed

                    if the required key is missing in the input value passed

        &#34;&#34;&#34;

        try:

            for outgoing_route in outgoing_routes:
                if outgoing_route[&#39;isClient&#39;]:
                    remote_entity_dict = {
                        &#34;clientName&#34;: outgoing_route[&#39;remoteEntity&#39;]

                    }

                else:
                    remote_entity_dict = {

                        &#34;clientGroupName&#34;: outgoing_route[&#39;remoteEntity&#39;]
                    }

                if outgoing_route[&#39;routeType&#39;] == self._firewall_outgoing_route_type[0]:
                    gatewayport = 0
                    gatewayhostname = &#34;&#34;
                    remote_proxy = {}
                    nstreams = outgoing_route[&#39;streams&#39;]
                    force_all_data_traffic = outgoing_route[&#39;forceAllDataTraffic&#39;]
                    connection_protocol = outgoing_route.get(&#39;connectionProtocol&#39;, 2)

                elif outgoing_route[&#39;routeType&#39;] == self._firewall_outgoing_route_type[1]:
                    gatewayport = outgoing_route[&#39;gatewayPort&#39;]
                    gatewayhostname = outgoing_route[&#39;gatewayHost&#39;]
                    remote_proxy = {}
                    nstreams = outgoing_route[&#39;streams&#39;]
                    force_all_data_traffic = outgoing_route[&#39;forceAllDataTraffic&#39;]
                    connection_protocol = outgoing_route.get(&#39;connectionProtocol&#39;, 2)

                elif outgoing_route[&#39;routeType&#39;] == self._firewall_outgoing_route_type[2]:
                    gatewayport = 0
                    gatewayhostname = &#34;&#34;
                    nstreams = 1
                    force_all_data_traffic = False
                    connection_protocol = 2
                    remote_proxy = {
                        &#34;clientName&#34;: outgoing_route[&#39;remoteProxy&#39;],

                        &#34;clientGroupName&#34;: &#34;&#34;,
                        &#34;_type_&#34;: 3
                    }

                else:
                    raise SDKException(&#39;Client&#39;, &#39;101&#39;)

                outgoing_routes_dict = {
                    &#34;fireWallOutGoingRouteOptions&#34;: {
                        &#34;numberOfStreams&#34;: nstreams,
                        &#34;connectionProtocol&#34;: connection_protocol,
                        &#34;gatewayTunnelPort&#34;: gatewayport,
                        &#34;forceAllBackupRestoreDataTraffic&#34;: force_all_data_traffic,
                        &#34;gatewayHostname&#34;: gatewayhostname,
                        &#34;routeType&#34;: outgoing_route[&#39;routeType&#39;],
                        &#34;remoteProxy&#34;: remote_proxy
                    },
                    &#34;remoteEntity&#34;: remote_entity_dict
                }

                self._network_outgoing_routes.append(outgoing_routes_dict)
            self.configure_network_settings = True

        except KeyError as err:
            raise SDKException(&#39;Client&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))

    @property
    def tppm_settings(self):
        &#34;&#34;&#34;Gets the list of tppm settings on a client

        :return:
            list - tppm settings
        &#34;&#34;&#34;

        for tppm_setting in self._tppm_settings:
            if tppm_setting[&#39;tppmType&#39;] in self._tppm_type.keys():
                tppm_setting[&#39;tppmType&#39;] = self._tppm_type[tppm_setting[&#39;tppmType&#39;]]

        return self._tppm_settings

    def set_tppm_settings(self, tppm_settings):
        &#34;&#34;&#34;Sets tppm on the client with the list of values provided as parameter


            Note:  This is supported only on client level

            Args:
                tppm_settings(list)  -- list of tppm settings should be a list of dict containing
                tppm type, port number and proxy entity
                [{&#39;tppmType&#39;:val, &#39;portNumber&#39;:val, &#39;proxyEntity&#39;:val}]

                Valid values for tppmType:
                1. WEB_SERVER_FOR_IIS_SERVER
                2. COMMSERVE
                3. REPORTS
                4. CUSTOM_REPORT_ENGINE

            Example:
            [
                {
                &#39;tppmType&#39;: &#39;WEB_SERVER_FOR_IIS_SERVER&#39;,
                &#39;portNumber&#39;:9999,
                &#39;proxyEntity&#39; : &#39;shezavm3&#39;
                },

                {
                &#39;tppmType&#39;: &#39;REPORTS&#39;,
                &#39;portNumber&#39;:8888,
                &#39;proxyEntity&#39; : &#39;shezavm11&#39;
                }
            ]

            Raises:
                SDKException:
                    if tppmType is invalid in the input value passed

                    if the required key is missing in the input value passed

        &#34;&#34;&#34;

        try:

            if self.flag == &#34;CLIENT&#34;:
                for tppm_setting in tppm_settings:

                    if tppm_setting[&#39;tppmType&#39;] in self._tppm_type.values():
                        tppm_dict = {
                            &#34;enable&#34;: True,
                            &#34;tppmType&#34;: tppm_setting[&#39;tppmType&#39;],
                            &#34;proxyInformation&#34;: {
                                &#34;portNumber&#34;: tppm_setting[&#39;portNumber&#39;],
                                &#34;proxyEntity&#34;: {
                                    &#34;clientName&#34;: tppm_setting[&#39;proxyEntity&#39;],
                                    &#34;_type_&#34;: 3
                                }
                            }
                        }
                        self._tppm_settings.append(tppm_dict)

                    else:
                        raise SDKException(&#39;Client&#39;, &#39;101&#39;)

            self.configure_network_settings = True

        except KeyError as err:
            raise SDKException(&#39;Client&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))

    def _advanced_network_config(self):

        &#34;&#34;&#34;Sets network properties on the client/client group with all the network properties


            Raises:
                SDKException:
                    if  request was not successful

                    if  invalid input was provided in the request

                    if empty response was received

        &#34;&#34;&#34;

        if self.flag == &#34;CLIENT&#34;:
            if not self._config_network_settings:
                update_networkconfig_dict = {
                    &#34;firewallConfiguration&#34;:
                        {
                            &#34;configureFirewallSettings&#34;: self._config_network_settings
                        }
                }

            else:
                update_networkconfig_dict = {
                    &#34;firewallConfiguration&#34;:
                        {
                            &#34;configureFirewallSettings&#34;: self._config_network_settings,
                            &#34;isTrivialConfig&#34;: False,
                            &#34;portRange&#34;: self._port_range,
                            &#34;proxyEntities&#34;: self._proxy_entities,
                            &#34;firewallOutGoingRoutes&#34;: self._network_outgoing_routes,
                            &#34;restrictionTo&#34;: self._restriction_to,
                            &#34;firewallOptions&#34;: {
                                &#34;isRoamingClient&#34;: self._is_roaming_client,
                                &#34;extendedProperties&#34;: &#34;&lt;App_FirewallExtendedProperties &#34;
                                                      &#34;configureAutomatically=\&#34;0\&#34; &#34;
                                                      &#34;defaultOutgoingProtocol=\&#34;0\&#34;/&gt;&#34;,
                                &#34;tunnelconnectionPort&#34;: self._tunnel_connection_port,
                                &#34;foreceSSL&#34;: self._force_ssl,
                                &#34;tunnelInitSeconds&#34;: self._tunnel_init_seconds,
                                &#34;lockdown&#34;: self._lockdown,
                                &#34;bindOpenPortsOnly&#34;: self._bind_open_ports_only,
                                &#34;isDMZ&#34;: self._is_dmz,
                                &#34;keepAliveSeconds&#34;: self._keep_alive_seconds,
                                &#34;tppm&#34;: self._tppm_settings
                            }
                        }
                }

            request_json = self._client_object._update_client_props_json(update_networkconfig_dict)
            flag, response = (self._commcell_object._cvpysdk_object.make_request(
                &#39;POST&#39;, self._client_object._CLIENT, request_json))

            if flag:
                if response.json() and &#39;response&#39; in response.json():
                    error_code = response.json()[&#39;response&#39;][0][&#39;errorCode&#39;]

                    if error_code == 0:
                        self._client_object._get_client_properties()

                    elif &#39;errorMessage&#39; in response.json():
                        error_message = response.json()[&#39;errorMessage&#39;]
                        self._get_network_properties()
                        raise SDKException(&#39;Client&#39;, &#39;102&#39;, error_message)

                else:
                    self._get_network_properties()
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)

            else:
                response_string = self._commcell_object._update_response_(response.text)
                self._get_network_properties()
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        elif self.flag == &#34;CLIENTGROUP&#34;:

            if not self._config_network_settings:
                request_json = {
                    &#34;clientGroupOperationType&#34;: 2,
                    &#34;clientGroupDetail&#34;: {
                        &#34;clientGroup&#34;: {
                            &#34;clientGroupName&#34;: self._clientgroup_object._clientgroup_name
                        },
                        &#34;firewallConfiguration&#34;: {
                            &#34;configureFirewallSettings&#34;: self._config_network_settings,

                        }
                    }
                }

            else:
                request_json = {
                    &#34;clientGroupOperationType&#34;: 2,
                    &#34;clientGroupDetail&#34;: {
                        &#34;clientGroup&#34;: {
                            &#34;clientGroupName&#34;: self._clientgroup_object._clientgroup_name
                            },
                        &#34;firewallConfiguration&#34;: {
                            &#34;configureFirewallSettings&#34;: self._config_network_settings,
                            &#34;isTrivialConfig&#34;: False,
                            &#34;portRange&#34;: self._port_range,
                            &#34;proxyEntities&#34;: self._proxy_entities,
                            &#34;firewallOutGoingRoutes&#34;: self._network_outgoing_routes,
                            &#34;restrictionTo&#34;: self._restriction_to,
                            &#34;firewallOptions&#34;: {
                                &#34;isRoamingClient&#34;: self._is_roaming_client,
                                &#34;extendedProperties&#34;: &#34;&lt;App_FirewallExtendedProperties &#34;
                                                      &#34;configureAutomatically=\&#34;0\&#34; &#34;
                                                      &#34;defaultOutgoingProtocol=\&#34;0\&#34;/&gt;&#34;,
                                &#34;tunnelconnectionPort&#34;: self._tunnel_connection_port,
                                &#34;foreceSSL&#34;: self._force_ssl,
                                &#34;tunnelInitSeconds&#34;: self._tunnel_init_seconds,
                                &#34;lockdown&#34;: self._lockdown,
                                &#34;bindOpenPortsOnly&#34;: self._bind_open_ports_only,
                                &#34;isDMZ&#34;: self._is_dmz,
                                &#34;keepAliveSeconds&#34;: self._keep_alive_seconds,
                            }
                        }

                    }}

            flag, response = self._commcell_object._cvpysdk_object.make_request(
                &#39;POST&#39;, self._clientgroup_object._CLIENTGROUP, request_json)

            if flag:
                if response.json():
                    error_code = str(response.json()[&#39;errorCode&#39;])

                    if &#39;errorMessage&#39; in response.json():
                        error_message = response.json()[&#39;errorMessage&#39;]
                    else:
                        error_message = &#34;&#34;

                    if error_code == &#39;0&#39;:
                        self._clientgroup_object._get_clientgroup_properties()

                    else:
                        self._get_network_properties()
                        raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;,
                                           &#39;Client group properties were not updated&#39;)

                else:
                    self._get_network_properties()
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                response_string = self._commcell_object._update_response_(response.text)
                self._get_network_properties()
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.network.Network"><code class="flex name class">
<span>class <span class="ident">Network</span></span>
<span>(</span><span>class_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for performing network related operations on a client or client group</p>
<p>Initialize the Network class object</p>
<h2 id="args">Args</h2>
<p>class_object (object)
&ndash;
instance of the client/client group class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/network.py#L105-L922" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Network(object):
    &#34;&#34;&#34;Class for performing network related operations on a client or client group&#34;&#34;&#34;

    def __init__(self, class_object):
        &#34;&#34;&#34;Initialize the Network class object

            Args:
                class_object (object)  --  instance of the client/client group class



        &#34;&#34;&#34;
        from .client import Client
        from .clientgroup import ClientGroup

        self._class_object = class_object
        self._commcell_object = self._class_object._commcell_object
        self.flag = &#34;&#34;
        if isinstance(class_object, Client):
            self._client_object = class_object
            self.flag = &#34;CLIENT&#34;

        elif isinstance(class_object, ClientGroup):
            self._clientgroup_object = class_object
            self.flag = &#34;CLIENTGROUP&#34;

        self._config_network_settings = None
        self._is_trivial_config = False
        self._proxy_entities = []
        self._port_range = []
        self._network_outgoing_routes = []
        self._restriction_to = []
        self._tppm_settings = []
        self._is_roaming_client = False
        self._tunnel_connection_port = 8403
        self._force_ssl = False
        self._tunnel_init_seconds = 30
        self._lockdown = False
        self._bind_open_ports_only = False
        self._is_dmz = False
        self._keep_alive_seconds = 300
        self.enable_network_settings = None

        self._incoming_connection_type = {
            0: &#39;RESTRICTED&#39;,
            1: &#39;BLOCKED&#39;
        }

        self._firewall_outgoing_route_type = {
            0: &#39;DIRECT&#39;,
            1: &#39;VIA_GATEWAY&#39;,
            2: &#39;VIA_PROXY&#39;
        }

        self._firewall_outgoing_connection_protocol = {
            0: &#39;HTTP&#39;,
            1: &#39;HTTPS&#39;,
            2: &#39;HTTPS_AuthOnly&#39;,
            3: &#39;RAW_PROTOCOL&#39;
        }

        self._tppm_type = {
            2: &#39;WEB_SERVER_FOR_IIS_SERVER&#39;,
            3: &#39;COMMSERVE&#39;,
            5: &#39;REPORTS&#39;,
            6: &#39;CUSTOM_REPORT_ENGINE&#39;
        }

        self._get_network_properties()

    def _get_network_properties(self):
        &#34;&#34;&#34;Get all the existing network properties on a client/client group and retain each of them


        &#34;&#34;&#34;
        if self.flag == &#34;CLIENT&#34;:
            network_prop = self._client_object._properties[&#39;clientProps&#39;]

        elif self.flag == &#34;CLIENTGROUP&#34;:
            network_prop = self._clientgroup_object._properties

        if &#39;firewallConfiguration&#39; in network_prop:
            self._config_network_settings = (network_prop[&#39;firewallConfiguration&#39;][
                &#39;configureFirewallSettings&#39;])

            self._is_trivial_config = network_prop[&#39;firewallConfiguration&#39;][&#39;isTrivialConfig&#39;]

            if &#39;portRange&#39; in network_prop[&#39;firewallConfiguration&#39;]:
                self._port_range = network_prop[&#39;firewallConfiguration&#39;][&#39;portRange&#39;]

            if &#39;proxyEntities&#39; in network_prop[&#39;firewallConfiguration&#39;]:
                self._proxy_entities = network_prop[&#39;firewallConfiguration&#39;][&#39;proxyEntities&#39;]

            if &#39;firewallOutGoingRoutes&#39; in network_prop[&#39;firewallConfiguration&#39;]:
                self._network_outgoing_routes = (network_prop[&#39;firewallConfiguration&#39;][
                    &#39;firewallOutGoingRoutes&#39;])

            if &#39;restrictionTo&#39; in network_prop[&#39;firewallConfiguration&#39;]:
                self._restriction_to = network_prop[&#39;firewallConfiguration&#39;][&#39;restrictionTo&#39;]

            if &#39;firewallOptions&#39; in network_prop[&#39;firewallConfiguration&#39;]:
                self._network_options = network_prop[&#39;firewallConfiguration&#39;][&#39;firewallOptions&#39;]

            if &#39;isRoamingClient&#39; in network_prop[&#39;firewallConfiguration&#39;]:
                self._is_roaming_client = (network_prop[&#39;firewallConfiguration&#39;][
                    &#39;firewallOptions&#39;][&#39;isRoamingClient&#39;])

            self._tunnel_connection_port = network_prop[&#39;firewallConfiguration&#39;][&#39;firewallOptions&#39;][
                &#39;tunnelconnectionPort&#39;]

            self._force_ssl = network_prop[&#39;firewallConfiguration&#39;][&#39;firewallOptions&#39;][&#39;foreceSSL&#39;]

            self._tunnel_init_seconds = (network_prop[&#39;firewallConfiguration&#39;][
                &#39;firewallOptions&#39;][&#39;tunnelInitSeconds&#39;])

            self._lockdown = network_prop[&#39;firewallConfiguration&#39;][&#39;firewallOptions&#39;][&#39;lockdown&#39;]

            self._bind_open_ports_only = (network_prop[&#39;firewallConfiguration&#39;][
                &#39;firewallOptions&#39;][&#39;bindOpenPortsOnly&#39;])

            self._is_dmz = network_prop[&#39;firewallConfiguration&#39;][&#39;firewallOptions&#39;][&#39;isDMZ&#39;]

            self._keep_alive_seconds = (network_prop[&#39;firewallConfiguration&#39;][
                &#39;firewallOptions&#39;][&#39;keepAliveSeconds&#39;])

            if &#39;tppm&#39; in self._network_options:
                self._tppm_settings = (network_prop[&#39;firewallConfiguration&#39;][
                    &#39;firewallOptions&#39;][&#39;tppm&#39;])

    @property
    def configure_network_settings(self):
        &#34;&#34;&#34;Gets the value for configure firewall settings

        :return:
            boolean - configureFirewallSettings
        &#34;&#34;&#34;
        return self._config_network_settings

    @configure_network_settings.setter
    def configure_network_settings(self, val):
        &#34;&#34;&#34;Sets the value for configureFirewallSettings with the parameter provided


        &#34;&#34;&#34;
        self._config_network_settings = val
        self._advanced_network_config()

    @property
    def trivial_config(self):
        &#34;&#34;&#34;Gets the value for isTrivialConfig

        :return:
            boolean - isTrivialConfig
        &#34;&#34;&#34;
        return self._is_trivial_config

    @trivial_config.setter
    def trivial_config(self, val):
        &#34;&#34;&#34;Sets the value for isTrivialConfig with the parameter provided


        &#34;&#34;&#34;
        self._is_trivial_config = val
        self.enable_network_settings = True

    @property
    def roaming_client(self):
        &#34;&#34;&#34;Gets the value for isRoamingClient

        :return:
            boolen - isRoamingClient
        &#34;&#34;&#34;
        return self._is_roaming_client

    @roaming_client.setter
    def roaming_client(self, val):
        &#34;&#34;&#34;Sets the value for isRoamingClient with the parameter provided

        &#34;&#34;&#34;
        self._is_roaming_client = val
        self.configure_network_settings = True

    @property
    def tunnel_connection_port(self):
        &#34;&#34;&#34;Gets the value for tunnel port on the client/client group

        :return:
            int - tunnelConnectionPort
        &#34;&#34;&#34;
        return self._tunnel_connection_port

    @tunnel_connection_port.setter
    def tunnel_connection_port(self, val):
        &#34;&#34;&#34;Sets the value for tunnelConnectionPort with the parameter provided

        &#34;&#34;&#34;
        self._tunnel_connection_port = val
        self.configure_network_settings = True

    @property
    def force_ssl(self):
        &#34;&#34;&#34;Gets the value for forceSSL

        :return:
            boolean - forceSSL
        &#34;&#34;&#34;
        return self._force_ssl

    @force_ssl.setter
    def force_ssl(self, val):
        &#34;&#34;&#34;Sets the value for forceSSL with the parameter provided


        &#34;&#34;&#34;
        self._force_ssl = val
        self.configure_network_settings = True

    @property
    def tunnel_init_seconds(self):
        &#34;&#34;&#34;Gets the tunnel init seconds

        :return:
            int - tunnelInitSeconds
        &#34;&#34;&#34;
        return self._tunnel_init_seconds

    @tunnel_init_seconds.setter
    def tunnel_init_seconds(self, val):
        &#34;&#34;&#34;Sets the tunnelInitSeconds with the parameter provided


        &#34;&#34;&#34;
        self._tunnel_init_seconds = val
        self.configure_network_settings = True

    @property
    def lockdown(self):
        &#34;&#34;&#34;Gets the value for lockdown

        :return:
            boolean - lockdown
        &#34;&#34;&#34;
        return self._lockdown

    @lockdown.setter
    def lockdown(self, val):
        &#34;&#34;&#34;Sets the lockdown with the parameter provided


        &#34;&#34;&#34;
        self._lockdown = val
        self.configure_network_settings = True

    @property
    def bind_open_ports(self):
        &#34;&#34;&#34;Gets the value for bindOpenports only

        :return:
            boolean - bindOpenPortsOnly
        &#34;&#34;&#34;
        return self._bind_open_ports_only

    @bind_open_ports.setter
    def bind_open_ports(self, val):
        &#34;&#34;&#34;Sets bindopenportsonly with the parameter provided


        &#34;&#34;&#34;
        self._bind_open_ports_only = val
        self.configure_network_settings = True

    @property
    def proxy(self):
        &#34;&#34;&#34;Gets the value for isDMZ

        :return:
            boolean - isDMZ
        &#34;&#34;&#34;
        return self._is_dmz

    @proxy.setter
    def proxy(self, val):
        &#34;&#34;&#34;Sets the value for isDMZ with the parameter provided


        &#34;&#34;&#34;
        self._is_dmz = val
        self.configure_network_settings = True

    @property
    def keep_alive_seconds(self):
        &#34;&#34;&#34;Gets the value set for keep alive

        :return:
            int - keepAliveSeconds
        &#34;&#34;&#34;
        return self._keep_alive_seconds

    @keep_alive_seconds.setter
    def keep_alive_seconds(self, val):
        &#34;&#34;&#34;Sets the value for keep alive seconds with the parameter provided

        &#34;&#34;&#34;
        self._keep_alive_seconds = val
        self.configure_network_settings = True

    @property
    def incoming_connections(self):
        &#34;&#34;&#34;Gets all the incoming connections on a client

        :return:
            list - incoming connections
        &#34;&#34;&#34;

        for incoming_connection in self._restriction_to:
            if incoming_connection[&#39;state&#39;] in self._incoming_connection_type.keys():
                incoming_connection[&#39;state&#39;] = (self._incoming_connection_type[
                    incoming_connection[&#39;state&#39;]])

        return self._restriction_to

    def set_incoming_connections(self, incoming_connections):
        &#34;&#34;&#34;Sets the incoming connections on a client/client group with the list of values provided

         Args:
                incoming_connections(list)  -- list of incoming connections should be
                a list of dict containing incoming connection type, entity name and entity type.
                [{&#39;state&#39;:val,&#39;entity&#39;:val,&#39;isClient&#39;:val}]

            Example:
            [
                {
                &#39;state&#39;: &#39;RESTRICTED&#39;,
                &#39;entity&#39;: &#39;centOS&#39;,
                &#39;isClient&#39; : True
                },

                {
                &#39;state&#39;: &#39;BLOCKED&#39;,
                &#39;entity&#39;:  &#39;Edge Clients&#39;,
                &#39;isClient&#39; : False
                }
            ]

        Raises:
                SDKException:
                    if the required key is missing in the input value passed

        &#34;&#34;&#34;
        try:

            for incoming_connection in incoming_connections:

                if incoming_connection[&#39;isClient&#39;]:
                    restriction_to_dict = {
                        &#34;state&#34;: incoming_connection[&#39;state&#39;],
                        &#34;entity&#34;: {
                            &#34;clientName&#34;: incoming_connection[&#39;entity&#39;]
                        }
                    }

                else:
                    restriction_to_dict = {
                        &#34;state&#34;: incoming_connection[&#39;state&#39;],
                        &#34;entity&#34;: {
                            &#34;clientGroupName&#34;: incoming_connection[&#39;entity&#39;]
                        }
                    }

                self._restriction_to.append(restriction_to_dict)
            self.configure_network_settings = True

        except KeyError as err:
            raise SDKException(&#39;Client&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))

    @property
    def additional_ports(self):
        &#34;&#34;&#34;Gets the additional ports

        :return:
            list - ports
        &#34;&#34;&#34;
        return self._port_range

    def set_additional_ports(self, ports, tunnel_port=8403):
        &#34;&#34;&#34;Sets additional incoming ports and tunnel port with the values provided as parameter

            Args:
                tunnel_port (int) -- value to be set for tunnel port
                ports(list)  -- list of ports should be a list of dict containing
                start port and end port
                [{&#39;startPort&#39;:val,&#39;endPort&#39;:val}]

            Example:
            [
                {
                &#39;startPort&#39;: 1024,
                &#39;endPort&#39;: 1030
                },
                {
                &#39;startPort&#39;: 2000,
                &#39;endPort&#39;:4000
                }
            ]

            Raises:
                SDKException:
                    if the required key is missing in the input value passed
        &#34;&#34;&#34;
        try:
            self._tunnel_connection_port = tunnel_port
            for port in ports:
                additional_port_dict = {
                    &#34;startPort&#34;: port[&#39;startPort&#39;],
                    &#34;endPort&#34;: port[&#39;endPort&#39;]
                }

                self._port_range.append(additional_port_dict)

            self.configure_network_settings = True

        except KeyError as err:
            raise SDKException(&#39;Client&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))

    @property
    def outgoing_routes(self):
        &#34;&#34;&#34;Gets the list of all outgoing routes

            :return:
                list - outgoing routes

        &#34;&#34;&#34;

        for outgoing_route in self._network_outgoing_routes:
            if (outgoing_route[&#39;fireWallOutGoingRouteOptions&#39;][&#39;connectionProtocol&#39;]
                    in self._firewall_outgoing_connection_protocol.keys()):
                (outgoing_route[&#39;fireWallOutGoingRouteOptions&#39;][
                    &#39;connectionProtocol&#39;]) = (self._firewall_outgoing_connection_protocol[
                    outgoing_route[&#39;fireWallOutGoingRouteOptions&#39;][&#39;connectionProtocol&#39;]])
            if (outgoing_route[&#39;fireWallOutGoingRouteOptions&#39;][&#39;routeType&#39;]
                    in self._firewall_outgoing_route_type.keys()):
                outgoing_route[&#39;fireWallOutGoingRouteOptions&#39;][&#39;routeType&#39;] = (self._firewall_outgoing_route_type[
                    outgoing_route[&#39;fireWallOutGoingRouteOptions&#39;][&#39;routeType&#39;]])

        return self._network_outgoing_routes

    def set_outgoing_routes(self, outgoing_routes):
        &#34;&#34;&#34;Sets outgoing routes on the client with the list of values provided as parameter

            Args:
                outgoing_routes(list)  -- list of outgoing routes should be a list of dict
                containing route type, entity name, entity type, streams, gateway host,
                gateway port, tunnel connection protocol and remote proxy based on route type.

                For routeType: DIRECT
                [{&#39;routeType&#39;:&#39;DIRECT&#39;,
                &#39;remoteEntity&#39;:val ,
                &#39;streams&#39;:val,
                &#39;isClient&#39;:val,
                &#39;forceAllDataTraffic&#39;: True,
                &#39;connectionProtocol&#39; : 0}]

                For routeType: VIA_GATEWAY
                [{&#39;routeType&#39;:&#39;VIA_GATEWAY&#39;,
                &#39;remoteEntity&#39;:val,
                &#39;streams&#39;:val,
                &#39;gatewayPort&#39;:val,
                &#39;gatewayHost&#39;: val,
                &#39;isClient&#39;:val,
                &#39;forceAllDataTraffic&#39;: False,
                &#39;connectionProtocol&#39; : 3}]

                For routeType: VIA_PROXY
                [{&#39;routeType&#39;:&#39;VIA_PROXY&#39;,
                &#39;remoteEntity&#39;:val,
                &#39;remoteProxy&#39;:val,
                &#39;isClient&#39;:val}]


                Valid values for connectionProtocol:
                0: &#39;HTTP&#39;,
                1: &#39;HTTPS&#39;,
                2: &#39;HTTPS_AuthOnly&#39;,
                3: &#39;RAW_PROTOCOL&#39;

            Example:
            [
                {
                &#39;routeType&#39;: &#39;DIRECT&#39;,
                &#39;remoteEntity&#39;:&#39;Testcs&#39; ,
                &#39;streams&#39;: 1,
                &#39;isClient&#39;: True,
                &#39;forceAllDataTraffic&#39; : True
                &#39;connectionProtocol&#39; : 0
                },
                {
                &#39;routeType&#39;: &#39;VIA_GATEWAY&#39;,
                &#39;remoteEntity&#39;: &#39;centOS&#39;,
                &#39;streams&#39;: 2,
                &#39;gatewayPort&#39;: 443,
                &#39;gatewayHost&#39;: &#39;*******&#39;,
                &#39;isClient&#39;: True,
                &#39;forceAllDataTraffic&#39; :False
                &#39;connectionProtocol&#39; : 1
                },
                {
                &#39;routeType&#39;: &#39;VIA_PROXY&#39;,
                &#39;remoteEntity&#39;: &#39;Laptop Clients&#39;,
                &#39;remoteProxy&#39;: &#39;TemplateRHEL65_4&#39;,
                &#39;isClient&#39;: False
                }
            ]

            Raises:
                SDKException:
                    if routeType is invalid in the input value passed

                    if the required key is missing in the input value passed

        &#34;&#34;&#34;

        try:

            for outgoing_route in outgoing_routes:
                if outgoing_route[&#39;isClient&#39;]:
                    remote_entity_dict = {
                        &#34;clientName&#34;: outgoing_route[&#39;remoteEntity&#39;]

                    }

                else:
                    remote_entity_dict = {

                        &#34;clientGroupName&#34;: outgoing_route[&#39;remoteEntity&#39;]
                    }

                if outgoing_route[&#39;routeType&#39;] == self._firewall_outgoing_route_type[0]:
                    gatewayport = 0
                    gatewayhostname = &#34;&#34;
                    remote_proxy = {}
                    nstreams = outgoing_route[&#39;streams&#39;]
                    force_all_data_traffic = outgoing_route[&#39;forceAllDataTraffic&#39;]
                    connection_protocol = outgoing_route.get(&#39;connectionProtocol&#39;, 2)

                elif outgoing_route[&#39;routeType&#39;] == self._firewall_outgoing_route_type[1]:
                    gatewayport = outgoing_route[&#39;gatewayPort&#39;]
                    gatewayhostname = outgoing_route[&#39;gatewayHost&#39;]
                    remote_proxy = {}
                    nstreams = outgoing_route[&#39;streams&#39;]
                    force_all_data_traffic = outgoing_route[&#39;forceAllDataTraffic&#39;]
                    connection_protocol = outgoing_route.get(&#39;connectionProtocol&#39;, 2)

                elif outgoing_route[&#39;routeType&#39;] == self._firewall_outgoing_route_type[2]:
                    gatewayport = 0
                    gatewayhostname = &#34;&#34;
                    nstreams = 1
                    force_all_data_traffic = False
                    connection_protocol = 2
                    remote_proxy = {
                        &#34;clientName&#34;: outgoing_route[&#39;remoteProxy&#39;],

                        &#34;clientGroupName&#34;: &#34;&#34;,
                        &#34;_type_&#34;: 3
                    }

                else:
                    raise SDKException(&#39;Client&#39;, &#39;101&#39;)

                outgoing_routes_dict = {
                    &#34;fireWallOutGoingRouteOptions&#34;: {
                        &#34;numberOfStreams&#34;: nstreams,
                        &#34;connectionProtocol&#34;: connection_protocol,
                        &#34;gatewayTunnelPort&#34;: gatewayport,
                        &#34;forceAllBackupRestoreDataTraffic&#34;: force_all_data_traffic,
                        &#34;gatewayHostname&#34;: gatewayhostname,
                        &#34;routeType&#34;: outgoing_route[&#39;routeType&#39;],
                        &#34;remoteProxy&#34;: remote_proxy
                    },
                    &#34;remoteEntity&#34;: remote_entity_dict
                }

                self._network_outgoing_routes.append(outgoing_routes_dict)
            self.configure_network_settings = True

        except KeyError as err:
            raise SDKException(&#39;Client&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))

    @property
    def tppm_settings(self):
        &#34;&#34;&#34;Gets the list of tppm settings on a client

        :return:
            list - tppm settings
        &#34;&#34;&#34;

        for tppm_setting in self._tppm_settings:
            if tppm_setting[&#39;tppmType&#39;] in self._tppm_type.keys():
                tppm_setting[&#39;tppmType&#39;] = self._tppm_type[tppm_setting[&#39;tppmType&#39;]]

        return self._tppm_settings

    def set_tppm_settings(self, tppm_settings):
        &#34;&#34;&#34;Sets tppm on the client with the list of values provided as parameter


            Note:  This is supported only on client level

            Args:
                tppm_settings(list)  -- list of tppm settings should be a list of dict containing
                tppm type, port number and proxy entity
                [{&#39;tppmType&#39;:val, &#39;portNumber&#39;:val, &#39;proxyEntity&#39;:val}]

                Valid values for tppmType:
                1. WEB_SERVER_FOR_IIS_SERVER
                2. COMMSERVE
                3. REPORTS
                4. CUSTOM_REPORT_ENGINE

            Example:
            [
                {
                &#39;tppmType&#39;: &#39;WEB_SERVER_FOR_IIS_SERVER&#39;,
                &#39;portNumber&#39;:9999,
                &#39;proxyEntity&#39; : &#39;shezavm3&#39;
                },

                {
                &#39;tppmType&#39;: &#39;REPORTS&#39;,
                &#39;portNumber&#39;:8888,
                &#39;proxyEntity&#39; : &#39;shezavm11&#39;
                }
            ]

            Raises:
                SDKException:
                    if tppmType is invalid in the input value passed

                    if the required key is missing in the input value passed

        &#34;&#34;&#34;

        try:

            if self.flag == &#34;CLIENT&#34;:
                for tppm_setting in tppm_settings:

                    if tppm_setting[&#39;tppmType&#39;] in self._tppm_type.values():
                        tppm_dict = {
                            &#34;enable&#34;: True,
                            &#34;tppmType&#34;: tppm_setting[&#39;tppmType&#39;],
                            &#34;proxyInformation&#34;: {
                                &#34;portNumber&#34;: tppm_setting[&#39;portNumber&#39;],
                                &#34;proxyEntity&#34;: {
                                    &#34;clientName&#34;: tppm_setting[&#39;proxyEntity&#39;],
                                    &#34;_type_&#34;: 3
                                }
                            }
                        }
                        self._tppm_settings.append(tppm_dict)

                    else:
                        raise SDKException(&#39;Client&#39;, &#39;101&#39;)

            self.configure_network_settings = True

        except KeyError as err:
            raise SDKException(&#39;Client&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))

    def _advanced_network_config(self):

        &#34;&#34;&#34;Sets network properties on the client/client group with all the network properties


            Raises:
                SDKException:
                    if  request was not successful

                    if  invalid input was provided in the request

                    if empty response was received

        &#34;&#34;&#34;

        if self.flag == &#34;CLIENT&#34;:
            if not self._config_network_settings:
                update_networkconfig_dict = {
                    &#34;firewallConfiguration&#34;:
                        {
                            &#34;configureFirewallSettings&#34;: self._config_network_settings
                        }
                }

            else:
                update_networkconfig_dict = {
                    &#34;firewallConfiguration&#34;:
                        {
                            &#34;configureFirewallSettings&#34;: self._config_network_settings,
                            &#34;isTrivialConfig&#34;: False,
                            &#34;portRange&#34;: self._port_range,
                            &#34;proxyEntities&#34;: self._proxy_entities,
                            &#34;firewallOutGoingRoutes&#34;: self._network_outgoing_routes,
                            &#34;restrictionTo&#34;: self._restriction_to,
                            &#34;firewallOptions&#34;: {
                                &#34;isRoamingClient&#34;: self._is_roaming_client,
                                &#34;extendedProperties&#34;: &#34;&lt;App_FirewallExtendedProperties &#34;
                                                      &#34;configureAutomatically=\&#34;0\&#34; &#34;
                                                      &#34;defaultOutgoingProtocol=\&#34;0\&#34;/&gt;&#34;,
                                &#34;tunnelconnectionPort&#34;: self._tunnel_connection_port,
                                &#34;foreceSSL&#34;: self._force_ssl,
                                &#34;tunnelInitSeconds&#34;: self._tunnel_init_seconds,
                                &#34;lockdown&#34;: self._lockdown,
                                &#34;bindOpenPortsOnly&#34;: self._bind_open_ports_only,
                                &#34;isDMZ&#34;: self._is_dmz,
                                &#34;keepAliveSeconds&#34;: self._keep_alive_seconds,
                                &#34;tppm&#34;: self._tppm_settings
                            }
                        }
                }

            request_json = self._client_object._update_client_props_json(update_networkconfig_dict)
            flag, response = (self._commcell_object._cvpysdk_object.make_request(
                &#39;POST&#39;, self._client_object._CLIENT, request_json))

            if flag:
                if response.json() and &#39;response&#39; in response.json():
                    error_code = response.json()[&#39;response&#39;][0][&#39;errorCode&#39;]

                    if error_code == 0:
                        self._client_object._get_client_properties()

                    elif &#39;errorMessage&#39; in response.json():
                        error_message = response.json()[&#39;errorMessage&#39;]
                        self._get_network_properties()
                        raise SDKException(&#39;Client&#39;, &#39;102&#39;, error_message)

                else:
                    self._get_network_properties()
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)

            else:
                response_string = self._commcell_object._update_response_(response.text)
                self._get_network_properties()
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        elif self.flag == &#34;CLIENTGROUP&#34;:

            if not self._config_network_settings:
                request_json = {
                    &#34;clientGroupOperationType&#34;: 2,
                    &#34;clientGroupDetail&#34;: {
                        &#34;clientGroup&#34;: {
                            &#34;clientGroupName&#34;: self._clientgroup_object._clientgroup_name
                        },
                        &#34;firewallConfiguration&#34;: {
                            &#34;configureFirewallSettings&#34;: self._config_network_settings,

                        }
                    }
                }

            else:
                request_json = {
                    &#34;clientGroupOperationType&#34;: 2,
                    &#34;clientGroupDetail&#34;: {
                        &#34;clientGroup&#34;: {
                            &#34;clientGroupName&#34;: self._clientgroup_object._clientgroup_name
                            },
                        &#34;firewallConfiguration&#34;: {
                            &#34;configureFirewallSettings&#34;: self._config_network_settings,
                            &#34;isTrivialConfig&#34;: False,
                            &#34;portRange&#34;: self._port_range,
                            &#34;proxyEntities&#34;: self._proxy_entities,
                            &#34;firewallOutGoingRoutes&#34;: self._network_outgoing_routes,
                            &#34;restrictionTo&#34;: self._restriction_to,
                            &#34;firewallOptions&#34;: {
                                &#34;isRoamingClient&#34;: self._is_roaming_client,
                                &#34;extendedProperties&#34;: &#34;&lt;App_FirewallExtendedProperties &#34;
                                                      &#34;configureAutomatically=\&#34;0\&#34; &#34;
                                                      &#34;defaultOutgoingProtocol=\&#34;0\&#34;/&gt;&#34;,
                                &#34;tunnelconnectionPort&#34;: self._tunnel_connection_port,
                                &#34;foreceSSL&#34;: self._force_ssl,
                                &#34;tunnelInitSeconds&#34;: self._tunnel_init_seconds,
                                &#34;lockdown&#34;: self._lockdown,
                                &#34;bindOpenPortsOnly&#34;: self._bind_open_ports_only,
                                &#34;isDMZ&#34;: self._is_dmz,
                                &#34;keepAliveSeconds&#34;: self._keep_alive_seconds,
                            }
                        }

                    }}

            flag, response = self._commcell_object._cvpysdk_object.make_request(
                &#39;POST&#39;, self._clientgroup_object._CLIENTGROUP, request_json)

            if flag:
                if response.json():
                    error_code = str(response.json()[&#39;errorCode&#39;])

                    if &#39;errorMessage&#39; in response.json():
                        error_message = response.json()[&#39;errorMessage&#39;]
                    else:
                        error_message = &#34;&#34;

                    if error_code == &#39;0&#39;:
                        self._clientgroup_object._get_clientgroup_properties()

                    else:
                        self._get_network_properties()
                        raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;,
                                           &#39;Client group properties were not updated&#39;)

                else:
                    self._get_network_properties()
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                response_string = self._commcell_object._update_response_(response.text)
                self._get_network_properties()
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.network.Network.additional_ports"><code class="name">var <span class="ident">additional_ports</span></code></dt>
<dd>
<div class="desc"><p>Gets the additional ports</p>
<p>:return:
list - ports</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/network.py#L480-L487" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def additional_ports(self):
    &#34;&#34;&#34;Gets the additional ports

    :return:
        list - ports
    &#34;&#34;&#34;
    return self._port_range</code></pre>
</details>
</dd>
<dt id="cvpysdk.network.Network.bind_open_ports"><code class="name">var <span class="ident">bind_open_ports</span></code></dt>
<dd>
<div class="desc"><p>Gets the value for bindOpenports only</p>
<p>:return:
boolean - bindOpenPortsOnly</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/network.py#L358-L365" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def bind_open_ports(self):
    &#34;&#34;&#34;Gets the value for bindOpenports only

    :return:
        boolean - bindOpenPortsOnly
    &#34;&#34;&#34;
    return self._bind_open_ports_only</code></pre>
</details>
</dd>
<dt id="cvpysdk.network.Network.configure_network_settings"><code class="name">var <span class="ident">configure_network_settings</span></code></dt>
<dd>
<div class="desc"><p>Gets the value for configure firewall settings</p>
<p>:return:
boolean - configureFirewallSettings</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/network.py#L234-L241" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def configure_network_settings(self):
    &#34;&#34;&#34;Gets the value for configure firewall settings

    :return:
        boolean - configureFirewallSettings
    &#34;&#34;&#34;
    return self._config_network_settings</code></pre>
</details>
</dd>
<dt id="cvpysdk.network.Network.force_ssl"><code class="name">var <span class="ident">force_ssl</span></code></dt>
<dd>
<div class="desc"><p>Gets the value for forceSSL</p>
<p>:return:
boolean - forceSSL</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/network.py#L304-L311" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def force_ssl(self):
    &#34;&#34;&#34;Gets the value for forceSSL

    :return:
        boolean - forceSSL
    &#34;&#34;&#34;
    return self._force_ssl</code></pre>
</details>
</dd>
<dt id="cvpysdk.network.Network.incoming_connections"><code class="name">var <span class="ident">incoming_connections</span></code></dt>
<dd>
<div class="desc"><p>Gets all the incoming connections on a client</p>
<p>:return:
list - incoming connections</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/network.py#L411-L424" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def incoming_connections(self):
    &#34;&#34;&#34;Gets all the incoming connections on a client

    :return:
        list - incoming connections
    &#34;&#34;&#34;

    for incoming_connection in self._restriction_to:
        if incoming_connection[&#39;state&#39;] in self._incoming_connection_type.keys():
            incoming_connection[&#39;state&#39;] = (self._incoming_connection_type[
                incoming_connection[&#39;state&#39;]])

    return self._restriction_to</code></pre>
</details>
</dd>
<dt id="cvpysdk.network.Network.keep_alive_seconds"><code class="name">var <span class="ident">keep_alive_seconds</span></code></dt>
<dd>
<div class="desc"><p>Gets the value set for keep alive</p>
<p>:return:
int - keepAliveSeconds</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/network.py#L394-L401" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def keep_alive_seconds(self):
    &#34;&#34;&#34;Gets the value set for keep alive

    :return:
        int - keepAliveSeconds
    &#34;&#34;&#34;
    return self._keep_alive_seconds</code></pre>
</details>
</dd>
<dt id="cvpysdk.network.Network.lockdown"><code class="name">var <span class="ident">lockdown</span></code></dt>
<dd>
<div class="desc"><p>Gets the value for lockdown</p>
<p>:return:
boolean - lockdown</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/network.py#L340-L347" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def lockdown(self):
    &#34;&#34;&#34;Gets the value for lockdown

    :return:
        boolean - lockdown
    &#34;&#34;&#34;
    return self._lockdown</code></pre>
</details>
</dd>
<dt id="cvpysdk.network.Network.outgoing_routes"><code class="name">var <span class="ident">outgoing_routes</span></code></dt>
<dd>
<div class="desc"><p>Gets the list of all outgoing routes</p>
<p>:return:
list - outgoing routes</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/network.py#L529-L549" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def outgoing_routes(self):
    &#34;&#34;&#34;Gets the list of all outgoing routes

        :return:
            list - outgoing routes

    &#34;&#34;&#34;

    for outgoing_route in self._network_outgoing_routes:
        if (outgoing_route[&#39;fireWallOutGoingRouteOptions&#39;][&#39;connectionProtocol&#39;]
                in self._firewall_outgoing_connection_protocol.keys()):
            (outgoing_route[&#39;fireWallOutGoingRouteOptions&#39;][
                &#39;connectionProtocol&#39;]) = (self._firewall_outgoing_connection_protocol[
                outgoing_route[&#39;fireWallOutGoingRouteOptions&#39;][&#39;connectionProtocol&#39;]])
        if (outgoing_route[&#39;fireWallOutGoingRouteOptions&#39;][&#39;routeType&#39;]
                in self._firewall_outgoing_route_type.keys()):
            outgoing_route[&#39;fireWallOutGoingRouteOptions&#39;][&#39;routeType&#39;] = (self._firewall_outgoing_route_type[
                outgoing_route[&#39;fireWallOutGoingRouteOptions&#39;][&#39;routeType&#39;]])

    return self._network_outgoing_routes</code></pre>
</details>
</dd>
<dt id="cvpysdk.network.Network.proxy"><code class="name">var <span class="ident">proxy</span></code></dt>
<dd>
<div class="desc"><p>Gets the value for isDMZ</p>
<p>:return:
boolean - isDMZ</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/network.py#L376-L383" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def proxy(self):
    &#34;&#34;&#34;Gets the value for isDMZ

    :return:
        boolean - isDMZ
    &#34;&#34;&#34;
    return self._is_dmz</code></pre>
</details>
</dd>
<dt id="cvpysdk.network.Network.roaming_client"><code class="name">var <span class="ident">roaming_client</span></code></dt>
<dd>
<div class="desc"><p>Gets the value for isRoamingClient</p>
<p>:return:
boolen - isRoamingClient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/network.py#L270-L277" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def roaming_client(self):
    &#34;&#34;&#34;Gets the value for isRoamingClient

    :return:
        boolen - isRoamingClient
    &#34;&#34;&#34;
    return self._is_roaming_client</code></pre>
</details>
</dd>
<dt id="cvpysdk.network.Network.tppm_settings"><code class="name">var <span class="ident">tppm_settings</span></code></dt>
<dd>
<div class="desc"><p>Gets the list of tppm settings on a client</p>
<p>:return:
list - tppm settings</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/network.py#L692-L704" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def tppm_settings(self):
    &#34;&#34;&#34;Gets the list of tppm settings on a client

    :return:
        list - tppm settings
    &#34;&#34;&#34;

    for tppm_setting in self._tppm_settings:
        if tppm_setting[&#39;tppmType&#39;] in self._tppm_type.keys():
            tppm_setting[&#39;tppmType&#39;] = self._tppm_type[tppm_setting[&#39;tppmType&#39;]]

    return self._tppm_settings</code></pre>
</details>
</dd>
<dt id="cvpysdk.network.Network.trivial_config"><code class="name">var <span class="ident">trivial_config</span></code></dt>
<dd>
<div class="desc"><p>Gets the value for isTrivialConfig</p>
<p>:return:
boolean - isTrivialConfig</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/network.py#L252-L259" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def trivial_config(self):
    &#34;&#34;&#34;Gets the value for isTrivialConfig

    :return:
        boolean - isTrivialConfig
    &#34;&#34;&#34;
    return self._is_trivial_config</code></pre>
</details>
</dd>
<dt id="cvpysdk.network.Network.tunnel_connection_port"><code class="name">var <span class="ident">tunnel_connection_port</span></code></dt>
<dd>
<div class="desc"><p>Gets the value for tunnel port on the client/client group</p>
<p>:return:
int - tunnelConnectionPort</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/network.py#L287-L294" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def tunnel_connection_port(self):
    &#34;&#34;&#34;Gets the value for tunnel port on the client/client group

    :return:
        int - tunnelConnectionPort
    &#34;&#34;&#34;
    return self._tunnel_connection_port</code></pre>
</details>
</dd>
<dt id="cvpysdk.network.Network.tunnel_init_seconds"><code class="name">var <span class="ident">tunnel_init_seconds</span></code></dt>
<dd>
<div class="desc"><p>Gets the tunnel init seconds</p>
<p>:return:
int - tunnelInitSeconds</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/network.py#L322-L329" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def tunnel_init_seconds(self):
    &#34;&#34;&#34;Gets the tunnel init seconds

    :return:
        int - tunnelInitSeconds
    &#34;&#34;&#34;
    return self._tunnel_init_seconds</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.network.Network.set_additional_ports"><code class="name flex">
<span>def <span class="ident">set_additional_ports</span></span>(<span>self, ports, tunnel_port=8403)</span>
</code></dt>
<dd>
<div class="desc"><p>Sets additional incoming ports and tunnel port with the values provided as parameter</p>
<h2 id="args">Args</h2>
<p>tunnel_port (int) &ndash; value to be set for tunnel port
ports(list)
&ndash; list of ports should be a list of dict containing
start port and end port
[{'startPort':val,'endPort':val}]
Example:
[
{
'startPort': 1024,
'endPort': 1030
},
{
'startPort': 2000,
'endPort':4000
}
]</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if the required key is missing in the input value passed</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/network.py#L489-L527" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_additional_ports(self, ports, tunnel_port=8403):
    &#34;&#34;&#34;Sets additional incoming ports and tunnel port with the values provided as parameter

        Args:
            tunnel_port (int) -- value to be set for tunnel port
            ports(list)  -- list of ports should be a list of dict containing
            start port and end port
            [{&#39;startPort&#39;:val,&#39;endPort&#39;:val}]

        Example:
        [
            {
            &#39;startPort&#39;: 1024,
            &#39;endPort&#39;: 1030
            },
            {
            &#39;startPort&#39;: 2000,
            &#39;endPort&#39;:4000
            }
        ]

        Raises:
            SDKException:
                if the required key is missing in the input value passed
    &#34;&#34;&#34;
    try:
        self._tunnel_connection_port = tunnel_port
        for port in ports:
            additional_port_dict = {
                &#34;startPort&#34;: port[&#39;startPort&#39;],
                &#34;endPort&#34;: port[&#39;endPort&#39;]
            }

            self._port_range.append(additional_port_dict)

        self.configure_network_settings = True

    except KeyError as err:
        raise SDKException(&#39;Client&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))</code></pre>
</details>
</dd>
<dt id="cvpysdk.network.Network.set_incoming_connections"><code class="name flex">
<span>def <span class="ident">set_incoming_connections</span></span>(<span>self, incoming_connections)</span>
</code></dt>
<dd>
<div class="desc"><p>Sets the incoming connections on a client/client group with the list of values provided</p>
<p>Args:
incoming_connections(list)
&ndash; list of incoming connections should be
a list of dict containing incoming connection type, entity name and entity type.
[{'state':val,'entity':val,'isClient':val}]</p>
<pre><code>Example:
[
    {
    'state': 'RESTRICTED',
    'entity': 'centOS',
    'isClient' : True
    },

    {
    'state': 'BLOCKED',
    'entity':  'Edge Clients',
    'isClient' : False
    }
]
</code></pre>
<h2 id="raises">Raises</h2>
<p>SDKException:
if the required key is missing in the input value passed</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/network.py#L426-L478" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_incoming_connections(self, incoming_connections):
    &#34;&#34;&#34;Sets the incoming connections on a client/client group with the list of values provided

     Args:
            incoming_connections(list)  -- list of incoming connections should be
            a list of dict containing incoming connection type, entity name and entity type.
            [{&#39;state&#39;:val,&#39;entity&#39;:val,&#39;isClient&#39;:val}]

        Example:
        [
            {
            &#39;state&#39;: &#39;RESTRICTED&#39;,
            &#39;entity&#39;: &#39;centOS&#39;,
            &#39;isClient&#39; : True
            },

            {
            &#39;state&#39;: &#39;BLOCKED&#39;,
            &#39;entity&#39;:  &#39;Edge Clients&#39;,
            &#39;isClient&#39; : False
            }
        ]

    Raises:
            SDKException:
                if the required key is missing in the input value passed

    &#34;&#34;&#34;
    try:

        for incoming_connection in incoming_connections:

            if incoming_connection[&#39;isClient&#39;]:
                restriction_to_dict = {
                    &#34;state&#34;: incoming_connection[&#39;state&#39;],
                    &#34;entity&#34;: {
                        &#34;clientName&#34;: incoming_connection[&#39;entity&#39;]
                    }
                }

            else:
                restriction_to_dict = {
                    &#34;state&#34;: incoming_connection[&#39;state&#39;],
                    &#34;entity&#34;: {
                        &#34;clientGroupName&#34;: incoming_connection[&#39;entity&#39;]
                    }
                }

            self._restriction_to.append(restriction_to_dict)
        self.configure_network_settings = True

    except KeyError as err:
        raise SDKException(&#39;Client&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))</code></pre>
</details>
</dd>
<dt id="cvpysdk.network.Network.set_outgoing_routes"><code class="name flex">
<span>def <span class="ident">set_outgoing_routes</span></span>(<span>self, outgoing_routes)</span>
</code></dt>
<dd>
<div class="desc"><p>Sets outgoing routes on the client with the list of values provided as parameter</p>
<h2 id="args">Args</h2>
<p>outgoing_routes(list)
&ndash; list of outgoing routes should be a list of dict
containing route type, entity name, entity type, streams, gateway host,
gateway port, tunnel connection protocol and remote proxy based on route type.</p>
<p>For routeType: DIRECT
[{'routeType':'DIRECT',
'remoteEntity':val ,
'streams':val,
'isClient':val,
'forceAllDataTraffic': True,
'connectionProtocol' : 0}]</p>
<p>For routeType: VIA_GATEWAY
[{'routeType':'VIA_GATEWAY',
'remoteEntity':val,
'streams':val,
'gatewayPort':val,
'gatewayHost': val,
'isClient':val,
'forceAllDataTraffic': False,
'connectionProtocol' : 3}]</p>
<p>For routeType: VIA_PROXY
[{'routeType':'VIA_PROXY',
'remoteEntity':val,
'remoteProxy':val,
'isClient':val}]</p>
<dl>
<dt>Valid values for connectionProtocol:</dt>
<dt><strong><code>0</code></strong></dt>
<dd>'HTTP',</dd>
<dt><strong><code>1</code></strong></dt>
<dd>'HTTPS',</dd>
<dt><strong><code>2</code></strong></dt>
<dd>'HTTPS_AuthOnly',</dd>
<dt><strong><code>3</code></strong></dt>
<dd>'RAW_PROTOCOL'</dd>
</dl>
<p>Example:
[
{
'routeType': 'DIRECT',
'remoteEntity':'Testcs' ,
'streams': 1,
'isClient': True,
'forceAllDataTraffic' : True
'connectionProtocol' : 0
},
{
'routeType': 'VIA_GATEWAY',
'remoteEntity': 'centOS',
'streams': 2,
'gatewayPort': 443,
'gatewayHost': '*******',
'isClient': True,
'forceAllDataTraffic' :False
'connectionProtocol' : 1
},
{
'routeType': 'VIA_PROXY',
'remoteEntity': 'Laptop Clients',
'remoteProxy': 'TemplateRHEL65_4',
'isClient': False
}
]</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if routeType is invalid in the input value passed</p>
<pre><code>if the required key is missing in the input value passed
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/network.py#L551-L690" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_outgoing_routes(self, outgoing_routes):
    &#34;&#34;&#34;Sets outgoing routes on the client with the list of values provided as parameter

        Args:
            outgoing_routes(list)  -- list of outgoing routes should be a list of dict
            containing route type, entity name, entity type, streams, gateway host,
            gateway port, tunnel connection protocol and remote proxy based on route type.

            For routeType: DIRECT
            [{&#39;routeType&#39;:&#39;DIRECT&#39;,
            &#39;remoteEntity&#39;:val ,
            &#39;streams&#39;:val,
            &#39;isClient&#39;:val,
            &#39;forceAllDataTraffic&#39;: True,
            &#39;connectionProtocol&#39; : 0}]

            For routeType: VIA_GATEWAY
            [{&#39;routeType&#39;:&#39;VIA_GATEWAY&#39;,
            &#39;remoteEntity&#39;:val,
            &#39;streams&#39;:val,
            &#39;gatewayPort&#39;:val,
            &#39;gatewayHost&#39;: val,
            &#39;isClient&#39;:val,
            &#39;forceAllDataTraffic&#39;: False,
            &#39;connectionProtocol&#39; : 3}]

            For routeType: VIA_PROXY
            [{&#39;routeType&#39;:&#39;VIA_PROXY&#39;,
            &#39;remoteEntity&#39;:val,
            &#39;remoteProxy&#39;:val,
            &#39;isClient&#39;:val}]


            Valid values for connectionProtocol:
            0: &#39;HTTP&#39;,
            1: &#39;HTTPS&#39;,
            2: &#39;HTTPS_AuthOnly&#39;,
            3: &#39;RAW_PROTOCOL&#39;

        Example:
        [
            {
            &#39;routeType&#39;: &#39;DIRECT&#39;,
            &#39;remoteEntity&#39;:&#39;Testcs&#39; ,
            &#39;streams&#39;: 1,
            &#39;isClient&#39;: True,
            &#39;forceAllDataTraffic&#39; : True
            &#39;connectionProtocol&#39; : 0
            },
            {
            &#39;routeType&#39;: &#39;VIA_GATEWAY&#39;,
            &#39;remoteEntity&#39;: &#39;centOS&#39;,
            &#39;streams&#39;: 2,
            &#39;gatewayPort&#39;: 443,
            &#39;gatewayHost&#39;: &#39;*******&#39;,
            &#39;isClient&#39;: True,
            &#39;forceAllDataTraffic&#39; :False
            &#39;connectionProtocol&#39; : 1
            },
            {
            &#39;routeType&#39;: &#39;VIA_PROXY&#39;,
            &#39;remoteEntity&#39;: &#39;Laptop Clients&#39;,
            &#39;remoteProxy&#39;: &#39;TemplateRHEL65_4&#39;,
            &#39;isClient&#39;: False
            }
        ]

        Raises:
            SDKException:
                if routeType is invalid in the input value passed

                if the required key is missing in the input value passed

    &#34;&#34;&#34;

    try:

        for outgoing_route in outgoing_routes:
            if outgoing_route[&#39;isClient&#39;]:
                remote_entity_dict = {
                    &#34;clientName&#34;: outgoing_route[&#39;remoteEntity&#39;]

                }

            else:
                remote_entity_dict = {

                    &#34;clientGroupName&#34;: outgoing_route[&#39;remoteEntity&#39;]
                }

            if outgoing_route[&#39;routeType&#39;] == self._firewall_outgoing_route_type[0]:
                gatewayport = 0
                gatewayhostname = &#34;&#34;
                remote_proxy = {}
                nstreams = outgoing_route[&#39;streams&#39;]
                force_all_data_traffic = outgoing_route[&#39;forceAllDataTraffic&#39;]
                connection_protocol = outgoing_route.get(&#39;connectionProtocol&#39;, 2)

            elif outgoing_route[&#39;routeType&#39;] == self._firewall_outgoing_route_type[1]:
                gatewayport = outgoing_route[&#39;gatewayPort&#39;]
                gatewayhostname = outgoing_route[&#39;gatewayHost&#39;]
                remote_proxy = {}
                nstreams = outgoing_route[&#39;streams&#39;]
                force_all_data_traffic = outgoing_route[&#39;forceAllDataTraffic&#39;]
                connection_protocol = outgoing_route.get(&#39;connectionProtocol&#39;, 2)

            elif outgoing_route[&#39;routeType&#39;] == self._firewall_outgoing_route_type[2]:
                gatewayport = 0
                gatewayhostname = &#34;&#34;
                nstreams = 1
                force_all_data_traffic = False
                connection_protocol = 2
                remote_proxy = {
                    &#34;clientName&#34;: outgoing_route[&#39;remoteProxy&#39;],

                    &#34;clientGroupName&#34;: &#34;&#34;,
                    &#34;_type_&#34;: 3
                }

            else:
                raise SDKException(&#39;Client&#39;, &#39;101&#39;)

            outgoing_routes_dict = {
                &#34;fireWallOutGoingRouteOptions&#34;: {
                    &#34;numberOfStreams&#34;: nstreams,
                    &#34;connectionProtocol&#34;: connection_protocol,
                    &#34;gatewayTunnelPort&#34;: gatewayport,
                    &#34;forceAllBackupRestoreDataTraffic&#34;: force_all_data_traffic,
                    &#34;gatewayHostname&#34;: gatewayhostname,
                    &#34;routeType&#34;: outgoing_route[&#39;routeType&#39;],
                    &#34;remoteProxy&#34;: remote_proxy
                },
                &#34;remoteEntity&#34;: remote_entity_dict
            }

            self._network_outgoing_routes.append(outgoing_routes_dict)
        self.configure_network_settings = True

    except KeyError as err:
        raise SDKException(&#39;Client&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))</code></pre>
</details>
</dd>
<dt id="cvpysdk.network.Network.set_tppm_settings"><code class="name flex">
<span>def <span class="ident">set_tppm_settings</span></span>(<span>self, tppm_settings)</span>
</code></dt>
<dd>
<div class="desc"><p>Sets tppm on the client with the list of values provided as parameter</p>
<p>Note:
This is supported only on client level</p>
<h2 id="args">Args</h2>
<p>tppm_settings(list)
&ndash; list of tppm settings should be a list of dict containing
tppm type, port number and proxy entity
[{'tppmType':val, 'portNumber':val, 'proxyEntity':val}]</p>
<p>Valid values for tppmType:
1. WEB_SERVER_FOR_IIS_SERVER
2. COMMSERVE
3. REPORTS
4. CUSTOM_REPORT_ENGINE
Example:
[
{
'tppmType': 'WEB_SERVER_FOR_IIS_SERVER',
'portNumber':9999,
'proxyEntity' : 'shezavm3'
},</p>
<pre><code>{
'tppmType': 'REPORTS',
'portNumber':8888,
'proxyEntity' : 'shezavm11'
}
</code></pre>
<p>]</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if tppmType is invalid in the input value passed</p>
<pre><code>if the required key is missing in the input value passed
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/network.py#L706-L771" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_tppm_settings(self, tppm_settings):
    &#34;&#34;&#34;Sets tppm on the client with the list of values provided as parameter


        Note:  This is supported only on client level

        Args:
            tppm_settings(list)  -- list of tppm settings should be a list of dict containing
            tppm type, port number and proxy entity
            [{&#39;tppmType&#39;:val, &#39;portNumber&#39;:val, &#39;proxyEntity&#39;:val}]

            Valid values for tppmType:
            1. WEB_SERVER_FOR_IIS_SERVER
            2. COMMSERVE
            3. REPORTS
            4. CUSTOM_REPORT_ENGINE

        Example:
        [
            {
            &#39;tppmType&#39;: &#39;WEB_SERVER_FOR_IIS_SERVER&#39;,
            &#39;portNumber&#39;:9999,
            &#39;proxyEntity&#39; : &#39;shezavm3&#39;
            },

            {
            &#39;tppmType&#39;: &#39;REPORTS&#39;,
            &#39;portNumber&#39;:8888,
            &#39;proxyEntity&#39; : &#39;shezavm11&#39;
            }
        ]

        Raises:
            SDKException:
                if tppmType is invalid in the input value passed

                if the required key is missing in the input value passed

    &#34;&#34;&#34;

    try:

        if self.flag == &#34;CLIENT&#34;:
            for tppm_setting in tppm_settings:

                if tppm_setting[&#39;tppmType&#39;] in self._tppm_type.values():
                    tppm_dict = {
                        &#34;enable&#34;: True,
                        &#34;tppmType&#34;: tppm_setting[&#39;tppmType&#39;],
                        &#34;proxyInformation&#34;: {
                            &#34;portNumber&#34;: tppm_setting[&#39;portNumber&#39;],
                            &#34;proxyEntity&#34;: {
                                &#34;clientName&#34;: tppm_setting[&#39;proxyEntity&#39;],
                                &#34;_type_&#34;: 3
                            }
                        }
                    }
                    self._tppm_settings.append(tppm_dict)

                else:
                    raise SDKException(&#39;Client&#39;, &#39;101&#39;)

        self.configure_network_settings = True

    except KeyError as err:
        raise SDKException(&#39;Client&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.network.Network" href="#cvpysdk.network.Network">Network</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.network.Network.additional_ports" href="#cvpysdk.network.Network.additional_ports">additional_ports</a></code></li>
<li><code><a title="cvpysdk.network.Network.bind_open_ports" href="#cvpysdk.network.Network.bind_open_ports">bind_open_ports</a></code></li>
<li><code><a title="cvpysdk.network.Network.configure_network_settings" href="#cvpysdk.network.Network.configure_network_settings">configure_network_settings</a></code></li>
<li><code><a title="cvpysdk.network.Network.force_ssl" href="#cvpysdk.network.Network.force_ssl">force_ssl</a></code></li>
<li><code><a title="cvpysdk.network.Network.incoming_connections" href="#cvpysdk.network.Network.incoming_connections">incoming_connections</a></code></li>
<li><code><a title="cvpysdk.network.Network.keep_alive_seconds" href="#cvpysdk.network.Network.keep_alive_seconds">keep_alive_seconds</a></code></li>
<li><code><a title="cvpysdk.network.Network.lockdown" href="#cvpysdk.network.Network.lockdown">lockdown</a></code></li>
<li><code><a title="cvpysdk.network.Network.outgoing_routes" href="#cvpysdk.network.Network.outgoing_routes">outgoing_routes</a></code></li>
<li><code><a title="cvpysdk.network.Network.proxy" href="#cvpysdk.network.Network.proxy">proxy</a></code></li>
<li><code><a title="cvpysdk.network.Network.roaming_client" href="#cvpysdk.network.Network.roaming_client">roaming_client</a></code></li>
<li><code><a title="cvpysdk.network.Network.set_additional_ports" href="#cvpysdk.network.Network.set_additional_ports">set_additional_ports</a></code></li>
<li><code><a title="cvpysdk.network.Network.set_incoming_connections" href="#cvpysdk.network.Network.set_incoming_connections">set_incoming_connections</a></code></li>
<li><code><a title="cvpysdk.network.Network.set_outgoing_routes" href="#cvpysdk.network.Network.set_outgoing_routes">set_outgoing_routes</a></code></li>
<li><code><a title="cvpysdk.network.Network.set_tppm_settings" href="#cvpysdk.network.Network.set_tppm_settings">set_tppm_settings</a></code></li>
<li><code><a title="cvpysdk.network.Network.tppm_settings" href="#cvpysdk.network.Network.tppm_settings">tppm_settings</a></code></li>
<li><code><a title="cvpysdk.network.Network.trivial_config" href="#cvpysdk.network.Network.trivial_config">trivial_config</a></code></li>
<li><code><a title="cvpysdk.network.Network.tunnel_connection_port" href="#cvpysdk.network.Network.tunnel_connection_port">tunnel_connection_port</a></code></li>
<li><code><a title="cvpysdk.network.Network.tunnel_init_seconds" href="#cvpysdk.network.Network.tunnel_init_seconds">tunnel_init_seconds</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>