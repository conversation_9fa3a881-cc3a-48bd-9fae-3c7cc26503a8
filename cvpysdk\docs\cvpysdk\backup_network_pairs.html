<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.backup_network_pairs API documentation</title>
<meta name="description" content="Class to perform all the Backup Network Pairs operations on commcell …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.backup_network_pairs</code></h1>
</header>
<section id="section-intro">
<p>Class to perform all the Backup Network Pairs operations on commcell</p>
<p>BackupNetworkPairs is the only class defined in this file.</p>
<p>BackupNetworkPairs: Helper class to perform Backup Network Pairs operations.</p>
<h2 id="backupnetworkpairs">Backupnetworkpairs</h2>
<p><strong>init</strong>()
&ndash;
initializes BackupNetworkPairs class object.</p>
<p><strong>repr</strong>()
&ndash;
returns the string to represent the instance
of the BackupNetworkPairs class</p>
<p>get_backup_interface_for_client()
&ndash;
returns list of interfaces on a client</p>
<p>add_backup_interface_pairs ()
&ndash;
sets backup interface pairs on a client/client group</p>
<p>delete__backup_interface_pairs()
&ndash;
deletes backup interface pairs on a client/client group</p>
<p>_modify_backup_interface_pairs()
&ndash; modifies backup interface pairs on a client/client group</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backup_network_pairs.py#L1-L244" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-
# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Class to perform all the Backup Network Pairs operations on commcell

BackupNetworkPairs is the only class defined in this file.

BackupNetworkPairs: Helper class to perform Backup Network Pairs operations.

BackupNetworkPairs:

    __init__()                          --  initializes BackupNetworkPairs class object.

    __repr__()                          --  returns the string to represent the instance
                                            of the BackupNetworkPairs class

    get_backup_interface_for_client()   --  returns list of interfaces on a client

    add_backup_interface_pairs ()       --  sets backup interface pairs on a client/client group

    delete__backup_interface_pairs()    --  deletes backup interface pairs on a client/client group

    _modify_backup_interface_pairs()    -- modifies backup interface pairs on a client/client group

&#34;&#34;&#34;

from __future__ import absolute_import
from __future__ import unicode_literals
from .exception import SDKException


class BackupNetworkPairs(object):
    &#34;&#34;&#34;Class for representing backup network pairs operations from commcell&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initializes object of the BackupNetworkPairs class.

            Args:
               commcell_object (object) -instance of the commcell class

            Returns:
               object - instance of the BackupNetworkPairs class
        &#34;&#34;&#34;

        self._commcell_object = commcell_object
        self._cvpysdk_object = self._commcell_object._cvpysdk_object
        self._services = self._commcell_object._services
        self._backup_network_pairs = None
        self._backup_network_pair = self._services[&#39;BACKUP_NETWORK_PAIR&#39;]
        self._update_response_ = self._commcell_object._update_response_
        self.operation_type = [&#39;ADD&#39;, &#39;DELETE&#39;]

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of BackupNetworkPairs class&#34;&#34;&#34;

        return &#34;BackupNetworkPairs class instance for Commcell&#34;

    def get_backup_interface_for_client(self, client_name):
        &#34;&#34;&#34;Returns interfaces set on a particular client

            Args:
                client_name (str)  --  name of client

            Returns:
                list - list of interfaces with source and destination

            Raises:
                SDKException:
                    if response is not received

        &#34;&#34;&#34;

        client_id = self._commcell_object.clients.all_clients.get(client_name).get(&#39;id&#39;)

        self._backup_network_pairs = self._services[&#39;BACKUP_NETWORK_PAIRS&#39;] % client_id

        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._backup_network_pairs)

        if flag:
            if response.json() and &#39;ArchPipeLineList&#39; in response.json():
                interface = response.json()[&#39;ArchPipeLineList&#39;]

            else:
                interface = {}
            return interface

        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def add_backup_interface_pairs(self, interface_pairs):
        &#34;&#34;&#34;Adds backup interface pairs on clients/client groups

            Args:
                interface_pairs (list)  --  list of tuples containing dict of source and destination

                Example:
                [({&#39;client&#39;: &#39;featuretest&#39;, &#39;srcip&#39;: &#39;*************&#39;},
                {&#39;client&#39;: &#39;SP9client&#39;, &#39;destip&#39;: &#39;**********&#39;}),
                ({&#39;client&#39;: &#39;featuretest&#39;, &#39;srcip&#39;: &#39;*************&#39;},
                {&#39;clientgroup&#39;: &#39;G1&#39;, &#39;destip&#39;: &#39;No Default Interface&#39;}),
                ({&#39;clientgroup&#39;: &#39;G2&#39;, &#39;srcip&#39;: &#39;**********/16&#39;},
                {&#39;clientgroup&#39;: &#39;G3&#39;, &#39;destip&#39;: &#39;172.19.0.*&#39;})]

                Note: 0th index should be source with key &#39;srcip&#39; and 1st index
                should be destination with key &#39;destip&#39;

                      entities should be passed with key client/clientgroup

            Raises:
                SDKException:
                    if input is not correct

                    if response is not received

        &#34;&#34;&#34;

        if not isinstance(interface_pairs, list):
            raise SDKException(&#39;BackupNetworkPairs&#39;, &#39;101&#39;,
                               &#39;Interface Pairs should be a list of tuples &#39;
                               &#39;containing dictionary of source and destination&#39;)

        self._modify_backup_interface_pairs(interface_pairs, self.operation_type[0])

    def delete_backup_interface_pairs(self, interface_pairs):
        &#34;&#34;&#34;Deletes backup interface pairs on clients/client groups

            Args:
                interface_pairs (list)  --  list of tuples containing dict of source and destination

                Example:
                [({&#39;client&#39;: &#39;featuretest&#39;, &#39;srcip&#39;: &#39;*************&#39;},
                {&#39;client&#39;: &#39;SP9client&#39;, &#39;destip&#39;: &#39;**********&#39;}),
                ({&#39;client&#39;: &#39;featuretest&#39;, &#39;srcip&#39;: &#39;*************&#39;},
                {&#39;clientgroup&#39;: &#39;G1&#39;, &#39;destip&#39;: &#39;No Default Interface&#39;}),
                ({&#39;clientgroup&#39;: &#39;G2&#39;, &#39;srcip&#39;: &#39;**********/16&#39;},
                {&#39;clientgroup&#39;: &#39;G3&#39;, &#39;destip&#39;: &#39;172.19.0.*&#39;})]

                Note: 0th index should be source with key &#39;srcip&#39; and 1st index
                should be destination with key &#39;destip&#39;

                      entities should be passed with key client/clientgroup

            Raises:
                SDKException:
                    if input is not correct

                    if response is not received

        &#34;&#34;&#34;

        if not isinstance(interface_pairs, list):
            raise SDKException(&#39;BackupNetworkPairs&#39;, &#39;101&#39;,
                               &#39;Interface Pairs should be a list of tuples &#39;
                               &#39;containing dictionary of source and destination&#39;)

        self._modify_backup_interface_pairs(interface_pairs, self.operation_type[1])

    def _modify_backup_interface_pairs(self, interface_pairs, operation_type):
        &#34;&#34;&#34;Sets a backup interface pair between clients/client-groups

            Args:
                operation_type  (str)  --  operation type--&gt; add, update, delete

                interface_pairs (list)  --  list of tuples containing dict of source and destination
                                           source and destination can be a combination of
                                           client/client group

                Example:
                [({&#39;client&#39;: &#39;featuretest&#39;, &#39;srcip&#39;: &#39;*************&#39;},
                {&#39;client&#39;: &#39;SP9client&#39;, &#39;destip&#39;: &#39;**********&#39;}),
                ({&#39;client&#39;: &#39;featuretest&#39;, &#39;srcip&#39;: &#39;*************&#39;},
                {&#39;clientgroup&#39;: &#39;G1&#39;, &#39;destip&#39;: &#39;No Default Interface&#39;}),
                ({&#39;clientgroup&#39;: &#39;G2&#39;, &#39;srcip&#39;: &#39;**********/16&#39;},
                {&#39;clientgroup&#39;: &#39;G3&#39;, &#39;destip&#39;: &#39;172.19.0.*&#39;})]

                Note: 0th index should be source with key &#39;srcip&#39; and 1st index
                should be destination with key &#39;destip&#39;

                       entities should be passed with key client/clientgroup

            Raises:
                SDKException:
                    if input is not correct

                    if response is not received

        &#34;&#34;&#34;

        archpipeline_list = []

        for interface_pair in interface_pairs:
            interface_pair_dict = {
                &#34;destGroupId&#34;: int(self._commcell_object.client_groups.all_clientgroups.get(
                    interface_pair[1].get(&#39;clientgroup&#39;, &#39;&#39;).lower(), 0)),
                &#34;srcGroupId&#34;: int(self._commcell_object.client_groups.all_clientgroups.get(
                    interface_pair[0].get(&#39;clientgroup&#39;, &#39;&#39;).lower(), 0)),
                &#34;isActive&#34;: 1,
                &#34;client2&#34;: {
                    &#34;name&#34;: interface_pair[1][&#39;destip&#39;],
                    &#34;id&#34;: int(self._commcell_object.clients.all_clients.get(
                        interface_pair[1].get(&#39;client&#39;, &#39;&#39;).lower(), {}).get(&#39;id&#39;, 0))
                },
                &#34;client1&#34;: {
                    &#34;name&#34;: interface_pair[0][&#39;srcip&#39;],
                    &#34;id&#34;: int(self._commcell_object.clients.all_clients.get(
                        interface_pair[0].get(&#39;client&#39;, &#39;&#39;).lower(), {}).get(&#39;id&#39;, 0))
                }
            }

            archpipeline_list.append(interface_pair_dict)

        request_json = {
            &#34;operationType&#34;: operation_type,
            &#34;ArchPipeLineList&#34;: archpipeline_list
        }

        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;,
                                                           self._backup_network_pair,
                                                           request_json)

        if flag:
            if response.json():
                if response.json()[&#39;errorCode&#39;] != 0:
                    raise SDKException(&#39;BackupNetworkPairs&#39;, &#39;101&#39;,
                                       &#34;Failed to set network pairs&#34;)

        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.backup_network_pairs.BackupNetworkPairs"><code class="flex name class">
<span>class <span class="ident">BackupNetworkPairs</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing backup network pairs operations from commcell</p>
<p>Initializes object of the BackupNetworkPairs class.</p>
<h2 id="args">Args</h2>
<p>commcell_object (object) -instance of the commcell class</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the BackupNetworkPairs class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backup_network_pairs.py#L46-L244" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class BackupNetworkPairs(object):
    &#34;&#34;&#34;Class for representing backup network pairs operations from commcell&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initializes object of the BackupNetworkPairs class.

            Args:
               commcell_object (object) -instance of the commcell class

            Returns:
               object - instance of the BackupNetworkPairs class
        &#34;&#34;&#34;

        self._commcell_object = commcell_object
        self._cvpysdk_object = self._commcell_object._cvpysdk_object
        self._services = self._commcell_object._services
        self._backup_network_pairs = None
        self._backup_network_pair = self._services[&#39;BACKUP_NETWORK_PAIR&#39;]
        self._update_response_ = self._commcell_object._update_response_
        self.operation_type = [&#39;ADD&#39;, &#39;DELETE&#39;]

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of BackupNetworkPairs class&#34;&#34;&#34;

        return &#34;BackupNetworkPairs class instance for Commcell&#34;

    def get_backup_interface_for_client(self, client_name):
        &#34;&#34;&#34;Returns interfaces set on a particular client

            Args:
                client_name (str)  --  name of client

            Returns:
                list - list of interfaces with source and destination

            Raises:
                SDKException:
                    if response is not received

        &#34;&#34;&#34;

        client_id = self._commcell_object.clients.all_clients.get(client_name).get(&#39;id&#39;)

        self._backup_network_pairs = self._services[&#39;BACKUP_NETWORK_PAIRS&#39;] % client_id

        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._backup_network_pairs)

        if flag:
            if response.json() and &#39;ArchPipeLineList&#39; in response.json():
                interface = response.json()[&#39;ArchPipeLineList&#39;]

            else:
                interface = {}
            return interface

        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def add_backup_interface_pairs(self, interface_pairs):
        &#34;&#34;&#34;Adds backup interface pairs on clients/client groups

            Args:
                interface_pairs (list)  --  list of tuples containing dict of source and destination

                Example:
                [({&#39;client&#39;: &#39;featuretest&#39;, &#39;srcip&#39;: &#39;*************&#39;},
                {&#39;client&#39;: &#39;SP9client&#39;, &#39;destip&#39;: &#39;**********&#39;}),
                ({&#39;client&#39;: &#39;featuretest&#39;, &#39;srcip&#39;: &#39;*************&#39;},
                {&#39;clientgroup&#39;: &#39;G1&#39;, &#39;destip&#39;: &#39;No Default Interface&#39;}),
                ({&#39;clientgroup&#39;: &#39;G2&#39;, &#39;srcip&#39;: &#39;**********/16&#39;},
                {&#39;clientgroup&#39;: &#39;G3&#39;, &#39;destip&#39;: &#39;172.19.0.*&#39;})]

                Note: 0th index should be source with key &#39;srcip&#39; and 1st index
                should be destination with key &#39;destip&#39;

                      entities should be passed with key client/clientgroup

            Raises:
                SDKException:
                    if input is not correct

                    if response is not received

        &#34;&#34;&#34;

        if not isinstance(interface_pairs, list):
            raise SDKException(&#39;BackupNetworkPairs&#39;, &#39;101&#39;,
                               &#39;Interface Pairs should be a list of tuples &#39;
                               &#39;containing dictionary of source and destination&#39;)

        self._modify_backup_interface_pairs(interface_pairs, self.operation_type[0])

    def delete_backup_interface_pairs(self, interface_pairs):
        &#34;&#34;&#34;Deletes backup interface pairs on clients/client groups

            Args:
                interface_pairs (list)  --  list of tuples containing dict of source and destination

                Example:
                [({&#39;client&#39;: &#39;featuretest&#39;, &#39;srcip&#39;: &#39;*************&#39;},
                {&#39;client&#39;: &#39;SP9client&#39;, &#39;destip&#39;: &#39;**********&#39;}),
                ({&#39;client&#39;: &#39;featuretest&#39;, &#39;srcip&#39;: &#39;*************&#39;},
                {&#39;clientgroup&#39;: &#39;G1&#39;, &#39;destip&#39;: &#39;No Default Interface&#39;}),
                ({&#39;clientgroup&#39;: &#39;G2&#39;, &#39;srcip&#39;: &#39;**********/16&#39;},
                {&#39;clientgroup&#39;: &#39;G3&#39;, &#39;destip&#39;: &#39;172.19.0.*&#39;})]

                Note: 0th index should be source with key &#39;srcip&#39; and 1st index
                should be destination with key &#39;destip&#39;

                      entities should be passed with key client/clientgroup

            Raises:
                SDKException:
                    if input is not correct

                    if response is not received

        &#34;&#34;&#34;

        if not isinstance(interface_pairs, list):
            raise SDKException(&#39;BackupNetworkPairs&#39;, &#39;101&#39;,
                               &#39;Interface Pairs should be a list of tuples &#39;
                               &#39;containing dictionary of source and destination&#39;)

        self._modify_backup_interface_pairs(interface_pairs, self.operation_type[1])

    def _modify_backup_interface_pairs(self, interface_pairs, operation_type):
        &#34;&#34;&#34;Sets a backup interface pair between clients/client-groups

            Args:
                operation_type  (str)  --  operation type--&gt; add, update, delete

                interface_pairs (list)  --  list of tuples containing dict of source and destination
                                           source and destination can be a combination of
                                           client/client group

                Example:
                [({&#39;client&#39;: &#39;featuretest&#39;, &#39;srcip&#39;: &#39;*************&#39;},
                {&#39;client&#39;: &#39;SP9client&#39;, &#39;destip&#39;: &#39;**********&#39;}),
                ({&#39;client&#39;: &#39;featuretest&#39;, &#39;srcip&#39;: &#39;*************&#39;},
                {&#39;clientgroup&#39;: &#39;G1&#39;, &#39;destip&#39;: &#39;No Default Interface&#39;}),
                ({&#39;clientgroup&#39;: &#39;G2&#39;, &#39;srcip&#39;: &#39;**********/16&#39;},
                {&#39;clientgroup&#39;: &#39;G3&#39;, &#39;destip&#39;: &#39;172.19.0.*&#39;})]

                Note: 0th index should be source with key &#39;srcip&#39; and 1st index
                should be destination with key &#39;destip&#39;

                       entities should be passed with key client/clientgroup

            Raises:
                SDKException:
                    if input is not correct

                    if response is not received

        &#34;&#34;&#34;

        archpipeline_list = []

        for interface_pair in interface_pairs:
            interface_pair_dict = {
                &#34;destGroupId&#34;: int(self._commcell_object.client_groups.all_clientgroups.get(
                    interface_pair[1].get(&#39;clientgroup&#39;, &#39;&#39;).lower(), 0)),
                &#34;srcGroupId&#34;: int(self._commcell_object.client_groups.all_clientgroups.get(
                    interface_pair[0].get(&#39;clientgroup&#39;, &#39;&#39;).lower(), 0)),
                &#34;isActive&#34;: 1,
                &#34;client2&#34;: {
                    &#34;name&#34;: interface_pair[1][&#39;destip&#39;],
                    &#34;id&#34;: int(self._commcell_object.clients.all_clients.get(
                        interface_pair[1].get(&#39;client&#39;, &#39;&#39;).lower(), {}).get(&#39;id&#39;, 0))
                },
                &#34;client1&#34;: {
                    &#34;name&#34;: interface_pair[0][&#39;srcip&#39;],
                    &#34;id&#34;: int(self._commcell_object.clients.all_clients.get(
                        interface_pair[0].get(&#39;client&#39;, &#39;&#39;).lower(), {}).get(&#39;id&#39;, 0))
                }
            }

            archpipeline_list.append(interface_pair_dict)

        request_json = {
            &#34;operationType&#34;: operation_type,
            &#34;ArchPipeLineList&#34;: archpipeline_list
        }

        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;,
                                                           self._backup_network_pair,
                                                           request_json)

        if flag:
            if response.json():
                if response.json()[&#39;errorCode&#39;] != 0:
                    raise SDKException(&#39;BackupNetworkPairs&#39;, &#39;101&#39;,
                                       &#34;Failed to set network pairs&#34;)

        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.backup_network_pairs.BackupNetworkPairs.add_backup_interface_pairs"><code class="name flex">
<span>def <span class="ident">add_backup_interface_pairs</span></span>(<span>self, interface_pairs)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds backup interface pairs on clients/client groups</p>
<h2 id="args">Args</h2>
<p>interface_pairs (list)
&ndash;
list of tuples containing dict of source and destination</p>
<p>Example:
[({'client': 'featuretest', 'srcip': '*************'},
{'client': 'SP9client', 'destip': '**********'}),
({'client': 'featuretest', 'srcip': '*************'},
{'clientgroup': 'G1', 'destip': 'No Default Interface'}),
({'clientgroup': 'G2', 'srcip': '**********/16'},
{'clientgroup': 'G3', 'destip': '172.19.0.*'})]</p>
<dl>
<dt><strong><code>Note</code></strong></dt>
<dd>0th index should be source with key 'srcip' and 1st index</dd>
</dl>
<p>should be destination with key 'destip'</p>
<pre><code>  entities should be passed with key client/clientgroup
</code></pre>
<h2 id="raises">Raises</h2>
<p>SDKException:
if input is not correct</p>
<pre><code>if response is not received
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backup_network_pairs.py#L105-L137" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_backup_interface_pairs(self, interface_pairs):
    &#34;&#34;&#34;Adds backup interface pairs on clients/client groups

        Args:
            interface_pairs (list)  --  list of tuples containing dict of source and destination

            Example:
            [({&#39;client&#39;: &#39;featuretest&#39;, &#39;srcip&#39;: &#39;*************&#39;},
            {&#39;client&#39;: &#39;SP9client&#39;, &#39;destip&#39;: &#39;**********&#39;}),
            ({&#39;client&#39;: &#39;featuretest&#39;, &#39;srcip&#39;: &#39;*************&#39;},
            {&#39;clientgroup&#39;: &#39;G1&#39;, &#39;destip&#39;: &#39;No Default Interface&#39;}),
            ({&#39;clientgroup&#39;: &#39;G2&#39;, &#39;srcip&#39;: &#39;**********/16&#39;},
            {&#39;clientgroup&#39;: &#39;G3&#39;, &#39;destip&#39;: &#39;172.19.0.*&#39;})]

            Note: 0th index should be source with key &#39;srcip&#39; and 1st index
            should be destination with key &#39;destip&#39;

                  entities should be passed with key client/clientgroup

        Raises:
            SDKException:
                if input is not correct

                if response is not received

    &#34;&#34;&#34;

    if not isinstance(interface_pairs, list):
        raise SDKException(&#39;BackupNetworkPairs&#39;, &#39;101&#39;,
                           &#39;Interface Pairs should be a list of tuples &#39;
                           &#39;containing dictionary of source and destination&#39;)

    self._modify_backup_interface_pairs(interface_pairs, self.operation_type[0])</code></pre>
</details>
</dd>
<dt id="cvpysdk.backup_network_pairs.BackupNetworkPairs.delete_backup_interface_pairs"><code class="name flex">
<span>def <span class="ident">delete_backup_interface_pairs</span></span>(<span>self, interface_pairs)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes backup interface pairs on clients/client groups</p>
<h2 id="args">Args</h2>
<p>interface_pairs (list)
&ndash;
list of tuples containing dict of source and destination</p>
<p>Example:
[({'client': 'featuretest', 'srcip': '*************'},
{'client': 'SP9client', 'destip': '**********'}),
({'client': 'featuretest', 'srcip': '*************'},
{'clientgroup': 'G1', 'destip': 'No Default Interface'}),
({'clientgroup': 'G2', 'srcip': '**********/16'},
{'clientgroup': 'G3', 'destip': '172.19.0.*'})]</p>
<dl>
<dt><strong><code>Note</code></strong></dt>
<dd>0th index should be source with key 'srcip' and 1st index</dd>
</dl>
<p>should be destination with key 'destip'</p>
<pre><code>  entities should be passed with key client/clientgroup
</code></pre>
<h2 id="raises">Raises</h2>
<p>SDKException:
if input is not correct</p>
<pre><code>if response is not received
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backup_network_pairs.py#L139-L171" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete_backup_interface_pairs(self, interface_pairs):
    &#34;&#34;&#34;Deletes backup interface pairs on clients/client groups

        Args:
            interface_pairs (list)  --  list of tuples containing dict of source and destination

            Example:
            [({&#39;client&#39;: &#39;featuretest&#39;, &#39;srcip&#39;: &#39;*************&#39;},
            {&#39;client&#39;: &#39;SP9client&#39;, &#39;destip&#39;: &#39;**********&#39;}),
            ({&#39;client&#39;: &#39;featuretest&#39;, &#39;srcip&#39;: &#39;*************&#39;},
            {&#39;clientgroup&#39;: &#39;G1&#39;, &#39;destip&#39;: &#39;No Default Interface&#39;}),
            ({&#39;clientgroup&#39;: &#39;G2&#39;, &#39;srcip&#39;: &#39;**********/16&#39;},
            {&#39;clientgroup&#39;: &#39;G3&#39;, &#39;destip&#39;: &#39;172.19.0.*&#39;})]

            Note: 0th index should be source with key &#39;srcip&#39; and 1st index
            should be destination with key &#39;destip&#39;

                  entities should be passed with key client/clientgroup

        Raises:
            SDKException:
                if input is not correct

                if response is not received

    &#34;&#34;&#34;

    if not isinstance(interface_pairs, list):
        raise SDKException(&#39;BackupNetworkPairs&#39;, &#39;101&#39;,
                           &#39;Interface Pairs should be a list of tuples &#39;
                           &#39;containing dictionary of source and destination&#39;)

    self._modify_backup_interface_pairs(interface_pairs, self.operation_type[1])</code></pre>
</details>
</dd>
<dt id="cvpysdk.backup_network_pairs.BackupNetworkPairs.get_backup_interface_for_client"><code class="name flex">
<span>def <span class="ident">get_backup_interface_for_client</span></span>(<span>self, client_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns interfaces set on a particular client</p>
<h2 id="args">Args</h2>
<p>client_name (str)
&ndash;
name of client</p>
<h2 id="returns">Returns</h2>
<p>list - list of interfaces with source and destination</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if response is not received</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backup_network_pairs.py#L72-L103" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_backup_interface_for_client(self, client_name):
    &#34;&#34;&#34;Returns interfaces set on a particular client

        Args:
            client_name (str)  --  name of client

        Returns:
            list - list of interfaces with source and destination

        Raises:
            SDKException:
                if response is not received

    &#34;&#34;&#34;

    client_id = self._commcell_object.clients.all_clients.get(client_name).get(&#39;id&#39;)

    self._backup_network_pairs = self._services[&#39;BACKUP_NETWORK_PAIRS&#39;] % client_id

    flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._backup_network_pairs)

    if flag:
        if response.json() and &#39;ArchPipeLineList&#39; in response.json():
            interface = response.json()[&#39;ArchPipeLineList&#39;]

        else:
            interface = {}
        return interface

    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.backup_network_pairs.BackupNetworkPairs" href="#cvpysdk.backup_network_pairs.BackupNetworkPairs">BackupNetworkPairs</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.backup_network_pairs.BackupNetworkPairs.add_backup_interface_pairs" href="#cvpysdk.backup_network_pairs.BackupNetworkPairs.add_backup_interface_pairs">add_backup_interface_pairs</a></code></li>
<li><code><a title="cvpysdk.backup_network_pairs.BackupNetworkPairs.delete_backup_interface_pairs" href="#cvpysdk.backup_network_pairs.BackupNetworkPairs.delete_backup_interface_pairs">delete_backup_interface_pairs</a></code></li>
<li><code><a title="cvpysdk.backup_network_pairs.BackupNetworkPairs.get_backup_interface_for_client" href="#cvpysdk.backup_network_pairs.BackupNetworkPairs.get_backup_interface_for_client">get_backup_interface_for_client</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>