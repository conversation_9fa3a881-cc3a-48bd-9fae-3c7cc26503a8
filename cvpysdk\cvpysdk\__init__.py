# -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

"""
CVPySDK (Developer SDK - Python) is a Python Package for Commvault Software.

CVPySDK uses Commvault REST API to perform operations on a Commcell.

CVPySDK is available on GitHub (https://github.com/Commvault/cvpysdk).

CVPySDK is compatible with Python 3

CVPySDK requires the following Python packages to run:

    -   **requests**

    -   **xmltodict**

And Commvault Software v11 SP7 or later release with WebConsole installed

"""

__author__ = 'Commvault Systems Inc.'
__version__ = '11.40'
