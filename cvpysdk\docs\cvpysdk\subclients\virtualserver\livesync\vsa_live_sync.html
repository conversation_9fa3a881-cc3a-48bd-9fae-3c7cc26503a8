<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.subclients.virtualserver.livesync.vsa_live_sync API documentation</title>
<meta name="description" content="File for configuring and monitoring live sync on the VSA subclient …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.subclients.virtualserver.livesync.vsa_live_sync</code></h1>
</header>
<section id="section-intro">
<p>File for configuring and monitoring live sync on the VSA subclient.</p>
<p>VsaLiveSync, LiveSyncPairs and LiveSyncVMPair are the 3 classes defined in this file.</p>
<p>VsaLiveSync: Class for configuring virtual server agent live sync</p>
<p>LiveSyncPairs: Class for all live sync pairs under a subclient</p>
<p>LiveSyncVMPair: Class for monitoring and configuring a Live sync VM pair</p>
<h1 id="vsalivesync">VsaLiveSync:</h1>
<pre><code>__init__(subclient_object)      -- Initializing instance of the VsaLiveSync class

 __str__()                      -- Returns all the Live sync pairs associated with the subclient

__repr__()                      -- Returns the string to represent the instance of the VsaLiveSync class

_get_live_sync_pairs()          -- Gets all the live sync pairs associated with the subclient

_live_sync_subtask_json()       -- Returns subtask JSON for live sync

_configure_live_sync()          -- To configure live sync

get()                           -- Returns a LiveSyncPairs object for the given live sync name

has_live_sync_pair()            -- Checks if a live sync pair exists with the given name

refresh()                       -- Refresh the live sync pairs associated with the subclient
</code></pre>
<h2 id="vsalivesync-attributes">VsaLiveSync Attributes:</h2>
<pre><code>**live_sync_pairs**     -- Returns the dictionary of all the live sync pairs and their info
</code></pre>
<h1 id="livesyncpairs">LiveSyncPairs:</h1>
<pre><code>__init__(subclient_object)      -- Initializing instance of the LiveSyncPairs class

__str__()                       -- Returns all the Live sync VM pairs associated with this live sync

__repr__()                      -- Returns the string to represent the instance of the LiveSyncPairs class

_get_live_sync_id()             -- Gets the live sync pair id associated with this subclient

_get_live_sync_vm_pairs()       -- Gets the live sync VM pairs associated with the Live sync pair

get()                           -- Returns a LiveSyncVMPair object for the given live sync VM pair name

has_vm_pair()                   -- Checks if a live sync VM pair exists with the given name

refresh()                       -- Refreshes the properties of the live sync
</code></pre>
<h2 id="livesyncpairs-attributes">LiveSyncPairs Attributes:</h2>
<pre><code>**vm_pairs**            -- Returns the dictionary of all the live sync VM pairs and their info

**live_sync_id**        -- Returns the ID of the live sync pair

**live_sync_name**      -- Returns the name of the live sync pair
</code></pre>
<h1 id="livesyncvmpair">LiveSyncVMPair:</h1>
<pre><code>__init__()              -- Initializing instance of the LiveSyncVMPair class

__repr__()              -- Returns the string to represent the instance of the LiveSyncVMPair class

_get_vm_pair_id()       -- Gets the VM pair id associated with the LiveSyncPair

_get_vm_pair_properties()   -- Gets the live sync properties for this VM pair
</code></pre>
<h2 id="livesyncvmpair-attributes">LiveSyncVMPair Attributes:</h2>
<pre><code>**vm_pair_id**          -- Returns the live sync VM pair ID

**vm_pair_name**        -- Returns the live sync VM pair name

**replication_guid**    -- Returns the replication guid of the live sync pair

**source_vm**           -- Returns the name of the source virtual machine

**destination_vm**      -- Returns the name of the destination virtual machine

**destination_client**  -- Returns the destination client of the Live sync VM pair

**destination_proxy**   -- Returns the destination proxy of the Live sync VM pair

**destination_instance**-- Returns the destination instance of the Live sync VM pair

**status**              -- Returns the status of the live sync pair

**last_synced_backup_job** -- Returns the last synced backup job ID

**latest_replication_job** -- Returns the latest replication job ID

**last_replication_job**   -- Returns the last replication job ID

**reverse_replication_schedule_id -- Returns the ID of the reverse replication schedule

**replication_group_name** -- Returns the name of the replication group associated
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/livesync/vsa_live_sync.py#L1-L823" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;File for configuring and monitoring live sync on the VSA subclient.

VsaLiveSync, LiveSyncPairs and LiveSyncVMPair are the 3 classes defined in this file.

VsaLiveSync: Class for configuring virtual server agent live sync

LiveSyncPairs: Class for all live sync pairs under a subclient

LiveSyncVMPair: Class for monitoring and configuring a Live sync VM pair


VsaLiveSync:
============

    __init__(subclient_object)      -- Initializing instance of the VsaLiveSync class

     __str__()                      -- Returns all the Live sync pairs associated with the subclient

    __repr__()                      -- Returns the string to represent the instance of the VsaLiveSync class

    _get_live_sync_pairs()          -- Gets all the live sync pairs associated with the subclient

    _live_sync_subtask_json()       -- Returns subtask JSON for live sync

    _configure_live_sync()          -- To configure live sync

    get()                           -- Returns a LiveSyncPairs object for the given live sync name

    has_live_sync_pair()            -- Checks if a live sync pair exists with the given name

    refresh()                       -- Refresh the live sync pairs associated with the subclient


VsaLiveSync Attributes:
-----------------------

    **live_sync_pairs**     -- Returns the dictionary of all the live sync pairs and their info


LiveSyncPairs:
=============

    __init__(subclient_object)      -- Initializing instance of the LiveSyncPairs class

    __str__()                       -- Returns all the Live sync VM pairs associated with this live sync

    __repr__()                      -- Returns the string to represent the instance of the LiveSyncPairs class

    _get_live_sync_id()             -- Gets the live sync pair id associated with this subclient

    _get_live_sync_vm_pairs()       -- Gets the live sync VM pairs associated with the Live sync pair

    get()                           -- Returns a LiveSyncVMPair object for the given live sync VM pair name

    has_vm_pair()                   -- Checks if a live sync VM pair exists with the given name

    refresh()                       -- Refreshes the properties of the live sync


LiveSyncPairs Attributes:
-------------------------

    **vm_pairs**            -- Returns the dictionary of all the live sync VM pairs and their info

    **live_sync_id**        -- Returns the ID of the live sync pair

    **live_sync_name**      -- Returns the name of the live sync pair


LiveSyncVMPair:
===============

    __init__()              -- Initializing instance of the LiveSyncVMPair class

    __repr__()              -- Returns the string to represent the instance of the LiveSyncVMPair class

    _get_vm_pair_id()       -- Gets the VM pair id associated with the LiveSyncPair

    _get_vm_pair_properties()   -- Gets the live sync properties for this VM pair


LiveSyncVMPair Attributes:
--------------------------

    **vm_pair_id**          -- Returns the live sync VM pair ID

    **vm_pair_name**        -- Returns the live sync VM pair name

    **replication_guid**    -- Returns the replication guid of the live sync pair

    **source_vm**           -- Returns the name of the source virtual machine

    **destination_vm**      -- Returns the name of the destination virtual machine

    **destination_client**  -- Returns the destination client of the Live sync VM pair

    **destination_proxy**   -- Returns the destination proxy of the Live sync VM pair

    **destination_instance**-- Returns the destination instance of the Live sync VM pair

    **status**              -- Returns the status of the live sync pair

    **last_synced_backup_job** -- Returns the last synced backup job ID

    **latest_replication_job** -- Returns the latest replication job ID

    **last_replication_job**   -- Returns the last replication job ID

    **reverse_replication_schedule_id -- Returns the ID of the reverse replication schedule

    **replication_group_name** -- Returns the name of the replication group associated

&#34;&#34;&#34;

import uuid

from ....constants import HypervisorType as hv_type
from ....constants import VSALiveSyncStatus as sync_status
from ....constants import VSAFailOverStatus as failover_status
from ....exception import SDKException
from ....schedules import SchedulePattern


class VsaLiveSync:
    &#34;&#34;&#34;Class for configuring and monitoring virtual server live sync operations&#34;&#34;&#34;

    def __new__(cls, subclient_object):
        &#34;&#34;&#34;Decides which instance object needs to be created&#34;&#34;&#34;
        instance_name = subclient_object._instance_object.instance_name

        if instance_name == hv_type.MS_VIRTUAL_SERVER.value.lower():
            from .hyperv_live_sync import HyperVLiveSync
            return object.__new__(HyperVLiveSync)
        if instance_name == hv_type.AZURE_V2.value.lower():
            from .azure_live_sync import AzureLiveSync
            return object.__new__(AzureLiveSync)

        if instance_name == hv_type.VIRTUAL_CENTER.value.lower():
            from .vmware_live_sync import VMWareLiveSync
            return object.__new__(VMWareLiveSync)

        if instance_name == hv_type.AMAZON_AWS.value.lower():
            from .amazon_live_sync import AmazonLiveSync
            return object.__new__(AmazonLiveSync)

        raise SDKException(
            &#39;LiveSync&#39;,
            &#39;102&#39;,
            &#39;Virtual server Live Sync for Instance: &#34;{0}&#34; is not yet supported&#39;.format(instance_name)
        )

    def __init__(self, subclient_object):
        &#34;&#34;&#34;Initializing instance of the VsaLiveSync class

        Args:
            subclient_object    (obj)   -- Instance of Subclient class

        &#34;&#34;&#34;
        self._subclient_object = subclient_object
        self._subclient_id = self._subclient_object.subclient_id
        self._subclient_name = self._subclient_object.name

        self.schedule_pattern = SchedulePattern()

        self._live_sync_pairs = None

        self._commcell_object = self._subclient_object._commcell_object
        self._cvpysdk_object = self._commcell_object._cvpysdk_object
        self._services = self._commcell_object._services
        self._update_response_ = self._commcell_object._update_response_

        self._ALL_LIVE_SYNC_PAIRS = self._services[&#39;GET_ALL_LIVE_SYNC_PAIRS&#39;] % self._subclient_id

        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all Live sync pairs of the subclient.

        Returns:
            str - string of all the live sync pairs associated with the subclient

        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\n\n&#39;.format(&#39;S. No.&#39;, &#39;LiveSyncPair&#39;)

        for index, live_sync in enumerate(self.live_sync_pairs):
            sub_str = &#39;{:^5}\t{:20}\n&#39;.format(index + 1, live_sync)
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.

        Returns:
            str - string about the details of the VSALiveSync class instance

        &#34;&#34;&#34;
        return &#39;VSALiveSync class instance for the Subclient: &#34;{0}&#34;&#39;.format(self._subclient_name)

    def _get_live_sync_pairs(self):
        &#34;&#34;&#34;Gets all the live sync pairs associated with the subclient

        Returns:
            dict    -- consists of all the live sync pairs in the subclient

                {
                    &#34;live_sync_pair1_name&#34;: {
                                &#34;id&#34;: live_sync_pair1_id
                    },
                    &#34;live_sync_pair2_name&#34;: {
                                &#34;id&#34;: live_sync_pair2_id
                    },
                }

        Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._ALL_LIVE_SYNC_PAIRS)

        if flag:
            live_sync_pairs_dict = {}
            if not bool(response.json()):
                return live_sync_pairs_dict
            if response.json() and &#39;siteInfo&#39; not in response.json():
                return live_sync_pairs_dict
            elif response.json() and &#39;siteInfo&#39; in response.json():
                for dictionary in response.json()[&#39;siteInfo&#39;]:
                    if dictionary[&#34;replicationGroup&#34;][&#34;replicationGroupName&#34;] != &#34;&#34;:
                        temp_name = dictionary[&#34;replicationGroup&#34;][&#34;replicationGroupName&#34;]
                    else:
                        temp_name = dictionary[&#39;subTask&#39;][&#39;subtaskName&#39;]
                    temp_id = str(dictionary[&#39;subTask&#39;][&#39;taskId&#39;])
                    live_sync_pairs_dict[temp_name.lower()] = {
                        &#39;id&#39;: temp_id
                    }

                return live_sync_pairs_dict

            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    @staticmethod
    def _live_sync_subtask_json(schedule_name):
        &#34;&#34;&#34;setter for the subtask in live sync JSON

        Args:
            schedule_name   (str)   -- Name of the Live sync schedule to be created

        &#34;&#34;&#34;
        return {
            &#34;subTaskType&#34;: &#34;RESTORE&#34;,
            &#34;operationType&#34;: &#34;SITE_REPLICATION&#34;,
            &#34;subTaskName&#34;: schedule_name
        }

    def _configure_live_sync(self, schedule_name, restore_options, pattern_dict=None):
        &#34;&#34;&#34;Configures Live sync after generating the full live sync json

        Args:
            schedule_name       (str)   -- Name of the Live sync schedule to be created

            restore_options     (dict)  -- Dictionary with all necessary values for Live sync

            pattern_dict        (dict)  -- Dictionary to generate the live sync schedule

                Sample:

                    for after_job_completes :
                    {
                        &#34;freq_type&#34;: &#39;after_job_completes&#39;,
                        &#34;active_start_date&#34;: date_in_%m/%d/%y (str),
                        &#34;active_start_time&#34;: time_in_%H/%S (str),
                        &#34;repeat_days&#34;: days_to_repeat (int)
                    }

                    for daily:
                    {
                         &#34;freq_type&#34;: &#39;daily&#39;,
                         &#34;active_start_time&#34;: time_in_%H/%S (str),
                         &#34;repeat_days&#34;: days_to_repeat (int)
                    }

                    for weekly:
                    {
                         &#34;freq_type&#34;: &#39;weekly&#39;,
                         &#34;active_start_time&#34;: time_in_%H/%S (str),
                         &#34;repeat_weeks&#34;: weeks_to_repeat (int)
                         &#34;weekdays&#34;: list of weekdays [&#39;Monday&#39;,&#39;Tuesday&#39;]
                    }

                    for monthly:
                    {
                         &#34;freq_type&#34;: &#39;monthly&#39;,
                         &#34;active_start_time&#34;: time_in_%H/%S (str),
                         &#34;repeat_months&#34;: weeks_to_repeat (int)
                         &#34;on_day&#34;: Day to run schedule (int)
                    }

                    for yearly:
                    {
                         &#34;active_start_time&#34;: time_in_%H/%S (str),
                         &#34;on_month&#34;: month to run schedule (str) January, Febuary...
                         &#34;on_day&#34;: Day to run schedule (int)
                    }

        Returns:
            object - instance of the Schedule class for this Live sync

        **Note** use this method to generate full Live sync JSON and to post the API call in derived class

        &#34;&#34;&#34;
        # To generate a random UUID for replication
        restore_options[&#39;replication_guid&#39;] = str(uuid.uuid1())

        request_json = self._subclient_object._prepare_fullvm_restore_json(restore_options)

        # To include the schedule pattern
        request_json = self.schedule_pattern.create_schedule(
            request_json,
            pattern_dict or {&#39;freq_type&#39;: &#39;after_job_completes&#39;})

        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;subTask&#39;] = self._live_sync_subtask_json(schedule_name)

        return self._subclient_object._process_restore_response(request_json)

    @property
    def live_sync_pairs(self):
        &#34;&#34;&#34;Returns the dictionary of all the live sync pairs and their info

        Returns:
            dict    -- consists of all the live sync pairs in the subclient

                {
                    &#34;live_sync_pair1_name&#34;: {
                                &#34;id&#34;: live_sync_pair1_id
                    },
                    &#34;live_sync_pair2_name&#34;: {
                                &#34;id&#34;: live_sync_pair2_id
                    },
                }

        &#34;&#34;&#34;
        return self._live_sync_pairs

    def get(self, live_sync_name):
        &#34;&#34;&#34;Returns a LiveSyncPairs object for the given live sync name

        Args:
             live_sync_name     (str)   -- Name of the live sync

        Returns:
            object  - Instance of the LiveSyncPairs class for the given live sync name

        Raises:
            SDKException:
                if type of the live sync name argument is not string

        &#34;&#34;&#34;
        if not isinstance(live_sync_name, str):
            raise SDKException(&#39;LiveSync&#39;, &#39;101&#39;)
        live_sync_name = live_sync_name.lower()
        if self.has_live_sync_pair(live_sync_name):
            return LiveSyncPairs(
                self._subclient_object,
                live_sync_name,
                self.live_sync_pairs[live_sync_name][&#39;id&#39;])
        raise SDKException(
            &#39;LiveSync&#39;, &#39;102&#39;, &#39;No Live Sync exists with given name: {0}&#39;.format(live_sync_name)
        )

    def has_live_sync_pair(self, live_sync_name):
        &#34;&#34;&#34;Checks if a live sync pair exists with the given name

        Args:
            live_sync_name      (str)   -- Name of the live sync

        Returns:
                bool    -   boolean output whether the live sync pair exists in the subclient or not

        Raises:
            SDKException:
                if type of the live sync name argument is not string

        &#34;&#34;&#34;
        return self.live_sync_pairs and live_sync_name.lower() in self.live_sync_pairs

    def refresh(self):
        &#34;&#34;&#34;Refresh the live sync pairs associated with the subclient&#34;&#34;&#34;
        self._live_sync_pairs = self._get_live_sync_pairs()


class LiveSyncPairs:
    &#34;&#34;&#34;Class for all live sync pairs under a subclient&#34;&#34;&#34;

    def __init__(self, subclient_object, live_sync_name, live_sync_id=None):
        &#34;&#34;&#34;Initializing instance of the LiveSyncPairs class

         Args:
            subclient_object    (obj)   -- Instance of Subclient class

            live_sync_name      (str)   -- Name of the Live sync

            live_sync_id        (str)   -- Task ID of the live sync

        &#34;&#34;&#34;
        self._subclient_object = subclient_object
        self._subclient_id = self._subclient_object.subclient_id
        self._subclient_name = self._subclient_object.name

        self._live_sync_name = live_sync_name.lower()

        self._commcell_object = self._subclient_object._commcell_object
        self._cvpysdk_object = self._commcell_object._cvpysdk_object
        self._services = self._commcell_object._services
        self._update_response_ = self._commcell_object._update_response_

        self._live_sync_id = live_sync_id or self._get_live_sync_id()

        self._LIVE_SYNC_VM_PAIRS = self._services[&#39;GET_ALL_LIVE_SYNC_VM_PAIRS&#39;] % (
            self._subclient_id,
            self.live_sync_id
        )

        self._vm_pairs = None

        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all Live sync VM pairs of the subclient.

        Returns:
            str - string of all the live sync pairs associated with the subclient

        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\n\n&#39;.format(&#39;S. No.&#39;, &#39;LiveSyncVMPair&#39;)

        for index, vm_pair in enumerate(self.vm_pairs):
            sub_str = &#39;{:^5}\t{:20}\n&#39;.format(index + 1, vm_pair)
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        representation_string = &#39;LiveSyncPairs class instance for Subclient: &#34;{0}&#34;&#39;
        return representation_string.format(self._subclient_name)

    def _get_live_sync_id(self):
        &#34;&#34;&#34;Gets the live sync pair id associated with this subclient

        Returns:
            str - id associated with this Live sync pair

        &#34;&#34;&#34;
        return self._subclient_object.live_sync.get(self.live_sync_name).live_sync_id

    def _get_live_sync_vm_pairs(self):
        &#34;&#34;&#34;Gets the live sync VM pairs associated with the live sync pair

        Returns:
            dict    -- consists of all the live sync vm pairs for the Live sync pair

                {
                    &#34;vm_pair1_name&#34;: {
                                &#34;id&#34;: vm_pair1_id
                    },
                    &#34;vm_pair2_name&#34;: {
                                &#34;id&#34;: vm_pair2_id
                    },
                }

        Raises:
            SDKException:
                if response is empty

                if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._LIVE_SYNC_VM_PAIRS)

        if flag:
            live_sync_vm_pairs = {}
            if not bool(response.json()):
                return live_sync_vm_pairs
            elif response.json() and &#39;siteInfo&#39; in response.json():
                for dictionary in response.json()[&#39;siteInfo&#39;]:
                    temp_name = dictionary[&#39;sourceName&#39;]
                    temp_id = str(dictionary[&#39;replicationId&#39;])
                    live_sync_vm_pairs[temp_name] = {
                        &#39;id&#39;: temp_id
                    }

                return live_sync_vm_pairs

            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    @property
    def vm_pairs(self):
        &#34;&#34;&#34;Returns the dictionary of all the live sync vm pairs and their info

        Returns:
            dict    -- consists of all the live sync vm pairs for the Live sync pair

                {
                    &#34;vm_pair1_name&#34;: {
                                &#34;id&#34;: vm_pair1_id
                    },
                    &#34;vm_pair2_name&#34;: {
                                &#34;id&#34;: vm_pair2_id
                    },
                }

        &#34;&#34;&#34;
        return self._vm_pairs

    def get(self, vm_pair_name):
        &#34;&#34;&#34;Returns the LiveSyncVMPair object assoicated with the subclient

        Args:
            vm_pair_name    (str)   -- Name of the vm pair

        Returns:
             object  - Instance of the LiveSyncVMPair class for the given vm pair name

        Raises:
            SDKException:
                if type of the vm pair name argument is not string

        &#34;&#34;&#34;
        if not isinstance(vm_pair_name, str):
            raise SDKException(&#39;LiveSync&#39;, &#39;101&#39;)
        if self.has_vm_pair(vm_pair_name):
            return LiveSyncVMPair(
                self,
                vm_pair_name,
                self.vm_pairs[vm_pair_name][&#39;id&#39;]
            )
        raise SDKException(
            &#39;LiveSync&#39;, &#39;102&#39;, &#39;No VM pair exists with given name: {0}&#39;.format(vm_pair_name)
        )

    def has_vm_pair(self, vm_pair_name):
        &#34;&#34;&#34;Checks if a live sync pair exists with the given name

        Args:
            vm_pair_name      (str)   -- Name of the vm pair

        Returns:
                bool    -   boolean output whether the vm pair is there in the live sync pair or not

        Raises:
            SDKException:
                if type of the live sync name argument is not string

        &#34;&#34;&#34;
        return self.vm_pairs and vm_pair_name in self.vm_pairs

    @property
    def live_sync_id(self):
        &#34;&#34;&#34;Treats the live sync id as a read-only attribute.&#34;&#34;&#34;
        return self._live_sync_id

    @property
    def live_sync_name(self):
        &#34;&#34;&#34;Treats the live sync name as a read-only attribute.&#34;&#34;&#34;
        return self._live_sync_name

    def refresh(self):
        &#34;&#34;&#34;Refreshes the VM pairs associated with the subclient&#34;&#34;&#34;
        self._vm_pairs = self._get_live_sync_vm_pairs()


class LiveSyncVMPair:
    &#34;&#34;&#34;Class for monitoring a live sync VM pair&#34;&#34;&#34;

    def __init__(self, live_sync_pair_object, vm_pair_name, vm_pair_id=None):
        &#34;&#34;&#34;Initializing instance of the LiveSyncPair class

         Args:
            live_sync_pair_object   (obj)   -- Instance of LiveSyncPairs class

            vm_pair_name            (str)   -- Name of the vm pair

            vm_pair_id              (str)   -- ID of the live sync VM pair

        &#34;&#34;&#34;
        self.live_sync_pair = live_sync_pair_object
        self._subclient_object = self.live_sync_pair._subclient_object
        self._subclient_id = self._subclient_object.subclient_id
        self._subclient_name = self._subclient_object.name

        self._vm_pair_name = vm_pair_name

        self._commcell_object = self._subclient_object._commcell_object
        self._agent_object = self._subclient_object._agent_object
        self._cvpysdk_object = self._commcell_object._cvpysdk_object
        self._services = self._commcell_object._services
        self._update_response_ = self._commcell_object._update_response_

        self._vm_pair_id = vm_pair_id or self._get_vm_pair_id()

        self._VM_PAIR = self._services[&#39;GET_LIVE_SYNC_VM_PAIR&#39;] % (
            self._subclient_id,
            self._vm_pair_id
        )

        self._properties = None
        self._replication_guid = None
        self._status = None
        self._failover_status = None
        self._source_vm = None
        self._destination_vm = None
        self._destination_client = None
        self._destination_proxy = None
        self._destination_instance = None
        self._last_backup_job = None
        self._latest_replication_job = None
        self._is_warm_sync_pair = None

        self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        representation_string = &#39;LiveSyncVMPair class instance for Live Sync: &#34;{0}&#34;&#39;
        return representation_string.format(self.live_sync_pair.live_sync_name)

    def __str__(self):
        &#34;&#34;&#34;String representation of the instance of BLR pair&#34;&#34;&#34;
        return f&#39;Live sync pair: {self._source_vm} -&gt; {self._destination_vm}&#39;

    def _get_vm_pair_id(self):
        &#34;&#34;&#34;Gets the VM pair id associated with the LiveSyncPair

        Returns:
            str - id associated with this VM pair

        &#34;&#34;&#34;
        return self.live_sync_pair.get(self.vm_pair_name).vm_pair_id

    def _get_vm_pair_properties(self):
        &#34;&#34;&#34;Gets the live sync properties for this VM pair

        Raises:
            SDKException:
                if response is empty

                if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._VM_PAIR)

        if flag:
            if not bool(response.json()):
                pass
            elif response.json() and &#39;siteInfo&#39; in response.json():
                self._properties = response.json()[&#39;siteInfo&#39;][0]
                self._replication_guid = self._properties[&#39;replicationGuid&#39;]
                self._status = self._properties[&#39;status&#39;]
                self._failover_status = self._properties[&#39;FailoverStatus&#39;]
                self._source_vm = self._properties[&#39;sourceName&#39;]
                self._destination_vm = self._properties[&#39;destinationName&#39;]
                self._destination_client = self._properties[&#39;destinationInstance&#39;].get(
                    &#39;clientName&#39;) or self._commcell_object.clients.get(
                        self._properties[&#39;destinationInstance&#39;].get(&#39;clientId&#39;)).name
                self._destination_proxy = self._properties[&#39;destProxy&#39;].get(
                    &#39;clientName&#39;) or self._commcell_object.clients.get(
                        self._properties[&#39;destProxy&#39;].get(&#39;clientId&#39;)).name
                self._destination_instance = self._properties[&#39;destinationInstance&#39;].get(
                    &#39;instanceName&#39;) or self._agent_object.instances.get(
                        self._properties[&#39;destinationInstance&#39;].get(&#39;instanceId&#39;)).name
                self._last_backup_job = self._properties[&#39;lastSyncedBkpJob&#39;]
                self._is_warm_sync_pair = self._properties.get(&#39;isWarmSyncPair&#39;, False)

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    @property
    def task_id(self):
        &#34;&#34;&#34; Returns: (int) The ID of the replication schedule task&#34;&#34;&#34;
        return self._properties.get(&#39;subTask&#39;, {}).get(&#39;taskId&#39;)

    @property
    def vm_pair_id(self):
        &#34;&#34;&#34;Treats the live sync id as a read-only attribute.&#34;&#34;&#34;
        return self._vm_pair_id

    @property
    def vm_pair_name(self):
        &#34;&#34;&#34;Treats the live sync name as a read-only attribute.&#34;&#34;&#34;
        return self._vm_pair_name

    @property
    def replication_guid(self):
        &#34;&#34;&#34;Treats the replication guid as a read-only attribute.&#34;&#34;&#34;
        return self._replication_guid

    @property
    def source_vm(self):
        &#34;&#34;&#34;Treats the source VM as a read-only attribute.&#34;&#34;&#34;
        return self._source_vm

    @property
    def source_vm_guid(self):
        &#34;&#34;&#34; Returns (str): The GUID of the source VM &#34;&#34;&#34;
        return self._properties.get(&#39;sourceGuid&#39;)

    @property
    def destination_vm(self):
        &#34;&#34;&#34;Treats the destination VM as a read-only attribute.&#34;&#34;&#34;
        return self._destination_vm

    @property
    def destination_vm_guid(self):
        &#34;&#34;&#34; Returns (str): The GUID of the destination VM &#34;&#34;&#34;
        return self._properties.get(&#39;destinationGuid&#39;)

    @property
    def destination_client(self):
        &#34;&#34;&#34;Treats the destination VM as a read-only attribute.&#34;&#34;&#34;
        return self._destination_client

    @property
    def destination_proxy(self):
        &#34;&#34;&#34;Treats the destination VM as a read-only attribute.&#34;&#34;&#34;
        return self._destination_proxy

    @property
    def destination_instance(self):
        &#34;&#34;&#34;Treats the destination instance as a read-only attribute.&#34;&#34;&#34;
        return self._destination_instance

    @property
    def status(self):
        &#34;&#34;&#34;Treats the status as a read-only attribute.&#34;&#34;&#34;
        return sync_status(self._status).name

    @property
    def failover_status(self):
        &#34;&#34;&#34;Treats the failover_status as a read-only attribute.&#34;&#34;&#34;
        return failover_status(self._failover_status).name

    @property
    def last_synced_backup_job(self):
        &#34;&#34;&#34;Treats the synced backup job as a read-only attribute.&#34;&#34;&#34;
        return self._last_backup_job

    @property
    def last_replication_job(self):
        &#34;&#34;&#34;Returns (int): the last replication job that has been run for the Live sync VM pair&#34;&#34;&#34;
        for prop in self._properties.get(&#39;VMReplInfoProperties&#39;, []):
            if prop.get(&#39;propertyId&#39;, 0) == 2215 and prop.get(&#39;propertyValue&#39;):
                return int(prop.get(&#39;propertyValue&#39;))
        return None

    @property
    def latest_replication_job(self):
        &#34;&#34;&#34;Returns (int): the latest successful replication job for the Live sync VM pair&#34;&#34;&#34;
        for prop in self._properties.get(&#39;VMReplInfoProperties&#39;, []):
            if prop.get(&#39;propertyId&#39;, 0) == 2208 and prop.get(&#39;propertyValue&#39;):
                self._latest_replication_job = int(prop.get(&#39;propertyValue&#39;))
                return self._latest_replication_job
        return None

    @property
    def failover_job_id(self):
        &#34;&#34;&#34;Returns (int): the job ID of the failover job&#34;&#34;&#34;
        for prop in self._properties.get(&#39;VMReplInfoProperties&#39;, []):
            if prop.get(&#39;propertyId&#39;, 0) == 2216 and prop.get(&#39;propertyValue&#39;):
                return int(prop.get(&#39;propertyValue&#39;))
        return None

    @property
    def reverse_replication_schedule_id(self):
        &#34;&#34;&#34;Returns (int): The schedule ID of the reverse replication schedule&#34;&#34;&#34;
        for prop in self._properties.get(&#39;VMReplInfoProperties&#39;, []):
            if prop.get(&#39;propertyId&#39;, 0) == 2212 and prop.get(&#39;propertyValue&#39;):
                return int(prop.get(&#39;propertyValue&#39;))
        return None

    @property
    def replication_group_name(self):
        &#34;&#34;&#34;Returns (str): The name of the replication group associated to the VM pair
        Note: This also removes the CV prefix for new replication groups
        &#34;&#34;&#34;
        group_name = (self._properties.get(&#39;replicationGroup&#39;, {}).get(&#39;replicationGroupName&#39;)
                      or self._properties.get(&#39;subTask&#39;, {}).get(&#39;subTaskName&#39;))
        return group_name.replace(&#39;_ReplicationPlan__ReplicationGroup&#39;, &#39;&#39;)

    @property
    def is_warm_sync_pair(self):
        &#34;&#34;&#34;Returns (bool): Warm Sync enabled/disabled&#34;&#34;&#34;
        return self._is_warm_sync_pair

    def refresh(self):
        &#34;&#34;&#34;Refreshes the properties of the live sync&#34;&#34;&#34;
        self._get_vm_pair_properties()</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncPairs"><code class="flex name class">
<span>class <span class="ident">LiveSyncPairs</span></span>
<span>(</span><span>subclient_object, live_sync_name, live_sync_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for all live sync pairs under a subclient</p>
<p>Initializing instance of the LiveSyncPairs class</p>
<h2 id="args">Args</h2>
<p>subclient_object
(obj)
&ndash; Instance of Subclient class</p>
<p>live_sync_name
(str)
&ndash; Name of the Live sync</p>
<p>live_sync_id
(str)
&ndash; Task ID of the live sync</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/livesync/vsa_live_sync.py#L415-L595" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class LiveSyncPairs:
    &#34;&#34;&#34;Class for all live sync pairs under a subclient&#34;&#34;&#34;

    def __init__(self, subclient_object, live_sync_name, live_sync_id=None):
        &#34;&#34;&#34;Initializing instance of the LiveSyncPairs class

         Args:
            subclient_object    (obj)   -- Instance of Subclient class

            live_sync_name      (str)   -- Name of the Live sync

            live_sync_id        (str)   -- Task ID of the live sync

        &#34;&#34;&#34;
        self._subclient_object = subclient_object
        self._subclient_id = self._subclient_object.subclient_id
        self._subclient_name = self._subclient_object.name

        self._live_sync_name = live_sync_name.lower()

        self._commcell_object = self._subclient_object._commcell_object
        self._cvpysdk_object = self._commcell_object._cvpysdk_object
        self._services = self._commcell_object._services
        self._update_response_ = self._commcell_object._update_response_

        self._live_sync_id = live_sync_id or self._get_live_sync_id()

        self._LIVE_SYNC_VM_PAIRS = self._services[&#39;GET_ALL_LIVE_SYNC_VM_PAIRS&#39;] % (
            self._subclient_id,
            self.live_sync_id
        )

        self._vm_pairs = None

        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all Live sync VM pairs of the subclient.

        Returns:
            str - string of all the live sync pairs associated with the subclient

        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\n\n&#39;.format(&#39;S. No.&#39;, &#39;LiveSyncVMPair&#39;)

        for index, vm_pair in enumerate(self.vm_pairs):
            sub_str = &#39;{:^5}\t{:20}\n&#39;.format(index + 1, vm_pair)
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        representation_string = &#39;LiveSyncPairs class instance for Subclient: &#34;{0}&#34;&#39;
        return representation_string.format(self._subclient_name)

    def _get_live_sync_id(self):
        &#34;&#34;&#34;Gets the live sync pair id associated with this subclient

        Returns:
            str - id associated with this Live sync pair

        &#34;&#34;&#34;
        return self._subclient_object.live_sync.get(self.live_sync_name).live_sync_id

    def _get_live_sync_vm_pairs(self):
        &#34;&#34;&#34;Gets the live sync VM pairs associated with the live sync pair

        Returns:
            dict    -- consists of all the live sync vm pairs for the Live sync pair

                {
                    &#34;vm_pair1_name&#34;: {
                                &#34;id&#34;: vm_pair1_id
                    },
                    &#34;vm_pair2_name&#34;: {
                                &#34;id&#34;: vm_pair2_id
                    },
                }

        Raises:
            SDKException:
                if response is empty

                if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._LIVE_SYNC_VM_PAIRS)

        if flag:
            live_sync_vm_pairs = {}
            if not bool(response.json()):
                return live_sync_vm_pairs
            elif response.json() and &#39;siteInfo&#39; in response.json():
                for dictionary in response.json()[&#39;siteInfo&#39;]:
                    temp_name = dictionary[&#39;sourceName&#39;]
                    temp_id = str(dictionary[&#39;replicationId&#39;])
                    live_sync_vm_pairs[temp_name] = {
                        &#39;id&#39;: temp_id
                    }

                return live_sync_vm_pairs

            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    @property
    def vm_pairs(self):
        &#34;&#34;&#34;Returns the dictionary of all the live sync vm pairs and their info

        Returns:
            dict    -- consists of all the live sync vm pairs for the Live sync pair

                {
                    &#34;vm_pair1_name&#34;: {
                                &#34;id&#34;: vm_pair1_id
                    },
                    &#34;vm_pair2_name&#34;: {
                                &#34;id&#34;: vm_pair2_id
                    },
                }

        &#34;&#34;&#34;
        return self._vm_pairs

    def get(self, vm_pair_name):
        &#34;&#34;&#34;Returns the LiveSyncVMPair object assoicated with the subclient

        Args:
            vm_pair_name    (str)   -- Name of the vm pair

        Returns:
             object  - Instance of the LiveSyncVMPair class for the given vm pair name

        Raises:
            SDKException:
                if type of the vm pair name argument is not string

        &#34;&#34;&#34;
        if not isinstance(vm_pair_name, str):
            raise SDKException(&#39;LiveSync&#39;, &#39;101&#39;)
        if self.has_vm_pair(vm_pair_name):
            return LiveSyncVMPair(
                self,
                vm_pair_name,
                self.vm_pairs[vm_pair_name][&#39;id&#39;]
            )
        raise SDKException(
            &#39;LiveSync&#39;, &#39;102&#39;, &#39;No VM pair exists with given name: {0}&#39;.format(vm_pair_name)
        )

    def has_vm_pair(self, vm_pair_name):
        &#34;&#34;&#34;Checks if a live sync pair exists with the given name

        Args:
            vm_pair_name      (str)   -- Name of the vm pair

        Returns:
                bool    -   boolean output whether the vm pair is there in the live sync pair or not

        Raises:
            SDKException:
                if type of the live sync name argument is not string

        &#34;&#34;&#34;
        return self.vm_pairs and vm_pair_name in self.vm_pairs

    @property
    def live_sync_id(self):
        &#34;&#34;&#34;Treats the live sync id as a read-only attribute.&#34;&#34;&#34;
        return self._live_sync_id

    @property
    def live_sync_name(self):
        &#34;&#34;&#34;Treats the live sync name as a read-only attribute.&#34;&#34;&#34;
        return self._live_sync_name

    def refresh(self):
        &#34;&#34;&#34;Refreshes the VM pairs associated with the subclient&#34;&#34;&#34;
        self._vm_pairs = self._get_live_sync_vm_pairs()</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncPairs.live_sync_id"><code class="name">var <span class="ident">live_sync_id</span></code></dt>
<dd>
<div class="desc"><p>Treats the live sync id as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/livesync/vsa_live_sync.py#L583-L586" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def live_sync_id(self):
    &#34;&#34;&#34;Treats the live sync id as a read-only attribute.&#34;&#34;&#34;
    return self._live_sync_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncPairs.live_sync_name"><code class="name">var <span class="ident">live_sync_name</span></code></dt>
<dd>
<div class="desc"><p>Treats the live sync name as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/livesync/vsa_live_sync.py#L588-L591" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def live_sync_name(self):
    &#34;&#34;&#34;Treats the live sync name as a read-only attribute.&#34;&#34;&#34;
    return self._live_sync_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncPairs.vm_pairs"><code class="name">var <span class="ident">vm_pairs</span></code></dt>
<dd>
<div class="desc"><p>Returns the dictionary of all the live sync vm pairs and their info</p>
<h2 id="returns">Returns</h2>
<p>dict
&ndash; consists of all the live sync vm pairs for the Live sync pair</p>
<pre><code>{
    "vm_pair1_name": {
                "id": vm_pair1_id
    },
    "vm_pair2_name": {
                "id": vm_pair2_id
    },
}
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/livesync/vsa_live_sync.py#L522-L539" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def vm_pairs(self):
    &#34;&#34;&#34;Returns the dictionary of all the live sync vm pairs and their info

    Returns:
        dict    -- consists of all the live sync vm pairs for the Live sync pair

            {
                &#34;vm_pair1_name&#34;: {
                            &#34;id&#34;: vm_pair1_id
                },
                &#34;vm_pair2_name&#34;: {
                            &#34;id&#34;: vm_pair2_id
                },
            }

    &#34;&#34;&#34;
    return self._vm_pairs</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncPairs.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, vm_pair_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the LiveSyncVMPair object assoicated with the subclient</p>
<h2 id="args">Args</h2>
<p>vm_pair_name
(str)
&ndash; Name of the vm pair</p>
<h2 id="returns">Returns</h2>
<p>object
- Instance of the LiveSyncVMPair class for the given vm pair name</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the vm pair name argument is not string</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/livesync/vsa_live_sync.py#L541-L565" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, vm_pair_name):
    &#34;&#34;&#34;Returns the LiveSyncVMPair object assoicated with the subclient

    Args:
        vm_pair_name    (str)   -- Name of the vm pair

    Returns:
         object  - Instance of the LiveSyncVMPair class for the given vm pair name

    Raises:
        SDKException:
            if type of the vm pair name argument is not string

    &#34;&#34;&#34;
    if not isinstance(vm_pair_name, str):
        raise SDKException(&#39;LiveSync&#39;, &#39;101&#39;)
    if self.has_vm_pair(vm_pair_name):
        return LiveSyncVMPair(
            self,
            vm_pair_name,
            self.vm_pairs[vm_pair_name][&#39;id&#39;]
        )
    raise SDKException(
        &#39;LiveSync&#39;, &#39;102&#39;, &#39;No VM pair exists with given name: {0}&#39;.format(vm_pair_name)
    )</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncPairs.has_vm_pair"><code class="name flex">
<span>def <span class="ident">has_vm_pair</span></span>(<span>self, vm_pair_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks if a live sync pair exists with the given name</p>
<h2 id="args">Args</h2>
<p>vm_pair_name
(str)
&ndash; Name of the vm pair</p>
<h2 id="returns">Returns</h2>
<p>bool
-
boolean output whether the vm pair is there in the live sync pair or not</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the live sync name argument is not string</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/livesync/vsa_live_sync.py#L567-L581" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_vm_pair(self, vm_pair_name):
    &#34;&#34;&#34;Checks if a live sync pair exists with the given name

    Args:
        vm_pair_name      (str)   -- Name of the vm pair

    Returns:
            bool    -   boolean output whether the vm pair is there in the live sync pair or not

    Raises:
        SDKException:
            if type of the live sync name argument is not string

    &#34;&#34;&#34;
    return self.vm_pairs and vm_pair_name in self.vm_pairs</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncPairs.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refreshes the VM pairs associated with the subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/livesync/vsa_live_sync.py#L593-L595" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refreshes the VM pairs associated with the subclient&#34;&#34;&#34;
    self._vm_pairs = self._get_live_sync_vm_pairs()</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair"><code class="flex name class">
<span>class <span class="ident">LiveSyncVMPair</span></span>
<span>(</span><span>live_sync_pair_object, vm_pair_name, vm_pair_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for monitoring a live sync VM pair</p>
<p>Initializing instance of the LiveSyncPair class</p>
<h2 id="args">Args</h2>
<p>live_sync_pair_object
(obj)
&ndash; Instance of LiveSyncPairs class</p>
<p>vm_pair_name
(str)
&ndash; Name of the vm pair</p>
<p>vm_pair_id
(str)
&ndash; ID of the live sync VM pair</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/livesync/vsa_live_sync.py#L598-L823" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class LiveSyncVMPair:
    &#34;&#34;&#34;Class for monitoring a live sync VM pair&#34;&#34;&#34;

    def __init__(self, live_sync_pair_object, vm_pair_name, vm_pair_id=None):
        &#34;&#34;&#34;Initializing instance of the LiveSyncPair class

         Args:
            live_sync_pair_object   (obj)   -- Instance of LiveSyncPairs class

            vm_pair_name            (str)   -- Name of the vm pair

            vm_pair_id              (str)   -- ID of the live sync VM pair

        &#34;&#34;&#34;
        self.live_sync_pair = live_sync_pair_object
        self._subclient_object = self.live_sync_pair._subclient_object
        self._subclient_id = self._subclient_object.subclient_id
        self._subclient_name = self._subclient_object.name

        self._vm_pair_name = vm_pair_name

        self._commcell_object = self._subclient_object._commcell_object
        self._agent_object = self._subclient_object._agent_object
        self._cvpysdk_object = self._commcell_object._cvpysdk_object
        self._services = self._commcell_object._services
        self._update_response_ = self._commcell_object._update_response_

        self._vm_pair_id = vm_pair_id or self._get_vm_pair_id()

        self._VM_PAIR = self._services[&#39;GET_LIVE_SYNC_VM_PAIR&#39;] % (
            self._subclient_id,
            self._vm_pair_id
        )

        self._properties = None
        self._replication_guid = None
        self._status = None
        self._failover_status = None
        self._source_vm = None
        self._destination_vm = None
        self._destination_client = None
        self._destination_proxy = None
        self._destination_instance = None
        self._last_backup_job = None
        self._latest_replication_job = None
        self._is_warm_sync_pair = None

        self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        representation_string = &#39;LiveSyncVMPair class instance for Live Sync: &#34;{0}&#34;&#39;
        return representation_string.format(self.live_sync_pair.live_sync_name)

    def __str__(self):
        &#34;&#34;&#34;String representation of the instance of BLR pair&#34;&#34;&#34;
        return f&#39;Live sync pair: {self._source_vm} -&gt; {self._destination_vm}&#39;

    def _get_vm_pair_id(self):
        &#34;&#34;&#34;Gets the VM pair id associated with the LiveSyncPair

        Returns:
            str - id associated with this VM pair

        &#34;&#34;&#34;
        return self.live_sync_pair.get(self.vm_pair_name).vm_pair_id

    def _get_vm_pair_properties(self):
        &#34;&#34;&#34;Gets the live sync properties for this VM pair

        Raises:
            SDKException:
                if response is empty

                if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._VM_PAIR)

        if flag:
            if not bool(response.json()):
                pass
            elif response.json() and &#39;siteInfo&#39; in response.json():
                self._properties = response.json()[&#39;siteInfo&#39;][0]
                self._replication_guid = self._properties[&#39;replicationGuid&#39;]
                self._status = self._properties[&#39;status&#39;]
                self._failover_status = self._properties[&#39;FailoverStatus&#39;]
                self._source_vm = self._properties[&#39;sourceName&#39;]
                self._destination_vm = self._properties[&#39;destinationName&#39;]
                self._destination_client = self._properties[&#39;destinationInstance&#39;].get(
                    &#39;clientName&#39;) or self._commcell_object.clients.get(
                        self._properties[&#39;destinationInstance&#39;].get(&#39;clientId&#39;)).name
                self._destination_proxy = self._properties[&#39;destProxy&#39;].get(
                    &#39;clientName&#39;) or self._commcell_object.clients.get(
                        self._properties[&#39;destProxy&#39;].get(&#39;clientId&#39;)).name
                self._destination_instance = self._properties[&#39;destinationInstance&#39;].get(
                    &#39;instanceName&#39;) or self._agent_object.instances.get(
                        self._properties[&#39;destinationInstance&#39;].get(&#39;instanceId&#39;)).name
                self._last_backup_job = self._properties[&#39;lastSyncedBkpJob&#39;]
                self._is_warm_sync_pair = self._properties.get(&#39;isWarmSyncPair&#39;, False)

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    @property
    def task_id(self):
        &#34;&#34;&#34; Returns: (int) The ID of the replication schedule task&#34;&#34;&#34;
        return self._properties.get(&#39;subTask&#39;, {}).get(&#39;taskId&#39;)

    @property
    def vm_pair_id(self):
        &#34;&#34;&#34;Treats the live sync id as a read-only attribute.&#34;&#34;&#34;
        return self._vm_pair_id

    @property
    def vm_pair_name(self):
        &#34;&#34;&#34;Treats the live sync name as a read-only attribute.&#34;&#34;&#34;
        return self._vm_pair_name

    @property
    def replication_guid(self):
        &#34;&#34;&#34;Treats the replication guid as a read-only attribute.&#34;&#34;&#34;
        return self._replication_guid

    @property
    def source_vm(self):
        &#34;&#34;&#34;Treats the source VM as a read-only attribute.&#34;&#34;&#34;
        return self._source_vm

    @property
    def source_vm_guid(self):
        &#34;&#34;&#34; Returns (str): The GUID of the source VM &#34;&#34;&#34;
        return self._properties.get(&#39;sourceGuid&#39;)

    @property
    def destination_vm(self):
        &#34;&#34;&#34;Treats the destination VM as a read-only attribute.&#34;&#34;&#34;
        return self._destination_vm

    @property
    def destination_vm_guid(self):
        &#34;&#34;&#34; Returns (str): The GUID of the destination VM &#34;&#34;&#34;
        return self._properties.get(&#39;destinationGuid&#39;)

    @property
    def destination_client(self):
        &#34;&#34;&#34;Treats the destination VM as a read-only attribute.&#34;&#34;&#34;
        return self._destination_client

    @property
    def destination_proxy(self):
        &#34;&#34;&#34;Treats the destination VM as a read-only attribute.&#34;&#34;&#34;
        return self._destination_proxy

    @property
    def destination_instance(self):
        &#34;&#34;&#34;Treats the destination instance as a read-only attribute.&#34;&#34;&#34;
        return self._destination_instance

    @property
    def status(self):
        &#34;&#34;&#34;Treats the status as a read-only attribute.&#34;&#34;&#34;
        return sync_status(self._status).name

    @property
    def failover_status(self):
        &#34;&#34;&#34;Treats the failover_status as a read-only attribute.&#34;&#34;&#34;
        return failover_status(self._failover_status).name

    @property
    def last_synced_backup_job(self):
        &#34;&#34;&#34;Treats the synced backup job as a read-only attribute.&#34;&#34;&#34;
        return self._last_backup_job

    @property
    def last_replication_job(self):
        &#34;&#34;&#34;Returns (int): the last replication job that has been run for the Live sync VM pair&#34;&#34;&#34;
        for prop in self._properties.get(&#39;VMReplInfoProperties&#39;, []):
            if prop.get(&#39;propertyId&#39;, 0) == 2215 and prop.get(&#39;propertyValue&#39;):
                return int(prop.get(&#39;propertyValue&#39;))
        return None

    @property
    def latest_replication_job(self):
        &#34;&#34;&#34;Returns (int): the latest successful replication job for the Live sync VM pair&#34;&#34;&#34;
        for prop in self._properties.get(&#39;VMReplInfoProperties&#39;, []):
            if prop.get(&#39;propertyId&#39;, 0) == 2208 and prop.get(&#39;propertyValue&#39;):
                self._latest_replication_job = int(prop.get(&#39;propertyValue&#39;))
                return self._latest_replication_job
        return None

    @property
    def failover_job_id(self):
        &#34;&#34;&#34;Returns (int): the job ID of the failover job&#34;&#34;&#34;
        for prop in self._properties.get(&#39;VMReplInfoProperties&#39;, []):
            if prop.get(&#39;propertyId&#39;, 0) == 2216 and prop.get(&#39;propertyValue&#39;):
                return int(prop.get(&#39;propertyValue&#39;))
        return None

    @property
    def reverse_replication_schedule_id(self):
        &#34;&#34;&#34;Returns (int): The schedule ID of the reverse replication schedule&#34;&#34;&#34;
        for prop in self._properties.get(&#39;VMReplInfoProperties&#39;, []):
            if prop.get(&#39;propertyId&#39;, 0) == 2212 and prop.get(&#39;propertyValue&#39;):
                return int(prop.get(&#39;propertyValue&#39;))
        return None

    @property
    def replication_group_name(self):
        &#34;&#34;&#34;Returns (str): The name of the replication group associated to the VM pair
        Note: This also removes the CV prefix for new replication groups
        &#34;&#34;&#34;
        group_name = (self._properties.get(&#39;replicationGroup&#39;, {}).get(&#39;replicationGroupName&#39;)
                      or self._properties.get(&#39;subTask&#39;, {}).get(&#39;subTaskName&#39;))
        return group_name.replace(&#39;_ReplicationPlan__ReplicationGroup&#39;, &#39;&#39;)

    @property
    def is_warm_sync_pair(self):
        &#34;&#34;&#34;Returns (bool): Warm Sync enabled/disabled&#34;&#34;&#34;
        return self._is_warm_sync_pair

    def refresh(self):
        &#34;&#34;&#34;Refreshes the properties of the live sync&#34;&#34;&#34;
        self._get_vm_pair_properties()</code></pre>
</details>
<h3>Subclasses</h3>
<ul class="hlist">
<li><a title="cvpysdk.drorchestration.replication_pairs.ReplicationPair" href="../../../drorchestration/replication_pairs.html#cvpysdk.drorchestration.replication_pairs.ReplicationPair">ReplicationPair</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.destination_client"><code class="name">var <span class="ident">destination_client</span></code></dt>
<dd>
<div class="desc"><p>Treats the destination VM as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/livesync/vsa_live_sync.py#L744-L747" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def destination_client(self):
    &#34;&#34;&#34;Treats the destination VM as a read-only attribute.&#34;&#34;&#34;
    return self._destination_client</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.destination_instance"><code class="name">var <span class="ident">destination_instance</span></code></dt>
<dd>
<div class="desc"><p>Treats the destination instance as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/livesync/vsa_live_sync.py#L754-L757" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def destination_instance(self):
    &#34;&#34;&#34;Treats the destination instance as a read-only attribute.&#34;&#34;&#34;
    return self._destination_instance</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.destination_proxy"><code class="name">var <span class="ident">destination_proxy</span></code></dt>
<dd>
<div class="desc"><p>Treats the destination VM as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/livesync/vsa_live_sync.py#L749-L752" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def destination_proxy(self):
    &#34;&#34;&#34;Treats the destination VM as a read-only attribute.&#34;&#34;&#34;
    return self._destination_proxy</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.destination_vm"><code class="name">var <span class="ident">destination_vm</span></code></dt>
<dd>
<div class="desc"><p>Treats the destination VM as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/livesync/vsa_live_sync.py#L734-L737" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def destination_vm(self):
    &#34;&#34;&#34;Treats the destination VM as a read-only attribute.&#34;&#34;&#34;
    return self._destination_vm</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.destination_vm_guid"><code class="name">var <span class="ident">destination_vm_guid</span></code></dt>
<dd>
<div class="desc"><p>Returns (str): The GUID of the destination VM</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/livesync/vsa_live_sync.py#L739-L742" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def destination_vm_guid(self):
    &#34;&#34;&#34; Returns (str): The GUID of the destination VM &#34;&#34;&#34;
    return self._properties.get(&#39;destinationGuid&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.failover_job_id"><code class="name">var <span class="ident">failover_job_id</span></code></dt>
<dd>
<div class="desc"><p>Returns (int): the job ID of the failover job</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/livesync/vsa_live_sync.py#L791-L797" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def failover_job_id(self):
    &#34;&#34;&#34;Returns (int): the job ID of the failover job&#34;&#34;&#34;
    for prop in self._properties.get(&#39;VMReplInfoProperties&#39;, []):
        if prop.get(&#39;propertyId&#39;, 0) == 2216 and prop.get(&#39;propertyValue&#39;):
            return int(prop.get(&#39;propertyValue&#39;))
    return None</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.failover_status"><code class="name">var <span class="ident">failover_status</span></code></dt>
<dd>
<div class="desc"><p>Treats the failover_status as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/livesync/vsa_live_sync.py#L764-L767" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def failover_status(self):
    &#34;&#34;&#34;Treats the failover_status as a read-only attribute.&#34;&#34;&#34;
    return failover_status(self._failover_status).name</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.is_warm_sync_pair"><code class="name">var <span class="ident">is_warm_sync_pair</span></code></dt>
<dd>
<div class="desc"><p>Returns (bool): Warm Sync enabled/disabled</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/livesync/vsa_live_sync.py#L816-L819" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_warm_sync_pair(self):
    &#34;&#34;&#34;Returns (bool): Warm Sync enabled/disabled&#34;&#34;&#34;
    return self._is_warm_sync_pair</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.last_replication_job"><code class="name">var <span class="ident">last_replication_job</span></code></dt>
<dd>
<div class="desc"><p>Returns (int): the last replication job that has been run for the Live sync VM pair</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/livesync/vsa_live_sync.py#L774-L780" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def last_replication_job(self):
    &#34;&#34;&#34;Returns (int): the last replication job that has been run for the Live sync VM pair&#34;&#34;&#34;
    for prop in self._properties.get(&#39;VMReplInfoProperties&#39;, []):
        if prop.get(&#39;propertyId&#39;, 0) == 2215 and prop.get(&#39;propertyValue&#39;):
            return int(prop.get(&#39;propertyValue&#39;))
    return None</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.last_synced_backup_job"><code class="name">var <span class="ident">last_synced_backup_job</span></code></dt>
<dd>
<div class="desc"><p>Treats the synced backup job as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/livesync/vsa_live_sync.py#L769-L772" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def last_synced_backup_job(self):
    &#34;&#34;&#34;Treats the synced backup job as a read-only attribute.&#34;&#34;&#34;
    return self._last_backup_job</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.latest_replication_job"><code class="name">var <span class="ident">latest_replication_job</span></code></dt>
<dd>
<div class="desc"><p>Returns (int): the latest successful replication job for the Live sync VM pair</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/livesync/vsa_live_sync.py#L782-L789" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def latest_replication_job(self):
    &#34;&#34;&#34;Returns (int): the latest successful replication job for the Live sync VM pair&#34;&#34;&#34;
    for prop in self._properties.get(&#39;VMReplInfoProperties&#39;, []):
        if prop.get(&#39;propertyId&#39;, 0) == 2208 and prop.get(&#39;propertyValue&#39;):
            self._latest_replication_job = int(prop.get(&#39;propertyValue&#39;))
            return self._latest_replication_job
    return None</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.replication_group_name"><code class="name">var <span class="ident">replication_group_name</span></code></dt>
<dd>
<div class="desc"><p>Returns (str): The name of the replication group associated to the VM pair
Note: This also removes the CV prefix for new replication groups</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/livesync/vsa_live_sync.py#L807-L814" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def replication_group_name(self):
    &#34;&#34;&#34;Returns (str): The name of the replication group associated to the VM pair
    Note: This also removes the CV prefix for new replication groups
    &#34;&#34;&#34;
    group_name = (self._properties.get(&#39;replicationGroup&#39;, {}).get(&#39;replicationGroupName&#39;)
                  or self._properties.get(&#39;subTask&#39;, {}).get(&#39;subTaskName&#39;))
    return group_name.replace(&#39;_ReplicationPlan__ReplicationGroup&#39;, &#39;&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.replication_guid"><code class="name">var <span class="ident">replication_guid</span></code></dt>
<dd>
<div class="desc"><p>Treats the replication guid as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/livesync/vsa_live_sync.py#L719-L722" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def replication_guid(self):
    &#34;&#34;&#34;Treats the replication guid as a read-only attribute.&#34;&#34;&#34;
    return self._replication_guid</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.reverse_replication_schedule_id"><code class="name">var <span class="ident">reverse_replication_schedule_id</span></code></dt>
<dd>
<div class="desc"><p>Returns (int): The schedule ID of the reverse replication schedule</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/livesync/vsa_live_sync.py#L799-L805" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def reverse_replication_schedule_id(self):
    &#34;&#34;&#34;Returns (int): The schedule ID of the reverse replication schedule&#34;&#34;&#34;
    for prop in self._properties.get(&#39;VMReplInfoProperties&#39;, []):
        if prop.get(&#39;propertyId&#39;, 0) == 2212 and prop.get(&#39;propertyValue&#39;):
            return int(prop.get(&#39;propertyValue&#39;))
    return None</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.source_vm"><code class="name">var <span class="ident">source_vm</span></code></dt>
<dd>
<div class="desc"><p>Treats the source VM as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/livesync/vsa_live_sync.py#L724-L727" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def source_vm(self):
    &#34;&#34;&#34;Treats the source VM as a read-only attribute.&#34;&#34;&#34;
    return self._source_vm</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.source_vm_guid"><code class="name">var <span class="ident">source_vm_guid</span></code></dt>
<dd>
<div class="desc"><p>Returns (str): The GUID of the source VM</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/livesync/vsa_live_sync.py#L729-L732" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def source_vm_guid(self):
    &#34;&#34;&#34; Returns (str): The GUID of the source VM &#34;&#34;&#34;
    return self._properties.get(&#39;sourceGuid&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.status"><code class="name">var <span class="ident">status</span></code></dt>
<dd>
<div class="desc"><p>Treats the status as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/livesync/vsa_live_sync.py#L759-L762" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def status(self):
    &#34;&#34;&#34;Treats the status as a read-only attribute.&#34;&#34;&#34;
    return sync_status(self._status).name</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.task_id"><code class="name">var <span class="ident">task_id</span></code></dt>
<dd>
<div class="desc"><p>Returns: (int) The ID of the replication schedule task</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/livesync/vsa_live_sync.py#L704-L707" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def task_id(self):
    &#34;&#34;&#34; Returns: (int) The ID of the replication schedule task&#34;&#34;&#34;
    return self._properties.get(&#39;subTask&#39;, {}).get(&#39;taskId&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.vm_pair_id"><code class="name">var <span class="ident">vm_pair_id</span></code></dt>
<dd>
<div class="desc"><p>Treats the live sync id as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/livesync/vsa_live_sync.py#L709-L712" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def vm_pair_id(self):
    &#34;&#34;&#34;Treats the live sync id as a read-only attribute.&#34;&#34;&#34;
    return self._vm_pair_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.vm_pair_name"><code class="name">var <span class="ident">vm_pair_name</span></code></dt>
<dd>
<div class="desc"><p>Treats the live sync name as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/livesync/vsa_live_sync.py#L714-L717" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def vm_pair_name(self):
    &#34;&#34;&#34;Treats the live sync name as a read-only attribute.&#34;&#34;&#34;
    return self._vm_pair_name</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refreshes the properties of the live sync</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/livesync/vsa_live_sync.py#L821-L823" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refreshes the properties of the live sync&#34;&#34;&#34;
    self._get_vm_pair_properties()</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.VsaLiveSync"><code class="flex name class">
<span>class <span class="ident">VsaLiveSync</span></span>
<span>(</span><span>subclient_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for configuring and monitoring virtual server live sync operations</p>
<p>Initializing instance of the VsaLiveSync class</p>
<h2 id="args">Args</h2>
<p>subclient_object
(obj)
&ndash; Instance of Subclient class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/livesync/vsa_live_sync.py#L142-L412" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class VsaLiveSync:
    &#34;&#34;&#34;Class for configuring and monitoring virtual server live sync operations&#34;&#34;&#34;

    def __new__(cls, subclient_object):
        &#34;&#34;&#34;Decides which instance object needs to be created&#34;&#34;&#34;
        instance_name = subclient_object._instance_object.instance_name

        if instance_name == hv_type.MS_VIRTUAL_SERVER.value.lower():
            from .hyperv_live_sync import HyperVLiveSync
            return object.__new__(HyperVLiveSync)
        if instance_name == hv_type.AZURE_V2.value.lower():
            from .azure_live_sync import AzureLiveSync
            return object.__new__(AzureLiveSync)

        if instance_name == hv_type.VIRTUAL_CENTER.value.lower():
            from .vmware_live_sync import VMWareLiveSync
            return object.__new__(VMWareLiveSync)

        if instance_name == hv_type.AMAZON_AWS.value.lower():
            from .amazon_live_sync import AmazonLiveSync
            return object.__new__(AmazonLiveSync)

        raise SDKException(
            &#39;LiveSync&#39;,
            &#39;102&#39;,
            &#39;Virtual server Live Sync for Instance: &#34;{0}&#34; is not yet supported&#39;.format(instance_name)
        )

    def __init__(self, subclient_object):
        &#34;&#34;&#34;Initializing instance of the VsaLiveSync class

        Args:
            subclient_object    (obj)   -- Instance of Subclient class

        &#34;&#34;&#34;
        self._subclient_object = subclient_object
        self._subclient_id = self._subclient_object.subclient_id
        self._subclient_name = self._subclient_object.name

        self.schedule_pattern = SchedulePattern()

        self._live_sync_pairs = None

        self._commcell_object = self._subclient_object._commcell_object
        self._cvpysdk_object = self._commcell_object._cvpysdk_object
        self._services = self._commcell_object._services
        self._update_response_ = self._commcell_object._update_response_

        self._ALL_LIVE_SYNC_PAIRS = self._services[&#39;GET_ALL_LIVE_SYNC_PAIRS&#39;] % self._subclient_id

        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all Live sync pairs of the subclient.

        Returns:
            str - string of all the live sync pairs associated with the subclient

        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\n\n&#39;.format(&#39;S. No.&#39;, &#39;LiveSyncPair&#39;)

        for index, live_sync in enumerate(self.live_sync_pairs):
            sub_str = &#39;{:^5}\t{:20}\n&#39;.format(index + 1, live_sync)
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.

        Returns:
            str - string about the details of the VSALiveSync class instance

        &#34;&#34;&#34;
        return &#39;VSALiveSync class instance for the Subclient: &#34;{0}&#34;&#39;.format(self._subclient_name)

    def _get_live_sync_pairs(self):
        &#34;&#34;&#34;Gets all the live sync pairs associated with the subclient

        Returns:
            dict    -- consists of all the live sync pairs in the subclient

                {
                    &#34;live_sync_pair1_name&#34;: {
                                &#34;id&#34;: live_sync_pair1_id
                    },
                    &#34;live_sync_pair2_name&#34;: {
                                &#34;id&#34;: live_sync_pair2_id
                    },
                }

        Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._ALL_LIVE_SYNC_PAIRS)

        if flag:
            live_sync_pairs_dict = {}
            if not bool(response.json()):
                return live_sync_pairs_dict
            if response.json() and &#39;siteInfo&#39; not in response.json():
                return live_sync_pairs_dict
            elif response.json() and &#39;siteInfo&#39; in response.json():
                for dictionary in response.json()[&#39;siteInfo&#39;]:
                    if dictionary[&#34;replicationGroup&#34;][&#34;replicationGroupName&#34;] != &#34;&#34;:
                        temp_name = dictionary[&#34;replicationGroup&#34;][&#34;replicationGroupName&#34;]
                    else:
                        temp_name = dictionary[&#39;subTask&#39;][&#39;subtaskName&#39;]
                    temp_id = str(dictionary[&#39;subTask&#39;][&#39;taskId&#39;])
                    live_sync_pairs_dict[temp_name.lower()] = {
                        &#39;id&#39;: temp_id
                    }

                return live_sync_pairs_dict

            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    @staticmethod
    def _live_sync_subtask_json(schedule_name):
        &#34;&#34;&#34;setter for the subtask in live sync JSON

        Args:
            schedule_name   (str)   -- Name of the Live sync schedule to be created

        &#34;&#34;&#34;
        return {
            &#34;subTaskType&#34;: &#34;RESTORE&#34;,
            &#34;operationType&#34;: &#34;SITE_REPLICATION&#34;,
            &#34;subTaskName&#34;: schedule_name
        }

    def _configure_live_sync(self, schedule_name, restore_options, pattern_dict=None):
        &#34;&#34;&#34;Configures Live sync after generating the full live sync json

        Args:
            schedule_name       (str)   -- Name of the Live sync schedule to be created

            restore_options     (dict)  -- Dictionary with all necessary values for Live sync

            pattern_dict        (dict)  -- Dictionary to generate the live sync schedule

                Sample:

                    for after_job_completes :
                    {
                        &#34;freq_type&#34;: &#39;after_job_completes&#39;,
                        &#34;active_start_date&#34;: date_in_%m/%d/%y (str),
                        &#34;active_start_time&#34;: time_in_%H/%S (str),
                        &#34;repeat_days&#34;: days_to_repeat (int)
                    }

                    for daily:
                    {
                         &#34;freq_type&#34;: &#39;daily&#39;,
                         &#34;active_start_time&#34;: time_in_%H/%S (str),
                         &#34;repeat_days&#34;: days_to_repeat (int)
                    }

                    for weekly:
                    {
                         &#34;freq_type&#34;: &#39;weekly&#39;,
                         &#34;active_start_time&#34;: time_in_%H/%S (str),
                         &#34;repeat_weeks&#34;: weeks_to_repeat (int)
                         &#34;weekdays&#34;: list of weekdays [&#39;Monday&#39;,&#39;Tuesday&#39;]
                    }

                    for monthly:
                    {
                         &#34;freq_type&#34;: &#39;monthly&#39;,
                         &#34;active_start_time&#34;: time_in_%H/%S (str),
                         &#34;repeat_months&#34;: weeks_to_repeat (int)
                         &#34;on_day&#34;: Day to run schedule (int)
                    }

                    for yearly:
                    {
                         &#34;active_start_time&#34;: time_in_%H/%S (str),
                         &#34;on_month&#34;: month to run schedule (str) January, Febuary...
                         &#34;on_day&#34;: Day to run schedule (int)
                    }

        Returns:
            object - instance of the Schedule class for this Live sync

        **Note** use this method to generate full Live sync JSON and to post the API call in derived class

        &#34;&#34;&#34;
        # To generate a random UUID for replication
        restore_options[&#39;replication_guid&#39;] = str(uuid.uuid1())

        request_json = self._subclient_object._prepare_fullvm_restore_json(restore_options)

        # To include the schedule pattern
        request_json = self.schedule_pattern.create_schedule(
            request_json,
            pattern_dict or {&#39;freq_type&#39;: &#39;after_job_completes&#39;})

        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;subTask&#39;] = self._live_sync_subtask_json(schedule_name)

        return self._subclient_object._process_restore_response(request_json)

    @property
    def live_sync_pairs(self):
        &#34;&#34;&#34;Returns the dictionary of all the live sync pairs and their info

        Returns:
            dict    -- consists of all the live sync pairs in the subclient

                {
                    &#34;live_sync_pair1_name&#34;: {
                                &#34;id&#34;: live_sync_pair1_id
                    },
                    &#34;live_sync_pair2_name&#34;: {
                                &#34;id&#34;: live_sync_pair2_id
                    },
                }

        &#34;&#34;&#34;
        return self._live_sync_pairs

    def get(self, live_sync_name):
        &#34;&#34;&#34;Returns a LiveSyncPairs object for the given live sync name

        Args:
             live_sync_name     (str)   -- Name of the live sync

        Returns:
            object  - Instance of the LiveSyncPairs class for the given live sync name

        Raises:
            SDKException:
                if type of the live sync name argument is not string

        &#34;&#34;&#34;
        if not isinstance(live_sync_name, str):
            raise SDKException(&#39;LiveSync&#39;, &#39;101&#39;)
        live_sync_name = live_sync_name.lower()
        if self.has_live_sync_pair(live_sync_name):
            return LiveSyncPairs(
                self._subclient_object,
                live_sync_name,
                self.live_sync_pairs[live_sync_name][&#39;id&#39;])
        raise SDKException(
            &#39;LiveSync&#39;, &#39;102&#39;, &#39;No Live Sync exists with given name: {0}&#39;.format(live_sync_name)
        )

    def has_live_sync_pair(self, live_sync_name):
        &#34;&#34;&#34;Checks if a live sync pair exists with the given name

        Args:
            live_sync_name      (str)   -- Name of the live sync

        Returns:
                bool    -   boolean output whether the live sync pair exists in the subclient or not

        Raises:
            SDKException:
                if type of the live sync name argument is not string

        &#34;&#34;&#34;
        return self.live_sync_pairs and live_sync_name.lower() in self.live_sync_pairs

    def refresh(self):
        &#34;&#34;&#34;Refresh the live sync pairs associated with the subclient&#34;&#34;&#34;
        self._live_sync_pairs = self._get_live_sync_pairs()</code></pre>
</details>
<h3>Subclasses</h3>
<ul class="hlist">
<li><a title="cvpysdk.subclients.virtualserver.livesync.amazon_live_sync.AmazonLiveSync" href="amazon_live_sync.html#cvpysdk.subclients.virtualserver.livesync.amazon_live_sync.AmazonLiveSync">AmazonLiveSync</a></li>
<li><a title="cvpysdk.subclients.virtualserver.livesync.azure_live_sync.AzureLiveSync" href="azure_live_sync.html#cvpysdk.subclients.virtualserver.livesync.azure_live_sync.AzureLiveSync">AzureLiveSync</a></li>
<li><a title="cvpysdk.subclients.virtualserver.livesync.hyperv_live_sync.HyperVLiveSync" href="hyperv_live_sync.html#cvpysdk.subclients.virtualserver.livesync.hyperv_live_sync.HyperVLiveSync">HyperVLiveSync</a></li>
<li><a title="cvpysdk.subclients.virtualserver.livesync.vmware_live_sync.VMWareLiveSync" href="vmware_live_sync.html#cvpysdk.subclients.virtualserver.livesync.vmware_live_sync.VMWareLiveSync">VMWareLiveSync</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.VsaLiveSync.live_sync_pairs"><code class="name">var <span class="ident">live_sync_pairs</span></code></dt>
<dd>
<div class="desc"><p>Returns the dictionary of all the live sync pairs and their info</p>
<h2 id="returns">Returns</h2>
<p>dict
&ndash; consists of all the live sync pairs in the subclient</p>
<pre><code>{
    "live_sync_pair1_name": {
                "id": live_sync_pair1_id
    },
    "live_sync_pair2_name": {
                "id": live_sync_pair2_id
    },
}
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/livesync/vsa_live_sync.py#L349-L366" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def live_sync_pairs(self):
    &#34;&#34;&#34;Returns the dictionary of all the live sync pairs and their info

    Returns:
        dict    -- consists of all the live sync pairs in the subclient

            {
                &#34;live_sync_pair1_name&#34;: {
                            &#34;id&#34;: live_sync_pair1_id
                },
                &#34;live_sync_pair2_name&#34;: {
                            &#34;id&#34;: live_sync_pair2_id
                },
            }

    &#34;&#34;&#34;
    return self._live_sync_pairs</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.VsaLiveSync.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, live_sync_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a LiveSyncPairs object for the given live sync name</p>
<h2 id="args">Args</h2>
<p>live_sync_name
(str)
&ndash; Name of the live sync</p>
<h2 id="returns">Returns</h2>
<p>object
- Instance of the LiveSyncPairs class for the given live sync name</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the live sync name argument is not string</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/livesync/vsa_live_sync.py#L368-L392" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, live_sync_name):
    &#34;&#34;&#34;Returns a LiveSyncPairs object for the given live sync name

    Args:
         live_sync_name     (str)   -- Name of the live sync

    Returns:
        object  - Instance of the LiveSyncPairs class for the given live sync name

    Raises:
        SDKException:
            if type of the live sync name argument is not string

    &#34;&#34;&#34;
    if not isinstance(live_sync_name, str):
        raise SDKException(&#39;LiveSync&#39;, &#39;101&#39;)
    live_sync_name = live_sync_name.lower()
    if self.has_live_sync_pair(live_sync_name):
        return LiveSyncPairs(
            self._subclient_object,
            live_sync_name,
            self.live_sync_pairs[live_sync_name][&#39;id&#39;])
    raise SDKException(
        &#39;LiveSync&#39;, &#39;102&#39;, &#39;No Live Sync exists with given name: {0}&#39;.format(live_sync_name)
    )</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.VsaLiveSync.has_live_sync_pair"><code class="name flex">
<span>def <span class="ident">has_live_sync_pair</span></span>(<span>self, live_sync_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks if a live sync pair exists with the given name</p>
<h2 id="args">Args</h2>
<p>live_sync_name
(str)
&ndash; Name of the live sync</p>
<h2 id="returns">Returns</h2>
<p>bool
-
boolean output whether the live sync pair exists in the subclient or not</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the live sync name argument is not string</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/livesync/vsa_live_sync.py#L394-L408" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_live_sync_pair(self, live_sync_name):
    &#34;&#34;&#34;Checks if a live sync pair exists with the given name

    Args:
        live_sync_name      (str)   -- Name of the live sync

    Returns:
            bool    -   boolean output whether the live sync pair exists in the subclient or not

    Raises:
        SDKException:
            if type of the live sync name argument is not string

    &#34;&#34;&#34;
    return self.live_sync_pairs and live_sync_name.lower() in self.live_sync_pairs</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.VsaLiveSync.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the live sync pairs associated with the subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/livesync/vsa_live_sync.py#L410-L412" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the live sync pairs associated with the subclient&#34;&#34;&#34;
    self._live_sync_pairs = self._get_live_sync_pairs()</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#vsalivesync">VsaLiveSync:</a><ul>
<li><a href="#vsalivesync-attributes">VsaLiveSync Attributes:</a></li>
</ul>
</li>
<li><a href="#livesyncpairs">LiveSyncPairs:</a><ul>
<li><a href="#livesyncpairs-attributes">LiveSyncPairs Attributes:</a></li>
</ul>
</li>
<li><a href="#livesyncvmpair">LiveSyncVMPair:</a><ul>
<li><a href="#livesyncvmpair-attributes">LiveSyncVMPair Attributes:</a></li>
</ul>
</li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync" href="index.html">cvpysdk.subclients.virtualserver.livesync</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncPairs" href="#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncPairs">LiveSyncPairs</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncPairs.get" href="#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncPairs.get">get</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncPairs.has_vm_pair" href="#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncPairs.has_vm_pair">has_vm_pair</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncPairs.live_sync_id" href="#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncPairs.live_sync_id">live_sync_id</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncPairs.live_sync_name" href="#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncPairs.live_sync_name">live_sync_name</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncPairs.refresh" href="#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncPairs.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncPairs.vm_pairs" href="#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncPairs.vm_pairs">vm_pairs</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair" href="#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair">LiveSyncVMPair</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.destination_client" href="#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.destination_client">destination_client</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.destination_instance" href="#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.destination_instance">destination_instance</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.destination_proxy" href="#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.destination_proxy">destination_proxy</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.destination_vm" href="#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.destination_vm">destination_vm</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.destination_vm_guid" href="#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.destination_vm_guid">destination_vm_guid</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.failover_job_id" href="#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.failover_job_id">failover_job_id</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.failover_status" href="#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.failover_status">failover_status</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.is_warm_sync_pair" href="#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.is_warm_sync_pair">is_warm_sync_pair</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.last_replication_job" href="#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.last_replication_job">last_replication_job</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.last_synced_backup_job" href="#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.last_synced_backup_job">last_synced_backup_job</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.latest_replication_job" href="#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.latest_replication_job">latest_replication_job</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.refresh" href="#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.replication_group_name" href="#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.replication_group_name">replication_group_name</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.replication_guid" href="#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.replication_guid">replication_guid</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.reverse_replication_schedule_id" href="#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.reverse_replication_schedule_id">reverse_replication_schedule_id</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.source_vm" href="#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.source_vm">source_vm</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.source_vm_guid" href="#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.source_vm_guid">source_vm_guid</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.status" href="#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.status">status</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.task_id" href="#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.task_id">task_id</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.vm_pair_id" href="#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.vm_pair_id">vm_pair_id</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.vm_pair_name" href="#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.vm_pair_name">vm_pair_name</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.VsaLiveSync" href="#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.VsaLiveSync">VsaLiveSync</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.VsaLiveSync.get" href="#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.VsaLiveSync.get">get</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.VsaLiveSync.has_live_sync_pair" href="#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.VsaLiveSync.has_live_sync_pair">has_live_sync_pair</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.VsaLiveSync.live_sync_pairs" href="#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.VsaLiveSync.live_sync_pairs">live_sync_pairs</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.VsaLiveSync.refresh" href="#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.VsaLiveSync.refresh">refresh</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>