<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.commcell_migration API documentation</title>
<meta name="description" content="Class to perform all the CommCell Migration operations on commcell …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.commcell_migration</code></h1>
</header>
<section id="section-intro">
<p>Class to perform all the CommCell Migration operations on commcell</p>
<p>CommCellMigration, GlobalRepositoryCell are the only classes defined in this file.</p>
<p>CommCellMigration: Helper class to perform CommCell Import &amp; Export operations.</p>
<h2 id="commcellmigration">Commcellmigration</h2>
<p><strong>init</strong>()
&ndash;
initializes CommCellMigration helper object.</p>
<p>commcell_export()
&ndash;
function to run CCM Export operation.</p>
<p>commcell_import()
&ndash;
function to run CCM Import operation.</p>
<p>tape_import()
&ndash;
function to run tape import operation.</p>
<p>GlobalRepositoryCell: Helper class to perform GRC related operations</p>
<h2 id="globalrepositorycell">Globalrepositorycell</h2>
<p><strong>init</strong>()
&ndash;
initializes GlobalRepositoryCell object</p>
<p>get_podcell_entities()
&ndash;
gets all entities from registered podcell that can be imported</p>
<p>get_podcell_properties()
&ndash;
gets all grc related properties for registered podcell</p>
<p>modify_monitored_clients()
&ndash;
overwrites imported clients in podcell grc schedule</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/commcell_migration.py#L1-L853" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-
# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Class to perform all the CommCell Migration operations on commcell

CommCellMigration, GlobalRepositoryCell are the only classes defined in this file.

CommCellMigration: Helper class to perform CommCell Import &amp; Export operations.

CommCellMigration:

    __init__()                      --  initializes CommCellMigration helper object.

    commcell_export()               --  function to run CCM Export operation.

    commcell_import()               --  function to run CCM Import operation.

    tape_import()                   --  function to run tape import operation.

GlobalRepositoryCell: Helper class to perform GRC related operations

GlobalRepositoryCell:

    __init__()                      --  initializes GlobalRepositoryCell object

    get_podcell_entities()          --  gets all entities from registered podcell that can be imported

    get_podcell_properties()        --  gets all grc related properties for registered podcell

    modify_monitored_clients()      --  overwrites imported clients in podcell grc schedule

&#34;&#34;&#34;
import html
import xml.etree.ElementTree as ET
from base64 import b64encode
from .job import Job
from .client import Client
from .exception import SDKException


class CommCellMigration(object):
    &#34;&#34;&#34;Class for representing the commcell export &amp; import operations from commcell. &#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initializes object of the CommCellMigration class.

            Args:
               commcell_object (object) -instance of the commcell class

            Returns:
               object - instance of the CommCellMigration class
        &#34;&#34;&#34;

        self._commcell_object = commcell_object
        self._cvpysdk_object = self._commcell_object._cvpysdk_object
        self._services = self._commcell_object._services
        self._update_response_ = self._commcell_object._update_response_
        self._commcell_name = self._commcell_object.commserv_name
        self._path_type = 0

    def commcell_export(self, export_location, client_list=None, options_dictionary=None, other_entities=None):
        &#34;&#34;&#34; Starts the Commcell Export job.

            Args:
                export_location     ( str )         --  Location to export generated dumps.

                client_list         ( list )        --  Contains list of clients used for export.
                    [
                        &#34;Server_1&#34;,&#34;Client1&#34;,&#34;Client2&#34;
                    ]

                options_dictionary  ( dict )        --  Contains options used to perform CCM Export.
                    {
                        &#34;pathType&#34;:&#34;Local&#34;,

                        &#34;otherSqlInstance&#34;:True,

                        &#34;userName&#34;:&#34;UserName&#34;,

                        &#34;password&#34;:&#34;...&#34;,

                        &#34;otherSqlInstance&#34;: False,

                        &#34;sqlInstanceName&#34;:&#34;SQLInstanceName&#34;,

                        &#34;sqlUserName&#34;:&#34;SQLUserName&#34;,

                        &#34;sqlPassword&#34;:&#34;...&#34;,

                        &#34;Database&#34;:&#34;commserv&#34;,

                        &#34;captureMediaAgents&#34;:True,
                        
                        &#34;captureSchedules&#34;:True,

                        &#34;captureActivityControl&#34;:True,

                        &#34;captureOperationWindow&#34;:True,

                        &#34;captureHolidays&#34;:True,

                        &#34;csName&#34;: &#34;CommservName&#34;,  # host cs for using sql instance export

                        &#34;clientIds&#34;: [client_id1, client_id2],  # required only when exporting clients using sql instance

                        &#34;autopickCluster&#34;:False
                    }
                
                other_entities      ( list )        --  list of other entities to be exporteddd
                    [
                        &#34;schedule_policies&#34;,

                        &#34;users_and_user_groups&#34;,

                        &#34;alerts&#34;
                    ]   

            Returns:
                CCM Export Job instance             --  returns the CCM Export job instance.

            Raises:
                SDKException:
                    if type of the input is not valid.

                    if all the required inputs are not provided.

                    if invalid inputs are passed.
        &#34;&#34;&#34;

        if client_list is None and other_entities is None:
            raise SDKException(&#39;CommCellMigration&#39;, &#39;105&#39;)

        options_dictionary = options_dictionary or {}

        path_type = options_dictionary.get(&#34;pathType&#34;, &#34;Local&#34;)
        network_user_name = options_dictionary.get(&#34;userName&#34;, &#34;&#34;)
        network_user_password = options_dictionary.get(&#34;password&#34;, &#34;&#34;)
        other_sql_instance = options_dictionary.get(&#34;otherSqlInstance&#34;, False)
        sql_instance_name = options_dictionary.get(&#34;sqlInstanceName&#34;, &#34;&#34;)
        sql_user_name = options_dictionary.get(&#34;sqlUserName&#34;, &#34;&#34;)
        sql_password = options_dictionary.get(&#34;sqlPassword&#34;, &#34;&#34;)
        database = options_dictionary.get(&#34;Database&#34;, &#34;Commserv&#34;)
        capture_ma = options_dictionary.get(&#34;captureMediaAgents&#34;, True)
        capture_schedules = options_dictionary.get(&#34;captureSchedules&#34;, True)
        capture_activity_control = options_dictionary.get(&#34;captureActivityControl&#34;, True)
        capture_opw = options_dictionary.get(&#34;captureOperationWindow&#34;, True)
        capture_holidays = options_dictionary.get(&#34;captureHolidays&#34;, True)
        auto_pick_cluster = options_dictionary.get(&#34;autopickCluster&#34;, False)
        cs_name = options_dictionary.get(&#34;csName&#34;, self._commcell_name)
        client_ids = options_dictionary.get(&#34;clientIds&#34;, [])

        if not (isinstance(path_type, str)
                and isinstance(network_user_name, str)
                and isinstance(network_user_password, str)
                and isinstance(other_sql_instance, bool)
                and isinstance(sql_instance_name, str)
                and isinstance(export_location, str)
                and isinstance(sql_user_name, str)
                and isinstance(sql_password, str)
                and isinstance(database, str)
                and isinstance(capture_ma, bool)
                and isinstance(capture_schedules, bool)
                and isinstance(capture_activity_control, bool)
                and isinstance(capture_opw, bool)
                and isinstance(capture_holidays, bool)
                and isinstance(auto_pick_cluster, bool)
                and isinstance(cs_name, str)
                and isinstance(client_ids, list)):
            raise SDKException(&#39;CommCellMigration&#39;, &#39;101&#39;)

        if path_type.lower() == &#39;local&#39;:
            self._path_type = 0
        elif path_type.lower() == &#39;network&#39;:
            self._path_type = 1
        else:
            raise SDKException(&#39;CommCellMigration&#39;, &#39;104&#39;)

        if other_sql_instance:
            if sql_instance_name == &#34;&#34; or sql_user_name == &#34;&#34; or sql_password == &#34;&#34;:
                raise SDKException(&#39;CommCellMigration&#39;, &#39;103&#39;)
            sql_password = b64encode(sql_password.encode()).decode()

        common_options = {
            &#34;otherSqlInstance&#34;: other_sql_instance,
            &#34;pathType&#34;: self._path_type,
            &#34;dumpFolder&#34;: export_location,
            &#34;splitCSDB&#34;: 1,
            &#34;sqlLinkedServer&#34;: {
                &#34;sqlServerName&#34;: sql_instance_name,
                &#34;sqlUserAccount&#34;: {
                    &#34;userName&#34;: sql_user_name,
                    &#34;password&#34;: sql_password
                }
            }
        }

        if self._path_type == 1:
            if network_user_name == &#34;&#34; or network_user_password == &#34;&#34;:
                raise SDKException(&#39;CommCellMigration&#39;, &#39;103&#39;)
            network_user_password = b64encode(network_user_password.encode()).decode()
            common_options[&#34;userAccount&#34;] = {
                &#34;password&#34;: network_user_password,
                &#34;userName&#34;: network_user_name
            }

        export_json = {
            &#34;taskInfo&#34;: {
                &#34;task&#34;: {
                    &#34;taskType&#34;: 1,
                    &#34;isEditing&#34;: False,
                    &#34;initiatedFrom&#34;: 2,
                    &#34;policyType&#34;: 0,
                    &#34;taskFlags&#34;: {
                        &#34;disabled&#34;: False
                    }
                },
                &#34;appGroup&#34;: {
                },
                &#34;subTasks&#34;: [
                    {
                        &#34;subTask&#34;: {
                            &#34;subTaskType&#34;: 1,
                            &#34;operationType&#34;: 4029
                        },
                        &#34;options&#34;: {
                            &#34;adminOpts&#34;: {
                                &#34;ccmOption&#34;: {
                                    &#34;commonOptions&#34;: common_options,
                                    &#34;captureOptions&#34;: {
                                        &#34;captureMediaAgents&#34;: capture_ma,
                                        &#34;lastHours&#34;: 60,
                                        &#34;remoteDumpDir&#34;: &#34;&#34;,
                                        &#34;remoteCSName&#34;: &#34;&#34;,
                                        &#34;captureSchedules&#34;: capture_schedules,
                                        &#34;captureActivityControl&#34;: capture_activity_control,
                                        &#34;captureOperationWindow&#34;: capture_opw,
                                        &#34;captureHolidays&#34;: capture_holidays,
                                        &#34;pruneExportedDump&#34;: False,
                                        &#34;autopickCluster&#34;: auto_pick_cluster,
                                        &#34;copyDumpToRemoteCS&#34;: False,
                                        &#34;useJobResultsDirForExport&#34;: False,
                                        &#34;captureFromDB&#34;: {
                                            &#34;csName&#34;: cs_name,
                                            &#34;csDbName&#34;: database
                                        },
                                        &#34;entities&#34;: [
                                        ],
                                        &#34;timeRange&#34;: {
                                            &#34;_type_&#34;: 54,
                                        }
                                    }
                                }
                            }
                        }
                    }
                ]
            }
        }

        if not other_sql_instance:
            del export_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;ccmOption&#39;] \
                [&#39;captureOptions&#39;][&#39;captureFromDB&#39;]

        sub_dict = export_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;ccmOption&#39;] \
            [&#39;captureOptions&#39;][&#39;entities&#39;]

        if other_entities:
            for entity in other_entities:
                if entity == &#34;schedule_policies&#34;:
                    sub_dict.append({&#39;commCellName&#39;: self._commcell_name, &#34;_type_&#34;: 34})

                elif entity == &#34;users_and_user_groups&#34;:
                    sub_dict.append({&#39;commCellName&#39;: self._commcell_name, &#34;_type_&#34;: 36})

                elif entity == &#34;alerts&#34;:
                    sub_dict.append({&#39;commCellName&#39;: self._commcell_name, &#34;_type_&#34;: 42})

        if client_list:
            if other_sql_instance:
                if not sql_instance_name \
                        or not sql_user_name \
                        or not sql_password \
                        or not client_ids:
                    raise SDKException(&#39;CommCellMigration&#39;, &#39;106&#39;)

                for index, client in enumerate(client_list):
                    temp_dic = {&#39;clientName&#39;: client, &#34;clientId&#34;: client_ids[index]}
                    sub_dict.append(temp_dic)

            else:
                exportable_clients = list(self._commcell_object.grc.get_clients_for_migration(
                    podcell_id=2, podcell_guid=self._commcell_object.commserv_guid
                ).values())
                for client in client_list:
                    if not self._commcell_object.clients.has_client(client):
                        raise SDKException(
                            &#39;CommCellMigration&#39;, &#39;107&#39;,
                            f&#39;Client {client} not found&#39;
                        )
                    agents = self._commcell_object.clients.get(client).agents.all_agents
                    if not agents:
                        raise SDKException(
                            &#39;CommCellMigration&#39;, &#39;107&#39;,
                            f&#39;Client {client} does not have any agents&#39;
                        )
                    temp_dic = {&#39;clientName&#39;: client, &#39;commCellName&#39;: self._commcell_name}
                    sub_dict.append(temp_dic)

        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;,
                                                           self._services[&#39;RESTORE&#39;],
                                                           export_json)

        if flag:
            if response.json() and &#39;jobIds&#39; in response.json():
                return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])
            elif response.json() and &#39;errorCode&#39; in response.json():
                raise SDKException(&#39;CommCellMigration&#39;, &#39;102&#39;, &#39;CCM Export job failed with error code : &#39; +
                                   str(response.json()[&#39;errorCode&#39;]))
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def commcell_import(self, import_location, options_dictionary):
        &#34;&#34;&#34; Starts the Commcell Import job.

            Args:
                import_location     ( str )         --  Location to import the generated dumps.

                options_dictionary  ( dict )        --  Contains list of options used for CCMImport and default values.
                    {
                        &#34;pathType&#34;: &#34;Network&#34;,
                        &#34;userName&#34; : &#34;username&#34;,
                        &#34;password&#34;: &#34;...&#34;,
                        &#34;forceOverwrite&#34;: False,
                        &#34;failIfEntityAlreadyExists&#34;: False,
                        &#34;deleteEntitiesNotPresent&#34;: False,
                        &#34;deleteEntitiesIfOnlyfromSource&#34;: False,
                        &#34;forceOverwriteHolidays&#34;: False,
                        &#34;mergeHolidays&#34;: True,
                        &#34;forceOverwriteOperationWindow&#34;: False,
                        &#34;mergeOperationWindow&#34;: False,
                        &#34;forceOverwriteSchedule&#34;: False,
                        &#34;mergeSchedules&#34;: True
                    }

            Returns:
                CCM Import Job instance             --  returns the CCM Import job instance.

            Raises:
                SDKException:
                    if type of the input is not valid.

                    if all the required inputs are not provided.

                    if invalid inputs are passed.
        &#34;&#34;&#34;
        path_type = options_dictionary.get(&#34;pathType&#34;, &#34;Local&#34;)
        network_user_name = options_dictionary.get(&#34;userName&#34;, &#34;&#34;)
        network_user_password = options_dictionary.get(&#34;password&#34;, &#34;&#34;)
        force_overwrite = options_dictionary.get(&#39;forceOverwrite&#39;, False)
        fail_if_entry_already_exists = options_dictionary.get(&#39;failIfEntityAlreadyExists&#39;, False)
        delete_entities_not_present = options_dictionary.get(&#39;deleteEntitiesNotPresent&#39;, False)
        delete_only_source = options_dictionary.get(&#39;deleteEntitiesIfOnlyfromSource&#39;, False)
        fo_holidays = options_dictionary.get(&#34;forceOverwriteHolidays&#34;, False)
        merge_holidays = options_dictionary.get(&#34;mergeHolidays&#34;, True)
        fo_operation_window = options_dictionary.get(&#34;forceOverwriteOperationWindow&#34;, False)
        merge_operation_window = options_dictionary.get(&#34;mergeOperationWindow&#34;, False)
        fo_schedules = options_dictionary.get(&#34;forceOverwriteSchedule&#34;, False)
        merge_schedules = options_dictionary.get(&#34;mergeSchedules&#34;, True)

        if not (isinstance(path_type, str) and isinstance(import_location, str)):
            raise SDKException(&#39;CommCellMigration&#39;, &#39;101&#39;)

        common_options = {
            &#34;bRoboJob&#34;: False,
            &#34;databaseConfiguredRemote&#34;: False,
            &#34;pathType&#34;: self._path_type,
            &#34;dumpFolder&#34;: import_location,
            &#34;splitCSDB&#34;: 0
        }

        if path_type.lower() == &#39;local&#39;:
            self._path_type = 0
        elif path_type.lower() == &#39;network&#39;:
            self._path_type = 1
            common_options[&#34;userAccount&#34;] = {
                &#34;password&#34;: network_user_password,
                &#34;userName&#34;: network_user_name
            }
        else:
            raise SDKException(&#39;CommCellMigration&#39;, &#39;104&#39;)

        if self._path_type == 1:
            if network_user_name == &#34;&#34; or network_user_password == &#34;&#34;:
                raise SDKException(&#39;CommCellMigration&#39;, &#39;103&#39;)

        import_json = {
            &#34;taskInfo&#34;: {
                &#34;associations&#34;: [
                    {
                        &#34;type&#34;: 0,
                        &#34;clientSidePackage&#34;: True,
                        &#34;consumeLicense&#34;: True
                    }
                ],
                &#34;task&#34;: {
                    &#34;taskType&#34;: 1,
                    &#34;initiatedFrom&#34;: 2,
                    &#34;taskFlags&#34;: {
                        &#34;disabled&#34;: False
                    }
                },
                &#34;subTasks&#34;: [
                    {
                        &#34;subTask&#34;: {
                            &#34;subTaskType&#34;: 1,
                            &#34;operationType&#34;: 4030
                        },
                        &#34;options&#34;: {
                            &#34;adminOpts&#34;: {
                                &#34;ccmOption&#34;: {
                                    &#34;mergeOptions&#34;: {
                                        &#34;deleteEntitiesIfOnlyfromSource&#34;: False,
                                        &#34;forceOverwriteHolidays&#34;: fo_holidays,
                                        &#34;reuseTapes&#34;: False,
                                        &#34;specifyStagingPath&#34;: False,
                                        &#34;forceOverwriteOperationWindow&#34;: fo_operation_window,
                                        &#34;fallbackSpareGroup&#34;: &#34;&#34;,
                                        &#34;mergeOperationWindow&#34;: merge_operation_window,
                                        &#34;pruneImportedDump&#34;: False,
                                        &#34;alwaysUseFallbackDataPath&#34;: True,
                                        &#34;deleteEntitiesNotPresent&#34;: delete_entities_not_present,
                                        &#34;deleteEntitiesIfOnlyfromSource&#34;: delete_only_source,
                                        &#34;forceOverwrite&#34;: force_overwrite,
                                        &#34;mergeHolidays&#34;: merge_holidays,
                                        &#34;forceOverwriteSchedule&#34;: fo_schedules,
                                        &#34;fallbackDrivePool&#34;: &#34;&#34;,
                                        &#34;mergeActivityControl&#34;: True,
                                        &#34;fallbackMediaAgent&#34;: &#34;&#34;,
                                        &#34;mergeSchedules&#34;: merge_schedules,
                                        &#34;failIfEntityAlreadyExists&#34;: fail_if_entry_already_exists,
                                        &#34;fallbackLibrary&#34;: &#34;&#34;,
                                        &#34;skipConflictMedia&#34;: False,
                                        &#34;stagingPath&#34;: &#34;&#34;
                                    },
                                    &#34;commonOptions&#34;: common_options
                                }
                            }
                        }
                    }
                ]
            }
        }
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;,
                                                           self._services[&#39;RESTORE&#39;],
                                                           import_json)

        if flag:
            if response.json() and &#39;jobIds&#39; in response.json():
                return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])
            elif response.json() and &#39;errorCode&#39; in response.json():
                raise SDKException(&#39;CommCellMigration&#39;, &#39;102&#39;, &#39;CCM Import job failed with error code : &#39; +
                                   str(response.json()[&#39;errorCode&#39;]))
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def tape_import(self, library_id, medias_id, drive_pool_id):

        &#34;&#34;&#34; performs the tape import import operation for the specified tape.

            Args:
                library_id      (int)       --      tape library id.

                medias_id        (list)       --      tape id.

                drive_pool_id   (int)       --      drive pool id

            Returns:
                Tape import job instance
        &#34;&#34;&#34;

        tape_import_json = {
            &#34;taskInfo&#34;: {
                &#34;associations&#34;: [
                ], &#34;task&#34;: {
                    &#34;ownerId&#34;: 1, &#34;taskType&#34;: 1, &#34;ownerName&#34;: &#34;admin&#34;, &#34;sequenceNumber&#34;: 0, &#34;initiatedFrom&#34;: 1,
                    &#34;policyType&#34;: 0, &#34;taskId&#34;: 0, &#34;taskFlags&#34;: {
                        &#34;disabled&#34;: False
                    }
                }, &#34;subTasks&#34;: [
                    {
                        &#34;subTask&#34;: {
                            &#34;subTaskType&#34;: 1, &#34;operationType&#34;: 4017
                        },
                        &#34;options&#34;: {
                            &#34;adminOpts&#34;: {
                                &#34;contentIndexingOption&#34;: {
                                    &#34;subClientBasedAnalytics&#34;: False
                                }, &#34;libraryOption&#34;: {
                                    &#34;operation&#34;: 15, &#34;media&#34;: [
                                    ], &#34;library&#34;: {
                                        &#34;libraryName&#34;: &#34;&#34;, &#34;_type_&#34;: 9, &#34;libraryId&#34;: library_id
                                    }, &#34;catalogMedia&#34;: {
                                        &#34;fileMarkerToStart&#34;: 0, &#34;fileMarkerToEnd&#34;: 0, &#34;reCatalog&#34;: True,
                                        &#34;maxNumOfDrives&#34;: 1,
                                        &#34;spareGroupId&#34;: 0,
                                        &#34;merge&#34;: True,
                                        &#34;subTaskType&#34;: 2,
                                        &#34;drivePoolEntity&#34;: {
                                            &#34;_type_&#34;: 47, &#34;drivePoolId&#34;: drive_pool_id
                                        }
                                    }, &#34;mediaAgent&#34;: {
                                        &#34;mediaAgentId&#34;: 2, &#34;_type_&#34;: 11
                                    }
                                }
                            }, &#34;restoreOptions&#34;: {
                                &#34;virtualServerRstOption&#34;: {
                                    &#34;isBlockLevelReplication&#34;: False
                                }, &#34;commonOptions&#34;: {
                                    &#34;syncRestore&#34;: False
                                }
                            }
                        }
                    }
                ]
            }
        }

        sub_dict = tape_import_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;adminOpts&#34;][&#34;libraryOption&#34;][&#34;media&#34;]

        for media in medias_id:
            temp_dict = {&#34;_type_&#34;: 46, &#34;mediaId&#34;: int(media), &#34;mediaName&#34;: &#34;&#34;}
            sub_dict.append(temp_dict)

        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;,
                                                           self._services[&#39;RESTORE&#39;],
                                                           tape_import_json)

        if flag:
            if response.json() and &#39;jobIds&#39; in response.json():
                return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])
            elif response.json() and &#39;errorCode&#39; in response.json():
                raise SDKException(&#39;CommCellMigration&#39;, &#39;102&#39;, &#39;Tape Import job failed with error code : &#39; +
                                   str(response.json()[&#39;errorCode&#39;]))
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

class GlobalRepositoryCell:
    &#34;&#34;&#34;Class for representing the GRC feature from commcell&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;
        Initializes the object of GlobalRepositoryCell class

        Args:
            commcell_object (Commcell)  -   Commcell class instance

        Returns:
            grc (GlobalRepositoryCell) - instance of the GlobalRepositoryCell class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._cvpysdk_object = self._commcell_object._cvpysdk_object
        self._services = self._commcell_object._services
        self._commcell_name = self._commcell_object.commserv_name

    def _get_task_details(self, task_id):
        &#34;&#34;&#34;
        Util for getting XML of GRC schedule task (required for generating more XMLs)

        Args:
            task_id     (int)   -   id of grc schedule&#39;s task

        Returns:
            task_xml    (str)   -   xml form string with grc schedule details
            Example:
                &lt;TMMsg_GetTaskDetailResp&gt;
                        &lt;taskInfo&gt;
                                &lt;task taskId=&#34;&#34; taskName=&#34;&#34; &gt; ... &lt;/task&gt;
                        &lt;appGroup/&gt;
                        &lt;subTasks&gt;
                            &lt;subTask subTaskId=&#34;&#34; subTaskType=&#34;&#34; ...&gt;
                            &lt;options&gt;
                                &lt;backupOpts backupLevel=&#34;&#34;&gt;
                                    &lt;dataOpt autoCopy=&#34;&#34;/&gt;
                                &lt;/backupOpts&gt;
                                &lt;adminOpts&gt;
                                    &lt;ccmOption&gt;
                                        &lt;mergeOptions ...&gt;
                                        &lt;captureOptions ...&gt;
                                            ...
                                        &lt;/captureOptions&gt;
                                    &lt;/ccmOption&gt;
                                &lt;/adminOpts&gt;
                            &lt;/options&gt;
                            &lt;pattern ...&gt;...&lt;/pattern&gt;
                        &lt;/subTasks&gt;
                    &lt;/taskInfo&gt;
                &lt;/TMMsg_GetTaskDetailResp&gt;
        &#34;&#34;&#34;
        get_task_xml = f&#39;&lt;TMMsg_GetTaskDetailReq taskId=&#34;{task_id}&#34;/&gt;&#39;
        return self._commcell_object.qoperation_execute(get_task_xml, return_xml=True)

    def _get_commcell_from_id(self, commcell_id):
        &#34;&#34;&#34;
        Util to get registered commcell name from given commcell id

        Args:
            commcell_id (int)   -   id of commcell

        Returns:
            commcell_name   (str)   -   name of commcell
        &#34;&#34;&#34;
        for commcell_name, commcell_data in self._commcell_object.registered_commcells.items():
            if commcell_data.get(&#39;commCell&#39;, {}).get(&#39;commCellId&#39;) == commcell_id:
                return commcell_name

    def _modify_task_props(self, podcell_properties, task_xml):
        &#34;&#34;&#34;
        Util for modifying task properties, after grc properties are updated

        Args:
            podcell_properties  (dict)  -   the dict returned by get_podcell_properties
            task_xml    (str)           -   the xml returned for grc schedule&#39;s task info

        Returns:
            response    (dict)   -   the response from execute qoperation
        &#34;&#34;&#34;
        grc_schedule_xml = ET.fromstring(podcell_properties[&#39;schedule_xml&#39;])
        task_info_xml = ET.fromstring(task_xml)
        modify_task_xml = &#34;&#34;&#34;
        &lt;TMMsg_ModifyTaskReq&gt;
            &lt;taskInfo&gt;
                &lt;task initiatedFrom=&#34;1&#34; ownerId=&#34;{0}&#34; ownerName=&#34;{1}&#34; policyType=&#34;0&#34; sequenceNumber=&#34;0&#34; taskId=&#34;{2}&#34; taskType=&#34;2&#34;&gt;
                    &lt;taskFlags disabled=&#34;0&#34; isEZOperation=&#34;0&#34; isEdgeDrive=&#34;0&#34;/&gt;
                &lt;/task&gt;
                &lt;appGroup/&gt;
                {3}
            &lt;/taskInfo&gt;
        &lt;/TMMsg_ModifyTaskReq&gt;
        &#34;&#34;&#34;
        modify_task_xml = modify_task_xml.format(
            grc_schedule_xml.find(&#39;taskInfo/task&#39;).get(&#39;ownerId&#39;),
            grc_schedule_xml.find(&#39;taskInfo/task&#39;).get(&#39;ownerName&#39;),
            podcell_properties[&#39;task_id&#39;],
            ET.tostring(task_info_xml.find(&#39;taskInfo/subTasks&#39;), encoding=&#39;unicode&#39;)
        )
        return self._commcell_object.qoperation_execute(modify_task_xml)

    def _get_podcell_entities(self, podcell_name: str = None, podcell_id: int = None, podcell_guid: str = None):
        &#34;&#34;&#34;
        Gets the entities in podcell available for monitoring via GRC

        Args:
            podcell_name    (str)   -   name of pod cell
            podcell_id      (int)   -   id of podcell
            podcell_guid    (str)   -   guid of podcell (Optional)

        Returns:
            monitor_entities    (str)   -   all entities of pod cell in XML format
            Example:
                &lt;EVGui_CCMCommCellInfo commcellName=&#34;&#34; commcellNumber=&#34;&#34; commcellId=&#34;&#34;&gt;
                    &lt;clientEntityLst clientId=&#34;&#34; clientName=&#34;&#34;&gt;
                        ...
                    &lt;/clientEntityLst&gt;
                    &lt;clientEntityLst clientId=&#34;&#34; clientName=&#34;&#34;&gt;
                        ...
                    &lt;/clientEntityLst&gt;
                    &lt;clientEntityLst clientId=&#34;&#34; ...&gt;
                        &lt;appTypeEntityList ... appTypeId=&#34;&#34;&gt;
                            &lt;instanceList ... instanceId=&#34;&#34;&gt;
                                &lt;backupSetList ... backupsetId=&#34;&#34;&gt;
                                    &lt;subclientList ... subclientId=&#34;&#34;/&gt;
                                &lt;/backupSetList&gt;
                            &lt;/instanceList&gt;
                        &lt;/appTypeEntityList&gt;
                    &lt;/clientEntityLst&gt;
                    &lt;clientComputerGrp clientGroupId=&#34;&#34; clientGroupName=&#34;&#34;/&gt;
                    ...
                    &lt;clientComputerGrp clientGroupId=&#34;&#34; clientGroupName=&#34;&#34;/&gt;
                &lt;/EVGui_CCMCommCellInfo&gt;
        &#34;&#34;&#34;
        if podcell_id is None:
            if podcell_name is None:
                raise SDKException(&#39;GlobalRepositoryCell&#39;, &#39;103&#39;)
            podcell_id = self._commcell_object.registered_commcells.get(podcell_name, {}) \
                .get(&#39;commCell&#39;, {}).get(&#39;commCellId&#39;)
            if podcell_id is None:
                raise SDKException(&#39;GlobalRepositoryCell&#39;, &#39;104&#39;, f&#39;for podcell: {podcell_name}&#39;)
        if podcell_name is None:
            podcell_name = self._get_commcell_from_id(podcell_id)
        if podcell_guid is None:
            podcell_guid = self._commcell_object.registered_commcells[podcell_name].get(&#39;commCell&#39;, {}).get(&#39;csGUID&#39;)

        entities_xml = &#34;&#34;&#34;
        &lt;EVGui_GetCCMExportInfo exportMsgType=&#34;3&#34; strCSName=&#34;{0}*{0}*8400&#34;&gt;
            &lt;mediaAgent _type_=&#34;3&#34;/&gt;
            &lt;userInfo/&gt;
            &lt;commCell _type_=&#34;1&#34; commCellId=&#34;{1}&#34; commCellName=&#34;{0}&#34; csGUID=&#34;{2}&#34;/&gt;
        &lt;/EVGui_GetCCMExportInfo&gt;
        &#34;&#34;&#34;
        exec_xml = entities_xml.format(podcell_name, podcell_id, podcell_guid)
        resp = self._commcell_object.qoperation_execute(exec_xml)
        return resp.get(&#39;strXmlInfo&#39;)

    def get_clients_for_migration(self, podcell_name: str = None, podcell_id: int = None, podcell_guid: str = None):
        &#34;&#34;&#34;
        Gets the podcell clients that can be migrated
        
        Args:
            podcell_name    (str)   -   name of pod cell
            podcell_id      (int)   -   id of podcell
            podcell_guid    (str)   -   guid of podcell (Optional)
        
        Returns:
            clients_dict    (dict)  -   dict with client ID as key and client name value
            Example:
                {
                    X: &#34;clientA&#34;,
                    Y: &#34;clientB&#34;,
                    Z: &#34;clienta&#34;
                }
        &#34;&#34;&#34;
        clients_dict = {}
        entities_xml = self._get_podcell_entities(
            podcell_name=podcell_name,
            podcell_id=podcell_id,
            podcell_guid=podcell_guid
        )
        entities_xml = ET.fromstring(entities_xml)
        for client_node in entities_xml.findall(&#39;clientEntityLst&#39;):
            cl_id = client_node.get(&#39;clientId&#39;)
            cl_name = client_node.get(&#39;clientName&#39;)
            clients_dict[cl_id] = cl_name
        return clients_dict

    def _get_podcell_properties(self, podcell_name: str = None, podcell_id: int = None):
        &#34;&#34;&#34;
        Gets the GRC properties of given pod cell

        Args:
            podcell_name    (str)   -   name of pod cell
            podcell_id      (int)   -   id of podcell

        Returns:
            podcell_properties  (dict)  -   different properties of pod cell in dict format with xml values
        &#34;&#34;&#34;
        # TODO: Update grc properties map
        grc_prop_map = {
            2: &#39;podcell_name&#39;,
            4: &#39;schedule_xml&#39;,
            15: &#39;entities_xml&#39;,
            16: &#39;libraries_xml&#39;,
            19: &#39;task_id&#39;
        }
        if podcell_id is None:
            if podcell_name is None:
                raise SDKException(&#39;GlobalRepositoryCell&#39;, &#39;103&#39;)
            podcell_id = self._commcell_object.registered_commcells.get(podcell_name, {}) \
                .get(&#39;commCell&#39;, {}).get(&#39;commCellId&#39;)
            if podcell_id is None:
                raise SDKException(&#39;GlobalRepositoryCell&#39;, &#39;104&#39;, f&#39;for podcell: {podcell_name}&#39;)
        grc_props_xml = f&#39;&lt;App_GetGRCCommCellPropsReq commcellId=&#34;{podcell_id}&#34;/&gt;&#39;
        grc_props_response = self._commcell_object.qoperation_execute(grc_props_xml)
        podcell_properties = {
            grc_prop_map.get(prop.get(&#39;propId&#39;), prop.get(&#39;propId&#39;)): prop.get(&#39;stringVal&#39;) or prop.get(&#39;numVal&#39;)
            for prop in grc_props_response[&#39;grcCommcellPropList&#39;]
        }
        return podcell_properties

    def modify_monitored_clients(self, podcell_name: str = None, podcell_id: int = None, clients: list = None):
        &#34;&#34;&#34;
        Modifies (overwrites) the monitored clients in grc properties for given podcell

        Args:
            podcell_name    (str)   -   name of pod cell
            podcell_id      (int)   -   id of podcell
            client_ids      (list)  -   list of client ids, names
                                        or Client objects (of pod cell)

        Returns:
            None
        &#34;&#34;&#34;
        if podcell_id is None:
            if podcell_name is None:
                raise SDKException(&#39;GlobalRepositoryCell&#39;, &#39;103&#39;)
            podcell_id = self._commcell_object.registered_commcells.get(podcell_name, {}) \
                .get(&#39;commCell&#39;, {}).get(&#39;commCellId&#39;)
            if podcell_id is None:
                raise SDKException(&#39;GlobalRepositoryCell&#39;, &#39;104&#39;, f&#39;for podcell: {podcell_name}&#39;)

        set_grc_xml = &#34;&#34;&#34;
            &lt;App_SetGRCCommCellPropsReq commcellId=&#34;{0}&#34;&gt;
                &lt;grcCommcellProp numVal=&#34;0&#34; propId=&#34;4&#34; stringVal=&#34;{1}&#34;/&gt;
                &lt;grcCommcellProp numVal=&#34;1&#34; propId=&#34;1&#34; stringVal=&#34;&#34;/&gt;
                &lt;grcCommcellProp numVal=&#34;0&#34; propId=&#34;2&#34; stringVal=&#34;{2}&#34;/&gt;
                &lt;grcCommcellProp numVal=&#34;0&#34; propId=&#34;15&#34; stringVal=&#34;{3}&#34;/&gt;
                &lt;grcCommcellProp numVal=&#34;1&#34; propId=&#34;8&#34; stringVal=&#34;&#34;/&gt;
                &lt;grcCommcellProp numVal=&#34;0&#34; propId=&#34;14&#34; stringVal=&#34;&#34;/&gt;
            &lt;/App_SetGRCCommCellPropsReq&gt;
        &#34;&#34;&#34;
        xml_header = &#39;&lt;?xml version=\&#39;1.0\&#39; encoding=\&#39;UTF-8\&#39;?&gt;&#39;
        cc_props = self._get_podcell_properties(podcell_id=podcell_id)
        podcell_name = cc_props[&#39;podcell_name&#39;]
        task_xml = self._get_task_details(task_id=cc_props[&#39;task_id&#39;])
        podcell_entities = self._get_podcell_entities(podcell_id=podcell_id)
        entities_xml = ET.fromstring(podcell_entities)
        client_ids = []
        if isinstance(clients[0], str):
            for client_node in entities_xml.findall(&#39;clientEntityLst&#39;):
                if client_node.get(&#39;clientName&#39;) in clients:
                    client_ids.append(client_node.get(&#39;clientId&#39;))
        elif isinstance(clients[0], int):
            client_ids = clients
        elif isinstance(clients[0], Client):
            client_ids = [int(cl.client_id) for cl in clients]

        # Generate nested XML 1 (selected clients)
        current_schedule = ET.fromstring(cc_props[&#39;schedule_xml&#39;])
        capture_options = current_schedule.find(&#39;taskInfo/subTasks/options/adminOpts/ccmOption/captureOptions&#39;)
        # remove all &lt;entities ...&gt; tags
        for entity_node in capture_options.findall(&#39;entities&#39;):
            capture_options.remove(entity_node)
        # insert &lt;entities ...&gt; tags for selected client_ids
        for clid in client_ids:
            capture_options.insert(0, ET.Element(&#39;entities&#39;, {&#39;clientId&#39;: str(clid), &#39;_type_&#39;: &#39;3&#39;}))
        nested_xml1 = ET.tostring(current_schedule, encoding=&#39;unicode&#39;)
        nested_xml1 = html.escape(f&#39;{xml_header}{nested_xml1}&#39;)

        # Generate nested XML 2 (all clients in podcell)
        entities_xml = ET.fromstring(podcell_entities)
        nested_xml2 = ET.tostring(entities_xml, encoding=&#39;unicode&#39;)
        nested_xml2 = html.escape(nested_xml2)

        # Combine nested XMLs into parent XML
        final_xml = set_grc_xml.format(podcell_id, nested_xml1, podcell_name, nested_xml2)
        self._commcell_object.qoperation_execute(final_xml)
        self._modify_task_props(cc_props, task_xml)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.commcell_migration.CommCellMigration"><code class="flex name class">
<span>class <span class="ident">CommCellMigration</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing the commcell export &amp; import operations from commcell. </p>
<p>Initializes object of the CommCellMigration class.</p>
<h2 id="args">Args</h2>
<p>commcell_object (object) -instance of the commcell class</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the CommCellMigration class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/commcell_migration.py#L55-L562" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class CommCellMigration(object):
    &#34;&#34;&#34;Class for representing the commcell export &amp; import operations from commcell. &#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initializes object of the CommCellMigration class.

            Args:
               commcell_object (object) -instance of the commcell class

            Returns:
               object - instance of the CommCellMigration class
        &#34;&#34;&#34;

        self._commcell_object = commcell_object
        self._cvpysdk_object = self._commcell_object._cvpysdk_object
        self._services = self._commcell_object._services
        self._update_response_ = self._commcell_object._update_response_
        self._commcell_name = self._commcell_object.commserv_name
        self._path_type = 0

    def commcell_export(self, export_location, client_list=None, options_dictionary=None, other_entities=None):
        &#34;&#34;&#34; Starts the Commcell Export job.

            Args:
                export_location     ( str )         --  Location to export generated dumps.

                client_list         ( list )        --  Contains list of clients used for export.
                    [
                        &#34;Server_1&#34;,&#34;Client1&#34;,&#34;Client2&#34;
                    ]

                options_dictionary  ( dict )        --  Contains options used to perform CCM Export.
                    {
                        &#34;pathType&#34;:&#34;Local&#34;,

                        &#34;otherSqlInstance&#34;:True,

                        &#34;userName&#34;:&#34;UserName&#34;,

                        &#34;password&#34;:&#34;...&#34;,

                        &#34;otherSqlInstance&#34;: False,

                        &#34;sqlInstanceName&#34;:&#34;SQLInstanceName&#34;,

                        &#34;sqlUserName&#34;:&#34;SQLUserName&#34;,

                        &#34;sqlPassword&#34;:&#34;...&#34;,

                        &#34;Database&#34;:&#34;commserv&#34;,

                        &#34;captureMediaAgents&#34;:True,
                        
                        &#34;captureSchedules&#34;:True,

                        &#34;captureActivityControl&#34;:True,

                        &#34;captureOperationWindow&#34;:True,

                        &#34;captureHolidays&#34;:True,

                        &#34;csName&#34;: &#34;CommservName&#34;,  # host cs for using sql instance export

                        &#34;clientIds&#34;: [client_id1, client_id2],  # required only when exporting clients using sql instance

                        &#34;autopickCluster&#34;:False
                    }
                
                other_entities      ( list )        --  list of other entities to be exporteddd
                    [
                        &#34;schedule_policies&#34;,

                        &#34;users_and_user_groups&#34;,

                        &#34;alerts&#34;
                    ]   

            Returns:
                CCM Export Job instance             --  returns the CCM Export job instance.

            Raises:
                SDKException:
                    if type of the input is not valid.

                    if all the required inputs are not provided.

                    if invalid inputs are passed.
        &#34;&#34;&#34;

        if client_list is None and other_entities is None:
            raise SDKException(&#39;CommCellMigration&#39;, &#39;105&#39;)

        options_dictionary = options_dictionary or {}

        path_type = options_dictionary.get(&#34;pathType&#34;, &#34;Local&#34;)
        network_user_name = options_dictionary.get(&#34;userName&#34;, &#34;&#34;)
        network_user_password = options_dictionary.get(&#34;password&#34;, &#34;&#34;)
        other_sql_instance = options_dictionary.get(&#34;otherSqlInstance&#34;, False)
        sql_instance_name = options_dictionary.get(&#34;sqlInstanceName&#34;, &#34;&#34;)
        sql_user_name = options_dictionary.get(&#34;sqlUserName&#34;, &#34;&#34;)
        sql_password = options_dictionary.get(&#34;sqlPassword&#34;, &#34;&#34;)
        database = options_dictionary.get(&#34;Database&#34;, &#34;Commserv&#34;)
        capture_ma = options_dictionary.get(&#34;captureMediaAgents&#34;, True)
        capture_schedules = options_dictionary.get(&#34;captureSchedules&#34;, True)
        capture_activity_control = options_dictionary.get(&#34;captureActivityControl&#34;, True)
        capture_opw = options_dictionary.get(&#34;captureOperationWindow&#34;, True)
        capture_holidays = options_dictionary.get(&#34;captureHolidays&#34;, True)
        auto_pick_cluster = options_dictionary.get(&#34;autopickCluster&#34;, False)
        cs_name = options_dictionary.get(&#34;csName&#34;, self._commcell_name)
        client_ids = options_dictionary.get(&#34;clientIds&#34;, [])

        if not (isinstance(path_type, str)
                and isinstance(network_user_name, str)
                and isinstance(network_user_password, str)
                and isinstance(other_sql_instance, bool)
                and isinstance(sql_instance_name, str)
                and isinstance(export_location, str)
                and isinstance(sql_user_name, str)
                and isinstance(sql_password, str)
                and isinstance(database, str)
                and isinstance(capture_ma, bool)
                and isinstance(capture_schedules, bool)
                and isinstance(capture_activity_control, bool)
                and isinstance(capture_opw, bool)
                and isinstance(capture_holidays, bool)
                and isinstance(auto_pick_cluster, bool)
                and isinstance(cs_name, str)
                and isinstance(client_ids, list)):
            raise SDKException(&#39;CommCellMigration&#39;, &#39;101&#39;)

        if path_type.lower() == &#39;local&#39;:
            self._path_type = 0
        elif path_type.lower() == &#39;network&#39;:
            self._path_type = 1
        else:
            raise SDKException(&#39;CommCellMigration&#39;, &#39;104&#39;)

        if other_sql_instance:
            if sql_instance_name == &#34;&#34; or sql_user_name == &#34;&#34; or sql_password == &#34;&#34;:
                raise SDKException(&#39;CommCellMigration&#39;, &#39;103&#39;)
            sql_password = b64encode(sql_password.encode()).decode()

        common_options = {
            &#34;otherSqlInstance&#34;: other_sql_instance,
            &#34;pathType&#34;: self._path_type,
            &#34;dumpFolder&#34;: export_location,
            &#34;splitCSDB&#34;: 1,
            &#34;sqlLinkedServer&#34;: {
                &#34;sqlServerName&#34;: sql_instance_name,
                &#34;sqlUserAccount&#34;: {
                    &#34;userName&#34;: sql_user_name,
                    &#34;password&#34;: sql_password
                }
            }
        }

        if self._path_type == 1:
            if network_user_name == &#34;&#34; or network_user_password == &#34;&#34;:
                raise SDKException(&#39;CommCellMigration&#39;, &#39;103&#39;)
            network_user_password = b64encode(network_user_password.encode()).decode()
            common_options[&#34;userAccount&#34;] = {
                &#34;password&#34;: network_user_password,
                &#34;userName&#34;: network_user_name
            }

        export_json = {
            &#34;taskInfo&#34;: {
                &#34;task&#34;: {
                    &#34;taskType&#34;: 1,
                    &#34;isEditing&#34;: False,
                    &#34;initiatedFrom&#34;: 2,
                    &#34;policyType&#34;: 0,
                    &#34;taskFlags&#34;: {
                        &#34;disabled&#34;: False
                    }
                },
                &#34;appGroup&#34;: {
                },
                &#34;subTasks&#34;: [
                    {
                        &#34;subTask&#34;: {
                            &#34;subTaskType&#34;: 1,
                            &#34;operationType&#34;: 4029
                        },
                        &#34;options&#34;: {
                            &#34;adminOpts&#34;: {
                                &#34;ccmOption&#34;: {
                                    &#34;commonOptions&#34;: common_options,
                                    &#34;captureOptions&#34;: {
                                        &#34;captureMediaAgents&#34;: capture_ma,
                                        &#34;lastHours&#34;: 60,
                                        &#34;remoteDumpDir&#34;: &#34;&#34;,
                                        &#34;remoteCSName&#34;: &#34;&#34;,
                                        &#34;captureSchedules&#34;: capture_schedules,
                                        &#34;captureActivityControl&#34;: capture_activity_control,
                                        &#34;captureOperationWindow&#34;: capture_opw,
                                        &#34;captureHolidays&#34;: capture_holidays,
                                        &#34;pruneExportedDump&#34;: False,
                                        &#34;autopickCluster&#34;: auto_pick_cluster,
                                        &#34;copyDumpToRemoteCS&#34;: False,
                                        &#34;useJobResultsDirForExport&#34;: False,
                                        &#34;captureFromDB&#34;: {
                                            &#34;csName&#34;: cs_name,
                                            &#34;csDbName&#34;: database
                                        },
                                        &#34;entities&#34;: [
                                        ],
                                        &#34;timeRange&#34;: {
                                            &#34;_type_&#34;: 54,
                                        }
                                    }
                                }
                            }
                        }
                    }
                ]
            }
        }

        if not other_sql_instance:
            del export_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;ccmOption&#39;] \
                [&#39;captureOptions&#39;][&#39;captureFromDB&#39;]

        sub_dict = export_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;ccmOption&#39;] \
            [&#39;captureOptions&#39;][&#39;entities&#39;]

        if other_entities:
            for entity in other_entities:
                if entity == &#34;schedule_policies&#34;:
                    sub_dict.append({&#39;commCellName&#39;: self._commcell_name, &#34;_type_&#34;: 34})

                elif entity == &#34;users_and_user_groups&#34;:
                    sub_dict.append({&#39;commCellName&#39;: self._commcell_name, &#34;_type_&#34;: 36})

                elif entity == &#34;alerts&#34;:
                    sub_dict.append({&#39;commCellName&#39;: self._commcell_name, &#34;_type_&#34;: 42})

        if client_list:
            if other_sql_instance:
                if not sql_instance_name \
                        or not sql_user_name \
                        or not sql_password \
                        or not client_ids:
                    raise SDKException(&#39;CommCellMigration&#39;, &#39;106&#39;)

                for index, client in enumerate(client_list):
                    temp_dic = {&#39;clientName&#39;: client, &#34;clientId&#34;: client_ids[index]}
                    sub_dict.append(temp_dic)

            else:
                exportable_clients = list(self._commcell_object.grc.get_clients_for_migration(
                    podcell_id=2, podcell_guid=self._commcell_object.commserv_guid
                ).values())
                for client in client_list:
                    if not self._commcell_object.clients.has_client(client):
                        raise SDKException(
                            &#39;CommCellMigration&#39;, &#39;107&#39;,
                            f&#39;Client {client} not found&#39;
                        )
                    agents = self._commcell_object.clients.get(client).agents.all_agents
                    if not agents:
                        raise SDKException(
                            &#39;CommCellMigration&#39;, &#39;107&#39;,
                            f&#39;Client {client} does not have any agents&#39;
                        )
                    temp_dic = {&#39;clientName&#39;: client, &#39;commCellName&#39;: self._commcell_name}
                    sub_dict.append(temp_dic)

        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;,
                                                           self._services[&#39;RESTORE&#39;],
                                                           export_json)

        if flag:
            if response.json() and &#39;jobIds&#39; in response.json():
                return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])
            elif response.json() and &#39;errorCode&#39; in response.json():
                raise SDKException(&#39;CommCellMigration&#39;, &#39;102&#39;, &#39;CCM Export job failed with error code : &#39; +
                                   str(response.json()[&#39;errorCode&#39;]))
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def commcell_import(self, import_location, options_dictionary):
        &#34;&#34;&#34; Starts the Commcell Import job.

            Args:
                import_location     ( str )         --  Location to import the generated dumps.

                options_dictionary  ( dict )        --  Contains list of options used for CCMImport and default values.
                    {
                        &#34;pathType&#34;: &#34;Network&#34;,
                        &#34;userName&#34; : &#34;username&#34;,
                        &#34;password&#34;: &#34;...&#34;,
                        &#34;forceOverwrite&#34;: False,
                        &#34;failIfEntityAlreadyExists&#34;: False,
                        &#34;deleteEntitiesNotPresent&#34;: False,
                        &#34;deleteEntitiesIfOnlyfromSource&#34;: False,
                        &#34;forceOverwriteHolidays&#34;: False,
                        &#34;mergeHolidays&#34;: True,
                        &#34;forceOverwriteOperationWindow&#34;: False,
                        &#34;mergeOperationWindow&#34;: False,
                        &#34;forceOverwriteSchedule&#34;: False,
                        &#34;mergeSchedules&#34;: True
                    }

            Returns:
                CCM Import Job instance             --  returns the CCM Import job instance.

            Raises:
                SDKException:
                    if type of the input is not valid.

                    if all the required inputs are not provided.

                    if invalid inputs are passed.
        &#34;&#34;&#34;
        path_type = options_dictionary.get(&#34;pathType&#34;, &#34;Local&#34;)
        network_user_name = options_dictionary.get(&#34;userName&#34;, &#34;&#34;)
        network_user_password = options_dictionary.get(&#34;password&#34;, &#34;&#34;)
        force_overwrite = options_dictionary.get(&#39;forceOverwrite&#39;, False)
        fail_if_entry_already_exists = options_dictionary.get(&#39;failIfEntityAlreadyExists&#39;, False)
        delete_entities_not_present = options_dictionary.get(&#39;deleteEntitiesNotPresent&#39;, False)
        delete_only_source = options_dictionary.get(&#39;deleteEntitiesIfOnlyfromSource&#39;, False)
        fo_holidays = options_dictionary.get(&#34;forceOverwriteHolidays&#34;, False)
        merge_holidays = options_dictionary.get(&#34;mergeHolidays&#34;, True)
        fo_operation_window = options_dictionary.get(&#34;forceOverwriteOperationWindow&#34;, False)
        merge_operation_window = options_dictionary.get(&#34;mergeOperationWindow&#34;, False)
        fo_schedules = options_dictionary.get(&#34;forceOverwriteSchedule&#34;, False)
        merge_schedules = options_dictionary.get(&#34;mergeSchedules&#34;, True)

        if not (isinstance(path_type, str) and isinstance(import_location, str)):
            raise SDKException(&#39;CommCellMigration&#39;, &#39;101&#39;)

        common_options = {
            &#34;bRoboJob&#34;: False,
            &#34;databaseConfiguredRemote&#34;: False,
            &#34;pathType&#34;: self._path_type,
            &#34;dumpFolder&#34;: import_location,
            &#34;splitCSDB&#34;: 0
        }

        if path_type.lower() == &#39;local&#39;:
            self._path_type = 0
        elif path_type.lower() == &#39;network&#39;:
            self._path_type = 1
            common_options[&#34;userAccount&#34;] = {
                &#34;password&#34;: network_user_password,
                &#34;userName&#34;: network_user_name
            }
        else:
            raise SDKException(&#39;CommCellMigration&#39;, &#39;104&#39;)

        if self._path_type == 1:
            if network_user_name == &#34;&#34; or network_user_password == &#34;&#34;:
                raise SDKException(&#39;CommCellMigration&#39;, &#39;103&#39;)

        import_json = {
            &#34;taskInfo&#34;: {
                &#34;associations&#34;: [
                    {
                        &#34;type&#34;: 0,
                        &#34;clientSidePackage&#34;: True,
                        &#34;consumeLicense&#34;: True
                    }
                ],
                &#34;task&#34;: {
                    &#34;taskType&#34;: 1,
                    &#34;initiatedFrom&#34;: 2,
                    &#34;taskFlags&#34;: {
                        &#34;disabled&#34;: False
                    }
                },
                &#34;subTasks&#34;: [
                    {
                        &#34;subTask&#34;: {
                            &#34;subTaskType&#34;: 1,
                            &#34;operationType&#34;: 4030
                        },
                        &#34;options&#34;: {
                            &#34;adminOpts&#34;: {
                                &#34;ccmOption&#34;: {
                                    &#34;mergeOptions&#34;: {
                                        &#34;deleteEntitiesIfOnlyfromSource&#34;: False,
                                        &#34;forceOverwriteHolidays&#34;: fo_holidays,
                                        &#34;reuseTapes&#34;: False,
                                        &#34;specifyStagingPath&#34;: False,
                                        &#34;forceOverwriteOperationWindow&#34;: fo_operation_window,
                                        &#34;fallbackSpareGroup&#34;: &#34;&#34;,
                                        &#34;mergeOperationWindow&#34;: merge_operation_window,
                                        &#34;pruneImportedDump&#34;: False,
                                        &#34;alwaysUseFallbackDataPath&#34;: True,
                                        &#34;deleteEntitiesNotPresent&#34;: delete_entities_not_present,
                                        &#34;deleteEntitiesIfOnlyfromSource&#34;: delete_only_source,
                                        &#34;forceOverwrite&#34;: force_overwrite,
                                        &#34;mergeHolidays&#34;: merge_holidays,
                                        &#34;forceOverwriteSchedule&#34;: fo_schedules,
                                        &#34;fallbackDrivePool&#34;: &#34;&#34;,
                                        &#34;mergeActivityControl&#34;: True,
                                        &#34;fallbackMediaAgent&#34;: &#34;&#34;,
                                        &#34;mergeSchedules&#34;: merge_schedules,
                                        &#34;failIfEntityAlreadyExists&#34;: fail_if_entry_already_exists,
                                        &#34;fallbackLibrary&#34;: &#34;&#34;,
                                        &#34;skipConflictMedia&#34;: False,
                                        &#34;stagingPath&#34;: &#34;&#34;
                                    },
                                    &#34;commonOptions&#34;: common_options
                                }
                            }
                        }
                    }
                ]
            }
        }
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;,
                                                           self._services[&#39;RESTORE&#39;],
                                                           import_json)

        if flag:
            if response.json() and &#39;jobIds&#39; in response.json():
                return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])
            elif response.json() and &#39;errorCode&#39; in response.json():
                raise SDKException(&#39;CommCellMigration&#39;, &#39;102&#39;, &#39;CCM Import job failed with error code : &#39; +
                                   str(response.json()[&#39;errorCode&#39;]))
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def tape_import(self, library_id, medias_id, drive_pool_id):

        &#34;&#34;&#34; performs the tape import import operation for the specified tape.

            Args:
                library_id      (int)       --      tape library id.

                medias_id        (list)       --      tape id.

                drive_pool_id   (int)       --      drive pool id

            Returns:
                Tape import job instance
        &#34;&#34;&#34;

        tape_import_json = {
            &#34;taskInfo&#34;: {
                &#34;associations&#34;: [
                ], &#34;task&#34;: {
                    &#34;ownerId&#34;: 1, &#34;taskType&#34;: 1, &#34;ownerName&#34;: &#34;admin&#34;, &#34;sequenceNumber&#34;: 0, &#34;initiatedFrom&#34;: 1,
                    &#34;policyType&#34;: 0, &#34;taskId&#34;: 0, &#34;taskFlags&#34;: {
                        &#34;disabled&#34;: False
                    }
                }, &#34;subTasks&#34;: [
                    {
                        &#34;subTask&#34;: {
                            &#34;subTaskType&#34;: 1, &#34;operationType&#34;: 4017
                        },
                        &#34;options&#34;: {
                            &#34;adminOpts&#34;: {
                                &#34;contentIndexingOption&#34;: {
                                    &#34;subClientBasedAnalytics&#34;: False
                                }, &#34;libraryOption&#34;: {
                                    &#34;operation&#34;: 15, &#34;media&#34;: [
                                    ], &#34;library&#34;: {
                                        &#34;libraryName&#34;: &#34;&#34;, &#34;_type_&#34;: 9, &#34;libraryId&#34;: library_id
                                    }, &#34;catalogMedia&#34;: {
                                        &#34;fileMarkerToStart&#34;: 0, &#34;fileMarkerToEnd&#34;: 0, &#34;reCatalog&#34;: True,
                                        &#34;maxNumOfDrives&#34;: 1,
                                        &#34;spareGroupId&#34;: 0,
                                        &#34;merge&#34;: True,
                                        &#34;subTaskType&#34;: 2,
                                        &#34;drivePoolEntity&#34;: {
                                            &#34;_type_&#34;: 47, &#34;drivePoolId&#34;: drive_pool_id
                                        }
                                    }, &#34;mediaAgent&#34;: {
                                        &#34;mediaAgentId&#34;: 2, &#34;_type_&#34;: 11
                                    }
                                }
                            }, &#34;restoreOptions&#34;: {
                                &#34;virtualServerRstOption&#34;: {
                                    &#34;isBlockLevelReplication&#34;: False
                                }, &#34;commonOptions&#34;: {
                                    &#34;syncRestore&#34;: False
                                }
                            }
                        }
                    }
                ]
            }
        }

        sub_dict = tape_import_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;adminOpts&#34;][&#34;libraryOption&#34;][&#34;media&#34;]

        for media in medias_id:
            temp_dict = {&#34;_type_&#34;: 46, &#34;mediaId&#34;: int(media), &#34;mediaName&#34;: &#34;&#34;}
            sub_dict.append(temp_dict)

        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;,
                                                           self._services[&#39;RESTORE&#39;],
                                                           tape_import_json)

        if flag:
            if response.json() and &#39;jobIds&#39; in response.json():
                return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])
            elif response.json() and &#39;errorCode&#39; in response.json():
                raise SDKException(&#39;CommCellMigration&#39;, &#39;102&#39;, &#39;Tape Import job failed with error code : &#39; +
                                   str(response.json()[&#39;errorCode&#39;]))
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.commcell_migration.CommCellMigration.commcell_export"><code class="name flex">
<span>def <span class="ident">commcell_export</span></span>(<span>self, export_location, client_list=None, options_dictionary=None, other_entities=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Starts the Commcell Export job.</p>
<h2 id="args">Args</h2>
<p>export_location
( str )
&ndash;
Location to export generated dumps.</p>
<p>client_list
( list )
&ndash;
Contains list of clients used for export.
[
"Server_1","Client1","Client2"
]</p>
<p>options_dictionary
( dict )
&ndash;
Contains options used to perform CCM Export.
{
"pathType":"Local",</p>
<pre><code>    "otherSqlInstance":True,

    "userName":"UserName",

    "password":"...",

    "otherSqlInstance": False,

    "sqlInstanceName":"SQLInstanceName",

    "sqlUserName":"SQLUserName",

    "sqlPassword":"...",

    "Database":"commserv",

    "captureMediaAgents":True,

    "captureSchedules":True,

    "captureActivityControl":True,

    "captureOperationWindow":True,

    "captureHolidays":True,

    "csName": "CommservName",  # host cs for using sql instance export

    "clientIds": [client_id1, client_id2],  # required only when exporting clients using sql instance

    "autopickCluster":False
}
</code></pre>
<p>other_entities
( list )
&ndash;
list of other entities to be exporteddd
[
"schedule_policies",</p>
<pre><code>    "users_and_user_groups",

    "alerts"
]
</code></pre>
<h2 id="returns">Returns</h2>
<p>CCM Export Job instance
&ndash;
returns the CCM Export job instance.</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the input is not valid.</p>
<pre><code>if all the required inputs are not provided.

if invalid inputs are passed.
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/commcell_migration.py#L75-L335" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def commcell_export(self, export_location, client_list=None, options_dictionary=None, other_entities=None):
    &#34;&#34;&#34; Starts the Commcell Export job.

        Args:
            export_location     ( str )         --  Location to export generated dumps.

            client_list         ( list )        --  Contains list of clients used for export.
                [
                    &#34;Server_1&#34;,&#34;Client1&#34;,&#34;Client2&#34;
                ]

            options_dictionary  ( dict )        --  Contains options used to perform CCM Export.
                {
                    &#34;pathType&#34;:&#34;Local&#34;,

                    &#34;otherSqlInstance&#34;:True,

                    &#34;userName&#34;:&#34;UserName&#34;,

                    &#34;password&#34;:&#34;...&#34;,

                    &#34;otherSqlInstance&#34;: False,

                    &#34;sqlInstanceName&#34;:&#34;SQLInstanceName&#34;,

                    &#34;sqlUserName&#34;:&#34;SQLUserName&#34;,

                    &#34;sqlPassword&#34;:&#34;...&#34;,

                    &#34;Database&#34;:&#34;commserv&#34;,

                    &#34;captureMediaAgents&#34;:True,
                    
                    &#34;captureSchedules&#34;:True,

                    &#34;captureActivityControl&#34;:True,

                    &#34;captureOperationWindow&#34;:True,

                    &#34;captureHolidays&#34;:True,

                    &#34;csName&#34;: &#34;CommservName&#34;,  # host cs for using sql instance export

                    &#34;clientIds&#34;: [client_id1, client_id2],  # required only when exporting clients using sql instance

                    &#34;autopickCluster&#34;:False
                }
            
            other_entities      ( list )        --  list of other entities to be exporteddd
                [
                    &#34;schedule_policies&#34;,

                    &#34;users_and_user_groups&#34;,

                    &#34;alerts&#34;
                ]   

        Returns:
            CCM Export Job instance             --  returns the CCM Export job instance.

        Raises:
            SDKException:
                if type of the input is not valid.

                if all the required inputs are not provided.

                if invalid inputs are passed.
    &#34;&#34;&#34;

    if client_list is None and other_entities is None:
        raise SDKException(&#39;CommCellMigration&#39;, &#39;105&#39;)

    options_dictionary = options_dictionary or {}

    path_type = options_dictionary.get(&#34;pathType&#34;, &#34;Local&#34;)
    network_user_name = options_dictionary.get(&#34;userName&#34;, &#34;&#34;)
    network_user_password = options_dictionary.get(&#34;password&#34;, &#34;&#34;)
    other_sql_instance = options_dictionary.get(&#34;otherSqlInstance&#34;, False)
    sql_instance_name = options_dictionary.get(&#34;sqlInstanceName&#34;, &#34;&#34;)
    sql_user_name = options_dictionary.get(&#34;sqlUserName&#34;, &#34;&#34;)
    sql_password = options_dictionary.get(&#34;sqlPassword&#34;, &#34;&#34;)
    database = options_dictionary.get(&#34;Database&#34;, &#34;Commserv&#34;)
    capture_ma = options_dictionary.get(&#34;captureMediaAgents&#34;, True)
    capture_schedules = options_dictionary.get(&#34;captureSchedules&#34;, True)
    capture_activity_control = options_dictionary.get(&#34;captureActivityControl&#34;, True)
    capture_opw = options_dictionary.get(&#34;captureOperationWindow&#34;, True)
    capture_holidays = options_dictionary.get(&#34;captureHolidays&#34;, True)
    auto_pick_cluster = options_dictionary.get(&#34;autopickCluster&#34;, False)
    cs_name = options_dictionary.get(&#34;csName&#34;, self._commcell_name)
    client_ids = options_dictionary.get(&#34;clientIds&#34;, [])

    if not (isinstance(path_type, str)
            and isinstance(network_user_name, str)
            and isinstance(network_user_password, str)
            and isinstance(other_sql_instance, bool)
            and isinstance(sql_instance_name, str)
            and isinstance(export_location, str)
            and isinstance(sql_user_name, str)
            and isinstance(sql_password, str)
            and isinstance(database, str)
            and isinstance(capture_ma, bool)
            and isinstance(capture_schedules, bool)
            and isinstance(capture_activity_control, bool)
            and isinstance(capture_opw, bool)
            and isinstance(capture_holidays, bool)
            and isinstance(auto_pick_cluster, bool)
            and isinstance(cs_name, str)
            and isinstance(client_ids, list)):
        raise SDKException(&#39;CommCellMigration&#39;, &#39;101&#39;)

    if path_type.lower() == &#39;local&#39;:
        self._path_type = 0
    elif path_type.lower() == &#39;network&#39;:
        self._path_type = 1
    else:
        raise SDKException(&#39;CommCellMigration&#39;, &#39;104&#39;)

    if other_sql_instance:
        if sql_instance_name == &#34;&#34; or sql_user_name == &#34;&#34; or sql_password == &#34;&#34;:
            raise SDKException(&#39;CommCellMigration&#39;, &#39;103&#39;)
        sql_password = b64encode(sql_password.encode()).decode()

    common_options = {
        &#34;otherSqlInstance&#34;: other_sql_instance,
        &#34;pathType&#34;: self._path_type,
        &#34;dumpFolder&#34;: export_location,
        &#34;splitCSDB&#34;: 1,
        &#34;sqlLinkedServer&#34;: {
            &#34;sqlServerName&#34;: sql_instance_name,
            &#34;sqlUserAccount&#34;: {
                &#34;userName&#34;: sql_user_name,
                &#34;password&#34;: sql_password
            }
        }
    }

    if self._path_type == 1:
        if network_user_name == &#34;&#34; or network_user_password == &#34;&#34;:
            raise SDKException(&#39;CommCellMigration&#39;, &#39;103&#39;)
        network_user_password = b64encode(network_user_password.encode()).decode()
        common_options[&#34;userAccount&#34;] = {
            &#34;password&#34;: network_user_password,
            &#34;userName&#34;: network_user_name
        }

    export_json = {
        &#34;taskInfo&#34;: {
            &#34;task&#34;: {
                &#34;taskType&#34;: 1,
                &#34;isEditing&#34;: False,
                &#34;initiatedFrom&#34;: 2,
                &#34;policyType&#34;: 0,
                &#34;taskFlags&#34;: {
                    &#34;disabled&#34;: False
                }
            },
            &#34;appGroup&#34;: {
            },
            &#34;subTasks&#34;: [
                {
                    &#34;subTask&#34;: {
                        &#34;subTaskType&#34;: 1,
                        &#34;operationType&#34;: 4029
                    },
                    &#34;options&#34;: {
                        &#34;adminOpts&#34;: {
                            &#34;ccmOption&#34;: {
                                &#34;commonOptions&#34;: common_options,
                                &#34;captureOptions&#34;: {
                                    &#34;captureMediaAgents&#34;: capture_ma,
                                    &#34;lastHours&#34;: 60,
                                    &#34;remoteDumpDir&#34;: &#34;&#34;,
                                    &#34;remoteCSName&#34;: &#34;&#34;,
                                    &#34;captureSchedules&#34;: capture_schedules,
                                    &#34;captureActivityControl&#34;: capture_activity_control,
                                    &#34;captureOperationWindow&#34;: capture_opw,
                                    &#34;captureHolidays&#34;: capture_holidays,
                                    &#34;pruneExportedDump&#34;: False,
                                    &#34;autopickCluster&#34;: auto_pick_cluster,
                                    &#34;copyDumpToRemoteCS&#34;: False,
                                    &#34;useJobResultsDirForExport&#34;: False,
                                    &#34;captureFromDB&#34;: {
                                        &#34;csName&#34;: cs_name,
                                        &#34;csDbName&#34;: database
                                    },
                                    &#34;entities&#34;: [
                                    ],
                                    &#34;timeRange&#34;: {
                                        &#34;_type_&#34;: 54,
                                    }
                                }
                            }
                        }
                    }
                }
            ]
        }
    }

    if not other_sql_instance:
        del export_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;ccmOption&#39;] \
            [&#39;captureOptions&#39;][&#39;captureFromDB&#39;]

    sub_dict = export_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;ccmOption&#39;] \
        [&#39;captureOptions&#39;][&#39;entities&#39;]

    if other_entities:
        for entity in other_entities:
            if entity == &#34;schedule_policies&#34;:
                sub_dict.append({&#39;commCellName&#39;: self._commcell_name, &#34;_type_&#34;: 34})

            elif entity == &#34;users_and_user_groups&#34;:
                sub_dict.append({&#39;commCellName&#39;: self._commcell_name, &#34;_type_&#34;: 36})

            elif entity == &#34;alerts&#34;:
                sub_dict.append({&#39;commCellName&#39;: self._commcell_name, &#34;_type_&#34;: 42})

    if client_list:
        if other_sql_instance:
            if not sql_instance_name \
                    or not sql_user_name \
                    or not sql_password \
                    or not client_ids:
                raise SDKException(&#39;CommCellMigration&#39;, &#39;106&#39;)

            for index, client in enumerate(client_list):
                temp_dic = {&#39;clientName&#39;: client, &#34;clientId&#34;: client_ids[index]}
                sub_dict.append(temp_dic)

        else:
            exportable_clients = list(self._commcell_object.grc.get_clients_for_migration(
                podcell_id=2, podcell_guid=self._commcell_object.commserv_guid
            ).values())
            for client in client_list:
                if not self._commcell_object.clients.has_client(client):
                    raise SDKException(
                        &#39;CommCellMigration&#39;, &#39;107&#39;,
                        f&#39;Client {client} not found&#39;
                    )
                agents = self._commcell_object.clients.get(client).agents.all_agents
                if not agents:
                    raise SDKException(
                        &#39;CommCellMigration&#39;, &#39;107&#39;,
                        f&#39;Client {client} does not have any agents&#39;
                    )
                temp_dic = {&#39;clientName&#39;: client, &#39;commCellName&#39;: self._commcell_name}
                sub_dict.append(temp_dic)

    flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;,
                                                       self._services[&#39;RESTORE&#39;],
                                                       export_json)

    if flag:
        if response.json() and &#39;jobIds&#39; in response.json():
            return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])
        elif response.json() and &#39;errorCode&#39; in response.json():
            raise SDKException(&#39;CommCellMigration&#39;, &#39;102&#39;, &#39;CCM Export job failed with error code : &#39; +
                               str(response.json()[&#39;errorCode&#39;]))
    else:
        response_string = self._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.commcell_migration.CommCellMigration.commcell_import"><code class="name flex">
<span>def <span class="ident">commcell_import</span></span>(<span>self, import_location, options_dictionary)</span>
</code></dt>
<dd>
<div class="desc"><p>Starts the Commcell Import job.</p>
<h2 id="args">Args</h2>
<p>import_location
( str )
&ndash;
Location to import the generated dumps.</p>
<p>options_dictionary
( dict )
&ndash;
Contains list of options used for CCMImport and default values.
{
"pathType": "Network",
"userName" : "username",
"password": "&hellip;",
"forceOverwrite": False,
"failIfEntityAlreadyExists": False,
"deleteEntitiesNotPresent": False,
"deleteEntitiesIfOnlyfromSource": False,
"forceOverwriteHolidays": False,
"mergeHolidays": True,
"forceOverwriteOperationWindow": False,
"mergeOperationWindow": False,
"forceOverwriteSchedule": False,
"mergeSchedules": True
}</p>
<h2 id="returns">Returns</h2>
<p>CCM Import Job instance
&ndash;
returns the CCM Import job instance.</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the input is not valid.</p>
<pre><code>if all the required inputs are not provided.

if invalid inputs are passed.
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/commcell_migration.py#L337-L480" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def commcell_import(self, import_location, options_dictionary):
    &#34;&#34;&#34; Starts the Commcell Import job.

        Args:
            import_location     ( str )         --  Location to import the generated dumps.

            options_dictionary  ( dict )        --  Contains list of options used for CCMImport and default values.
                {
                    &#34;pathType&#34;: &#34;Network&#34;,
                    &#34;userName&#34; : &#34;username&#34;,
                    &#34;password&#34;: &#34;...&#34;,
                    &#34;forceOverwrite&#34;: False,
                    &#34;failIfEntityAlreadyExists&#34;: False,
                    &#34;deleteEntitiesNotPresent&#34;: False,
                    &#34;deleteEntitiesIfOnlyfromSource&#34;: False,
                    &#34;forceOverwriteHolidays&#34;: False,
                    &#34;mergeHolidays&#34;: True,
                    &#34;forceOverwriteOperationWindow&#34;: False,
                    &#34;mergeOperationWindow&#34;: False,
                    &#34;forceOverwriteSchedule&#34;: False,
                    &#34;mergeSchedules&#34;: True
                }

        Returns:
            CCM Import Job instance             --  returns the CCM Import job instance.

        Raises:
            SDKException:
                if type of the input is not valid.

                if all the required inputs are not provided.

                if invalid inputs are passed.
    &#34;&#34;&#34;
    path_type = options_dictionary.get(&#34;pathType&#34;, &#34;Local&#34;)
    network_user_name = options_dictionary.get(&#34;userName&#34;, &#34;&#34;)
    network_user_password = options_dictionary.get(&#34;password&#34;, &#34;&#34;)
    force_overwrite = options_dictionary.get(&#39;forceOverwrite&#39;, False)
    fail_if_entry_already_exists = options_dictionary.get(&#39;failIfEntityAlreadyExists&#39;, False)
    delete_entities_not_present = options_dictionary.get(&#39;deleteEntitiesNotPresent&#39;, False)
    delete_only_source = options_dictionary.get(&#39;deleteEntitiesIfOnlyfromSource&#39;, False)
    fo_holidays = options_dictionary.get(&#34;forceOverwriteHolidays&#34;, False)
    merge_holidays = options_dictionary.get(&#34;mergeHolidays&#34;, True)
    fo_operation_window = options_dictionary.get(&#34;forceOverwriteOperationWindow&#34;, False)
    merge_operation_window = options_dictionary.get(&#34;mergeOperationWindow&#34;, False)
    fo_schedules = options_dictionary.get(&#34;forceOverwriteSchedule&#34;, False)
    merge_schedules = options_dictionary.get(&#34;mergeSchedules&#34;, True)

    if not (isinstance(path_type, str) and isinstance(import_location, str)):
        raise SDKException(&#39;CommCellMigration&#39;, &#39;101&#39;)

    common_options = {
        &#34;bRoboJob&#34;: False,
        &#34;databaseConfiguredRemote&#34;: False,
        &#34;pathType&#34;: self._path_type,
        &#34;dumpFolder&#34;: import_location,
        &#34;splitCSDB&#34;: 0
    }

    if path_type.lower() == &#39;local&#39;:
        self._path_type = 0
    elif path_type.lower() == &#39;network&#39;:
        self._path_type = 1
        common_options[&#34;userAccount&#34;] = {
            &#34;password&#34;: network_user_password,
            &#34;userName&#34;: network_user_name
        }
    else:
        raise SDKException(&#39;CommCellMigration&#39;, &#39;104&#39;)

    if self._path_type == 1:
        if network_user_name == &#34;&#34; or network_user_password == &#34;&#34;:
            raise SDKException(&#39;CommCellMigration&#39;, &#39;103&#39;)

    import_json = {
        &#34;taskInfo&#34;: {
            &#34;associations&#34;: [
                {
                    &#34;type&#34;: 0,
                    &#34;clientSidePackage&#34;: True,
                    &#34;consumeLicense&#34;: True
                }
            ],
            &#34;task&#34;: {
                &#34;taskType&#34;: 1,
                &#34;initiatedFrom&#34;: 2,
                &#34;taskFlags&#34;: {
                    &#34;disabled&#34;: False
                }
            },
            &#34;subTasks&#34;: [
                {
                    &#34;subTask&#34;: {
                        &#34;subTaskType&#34;: 1,
                        &#34;operationType&#34;: 4030
                    },
                    &#34;options&#34;: {
                        &#34;adminOpts&#34;: {
                            &#34;ccmOption&#34;: {
                                &#34;mergeOptions&#34;: {
                                    &#34;deleteEntitiesIfOnlyfromSource&#34;: False,
                                    &#34;forceOverwriteHolidays&#34;: fo_holidays,
                                    &#34;reuseTapes&#34;: False,
                                    &#34;specifyStagingPath&#34;: False,
                                    &#34;forceOverwriteOperationWindow&#34;: fo_operation_window,
                                    &#34;fallbackSpareGroup&#34;: &#34;&#34;,
                                    &#34;mergeOperationWindow&#34;: merge_operation_window,
                                    &#34;pruneImportedDump&#34;: False,
                                    &#34;alwaysUseFallbackDataPath&#34;: True,
                                    &#34;deleteEntitiesNotPresent&#34;: delete_entities_not_present,
                                    &#34;deleteEntitiesIfOnlyfromSource&#34;: delete_only_source,
                                    &#34;forceOverwrite&#34;: force_overwrite,
                                    &#34;mergeHolidays&#34;: merge_holidays,
                                    &#34;forceOverwriteSchedule&#34;: fo_schedules,
                                    &#34;fallbackDrivePool&#34;: &#34;&#34;,
                                    &#34;mergeActivityControl&#34;: True,
                                    &#34;fallbackMediaAgent&#34;: &#34;&#34;,
                                    &#34;mergeSchedules&#34;: merge_schedules,
                                    &#34;failIfEntityAlreadyExists&#34;: fail_if_entry_already_exists,
                                    &#34;fallbackLibrary&#34;: &#34;&#34;,
                                    &#34;skipConflictMedia&#34;: False,
                                    &#34;stagingPath&#34;: &#34;&#34;
                                },
                                &#34;commonOptions&#34;: common_options
                            }
                        }
                    }
                }
            ]
        }
    }
    flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;,
                                                       self._services[&#39;RESTORE&#39;],
                                                       import_json)

    if flag:
        if response.json() and &#39;jobIds&#39; in response.json():
            return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])
        elif response.json() and &#39;errorCode&#39; in response.json():
            raise SDKException(&#39;CommCellMigration&#39;, &#39;102&#39;, &#39;CCM Import job failed with error code : &#39; +
                               str(response.json()[&#39;errorCode&#39;]))
    else:
        response_string = self._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.commcell_migration.CommCellMigration.tape_import"><code class="name flex">
<span>def <span class="ident">tape_import</span></span>(<span>self, library_id, medias_id, drive_pool_id)</span>
</code></dt>
<dd>
<div class="desc"><p>performs the tape import import operation for the specified tape.</p>
<h2 id="args">Args</h2>
<p>library_id
(int)
&ndash;
tape library id.</p>
<p>medias_id
(list)
&ndash;
tape id.</p>
<p>drive_pool_id
(int)
&ndash;
drive pool id</p>
<h2 id="returns">Returns</h2>
<p>Tape import job instance</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/commcell_migration.py#L482-L562" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def tape_import(self, library_id, medias_id, drive_pool_id):

    &#34;&#34;&#34; performs the tape import import operation for the specified tape.

        Args:
            library_id      (int)       --      tape library id.

            medias_id        (list)       --      tape id.

            drive_pool_id   (int)       --      drive pool id

        Returns:
            Tape import job instance
    &#34;&#34;&#34;

    tape_import_json = {
        &#34;taskInfo&#34;: {
            &#34;associations&#34;: [
            ], &#34;task&#34;: {
                &#34;ownerId&#34;: 1, &#34;taskType&#34;: 1, &#34;ownerName&#34;: &#34;admin&#34;, &#34;sequenceNumber&#34;: 0, &#34;initiatedFrom&#34;: 1,
                &#34;policyType&#34;: 0, &#34;taskId&#34;: 0, &#34;taskFlags&#34;: {
                    &#34;disabled&#34;: False
                }
            }, &#34;subTasks&#34;: [
                {
                    &#34;subTask&#34;: {
                        &#34;subTaskType&#34;: 1, &#34;operationType&#34;: 4017
                    },
                    &#34;options&#34;: {
                        &#34;adminOpts&#34;: {
                            &#34;contentIndexingOption&#34;: {
                                &#34;subClientBasedAnalytics&#34;: False
                            }, &#34;libraryOption&#34;: {
                                &#34;operation&#34;: 15, &#34;media&#34;: [
                                ], &#34;library&#34;: {
                                    &#34;libraryName&#34;: &#34;&#34;, &#34;_type_&#34;: 9, &#34;libraryId&#34;: library_id
                                }, &#34;catalogMedia&#34;: {
                                    &#34;fileMarkerToStart&#34;: 0, &#34;fileMarkerToEnd&#34;: 0, &#34;reCatalog&#34;: True,
                                    &#34;maxNumOfDrives&#34;: 1,
                                    &#34;spareGroupId&#34;: 0,
                                    &#34;merge&#34;: True,
                                    &#34;subTaskType&#34;: 2,
                                    &#34;drivePoolEntity&#34;: {
                                        &#34;_type_&#34;: 47, &#34;drivePoolId&#34;: drive_pool_id
                                    }
                                }, &#34;mediaAgent&#34;: {
                                    &#34;mediaAgentId&#34;: 2, &#34;_type_&#34;: 11
                                }
                            }
                        }, &#34;restoreOptions&#34;: {
                            &#34;virtualServerRstOption&#34;: {
                                &#34;isBlockLevelReplication&#34;: False
                            }, &#34;commonOptions&#34;: {
                                &#34;syncRestore&#34;: False
                            }
                        }
                    }
                }
            ]
        }
    }

    sub_dict = tape_import_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;adminOpts&#34;][&#34;libraryOption&#34;][&#34;media&#34;]

    for media in medias_id:
        temp_dict = {&#34;_type_&#34;: 46, &#34;mediaId&#34;: int(media), &#34;mediaName&#34;: &#34;&#34;}
        sub_dict.append(temp_dict)

    flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;,
                                                       self._services[&#39;RESTORE&#39;],
                                                       tape_import_json)

    if flag:
        if response.json() and &#39;jobIds&#39; in response.json():
            return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])
        elif response.json() and &#39;errorCode&#39; in response.json():
            raise SDKException(&#39;CommCellMigration&#39;, &#39;102&#39;, &#39;Tape Import job failed with error code : &#39; +
                               str(response.json()[&#39;errorCode&#39;]))
    else:
        response_string = self._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.commcell_migration.GlobalRepositoryCell"><code class="flex name class">
<span>class <span class="ident">GlobalRepositoryCell</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing the GRC feature from commcell</p>
<p>Initializes the object of GlobalRepositoryCell class</p>
<h2 id="args">Args</h2>
<p>commcell_object (Commcell)
-
Commcell class instance</p>
<h2 id="returns">Returns</h2>
<p>grc (GlobalRepositoryCell) - instance of the GlobalRepositoryCell class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/commcell_migration.py#L564-L853" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class GlobalRepositoryCell:
    &#34;&#34;&#34;Class for representing the GRC feature from commcell&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;
        Initializes the object of GlobalRepositoryCell class

        Args:
            commcell_object (Commcell)  -   Commcell class instance

        Returns:
            grc (GlobalRepositoryCell) - instance of the GlobalRepositoryCell class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._cvpysdk_object = self._commcell_object._cvpysdk_object
        self._services = self._commcell_object._services
        self._commcell_name = self._commcell_object.commserv_name

    def _get_task_details(self, task_id):
        &#34;&#34;&#34;
        Util for getting XML of GRC schedule task (required for generating more XMLs)

        Args:
            task_id     (int)   -   id of grc schedule&#39;s task

        Returns:
            task_xml    (str)   -   xml form string with grc schedule details
            Example:
                &lt;TMMsg_GetTaskDetailResp&gt;
                        &lt;taskInfo&gt;
                                &lt;task taskId=&#34;&#34; taskName=&#34;&#34; &gt; ... &lt;/task&gt;
                        &lt;appGroup/&gt;
                        &lt;subTasks&gt;
                            &lt;subTask subTaskId=&#34;&#34; subTaskType=&#34;&#34; ...&gt;
                            &lt;options&gt;
                                &lt;backupOpts backupLevel=&#34;&#34;&gt;
                                    &lt;dataOpt autoCopy=&#34;&#34;/&gt;
                                &lt;/backupOpts&gt;
                                &lt;adminOpts&gt;
                                    &lt;ccmOption&gt;
                                        &lt;mergeOptions ...&gt;
                                        &lt;captureOptions ...&gt;
                                            ...
                                        &lt;/captureOptions&gt;
                                    &lt;/ccmOption&gt;
                                &lt;/adminOpts&gt;
                            &lt;/options&gt;
                            &lt;pattern ...&gt;...&lt;/pattern&gt;
                        &lt;/subTasks&gt;
                    &lt;/taskInfo&gt;
                &lt;/TMMsg_GetTaskDetailResp&gt;
        &#34;&#34;&#34;
        get_task_xml = f&#39;&lt;TMMsg_GetTaskDetailReq taskId=&#34;{task_id}&#34;/&gt;&#39;
        return self._commcell_object.qoperation_execute(get_task_xml, return_xml=True)

    def _get_commcell_from_id(self, commcell_id):
        &#34;&#34;&#34;
        Util to get registered commcell name from given commcell id

        Args:
            commcell_id (int)   -   id of commcell

        Returns:
            commcell_name   (str)   -   name of commcell
        &#34;&#34;&#34;
        for commcell_name, commcell_data in self._commcell_object.registered_commcells.items():
            if commcell_data.get(&#39;commCell&#39;, {}).get(&#39;commCellId&#39;) == commcell_id:
                return commcell_name

    def _modify_task_props(self, podcell_properties, task_xml):
        &#34;&#34;&#34;
        Util for modifying task properties, after grc properties are updated

        Args:
            podcell_properties  (dict)  -   the dict returned by get_podcell_properties
            task_xml    (str)           -   the xml returned for grc schedule&#39;s task info

        Returns:
            response    (dict)   -   the response from execute qoperation
        &#34;&#34;&#34;
        grc_schedule_xml = ET.fromstring(podcell_properties[&#39;schedule_xml&#39;])
        task_info_xml = ET.fromstring(task_xml)
        modify_task_xml = &#34;&#34;&#34;
        &lt;TMMsg_ModifyTaskReq&gt;
            &lt;taskInfo&gt;
                &lt;task initiatedFrom=&#34;1&#34; ownerId=&#34;{0}&#34; ownerName=&#34;{1}&#34; policyType=&#34;0&#34; sequenceNumber=&#34;0&#34; taskId=&#34;{2}&#34; taskType=&#34;2&#34;&gt;
                    &lt;taskFlags disabled=&#34;0&#34; isEZOperation=&#34;0&#34; isEdgeDrive=&#34;0&#34;/&gt;
                &lt;/task&gt;
                &lt;appGroup/&gt;
                {3}
            &lt;/taskInfo&gt;
        &lt;/TMMsg_ModifyTaskReq&gt;
        &#34;&#34;&#34;
        modify_task_xml = modify_task_xml.format(
            grc_schedule_xml.find(&#39;taskInfo/task&#39;).get(&#39;ownerId&#39;),
            grc_schedule_xml.find(&#39;taskInfo/task&#39;).get(&#39;ownerName&#39;),
            podcell_properties[&#39;task_id&#39;],
            ET.tostring(task_info_xml.find(&#39;taskInfo/subTasks&#39;), encoding=&#39;unicode&#39;)
        )
        return self._commcell_object.qoperation_execute(modify_task_xml)

    def _get_podcell_entities(self, podcell_name: str = None, podcell_id: int = None, podcell_guid: str = None):
        &#34;&#34;&#34;
        Gets the entities in podcell available for monitoring via GRC

        Args:
            podcell_name    (str)   -   name of pod cell
            podcell_id      (int)   -   id of podcell
            podcell_guid    (str)   -   guid of podcell (Optional)

        Returns:
            monitor_entities    (str)   -   all entities of pod cell in XML format
            Example:
                &lt;EVGui_CCMCommCellInfo commcellName=&#34;&#34; commcellNumber=&#34;&#34; commcellId=&#34;&#34;&gt;
                    &lt;clientEntityLst clientId=&#34;&#34; clientName=&#34;&#34;&gt;
                        ...
                    &lt;/clientEntityLst&gt;
                    &lt;clientEntityLst clientId=&#34;&#34; clientName=&#34;&#34;&gt;
                        ...
                    &lt;/clientEntityLst&gt;
                    &lt;clientEntityLst clientId=&#34;&#34; ...&gt;
                        &lt;appTypeEntityList ... appTypeId=&#34;&#34;&gt;
                            &lt;instanceList ... instanceId=&#34;&#34;&gt;
                                &lt;backupSetList ... backupsetId=&#34;&#34;&gt;
                                    &lt;subclientList ... subclientId=&#34;&#34;/&gt;
                                &lt;/backupSetList&gt;
                            &lt;/instanceList&gt;
                        &lt;/appTypeEntityList&gt;
                    &lt;/clientEntityLst&gt;
                    &lt;clientComputerGrp clientGroupId=&#34;&#34; clientGroupName=&#34;&#34;/&gt;
                    ...
                    &lt;clientComputerGrp clientGroupId=&#34;&#34; clientGroupName=&#34;&#34;/&gt;
                &lt;/EVGui_CCMCommCellInfo&gt;
        &#34;&#34;&#34;
        if podcell_id is None:
            if podcell_name is None:
                raise SDKException(&#39;GlobalRepositoryCell&#39;, &#39;103&#39;)
            podcell_id = self._commcell_object.registered_commcells.get(podcell_name, {}) \
                .get(&#39;commCell&#39;, {}).get(&#39;commCellId&#39;)
            if podcell_id is None:
                raise SDKException(&#39;GlobalRepositoryCell&#39;, &#39;104&#39;, f&#39;for podcell: {podcell_name}&#39;)
        if podcell_name is None:
            podcell_name = self._get_commcell_from_id(podcell_id)
        if podcell_guid is None:
            podcell_guid = self._commcell_object.registered_commcells[podcell_name].get(&#39;commCell&#39;, {}).get(&#39;csGUID&#39;)

        entities_xml = &#34;&#34;&#34;
        &lt;EVGui_GetCCMExportInfo exportMsgType=&#34;3&#34; strCSName=&#34;{0}*{0}*8400&#34;&gt;
            &lt;mediaAgent _type_=&#34;3&#34;/&gt;
            &lt;userInfo/&gt;
            &lt;commCell _type_=&#34;1&#34; commCellId=&#34;{1}&#34; commCellName=&#34;{0}&#34; csGUID=&#34;{2}&#34;/&gt;
        &lt;/EVGui_GetCCMExportInfo&gt;
        &#34;&#34;&#34;
        exec_xml = entities_xml.format(podcell_name, podcell_id, podcell_guid)
        resp = self._commcell_object.qoperation_execute(exec_xml)
        return resp.get(&#39;strXmlInfo&#39;)

    def get_clients_for_migration(self, podcell_name: str = None, podcell_id: int = None, podcell_guid: str = None):
        &#34;&#34;&#34;
        Gets the podcell clients that can be migrated
        
        Args:
            podcell_name    (str)   -   name of pod cell
            podcell_id      (int)   -   id of podcell
            podcell_guid    (str)   -   guid of podcell (Optional)
        
        Returns:
            clients_dict    (dict)  -   dict with client ID as key and client name value
            Example:
                {
                    X: &#34;clientA&#34;,
                    Y: &#34;clientB&#34;,
                    Z: &#34;clienta&#34;
                }
        &#34;&#34;&#34;
        clients_dict = {}
        entities_xml = self._get_podcell_entities(
            podcell_name=podcell_name,
            podcell_id=podcell_id,
            podcell_guid=podcell_guid
        )
        entities_xml = ET.fromstring(entities_xml)
        for client_node in entities_xml.findall(&#39;clientEntityLst&#39;):
            cl_id = client_node.get(&#39;clientId&#39;)
            cl_name = client_node.get(&#39;clientName&#39;)
            clients_dict[cl_id] = cl_name
        return clients_dict

    def _get_podcell_properties(self, podcell_name: str = None, podcell_id: int = None):
        &#34;&#34;&#34;
        Gets the GRC properties of given pod cell

        Args:
            podcell_name    (str)   -   name of pod cell
            podcell_id      (int)   -   id of podcell

        Returns:
            podcell_properties  (dict)  -   different properties of pod cell in dict format with xml values
        &#34;&#34;&#34;
        # TODO: Update grc properties map
        grc_prop_map = {
            2: &#39;podcell_name&#39;,
            4: &#39;schedule_xml&#39;,
            15: &#39;entities_xml&#39;,
            16: &#39;libraries_xml&#39;,
            19: &#39;task_id&#39;
        }
        if podcell_id is None:
            if podcell_name is None:
                raise SDKException(&#39;GlobalRepositoryCell&#39;, &#39;103&#39;)
            podcell_id = self._commcell_object.registered_commcells.get(podcell_name, {}) \
                .get(&#39;commCell&#39;, {}).get(&#39;commCellId&#39;)
            if podcell_id is None:
                raise SDKException(&#39;GlobalRepositoryCell&#39;, &#39;104&#39;, f&#39;for podcell: {podcell_name}&#39;)
        grc_props_xml = f&#39;&lt;App_GetGRCCommCellPropsReq commcellId=&#34;{podcell_id}&#34;/&gt;&#39;
        grc_props_response = self._commcell_object.qoperation_execute(grc_props_xml)
        podcell_properties = {
            grc_prop_map.get(prop.get(&#39;propId&#39;), prop.get(&#39;propId&#39;)): prop.get(&#39;stringVal&#39;) or prop.get(&#39;numVal&#39;)
            for prop in grc_props_response[&#39;grcCommcellPropList&#39;]
        }
        return podcell_properties

    def modify_monitored_clients(self, podcell_name: str = None, podcell_id: int = None, clients: list = None):
        &#34;&#34;&#34;
        Modifies (overwrites) the monitored clients in grc properties for given podcell

        Args:
            podcell_name    (str)   -   name of pod cell
            podcell_id      (int)   -   id of podcell
            client_ids      (list)  -   list of client ids, names
                                        or Client objects (of pod cell)

        Returns:
            None
        &#34;&#34;&#34;
        if podcell_id is None:
            if podcell_name is None:
                raise SDKException(&#39;GlobalRepositoryCell&#39;, &#39;103&#39;)
            podcell_id = self._commcell_object.registered_commcells.get(podcell_name, {}) \
                .get(&#39;commCell&#39;, {}).get(&#39;commCellId&#39;)
            if podcell_id is None:
                raise SDKException(&#39;GlobalRepositoryCell&#39;, &#39;104&#39;, f&#39;for podcell: {podcell_name}&#39;)

        set_grc_xml = &#34;&#34;&#34;
            &lt;App_SetGRCCommCellPropsReq commcellId=&#34;{0}&#34;&gt;
                &lt;grcCommcellProp numVal=&#34;0&#34; propId=&#34;4&#34; stringVal=&#34;{1}&#34;/&gt;
                &lt;grcCommcellProp numVal=&#34;1&#34; propId=&#34;1&#34; stringVal=&#34;&#34;/&gt;
                &lt;grcCommcellProp numVal=&#34;0&#34; propId=&#34;2&#34; stringVal=&#34;{2}&#34;/&gt;
                &lt;grcCommcellProp numVal=&#34;0&#34; propId=&#34;15&#34; stringVal=&#34;{3}&#34;/&gt;
                &lt;grcCommcellProp numVal=&#34;1&#34; propId=&#34;8&#34; stringVal=&#34;&#34;/&gt;
                &lt;grcCommcellProp numVal=&#34;0&#34; propId=&#34;14&#34; stringVal=&#34;&#34;/&gt;
            &lt;/App_SetGRCCommCellPropsReq&gt;
        &#34;&#34;&#34;
        xml_header = &#39;&lt;?xml version=\&#39;1.0\&#39; encoding=\&#39;UTF-8\&#39;?&gt;&#39;
        cc_props = self._get_podcell_properties(podcell_id=podcell_id)
        podcell_name = cc_props[&#39;podcell_name&#39;]
        task_xml = self._get_task_details(task_id=cc_props[&#39;task_id&#39;])
        podcell_entities = self._get_podcell_entities(podcell_id=podcell_id)
        entities_xml = ET.fromstring(podcell_entities)
        client_ids = []
        if isinstance(clients[0], str):
            for client_node in entities_xml.findall(&#39;clientEntityLst&#39;):
                if client_node.get(&#39;clientName&#39;) in clients:
                    client_ids.append(client_node.get(&#39;clientId&#39;))
        elif isinstance(clients[0], int):
            client_ids = clients
        elif isinstance(clients[0], Client):
            client_ids = [int(cl.client_id) for cl in clients]

        # Generate nested XML 1 (selected clients)
        current_schedule = ET.fromstring(cc_props[&#39;schedule_xml&#39;])
        capture_options = current_schedule.find(&#39;taskInfo/subTasks/options/adminOpts/ccmOption/captureOptions&#39;)
        # remove all &lt;entities ...&gt; tags
        for entity_node in capture_options.findall(&#39;entities&#39;):
            capture_options.remove(entity_node)
        # insert &lt;entities ...&gt; tags for selected client_ids
        for clid in client_ids:
            capture_options.insert(0, ET.Element(&#39;entities&#39;, {&#39;clientId&#39;: str(clid), &#39;_type_&#39;: &#39;3&#39;}))
        nested_xml1 = ET.tostring(current_schedule, encoding=&#39;unicode&#39;)
        nested_xml1 = html.escape(f&#39;{xml_header}{nested_xml1}&#39;)

        # Generate nested XML 2 (all clients in podcell)
        entities_xml = ET.fromstring(podcell_entities)
        nested_xml2 = ET.tostring(entities_xml, encoding=&#39;unicode&#39;)
        nested_xml2 = html.escape(nested_xml2)

        # Combine nested XMLs into parent XML
        final_xml = set_grc_xml.format(podcell_id, nested_xml1, podcell_name, nested_xml2)
        self._commcell_object.qoperation_execute(final_xml)
        self._modify_task_props(cc_props, task_xml)</code></pre>
</details>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.commcell_migration.GlobalRepositoryCell.get_clients_for_migration"><code class="name flex">
<span>def <span class="ident">get_clients_for_migration</span></span>(<span>self, podcell_name: str = None, podcell_id: int = None, podcell_guid: str = None)</span>
</code></dt>
<dd>
<div class="desc"><p>Gets the podcell clients that can be migrated</p>
<h2 id="args">Args</h2>
<p>podcell_name
(str)
-
name of pod cell
podcell_id
(int)
-
id of podcell
podcell_guid
(str)
-
guid of podcell (Optional)</p>
<h2 id="returns">Returns</h2>
<dl>
<dt>clients_dict
(dict)
-
dict with client ID as key and client name value</dt>
<dt>Example:</dt>
<dt>{</dt>
<dt><code>
X</code></dt>
<dd>"clientA",
Y: "clientB",
Z: "clienta"
}</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/commcell_migration.py#L721-L750" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_clients_for_migration(self, podcell_name: str = None, podcell_id: int = None, podcell_guid: str = None):
    &#34;&#34;&#34;
    Gets the podcell clients that can be migrated
    
    Args:
        podcell_name    (str)   -   name of pod cell
        podcell_id      (int)   -   id of podcell
        podcell_guid    (str)   -   guid of podcell (Optional)
    
    Returns:
        clients_dict    (dict)  -   dict with client ID as key and client name value
        Example:
            {
                X: &#34;clientA&#34;,
                Y: &#34;clientB&#34;,
                Z: &#34;clienta&#34;
            }
    &#34;&#34;&#34;
    clients_dict = {}
    entities_xml = self._get_podcell_entities(
        podcell_name=podcell_name,
        podcell_id=podcell_id,
        podcell_guid=podcell_guid
    )
    entities_xml = ET.fromstring(entities_xml)
    for client_node in entities_xml.findall(&#39;clientEntityLst&#39;):
        cl_id = client_node.get(&#39;clientId&#39;)
        cl_name = client_node.get(&#39;clientName&#39;)
        clients_dict[cl_id] = cl_name
    return clients_dict</code></pre>
</details>
</dd>
<dt id="cvpysdk.commcell_migration.GlobalRepositoryCell.modify_monitored_clients"><code class="name flex">
<span>def <span class="ident">modify_monitored_clients</span></span>(<span>self, podcell_name: str = None, podcell_id: int = None, clients: list = None)</span>
</code></dt>
<dd>
<div class="desc"><p>Modifies (overwrites) the monitored clients in grc properties for given podcell</p>
<h2 id="args">Args</h2>
<p>podcell_name
(str)
-
name of pod cell
podcell_id
(int)
-
id of podcell
client_ids
(list)
-
list of client ids, names
or Client objects (of pod cell)</p>
<h2 id="returns">Returns</h2>
<p>None</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/commcell_migration.py#L786-L853" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def modify_monitored_clients(self, podcell_name: str = None, podcell_id: int = None, clients: list = None):
    &#34;&#34;&#34;
    Modifies (overwrites) the monitored clients in grc properties for given podcell

    Args:
        podcell_name    (str)   -   name of pod cell
        podcell_id      (int)   -   id of podcell
        client_ids      (list)  -   list of client ids, names
                                    or Client objects (of pod cell)

    Returns:
        None
    &#34;&#34;&#34;
    if podcell_id is None:
        if podcell_name is None:
            raise SDKException(&#39;GlobalRepositoryCell&#39;, &#39;103&#39;)
        podcell_id = self._commcell_object.registered_commcells.get(podcell_name, {}) \
            .get(&#39;commCell&#39;, {}).get(&#39;commCellId&#39;)
        if podcell_id is None:
            raise SDKException(&#39;GlobalRepositoryCell&#39;, &#39;104&#39;, f&#39;for podcell: {podcell_name}&#39;)

    set_grc_xml = &#34;&#34;&#34;
        &lt;App_SetGRCCommCellPropsReq commcellId=&#34;{0}&#34;&gt;
            &lt;grcCommcellProp numVal=&#34;0&#34; propId=&#34;4&#34; stringVal=&#34;{1}&#34;/&gt;
            &lt;grcCommcellProp numVal=&#34;1&#34; propId=&#34;1&#34; stringVal=&#34;&#34;/&gt;
            &lt;grcCommcellProp numVal=&#34;0&#34; propId=&#34;2&#34; stringVal=&#34;{2}&#34;/&gt;
            &lt;grcCommcellProp numVal=&#34;0&#34; propId=&#34;15&#34; stringVal=&#34;{3}&#34;/&gt;
            &lt;grcCommcellProp numVal=&#34;1&#34; propId=&#34;8&#34; stringVal=&#34;&#34;/&gt;
            &lt;grcCommcellProp numVal=&#34;0&#34; propId=&#34;14&#34; stringVal=&#34;&#34;/&gt;
        &lt;/App_SetGRCCommCellPropsReq&gt;
    &#34;&#34;&#34;
    xml_header = &#39;&lt;?xml version=\&#39;1.0\&#39; encoding=\&#39;UTF-8\&#39;?&gt;&#39;
    cc_props = self._get_podcell_properties(podcell_id=podcell_id)
    podcell_name = cc_props[&#39;podcell_name&#39;]
    task_xml = self._get_task_details(task_id=cc_props[&#39;task_id&#39;])
    podcell_entities = self._get_podcell_entities(podcell_id=podcell_id)
    entities_xml = ET.fromstring(podcell_entities)
    client_ids = []
    if isinstance(clients[0], str):
        for client_node in entities_xml.findall(&#39;clientEntityLst&#39;):
            if client_node.get(&#39;clientName&#39;) in clients:
                client_ids.append(client_node.get(&#39;clientId&#39;))
    elif isinstance(clients[0], int):
        client_ids = clients
    elif isinstance(clients[0], Client):
        client_ids = [int(cl.client_id) for cl in clients]

    # Generate nested XML 1 (selected clients)
    current_schedule = ET.fromstring(cc_props[&#39;schedule_xml&#39;])
    capture_options = current_schedule.find(&#39;taskInfo/subTasks/options/adminOpts/ccmOption/captureOptions&#39;)
    # remove all &lt;entities ...&gt; tags
    for entity_node in capture_options.findall(&#39;entities&#39;):
        capture_options.remove(entity_node)
    # insert &lt;entities ...&gt; tags for selected client_ids
    for clid in client_ids:
        capture_options.insert(0, ET.Element(&#39;entities&#39;, {&#39;clientId&#39;: str(clid), &#39;_type_&#39;: &#39;3&#39;}))
    nested_xml1 = ET.tostring(current_schedule, encoding=&#39;unicode&#39;)
    nested_xml1 = html.escape(f&#39;{xml_header}{nested_xml1}&#39;)

    # Generate nested XML 2 (all clients in podcell)
    entities_xml = ET.fromstring(podcell_entities)
    nested_xml2 = ET.tostring(entities_xml, encoding=&#39;unicode&#39;)
    nested_xml2 = html.escape(nested_xml2)

    # Combine nested XMLs into parent XML
    final_xml = set_grc_xml.format(podcell_id, nested_xml1, podcell_name, nested_xml2)
    self._commcell_object.qoperation_execute(final_xml)
    self._modify_task_props(cc_props, task_xml)</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.commcell_migration.CommCellMigration" href="#cvpysdk.commcell_migration.CommCellMigration">CommCellMigration</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.commcell_migration.CommCellMigration.commcell_export" href="#cvpysdk.commcell_migration.CommCellMigration.commcell_export">commcell_export</a></code></li>
<li><code><a title="cvpysdk.commcell_migration.CommCellMigration.commcell_import" href="#cvpysdk.commcell_migration.CommCellMigration.commcell_import">commcell_import</a></code></li>
<li><code><a title="cvpysdk.commcell_migration.CommCellMigration.tape_import" href="#cvpysdk.commcell_migration.CommCellMigration.tape_import">tape_import</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.commcell_migration.GlobalRepositoryCell" href="#cvpysdk.commcell_migration.GlobalRepositoryCell">GlobalRepositoryCell</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.commcell_migration.GlobalRepositoryCell.get_clients_for_migration" href="#cvpysdk.commcell_migration.GlobalRepositoryCell.get_clients_for_migration">get_clients_for_migration</a></code></li>
<li><code><a title="cvpysdk.commcell_migration.GlobalRepositoryCell.modify_monitored_clients" href="#cvpysdk.commcell_migration.GlobalRepositoryCell.modify_monitored_clients">modify_monitored_clients</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>