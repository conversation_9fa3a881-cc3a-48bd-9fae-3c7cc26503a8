<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.metricsreport API documentation</title>
<meta name="description" content="File for performing Metrics operations …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.metricsreport</code></h1>
</header>
<section id="section-intro">
<p>File for performing Metrics operations.</p>
<p>_Metrics
: Class for representing all common operations on Metrics Reporting
PrivateMetrics
: Class for representing Private Metrics and performing operations on it.
PublicMetrics
: Class for representing Public Metrics and performing operations on it.</p>
<p>use method save_config() or upload_now() to save the updated configurations.</p>
<h2 id="metrics">Metrics</h2>
<p><strong>init</strong>(Commcell_object, isprivate)&ndash;
initialise with object of CommCell and flag to
specificy metrics Type</p>
<p><strong>repr</strong>()
&ndash;
returns the string to represent the instance of the
Metrics class
enable_health()
&ndash;
enables Health service</p>
<p>disable_health()
&ndash;
disables Health service</p>
<p>enable_activity()
&ndash;
enables Activity service</p>
<p>disable_activity()
&ndash;
disables Activity service</p>
<p>enable_audit()
&ndash;
enables Audit service</p>
<p>disable_audit()
&ndash;
disables Audit service</p>
<p>disable_chargeback()
&ndash;
disables Chargeback service</p>
<p>enable_post_upgrade_check()
&ndash; enables enable_post_upgrade_check Service</p>
<p>enable_all_services()
&ndash; enables All Service in metrics</p>
<p>disable_all_services()
&ndash; disables All Service</p>
<p>enable_metrics()
&ndash; enables Metrics Service</p>
<p>disable_metrics()
&ndash; disables Metrics Service in CommServe</p>
<p>set_upload_freq()
&ndash;
updates the upload frequency</p>
<p>set_data_collection_window
&ndash; updates the data collection window</p>
<p>remove_data_collection_window&ndash; removes data collection window</p>
<p>set_all_clientgroup()
&ndash; updates metrics configuration with all client groups</p>
<p>set_clientgroups()
&ndash; sets the client groups for metrics</p>
<p>save_config()
&ndash; updates the configuration of Metrics, this must be
called to save the configuration changes made in this object</p>
<p>upload_now()
&ndash; Performs Upload Now operation of metrics</p>
<p>wait_for_download_completion()&ndash; waits for metrics download operation to complete</p>
<p>wait_for_collection_completion&ndash; waits for metrics collection operation to complete</p>
<p>wait_for_upload_completion()
&ndash; waits for metrics upload operation to complete</p>
<p>wait_for_uploadnow_completion()&ndash; waits for complete metrics operation to complete</p>
<p>get_possible_uploaded_filenames&ndash; gives the possible names for the uploaded files</p>
<p>refresh()
&ndash; refresh the properties and config of the Metrics Server
get_uploaded_filename()
&ndash; Gets last uploaded file name
get_uploaded_zip_filename()
&ndash; Gets last uploaded zip file name</p>
<h2 id="privatemetrics">Privatemetrics</h2>
<p><strong>init</strong>(Commcell_object)
&ndash;
initialise with object of CommCell</p>
<p>update_url(hostname)
&ndash;
Updates Metrics URL for download and upload</p>
<p>enable_chargeback(daily, weekly, monthly)
&ndash;
enables chargeback service</p>
<h2 id="publicmetrics">Publicmetrics</h2>
<p><strong>init</strong>(Commcell_object)
&ndash;
initialise with object of CommCell</p>
<p>enable_chargeback()
&ndash;
enables chargeback service</p>
<p>enable_upgrade_readiness()
&ndash; Enables pre upgrade readiness service</p>
<p>disable_upgrade_readiness() &ndash; disables pre upgrade readiness service</p>
<p>enable_proactive_support()
&ndash; Enables Proactive Support service</p>
<p>disable_proactive_support() &ndash; disables Proactive Support service</p>
<p>enable_cloud_assist()
&ndash; Enables Cloud Assist service</p>
<p>disable_cloud_assist()
&ndash; disables Cloud Assist service</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/metricsreport.py#L1-L689" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;File for performing Metrics operations.

_Metrics        : Class for representing all common operations on Metrics Reporting
PrivateMetrics  : Class for representing Private Metrics and performing operations on it.
PublicMetrics   : Class for representing Public Metrics and performing operations on it.

use method save_config() or upload_now() to save the updated configurations.

Metrics:
    __init__(Commcell_object, isprivate)--  initialise with object of CommCell and flag to
                                            specificy metrics Type

    __repr__()                   --  returns the string to represent the instance of the
                                            Metrics class
    enable_health()              --  enables Health service

    disable_health()             --  disables Health service

    enable_activity()            --  enables Activity service

    disable_activity()           --  disables Activity service

    enable_audit()               --  enables Audit service

    disable_audit()              --  disables Audit service

    disable_chargeback()         --  disables Chargeback service

    enable_post_upgrade_check()  -- enables enable_post_upgrade_check Service

    enable_all_services()        -- enables All Service in metrics

    disable_all_services()       -- disables All Service

    enable_metrics()             -- enables Metrics Service

    disable_metrics()            -- disables Metrics Service in CommServe

    set_upload_freq()            --  updates the upload frequency

    set_data_collection_window   -- updates the data collection window

    remove_data_collection_window-- removes data collection window

    set_all_clientgroup()        -- updates metrics configuration with all client groups

    set_clientgroups()           -- sets the client groups for metrics

    save_config()                -- updates the configuration of Metrics, this must be
                                    called to save the configuration changes made in this object

    upload_now()                 -- Performs Upload Now operation of metrics

    wait_for_download_completion()-- waits for metrics download operation to complete

    wait_for_collection_completion-- waits for metrics collection operation to complete

    wait_for_upload_completion()  -- waits for metrics upload operation to complete

    wait_for_uploadnow_completion()-- waits for complete metrics operation to complete

    get_possible_uploaded_filenames-- gives the possible names for the uploaded files

    refresh()                      -- refresh the properties and config of the Metrics Server
    get_uploaded_filename()        -- Gets last uploaded file name
    get_uploaded_zip_filename()    -- Gets last uploaded zip file name
PrivateMetrics:
    __init__(Commcell_object)   --  initialise with object of CommCell

    update_url(hostname)        --  Updates Metrics URL for download and upload

    enable_chargeback(daily, weekly, monthly)
                                --  enables chargeback service

PublicMetrics:
    __init__(Commcell_object)   --  initialise with object of CommCell

    enable_chargeback()         --  enables chargeback service

    enable_upgrade_readiness()  -- Enables pre upgrade readiness service

    disable_upgrade_readiness() -- disables pre upgrade readiness service

    enable_proactive_support()  -- Enables Proactive Support service

    disable_proactive_support() -- disables Proactive Support service

    enable_cloud_assist()       -- Enables Cloud Assist service

    disable_cloud_assist()      -- disables Cloud Assist service

&#34;&#34;&#34;

from __future__ import absolute_import
from __future__ import unicode_literals
from time import sleep
from urllib.parse import urlparse

from cvpysdk.license import LicenseDetails
from .exception import SDKException


class _Metrics(object):
    &#34;&#34;&#34;Class for common operations in Metrics reporting
    this will be inherited by Private and Cloud metrics&#34;&#34;&#34;

    def __init__(self, commcell_object, isprivate):
        self._commcell_object = commcell_object
        self._isprivate = isprivate
        self._METRICS = self._commcell_object._services[&#39;METRICS&#39;]
        self._GET_METRICS = self._commcell_object._services[&#39;GET_METRICS&#39;] % self._isprivate
        self._enable_service = True
        self._disable_service = False
        self._get_metrics_config()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the UserGroups class.&#34;&#34;&#34;
        if self._isprivate == 1:
            metrics_type = &#39;Private&#39;
        else:
            metrics_type = &#39;Public&#39;
        return &#34;{0} Metrics class instance with config &#39;{1}&#39;&#34;.format(
            metrics_type,
            self._metrics_config
        )

    def _get_metrics_config(self):
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._GET_METRICS
        )
        if flag:
            self._metrics_config = response.json()
            self._metrics_config.update({&#39;isPrivateCloud&#39;: bool(self._isprivate == 1)})
            if self._metrics_config and &#39;config&#39; in self._metrics_config:
                # get services
                self.services = {}
                self._cloud = self._metrics_config[&#39;config&#39;][&#39;cloud&#39;]
                self._service_list = self._cloud[&#39;serviceList&#39;]
                for service in self._service_list:
                    service_name = service[&#39;service&#39;][&#39;name&#39;]
                    status = service[&#39;enabled&#39;]
                    self.services[service_name] = status
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response.text)

    def refresh(self):
        &#34;&#34;&#34;updates metrics object with the latest configuration&#34;&#34;&#34;
        self._get_metrics_config()

    def _update_service_state(self, service_name, state):
        for idx, service in enumerate(self._service_list):
            if service[&#39;service&#39;][&#39;name&#39;] == service_name:
                self._service_list[idx][&#39;enabled&#39;] = state
                self.services[service_name] = state

    @property
    def lastdownloadtime(self):
        &#34;&#34;&#34;Returns last download time in unix time format&#34;&#34;&#34;
        return self._metrics_config[&#39;config&#39;][&#39;scriptDownloadTime&#39;]

    @property
    def lastcollectiontime(self):
        &#34;&#34;&#34;Returns last collection time in unix time format&#34;&#34;&#34;
        return self._metrics_config[&#39;config&#39;][&#39;lastCollectionTime&#39;]

    @property
    def lastuploadtime(self):
        &#34;&#34;&#34;Returns last upload time in unix time format&#34;&#34;&#34;
        return self._metrics_config[&#39;config&#39;][&#39;lastUploadTime&#39;]

    @property
    def nextuploadtime(self):
        &#34;&#34;&#34;Returns last Next time in unix time format&#34;&#34;&#34;
        return self._metrics_config[&#39;config&#39;][&#39;nextUploadTime&#39;]

    @property
    def uploadfrequency(self):
        &#34;&#34;&#34;Returns last Next time in unix time format&#34;&#34;&#34;
        return self._metrics_config[&#39;config&#39;][&#39;uploadFrequency&#39;]

    def enable_health(self):
        &#34;&#34;&#34;enables Health Service&#34;&#34;&#34;
        if self.services[&#39;Health Check&#39;] is not True:
            self._update_service_state(&#39;Health Check&#39;, self._enable_service)

    def disable_health(self):
        &#34;&#34;&#34;disables Health Service&#34;&#34;&#34;
        if self.services[&#39;Health Check&#39;] is True:
            self._update_service_state(&#39;Health Check&#39;, self._disable_service)

    def enable_activity(self):
        &#34;&#34;&#34;enables Activity Service&#34;&#34;&#34;
        if self.services[&#39;Activity&#39;] is not True:
            self._update_service_state(&#39;Activity&#39;, self._enable_service)

    def disable_activity(self):
        &#34;&#34;&#34;disables Activity Service&#34;&#34;&#34;
        if self.services[&#39;Activity&#39;] is True:
            self._update_service_state(&#39;Activity&#39;, self._disable_service)

    def enable_audit(self):
        &#34;&#34;&#34;enables Audit Service&#34;&#34;&#34;
        if self.services[&#39;Audit&#39;] is not True:
            self._update_service_state(&#39;Audit&#39;, self._enable_service)

    def disable_audit(self):
        &#34;&#34;&#34;disables Audit Service&#34;&#34;&#34;
        if self.services[&#39;Audit&#39;] is True:
            self._update_service_state(&#39;Audit&#39;, self._disable_service)

    def enable_post_upgrade_check(self):
        &#34;&#34;&#34;enables post_upgrade_check Service&#34;&#34;&#34;
        if self.services[&#39;Post Upgrade Check&#39;] is not True:
            self._update_service_state(&#39;Post Upgrade Check&#39;, self._enable_service)

    def disables_post_upgrade_check(self):
        &#34;&#34;&#34;disables post_upgrade_check Service&#34;&#34;&#34;
        if self.services[&#39;Post Upgrade Check&#39;] is True:
            self._update_service_state(&#39;Post Upgrade Check&#39;, self._disable_service)

    def disables_chargeback(self):
        &#34;&#34;&#34;disables post_upgrade_check Service&#34;&#34;&#34;
        if self.services[&#39;Charge Back&#39;] is True:
            self._update_service_state(&#39;Charge Back&#39;, self._disable_service)

    def enable_all_services(self):
        &#34;&#34;&#34;enables All Service&#34;&#34;&#34;
        for index, service in enumerate(self._service_list):
            if service[&#39;service&#39;][&#39;name&#39;] not in [&#39;Post Upgrade Check&#39;, &#39;Upgrade Readiness&#39;]:
                self._service_list[index][&#39;enabled&#39;] = self._enable_service
                service_name = service[&#39;service&#39;][&#39;name&#39;]
                self.services[service_name] = self._enable_service

    def disable_all_services(self):
        &#34;&#34;&#34;disables All Service&#34;&#34;&#34;
        for index, service in enumerate(self._service_list):
            if service[&#39;service&#39;][&#39;name&#39;] not in [&#39;Post Upgrade Check&#39;, &#39;Upgrade Readiness&#39;]:
                self._service_list[index][&#39;enabled&#39;] = self._disable_service
                service_name = service[&#39;service&#39;][&#39;name&#39;]
                self.services[service_name] = self._disable_service

    def set_upload_freq(self, days=1):
        &#34;&#34;&#34;
        updates the upload frequency
        Args:
            days (int): number of days for upload frequency, value can be between 1 to 7

        Raises:
            SDKException:
                if invalid days supplied for upload frequency

        &#34;&#34;&#34;
        if days &lt; 1:
            raise SDKException(&#39;Metrics&#39;, &#39;101&#39;, &#39;Invalid Upload Frequency supplied&#39;)
        self._metrics_config[&#39;config&#39;][&#39;uploadFrequency&#39;] = days

    def set_data_collection_window(self, seconds=28800):
        &#34;&#34;&#34;
        updates the data collection window
        Args:
            seconds: number for seconds after 12 AM
            e.g.; 28800 for 8 AM
            default; 28800

        Raises:
            SDKException:
                if window specified is below 12.05 am

        &#34;&#34;&#34;
        if seconds &lt; 300:  # minimum 5 minutes after 12 midnight
            raise SDKException(&#39;Metrics&#39;, &#39;101&#39;, &#39;Data collection window should be above 12.05 AM&#39;)
        self._metrics_config[&#39;config&#39;][&#39;dataCollectionTime&#39;] = seconds

    def remove_data_collection_window(self):
        &#34;&#34;&#34;removes data collection window&#34;&#34;&#34;
        self._metrics_config[&#39;config&#39;][&#39;dataCollectionTime&#39;] = -1

    def set_all_clientgroups(self):
        &#34;&#34;&#34;updates metrics configuration with all client groups&#34;&#34;&#34;

        # sets the list to one row with client group id as -1
        self._metrics_config[&#39;config&#39;][&#39;clientGroupList&#39;] = [{&#39;_type_&#39;: 28, &#39;clientGroupId&#39;: -1}]

    def set_clientgroups(self, clientgroup_name=None):
        &#34;&#34;&#34;
        sets the client groups for metrics
        Args:
            clientgroup_name (list): list of client group names, None is set all client groups
            will be enabled.
        &#34;&#34;&#34;
        if clientgroup_name is None:
            self.set_all_clientgroups()
        else:
            self._metrics_config[&#39;config&#39;][&#39;clientGroupList&#39;] = []
            clientgroup = self._metrics_config[&#39;config&#39;][&#39;clientGroupList&#39;]
            for each_client_grp in clientgroup_name:
                cg_id = self._commcell_object.client_groups.get(each_client_grp).clientgroup_id
                clientgroup.append(
                    {&#39;_type_&#39;: 28, &#39;clientGroupId&#39;: int(cg_id), &#39;clientGroupName&#39;: each_client_grp}
                )

    def enable_metrics(self):
        &#34;&#34;&#34;enables Metrics in CommServe&#34;&#34;&#34;
        self._metrics_config[&#39;config&#39;][&#39;commcellDiagUsage&#39;] = self._enable_service

    def disable_metrics(self):
        &#34;&#34;&#34;disables Metrics in CommServe&#34;&#34;&#34;
        self._metrics_config[&#39;config&#39;][&#39;commcellDiagUsage&#39;] = self._disable_service

    def save_config(self):
        &#34;&#34;&#34;
        updates the configuration of Metrics
        this must be called to save the configuration changes made in this object
        Raises:
            SDKException:
                if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._METRICS, self._metrics_config
        )
        if not flag:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response.text)

    def upload_now(self):
        &#34;&#34;&#34;
        Performs Upload Now operation of metrics
        Raises:
            SDKException:
                if response is not success:
        &#34;&#34;&#34;

        self._metrics_config[&#39;config&#39;][&#39;uploadNow&#39;] = 1
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._METRICS, self._metrics_config
        )
        if not flag:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response.text)
        # reset upload now flag
        self._metrics_config[&#39;config&#39;][&#39;uploadNow&#39;] = 0

    def wait_for_download_completion(self, timeout=300):
        &#34;&#34;&#34;
        Waits for Metrics collection to complete for maximum of seconds given in timeout

        Args:
            timeout (int): maximum seconds to wait
        &#34;&#34;&#34;
        self.refresh()
        time_limit = timeout
        while time_limit &gt; 0:
            if self.lastdownloadtime &gt; 0:
                return True
            else:
                sleep(30)
                time_limit -= 30
                self.refresh()
        raise TimeoutError(
            &#34;Download process didn&#39;t complete after {0} seconds&#34;.format(timeout))

    def wait_for_collection_completion(self, timeout=400):
        &#34;&#34;&#34;
        Waits for Metrics collection to complete for maximum of seconds given in timeout

        Args:
            timeout (int): maximum seconds to wait

        Raises: Timeout error if collection didn&#39;t complete within timeout period
        &#34;&#34;&#34;
        self.refresh()
        timelimit = timeout
        while timelimit &gt; 0:
            if self.lastcollectiontime &gt; 0:
                return True
            else:
                sleep(30)
                timelimit -= 30
                self.refresh()
        raise TimeoutError(&#34;Collection process didn&#39;t complete after {0} seconds&#34;.format(timeout))

    def wait_for_upload_completion(self, timeout=120):
        &#34;&#34;&#34;
        Waits for Metrics upload to complete for maximum of seconds given in timeout

        Args:
            timeout (int): maximum seconds to wait

        Raises: Timeout error if upload didn&#39;t complete within timeout period
        &#34;&#34;&#34;
        self.refresh()
        timelimit = timeout
        while timelimit &gt; 0:
            if self.lastuploadtime &gt;= self.lastcollectiontime and self.lastuploadtime &gt; 0:
                return True
            else:
                sleep(30)
                timelimit -= 30
                self.refresh()
        raise TimeoutError(&#34;Upload process didn&#39;t complete after {0} seconds&#34;.format(timeout))

    def wait_for_uploadnow_completion(self,
                                      download_timeout=300,
                                      collection_timeout=400,
                                      upload_timeout=120):
        &#34;&#34;&#34;
        Waits for Metrics uploadNow operation to complete, checks both collection and upload

        Args:
            download_timeout (int): maximum seconds to wait for download
            collection_timeout (int): maximum seconds to wait for collection
            upload_timeout (int): maximum seconds to wait for upload

        Raises: Timeout error if uploadNow operation didn&#39;t complete

        &#34;&#34;&#34;
        self.wait_for_download_completion(download_timeout)
        self.wait_for_collection_completion(collection_timeout)
        self.wait_for_upload_completion(upload_timeout)

    def _get_commcell_id(self):
        &#34;&#34;&#34;returns the hexadecimal value of commcell id&#34;&#34;&#34;
        license_details = LicenseDetails(self._commcell_object)
        ccid = license_details.commcell_id
        if ccid == -1:
            commcellid = &#39;FFFFF&#39;
        else:
            commcellid = hex(ccid).split(&#39;x&#39;)[1].upper()
        return commcellid

    def get_uploaded_filename(self, query_id=None, last_collection_time=None):
        &#34;&#34;&#34;
        Gets last uploaded file name

        Args:
            query_id (int): optional argument to get file name specific to a query
            last_collection_time (int): optional argument to get file name for specified last collection time

        Returns: Last uploaded file name
        &#34;&#34;&#34;

        commcellid = self._get_commcell_id()
        if last_collection_time is None:
            cs_lastcollectiontime = int(self.lastcollectiontime)
        else:
            cs_lastcollectiontime = last_collection_time
        if cs_lastcollectiontime == 0:
            raise Exception(&#34;last collection time is 0, Upload didn&#39;t complete or failed&#34;)
        if query_id is None:
            file_name = &#34;CSS&#34; + &#34;&#34; + str(cs_lastcollectiontime) + &#34;_&#34; + str(commcellid) + &#34;.xml&#34;
        else:
            file_name = &#34;CSS&#34; + &#34;&#34; + str(cs_lastcollectiontime) + &#34;_&#34; + str(
                commcellid) + &#34;_&#34; + str(query_id) + &#34;.xml&#34;
        return file_name

    def get_uploaded_zip_filename(self, commserv_guid, backupjob_id):
        &#34;&#34;&#34;
        Gets last uploaded zip file name
        Args:
            query_id (int): optional argument to get file name specific to a query
        Returns : Last uploaded file name
        &#34;&#34;&#34;
        commcellid = self._get_commcell_id()
        cs_lastcollectiontime = int(self.lastcollectiontime)
        if cs_lastcollectiontime == 0:
            raise Exception(&#34;last collection time is 0, Upload didn&#39;t complete or failed&#34;)
        file_name = &#34;CSS&#34; + &#34;&#34; + str(cs_lastcollectiontime) + &#34;_&#34; + str(commcellid)\
                    + &#34;_&#34; + str(commserv_guid) + &#34;_&#34; + str(backupjob_id) + &#34;.zip&#34;
        return file_name

class PrivateMetrics(_Metrics):
    &#34;&#34;&#34;Class for operations in private Metrics reporting&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the UserGroups class.

                    Args:
                        commcell_object (object)  --  instance of the Commcell class
                        type -- 1 for private, 0 for public

                    Returns:
                        object - instance of the UserGroups class
        &#34;&#34;&#34;
        _Metrics.__init__(self, commcell_object, isprivate=True)

    def _update_private_download_url(self, hostname, port, protocol):
        self._cloud[&#39;downloadURL&#39;] = &#39;{0}://{1}:{2}/downloads/sqlscripts/&#39;.format(protocol,
                                                                                  hostname,
                                                                                  port)

    def _update_private_upload_url(self, hostname, port, protocol):
        self._cloud[&#39;uploadURL&#39;] = &#39;{0}://{1}:{2}/commandcenter/&#39;.format(protocol, hostname, port)

    def _update_chargeback_flags(self, daily, weekly, monthly):
        flags = 0
        if daily:
            flags = flags | 4
        if weekly:
            flags = flags | 8
        if monthly:
            flags = flags | 16
        for service in self._service_list:
            if service[&#39;service&#39;][&#39;name&#39;] == &#39;Charge Back&#39;:
                service[&#39;flags&#39;] = flags

    @property
    def downloadurl(self):
        &#34;&#34;&#34;Returns download URL of private metrics&#34;&#34;&#34;
        return self._metrics_config[&#39;config&#39;][&#39;cloud&#39;][&#39;downloadURL&#39;]

    @property
    def uploadurl(self):
        &#34;&#34;&#34;Returns Upload URL of private metrics&#34;&#34;&#34;
        return self._metrics_config[&#39;config&#39;][&#39;cloud&#39;][&#39;uploadURL&#39;]

    @property
    def private_metrics_server_name(self):
        return urlparse(self.uploadurl).hostname

    def update_url(self, hostname, port=80, protocol=&#39;http&#39;):
        &#34;&#34;&#34;
        updates private Metrics URL in CommServe
        Args:
            hostname (str): Metrics server hostname
            port (int): port of webconsole
                e.g.; 80 for http and 443 for https
            protocol (str): http or https
                default: http
        &#34;&#34;&#34;
        self._update_private_download_url(hostname, port, protocol)
        self._update_private_upload_url(hostname, port, protocol)

    def enable_chargeback(self, daily=True, weekly=False, monthly=False):
        &#34;&#34;&#34;
        Enables Chargeback service as per the daily,weekly and Monthly arguments passes
        Args:
            daily  (bool): enables daily chargeback
            weekly (bool): enables weekly chargeback
            monthly(bool): enables Monthly chargeback

        &#34;&#34;&#34;
        if self.services[&#39;Charge Back&#39;] is not True:
            self._update_service_state(&#39;Charge Back&#39;, self._enable_service)
        self._update_chargeback_flags(daily, weekly, monthly)

    def enable_forwarding(self, forwarding_url):
        &#34;&#34;&#34;
        Enables forwarding
        Args:
            forwarding_url: Webconsole url where metrics data to be forwarded
        &#34;&#34;&#34;
        fwd_info = [{
            &#34;httpServerURL&#34;: forwarding_url,
            &#34;isPublic&#34;: False,
            &#34;urlPwd&#34;: &#34;&#34;,
            &#34;urlUser&#34;: &#34;&#34;
        }]
        self._metrics_config[&#39;config&#39;][&#39;tieringActive&#39;] = True
        self._metrics_config[&#39;config&#39;][&#39;HttpServerInfo&#39;][&#34;httpServer&#34;] = fwd_info

    def disable_forwarding(self):
        &#34;&#34;&#34;Disables forwarding&#34;&#34;&#34;
        self._metrics_config[&#39;config&#39;][&#39;tieringActive&#39;] = False


class CloudMetrics(_Metrics):
    &#34;&#34;&#34;Class for operations in Cloud Metrics reporting&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the UserGroups class.

                    Args:
                        commcell_object (object)  --  instance of the Commcell class

                    Returns:
                        object - instance of the UserGroups class
        &#34;&#34;&#34;
        _Metrics.__init__(self, commcell_object, isprivate=False)

    @property
    def randomization_minutes(self):
        return self._metrics_config[&#39;config&#39;][&#39;randomization&#39;]

    def enable_chargeback(self):
        &#34;&#34;&#34;Enables Chargeback service&#34;&#34;&#34;
        if self.services[&#39;Charge Back&#39;] is not True:
            self._update_service_state(&#39;Charge Back&#39;, self._enable_service)

    def enable_upgrade_readiness(self):
        &#34;&#34;&#34;Enables pre upgrade readiness service&#34;&#34;&#34;
        if self.services[&#39;Upgrade Readiness&#39;] is not True:
            self._update_service_state(&#39;Upgrade Readiness&#39;, self._enable_service)

    def disable_upgrade_readiness(self):
        &#34;&#34;&#34;disables pre upgrade readiness service&#34;&#34;&#34;
        if self.services[&#39;Upgrade Readiness&#39;] is True:
            self._update_service_state(&#39;Upgrade Readiness&#39;, self._disable_service)

    def enable_proactive_support(self):
        &#34;&#34;&#34;Enables Proactive Support service&#34;&#34;&#34;
        if self.services[&#39;Proactive Support&#39;] is not True:
            self._update_service_state(&#39;Proactive Support&#39;, self._enable_service)

    def disable_proactive_support(self):
        &#34;&#34;&#34;disables Proactive Support service&#34;&#34;&#34;
        if self.services[&#39;Proactive Support&#39;] is True:
            self._update_service_state(&#39;Proactive Support&#39;, self._disable_service)

    def enable_cloud_assist(self):
        &#34;&#34;&#34;Enables Cloud Assist service and proactive support if not already enabled&#34;&#34;&#34;
        if self.services[&#39;Proactive Support&#39;] is not True:
            # pro active support must be enabled to enable cloud assist
            self.enable_proactive_support()
            self._update_service_state(&#39;Cloud Assist&#39;, self._enable_service)

    def disable_cloud_assist(self):
        &#34;&#34;&#34;disables Cloud Assist service&#34;&#34;&#34;
        if self.services[&#39;Cloud Assist&#39;] is True:
            self._update_service_state(&#39;Cloud Assist&#39;, self._disable_service)

    def set_randomization_minutes(self, minutes=0):
        &#34;&#34;&#34;
        Sets the randomization value in gxglobal param

        Args:
            minutes (int): randomization value in minutes
        &#34;&#34;&#34;
        qcommand = self._commcell_object._services[&#39;QCOMMAND&#39;]
        qoperation = (&#39;qoperation execscript -sn SetKeyIntoGlobalParamTbl.sql &#39;
                      &#39;-si CommservSurveyRandomizationEnabled -si y -si {0}&#39;.format(minutes))

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, qcommand, qoperation
        )
        if not flag:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response.text)


class LocalMetrics:
    &#34;&#34;&#34;class for operation in localmetrics&#34;&#34;&#34;

    def __init__(self, commcell_object, islocalmetrics= True):
        self._commcell_object = commcell_object
        self._islocalmetrics = islocalmetrics
        self._LOCAL_METRICS = self._commcell_object._services[&#39;LOCAL_METRICS&#39;] % self._islocalmetrics
        self._get_metrics_config()

    def _get_metrics_config(self):
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._LOCAL_METRICS
        )
        if flag:
            self._metrics_config = response.json()
            config_value = self._metrics_config[&#39;config&#39;]
            return config_value
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response.text)

    def refresh(self):
        &#34;&#34;&#34;updates metrics object with the latest configuration&#34;&#34;&#34;
        self._get_metrics_config()

    @property
    def last_upload_time(self):
        &#34;&#34;&#34; get last upload time&#34;&#34;&#34;
        return self._metrics_config[&#39;config&#39;][&#39;lastCollectionTime&#39;]

    @property
    def nextup_load_time(self):
        &#34;&#34;&#34;get the next upload time&#34;&#34;&#34;
        return self._metrics_config[&#39;config&#39;][&#39;nextUploadTime&#39;]</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.metricsreport.CloudMetrics"><code class="flex name class">
<span>class <span class="ident">CloudMetrics</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for operations in Cloud Metrics reporting</p>
<p>Initialize object of the UserGroups class.</p>
<h2 id="args">Args</h2>
<p>commcell_object (object)
&ndash;
instance of the Commcell class</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the UserGroups class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/metricsreport.py#L584-L654" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class CloudMetrics(_Metrics):
    &#34;&#34;&#34;Class for operations in Cloud Metrics reporting&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the UserGroups class.

                    Args:
                        commcell_object (object)  --  instance of the Commcell class

                    Returns:
                        object - instance of the UserGroups class
        &#34;&#34;&#34;
        _Metrics.__init__(self, commcell_object, isprivate=False)

    @property
    def randomization_minutes(self):
        return self._metrics_config[&#39;config&#39;][&#39;randomization&#39;]

    def enable_chargeback(self):
        &#34;&#34;&#34;Enables Chargeback service&#34;&#34;&#34;
        if self.services[&#39;Charge Back&#39;] is not True:
            self._update_service_state(&#39;Charge Back&#39;, self._enable_service)

    def enable_upgrade_readiness(self):
        &#34;&#34;&#34;Enables pre upgrade readiness service&#34;&#34;&#34;
        if self.services[&#39;Upgrade Readiness&#39;] is not True:
            self._update_service_state(&#39;Upgrade Readiness&#39;, self._enable_service)

    def disable_upgrade_readiness(self):
        &#34;&#34;&#34;disables pre upgrade readiness service&#34;&#34;&#34;
        if self.services[&#39;Upgrade Readiness&#39;] is True:
            self._update_service_state(&#39;Upgrade Readiness&#39;, self._disable_service)

    def enable_proactive_support(self):
        &#34;&#34;&#34;Enables Proactive Support service&#34;&#34;&#34;
        if self.services[&#39;Proactive Support&#39;] is not True:
            self._update_service_state(&#39;Proactive Support&#39;, self._enable_service)

    def disable_proactive_support(self):
        &#34;&#34;&#34;disables Proactive Support service&#34;&#34;&#34;
        if self.services[&#39;Proactive Support&#39;] is True:
            self._update_service_state(&#39;Proactive Support&#39;, self._disable_service)

    def enable_cloud_assist(self):
        &#34;&#34;&#34;Enables Cloud Assist service and proactive support if not already enabled&#34;&#34;&#34;
        if self.services[&#39;Proactive Support&#39;] is not True:
            # pro active support must be enabled to enable cloud assist
            self.enable_proactive_support()
            self._update_service_state(&#39;Cloud Assist&#39;, self._enable_service)

    def disable_cloud_assist(self):
        &#34;&#34;&#34;disables Cloud Assist service&#34;&#34;&#34;
        if self.services[&#39;Cloud Assist&#39;] is True:
            self._update_service_state(&#39;Cloud Assist&#39;, self._disable_service)

    def set_randomization_minutes(self, minutes=0):
        &#34;&#34;&#34;
        Sets the randomization value in gxglobal param

        Args:
            minutes (int): randomization value in minutes
        &#34;&#34;&#34;
        qcommand = self._commcell_object._services[&#39;QCOMMAND&#39;]
        qoperation = (&#39;qoperation execscript -sn SetKeyIntoGlobalParamTbl.sql &#39;
                      &#39;-si CommservSurveyRandomizationEnabled -si y -si {0}&#39;.format(minutes))

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, qcommand, qoperation
        )
        if not flag:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response.text)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li>cvpysdk.metricsreport._Metrics</li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.metricsreport.CloudMetrics.randomization_minutes"><code class="name">var <span class="ident">randomization_minutes</span></code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/metricsreport.py#L598-L600" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def randomization_minutes(self):
    return self._metrics_config[&#39;config&#39;][&#39;randomization&#39;]</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.metricsreport.CloudMetrics.disable_cloud_assist"><code class="name flex">
<span>def <span class="ident">disable_cloud_assist</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>disables Cloud Assist service</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/metricsreport.py#L634-L637" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def disable_cloud_assist(self):
    &#34;&#34;&#34;disables Cloud Assist service&#34;&#34;&#34;
    if self.services[&#39;Cloud Assist&#39;] is True:
        self._update_service_state(&#39;Cloud Assist&#39;, self._disable_service)</code></pre>
</details>
</dd>
<dt id="cvpysdk.metricsreport.CloudMetrics.disable_proactive_support"><code class="name flex">
<span>def <span class="ident">disable_proactive_support</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>disables Proactive Support service</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/metricsreport.py#L622-L625" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def disable_proactive_support(self):
    &#34;&#34;&#34;disables Proactive Support service&#34;&#34;&#34;
    if self.services[&#39;Proactive Support&#39;] is True:
        self._update_service_state(&#39;Proactive Support&#39;, self._disable_service)</code></pre>
</details>
</dd>
<dt id="cvpysdk.metricsreport.CloudMetrics.disable_upgrade_readiness"><code class="name flex">
<span>def <span class="ident">disable_upgrade_readiness</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>disables pre upgrade readiness service</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/metricsreport.py#L612-L615" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def disable_upgrade_readiness(self):
    &#34;&#34;&#34;disables pre upgrade readiness service&#34;&#34;&#34;
    if self.services[&#39;Upgrade Readiness&#39;] is True:
        self._update_service_state(&#39;Upgrade Readiness&#39;, self._disable_service)</code></pre>
</details>
</dd>
<dt id="cvpysdk.metricsreport.CloudMetrics.enable_chargeback"><code class="name flex">
<span>def <span class="ident">enable_chargeback</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Enables Chargeback service</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/metricsreport.py#L602-L605" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable_chargeback(self):
    &#34;&#34;&#34;Enables Chargeback service&#34;&#34;&#34;
    if self.services[&#39;Charge Back&#39;] is not True:
        self._update_service_state(&#39;Charge Back&#39;, self._enable_service)</code></pre>
</details>
</dd>
<dt id="cvpysdk.metricsreport.CloudMetrics.enable_cloud_assist"><code class="name flex">
<span>def <span class="ident">enable_cloud_assist</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Enables Cloud Assist service and proactive support if not already enabled</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/metricsreport.py#L627-L632" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable_cloud_assist(self):
    &#34;&#34;&#34;Enables Cloud Assist service and proactive support if not already enabled&#34;&#34;&#34;
    if self.services[&#39;Proactive Support&#39;] is not True:
        # pro active support must be enabled to enable cloud assist
        self.enable_proactive_support()
        self._update_service_state(&#39;Cloud Assist&#39;, self._enable_service)</code></pre>
</details>
</dd>
<dt id="cvpysdk.metricsreport.CloudMetrics.enable_proactive_support"><code class="name flex">
<span>def <span class="ident">enable_proactive_support</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Enables Proactive Support service</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/metricsreport.py#L617-L620" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable_proactive_support(self):
    &#34;&#34;&#34;Enables Proactive Support service&#34;&#34;&#34;
    if self.services[&#39;Proactive Support&#39;] is not True:
        self._update_service_state(&#39;Proactive Support&#39;, self._enable_service)</code></pre>
</details>
</dd>
<dt id="cvpysdk.metricsreport.CloudMetrics.enable_upgrade_readiness"><code class="name flex">
<span>def <span class="ident">enable_upgrade_readiness</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Enables pre upgrade readiness service</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/metricsreport.py#L607-L610" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable_upgrade_readiness(self):
    &#34;&#34;&#34;Enables pre upgrade readiness service&#34;&#34;&#34;
    if self.services[&#39;Upgrade Readiness&#39;] is not True:
        self._update_service_state(&#39;Upgrade Readiness&#39;, self._enable_service)</code></pre>
</details>
</dd>
<dt id="cvpysdk.metricsreport.CloudMetrics.set_randomization_minutes"><code class="name flex">
<span>def <span class="ident">set_randomization_minutes</span></span>(<span>self, minutes=0)</span>
</code></dt>
<dd>
<div class="desc"><p>Sets the randomization value in gxglobal param</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>minutes</code></strong> :&ensp;<code>int</code></dt>
<dd>randomization value in minutes</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/metricsreport.py#L639-L654" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_randomization_minutes(self, minutes=0):
    &#34;&#34;&#34;
    Sets the randomization value in gxglobal param

    Args:
        minutes (int): randomization value in minutes
    &#34;&#34;&#34;
    qcommand = self._commcell_object._services[&#39;QCOMMAND&#39;]
    qoperation = (&#39;qoperation execscript -sn SetKeyIntoGlobalParamTbl.sql &#39;
                  &#39;-si CommservSurveyRandomizationEnabled -si y -si {0}&#39;.format(minutes))

    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, qcommand, qoperation
    )
    if not flag:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response.text)</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.metricsreport.LocalMetrics"><code class="flex name class">
<span>class <span class="ident">LocalMetrics</span></span>
<span>(</span><span>commcell_object, islocalmetrics=True)</span>
</code></dt>
<dd>
<div class="desc"><p>class for operation in localmetrics</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/metricsreport.py#L657-L689" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class LocalMetrics:
    &#34;&#34;&#34;class for operation in localmetrics&#34;&#34;&#34;

    def __init__(self, commcell_object, islocalmetrics= True):
        self._commcell_object = commcell_object
        self._islocalmetrics = islocalmetrics
        self._LOCAL_METRICS = self._commcell_object._services[&#39;LOCAL_METRICS&#39;] % self._islocalmetrics
        self._get_metrics_config()

    def _get_metrics_config(self):
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._LOCAL_METRICS
        )
        if flag:
            self._metrics_config = response.json()
            config_value = self._metrics_config[&#39;config&#39;]
            return config_value
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response.text)

    def refresh(self):
        &#34;&#34;&#34;updates metrics object with the latest configuration&#34;&#34;&#34;
        self._get_metrics_config()

    @property
    def last_upload_time(self):
        &#34;&#34;&#34; get last upload time&#34;&#34;&#34;
        return self._metrics_config[&#39;config&#39;][&#39;lastCollectionTime&#39;]

    @property
    def nextup_load_time(self):
        &#34;&#34;&#34;get the next upload time&#34;&#34;&#34;
        return self._metrics_config[&#39;config&#39;][&#39;nextUploadTime&#39;]</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.metricsreport.LocalMetrics.last_upload_time"><code class="name">var <span class="ident">last_upload_time</span></code></dt>
<dd>
<div class="desc"><p>get last upload time</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/metricsreport.py#L681-L684" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def last_upload_time(self):
    &#34;&#34;&#34; get last upload time&#34;&#34;&#34;
    return self._metrics_config[&#39;config&#39;][&#39;lastCollectionTime&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.metricsreport.LocalMetrics.nextup_load_time"><code class="name">var <span class="ident">nextup_load_time</span></code></dt>
<dd>
<div class="desc"><p>get the next upload time</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/metricsreport.py#L686-L689" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def nextup_load_time(self):
    &#34;&#34;&#34;get the next upload time&#34;&#34;&#34;
    return self._metrics_config[&#39;config&#39;][&#39;nextUploadTime&#39;]</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.metricsreport.LocalMetrics.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>updates metrics object with the latest configuration</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/metricsreport.py#L677-L679" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;updates metrics object with the latest configuration&#34;&#34;&#34;
    self._get_metrics_config()</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.metricsreport.PrivateMetrics"><code class="flex name class">
<span>class <span class="ident">PrivateMetrics</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for operations in private Metrics reporting</p>
<p>Initialize object of the UserGroups class.</p>
<h2 id="args">Args</h2>
<p>commcell_object (object)
&ndash;
instance of the Commcell class
type &ndash; 1 for private, 0 for public</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the UserGroups class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/metricsreport.py#L489-L581" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class PrivateMetrics(_Metrics):
    &#34;&#34;&#34;Class for operations in private Metrics reporting&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the UserGroups class.

                    Args:
                        commcell_object (object)  --  instance of the Commcell class
                        type -- 1 for private, 0 for public

                    Returns:
                        object - instance of the UserGroups class
        &#34;&#34;&#34;
        _Metrics.__init__(self, commcell_object, isprivate=True)

    def _update_private_download_url(self, hostname, port, protocol):
        self._cloud[&#39;downloadURL&#39;] = &#39;{0}://{1}:{2}/downloads/sqlscripts/&#39;.format(protocol,
                                                                                  hostname,
                                                                                  port)

    def _update_private_upload_url(self, hostname, port, protocol):
        self._cloud[&#39;uploadURL&#39;] = &#39;{0}://{1}:{2}/commandcenter/&#39;.format(protocol, hostname, port)

    def _update_chargeback_flags(self, daily, weekly, monthly):
        flags = 0
        if daily:
            flags = flags | 4
        if weekly:
            flags = flags | 8
        if monthly:
            flags = flags | 16
        for service in self._service_list:
            if service[&#39;service&#39;][&#39;name&#39;] == &#39;Charge Back&#39;:
                service[&#39;flags&#39;] = flags

    @property
    def downloadurl(self):
        &#34;&#34;&#34;Returns download URL of private metrics&#34;&#34;&#34;
        return self._metrics_config[&#39;config&#39;][&#39;cloud&#39;][&#39;downloadURL&#39;]

    @property
    def uploadurl(self):
        &#34;&#34;&#34;Returns Upload URL of private metrics&#34;&#34;&#34;
        return self._metrics_config[&#39;config&#39;][&#39;cloud&#39;][&#39;uploadURL&#39;]

    @property
    def private_metrics_server_name(self):
        return urlparse(self.uploadurl).hostname

    def update_url(self, hostname, port=80, protocol=&#39;http&#39;):
        &#34;&#34;&#34;
        updates private Metrics URL in CommServe
        Args:
            hostname (str): Metrics server hostname
            port (int): port of webconsole
                e.g.; 80 for http and 443 for https
            protocol (str): http or https
                default: http
        &#34;&#34;&#34;
        self._update_private_download_url(hostname, port, protocol)
        self._update_private_upload_url(hostname, port, protocol)

    def enable_chargeback(self, daily=True, weekly=False, monthly=False):
        &#34;&#34;&#34;
        Enables Chargeback service as per the daily,weekly and Monthly arguments passes
        Args:
            daily  (bool): enables daily chargeback
            weekly (bool): enables weekly chargeback
            monthly(bool): enables Monthly chargeback

        &#34;&#34;&#34;
        if self.services[&#39;Charge Back&#39;] is not True:
            self._update_service_state(&#39;Charge Back&#39;, self._enable_service)
        self._update_chargeback_flags(daily, weekly, monthly)

    def enable_forwarding(self, forwarding_url):
        &#34;&#34;&#34;
        Enables forwarding
        Args:
            forwarding_url: Webconsole url where metrics data to be forwarded
        &#34;&#34;&#34;
        fwd_info = [{
            &#34;httpServerURL&#34;: forwarding_url,
            &#34;isPublic&#34;: False,
            &#34;urlPwd&#34;: &#34;&#34;,
            &#34;urlUser&#34;: &#34;&#34;
        }]
        self._metrics_config[&#39;config&#39;][&#39;tieringActive&#39;] = True
        self._metrics_config[&#39;config&#39;][&#39;HttpServerInfo&#39;][&#34;httpServer&#34;] = fwd_info

    def disable_forwarding(self):
        &#34;&#34;&#34;Disables forwarding&#34;&#34;&#34;
        self._metrics_config[&#39;config&#39;][&#39;tieringActive&#39;] = False</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li>cvpysdk.metricsreport._Metrics</li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.metricsreport.PrivateMetrics.downloadurl"><code class="name">var <span class="ident">downloadurl</span></code></dt>
<dd>
<div class="desc"><p>Returns download URL of private metrics</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/metricsreport.py#L524-L527" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def downloadurl(self):
    &#34;&#34;&#34;Returns download URL of private metrics&#34;&#34;&#34;
    return self._metrics_config[&#39;config&#39;][&#39;cloud&#39;][&#39;downloadURL&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.metricsreport.PrivateMetrics.private_metrics_server_name"><code class="name">var <span class="ident">private_metrics_server_name</span></code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/metricsreport.py#L534-L536" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def private_metrics_server_name(self):
    return urlparse(self.uploadurl).hostname</code></pre>
</details>
</dd>
<dt id="cvpysdk.metricsreport.PrivateMetrics.uploadurl"><code class="name">var <span class="ident">uploadurl</span></code></dt>
<dd>
<div class="desc"><p>Returns Upload URL of private metrics</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/metricsreport.py#L529-L532" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def uploadurl(self):
    &#34;&#34;&#34;Returns Upload URL of private metrics&#34;&#34;&#34;
    return self._metrics_config[&#39;config&#39;][&#39;cloud&#39;][&#39;uploadURL&#39;]</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.metricsreport.PrivateMetrics.disable_forwarding"><code class="name flex">
<span>def <span class="ident">disable_forwarding</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Disables forwarding</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/metricsreport.py#L579-L581" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def disable_forwarding(self):
    &#34;&#34;&#34;Disables forwarding&#34;&#34;&#34;
    self._metrics_config[&#39;config&#39;][&#39;tieringActive&#39;] = False</code></pre>
</details>
</dd>
<dt id="cvpysdk.metricsreport.PrivateMetrics.enable_chargeback"><code class="name flex">
<span>def <span class="ident">enable_chargeback</span></span>(<span>self, daily=True, weekly=False, monthly=False)</span>
</code></dt>
<dd>
<div class="desc"><p>Enables Chargeback service as per the daily,weekly and Monthly arguments passes</p>
<h2 id="args">Args</h2>
<dl>
<dt>daily
(bool): enables daily chargeback</dt>
<dt><strong><code>weekly</code></strong> :&ensp;<code>bool</code></dt>
<dd>enables weekly chargeback</dd>
</dl>
<p>monthly(bool): enables Monthly chargeback</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/metricsreport.py#L551-L562" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable_chargeback(self, daily=True, weekly=False, monthly=False):
    &#34;&#34;&#34;
    Enables Chargeback service as per the daily,weekly and Monthly arguments passes
    Args:
        daily  (bool): enables daily chargeback
        weekly (bool): enables weekly chargeback
        monthly(bool): enables Monthly chargeback

    &#34;&#34;&#34;
    if self.services[&#39;Charge Back&#39;] is not True:
        self._update_service_state(&#39;Charge Back&#39;, self._enable_service)
    self._update_chargeback_flags(daily, weekly, monthly)</code></pre>
</details>
</dd>
<dt id="cvpysdk.metricsreport.PrivateMetrics.enable_forwarding"><code class="name flex">
<span>def <span class="ident">enable_forwarding</span></span>(<span>self, forwarding_url)</span>
</code></dt>
<dd>
<div class="desc"><p>Enables forwarding</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>forwarding_url</code></strong></dt>
<dd>Webconsole url where metrics data to be forwarded</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/metricsreport.py#L564-L577" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable_forwarding(self, forwarding_url):
    &#34;&#34;&#34;
    Enables forwarding
    Args:
        forwarding_url: Webconsole url where metrics data to be forwarded
    &#34;&#34;&#34;
    fwd_info = [{
        &#34;httpServerURL&#34;: forwarding_url,
        &#34;isPublic&#34;: False,
        &#34;urlPwd&#34;: &#34;&#34;,
        &#34;urlUser&#34;: &#34;&#34;
    }]
    self._metrics_config[&#39;config&#39;][&#39;tieringActive&#39;] = True
    self._metrics_config[&#39;config&#39;][&#39;HttpServerInfo&#39;][&#34;httpServer&#34;] = fwd_info</code></pre>
</details>
</dd>
<dt id="cvpysdk.metricsreport.PrivateMetrics.update_url"><code class="name flex">
<span>def <span class="ident">update_url</span></span>(<span>self, hostname, port=80, protocol='http')</span>
</code></dt>
<dd>
<div class="desc"><p>updates private Metrics URL in CommServe</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>hostname</code></strong> :&ensp;<code>str</code></dt>
<dd>Metrics server hostname</dd>
<dt><strong><code>port</code></strong> :&ensp;<code>int</code></dt>
<dd>port of webconsole
e.g.; 80 for http and 443 for https</dd>
<dt><strong><code>protocol</code></strong> :&ensp;<code>str</code></dt>
<dd>http or https
default: http</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/metricsreport.py#L538-L549" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def update_url(self, hostname, port=80, protocol=&#39;http&#39;):
    &#34;&#34;&#34;
    updates private Metrics URL in CommServe
    Args:
        hostname (str): Metrics server hostname
        port (int): port of webconsole
            e.g.; 80 for http and 443 for https
        protocol (str): http or https
            default: http
    &#34;&#34;&#34;
    self._update_private_download_url(hostname, port, protocol)
    self._update_private_upload_url(hostname, port, protocol)</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.metricsreport.CloudMetrics" href="#cvpysdk.metricsreport.CloudMetrics">CloudMetrics</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.metricsreport.CloudMetrics.disable_cloud_assist" href="#cvpysdk.metricsreport.CloudMetrics.disable_cloud_assist">disable_cloud_assist</a></code></li>
<li><code><a title="cvpysdk.metricsreport.CloudMetrics.disable_proactive_support" href="#cvpysdk.metricsreport.CloudMetrics.disable_proactive_support">disable_proactive_support</a></code></li>
<li><code><a title="cvpysdk.metricsreport.CloudMetrics.disable_upgrade_readiness" href="#cvpysdk.metricsreport.CloudMetrics.disable_upgrade_readiness">disable_upgrade_readiness</a></code></li>
<li><code><a title="cvpysdk.metricsreport.CloudMetrics.enable_chargeback" href="#cvpysdk.metricsreport.CloudMetrics.enable_chargeback">enable_chargeback</a></code></li>
<li><code><a title="cvpysdk.metricsreport.CloudMetrics.enable_cloud_assist" href="#cvpysdk.metricsreport.CloudMetrics.enable_cloud_assist">enable_cloud_assist</a></code></li>
<li><code><a title="cvpysdk.metricsreport.CloudMetrics.enable_proactive_support" href="#cvpysdk.metricsreport.CloudMetrics.enable_proactive_support">enable_proactive_support</a></code></li>
<li><code><a title="cvpysdk.metricsreport.CloudMetrics.enable_upgrade_readiness" href="#cvpysdk.metricsreport.CloudMetrics.enable_upgrade_readiness">enable_upgrade_readiness</a></code></li>
<li><code><a title="cvpysdk.metricsreport.CloudMetrics.randomization_minutes" href="#cvpysdk.metricsreport.CloudMetrics.randomization_minutes">randomization_minutes</a></code></li>
<li><code><a title="cvpysdk.metricsreport.CloudMetrics.set_randomization_minutes" href="#cvpysdk.metricsreport.CloudMetrics.set_randomization_minutes">set_randomization_minutes</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.metricsreport.LocalMetrics" href="#cvpysdk.metricsreport.LocalMetrics">LocalMetrics</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.metricsreport.LocalMetrics.last_upload_time" href="#cvpysdk.metricsreport.LocalMetrics.last_upload_time">last_upload_time</a></code></li>
<li><code><a title="cvpysdk.metricsreport.LocalMetrics.nextup_load_time" href="#cvpysdk.metricsreport.LocalMetrics.nextup_load_time">nextup_load_time</a></code></li>
<li><code><a title="cvpysdk.metricsreport.LocalMetrics.refresh" href="#cvpysdk.metricsreport.LocalMetrics.refresh">refresh</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.metricsreport.PrivateMetrics" href="#cvpysdk.metricsreport.PrivateMetrics">PrivateMetrics</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.metricsreport.PrivateMetrics.disable_forwarding" href="#cvpysdk.metricsreport.PrivateMetrics.disable_forwarding">disable_forwarding</a></code></li>
<li><code><a title="cvpysdk.metricsreport.PrivateMetrics.downloadurl" href="#cvpysdk.metricsreport.PrivateMetrics.downloadurl">downloadurl</a></code></li>
<li><code><a title="cvpysdk.metricsreport.PrivateMetrics.enable_chargeback" href="#cvpysdk.metricsreport.PrivateMetrics.enable_chargeback">enable_chargeback</a></code></li>
<li><code><a title="cvpysdk.metricsreport.PrivateMetrics.enable_forwarding" href="#cvpysdk.metricsreport.PrivateMetrics.enable_forwarding">enable_forwarding</a></code></li>
<li><code><a title="cvpysdk.metricsreport.PrivateMetrics.private_metrics_server_name" href="#cvpysdk.metricsreport.PrivateMetrics.private_metrics_server_name">private_metrics_server_name</a></code></li>
<li><code><a title="cvpysdk.metricsreport.PrivateMetrics.update_url" href="#cvpysdk.metricsreport.PrivateMetrics.update_url">update_url</a></code></li>
<li><code><a title="cvpysdk.metricsreport.PrivateMetrics.uploadurl" href="#cvpysdk.metricsreport.PrivateMetrics.uploadurl">uploadurl</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>