<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.clientgroup API documentation</title>
<meta name="description" content="Main file for performing client group operations …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.clientgroup</code></h1>
</header>
<section id="section-intro">
<p>Main file for performing client group operations.</p>
<p>ClientGroups and ClientGroup are the classes defined in this file.</p>
<p>ClientGroups: Class for representing all the client groups associated with a commcell</p>
<p>ClientGroup:
Class for representing a single Client Group of the commcell</p>
<h1 id="clientgroups">ClientGroups:</h1>
<pre><code>__init__(commcell_object)  -- initialise instance of the ClientGroups associated with
the specified commcell

__str__()                  -- returns all the client groups associated with the Commcell

__repr__()                 -- returns the string for the instance of the ClientGroups class

__len__()                  -- returns the number of client groups associated with the Commcell

__getitem__()              -- returns the name of the client group for the given client group
Id or the details for the given client group name

_get_clientgroups()        -- gets all the clientgroups associated with the commcell specified

_valid_clients()           -- returns the list of all the valid clients,
from the list of clients provided

_get_fl_paramters()        -- Returns the fl parameters to be passed in the mongodb caching api call

_get_sort_parameters()     -- Returns the sort parameters to be passed in the mongodb caching api call

_get_fq_parameters()       -- Returns the fq parameters based on the fq list passed

get_client_groups_cache()  -- Gets all the client groups present in CommcellEntityCache DB.


has_clientgroup()          -- checks if a client group exists with the given name or not

create_smart_rule()        -- Create rules required for smart client group creation
based on input parameters

merge_smart_rules()        -- Merge multiple rules into (SCG) rule to create smart client group

_create_scope_dict()       -- Creates Scope Dictionary needed for Smart Client group association

add(clientgroup_name)      -- adds a new client group to the commcell

get(clientgroup_name)      -- returns the instance of the ClientGroup class,
for the the input client group name

delete(clientgroup_name)   -- deletes the client group from the commcell

refresh()                  -- refresh the client groups associated with the commcell
</code></pre>
<h2 id="clientgroups-attributes">Clientgroups Attributes</h2>
<pre><code>**all_clientgroups**         -- returns the dict of all the clientgroups on the commcell

**all_clientgroups_cache**   -- Returns dict of all the client groups and their info present in
CommcellEntityCache in mongoDB

**all_client_groups_prop**   -- Returns complete GET API response
</code></pre>
<h1 id="clientgroup">ClientGroup:</h1>
<pre><code>__init__(commcell_object,
         clientgroup_name,
         clientgroup_id=None)  -- initialise object of ClientGroup class with the specified
client group name and id

__repr__()                     -- return the client group name, the instance is associated with

_get_clientgroup_id()          -- method to get the clientgroup id, if not specified

_get_clientgroup_properties()  -- get the properties of this clientgroup

_initialize_clientgroup_properties() --  initializes the properties of this ClientGroup

_request_json_()               -- returns the appropriate JSON to pass for enabling/disabling
an activity

_process_update_request()      -- processes the clientgroup update API call

_update()                      -- updates the client group properties

_add_or_remove_clients()       -- adds/removes clients to/from a ClientGroup

enable_backup_at_time()        -- enables backup for the client group at the time specified

enable_backup()                -- enables the backup flag

disable_backup()               -- disables the backup flag

enable_restore_at_time()       -- enables restore for the client group at the time specified

enable_restore()               -- enables the restore flag

disable_restore()              -- disables the restore flag

enable_data_aging_at_time()    -- enables data aging for the client group at the time specified

enable_data_aging()            -- enables the data aging flag

disable_data_aging()           -- disables the data aging flag

add_clients()                  -- adds the valid clients to client group

remove_clients()               -- removes the valid clients from client group

remove_all_clients()           -- removes all the associated clients from client group

network()                      -- returns Network class object

push_network_config()          -- performs a push network configuration on client group

refresh()                      -- refresh the properties of the client group

push_servicepack_and_hotfixes() -- triggers installation of service pack and hotfixes

repair_software()               -- triggers Repair software on the client group

update_properties()             -- to update the client group properties

add_additional_setting()        -- adds registry key to client group property

delete_additional_setting()     -- Delete registry key from client group property

is_auto_discover_enabled()      -- gets the autodiscover option for the Organization

enable_auto_discover()          -- enables  autodiscover option at client group level

disable_auto_discover()         -- disables  autodiscover option at client group level

refresh_clients()               -- force refreshes clients in a client group
</code></pre>
<h2 id="clientgroup-attributes">Clientgroup Attributes</h2>
<pre><code>Following attributes are available for an instance of the ClientGroup class:

    **name**                       --      returns the name of client group

    **clientgroup_id**             --      returns the id of client group

    **clientgroup_name**           --      returns the name of client group

    **description**                --      returns the description of client group

    **associated_clients**         --      returns the associated clients of client group

    **is_backup_enabled**          --      returns the backup activity status of client group

    **is_restore_enabled**         --      returns the restore activity status of client group

    **is_data_aging_enabled**      --      returns the data aging activity status of client group

    **is_smart_client_group**      --      returns true if client group is a smart client group

    **is_auto_discover_enabled**   --      returns the auto discover status of client group
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L1-L2329" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Main file for performing client group operations.

ClientGroups and ClientGroup are the classes defined in this file.

ClientGroups: Class for representing all the client groups associated with a commcell

ClientGroup:  Class for representing a single Client Group of the commcell

ClientGroups:
=============

    __init__(commcell_object)  -- initialise instance of the ClientGroups associated with
    the specified commcell

    __str__()                  -- returns all the client groups associated with the Commcell

    __repr__()                 -- returns the string for the instance of the ClientGroups class

    __len__()                  -- returns the number of client groups associated with the Commcell

    __getitem__()              -- returns the name of the client group for the given client group
    Id or the details for the given client group name

    _get_clientgroups()        -- gets all the clientgroups associated with the commcell specified

    _valid_clients()           -- returns the list of all the valid clients,
    from the list of clients provided

    _get_fl_paramters()        -- Returns the fl parameters to be passed in the mongodb caching api call

    _get_sort_parameters()     -- Returns the sort parameters to be passed in the mongodb caching api call

    _get_fq_parameters()       -- Returns the fq parameters based on the fq list passed

    get_client_groups_cache()  -- Gets all the client groups present in CommcellEntityCache DB.


    has_clientgroup()          -- checks if a client group exists with the given name or not

    create_smart_rule()        -- Create rules required for smart client group creation
    based on input parameters

    merge_smart_rules()        -- Merge multiple rules into (SCG) rule to create smart client group

    _create_scope_dict()       -- Creates Scope Dictionary needed for Smart Client group association

    add(clientgroup_name)      -- adds a new client group to the commcell

    get(clientgroup_name)      -- returns the instance of the ClientGroup class,
    for the the input client group name

    delete(clientgroup_name)   -- deletes the client group from the commcell

    refresh()                  -- refresh the client groups associated with the commcell

ClientGroups Attributes
-----------------------

    **all_clientgroups**         -- returns the dict of all the clientgroups on the commcell

    **all_clientgroups_cache**   -- Returns dict of all the client groups and their info present in
    CommcellEntityCache in mongoDB

    **all_client_groups_prop**   -- Returns complete GET API response

ClientGroup:
============

    __init__(commcell_object,
             clientgroup_name,
             clientgroup_id=None)  -- initialise object of ClientGroup class with the specified
    client group name and id

    __repr__()                     -- return the client group name, the instance is associated with

    _get_clientgroup_id()          -- method to get the clientgroup id, if not specified

    _get_clientgroup_properties()  -- get the properties of this clientgroup

    _initialize_clientgroup_properties() --  initializes the properties of this ClientGroup

    _request_json_()               -- returns the appropriate JSON to pass for enabling/disabling
    an activity

    _process_update_request()      -- processes the clientgroup update API call

    _update()                      -- updates the client group properties

    _add_or_remove_clients()       -- adds/removes clients to/from a ClientGroup

    enable_backup_at_time()        -- enables backup for the client group at the time specified

    enable_backup()                -- enables the backup flag

    disable_backup()               -- disables the backup flag

    enable_restore_at_time()       -- enables restore for the client group at the time specified

    enable_restore()               -- enables the restore flag

    disable_restore()              -- disables the restore flag

    enable_data_aging_at_time()    -- enables data aging for the client group at the time specified

    enable_data_aging()            -- enables the data aging flag

    disable_data_aging()           -- disables the data aging flag

    add_clients()                  -- adds the valid clients to client group

    remove_clients()               -- removes the valid clients from client group

    remove_all_clients()           -- removes all the associated clients from client group

    network()                      -- returns Network class object

    push_network_config()          -- performs a push network configuration on client group

    refresh()                      -- refresh the properties of the client group

    push_servicepack_and_hotfixes() -- triggers installation of service pack and hotfixes

    repair_software()               -- triggers Repair software on the client group

    update_properties()             -- to update the client group properties

    add_additional_setting()        -- adds registry key to client group property

    delete_additional_setting()     -- Delete registry key from client group property

    is_auto_discover_enabled()      -- gets the autodiscover option for the Organization

    enable_auto_discover()          -- enables  autodiscover option at client group level

    disable_auto_discover()         -- disables  autodiscover option at client group level

    refresh_clients()               -- force refreshes clients in a client group

ClientGroup Attributes
-----------------------

    Following attributes are available for an instance of the ClientGroup class:

        **name**                       --      returns the name of client group
        
        **clientgroup_id**             --      returns the id of client group
        
        **clientgroup_name**           --      returns the name of client group
        
        **description**                --      returns the description of client group
        
        **associated_clients**         --      returns the associated clients of client group
        
        **is_backup_enabled**          --      returns the backup activity status of client group
        
        **is_restore_enabled**         --      returns the restore activity status of client group
        
        **is_data_aging_enabled**      --      returns the data aging activity status of client group
        
        **is_smart_client_group**      --      returns true if client group is a smart client group
        
        **is_auto_discover_enabled**   --      returns the auto discover status of client group
        
&#34;&#34;&#34;

from __future__ import absolute_import
from __future__ import unicode_literals

import time
import copy

from .exception import SDKException
from .network import Network
from .network_throttle import NetworkThrottle
from .deployment.install import Install


class ClientGroups(object):
    &#34;&#34;&#34;Class for representing all the clientgroups associated with a Commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the ClientGroups class.

            Args:
                commcell_object (object)  --  instance of the Commcell class

            Returns:
                object - instance of the ClientGroups class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._CLIENTGROUPS = self._commcell_object._services[&#39;CLIENTGROUPS&#39;]

        self._clientgroups = None
        self._clientgroups_cache = None
        self._all_client_groups_prop = None
        self.filter_query_count = 0
        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all clientgroups of the Commcell.

            Returns:
                str - string of all the clientgroups for a commcell
        &#34;&#34;&#34;
        representation_string = &#34;{:^5}\t{:^50}\n\n&#34;.format(&#39;S. No.&#39;, &#39;ClientGroup&#39;)

        for index, clientgroup_name in enumerate(self._clientgroups):
            sub_str = &#39;{:^5}\t{:50}\n&#39;.format(index + 1, clientgroup_name)
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the ClientGroups class.

            Returns:
                str - string of all the client groups associated with the commcell
        &#34;&#34;&#34;
        return &#34;ClientGroups class instance for Commcell&#34;

    def __len__(self):
        &#34;&#34;&#34;Returns the number of the client groups associated to the Commcell.&#34;&#34;&#34;
        return len(self.all_clientgroups)

    def __getitem__(self, value):
        &#34;&#34;&#34;Returns the name of the client group for the given client group ID or
            the details of the client group for given client group Name.

            Args:
                value   (str / int)     --  Name or ID of the client group

            Returns:
                str     -   name of the client group, if the client group id was given

                dict    -   dict of details of the client group, if client group name was given

            Raises:
                IndexError:
                    no client group exists with the given Name / Id

        &#34;&#34;&#34;
        value = str(value)

        if value in self.all_clientgroups:
            return self.all_clientgroups[value]
        else:
            try:
                return list(
                    filter(lambda x: x[1][&#39;id&#39;] == value, self.all_clientgroups.items())
                )[0][0]
            except IndexError:
                raise IndexError(&#39;No client group exists with the given Name / Id&#39;)

    def _get_clientgroups(self, full_response: bool = False):
        &#34;&#34;&#34;Gets all the clientgroups associated with the commcell

            Args:
                full_response(bool) --  flag to return complete response

            Returns:
                dict - consists of all clientgroups of the commcell
                    {
                         &#34;clientgroup1_name&#34;: clientgroup1_id,
                         &#34;clientgroup2_name&#34;: clientgroup2_id,
                    }

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._CLIENTGROUPS
        )

        if flag:
            if response.json() and &#39;groups&#39; in response.json():
                if full_response:
                    return response.json()
                clientgroups_dict = {}

                name_count = {}

                for client_group in response.json()[&#39;groups&#39;]:
                    temp_name = client_group[&#39;name&#39;].lower()
                    temp_company = \
                        client_group.get(&#39;clientGroup&#39;, {}).get(&#39;entityInfo&#39;, {}).get(&#39;companyName&#39;, &#39;&#39;).lower()

                    if temp_name in name_count:
                        name_count[temp_name].add(temp_company)
                    else:
                        name_count[temp_name] = {temp_company}

                for client_group in response.json()[&#39;groups&#39;]:
                    temp_name = client_group[&#39;name&#39;].lower()
                    temp_id = str(client_group[&#39;Id&#39;]).lower()
                    temp_company = \
                        client_group.get(&#39;clientGroup&#39;, {}).get(&#39;entityInfo&#39;, {}).get(&#39;companyName&#39;, &#39;&#39;).lower()

                    if len(name_count[temp_name]) &gt; 1:
                        unique_key = f&#34;{temp_name}_({temp_company})&#34;
                    else:
                        unique_key = temp_name

                    clientgroups_dict[unique_key] = temp_id

                return clientgroups_dict
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _valid_clients(self, clients_list):
        &#34;&#34;&#34;Returns only the valid clients specified in the input clients list

            Args:
                clients_list (list)    --  list of the clients to add to the client group

            Returns:
                list - list consisting of the names of all valid clients in the input clients list

            Raises:
                SDKException:
                    if type of clients list argument is not list
        &#34;&#34;&#34;
        if not isinstance(clients_list, list):
            raise SDKException(&#39;ClientGroup&#39;, &#39;101&#39;)

        clients = []

        for client in clients_list:
            if isinstance(client, str):
                client = client.strip().lower()

                if self._commcell_object.clients.has_client(client):
                    clients.append(client)

        return clients

    def _get_fl_parameters(self, fl: list = None) -&gt; str:
        &#34;&#34;&#34;
        Returns the fl parameters to be passed in the mongodb caching api call

        Args:
            fl    (list)  --   list of columns to be passed in API request

        Returns:
            fl_parameters(str) -- fl parameter string
        &#34;&#34;&#34;
        self.valid_columns = {
            &#39;name&#39;: &#39;name&#39;,
            &#39;id&#39;: &#39;groups.Id&#39;,
            &#39;association&#39;: &#39;groups.groupAssocType&#39;,
            &#39;companyName&#39;: &#39;groups.clientGroup.entityInfo.companyName&#39;,
            &#39;tags&#39;: &#39;tags&#39;
        }
        default_columns = &#39;name&#39;

        if fl:
            if all(col in self.valid_columns for col in fl):
                fl_parameters = f&#34;&amp;fl={default_columns},{&#39;,&#39;.join(self.valid_columns[column] for column in fl)}&#34;
            else:
                raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, &#39;Invalid column name passed&#39;)
        else:
            fl_parameters = &#34;&amp;fl=groups.clientGroup,groups.discoverRulesInfo,groups.groupAssocType,groups.Id,&#34; \
                            &#34;groups.name,groups.isCompanySmartClientGroup&#34;

        return fl_parameters

    def _get_sort_parameters(self, sort: list = None) -&gt; str:
        &#34;&#34;&#34;
        Returns the sort parameters to be passed in the mongodb caching api call

        Args:
            sort  (list)  --   contains the name of the column on which sorting will be performed and type of sort
                                valid sor type -- 1 for ascending and -1 for descending
                                e.g. sort = [&#39;name&#39;,&#39;1&#39;]

        Returns:
            sort_parameters(str) -- sort parameter string
        &#34;&#34;&#34;
        sort_type = str(sort[1])
        col = sort[0]
        if col in self.valid_columns.keys() and sort_type in [&#39;1&#39;, &#39;-1&#39;]:
            sort_parameter = &#39;&amp;sort=&#39; + self.valid_columns[col] + &#39;:&#39; + sort_type
        else:
            raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, &#39;Invalid column name passed&#39;)
        return sort_parameter

    def _get_fq_parameters(self, fq: list = None) -&gt; str:
        &#34;&#34;&#34;
        Returns the fq parameters based on the fq list passed
        Args:
             fq     (list) --   contains the columnName, condition and value
                    e.g. fq = [[&#39;name&#39;,&#39;contains&#39;, &#39;test&#39;],[&#39;association&#39;,&#39;eq&#39;, &#39;Manual&#39;]]

        Returns:
            fq_parameters(str) -- fq parameter string
        &#34;&#34;&#34;
        conditions = {&#34;contains&#34;, &#34;notContain&#34;, &#34;eq&#34;, &#34;neq&#34;}
        params = [
            &#34;&amp;fq=groups.isCompanySmartClientGroup:eq:false&#34;,
            &#34;&amp;fq=groups.clientGroup.clientGroupName:neq:Index Servers&#34;
        ]

        for column, condition, *value in fq or []:
            if column not in self.valid_columns:
                raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, &#39;Invalid column name passed&#39;)

            # Handle &#39;tags&#39; column separately
            if column == &#34;tags&#34; and condition == &#34;contains&#34;:
                params.append(f&#34;&amp;tags={value[0]}&#34;)
            elif condition in conditions:
                params.append(f&#34;&amp;fq={self.valid_columns[column]}:{condition.lower()}:{value[0]}&#34;)
            elif condition == &#34;isEmpty&#34; and not value:
                params.append(f&#34;&amp;fq={self.valid_columns[column]}:in:null,&#34;)
            else:
                raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, &#39;Invalid condition passed&#39;)

        return &#34;&#34;.join(params)

    def get_client_groups_cache(self, hard: bool = False, **kwargs) -&gt; dict:
        &#34;&#34;&#34;
        Gets all the client groups present in CommcellEntityCache DB.

        Args:
            hard  (bool)        --   Flag to perform hard refresh on client groups cache.
            **kwargs (dict):
                - fl (list)     --   List of columns to return in response (default: None).
                - sort (list)   --   Contains the name of the column on which sorting will be performed and type of sort.
                                           Valid sort type: 1 for ascending and -1 for descending
                                           e.g. sort = [&#39;columnName&#39;, &#39;1&#39;] (default: None).
                - limit (list)  --   Contains the start and limit parameter value.
                                            Default [&#39;0&#39;, &#39;100&#39;].
                - search (str)  --   Contains the string to search in the commcell entity cache (default: None).
                - fq (list)     --   Contains the columnName, condition and value.
                                            e.g. fq = [[&#39;name&#39;, &#39;contains&#39;, &#39;test&#39;],
                                             [&#39;association&#39;, &#39;eq&#39;, &#39;Manual&#39;]] (default: None).

        Returns:
            dict: Dictionary of all the properties present in response.
        &#34;&#34;&#34;
        # computing params
        fl_parameters = self._get_fl_parameters(kwargs.get(&#39;fl&#39;, None))
        fq_parameters = self._get_fq_parameters(kwargs.get(&#39;fq&#39;, None))
        limit = kwargs.get(&#39;limit&#39;, None)
        limit_parameters = f&#39;start={limit[0]}&amp;limit={limit[1]}&#39; if limit else &#39;&#39;
        hard_refresh = &#39;&amp;hardRefresh=true&#39; if hard else &#39;&#39;
        sort_parameters = self._get_sort_parameters(kwargs.get(&#39;sort&#39;, None)) if kwargs.get(&#39;sort&#39;, None) else &#39;&#39;

        # Search operation can only be performed on limited columns, so filtering out the columns on which search works
        searchable_columns = [&#34;name&#34;,&#34;association&#34;,&#39;companyName&#39;]
        search_parameter = (f&#39;&amp;search={&#34;,&#34;.join(self.valid_columns[col] for col in searchable_columns)}:contains:&#39;
                            f&#39;{kwargs.get(&#34;search&#34;, None)}&#39;) if kwargs.get(&#39;search&#39;, None) else &#39;&#39;

        params = [
            limit_parameters,
            sort_parameters,
            fl_parameters,
            hard_refresh,
            search_parameter,
            fq_parameters
        ]
        request_url = f&#34;{self._CLIENTGROUPS}?&#34; + &#34;&#34;.join(params)
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#34;GET&#34;, request_url)
        if not flag:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        client_group_cache = {}
        if response.json() and &#39;groups&#39; in response.json():
            self.filter_query_count = response.json().get(&#39;filterQueryCount&#39;,0)
            for group in response.json()[&#39;groups&#39;]:
                name = group.get(&#39;name&#39;)
                client_group_config = {
                    &#39;name&#39;: name,
                    &#39;id&#39;: group.get(&#39;Id&#39;),
                    &#39;association&#39;: group.get(&#39;groupAssocType&#39;),
                }
                if &#39;clientGroup&#39; in group:
                    if &#39;companyName&#39; in group.get(&#39;clientGroup&#39;, {}).get(&#39;entityInfo&#39;, {}):
                        client_group_config[&#39;companyName&#39;] = group.get(&#39;clientGroup&#39;, {}).get(&#39;entityInfo&#39;, {}).get(
                            &#39;companyName&#39;)
                    if &#39;tags&#39; in group.get(&#39;clientGroup&#39;, {}):
                        client_group_config[&#39;tags&#39;] = group.get(&#39;clientGroup&#39;, {}).get(&#39;tags&#39;)
                client_group_cache[name] = client_group_config

            return client_group_cache
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

    @property
    def all_clientgroups(self):
        &#34;&#34;&#34;Returns dict of all the clientgroups associated with this commcell

            dict - consists of all clientgroups of the commcell
                    {
                         &#34;clientgroup1_name&#34;: clientgroup1_id,
                         &#34;clientgroup2_name&#34;: clientgroup2_id,
                    }
        &#34;&#34;&#34;
        return self._clientgroups

    @property
    def all_clientgroups_cache(self) -&gt; dict:
        &#34;&#34;&#34;Returns dict of all the client groups and their info present in CommcellEntityCache in mongoDB

            dict - consists of all client groups of the in the CommcellEntityCache
                    {
                         &#34;clientgroup1_name&#34;: {
                                &#34;id&#34;: clientgroup1_id,
                                &#34;association&#34;: clientgroup1 association type,
                                &#34;company&#34;: clientgroup1 company
                                &#34;tags&#34;: clientgroup1 tags
                                },
                         &#34;clientgroup2_name&#34;: {
                                &#34;id&#34;: clientgroup2_id,
                                &#34;association&#34;: clientgroup2 association type,
                                &#34;company&#34;: clientgroup2 company
                                &#34;tags&#34;: clientgroup2 tags
                                }
                    }
        &#34;&#34;&#34;
        if not self._clientgroups_cache:
            self._clientgroups_cache = self.get_client_groups_cache()
        return self._clientgroups_cache

    def has_clientgroup(self, clientgroup_name):
        &#34;&#34;&#34;Checks if a client group exists in the commcell with the input client group name.

            Args:
                clientgroup_name (str)  --  name of the client group

            Returns:
                bool - boolean output whether the client group exists in the commcell or not

            Raises:
                SDKException:
                    if type of the client group name argument is not string
        &#34;&#34;&#34;
        if not isinstance(clientgroup_name, str):
            raise SDKException(&#39;ClientGroup&#39;, &#39;101&#39;)

        return self._clientgroups and clientgroup_name.lower() in self._clientgroups

    def create_smart_rule(self,
                          filter_rule=&#39;OS Type&#39;,
                          filter_condition=&#39;equal to&#39;,
                          filter_value=&#39;Windows&#39;,
                          value=&#39;1&#39;):
        &#34;&#34;&#34;Create/Prepare rules required for smart client group creation based on input parameters

            Args:
                filter_rule (str)      --  Rule selection to match specific criterion

                filter_condition (str) --  Filter value between selections in rule

                filter_value(str)     --   Value of rule criterion

                value(str)            --   value required to create rule

            Returns:
                    dict    -   consists of single rule based on inputs
                {
                    &#34;rule&#34;: {
                        &#34;filterID&#34;: 100,
                        &#34;secValue&#34;: &#39;Windows&#39;,
                        &#34;propID&#34;: 8,
                        &#34;propType&#34;: 4,
                        &#34;value&#34;: &#39;1&#39;
                    }
                }
        &#34;&#34;&#34;

        filter_dict = {
            &#39;equal to&#39;: 100,
            &#39;not equal&#39;: 101,
            &#39;any in selection&#39;: 108,
            &#39;not in selection&#39;: 109,
            &#39;is true&#39;: 1,
            &#39;is false&#39;: 2,
            &#39;contains&#39;: 10,
            &#39;starts with&#39;: 14,
            &#39;ends with&#39;: 15,
            &#39;does not contain&#39;: 11,
            }
        prop_id_dict = {
            &#39;Name&#39;: 1,
            &#39;Client&#39;: 2,
            &#39;Agents Installed&#39;: 3,
            &#39;Associated Client Group&#39;: 4,
            &#39;Timezone&#39;: 5,
            &#39;Hostname&#39;: 6,
            &#39;Client Version&#39;: 7,
            &#39;OS Type&#39;: 8,
            &#39;Package Installed&#39;: 9,
            &#39;Client offline (days)&#39;: 10,
            &#39;User as client owner&#39;: 11,
            &#39;Local user group as client owner&#39;: 12,
            &#39;External group as client owner&#39;: 13,
            &#39;Associated library name&#39;: 14,
            &#39;OS Version&#39;: 15,
            &#39;Product Version&#39;: 16,
            &#39;Client Version Same as CS Version&#39;: 17,
            &#39;Days since client created&#39;: 18,
            &#39;Days since last backup&#39;: 19,
            &#39;SnapBackup clients&#39;: 20,
            &#39;Clients with attached storages&#39;: 21,
            &#39;Case manager hold clients&#39;: 22,
            &#39;MediaAgents for clients in group&#39;: 23,
            &#39;Client acts as proxy&#39;: 24,
            &#39;Backup activity enabled&#39;: 25,
            &#39;Restore activity enabled&#39;: 26,
            &#39;Client online (days)&#39;: 27,
            &#39;Inactive AD user as client owner&#39;: 28,
            &#39;Client excluded from SLA report&#39;: 29,
            &#39;Client uses storage policy&#39;: 30,
            &#39;Client is not ready&#39;: 31,
            &#39;Associated Storage Policy&#39;: 32,
            &#39;MediaAgent has Lucene Index Roles&#39;: 33,
            &#39;Client associated with plan&#39;: 34,
            &#39;Client by Schedule Interval&#39;: 35,
            &#39;Client needs Updates&#39;: 36,
            &#39;Subclient Name&#39;: 37,
            &#39;CommCell Psuedo Client&#39;: 38,
            &#39;Client Description&#39;: 39,
            &#39;Clients discovered using VSA Subclient&#39;: 40,
            &#39;Clients with no Archive Data&#39;: 41,
            &#39;User Client Provider Associations&#39;: 42,
            &#39;User Group Client Provider Associations&#39;: 43,
            &#39;Company Client Provider Associations&#39;: 44,
            &#39;Clients Meet SLA&#39;: 45,
            &#39;Index Servers&#39;: 46,
            &#39;Clients with OnePass enabled&#39;: 49,
            &#39;Clients by Role&#39;: 50,
            &#39;Clients by Permission&#39;: 51,
            &#39;User description contains&#39;: 52,
            &#39;User Group description contains&#39;: 53,
            &#39;Content Analyzer Cloud&#39;: 54,
            &#39;Company Installed Client Associations&#39;: 55,
            &#39;Client Online in Last 30 Days&#39;: 56,
            &#39;Clients With Subclients Having Associated Storage Policy&#39;: 60,
            &#39;Clients With Improperly Deconfigured Subclients&#39;: 61,
            &#39;Strikes count&#39;: 62,
            &#39;Clients With Backup Schedule&#39;: 63,
            &#39;Clients With Long Running Jobs&#39;: 64,
            &#39;Clients With Synthetic Full Backup N Days&#39;: 67,
            &#39;MediaAgents for clients in group list&#39;: 70,
            &#39;Associated Client Group List&#39;: 71,
            &#39;Timezone List&#39;: 72,
            &#39;MediaAgent has Lucene Index Role List&#39;: 73,
            &#39;Associated Storage Policy List&#39;: 74,
            &#39;Timezone Region List&#39;: 75,
            &#39;Clients With Encryption&#39;: 80,
            &#39;Client CIDR Address Range&#39;: 81,
            &#39;HAC Cluster&#39;: 85,
            &#39;Client Display Name&#39;: 116,
            &#39;Clients associated to any company&#39;: 158,
            &#39;VMs not in any Subclient Content&#39;: 166,
            &#39;Pseudo Clients&#39;: 115,
            }
        ptype_dict = {
            &#39;Name&#39;: 2,
            &#39;Client&#39;: 4,
            &#39;Agents Installed&#39;: 6,
            &#39;Associated Client Group&#39;: 4,
            &#39;Timezone&#39;: 4,
            &#39;Hostname&#39;: 2,
            &#39;Client Version&#39;: 4,
            &#39;OS Type&#39;: 4,
            &#39;Package Installed&#39;: 6,
            &#39;Client offline (days)&#39;: 3,
            &#39;User as client owner&#39;: 2,
            &#39;Local user group as client owner&#39;: 2,
            &#39;External group as client owner&#39;: 2,
            &#39;Associated library name&#39;: 2,
            &#39;OS Version&#39;: 2,
            &#39;Product Version&#39;: 2,
            &#39;Client Version Same as CS Version&#39;: 1,
            &#39;Days since client created&#39;: 3,
            &#39;Days since last backup&#39;: 3,
            &#39;SnapBackup clients&#39;: 1,
            &#39;Clients with attached storages&#39;: 1,
            &#39;Case manager hold clients&#39;: 1,
            &#39;MediaAgents for clients in group&#39;: 2,
            &#39;Client acts as proxy&#39;: 1,
            &#39;Backup activity enabled&#39;: 1,
            &#39;Restore activity enabled&#39;: 1,
            &#39;Client online (days)&#39;: 3,
            &#39;Inactive AD user as client owner&#39;: 1,
            &#39;Client excluded from SLA report&#39;: 1,
            &#39;Client uses storage policy&#39;: 2,
            &#39;Client is not ready&#39;: 1,
            &#39;Associated Storage Policy&#39;: 4,
            &#39;MediaAgent has Lucene Index Roles&#39;: 4,
            &#39;Client associated with plan&#39;: 2,
            &#39;Client by Schedule Interval&#39;: 4,
            &#39;Client needs Updates&#39;: 1,
            &#39;Subclient Name&#39;: 2,
            &#39;CommCell Psuedo Client&#39;: 1,
            &#39;Client Description&#39;: 2,
            &#39;Clients discovered using VSA Subclient&#39;: 6,
            &#39;Clients with no Archive Data&#39;: 1,
            &#39;User Client Provider Associations&#39;: 2,
            &#39;User Group Client Provider Associations&#39;: 2,
            &#39;Company Client Provider Associations&#39;: 4,
            &#39;Clients Meet SLA&#39;: 4,
            &#39;Index Servers&#39;: 1,
            &#39;Clients with OnePass enabled&#39;: 1,
            &#39;Clients by Role&#39;: 4,
            &#39;Clients by Permission&#39;: 4,
            &#39;User description contains&#39;: 2,
            &#39;User Group description contains&#39;: 2,
            &#39;Content Analyzer Cloud&#39;: 1,
            &#39;Company Installed Client Associations&#39;: 4,
            &#39;Client Online in Last 30 Days&#39;: 1,
            &#39;Clients With Subclients Having Associated Storage Policy&#39;: 1,
            &#39;Clients With Improperly Deconfigured Subclients&#39;: 1,
            &#39;Strikes count&#39;: 3,
            &#39;Clients With Backup Schedule&#39;: 1,
            &#39;Clients With Long Running Jobs&#39;: 3,
            &#39;Clients With Synthetic Full Backup N Days&#39;: 3,
            &#39;MediaAgents for clients in group list&#39;: 7,
            &#39;Associated Client Group List&#39;: 7,
            &#39;Timezone List&#39;: 7,
            &#39;MediaAgent has Lucene Index Role List&#39;: 7,
            &#39;Associated Storage Policy List&#39;: 7,
            &#39;Timezone Region List&#39;: 7,
            &#39;Clients With Encryption&#39;: 1,
            &#39;Client CIDR Address Range&#39;: 10,
            &#39;HAC Cluster&#39;: 1,
            &#39;Client Display Name&#39;: 2,
            &#39;Clients associated to any company&#39;: 1,
            &#39;VMs not in any Subclient Content&#39;: 1,
            &#39;Pseudo Clients&#39;: 1,
            }

        rule_mk = {
            &#34;rule&#34;: {
                &#34;filterID&#34;: filter_dict[filter_condition],
                &#34;secValue&#34;: filter_value,
                &#34;propID&#34;: prop_id_dict[filter_rule],
                &#34;propType&#34;: ptype_dict[filter_rule],
                &#34;value&#34;: value
            }
            }

        return rule_mk

    def merge_smart_rules(self, rule_list, op_value=&#39;all&#39;, scg_op=&#39;all&#39;):
        &#34;&#34;&#34;Merge multiple rules into (SCG) rule to create smart client group.

            Args:
                rule_list (list)  --  List of smart rules to be added in rule group

                op_value (str)--     condition to apply between smart rules
                ex: all, any,not any

                scg_op (str)--       condition to apply between smart rule groups (@group level)

            Returns:
               scg_rule (dict)    -   Rule group to create smart client group

        &#34;&#34;&#34;

        op_dict = {
            &#39;all&#39;: 0,
            &#39;any&#39;: 1,
            &#39;not any&#39;: 2
        }
        scg_rule = {
            &#34;op&#34;: op_dict[scg_op],
            &#34;rules&#34;: [
            ]
        }
        rules_dict = {
            &#34;rule&#34;: {
                &#34;op&#34;: op_dict[op_value],
                &#34;rules&#34;: [
                ]
            }
        }

        for each_rule in rule_list:
            rules_dict[&#34;rule&#34;][&#34;rules&#34;].append(each_rule)

        scg_rule[&#34;rules&#34;].append(rules_dict)
        return scg_rule

    def _create_scope_dict(self, client_scope, value=None):
        &#34;&#34;&#34;Creates required dictionary for given client scope

            Args:
                value (string)  --  Value to be selected for the client scope dropdown
                client_scope (string) -- Value of the client scope

            Accepted Values (client_scope) --
                Clients in this Commcell
                Clients of Companies
                Clients of User
                Clients of User Groups

            Returns:
                dictionary - Client Scope data required for the smart client group

            NOTE : Value is not required for client scope = &#34;Clients in this Commcell&#34;
            For this, value is automatically set to the Commserve Name
        &#34;&#34;&#34;
        scgscope = {
            &#34;entity&#34;: {}
        }
        if client_scope.lower() == &#39;clients in this commcell&#39;:
            scgscope[&#34;entity&#34;] = {
                &#34;commCellName&#34;: self._commcell_object.commserv_name,
                &#34;_type_&#34;: 1
            }
        elif client_scope.lower() == &#39;clients of companies&#39; and value is not None:
            scgscope[&#34;entity&#34;] = {
                &#34;providerDomainName&#34;: value,
                &#34;_type_&#34;: 61
            }
        elif client_scope.lower() == &#39;clients of user&#39; and value is not None:
            scgscope[&#34;entity&#34;] = {
                &#34;userName&#34;: value,
                &#34;_type_&#34;: 13
            }
        elif client_scope.lower() == &#39;clients of user group&#39; and value is not None:
            scgscope[&#34;entity&#34;] = {
                &#34;userGroupName&#34;: value,
                &#34;_type_&#34;: 15
            }
        return scgscope

    def add(self, clientgroup_name, clients=[], **kwargs):
        &#34;&#34;&#34;Adds a new Client Group to the Commcell.

            Args:
                clientgroup_name        (str)        --  name of the new client group to add

                clients                 (str/list)   --  &#39;,&#39; separated string of client names,
                                                             or a list of clients,
                                                             to be added under client group
                                                            default: []

                ** kwargs               (dict)       -- Key value pairs for supported arguments

                Supported:

                    clientgroup_description (str)        --  description of the client group
                                                                default: &#34;&#34;

                    enable_backup           (bool)       --  enable or disable backup
                                                                default: True

                    enable_restore          (bool)       --  enable or disable restore
                                                                default: True

                    enable_data_aging       (bool)       --  enable or disable data aging
                                                                default: True
                    scg_rule                (dict)       --  scg_rule required to create smart
                                                                client group

                    client_scope            (str)        --  Client scope for the Smart Client Group

                    client_scope_value      (str)        --  Client scope value for a particular scope

            Returns:
                object - instance of the ClientGroup class created by this method

            Raises:
                SDKException:
                    if type of client group name and description is not of type string

                    if clients argument is not of type list / string

                    if response is empty

                    if response is not success

                    if client group already exists with the given name
        &#34;&#34;&#34;
        if not (isinstance(clientgroup_name, str) and
                isinstance(kwargs.get(&#39;clientgroup_description&#39;, &#39;&#39;), str)):
            raise SDKException(&#39;ClientGroup&#39;, &#39;101&#39;)

        if not self.has_clientgroup(clientgroup_name):
            if isinstance(clients, list):
                clients = self._valid_clients(clients)
            elif isinstance(clients, str):
                clients = self._valid_clients(clients.split(&#39;,&#39;))
            else:
                raise SDKException(&#39;ClientGroup&#39;, &#39;101&#39;)

            clients_list = []

            for client in clients:
                clients_list.append({&#39;clientName&#39;: client})

            smart_client_group = bool(kwargs.get(&#39;scg_rule&#39;))
            if kwargs.get(&#39;scg_rule&#39;) is None:
                kwargs[&#39;scg_rule&#39;] = {}

            request_json = {
                &#34;clientGroupOperationType&#34;: 1,
                &#34;clientGroupDetail&#34;: {
                    &#34;description&#34;: kwargs.get(&#39;clientgroup_description&#39;, &#39;&#39;),
                    &#34;isSmartClientGroup&#34;: smart_client_group,
                    &#34;scgRule&#34;: kwargs.get(&#39;scg_rule&#39;),
                    &#34;clientGroup&#34;: {
                        &#34;clientGroupName&#34;: clientgroup_name
                    },
                    &#34;associatedClients&#34;: clients_list
                }
            }

            scg_scope = None
            if kwargs.get(&#34;client_scope&#34;) is not None:
                # Check if value is there or not
                if kwargs.get(&#34;client_scope&#34;).lower() == &#34;clients in this commcell&#34;:
                    scg_scope = [self._create_scope_dict(kwargs.get(&#34;client_scope&#34;))]
                else:
                    if kwargs.get(&#34;client_scope_value&#34;) is not None:
                        scg_scope = [self._create_scope_dict(kwargs.get(&#34;client_scope&#34;), kwargs.get(&#34;client_scope_value&#34;))]
                    else:
                        raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;,
                                           &#34;Client Scope {0} requires a value&#34;.format(kwargs.get(&#34;client_scope&#34;)))

            if scg_scope is not None:
                request_json[&#34;clientGroupDetail&#34;][&#34;scgScope&#34;] = scg_scope

            if kwargs.get(&#34;enable_backup&#34;) or kwargs.get(&#34;enable_data_aging&#34;) or kwargs.get(&#34;enable_restore&#34;):
                client_group_activity_control = {
                        &#34;activityControlOptions&#34;: [
                            {
                                &#34;activityType&#34;: 1,
                                &#34;enableAfterADelay&#34;: False,
                                &#34;enableActivityType&#34;: kwargs.get(&#39;enable_backup&#39;, True)
                            }, {
                                &#34;activityType&#34;: 16,
                                &#34;enableAfterADelay&#34;: False,
                                &#34;enableActivityType&#34;: kwargs.get(&#39;enable_data_aging&#39;, True)
                            }, {
                                &#34;activityType&#34;: 2,
                                &#34;enableAfterADelay&#34;: False,
                                &#34;enableActivityType&#34;: kwargs.get(&#39;enable_restore&#39;, True)
                            }
                        ]
                    }
                request_json[&#34;clientGroupDetail&#34;][&#34;clientGroupActivityControl&#34;] = client_group_activity_control

            flag, response = self._commcell_object._cvpysdk_object.make_request(
                &#39;POST&#39;, self._CLIENTGROUPS, request_json
            )

            if flag:
                if response.json():
                    error_message = None

                    if &#39;errorMessage&#39; in response.json():
                        error_message = response.json()[&#39;errorMessage&#39;]
                        o_str = &#39;Failed to create new ClientGroup\nError:&#34;{0}&#34;&#39;.format(
                            error_message
                        )
                        raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)
                    elif &#39;clientGroupDetail&#39; in response.json():
                        self.refresh()
                        clientgroup_id = response.json()[&#39;clientGroupDetail&#39;][
                            &#39;clientGroup&#39;][&#39;clientGroupId&#39;]

                        return ClientGroup(
                            self._commcell_object, clientgroup_name, clientgroup_id
                        )
                    else:
                        o_str = &#39;Failed to create new ClientGroup&#39;
                        raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                response_string = self._commcell_object._update_response_(response.text)
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        else:
            raise SDKException(
                &#39;ClientGroup&#39;, &#39;102&#39;, &#39;Client Group &#34;{0}&#34; already exists.&#39;.format(clientgroup_name)
            )

    def get(self, clientgroup_name):
        &#34;&#34;&#34;Returns a client group object of the specified client group name.

            Args:
                clientgroup_name (str)  --  name of the client group

            Returns:
                object - instance of the ClientGroup class for the given clientgroup name

            Raises:
                SDKException:
                    if type of the client group name argument is not string

                    if no client group exists with the given name
        &#34;&#34;&#34;
        if not isinstance(clientgroup_name, str):
            raise SDKException(&#39;ClientGroup&#39;, &#39;101&#39;)
        else:
            clientgroup_name = clientgroup_name.lower()

            if self.has_clientgroup(clientgroup_name):
                return ClientGroup(
                    self._commcell_object, clientgroup_name, self._clientgroups[clientgroup_name]
                )

            raise SDKException(
                &#39;ClientGroup&#39;,
                &#39;102&#39;,
                &#39;No ClientGroup exists with name: {0}&#39;.format(clientgroup_name)
            )

    def delete(self, clientgroup_name):
        &#34;&#34;&#34;Deletes the clientgroup from the commcell.

            Args:
                clientgroup_name (str)  --  name of the clientgroup

            Raises:
                SDKException:
                    if type of the clientgroup name argument is not string

                    if response is empty

                    if failed to delete the client group

                    if no clientgroup exists with the given name
        &#34;&#34;&#34;

        if not isinstance(clientgroup_name, str):
            raise SDKException(&#39;ClientGroup&#39;, &#39;101&#39;)
        else:
            clientgroup_name = clientgroup_name.lower()

            if self.has_clientgroup(clientgroup_name):
                clientgroup_id = self._clientgroups[clientgroup_name]

                delete_clientgroup_service = self._commcell_object._services[&#39;CLIENTGROUP&#39;]

                flag, response = self._commcell_object._cvpysdk_object.make_request(
                    &#39;DELETE&#39;, delete_clientgroup_service % clientgroup_id
                )

                if flag:
                    if response.json():
                        if &#39;errorCode&#39; in response.json():
                            error_code = str(response.json()[&#39;errorCode&#39;])
                            error_message = response.json()[&#39;errorMessage&#39;]

                            if error_code == &#39;0&#39;:
                                # initialize the clientgroups again
                                # so the clientgroups object has all the client groups
                                self.refresh()
                            else:
                                o_str = &#39;Failed to delete ClientGroup\nError: &#34;{0}&#34;&#39;.format(
                                    error_message
                                )
                                raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)
                        else:
                            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
                    else:
                        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
                else:
                    response_string = self._commcell_object._update_response_(response.text)
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
            else:
                raise SDKException(
                    &#39;ClientGroup&#39;,
                    &#39;102&#39;,
                    &#39;No ClientGroup exists with name: &#34;{0}&#34;&#39;.format(clientgroup_name)
                )

    def refresh(self, **kwargs):
        &#34;&#34;&#34;
        Refresh the list of client groups on this commcell.

            Args:
                **kwargs (dict):
                    mongodb (bool)  -- Flag to fetch client groups cache from MongoDB (default: False).
                    hard (bool)     -- Flag to hard refresh MongoDB cache for this entity (default: False).
        &#34;&#34;&#34;
        mongodb = kwargs.get(&#39;mongodb&#39;, False)
        hard = kwargs.get(&#39;hard&#39;, False)

        self._clientgroups = self._get_clientgroups()
        if mongodb:
            self._clientgroups_cache = self.get_client_groups_cache(hard=hard)

    @property
    def all_client_groups_prop(self)-&gt;list[dict]:
        &#34;&#34;&#34;
        Returns complete GET API response
        &#34;&#34;&#34;
        self._all_client_groups_prop = self._get_clientgroups(full_response=True).get(&#34;groups&#34;,[])
        return self._all_client_groups_prop


class ClientGroup(object):
    &#34;&#34;&#34;Class for performing operations for a specific ClientGroup.&#34;&#34;&#34;

    def __init__(self, commcell_object, clientgroup_name, clientgroup_id=None):
        &#34;&#34;&#34;Initialise the ClientGroup class instance.

            Args:
                commcell_object     (object)   --  instance of the Commcell class

                clientgroup_name    (str)      --  name of the clientgroup

                clientgroup_id      (str)      --  id of the clientgroup
                    default: None

            Returns:
                object - instance of the ClientGroup class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object

        self._clientgroup_name = clientgroup_name.lower()

        if clientgroup_id:
            # Use the client group id provided in the arguments
            self._clientgroup_id = str(clientgroup_id)
        else:
            # Get the id associated with this client group
            self._clientgroup_id = self._get_clientgroup_id()

        self._CLIENTGROUP = self._commcell_object._services[&#39;CLIENTGROUP&#39;] % (self.clientgroup_id)

        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_

        self._properties = None
        self._description = None
        self._is_backup_enabled = None
        self._is_restore_enabled = None
        self._is_data_aging_enabled = None
        self._is_smart_client_group = None
        self._company_name = None

        self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.

            Returns:
                str - string containing the details of this ClientGroup
        &#34;&#34;&#34;
        representation_string = &#39;ClientGroup class instance for ClientGroup: &#34;{0}&#34;&#39;
        return representation_string.format(self.clientgroup_name)

    def _get_clientgroup_id(self):
        &#34;&#34;&#34;Gets the clientgroup id associated with this clientgroup.

            Returns:
                str - id associated with this clientgroup
        &#34;&#34;&#34;
        clientgroups = ClientGroups(self._commcell_object)
        return clientgroups.get(self.clientgroup_name).clientgroup_id

    def _get_clientgroup_properties(self):
        &#34;&#34;&#34;Gets the clientgroup properties of this clientgroup.

            Returns:
                dict - dictionary consisting of the properties of this clientgroup

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._CLIENTGROUP
        )

        if flag:
            if response.json() and &#39;clientGroupDetail&#39; in response.json():
                return response.json()[&#39;clientGroupDetail&#39;]
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _initialize_clientgroup_properties(self):
        &#34;&#34;&#34;Initializes the common properties for the clientgroup.&#34;&#34;&#34;
        clientgroup_props = self._get_clientgroup_properties()
        self._properties = clientgroup_props

        if &#39;clientGroupName&#39; in clientgroup_props[&#39;clientGroup&#39;]:
            self._clientgroup_name = clientgroup_props[&#39;clientGroup&#39;][&#39;clientGroupName&#39;].lower()
        else:
            raise SDKException(
                &#39;ClientGroup&#39;, &#39;102&#39;, &#39;Client Group name is not specified in the response&#39;
            )

        self._description = None

        if &#39;description&#39; in clientgroup_props:
            self._description = clientgroup_props[&#39;description&#39;]

        self._associated_clients = []

        if &#39;associatedClients&#39; in clientgroup_props:
            for client in clientgroup_props[&#39;associatedClients&#39;]:
                self._associated_clients.append(client[&#39;clientName&#39;])

        self._is_smart_client_group = self._properties[&#39;isSmartClientGroup&#39;]
        self._is_backup_enabled = False
        self._is_restore_enabled = False
        self._is_data_aging_enabled = False

        if &#39;clientGroupActivityControl&#39; in clientgroup_props:
            cg_activity_control = clientgroup_props[&#39;clientGroupActivityControl&#39;]

            if &#39;activityControlOptions&#39; in cg_activity_control:
                for control_options in cg_activity_control[&#39;activityControlOptions&#39;]:
                    if control_options[&#39;activityType&#39;] == 1:
                        self._is_backup_enabled = control_options[&#39;enableActivityType&#39;]
                    elif control_options[&#39;activityType&#39;] == 2:
                        self._is_restore_enabled = control_options[&#39;enableActivityType&#39;]
                    elif control_options[&#39;activityType&#39;] == 16:
                        self._is_data_aging_enabled = control_options[&#39;enableActivityType&#39;]

        self._company_name = clientgroup_props.get(&#39;securityAssociations&#39;, {}).get(&#39;tagWithCompany&#39;, {}).get(&#39;providerDomainName&#39;)

    def _request_json_(self, option, enable=True, enable_time=None, **kwargs):
        &#34;&#34;&#34;Returns the JSON request to pass to the API as per the options selected by the user.

            Args:
                option (str)  --  string option for which to run the API for
                    e.g.; Backup / Restore / Data Aging

                **kwargs (dict)  -- dict of keyword arguments as follows

                    timezone    (str)   -- timezone to be used of the operation

                        **Note** make use of TIMEZONES dict in constants.py to pass timezone

                        **Note** In case of linux CommServer provide time in GMT timezone

            Returns:
                dict - JSON request to pass to the API
        &#34;&#34;&#34;
        options_dict = {
            &#34;Backup&#34;: 1,
            &#34;Restore&#34;: 2,
            &#34;Data Aging&#34;: 16
        }

        request_json1 = {
            &#34;clientGroupOperationType&#34;: 2,
            &#34;clientGroupDetail&#34;: {
                &#34;clientGroupActivityControl&#34;: {
                    &#34;activityControlOptions&#34;: [{
                        &#34;activityType&#34;: options_dict[option],
                        &#34;enableAfterADelay&#34;: False,
                        &#34;enableActivityType&#34;: enable
                    }]
                },
                &#34;clientGroup&#34;: {
                    &#34;newName&#34;: self.name
                }
            }
        }

        request_json2 = {
            &#34;clientGroupOperationType&#34;: 2,
            &#34;clientGroupDetail&#34;: {
                &#34;clientGroupActivityControl&#34;: {
                    &#34;activityControlOptions&#34;: [{
                        &#34;activityType&#34;: options_dict[option],
                        &#34;enableAfterADelay&#34;: True,
                        &#34;enableActivityType&#34;: False,
                        &#34;dateTime&#34;: {
                            &#34;TimeZoneName&#34;: kwargs.get(&#34;timezone&#34;, self._commcell_object.default_timezone),
                            &#34;timeValue&#34;: enable_time
                        }
                    }]
                },
                &#34;clientGroup&#34;: {
                    &#34;newName&#34;: self.name
                }
            }
        }

        if enable_time:
            return request_json2
        else:
            return request_json1

    def _process_update_request(self, request_json):
        &#34;&#34;&#34;Runs the Clientgroup update API

            Args:
                request_json    (dict)  -- request json sent as payload

            Returns:
                (str, str):
                    str  -  error code received in the response

                    str  -  error message received in the response

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._CLIENTGROUP, request_json
        )

        if flag:
            if response.json():
                error_code = str(response.json()[&#39;errorCode&#39;])

                if &#39;errorMessage&#39; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]
                else:
                    error_message = &#34;&#34;

                self.refresh()
                return error_code, error_message
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        response_string = self._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _update(
            self,
            clientgroup_name,
            clientgroup_description,
            associated_clients=None,
            operation_type=&#34;NONE&#34;):
        &#34;&#34;&#34;Update the clientgroup properties of this clientgroup.

            Args:
                clientgroup_name        (str)       --  new name of the clientgroup

                clientgroup_description (str)       --  description for the clientgroup

                associated_clients      (str/list)  --  &#39;,&#39; separated string of client names,
                                                            or a list of clients,
                                                            to be added/removed under client group
                    default: None

                operation_type          (str)       --  associated clients operation type
                        Valid values: NONE, OVERWRITE, ADD, DELETE, CLEAR
                    default: NONE

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        clients_list = []

        associated_clients_op_types = {
            &#34;NONE&#34;: 0,
            &#34;OVERWRITE&#34;: 1,
            &#34;ADD&#34;: 2,
            &#34;DELETE&#34;: 3,
            &#34;CLEAR&#34;: 4
        }

        for client in associated_clients:
            clients_list.append({&#39;clientName&#39;: client})

        request_json = {
            &#34;clientGroupOperationType&#34;: 2,
            &#34;clientGroupDetail&#34;: {
                &#34;description&#34;: clientgroup_description,
                &#34;clientGroup&#34;: {
                    &#34;newName&#34;: clientgroup_name
                },
                &#34;associatedClientsOperationType&#34;: associated_clients_op_types[operation_type],
                &#34;associatedClients&#34;: clients_list
            }
        }

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._CLIENTGROUP, request_json
        )

        self.refresh()

        if flag:
            if response.json():

                error_message = response.json()[&#39;errorMessage&#39;]
                error_code = str(response.json()[&#39;errorCode&#39;])

                if error_code == &#39;0&#39;:
                    return (True, &#34;0&#34;, &#34;&#34;)
                else:
                    return (False, error_code, error_message)

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _add_or_remove_clients(self, clients, operation_type):
        &#34;&#34;&#34;Adds/Removes clients to/from the ClientGroup.

        Args:
                clients         (str/list)   --  &#39;,&#39; separated string of client names,
                                                     or a list of clients,
                                                     to be added under client group

                operation_type  (bool)       --  type of operation to run for the request
                    ADD / OVERWRITE / DELETE

            Raises:
                SDKException:
                    if clients is not of type string / list

                    if no valid clients are found

                    if failed to add clients to ClientGroup

                                    OR

                    if failed to remove clients from the ClientGroup
        &#34;&#34;&#34;
        if isinstance(clients, (str, list)):
            clientgroups_object = ClientGroups(self._commcell_object)

            if isinstance(clients, list):
                validated_clients_list = clientgroups_object._valid_clients(clients)
            elif isinstance(clients, str):
                validated_clients_list = clientgroups_object._valid_clients(clients.split(&#39;,&#39;))

            if operation_type in [&#39;ADD&#39;, &#39;OVERWRITE&#39;]:
                for client in validated_clients_list:
                    if client in self._associated_clients:
                        validated_clients_list.remove(client)

            if not validated_clients_list:
                raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, &#39;No valid clients were found&#39;)

            output = self._update(
                clientgroup_name=self.name,
                clientgroup_description=self.description,
                associated_clients=validated_clients_list,
                operation_type=operation_type
            )

            exception_message_dict = {
                &#39;ADD&#39;: &#39;Failed to add clients to the ClientGroup\nError: &#34;{0}&#34;&#39;,
                &#39;OVERWRITE&#39;: &#39;Failed to add clients to the ClientGroup\nError: &#34;{0}&#34;&#39;,
                &#39;DELETE&#39;: &#39;Failed to remove clients from the ClientGroup\nError: &#34;{0}&#34;&#39;
            }

            if output[0]:
                return
            else:
                o_str = exception_message_dict[operation_type]
                raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str.format(output[2]))
        else:
            raise SDKException(
                &#39;ClientGroup&#39;, &#39;102&#39;, &#39;Client\&#39;s name should be a list or string value&#39;
            )

    @property
    def properties(self):
        &#34;&#34;&#34;Returns the client group properties&#34;&#34;&#34;
        return copy.deepcopy(self._properties)

    @property
    def name(self):
        &#34;&#34;&#34;Returns the client group display name&#34;&#34;&#34;
        return self._properties[&#39;clientGroup&#39;][&#39;clientGroupName&#39;]

    @property
    def clientgroup_id(self):
        &#34;&#34;&#34;Treats the clientgroup id as a read-only attribute.&#34;&#34;&#34;
        return self._clientgroup_id

    @property
    def clientgroup_name(self):
        &#34;&#34;&#34;Treats the clientgroup name as a read-only attribute.&#34;&#34;&#34;
        return self._clientgroup_name

    @property
    def description(self):
        &#34;&#34;&#34;Treats the clientgroup description as a read-only attribute.&#34;&#34;&#34;
        return self._description

    @property
    def associated_clients(self):
        &#34;&#34;&#34;Treats the clients associated to the ClientGroup as a read-only attribute.&#34;&#34;&#34;
        return self._associated_clients

    @property
    def is_backup_enabled(self):
        &#34;&#34;&#34;Treats the clientgroup backup attribute as a property of the ClientGroup class.&#34;&#34;&#34;
        return self._is_backup_enabled

    @property
    def is_restore_enabled(self):
        &#34;&#34;&#34;Treats the clientgroup restore attribute as a propetry of the ClientGroup class.&#34;&#34;&#34;
        return self._is_restore_enabled

    @property
    def is_data_aging_enabled(self):
        &#34;&#34;&#34;Treats the clientgroup data aging attribute as a property of the ClientGroup class.&#34;&#34;&#34;
        return self._is_data_aging_enabled

    @property
    def is_smart_client_group(self):
        &#34;&#34;&#34;Returns boolean indicating whether client group is smart client group&#34;&#34;&#34;
        return self._is_smart_client_group
    
    @property
    def company_name(self):
        &#34;&#34;&#34;Returns company name to which client group belongs to&#34;&#34;&#34;
        return self._company_name
    
    @property
    def network(self):
        &#34;&#34;&#34;Returns the object of Network class.&#34;&#34;&#34;
        if self._networkprop is None:
            self._networkprop = Network(self)

        return self._networkprop

    @property
    def network_throttle(self):
        &#34;&#34;&#34;Returns the object of NetworkThrottle class&#34;&#34;&#34;
        if self._network_throttle is None:
            self._network_throttle = NetworkThrottle(self)

        return self._network_throttle

    @property
    def client_group_filter(self):
        &#34;&#34;&#34;Returns the client group filters&#34;&#34;&#34;
        client_group_filters = {}

        os_type_map = {
            1: &#39;windows_filters&#39;,
            2: &#39;unix_filters&#39;
        }

        for filters_root in self._properties[&#39;globalFiltersInfo&#39;][&#39;globalFiltersInfoList&#39;]:
            client_group_filters[os_type_map[filters_root[&#39;operatingSystemType&#39;]]] = filters_root.get(
                &#39;globalFilters&#39;, {}).get(&#39;filters&#39;, [])

        return client_group_filters

    @property
    def is_auto_discover_enabled(self):
        &#34;&#34;&#34;Returns boolen for clientgroup autodiscover attribute whether property is enabled or not.&#34;&#34;&#34;
        return self._properties.get(&#39;enableAutoDiscovery&#39;, False)

    @client_group_filter.setter
    def client_group_filter(self, filters):
        &#34;&#34;&#34;&#34;&#34;Sets the specified server group filters&#34;&#34;&#34;
        request_json = {}
        request_json[&#39;clientGroupDetail&#39;] = self._properties
        filters_root = request_json[&#39;clientGroupDetail&#39;][&#39;globalFiltersInfo&#39;][&#39;globalFiltersInfoList&#39;]

        for var in filters_root:
            if var[&#39;operatingSystemType&#39;] == 1:
                var[&#39;globalFilters&#39;] = {
                    &#39;filters&#39;: filters.get(&#39;windows_filters&#39;, var[&#39;globalFilters&#39;].get(
                        &#39;filters&#39;, []))
                }
            if var[&#39;operatingSystemType&#39;] == 2:
                var[&#39;globalFilters&#39;] = {
                    &#39;filters&#39;: filters.get(&#39;unix_filters&#39;, var[&#39;globalFilters&#39;].get(
                        &#39;filters&#39;, []))
                }
            var[&#39;globalFilters&#39;][&#39;opType&#39;] = 1
        request_json[&#39;clientGroupOperationType&#39;] = 2

        self._process_update_request(request_json)
        self.refresh()

    def enable_backup(self):
        &#34;&#34;&#34;Enable Backup for this ClientGroup.

            Raises:
                SDKException:
                    if failed to enable backup
        &#34;&#34;&#34;
        request_json = self._request_json_(&#39;Backup&#39;)

        error_code, error_message = self._process_update_request(request_json)

        if error_code == &#39;0&#39;:
            self._is_backup_enabled = True
            return
        else:
            if error_message:
                o_str = &#39;Failed to enable Backup\nError: &#34;{0}&#34;&#39;.format(error_message)
            else:
                o_str = &#39;Failed to enable Backup&#39;

            raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)

    def enable_backup_at_time(self, enable_time, **kwargs):
        &#34;&#34;&#34;Disables Backup if not already disabled, and enables at the time specified.

            Args:
                enable_time (str)  --  UTC time to enable the backup at, in 24 Hour format
                    format: YYYY-MM-DD HH:mm:ss

                **kwargs (dict)  -- dict of keyword arguments as follows

                    timezone    (str)   -- timezone to be used of the operation

                        **Note** make use of TIMEZONES dict in constants.py to pass timezone

            Raises:
                SDKException:
                    if time value entered is less than the current time

                    if time value entered is not of correct format

                    if failed to enable backup
        &#34;&#34;&#34;
        try:
            time_tuple = time.strptime(enable_time, &#34;%Y-%m-%d %H:%M:%S&#34;)
            if time.mktime(time_tuple) &lt; time.time():
                raise SDKException(&#39;ClientGroup&#39;, &#39;103&#39;)
        except ValueError:
            raise SDKException(&#39;ClientGroup&#39;, &#39;104&#39;)

        request_json = self._request_json_(&#39;Backup&#39;, False, enable_time, **kwargs)

        error_code, error_message = self._process_update_request(request_json)

        if error_code == &#39;0&#39;:
            return
        else:
            if error_message:
                o_str = &#39;Failed to enable Backup\nError: &#34;{0}&#34;&#39;.format(error_message)
            else:
                o_str = &#39;Failed to enable Backup&#39;

            raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)

    def disable_backup(self):
        &#34;&#34;&#34;Disables Backup for this ClientGroup.

            Raises:
                SDKException:
                    if failed to disable backup
        &#34;&#34;&#34;
        request_json = self._request_json_(&#39;Backup&#39;, False)

        error_code, error_message = self._process_update_request(request_json)

        if error_code == &#39;0&#39;:
            self._is_backup_enabled = False
            return
        else:
            if error_message:
                o_str = &#39;Failed to disable Backup\nError: &#34;{0}&#34;&#39;.format(error_message)
            else:
                o_str = &#39;Failed to diable Backup&#39;

            raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)

    def enable_restore(self):
        &#34;&#34;&#34;Enable Restore for this ClientGroup.

            Raises:
                SDKException:
                    if failed to enable restore
        &#34;&#34;&#34;
        request_json = self._request_json_(&#39;Restore&#39;)

        error_code, error_message = self._process_update_request(request_json)

        if error_code == &#39;0&#39;:
            self._is_restore_enabled = True
            return
        else:
            if error_message:
                o_str = &#39;Failed to enable Restore\nError: &#34;{0}&#34;&#39;.format(error_message)
            else:
                o_str = &#39;Failed to enable Restore&#39;

            raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)

    def enable_restore_at_time(self, enable_time, **kwargs):
        &#34;&#34;&#34;Disables restore if not already disabled, and enables at the time specified.

            Args:
                enable_time (str)  --  UTC time to enable the backup at, in 24 Hour format
                    format: YYYY-MM-DD HH:mm:ss

                **kwargs (dict)  -- dict of keyword arguments as follows

                    timezone    (str)   -- timezone to be used of the operation

                        **Note** make use of TIMEZONES dict in constants.py to pass timezone

            Raises:
                SDKException:
                    if time value entered is less than the current time

                    if time value entered is not of correct format

                    if failed to enable Restore
        &#34;&#34;&#34;
        try:
            time_tuple = time.strptime(enable_time, &#34;%Y-%m-%d %H:%M:%S&#34;)
            if time.mktime(time_tuple) &lt; time.time():
                raise SDKException(&#39;ClientGroup&#39;, &#39;103&#39;)
        except ValueError:
            raise SDKException(&#39;ClientGroup&#39;, &#39;104&#39;)

        request_json = self._request_json_(&#39;Restore&#39;, False, enable_time, **kwargs)

        error_code, error_message = self._process_update_request(request_json)

        if error_code == &#39;0&#39;:
            return
        else:
            if error_message:
                o_str = &#39;Failed to enable Restore\nError: &#34;{0}&#34;&#39;.format(error_message)
            else:
                o_str = &#39;Failed to enable Restore&#39;

            raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)

    def disable_restore(self):
        &#34;&#34;&#34;Disables Restore for this ClientGroup.

            Raises:
                SDKException:
                    if failed to disable restore
        &#34;&#34;&#34;
        request_json = self._request_json_(&#39;Restore&#39;, False)

        error_code, error_message = self._process_update_request(request_json)

        if error_code == &#39;0&#39;:
            self._is_restore_enabled = False
            return
        else:
            if error_message:
                o_str = &#39;Failed to disable Restore\nError: &#34;{0}&#34;&#39;.format(error_message)
            else:
                o_str = &#39;Failed to disable Restore&#39;

            raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)

    def enable_data_aging(self):
        &#34;&#34;&#34;Enable Data Aging for this ClientGroup.

            Raises:
                SDKException:
                    if failed to enable data aging
        &#34;&#34;&#34;
        request_json = self._request_json_(&#39;Data Aging&#39;)

        error_code, error_message = self._process_update_request(request_json)

        if error_code == &#39;0&#39;:
            self._is_data_aging_enabled = True
            return
        else:
            if error_message:
                o_str = &#39;Failed to enable Data Aging\nError: &#34;{0}&#34;&#39;.format(error_message)
            else:
                o_str = &#39;Failed to enable Data Aging&#39;

            raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)

    def enable_data_aging_at_time(self, enable_time, **kwargs):
        &#34;&#34;&#34;Disables Data Aging if not already disabled, and enables at the time specified.

            Args:
                enable_time (str)  --  UTC time to enable the backup at, in 24 Hour format
                    format: YYYY-MM-DD HH:mm:ss

                **kwargs (dict)  -- dict of keyword arguments as follows

                    timezone    (str)   -- timezone to be used of the operation

                        **Note** make use of TIMEZONES dict in constants.py to pass timezone

            Raises:
                SDKException:
                    if time value entered is less than the current time

                    if time value entered is not of correct format

                    if failed to enable Data Aging
        &#34;&#34;&#34;
        try:
            time_tuple = time.strptime(enable_time, &#34;%Y-%m-%d %H:%M:%S&#34;)
            if time.mktime(time_tuple) &lt; time.time():
                raise SDKException(&#39;ClientGroup&#39;, &#39;103&#39;)
        except ValueError:
            raise SDKException(&#39;ClientGroup&#39;, &#39;104&#39;)

        request_json = self._request_json_(&#39;Data Aging&#39;, False, enable_time, **kwargs)

        error_code, error_message = self._process_update_request(request_json)

        if error_code == &#39;0&#39;:
            return
        else:
            if error_message:
                o_str = &#39;Failed to enable Data Aging\nError: &#34;{0}&#34;&#39;.format(error_message)
            else:
                o_str = &#39;Failed to enable Data Aging&#39;

            raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)

    def disable_data_aging(self):
        &#34;&#34;&#34;Disables Data Aging for this ClientGroup.

            Raises:
                SDKException:
                    if failed to disable data aging
        &#34;&#34;&#34;
        request_json = self._request_json_(&#39;Data Aging&#39;, False)

        error_code, error_message = self._process_update_request(request_json)

        if error_code == &#39;0&#39;:
            self._is_data_aging_enabled = False
            return
        else:
            if error_message:
                o_str = &#39;Failed to disable Data Aging\nError: &#34;{0}&#34;&#39;.format(error_message)
            else:
                o_str = &#39;Failed to disable Data Aging&#39;

            raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)

    @clientgroup_name.setter
    def clientgroup_name(self, value):
        &#34;&#34;&#34;Sets the name of the clientgroup as the value provided as input.&#34;&#34;&#34;
        if isinstance(value, str):
            output = self._update(
                clientgroup_name=value,
                clientgroup_description=self.description,
                associated_clients=self._associated_clients
            )

            if output[0]:
                return
            else:
                o_str = &#39;Failed to update the ClientGroup name\nError: &#34;{0}&#34;&#39;
                raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str.format(output[2]))
        else:
            raise SDKException(
                &#39;ClientGroup&#39;, &#39;102&#39;, &#39;Clientgroup name should be a string value&#39;
            )

    @description.setter
    def description(self, value):
        &#34;&#34;&#34;Sets the description of the clientgroup as the value provided in input.&#34;&#34;&#34;
        if isinstance(value, str):
            output = self._update(
                clientgroup_name=self.name,
                clientgroup_description=value,
                associated_clients=self._associated_clients
            )

            if output[0]:
                return
            else:
                o_str = &#39;Failed to update the ClientGroup description\nError: &#34;{0}&#34;&#39;
                raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str.format(output[2]))
        else:
            raise SDKException(
                &#39;ClientGroup&#39;, &#39;102&#39;, &#39;Clientgroup description should be a string value&#39;
            )

    def add_clients(self, clients, overwrite=False):
        &#34;&#34;&#34;Adds clients to the ClientGroup.

        Args:
                clients     (str/list)  --  &#39;,&#39; separated string of client names,
                                                or a list of clients,
                                                to be added under client group

                overwrite   (bool)      --  if set to true will remove old clients,
                                                and add new clients
                    default: False

            Raises:
                SDKException:
                    if clients is not of type string / list

                    if no valid clients are found

                    if failed to add clients to client group
        &#34;&#34;&#34;
        if overwrite is True:
            return self._add_or_remove_clients(clients, &#39;OVERWRITE&#39;)
        else:
            return self._add_or_remove_clients(clients, &#39;ADD&#39;)

    def remove_clients(self, clients):
        &#34;&#34;&#34;Deletes clients from the ClientGroup.

            Args:
                clients     (str/list)  --  &#39;,&#39; separated string of client names,
                                                or a list of clients,
                                                to be removed from the client group

            Raises:
                SDKException:
                    if clients is not of type string / list

                    if no valid clients are found

                    if failed to remove clients from client group
        &#34;&#34;&#34;
        return self._add_or_remove_clients(clients, &#39;DELETE&#39;)

    def remove_all_clients(self):
        &#34;&#34;&#34;Clears the associated clients from client group

            Raises:
                SDKException:
                    if failed to remove all clients from client group
        &#34;&#34;&#34;
        output = self._update(
            clientgroup_name=self.name,
            clientgroup_description=self.description,
            associated_clients=self._associated_clients,
            operation_type=&#34;CLEAR&#34;
        )

        if output[0]:
            return
        else:
            o_str = &#39;Failed to remove clients from the ClientGroup\nError: &#34;{0}&#34;&#39;
            raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str.format(output[2]))

    def push_network_config(self):
        &#34;&#34;&#34;Performs a push network configuration on the client group

                Raises:
                SDKException:
                    if input data is invalid

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;

        xml_execute_command = &#34;&#34;&#34;
                        &lt;App_PushFirewallConfigurationRequest&gt;
                        &lt;entity clientGroupName=&#34;{0}&#34;/&gt;
                        &lt;/App_PushFirewallConfigurationRequest&gt;
            &#34;&#34;&#34;.format(self.clientgroup_name)

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._commcell_object._services[&#39;EXECUTE_QCOMMAND&#39;], xml_execute_command
        )

        if flag:
            if response.json():
                error_code = -1
                error_message = &#34;&#34;
                if &#39;entityResponse&#39; in response.json():
                    error_code = response.json()[&#39;entityResponse&#39;][0][&#39;errorCode&#39;]

                    if &#39;errorMessage&#39; in response.json():
                        error_message = response.json()[&#39;errorMessage&#39;]

                elif &#39;errorMessage&#39; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]

                    if &#39;errorCode&#39; in response.json():
                        error_code = response.json()[&#39;errorCode&#39;]

                if error_code != 0:
                    raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, error_message)

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def push_servicepack_and_hotfix(
            self,
            reboot_client=False,
            run_db_maintenance=True):
        &#34;&#34;&#34;triggers installation of service pack and hotfixes

        Args:
            reboot_client (bool)            -- boolean to specify whether to reboot the client
            or not

                default: False

            run_db_maintenance (bool)      -- boolean to specify whether to run db
            maintenance not

                default: True

        Returns:
            object - instance of the Job class for this download job

        Raises:
                SDKException:
                    if Download job failed

                    if response is empty

                    if response is not success

                    if another download job is already running

        **NOTE:** push_serivcepack_and_hotfixes cannot be used for revision upgrades

        &#34;&#34;&#34;
        install = Install(self._commcell_object)
        return install.push_servicepack_and_hotfix(
            client_computer_groups=[self.name],
            reboot_client=reboot_client,
            run_db_maintenance=run_db_maintenance)

    def repair_software(
            self,
            username=None,
            password=None,
            reboot_client=False):
        &#34;&#34;&#34;triggers Repair software on the client group

        Args:
             username    (str)               -- username of the machine to re-install features on

                default : None

            password    (str)               -- base64 encoded password

                default : None

            reboot_client (bool)            -- boolean to specify whether to reboot the
                                                client_group or not

                default: False

        Returns:
            object - instance of the Job class for this download job

        Raises:
                SDKException:
                if install job failed

                if response is empty

                if response is not success

        &#34;&#34;&#34;
        install = Install(self._commcell_object)
        return install.repair_software(
            client_group=self.name,
            username=username,
            password=password,
            reboot_client=reboot_client
        )

    def update_properties(self, properties_dict):
        &#34;&#34;&#34;Updates the client group properties

            Args:
                properties_dict (dict)  --  client group property dict which is to be updated

            Returns:
                None

            Raises:
                SDKException:
                    if failed to add

                    if response is empty

                    if response code is not as expected

        **Note** self.properties can be used to get a deep copy of all the properties, modify the properties which you
        need to change and use the update_properties method to set the properties

        &#34;&#34;&#34;
        request_json = {
            &#34;clientGroupOperationType&#34;: 2,
            &#34;clientGroupDetail&#34;: {
                &#34;clientGroup&#34;: {
                    &#34;clientGroupName&#34;: self.name
                }
            }
        }

        request_json[&#39;clientGroupDetail&#39;].update(properties_dict)
        error_code, error_message = self._process_update_request(request_json)

        if error_code != &#39;0&#39;:
            raise SDKException(
                &#39;ClientGroup&#39;, &#39;102&#39;, &#39;Failed to update client group property\nError: &#34;{0}&#34;&#39;.format(error_message)
            )

    def add_additional_setting(
            self,
            category=None,
            key_name=None,
            data_type=None,
            value=None,
            comment=None,
            enabled=1):
        &#34;&#34;&#34;Adds registry key to the client group property

            Args:
                category        (str)           -- Category of registry key

                key_name        (str)           -- Name of the registry key

                data_type       (str)           -- Data type of registry key

                    Accepted Values: BOOLEAN, INTEGER, STRING, MULTISTRING, ENCRYPTED

                value           (str)           -- Value of registry key

                comment         (str)           -- Comment to be added for the additional setting

                enabled         (int)           -- To enable the additional setting
                                                    default: 1

            Raises:
                SDKException:
                    if failed to add

                    if response is empty

                    if response code is not as expected&#34;&#34;&#34;

        properties_dict = {
            &#34;registryKeys&#34;: [{&#34;deleted&#34;: 0,
                              &#34;hidden&#34;: False,
                              &#34;relativepath&#34;: category,
                              &#34;keyName&#34;: key_name,
                              &#34;isInheritedFromClientGroup&#34;: False,
                              &#34;comment&#34;: comment,
                              &#34;type&#34;: data_type,
                              &#34;value&#34;: value,
                              &#34;enabled&#34;: enabled}]
        }

        self.update_properties(properties_dict)

    def delete_additional_setting(
            self,
            category=None,
            key_name=None):
        &#34;&#34;&#34;Delete registry key from the client group property

            Args:
                category        (str)           -- Category of registry key

                key_name        (str)           -- Name of the registry key

            Raises:
                SDKException:
                    if failed to add

                    if response is empty

                    if response code is not as expected&#34;&#34;&#34;

        properties_dict = {
            &#34;registryKeys&#34;: [{&#34;deleted&#34;: 1,
                              &#34;relativepath&#34;: category,
                              &#34;keyName&#34;: key_name}]
        }

        self.update_properties(properties_dict)

    def enable_auto_discover(self):
        &#34;&#34;&#34;Enables autodiscover at ClientGroup level..

            Raises:
                SDKException:
                    if failed to enable_auto_discover
        &#34;&#34;&#34;
        request_json = {
            &#34;clientGroupOperationType&#34;: 2,
            &#39;clientGroupDetail&#39;: {
                    &#39;enableAutoDiscovery&#39;: True,
                    &#34;clientGroup&#34;: {
                            &#34;clientGroupName&#34;: self.clientgroup_name
            }
        }
        }
        error_code, error_message = self._process_update_request(request_json)
        if error_code != &#39;0&#39;:
            if error_message:
                o_str = &#39;Failed to enable autodiscover \nError: &#34;{0}&#34;&#39;.format(error_message)
            else:
                o_str = &#39;Failed to enable autodiscover&#39;

            raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)

    def disable_auto_discover(self):
        &#34;&#34;&#34;Disables autodiscover at ClientGroup level..

            Raises:
                SDKException:
                    if failed to disable_auto_discover
        &#34;&#34;&#34;
        request_json = {
            &#34;clientGroupOperationType&#34;: 2,
            &#39;clientGroupDetail&#39;: {
                    &#39;enableAutoDiscovery&#39;: False,
                    &#34;clientGroup&#34;: {
                            &#34;clientGroupName&#34;: self.clientgroup_name
            }
        }
        }
        error_code, error_message = self._process_update_request(request_json)
        if error_code != &#39;0&#39;:
            if error_message:
                o_str = &#39;Failed to Disable autodiscover \nError: &#34;{0}&#34;&#39;.format(error_message)
            else:
                o_str = &#39;Failed to Disable autodiscover&#39;

            raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the ClientGroup.&#34;&#34;&#34;
        self._initialize_clientgroup_properties()
        self._networkprop = Network(self)
        self._network_throttle = None

    def refresh_clients(self):
        &#34;&#34;&#34;Refreshes the clients of a client group&#34;&#34;&#34;
        refresh_client_api = self._services[&#39;SERVERGROUPS_V4&#39;] + f&#34;/{self._clientgroup_id}/Refresh&#34;

        flag, response = self._cvpysdk_object.make_request(&#34;PUT&#34;, refresh_client_api)

        if not flag:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        response_json = response.json()
        if not response_json:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        error_code = response_json.get(&#34;errorCode&#34;)
        error_message = response_json.get(&#34;errorMessage&#34;)

        if error_code:
            raise SDKException(&#34;ClientGroup&#34;, &#39;102&#39;, error_message)

        self.refresh()

    def change_company(self, target_company_name):
        &#34;&#34;&#34;
        Changes Company for client group and its belonging clients

        Args:
            target_company_name (str)  --  Company name to which clientgroup and its clients to be migrated

        Raises:
            SDKException:
                if response is empty

                if response is not success
        &#34;&#34;&#34;
        if target_company_name.lower() == &#39;commcell&#39;:
            company_id = 0
        else:
            company_id = int(self._commcell_object.organizations.get(target_company_name).organization_id)
    
        request_json = {
            &#34;entities&#34;: [
                {
                    &#34;name&#34;: self._clientgroup_name,
                    &#34;clientGroupId&#34;: int(self._clientgroup_id),
                    &#34;_type_&#34;: 28
                }
            ]
        }
        
        req_url = self._services[&#39;ORGANIZATION_ASSOCIATION&#39;] % company_id
        flag, response = self._cvpysdk_object.make_request(&#39;PUT&#39;, req_url, request_json)

        if flag:
            if response.json():
                if &#39;errorCode&#39; in response.json() and response.json()[&#39;errorCode&#39;] != 0:
                    raise SDKException(&#39;Organization&#39;, &#39;110&#39;, &#39;Error: {0}&#39;.format(response.json()[&#39;errorMessage&#39;]))
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        self.refresh()</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.clientgroup.ClientGroup"><code class="flex name class">
<span>class <span class="ident">ClientGroup</span></span>
<span>(</span><span>commcell_object, clientgroup_name, clientgroup_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for performing operations for a specific ClientGroup.</p>
<p>Initialise the ClientGroup class instance.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the Commcell class</p>
<p>clientgroup_name
(str)
&ndash;
name of the clientgroup</p>
<p>clientgroup_id
(str)
&ndash;
id of the clientgroup
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the ClientGroup class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L1122-L2329" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class ClientGroup(object):
    &#34;&#34;&#34;Class for performing operations for a specific ClientGroup.&#34;&#34;&#34;

    def __init__(self, commcell_object, clientgroup_name, clientgroup_id=None):
        &#34;&#34;&#34;Initialise the ClientGroup class instance.

            Args:
                commcell_object     (object)   --  instance of the Commcell class

                clientgroup_name    (str)      --  name of the clientgroup

                clientgroup_id      (str)      --  id of the clientgroup
                    default: None

            Returns:
                object - instance of the ClientGroup class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object

        self._clientgroup_name = clientgroup_name.lower()

        if clientgroup_id:
            # Use the client group id provided in the arguments
            self._clientgroup_id = str(clientgroup_id)
        else:
            # Get the id associated with this client group
            self._clientgroup_id = self._get_clientgroup_id()

        self._CLIENTGROUP = self._commcell_object._services[&#39;CLIENTGROUP&#39;] % (self.clientgroup_id)

        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_

        self._properties = None
        self._description = None
        self._is_backup_enabled = None
        self._is_restore_enabled = None
        self._is_data_aging_enabled = None
        self._is_smart_client_group = None
        self._company_name = None

        self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.

            Returns:
                str - string containing the details of this ClientGroup
        &#34;&#34;&#34;
        representation_string = &#39;ClientGroup class instance for ClientGroup: &#34;{0}&#34;&#39;
        return representation_string.format(self.clientgroup_name)

    def _get_clientgroup_id(self):
        &#34;&#34;&#34;Gets the clientgroup id associated with this clientgroup.

            Returns:
                str - id associated with this clientgroup
        &#34;&#34;&#34;
        clientgroups = ClientGroups(self._commcell_object)
        return clientgroups.get(self.clientgroup_name).clientgroup_id

    def _get_clientgroup_properties(self):
        &#34;&#34;&#34;Gets the clientgroup properties of this clientgroup.

            Returns:
                dict - dictionary consisting of the properties of this clientgroup

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._CLIENTGROUP
        )

        if flag:
            if response.json() and &#39;clientGroupDetail&#39; in response.json():
                return response.json()[&#39;clientGroupDetail&#39;]
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _initialize_clientgroup_properties(self):
        &#34;&#34;&#34;Initializes the common properties for the clientgroup.&#34;&#34;&#34;
        clientgroup_props = self._get_clientgroup_properties()
        self._properties = clientgroup_props

        if &#39;clientGroupName&#39; in clientgroup_props[&#39;clientGroup&#39;]:
            self._clientgroup_name = clientgroup_props[&#39;clientGroup&#39;][&#39;clientGroupName&#39;].lower()
        else:
            raise SDKException(
                &#39;ClientGroup&#39;, &#39;102&#39;, &#39;Client Group name is not specified in the response&#39;
            )

        self._description = None

        if &#39;description&#39; in clientgroup_props:
            self._description = clientgroup_props[&#39;description&#39;]

        self._associated_clients = []

        if &#39;associatedClients&#39; in clientgroup_props:
            for client in clientgroup_props[&#39;associatedClients&#39;]:
                self._associated_clients.append(client[&#39;clientName&#39;])

        self._is_smart_client_group = self._properties[&#39;isSmartClientGroup&#39;]
        self._is_backup_enabled = False
        self._is_restore_enabled = False
        self._is_data_aging_enabled = False

        if &#39;clientGroupActivityControl&#39; in clientgroup_props:
            cg_activity_control = clientgroup_props[&#39;clientGroupActivityControl&#39;]

            if &#39;activityControlOptions&#39; in cg_activity_control:
                for control_options in cg_activity_control[&#39;activityControlOptions&#39;]:
                    if control_options[&#39;activityType&#39;] == 1:
                        self._is_backup_enabled = control_options[&#39;enableActivityType&#39;]
                    elif control_options[&#39;activityType&#39;] == 2:
                        self._is_restore_enabled = control_options[&#39;enableActivityType&#39;]
                    elif control_options[&#39;activityType&#39;] == 16:
                        self._is_data_aging_enabled = control_options[&#39;enableActivityType&#39;]

        self._company_name = clientgroup_props.get(&#39;securityAssociations&#39;, {}).get(&#39;tagWithCompany&#39;, {}).get(&#39;providerDomainName&#39;)

    def _request_json_(self, option, enable=True, enable_time=None, **kwargs):
        &#34;&#34;&#34;Returns the JSON request to pass to the API as per the options selected by the user.

            Args:
                option (str)  --  string option for which to run the API for
                    e.g.; Backup / Restore / Data Aging

                **kwargs (dict)  -- dict of keyword arguments as follows

                    timezone    (str)   -- timezone to be used of the operation

                        **Note** make use of TIMEZONES dict in constants.py to pass timezone

                        **Note** In case of linux CommServer provide time in GMT timezone

            Returns:
                dict - JSON request to pass to the API
        &#34;&#34;&#34;
        options_dict = {
            &#34;Backup&#34;: 1,
            &#34;Restore&#34;: 2,
            &#34;Data Aging&#34;: 16
        }

        request_json1 = {
            &#34;clientGroupOperationType&#34;: 2,
            &#34;clientGroupDetail&#34;: {
                &#34;clientGroupActivityControl&#34;: {
                    &#34;activityControlOptions&#34;: [{
                        &#34;activityType&#34;: options_dict[option],
                        &#34;enableAfterADelay&#34;: False,
                        &#34;enableActivityType&#34;: enable
                    }]
                },
                &#34;clientGroup&#34;: {
                    &#34;newName&#34;: self.name
                }
            }
        }

        request_json2 = {
            &#34;clientGroupOperationType&#34;: 2,
            &#34;clientGroupDetail&#34;: {
                &#34;clientGroupActivityControl&#34;: {
                    &#34;activityControlOptions&#34;: [{
                        &#34;activityType&#34;: options_dict[option],
                        &#34;enableAfterADelay&#34;: True,
                        &#34;enableActivityType&#34;: False,
                        &#34;dateTime&#34;: {
                            &#34;TimeZoneName&#34;: kwargs.get(&#34;timezone&#34;, self._commcell_object.default_timezone),
                            &#34;timeValue&#34;: enable_time
                        }
                    }]
                },
                &#34;clientGroup&#34;: {
                    &#34;newName&#34;: self.name
                }
            }
        }

        if enable_time:
            return request_json2
        else:
            return request_json1

    def _process_update_request(self, request_json):
        &#34;&#34;&#34;Runs the Clientgroup update API

            Args:
                request_json    (dict)  -- request json sent as payload

            Returns:
                (str, str):
                    str  -  error code received in the response

                    str  -  error message received in the response

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._CLIENTGROUP, request_json
        )

        if flag:
            if response.json():
                error_code = str(response.json()[&#39;errorCode&#39;])

                if &#39;errorMessage&#39; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]
                else:
                    error_message = &#34;&#34;

                self.refresh()
                return error_code, error_message
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        response_string = self._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _update(
            self,
            clientgroup_name,
            clientgroup_description,
            associated_clients=None,
            operation_type=&#34;NONE&#34;):
        &#34;&#34;&#34;Update the clientgroup properties of this clientgroup.

            Args:
                clientgroup_name        (str)       --  new name of the clientgroup

                clientgroup_description (str)       --  description for the clientgroup

                associated_clients      (str/list)  --  &#39;,&#39; separated string of client names,
                                                            or a list of clients,
                                                            to be added/removed under client group
                    default: None

                operation_type          (str)       --  associated clients operation type
                        Valid values: NONE, OVERWRITE, ADD, DELETE, CLEAR
                    default: NONE

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        clients_list = []

        associated_clients_op_types = {
            &#34;NONE&#34;: 0,
            &#34;OVERWRITE&#34;: 1,
            &#34;ADD&#34;: 2,
            &#34;DELETE&#34;: 3,
            &#34;CLEAR&#34;: 4
        }

        for client in associated_clients:
            clients_list.append({&#39;clientName&#39;: client})

        request_json = {
            &#34;clientGroupOperationType&#34;: 2,
            &#34;clientGroupDetail&#34;: {
                &#34;description&#34;: clientgroup_description,
                &#34;clientGroup&#34;: {
                    &#34;newName&#34;: clientgroup_name
                },
                &#34;associatedClientsOperationType&#34;: associated_clients_op_types[operation_type],
                &#34;associatedClients&#34;: clients_list
            }
        }

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._CLIENTGROUP, request_json
        )

        self.refresh()

        if flag:
            if response.json():

                error_message = response.json()[&#39;errorMessage&#39;]
                error_code = str(response.json()[&#39;errorCode&#39;])

                if error_code == &#39;0&#39;:
                    return (True, &#34;0&#34;, &#34;&#34;)
                else:
                    return (False, error_code, error_message)

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _add_or_remove_clients(self, clients, operation_type):
        &#34;&#34;&#34;Adds/Removes clients to/from the ClientGroup.

        Args:
                clients         (str/list)   --  &#39;,&#39; separated string of client names,
                                                     or a list of clients,
                                                     to be added under client group

                operation_type  (bool)       --  type of operation to run for the request
                    ADD / OVERWRITE / DELETE

            Raises:
                SDKException:
                    if clients is not of type string / list

                    if no valid clients are found

                    if failed to add clients to ClientGroup

                                    OR

                    if failed to remove clients from the ClientGroup
        &#34;&#34;&#34;
        if isinstance(clients, (str, list)):
            clientgroups_object = ClientGroups(self._commcell_object)

            if isinstance(clients, list):
                validated_clients_list = clientgroups_object._valid_clients(clients)
            elif isinstance(clients, str):
                validated_clients_list = clientgroups_object._valid_clients(clients.split(&#39;,&#39;))

            if operation_type in [&#39;ADD&#39;, &#39;OVERWRITE&#39;]:
                for client in validated_clients_list:
                    if client in self._associated_clients:
                        validated_clients_list.remove(client)

            if not validated_clients_list:
                raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, &#39;No valid clients were found&#39;)

            output = self._update(
                clientgroup_name=self.name,
                clientgroup_description=self.description,
                associated_clients=validated_clients_list,
                operation_type=operation_type
            )

            exception_message_dict = {
                &#39;ADD&#39;: &#39;Failed to add clients to the ClientGroup\nError: &#34;{0}&#34;&#39;,
                &#39;OVERWRITE&#39;: &#39;Failed to add clients to the ClientGroup\nError: &#34;{0}&#34;&#39;,
                &#39;DELETE&#39;: &#39;Failed to remove clients from the ClientGroup\nError: &#34;{0}&#34;&#39;
            }

            if output[0]:
                return
            else:
                o_str = exception_message_dict[operation_type]
                raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str.format(output[2]))
        else:
            raise SDKException(
                &#39;ClientGroup&#39;, &#39;102&#39;, &#39;Client\&#39;s name should be a list or string value&#39;
            )

    @property
    def properties(self):
        &#34;&#34;&#34;Returns the client group properties&#34;&#34;&#34;
        return copy.deepcopy(self._properties)

    @property
    def name(self):
        &#34;&#34;&#34;Returns the client group display name&#34;&#34;&#34;
        return self._properties[&#39;clientGroup&#39;][&#39;clientGroupName&#39;]

    @property
    def clientgroup_id(self):
        &#34;&#34;&#34;Treats the clientgroup id as a read-only attribute.&#34;&#34;&#34;
        return self._clientgroup_id

    @property
    def clientgroup_name(self):
        &#34;&#34;&#34;Treats the clientgroup name as a read-only attribute.&#34;&#34;&#34;
        return self._clientgroup_name

    @property
    def description(self):
        &#34;&#34;&#34;Treats the clientgroup description as a read-only attribute.&#34;&#34;&#34;
        return self._description

    @property
    def associated_clients(self):
        &#34;&#34;&#34;Treats the clients associated to the ClientGroup as a read-only attribute.&#34;&#34;&#34;
        return self._associated_clients

    @property
    def is_backup_enabled(self):
        &#34;&#34;&#34;Treats the clientgroup backup attribute as a property of the ClientGroup class.&#34;&#34;&#34;
        return self._is_backup_enabled

    @property
    def is_restore_enabled(self):
        &#34;&#34;&#34;Treats the clientgroup restore attribute as a propetry of the ClientGroup class.&#34;&#34;&#34;
        return self._is_restore_enabled

    @property
    def is_data_aging_enabled(self):
        &#34;&#34;&#34;Treats the clientgroup data aging attribute as a property of the ClientGroup class.&#34;&#34;&#34;
        return self._is_data_aging_enabled

    @property
    def is_smart_client_group(self):
        &#34;&#34;&#34;Returns boolean indicating whether client group is smart client group&#34;&#34;&#34;
        return self._is_smart_client_group
    
    @property
    def company_name(self):
        &#34;&#34;&#34;Returns company name to which client group belongs to&#34;&#34;&#34;
        return self._company_name
    
    @property
    def network(self):
        &#34;&#34;&#34;Returns the object of Network class.&#34;&#34;&#34;
        if self._networkprop is None:
            self._networkprop = Network(self)

        return self._networkprop

    @property
    def network_throttle(self):
        &#34;&#34;&#34;Returns the object of NetworkThrottle class&#34;&#34;&#34;
        if self._network_throttle is None:
            self._network_throttle = NetworkThrottle(self)

        return self._network_throttle

    @property
    def client_group_filter(self):
        &#34;&#34;&#34;Returns the client group filters&#34;&#34;&#34;
        client_group_filters = {}

        os_type_map = {
            1: &#39;windows_filters&#39;,
            2: &#39;unix_filters&#39;
        }

        for filters_root in self._properties[&#39;globalFiltersInfo&#39;][&#39;globalFiltersInfoList&#39;]:
            client_group_filters[os_type_map[filters_root[&#39;operatingSystemType&#39;]]] = filters_root.get(
                &#39;globalFilters&#39;, {}).get(&#39;filters&#39;, [])

        return client_group_filters

    @property
    def is_auto_discover_enabled(self):
        &#34;&#34;&#34;Returns boolen for clientgroup autodiscover attribute whether property is enabled or not.&#34;&#34;&#34;
        return self._properties.get(&#39;enableAutoDiscovery&#39;, False)

    @client_group_filter.setter
    def client_group_filter(self, filters):
        &#34;&#34;&#34;&#34;&#34;Sets the specified server group filters&#34;&#34;&#34;
        request_json = {}
        request_json[&#39;clientGroupDetail&#39;] = self._properties
        filters_root = request_json[&#39;clientGroupDetail&#39;][&#39;globalFiltersInfo&#39;][&#39;globalFiltersInfoList&#39;]

        for var in filters_root:
            if var[&#39;operatingSystemType&#39;] == 1:
                var[&#39;globalFilters&#39;] = {
                    &#39;filters&#39;: filters.get(&#39;windows_filters&#39;, var[&#39;globalFilters&#39;].get(
                        &#39;filters&#39;, []))
                }
            if var[&#39;operatingSystemType&#39;] == 2:
                var[&#39;globalFilters&#39;] = {
                    &#39;filters&#39;: filters.get(&#39;unix_filters&#39;, var[&#39;globalFilters&#39;].get(
                        &#39;filters&#39;, []))
                }
            var[&#39;globalFilters&#39;][&#39;opType&#39;] = 1
        request_json[&#39;clientGroupOperationType&#39;] = 2

        self._process_update_request(request_json)
        self.refresh()

    def enable_backup(self):
        &#34;&#34;&#34;Enable Backup for this ClientGroup.

            Raises:
                SDKException:
                    if failed to enable backup
        &#34;&#34;&#34;
        request_json = self._request_json_(&#39;Backup&#39;)

        error_code, error_message = self._process_update_request(request_json)

        if error_code == &#39;0&#39;:
            self._is_backup_enabled = True
            return
        else:
            if error_message:
                o_str = &#39;Failed to enable Backup\nError: &#34;{0}&#34;&#39;.format(error_message)
            else:
                o_str = &#39;Failed to enable Backup&#39;

            raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)

    def enable_backup_at_time(self, enable_time, **kwargs):
        &#34;&#34;&#34;Disables Backup if not already disabled, and enables at the time specified.

            Args:
                enable_time (str)  --  UTC time to enable the backup at, in 24 Hour format
                    format: YYYY-MM-DD HH:mm:ss

                **kwargs (dict)  -- dict of keyword arguments as follows

                    timezone    (str)   -- timezone to be used of the operation

                        **Note** make use of TIMEZONES dict in constants.py to pass timezone

            Raises:
                SDKException:
                    if time value entered is less than the current time

                    if time value entered is not of correct format

                    if failed to enable backup
        &#34;&#34;&#34;
        try:
            time_tuple = time.strptime(enable_time, &#34;%Y-%m-%d %H:%M:%S&#34;)
            if time.mktime(time_tuple) &lt; time.time():
                raise SDKException(&#39;ClientGroup&#39;, &#39;103&#39;)
        except ValueError:
            raise SDKException(&#39;ClientGroup&#39;, &#39;104&#39;)

        request_json = self._request_json_(&#39;Backup&#39;, False, enable_time, **kwargs)

        error_code, error_message = self._process_update_request(request_json)

        if error_code == &#39;0&#39;:
            return
        else:
            if error_message:
                o_str = &#39;Failed to enable Backup\nError: &#34;{0}&#34;&#39;.format(error_message)
            else:
                o_str = &#39;Failed to enable Backup&#39;

            raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)

    def disable_backup(self):
        &#34;&#34;&#34;Disables Backup for this ClientGroup.

            Raises:
                SDKException:
                    if failed to disable backup
        &#34;&#34;&#34;
        request_json = self._request_json_(&#39;Backup&#39;, False)

        error_code, error_message = self._process_update_request(request_json)

        if error_code == &#39;0&#39;:
            self._is_backup_enabled = False
            return
        else:
            if error_message:
                o_str = &#39;Failed to disable Backup\nError: &#34;{0}&#34;&#39;.format(error_message)
            else:
                o_str = &#39;Failed to diable Backup&#39;

            raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)

    def enable_restore(self):
        &#34;&#34;&#34;Enable Restore for this ClientGroup.

            Raises:
                SDKException:
                    if failed to enable restore
        &#34;&#34;&#34;
        request_json = self._request_json_(&#39;Restore&#39;)

        error_code, error_message = self._process_update_request(request_json)

        if error_code == &#39;0&#39;:
            self._is_restore_enabled = True
            return
        else:
            if error_message:
                o_str = &#39;Failed to enable Restore\nError: &#34;{0}&#34;&#39;.format(error_message)
            else:
                o_str = &#39;Failed to enable Restore&#39;

            raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)

    def enable_restore_at_time(self, enable_time, **kwargs):
        &#34;&#34;&#34;Disables restore if not already disabled, and enables at the time specified.

            Args:
                enable_time (str)  --  UTC time to enable the backup at, in 24 Hour format
                    format: YYYY-MM-DD HH:mm:ss

                **kwargs (dict)  -- dict of keyword arguments as follows

                    timezone    (str)   -- timezone to be used of the operation

                        **Note** make use of TIMEZONES dict in constants.py to pass timezone

            Raises:
                SDKException:
                    if time value entered is less than the current time

                    if time value entered is not of correct format

                    if failed to enable Restore
        &#34;&#34;&#34;
        try:
            time_tuple = time.strptime(enable_time, &#34;%Y-%m-%d %H:%M:%S&#34;)
            if time.mktime(time_tuple) &lt; time.time():
                raise SDKException(&#39;ClientGroup&#39;, &#39;103&#39;)
        except ValueError:
            raise SDKException(&#39;ClientGroup&#39;, &#39;104&#39;)

        request_json = self._request_json_(&#39;Restore&#39;, False, enable_time, **kwargs)

        error_code, error_message = self._process_update_request(request_json)

        if error_code == &#39;0&#39;:
            return
        else:
            if error_message:
                o_str = &#39;Failed to enable Restore\nError: &#34;{0}&#34;&#39;.format(error_message)
            else:
                o_str = &#39;Failed to enable Restore&#39;

            raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)

    def disable_restore(self):
        &#34;&#34;&#34;Disables Restore for this ClientGroup.

            Raises:
                SDKException:
                    if failed to disable restore
        &#34;&#34;&#34;
        request_json = self._request_json_(&#39;Restore&#39;, False)

        error_code, error_message = self._process_update_request(request_json)

        if error_code == &#39;0&#39;:
            self._is_restore_enabled = False
            return
        else:
            if error_message:
                o_str = &#39;Failed to disable Restore\nError: &#34;{0}&#34;&#39;.format(error_message)
            else:
                o_str = &#39;Failed to disable Restore&#39;

            raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)

    def enable_data_aging(self):
        &#34;&#34;&#34;Enable Data Aging for this ClientGroup.

            Raises:
                SDKException:
                    if failed to enable data aging
        &#34;&#34;&#34;
        request_json = self._request_json_(&#39;Data Aging&#39;)

        error_code, error_message = self._process_update_request(request_json)

        if error_code == &#39;0&#39;:
            self._is_data_aging_enabled = True
            return
        else:
            if error_message:
                o_str = &#39;Failed to enable Data Aging\nError: &#34;{0}&#34;&#39;.format(error_message)
            else:
                o_str = &#39;Failed to enable Data Aging&#39;

            raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)

    def enable_data_aging_at_time(self, enable_time, **kwargs):
        &#34;&#34;&#34;Disables Data Aging if not already disabled, and enables at the time specified.

            Args:
                enable_time (str)  --  UTC time to enable the backup at, in 24 Hour format
                    format: YYYY-MM-DD HH:mm:ss

                **kwargs (dict)  -- dict of keyword arguments as follows

                    timezone    (str)   -- timezone to be used of the operation

                        **Note** make use of TIMEZONES dict in constants.py to pass timezone

            Raises:
                SDKException:
                    if time value entered is less than the current time

                    if time value entered is not of correct format

                    if failed to enable Data Aging
        &#34;&#34;&#34;
        try:
            time_tuple = time.strptime(enable_time, &#34;%Y-%m-%d %H:%M:%S&#34;)
            if time.mktime(time_tuple) &lt; time.time():
                raise SDKException(&#39;ClientGroup&#39;, &#39;103&#39;)
        except ValueError:
            raise SDKException(&#39;ClientGroup&#39;, &#39;104&#39;)

        request_json = self._request_json_(&#39;Data Aging&#39;, False, enable_time, **kwargs)

        error_code, error_message = self._process_update_request(request_json)

        if error_code == &#39;0&#39;:
            return
        else:
            if error_message:
                o_str = &#39;Failed to enable Data Aging\nError: &#34;{0}&#34;&#39;.format(error_message)
            else:
                o_str = &#39;Failed to enable Data Aging&#39;

            raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)

    def disable_data_aging(self):
        &#34;&#34;&#34;Disables Data Aging for this ClientGroup.

            Raises:
                SDKException:
                    if failed to disable data aging
        &#34;&#34;&#34;
        request_json = self._request_json_(&#39;Data Aging&#39;, False)

        error_code, error_message = self._process_update_request(request_json)

        if error_code == &#39;0&#39;:
            self._is_data_aging_enabled = False
            return
        else:
            if error_message:
                o_str = &#39;Failed to disable Data Aging\nError: &#34;{0}&#34;&#39;.format(error_message)
            else:
                o_str = &#39;Failed to disable Data Aging&#39;

            raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)

    @clientgroup_name.setter
    def clientgroup_name(self, value):
        &#34;&#34;&#34;Sets the name of the clientgroup as the value provided as input.&#34;&#34;&#34;
        if isinstance(value, str):
            output = self._update(
                clientgroup_name=value,
                clientgroup_description=self.description,
                associated_clients=self._associated_clients
            )

            if output[0]:
                return
            else:
                o_str = &#39;Failed to update the ClientGroup name\nError: &#34;{0}&#34;&#39;
                raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str.format(output[2]))
        else:
            raise SDKException(
                &#39;ClientGroup&#39;, &#39;102&#39;, &#39;Clientgroup name should be a string value&#39;
            )

    @description.setter
    def description(self, value):
        &#34;&#34;&#34;Sets the description of the clientgroup as the value provided in input.&#34;&#34;&#34;
        if isinstance(value, str):
            output = self._update(
                clientgroup_name=self.name,
                clientgroup_description=value,
                associated_clients=self._associated_clients
            )

            if output[0]:
                return
            else:
                o_str = &#39;Failed to update the ClientGroup description\nError: &#34;{0}&#34;&#39;
                raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str.format(output[2]))
        else:
            raise SDKException(
                &#39;ClientGroup&#39;, &#39;102&#39;, &#39;Clientgroup description should be a string value&#39;
            )

    def add_clients(self, clients, overwrite=False):
        &#34;&#34;&#34;Adds clients to the ClientGroup.

        Args:
                clients     (str/list)  --  &#39;,&#39; separated string of client names,
                                                or a list of clients,
                                                to be added under client group

                overwrite   (bool)      --  if set to true will remove old clients,
                                                and add new clients
                    default: False

            Raises:
                SDKException:
                    if clients is not of type string / list

                    if no valid clients are found

                    if failed to add clients to client group
        &#34;&#34;&#34;
        if overwrite is True:
            return self._add_or_remove_clients(clients, &#39;OVERWRITE&#39;)
        else:
            return self._add_or_remove_clients(clients, &#39;ADD&#39;)

    def remove_clients(self, clients):
        &#34;&#34;&#34;Deletes clients from the ClientGroup.

            Args:
                clients     (str/list)  --  &#39;,&#39; separated string of client names,
                                                or a list of clients,
                                                to be removed from the client group

            Raises:
                SDKException:
                    if clients is not of type string / list

                    if no valid clients are found

                    if failed to remove clients from client group
        &#34;&#34;&#34;
        return self._add_or_remove_clients(clients, &#39;DELETE&#39;)

    def remove_all_clients(self):
        &#34;&#34;&#34;Clears the associated clients from client group

            Raises:
                SDKException:
                    if failed to remove all clients from client group
        &#34;&#34;&#34;
        output = self._update(
            clientgroup_name=self.name,
            clientgroup_description=self.description,
            associated_clients=self._associated_clients,
            operation_type=&#34;CLEAR&#34;
        )

        if output[0]:
            return
        else:
            o_str = &#39;Failed to remove clients from the ClientGroup\nError: &#34;{0}&#34;&#39;
            raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str.format(output[2]))

    def push_network_config(self):
        &#34;&#34;&#34;Performs a push network configuration on the client group

                Raises:
                SDKException:
                    if input data is invalid

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;

        xml_execute_command = &#34;&#34;&#34;
                        &lt;App_PushFirewallConfigurationRequest&gt;
                        &lt;entity clientGroupName=&#34;{0}&#34;/&gt;
                        &lt;/App_PushFirewallConfigurationRequest&gt;
            &#34;&#34;&#34;.format(self.clientgroup_name)

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._commcell_object._services[&#39;EXECUTE_QCOMMAND&#39;], xml_execute_command
        )

        if flag:
            if response.json():
                error_code = -1
                error_message = &#34;&#34;
                if &#39;entityResponse&#39; in response.json():
                    error_code = response.json()[&#39;entityResponse&#39;][0][&#39;errorCode&#39;]

                    if &#39;errorMessage&#39; in response.json():
                        error_message = response.json()[&#39;errorMessage&#39;]

                elif &#39;errorMessage&#39; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]

                    if &#39;errorCode&#39; in response.json():
                        error_code = response.json()[&#39;errorCode&#39;]

                if error_code != 0:
                    raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, error_message)

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def push_servicepack_and_hotfix(
            self,
            reboot_client=False,
            run_db_maintenance=True):
        &#34;&#34;&#34;triggers installation of service pack and hotfixes

        Args:
            reboot_client (bool)            -- boolean to specify whether to reboot the client
            or not

                default: False

            run_db_maintenance (bool)      -- boolean to specify whether to run db
            maintenance not

                default: True

        Returns:
            object - instance of the Job class for this download job

        Raises:
                SDKException:
                    if Download job failed

                    if response is empty

                    if response is not success

                    if another download job is already running

        **NOTE:** push_serivcepack_and_hotfixes cannot be used for revision upgrades

        &#34;&#34;&#34;
        install = Install(self._commcell_object)
        return install.push_servicepack_and_hotfix(
            client_computer_groups=[self.name],
            reboot_client=reboot_client,
            run_db_maintenance=run_db_maintenance)

    def repair_software(
            self,
            username=None,
            password=None,
            reboot_client=False):
        &#34;&#34;&#34;triggers Repair software on the client group

        Args:
             username    (str)               -- username of the machine to re-install features on

                default : None

            password    (str)               -- base64 encoded password

                default : None

            reboot_client (bool)            -- boolean to specify whether to reboot the
                                                client_group or not

                default: False

        Returns:
            object - instance of the Job class for this download job

        Raises:
                SDKException:
                if install job failed

                if response is empty

                if response is not success

        &#34;&#34;&#34;
        install = Install(self._commcell_object)
        return install.repair_software(
            client_group=self.name,
            username=username,
            password=password,
            reboot_client=reboot_client
        )

    def update_properties(self, properties_dict):
        &#34;&#34;&#34;Updates the client group properties

            Args:
                properties_dict (dict)  --  client group property dict which is to be updated

            Returns:
                None

            Raises:
                SDKException:
                    if failed to add

                    if response is empty

                    if response code is not as expected

        **Note** self.properties can be used to get a deep copy of all the properties, modify the properties which you
        need to change and use the update_properties method to set the properties

        &#34;&#34;&#34;
        request_json = {
            &#34;clientGroupOperationType&#34;: 2,
            &#34;clientGroupDetail&#34;: {
                &#34;clientGroup&#34;: {
                    &#34;clientGroupName&#34;: self.name
                }
            }
        }

        request_json[&#39;clientGroupDetail&#39;].update(properties_dict)
        error_code, error_message = self._process_update_request(request_json)

        if error_code != &#39;0&#39;:
            raise SDKException(
                &#39;ClientGroup&#39;, &#39;102&#39;, &#39;Failed to update client group property\nError: &#34;{0}&#34;&#39;.format(error_message)
            )

    def add_additional_setting(
            self,
            category=None,
            key_name=None,
            data_type=None,
            value=None,
            comment=None,
            enabled=1):
        &#34;&#34;&#34;Adds registry key to the client group property

            Args:
                category        (str)           -- Category of registry key

                key_name        (str)           -- Name of the registry key

                data_type       (str)           -- Data type of registry key

                    Accepted Values: BOOLEAN, INTEGER, STRING, MULTISTRING, ENCRYPTED

                value           (str)           -- Value of registry key

                comment         (str)           -- Comment to be added for the additional setting

                enabled         (int)           -- To enable the additional setting
                                                    default: 1

            Raises:
                SDKException:
                    if failed to add

                    if response is empty

                    if response code is not as expected&#34;&#34;&#34;

        properties_dict = {
            &#34;registryKeys&#34;: [{&#34;deleted&#34;: 0,
                              &#34;hidden&#34;: False,
                              &#34;relativepath&#34;: category,
                              &#34;keyName&#34;: key_name,
                              &#34;isInheritedFromClientGroup&#34;: False,
                              &#34;comment&#34;: comment,
                              &#34;type&#34;: data_type,
                              &#34;value&#34;: value,
                              &#34;enabled&#34;: enabled}]
        }

        self.update_properties(properties_dict)

    def delete_additional_setting(
            self,
            category=None,
            key_name=None):
        &#34;&#34;&#34;Delete registry key from the client group property

            Args:
                category        (str)           -- Category of registry key

                key_name        (str)           -- Name of the registry key

            Raises:
                SDKException:
                    if failed to add

                    if response is empty

                    if response code is not as expected&#34;&#34;&#34;

        properties_dict = {
            &#34;registryKeys&#34;: [{&#34;deleted&#34;: 1,
                              &#34;relativepath&#34;: category,
                              &#34;keyName&#34;: key_name}]
        }

        self.update_properties(properties_dict)

    def enable_auto_discover(self):
        &#34;&#34;&#34;Enables autodiscover at ClientGroup level..

            Raises:
                SDKException:
                    if failed to enable_auto_discover
        &#34;&#34;&#34;
        request_json = {
            &#34;clientGroupOperationType&#34;: 2,
            &#39;clientGroupDetail&#39;: {
                    &#39;enableAutoDiscovery&#39;: True,
                    &#34;clientGroup&#34;: {
                            &#34;clientGroupName&#34;: self.clientgroup_name
            }
        }
        }
        error_code, error_message = self._process_update_request(request_json)
        if error_code != &#39;0&#39;:
            if error_message:
                o_str = &#39;Failed to enable autodiscover \nError: &#34;{0}&#34;&#39;.format(error_message)
            else:
                o_str = &#39;Failed to enable autodiscover&#39;

            raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)

    def disable_auto_discover(self):
        &#34;&#34;&#34;Disables autodiscover at ClientGroup level..

            Raises:
                SDKException:
                    if failed to disable_auto_discover
        &#34;&#34;&#34;
        request_json = {
            &#34;clientGroupOperationType&#34;: 2,
            &#39;clientGroupDetail&#39;: {
                    &#39;enableAutoDiscovery&#39;: False,
                    &#34;clientGroup&#34;: {
                            &#34;clientGroupName&#34;: self.clientgroup_name
            }
        }
        }
        error_code, error_message = self._process_update_request(request_json)
        if error_code != &#39;0&#39;:
            if error_message:
                o_str = &#39;Failed to Disable autodiscover \nError: &#34;{0}&#34;&#39;.format(error_message)
            else:
                o_str = &#39;Failed to Disable autodiscover&#39;

            raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the ClientGroup.&#34;&#34;&#34;
        self._initialize_clientgroup_properties()
        self._networkprop = Network(self)
        self._network_throttle = None

    def refresh_clients(self):
        &#34;&#34;&#34;Refreshes the clients of a client group&#34;&#34;&#34;
        refresh_client_api = self._services[&#39;SERVERGROUPS_V4&#39;] + f&#34;/{self._clientgroup_id}/Refresh&#34;

        flag, response = self._cvpysdk_object.make_request(&#34;PUT&#34;, refresh_client_api)

        if not flag:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        response_json = response.json()
        if not response_json:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        error_code = response_json.get(&#34;errorCode&#34;)
        error_message = response_json.get(&#34;errorMessage&#34;)

        if error_code:
            raise SDKException(&#34;ClientGroup&#34;, &#39;102&#39;, error_message)

        self.refresh()

    def change_company(self, target_company_name):
        &#34;&#34;&#34;
        Changes Company for client group and its belonging clients

        Args:
            target_company_name (str)  --  Company name to which clientgroup and its clients to be migrated

        Raises:
            SDKException:
                if response is empty

                if response is not success
        &#34;&#34;&#34;
        if target_company_name.lower() == &#39;commcell&#39;:
            company_id = 0
        else:
            company_id = int(self._commcell_object.organizations.get(target_company_name).organization_id)
    
        request_json = {
            &#34;entities&#34;: [
                {
                    &#34;name&#34;: self._clientgroup_name,
                    &#34;clientGroupId&#34;: int(self._clientgroup_id),
                    &#34;_type_&#34;: 28
                }
            ]
        }
        
        req_url = self._services[&#39;ORGANIZATION_ASSOCIATION&#39;] % company_id
        flag, response = self._cvpysdk_object.make_request(&#39;PUT&#39;, req_url, request_json)

        if flag:
            if response.json():
                if &#39;errorCode&#39; in response.json() and response.json()[&#39;errorCode&#39;] != 0:
                    raise SDKException(&#39;Organization&#39;, &#39;110&#39;, &#39;Error: {0}&#39;.format(response.json()[&#39;errorMessage&#39;]))
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        self.refresh()</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.clientgroup.ClientGroup.associated_clients"><code class="name">var <span class="ident">associated_clients</span></code></dt>
<dd>
<div class="desc"><p>Treats the clients associated to the ClientGroup as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L1518-L1521" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def associated_clients(self):
    &#34;&#34;&#34;Treats the clients associated to the ClientGroup as a read-only attribute.&#34;&#34;&#34;
    return self._associated_clients</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroup.client_group_filter"><code class="name">var <span class="ident">client_group_filter</span></code></dt>
<dd>
<div class="desc"><p>Returns the client group filters</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L1564-L1578" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def client_group_filter(self):
    &#34;&#34;&#34;Returns the client group filters&#34;&#34;&#34;
    client_group_filters = {}

    os_type_map = {
        1: &#39;windows_filters&#39;,
        2: &#39;unix_filters&#39;
    }

    for filters_root in self._properties[&#39;globalFiltersInfo&#39;][&#39;globalFiltersInfoList&#39;]:
        client_group_filters[os_type_map[filters_root[&#39;operatingSystemType&#39;]]] = filters_root.get(
            &#39;globalFilters&#39;, {}).get(&#39;filters&#39;, [])

    return client_group_filters</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroup.clientgroup_id"><code class="name">var <span class="ident">clientgroup_id</span></code></dt>
<dd>
<div class="desc"><p>Treats the clientgroup id as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L1503-L1506" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def clientgroup_id(self):
    &#34;&#34;&#34;Treats the clientgroup id as a read-only attribute.&#34;&#34;&#34;
    return self._clientgroup_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroup.clientgroup_name"><code class="name">var <span class="ident">clientgroup_name</span></code></dt>
<dd>
<div class="desc"><p>Treats the clientgroup name as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L1508-L1511" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def clientgroup_name(self):
    &#34;&#34;&#34;Treats the clientgroup name as a read-only attribute.&#34;&#34;&#34;
    return self._clientgroup_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroup.company_name"><code class="name">var <span class="ident">company_name</span></code></dt>
<dd>
<div class="desc"><p>Returns company name to which client group belongs to</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L1543-L1546" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def company_name(self):
    &#34;&#34;&#34;Returns company name to which client group belongs to&#34;&#34;&#34;
    return self._company_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroup.description"><code class="name">var <span class="ident">description</span></code></dt>
<dd>
<div class="desc"><p>Treats the clientgroup description as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L1513-L1516" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def description(self):
    &#34;&#34;&#34;Treats the clientgroup description as a read-only attribute.&#34;&#34;&#34;
    return self._description</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroup.is_auto_discover_enabled"><code class="name">var <span class="ident">is_auto_discover_enabled</span></code></dt>
<dd>
<div class="desc"><p>Returns boolen for clientgroup autodiscover attribute whether property is enabled or not.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L1580-L1583" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_auto_discover_enabled(self):
    &#34;&#34;&#34;Returns boolen for clientgroup autodiscover attribute whether property is enabled or not.&#34;&#34;&#34;
    return self._properties.get(&#39;enableAutoDiscovery&#39;, False)</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroup.is_backup_enabled"><code class="name">var <span class="ident">is_backup_enabled</span></code></dt>
<dd>
<div class="desc"><p>Treats the clientgroup backup attribute as a property of the ClientGroup class.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L1523-L1526" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_backup_enabled(self):
    &#34;&#34;&#34;Treats the clientgroup backup attribute as a property of the ClientGroup class.&#34;&#34;&#34;
    return self._is_backup_enabled</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroup.is_data_aging_enabled"><code class="name">var <span class="ident">is_data_aging_enabled</span></code></dt>
<dd>
<div class="desc"><p>Treats the clientgroup data aging attribute as a property of the ClientGroup class.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L1533-L1536" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_data_aging_enabled(self):
    &#34;&#34;&#34;Treats the clientgroup data aging attribute as a property of the ClientGroup class.&#34;&#34;&#34;
    return self._is_data_aging_enabled</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroup.is_restore_enabled"><code class="name">var <span class="ident">is_restore_enabled</span></code></dt>
<dd>
<div class="desc"><p>Treats the clientgroup restore attribute as a propetry of the ClientGroup class.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L1528-L1531" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_restore_enabled(self):
    &#34;&#34;&#34;Treats the clientgroup restore attribute as a propetry of the ClientGroup class.&#34;&#34;&#34;
    return self._is_restore_enabled</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroup.is_smart_client_group"><code class="name">var <span class="ident">is_smart_client_group</span></code></dt>
<dd>
<div class="desc"><p>Returns boolean indicating whether client group is smart client group</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L1538-L1541" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_smart_client_group(self):
    &#34;&#34;&#34;Returns boolean indicating whether client group is smart client group&#34;&#34;&#34;
    return self._is_smart_client_group</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroup.name"><code class="name">var <span class="ident">name</span></code></dt>
<dd>
<div class="desc"><p>Returns the client group display name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L1498-L1501" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def name(self):
    &#34;&#34;&#34;Returns the client group display name&#34;&#34;&#34;
    return self._properties[&#39;clientGroup&#39;][&#39;clientGroupName&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroup.network"><code class="name">var <span class="ident">network</span></code></dt>
<dd>
<div class="desc"><p>Returns the object of Network class.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L1548-L1554" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def network(self):
    &#34;&#34;&#34;Returns the object of Network class.&#34;&#34;&#34;
    if self._networkprop is None:
        self._networkprop = Network(self)

    return self._networkprop</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroup.network_throttle"><code class="name">var <span class="ident">network_throttle</span></code></dt>
<dd>
<div class="desc"><p>Returns the object of NetworkThrottle class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L1556-L1562" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def network_throttle(self):
    &#34;&#34;&#34;Returns the object of NetworkThrottle class&#34;&#34;&#34;
    if self._network_throttle is None:
        self._network_throttle = NetworkThrottle(self)

    return self._network_throttle</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroup.properties"><code class="name">var <span class="ident">properties</span></code></dt>
<dd>
<div class="desc"><p>Returns the client group properties</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L1493-L1496" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def properties(self):
    &#34;&#34;&#34;Returns the client group properties&#34;&#34;&#34;
    return copy.deepcopy(self._properties)</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.clientgroup.ClientGroup.add_additional_setting"><code class="name flex">
<span>def <span class="ident">add_additional_setting</span></span>(<span>self, category=None, key_name=None, data_type=None, value=None, comment=None, enabled=1)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds registry key to the client group property</p>
<h2 id="args">Args</h2>
<p>category
(str)
&ndash; Category of registry key</p>
<p>key_name
(str)
&ndash; Name of the registry key</p>
<p>data_type
(str)
&ndash; Data type of registry key</p>
<pre><code>Accepted Values: BOOLEAN, INTEGER, STRING, MULTISTRING, ENCRYPTED
</code></pre>
<p>value
(str)
&ndash; Value of registry key</p>
<p>comment
(str)
&ndash; Comment to be added for the additional setting</p>
<p>enabled
(int)
&ndash; To enable the additional setting
default: 1</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to add</p>
<pre><code>if response is empty

if response code is not as expected
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L2136-L2182" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_additional_setting(
        self,
        category=None,
        key_name=None,
        data_type=None,
        value=None,
        comment=None,
        enabled=1):
    &#34;&#34;&#34;Adds registry key to the client group property

        Args:
            category        (str)           -- Category of registry key

            key_name        (str)           -- Name of the registry key

            data_type       (str)           -- Data type of registry key

                Accepted Values: BOOLEAN, INTEGER, STRING, MULTISTRING, ENCRYPTED

            value           (str)           -- Value of registry key

            comment         (str)           -- Comment to be added for the additional setting

            enabled         (int)           -- To enable the additional setting
                                                default: 1

        Raises:
            SDKException:
                if failed to add

                if response is empty

                if response code is not as expected&#34;&#34;&#34;

    properties_dict = {
        &#34;registryKeys&#34;: [{&#34;deleted&#34;: 0,
                          &#34;hidden&#34;: False,
                          &#34;relativepath&#34;: category,
                          &#34;keyName&#34;: key_name,
                          &#34;isInheritedFromClientGroup&#34;: False,
                          &#34;comment&#34;: comment,
                          &#34;type&#34;: data_type,
                          &#34;value&#34;: value,
                          &#34;enabled&#34;: enabled}]
    }

    self.update_properties(properties_dict)</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroup.add_clients"><code class="name flex">
<span>def <span class="ident">add_clients</span></span>(<span>self, clients, overwrite=False)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds clients to the ClientGroup.</p>
<h2 id="args">Args</h2>
<pre><code>clients     (str/list)  --  ',' separated string of client names,
                                or a list of clients,
                                to be added under client group

overwrite   (bool)      --  if set to true will remove old clients,
                                and add new clients
    default: False
</code></pre>
<p>Raises:
SDKException:
if clients is not of type string / list</p>
<pre><code>    if no valid clients are found

    if failed to add clients to client group
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L1907-L1930" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_clients(self, clients, overwrite=False):
    &#34;&#34;&#34;Adds clients to the ClientGroup.

    Args:
            clients     (str/list)  --  &#39;,&#39; separated string of client names,
                                            or a list of clients,
                                            to be added under client group

            overwrite   (bool)      --  if set to true will remove old clients,
                                            and add new clients
                default: False

        Raises:
            SDKException:
                if clients is not of type string / list

                if no valid clients are found

                if failed to add clients to client group
    &#34;&#34;&#34;
    if overwrite is True:
        return self._add_or_remove_clients(clients, &#39;OVERWRITE&#39;)
    else:
        return self._add_or_remove_clients(clients, &#39;ADD&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroup.change_company"><code class="name flex">
<span>def <span class="ident">change_company</span></span>(<span>self, target_company_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Changes Company for client group and its belonging clients</p>
<h2 id="args">Args</h2>
<p>target_company_name (str)
&ndash;
Company name to which clientgroup and its clients to be migrated</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if response is empty</p>
<pre><code>if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L2289-L2329" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def change_company(self, target_company_name):
    &#34;&#34;&#34;
    Changes Company for client group and its belonging clients

    Args:
        target_company_name (str)  --  Company name to which clientgroup and its clients to be migrated

    Raises:
        SDKException:
            if response is empty

            if response is not success
    &#34;&#34;&#34;
    if target_company_name.lower() == &#39;commcell&#39;:
        company_id = 0
    else:
        company_id = int(self._commcell_object.organizations.get(target_company_name).organization_id)

    request_json = {
        &#34;entities&#34;: [
            {
                &#34;name&#34;: self._clientgroup_name,
                &#34;clientGroupId&#34;: int(self._clientgroup_id),
                &#34;_type_&#34;: 28
            }
        ]
    }
    
    req_url = self._services[&#39;ORGANIZATION_ASSOCIATION&#39;] % company_id
    flag, response = self._cvpysdk_object.make_request(&#39;PUT&#39;, req_url, request_json)

    if flag:
        if response.json():
            if &#39;errorCode&#39; in response.json() and response.json()[&#39;errorCode&#39;] != 0:
                raise SDKException(&#39;Organization&#39;, &#39;110&#39;, &#39;Error: {0}&#39;.format(response.json()[&#39;errorMessage&#39;]))
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
    self.refresh()</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroup.delete_additional_setting"><code class="name flex">
<span>def <span class="ident">delete_additional_setting</span></span>(<span>self, category=None, key_name=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Delete registry key from the client group property</p>
<h2 id="args">Args</h2>
<p>category
(str)
&ndash; Category of registry key</p>
<p>key_name
(str)
&ndash; Name of the registry key</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to add</p>
<pre><code>if response is empty

if response code is not as expected
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L2184-L2209" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete_additional_setting(
        self,
        category=None,
        key_name=None):
    &#34;&#34;&#34;Delete registry key from the client group property

        Args:
            category        (str)           -- Category of registry key

            key_name        (str)           -- Name of the registry key

        Raises:
            SDKException:
                if failed to add

                if response is empty

                if response code is not as expected&#34;&#34;&#34;

    properties_dict = {
        &#34;registryKeys&#34;: [{&#34;deleted&#34;: 1,
                          &#34;relativepath&#34;: category,
                          &#34;keyName&#34;: key_name}]
    }

    self.update_properties(properties_dict)</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroup.disable_auto_discover"><code class="name flex">
<span>def <span class="ident">disable_auto_discover</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Disables autodiscover at ClientGroup level..</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to disable_auto_discover</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L2236-L2259" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def disable_auto_discover(self):
    &#34;&#34;&#34;Disables autodiscover at ClientGroup level..

        Raises:
            SDKException:
                if failed to disable_auto_discover
    &#34;&#34;&#34;
    request_json = {
        &#34;clientGroupOperationType&#34;: 2,
        &#39;clientGroupDetail&#39;: {
                &#39;enableAutoDiscovery&#39;: False,
                &#34;clientGroup&#34;: {
                        &#34;clientGroupName&#34;: self.clientgroup_name
        }
    }
    }
    error_code, error_message = self._process_update_request(request_json)
    if error_code != &#39;0&#39;:
        if error_message:
            o_str = &#39;Failed to Disable autodiscover \nError: &#34;{0}&#34;&#39;.format(error_message)
        else:
            o_str = &#39;Failed to Disable autodiscover&#39;

        raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroup.disable_backup"><code class="name flex">
<span>def <span class="ident">disable_backup</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Disables Backup for this ClientGroup.</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to disable backup</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L1673-L1693" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def disable_backup(self):
    &#34;&#34;&#34;Disables Backup for this ClientGroup.

        Raises:
            SDKException:
                if failed to disable backup
    &#34;&#34;&#34;
    request_json = self._request_json_(&#39;Backup&#39;, False)

    error_code, error_message = self._process_update_request(request_json)

    if error_code == &#39;0&#39;:
        self._is_backup_enabled = False
        return
    else:
        if error_message:
            o_str = &#39;Failed to disable Backup\nError: &#34;{0}&#34;&#39;.format(error_message)
        else:
            o_str = &#39;Failed to diable Backup&#39;

        raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroup.disable_data_aging"><code class="name flex">
<span>def <span class="ident">disable_data_aging</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Disables Data Aging for this ClientGroup.</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to disable data aging</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L1845-L1865" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def disable_data_aging(self):
    &#34;&#34;&#34;Disables Data Aging for this ClientGroup.

        Raises:
            SDKException:
                if failed to disable data aging
    &#34;&#34;&#34;
    request_json = self._request_json_(&#39;Data Aging&#39;, False)

    error_code, error_message = self._process_update_request(request_json)

    if error_code == &#39;0&#39;:
        self._is_data_aging_enabled = False
        return
    else:
        if error_message:
            o_str = &#39;Failed to disable Data Aging\nError: &#34;{0}&#34;&#39;.format(error_message)
        else:
            o_str = &#39;Failed to disable Data Aging&#39;

        raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroup.disable_restore"><code class="name flex">
<span>def <span class="ident">disable_restore</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Disables Restore for this ClientGroup.</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to disable restore</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L1759-L1779" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def disable_restore(self):
    &#34;&#34;&#34;Disables Restore for this ClientGroup.

        Raises:
            SDKException:
                if failed to disable restore
    &#34;&#34;&#34;
    request_json = self._request_json_(&#39;Restore&#39;, False)

    error_code, error_message = self._process_update_request(request_json)

    if error_code == &#39;0&#39;:
        self._is_restore_enabled = False
        return
    else:
        if error_message:
            o_str = &#39;Failed to disable Restore\nError: &#34;{0}&#34;&#39;.format(error_message)
        else:
            o_str = &#39;Failed to disable Restore&#39;

        raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroup.enable_auto_discover"><code class="name flex">
<span>def <span class="ident">enable_auto_discover</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Enables autodiscover at ClientGroup level..</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to enable_auto_discover</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L2211-L2234" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable_auto_discover(self):
    &#34;&#34;&#34;Enables autodiscover at ClientGroup level..

        Raises:
            SDKException:
                if failed to enable_auto_discover
    &#34;&#34;&#34;
    request_json = {
        &#34;clientGroupOperationType&#34;: 2,
        &#39;clientGroupDetail&#39;: {
                &#39;enableAutoDiscovery&#39;: True,
                &#34;clientGroup&#34;: {
                        &#34;clientGroupName&#34;: self.clientgroup_name
        }
    }
    }
    error_code, error_message = self._process_update_request(request_json)
    if error_code != &#39;0&#39;:
        if error_message:
            o_str = &#39;Failed to enable autodiscover \nError: &#34;{0}&#34;&#39;.format(error_message)
        else:
            o_str = &#39;Failed to enable autodiscover&#39;

        raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroup.enable_backup"><code class="name flex">
<span>def <span class="ident">enable_backup</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Enable Backup for this ClientGroup.</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to enable backup</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L1609-L1629" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable_backup(self):
    &#34;&#34;&#34;Enable Backup for this ClientGroup.

        Raises:
            SDKException:
                if failed to enable backup
    &#34;&#34;&#34;
    request_json = self._request_json_(&#39;Backup&#39;)

    error_code, error_message = self._process_update_request(request_json)

    if error_code == &#39;0&#39;:
        self._is_backup_enabled = True
        return
    else:
        if error_message:
            o_str = &#39;Failed to enable Backup\nError: &#34;{0}&#34;&#39;.format(error_message)
        else:
            o_str = &#39;Failed to enable Backup&#39;

        raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroup.enable_backup_at_time"><code class="name flex">
<span>def <span class="ident">enable_backup_at_time</span></span>(<span>self, enable_time, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Disables Backup if not already disabled, and enables at the time specified.</p>
<h2 id="args">Args</h2>
<p>enable_time (str)
&ndash;
UTC time to enable the backup at, in 24 Hour format
format: YYYY-MM-DD HH:mm:ss</p>
<p>**kwargs (dict)
&ndash; dict of keyword arguments as follows</p>
<pre><code>timezone    (str)   -- timezone to be used of the operation

    **Note** make use of TIMEZONES dict in constants.py to pass timezone
</code></pre>
<h2 id="raises">Raises</h2>
<p>SDKException:
if time value entered is less than the current time</p>
<pre><code>if time value entered is not of correct format

if failed to enable backup
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L1631-L1671" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable_backup_at_time(self, enable_time, **kwargs):
    &#34;&#34;&#34;Disables Backup if not already disabled, and enables at the time specified.

        Args:
            enable_time (str)  --  UTC time to enable the backup at, in 24 Hour format
                format: YYYY-MM-DD HH:mm:ss

            **kwargs (dict)  -- dict of keyword arguments as follows

                timezone    (str)   -- timezone to be used of the operation

                    **Note** make use of TIMEZONES dict in constants.py to pass timezone

        Raises:
            SDKException:
                if time value entered is less than the current time

                if time value entered is not of correct format

                if failed to enable backup
    &#34;&#34;&#34;
    try:
        time_tuple = time.strptime(enable_time, &#34;%Y-%m-%d %H:%M:%S&#34;)
        if time.mktime(time_tuple) &lt; time.time():
            raise SDKException(&#39;ClientGroup&#39;, &#39;103&#39;)
    except ValueError:
        raise SDKException(&#39;ClientGroup&#39;, &#39;104&#39;)

    request_json = self._request_json_(&#39;Backup&#39;, False, enable_time, **kwargs)

    error_code, error_message = self._process_update_request(request_json)

    if error_code == &#39;0&#39;:
        return
    else:
        if error_message:
            o_str = &#39;Failed to enable Backup\nError: &#34;{0}&#34;&#39;.format(error_message)
        else:
            o_str = &#39;Failed to enable Backup&#39;

        raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroup.enable_data_aging"><code class="name flex">
<span>def <span class="ident">enable_data_aging</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Enable Data Aging for this ClientGroup.</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to enable data aging</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L1781-L1801" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable_data_aging(self):
    &#34;&#34;&#34;Enable Data Aging for this ClientGroup.

        Raises:
            SDKException:
                if failed to enable data aging
    &#34;&#34;&#34;
    request_json = self._request_json_(&#39;Data Aging&#39;)

    error_code, error_message = self._process_update_request(request_json)

    if error_code == &#39;0&#39;:
        self._is_data_aging_enabled = True
        return
    else:
        if error_message:
            o_str = &#39;Failed to enable Data Aging\nError: &#34;{0}&#34;&#39;.format(error_message)
        else:
            o_str = &#39;Failed to enable Data Aging&#39;

        raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroup.enable_data_aging_at_time"><code class="name flex">
<span>def <span class="ident">enable_data_aging_at_time</span></span>(<span>self, enable_time, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Disables Data Aging if not already disabled, and enables at the time specified.</p>
<h2 id="args">Args</h2>
<p>enable_time (str)
&ndash;
UTC time to enable the backup at, in 24 Hour format
format: YYYY-MM-DD HH:mm:ss</p>
<p>**kwargs (dict)
&ndash; dict of keyword arguments as follows</p>
<pre><code>timezone    (str)   -- timezone to be used of the operation

    **Note** make use of TIMEZONES dict in constants.py to pass timezone
</code></pre>
<h2 id="raises">Raises</h2>
<p>SDKException:
if time value entered is less than the current time</p>
<pre><code>if time value entered is not of correct format

if failed to enable Data Aging
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L1803-L1843" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable_data_aging_at_time(self, enable_time, **kwargs):
    &#34;&#34;&#34;Disables Data Aging if not already disabled, and enables at the time specified.

        Args:
            enable_time (str)  --  UTC time to enable the backup at, in 24 Hour format
                format: YYYY-MM-DD HH:mm:ss

            **kwargs (dict)  -- dict of keyword arguments as follows

                timezone    (str)   -- timezone to be used of the operation

                    **Note** make use of TIMEZONES dict in constants.py to pass timezone

        Raises:
            SDKException:
                if time value entered is less than the current time

                if time value entered is not of correct format

                if failed to enable Data Aging
    &#34;&#34;&#34;
    try:
        time_tuple = time.strptime(enable_time, &#34;%Y-%m-%d %H:%M:%S&#34;)
        if time.mktime(time_tuple) &lt; time.time():
            raise SDKException(&#39;ClientGroup&#39;, &#39;103&#39;)
    except ValueError:
        raise SDKException(&#39;ClientGroup&#39;, &#39;104&#39;)

    request_json = self._request_json_(&#39;Data Aging&#39;, False, enable_time, **kwargs)

    error_code, error_message = self._process_update_request(request_json)

    if error_code == &#39;0&#39;:
        return
    else:
        if error_message:
            o_str = &#39;Failed to enable Data Aging\nError: &#34;{0}&#34;&#39;.format(error_message)
        else:
            o_str = &#39;Failed to enable Data Aging&#39;

        raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroup.enable_restore"><code class="name flex">
<span>def <span class="ident">enable_restore</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Enable Restore for this ClientGroup.</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to enable restore</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L1695-L1715" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable_restore(self):
    &#34;&#34;&#34;Enable Restore for this ClientGroup.

        Raises:
            SDKException:
                if failed to enable restore
    &#34;&#34;&#34;
    request_json = self._request_json_(&#39;Restore&#39;)

    error_code, error_message = self._process_update_request(request_json)

    if error_code == &#39;0&#39;:
        self._is_restore_enabled = True
        return
    else:
        if error_message:
            o_str = &#39;Failed to enable Restore\nError: &#34;{0}&#34;&#39;.format(error_message)
        else:
            o_str = &#39;Failed to enable Restore&#39;

        raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroup.enable_restore_at_time"><code class="name flex">
<span>def <span class="ident">enable_restore_at_time</span></span>(<span>self, enable_time, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Disables restore if not already disabled, and enables at the time specified.</p>
<h2 id="args">Args</h2>
<p>enable_time (str)
&ndash;
UTC time to enable the backup at, in 24 Hour format
format: YYYY-MM-DD HH:mm:ss</p>
<p>**kwargs (dict)
&ndash; dict of keyword arguments as follows</p>
<pre><code>timezone    (str)   -- timezone to be used of the operation

    **Note** make use of TIMEZONES dict in constants.py to pass timezone
</code></pre>
<h2 id="raises">Raises</h2>
<p>SDKException:
if time value entered is less than the current time</p>
<pre><code>if time value entered is not of correct format

if failed to enable Restore
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L1717-L1757" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable_restore_at_time(self, enable_time, **kwargs):
    &#34;&#34;&#34;Disables restore if not already disabled, and enables at the time specified.

        Args:
            enable_time (str)  --  UTC time to enable the backup at, in 24 Hour format
                format: YYYY-MM-DD HH:mm:ss

            **kwargs (dict)  -- dict of keyword arguments as follows

                timezone    (str)   -- timezone to be used of the operation

                    **Note** make use of TIMEZONES dict in constants.py to pass timezone

        Raises:
            SDKException:
                if time value entered is less than the current time

                if time value entered is not of correct format

                if failed to enable Restore
    &#34;&#34;&#34;
    try:
        time_tuple = time.strptime(enable_time, &#34;%Y-%m-%d %H:%M:%S&#34;)
        if time.mktime(time_tuple) &lt; time.time():
            raise SDKException(&#39;ClientGroup&#39;, &#39;103&#39;)
    except ValueError:
        raise SDKException(&#39;ClientGroup&#39;, &#39;104&#39;)

    request_json = self._request_json_(&#39;Restore&#39;, False, enable_time, **kwargs)

    error_code, error_message = self._process_update_request(request_json)

    if error_code == &#39;0&#39;:
        return
    else:
        if error_message:
            o_str = &#39;Failed to enable Restore\nError: &#34;{0}&#34;&#39;.format(error_message)
        else:
            o_str = &#39;Failed to enable Restore&#39;

        raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroup.push_network_config"><code class="name flex">
<span>def <span class="ident">push_network_config</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Performs a push network configuration on the client group</p>
<p>Raises:</p>
<h2 id="sdkexception">Sdkexception</h2>
<p>if input data is invalid</p>
<p>if response is empty</p>
<p>if response is not success</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L1970-L2016" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def push_network_config(self):
    &#34;&#34;&#34;Performs a push network configuration on the client group

            Raises:
            SDKException:
                if input data is invalid

                if response is empty

                if response is not success
    &#34;&#34;&#34;

    xml_execute_command = &#34;&#34;&#34;
                    &lt;App_PushFirewallConfigurationRequest&gt;
                    &lt;entity clientGroupName=&#34;{0}&#34;/&gt;
                    &lt;/App_PushFirewallConfigurationRequest&gt;
        &#34;&#34;&#34;.format(self.clientgroup_name)

    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, self._commcell_object._services[&#39;EXECUTE_QCOMMAND&#39;], xml_execute_command
    )

    if flag:
        if response.json():
            error_code = -1
            error_message = &#34;&#34;
            if &#39;entityResponse&#39; in response.json():
                error_code = response.json()[&#39;entityResponse&#39;][0][&#39;errorCode&#39;]

                if &#39;errorMessage&#39; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]

            elif &#39;errorMessage&#39; in response.json():
                error_message = response.json()[&#39;errorMessage&#39;]

                if &#39;errorCode&#39; in response.json():
                    error_code = response.json()[&#39;errorCode&#39;]

            if error_code != 0:
                raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, error_message)

        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroup.push_servicepack_and_hotfix"><code class="name flex">
<span>def <span class="ident">push_servicepack_and_hotfix</span></span>(<span>self, reboot_client=False, run_db_maintenance=True)</span>
</code></dt>
<dd>
<div class="desc"><p>triggers installation of service pack and hotfixes</p>
<h2 id="args">Args</h2>
<p>reboot_client (bool)
&ndash; boolean to specify whether to reboot the client
or not</p>
<pre><code>default: False
</code></pre>
<p>run_db_maintenance (bool)
&ndash; boolean to specify whether to run db
maintenance not</p>
<pre><code>default: True
</code></pre>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this download job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if Download job failed</p>
<pre><code>if response is empty

if response is not success

if another download job is already running
</code></pre>
<p><strong>NOTE:</strong> push_serivcepack_and_hotfixes cannot be used for revision upgrades</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L2018-L2055" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def push_servicepack_and_hotfix(
        self,
        reboot_client=False,
        run_db_maintenance=True):
    &#34;&#34;&#34;triggers installation of service pack and hotfixes

    Args:
        reboot_client (bool)            -- boolean to specify whether to reboot the client
        or not

            default: False

        run_db_maintenance (bool)      -- boolean to specify whether to run db
        maintenance not

            default: True

    Returns:
        object - instance of the Job class for this download job

    Raises:
            SDKException:
                if Download job failed

                if response is empty

                if response is not success

                if another download job is already running

    **NOTE:** push_serivcepack_and_hotfixes cannot be used for revision upgrades

    &#34;&#34;&#34;
    install = Install(self._commcell_object)
    return install.push_servicepack_and_hotfix(
        client_computer_groups=[self.name],
        reboot_client=reboot_client,
        run_db_maintenance=run_db_maintenance)</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroup.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the properties of the ClientGroup.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L2261-L2265" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the properties of the ClientGroup.&#34;&#34;&#34;
    self._initialize_clientgroup_properties()
    self._networkprop = Network(self)
    self._network_throttle = None</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroup.refresh_clients"><code class="name flex">
<span>def <span class="ident">refresh_clients</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refreshes the clients of a client group</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L2267-L2287" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh_clients(self):
    &#34;&#34;&#34;Refreshes the clients of a client group&#34;&#34;&#34;
    refresh_client_api = self._services[&#39;SERVERGROUPS_V4&#39;] + f&#34;/{self._clientgroup_id}/Refresh&#34;

    flag, response = self._cvpysdk_object.make_request(&#34;PUT&#34;, refresh_client_api)

    if not flag:
        response_string = self._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    response_json = response.json()
    if not response_json:
        raise SDKException(&#39;Response&#39;, &#39;102&#39;)

    error_code = response_json.get(&#34;errorCode&#34;)
    error_message = response_json.get(&#34;errorMessage&#34;)

    if error_code:
        raise SDKException(&#34;ClientGroup&#34;, &#39;102&#39;, error_message)

    self.refresh()</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroup.remove_all_clients"><code class="name flex">
<span>def <span class="ident">remove_all_clients</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Clears the associated clients from client group</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to remove all clients from client group</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L1950-L1968" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def remove_all_clients(self):
    &#34;&#34;&#34;Clears the associated clients from client group

        Raises:
            SDKException:
                if failed to remove all clients from client group
    &#34;&#34;&#34;
    output = self._update(
        clientgroup_name=self.name,
        clientgroup_description=self.description,
        associated_clients=self._associated_clients,
        operation_type=&#34;CLEAR&#34;
    )

    if output[0]:
        return
    else:
        o_str = &#39;Failed to remove clients from the ClientGroup\nError: &#34;{0}&#34;&#39;
        raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str.format(output[2]))</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroup.remove_clients"><code class="name flex">
<span>def <span class="ident">remove_clients</span></span>(<span>self, clients)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes clients from the ClientGroup.</p>
<h2 id="args">Args</h2>
<p>clients
(str/list)
&ndash;
',' separated string of client names,
or a list of clients,
to be removed from the client group</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if clients is not of type string / list</p>
<pre><code>if no valid clients are found

if failed to remove clients from client group
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L1932-L1948" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def remove_clients(self, clients):
    &#34;&#34;&#34;Deletes clients from the ClientGroup.

        Args:
            clients     (str/list)  --  &#39;,&#39; separated string of client names,
                                            or a list of clients,
                                            to be removed from the client group

        Raises:
            SDKException:
                if clients is not of type string / list

                if no valid clients are found

                if failed to remove clients from client group
    &#34;&#34;&#34;
    return self._add_or_remove_clients(clients, &#39;DELETE&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroup.repair_software"><code class="name flex">
<span>def <span class="ident">repair_software</span></span>(<span>self, username=None, password=None, reboot_client=False)</span>
</code></dt>
<dd>
<div class="desc"><p>triggers Repair software on the client group</p>
<h2 id="args">Args</h2>
<p>username
(str)
&ndash; username of the machine to re-install features on</p>
<pre><code>default : None
</code></pre>
<p>password
(str)
&ndash; base64 encoded password</p>
<pre><code>default : None
</code></pre>
<p>reboot_client (bool)
&ndash; boolean to specify whether to reboot the
client_group or not</p>
<pre><code>default: False
</code></pre>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this download job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if install job failed</p>
<p>if response is empty</p>
<p>if response is not success</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L2057-L2096" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def repair_software(
        self,
        username=None,
        password=None,
        reboot_client=False):
    &#34;&#34;&#34;triggers Repair software on the client group

    Args:
         username    (str)               -- username of the machine to re-install features on

            default : None

        password    (str)               -- base64 encoded password

            default : None

        reboot_client (bool)            -- boolean to specify whether to reboot the
                                            client_group or not

            default: False

    Returns:
        object - instance of the Job class for this download job

    Raises:
            SDKException:
            if install job failed

            if response is empty

            if response is not success

    &#34;&#34;&#34;
    install = Install(self._commcell_object)
    return install.repair_software(
        client_group=self.name,
        username=username,
        password=password,
        reboot_client=reboot_client
    )</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroup.update_properties"><code class="name flex">
<span>def <span class="ident">update_properties</span></span>(<span>self, properties_dict)</span>
</code></dt>
<dd>
<div class="desc"><p>Updates the client group properties</p>
<pre><code>Args:
    properties_dict (dict)  --  client group property dict which is to be updated

Returns:
    None

Raises:
    SDKException:
        if failed to add

        if response is empty

        if response code is not as expected
</code></pre>
<p><strong>Note</strong> self.properties can be used to get a deep copy of all the properties, modify the properties which you
need to change and use the update_properties method to set the properties</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L2098-L2134" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def update_properties(self, properties_dict):
    &#34;&#34;&#34;Updates the client group properties

        Args:
            properties_dict (dict)  --  client group property dict which is to be updated

        Returns:
            None

        Raises:
            SDKException:
                if failed to add

                if response is empty

                if response code is not as expected

    **Note** self.properties can be used to get a deep copy of all the properties, modify the properties which you
    need to change and use the update_properties method to set the properties

    &#34;&#34;&#34;
    request_json = {
        &#34;clientGroupOperationType&#34;: 2,
        &#34;clientGroupDetail&#34;: {
            &#34;clientGroup&#34;: {
                &#34;clientGroupName&#34;: self.name
            }
        }
    }

    request_json[&#39;clientGroupDetail&#39;].update(properties_dict)
    error_code, error_message = self._process_update_request(request_json)

    if error_code != &#39;0&#39;:
        raise SDKException(
            &#39;ClientGroup&#39;, &#39;102&#39;, &#39;Failed to update client group property\nError: &#34;{0}&#34;&#39;.format(error_message)
        )</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroups"><code class="flex name class">
<span>class <span class="ident">ClientGroups</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing all the clientgroups associated with a Commcell.</p>
<p>Initialize object of the ClientGroups class.</p>
<h2 id="args">Args</h2>
<p>commcell_object (object)
&ndash;
instance of the Commcell class</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the ClientGroups class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L196-L1119" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class ClientGroups(object):
    &#34;&#34;&#34;Class for representing all the clientgroups associated with a Commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the ClientGroups class.

            Args:
                commcell_object (object)  --  instance of the Commcell class

            Returns:
                object - instance of the ClientGroups class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._CLIENTGROUPS = self._commcell_object._services[&#39;CLIENTGROUPS&#39;]

        self._clientgroups = None
        self._clientgroups_cache = None
        self._all_client_groups_prop = None
        self.filter_query_count = 0
        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all clientgroups of the Commcell.

            Returns:
                str - string of all the clientgroups for a commcell
        &#34;&#34;&#34;
        representation_string = &#34;{:^5}\t{:^50}\n\n&#34;.format(&#39;S. No.&#39;, &#39;ClientGroup&#39;)

        for index, clientgroup_name in enumerate(self._clientgroups):
            sub_str = &#39;{:^5}\t{:50}\n&#39;.format(index + 1, clientgroup_name)
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the ClientGroups class.

            Returns:
                str - string of all the client groups associated with the commcell
        &#34;&#34;&#34;
        return &#34;ClientGroups class instance for Commcell&#34;

    def __len__(self):
        &#34;&#34;&#34;Returns the number of the client groups associated to the Commcell.&#34;&#34;&#34;
        return len(self.all_clientgroups)

    def __getitem__(self, value):
        &#34;&#34;&#34;Returns the name of the client group for the given client group ID or
            the details of the client group for given client group Name.

            Args:
                value   (str / int)     --  Name or ID of the client group

            Returns:
                str     -   name of the client group, if the client group id was given

                dict    -   dict of details of the client group, if client group name was given

            Raises:
                IndexError:
                    no client group exists with the given Name / Id

        &#34;&#34;&#34;
        value = str(value)

        if value in self.all_clientgroups:
            return self.all_clientgroups[value]
        else:
            try:
                return list(
                    filter(lambda x: x[1][&#39;id&#39;] == value, self.all_clientgroups.items())
                )[0][0]
            except IndexError:
                raise IndexError(&#39;No client group exists with the given Name / Id&#39;)

    def _get_clientgroups(self, full_response: bool = False):
        &#34;&#34;&#34;Gets all the clientgroups associated with the commcell

            Args:
                full_response(bool) --  flag to return complete response

            Returns:
                dict - consists of all clientgroups of the commcell
                    {
                         &#34;clientgroup1_name&#34;: clientgroup1_id,
                         &#34;clientgroup2_name&#34;: clientgroup2_id,
                    }

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._CLIENTGROUPS
        )

        if flag:
            if response.json() and &#39;groups&#39; in response.json():
                if full_response:
                    return response.json()
                clientgroups_dict = {}

                name_count = {}

                for client_group in response.json()[&#39;groups&#39;]:
                    temp_name = client_group[&#39;name&#39;].lower()
                    temp_company = \
                        client_group.get(&#39;clientGroup&#39;, {}).get(&#39;entityInfo&#39;, {}).get(&#39;companyName&#39;, &#39;&#39;).lower()

                    if temp_name in name_count:
                        name_count[temp_name].add(temp_company)
                    else:
                        name_count[temp_name] = {temp_company}

                for client_group in response.json()[&#39;groups&#39;]:
                    temp_name = client_group[&#39;name&#39;].lower()
                    temp_id = str(client_group[&#39;Id&#39;]).lower()
                    temp_company = \
                        client_group.get(&#39;clientGroup&#39;, {}).get(&#39;entityInfo&#39;, {}).get(&#39;companyName&#39;, &#39;&#39;).lower()

                    if len(name_count[temp_name]) &gt; 1:
                        unique_key = f&#34;{temp_name}_({temp_company})&#34;
                    else:
                        unique_key = temp_name

                    clientgroups_dict[unique_key] = temp_id

                return clientgroups_dict
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _valid_clients(self, clients_list):
        &#34;&#34;&#34;Returns only the valid clients specified in the input clients list

            Args:
                clients_list (list)    --  list of the clients to add to the client group

            Returns:
                list - list consisting of the names of all valid clients in the input clients list

            Raises:
                SDKException:
                    if type of clients list argument is not list
        &#34;&#34;&#34;
        if not isinstance(clients_list, list):
            raise SDKException(&#39;ClientGroup&#39;, &#39;101&#39;)

        clients = []

        for client in clients_list:
            if isinstance(client, str):
                client = client.strip().lower()

                if self._commcell_object.clients.has_client(client):
                    clients.append(client)

        return clients

    def _get_fl_parameters(self, fl: list = None) -&gt; str:
        &#34;&#34;&#34;
        Returns the fl parameters to be passed in the mongodb caching api call

        Args:
            fl    (list)  --   list of columns to be passed in API request

        Returns:
            fl_parameters(str) -- fl parameter string
        &#34;&#34;&#34;
        self.valid_columns = {
            &#39;name&#39;: &#39;name&#39;,
            &#39;id&#39;: &#39;groups.Id&#39;,
            &#39;association&#39;: &#39;groups.groupAssocType&#39;,
            &#39;companyName&#39;: &#39;groups.clientGroup.entityInfo.companyName&#39;,
            &#39;tags&#39;: &#39;tags&#39;
        }
        default_columns = &#39;name&#39;

        if fl:
            if all(col in self.valid_columns for col in fl):
                fl_parameters = f&#34;&amp;fl={default_columns},{&#39;,&#39;.join(self.valid_columns[column] for column in fl)}&#34;
            else:
                raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, &#39;Invalid column name passed&#39;)
        else:
            fl_parameters = &#34;&amp;fl=groups.clientGroup,groups.discoverRulesInfo,groups.groupAssocType,groups.Id,&#34; \
                            &#34;groups.name,groups.isCompanySmartClientGroup&#34;

        return fl_parameters

    def _get_sort_parameters(self, sort: list = None) -&gt; str:
        &#34;&#34;&#34;
        Returns the sort parameters to be passed in the mongodb caching api call

        Args:
            sort  (list)  --   contains the name of the column on which sorting will be performed and type of sort
                                valid sor type -- 1 for ascending and -1 for descending
                                e.g. sort = [&#39;name&#39;,&#39;1&#39;]

        Returns:
            sort_parameters(str) -- sort parameter string
        &#34;&#34;&#34;
        sort_type = str(sort[1])
        col = sort[0]
        if col in self.valid_columns.keys() and sort_type in [&#39;1&#39;, &#39;-1&#39;]:
            sort_parameter = &#39;&amp;sort=&#39; + self.valid_columns[col] + &#39;:&#39; + sort_type
        else:
            raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, &#39;Invalid column name passed&#39;)
        return sort_parameter

    def _get_fq_parameters(self, fq: list = None) -&gt; str:
        &#34;&#34;&#34;
        Returns the fq parameters based on the fq list passed
        Args:
             fq     (list) --   contains the columnName, condition and value
                    e.g. fq = [[&#39;name&#39;,&#39;contains&#39;, &#39;test&#39;],[&#39;association&#39;,&#39;eq&#39;, &#39;Manual&#39;]]

        Returns:
            fq_parameters(str) -- fq parameter string
        &#34;&#34;&#34;
        conditions = {&#34;contains&#34;, &#34;notContain&#34;, &#34;eq&#34;, &#34;neq&#34;}
        params = [
            &#34;&amp;fq=groups.isCompanySmartClientGroup:eq:false&#34;,
            &#34;&amp;fq=groups.clientGroup.clientGroupName:neq:Index Servers&#34;
        ]

        for column, condition, *value in fq or []:
            if column not in self.valid_columns:
                raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, &#39;Invalid column name passed&#39;)

            # Handle &#39;tags&#39; column separately
            if column == &#34;tags&#34; and condition == &#34;contains&#34;:
                params.append(f&#34;&amp;tags={value[0]}&#34;)
            elif condition in conditions:
                params.append(f&#34;&amp;fq={self.valid_columns[column]}:{condition.lower()}:{value[0]}&#34;)
            elif condition == &#34;isEmpty&#34; and not value:
                params.append(f&#34;&amp;fq={self.valid_columns[column]}:in:null,&#34;)
            else:
                raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, &#39;Invalid condition passed&#39;)

        return &#34;&#34;.join(params)

    def get_client_groups_cache(self, hard: bool = False, **kwargs) -&gt; dict:
        &#34;&#34;&#34;
        Gets all the client groups present in CommcellEntityCache DB.

        Args:
            hard  (bool)        --   Flag to perform hard refresh on client groups cache.
            **kwargs (dict):
                - fl (list)     --   List of columns to return in response (default: None).
                - sort (list)   --   Contains the name of the column on which sorting will be performed and type of sort.
                                           Valid sort type: 1 for ascending and -1 for descending
                                           e.g. sort = [&#39;columnName&#39;, &#39;1&#39;] (default: None).
                - limit (list)  --   Contains the start and limit parameter value.
                                            Default [&#39;0&#39;, &#39;100&#39;].
                - search (str)  --   Contains the string to search in the commcell entity cache (default: None).
                - fq (list)     --   Contains the columnName, condition and value.
                                            e.g. fq = [[&#39;name&#39;, &#39;contains&#39;, &#39;test&#39;],
                                             [&#39;association&#39;, &#39;eq&#39;, &#39;Manual&#39;]] (default: None).

        Returns:
            dict: Dictionary of all the properties present in response.
        &#34;&#34;&#34;
        # computing params
        fl_parameters = self._get_fl_parameters(kwargs.get(&#39;fl&#39;, None))
        fq_parameters = self._get_fq_parameters(kwargs.get(&#39;fq&#39;, None))
        limit = kwargs.get(&#39;limit&#39;, None)
        limit_parameters = f&#39;start={limit[0]}&amp;limit={limit[1]}&#39; if limit else &#39;&#39;
        hard_refresh = &#39;&amp;hardRefresh=true&#39; if hard else &#39;&#39;
        sort_parameters = self._get_sort_parameters(kwargs.get(&#39;sort&#39;, None)) if kwargs.get(&#39;sort&#39;, None) else &#39;&#39;

        # Search operation can only be performed on limited columns, so filtering out the columns on which search works
        searchable_columns = [&#34;name&#34;,&#34;association&#34;,&#39;companyName&#39;]
        search_parameter = (f&#39;&amp;search={&#34;,&#34;.join(self.valid_columns[col] for col in searchable_columns)}:contains:&#39;
                            f&#39;{kwargs.get(&#34;search&#34;, None)}&#39;) if kwargs.get(&#39;search&#39;, None) else &#39;&#39;

        params = [
            limit_parameters,
            sort_parameters,
            fl_parameters,
            hard_refresh,
            search_parameter,
            fq_parameters
        ]
        request_url = f&#34;{self._CLIENTGROUPS}?&#34; + &#34;&#34;.join(params)
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#34;GET&#34;, request_url)
        if not flag:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        client_group_cache = {}
        if response.json() and &#39;groups&#39; in response.json():
            self.filter_query_count = response.json().get(&#39;filterQueryCount&#39;,0)
            for group in response.json()[&#39;groups&#39;]:
                name = group.get(&#39;name&#39;)
                client_group_config = {
                    &#39;name&#39;: name,
                    &#39;id&#39;: group.get(&#39;Id&#39;),
                    &#39;association&#39;: group.get(&#39;groupAssocType&#39;),
                }
                if &#39;clientGroup&#39; in group:
                    if &#39;companyName&#39; in group.get(&#39;clientGroup&#39;, {}).get(&#39;entityInfo&#39;, {}):
                        client_group_config[&#39;companyName&#39;] = group.get(&#39;clientGroup&#39;, {}).get(&#39;entityInfo&#39;, {}).get(
                            &#39;companyName&#39;)
                    if &#39;tags&#39; in group.get(&#39;clientGroup&#39;, {}):
                        client_group_config[&#39;tags&#39;] = group.get(&#39;clientGroup&#39;, {}).get(&#39;tags&#39;)
                client_group_cache[name] = client_group_config

            return client_group_cache
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

    @property
    def all_clientgroups(self):
        &#34;&#34;&#34;Returns dict of all the clientgroups associated with this commcell

            dict - consists of all clientgroups of the commcell
                    {
                         &#34;clientgroup1_name&#34;: clientgroup1_id,
                         &#34;clientgroup2_name&#34;: clientgroup2_id,
                    }
        &#34;&#34;&#34;
        return self._clientgroups

    @property
    def all_clientgroups_cache(self) -&gt; dict:
        &#34;&#34;&#34;Returns dict of all the client groups and their info present in CommcellEntityCache in mongoDB

            dict - consists of all client groups of the in the CommcellEntityCache
                    {
                         &#34;clientgroup1_name&#34;: {
                                &#34;id&#34;: clientgroup1_id,
                                &#34;association&#34;: clientgroup1 association type,
                                &#34;company&#34;: clientgroup1 company
                                &#34;tags&#34;: clientgroup1 tags
                                },
                         &#34;clientgroup2_name&#34;: {
                                &#34;id&#34;: clientgroup2_id,
                                &#34;association&#34;: clientgroup2 association type,
                                &#34;company&#34;: clientgroup2 company
                                &#34;tags&#34;: clientgroup2 tags
                                }
                    }
        &#34;&#34;&#34;
        if not self._clientgroups_cache:
            self._clientgroups_cache = self.get_client_groups_cache()
        return self._clientgroups_cache

    def has_clientgroup(self, clientgroup_name):
        &#34;&#34;&#34;Checks if a client group exists in the commcell with the input client group name.

            Args:
                clientgroup_name (str)  --  name of the client group

            Returns:
                bool - boolean output whether the client group exists in the commcell or not

            Raises:
                SDKException:
                    if type of the client group name argument is not string
        &#34;&#34;&#34;
        if not isinstance(clientgroup_name, str):
            raise SDKException(&#39;ClientGroup&#39;, &#39;101&#39;)

        return self._clientgroups and clientgroup_name.lower() in self._clientgroups

    def create_smart_rule(self,
                          filter_rule=&#39;OS Type&#39;,
                          filter_condition=&#39;equal to&#39;,
                          filter_value=&#39;Windows&#39;,
                          value=&#39;1&#39;):
        &#34;&#34;&#34;Create/Prepare rules required for smart client group creation based on input parameters

            Args:
                filter_rule (str)      --  Rule selection to match specific criterion

                filter_condition (str) --  Filter value between selections in rule

                filter_value(str)     --   Value of rule criterion

                value(str)            --   value required to create rule

            Returns:
                    dict    -   consists of single rule based on inputs
                {
                    &#34;rule&#34;: {
                        &#34;filterID&#34;: 100,
                        &#34;secValue&#34;: &#39;Windows&#39;,
                        &#34;propID&#34;: 8,
                        &#34;propType&#34;: 4,
                        &#34;value&#34;: &#39;1&#39;
                    }
                }
        &#34;&#34;&#34;

        filter_dict = {
            &#39;equal to&#39;: 100,
            &#39;not equal&#39;: 101,
            &#39;any in selection&#39;: 108,
            &#39;not in selection&#39;: 109,
            &#39;is true&#39;: 1,
            &#39;is false&#39;: 2,
            &#39;contains&#39;: 10,
            &#39;starts with&#39;: 14,
            &#39;ends with&#39;: 15,
            &#39;does not contain&#39;: 11,
            }
        prop_id_dict = {
            &#39;Name&#39;: 1,
            &#39;Client&#39;: 2,
            &#39;Agents Installed&#39;: 3,
            &#39;Associated Client Group&#39;: 4,
            &#39;Timezone&#39;: 5,
            &#39;Hostname&#39;: 6,
            &#39;Client Version&#39;: 7,
            &#39;OS Type&#39;: 8,
            &#39;Package Installed&#39;: 9,
            &#39;Client offline (days)&#39;: 10,
            &#39;User as client owner&#39;: 11,
            &#39;Local user group as client owner&#39;: 12,
            &#39;External group as client owner&#39;: 13,
            &#39;Associated library name&#39;: 14,
            &#39;OS Version&#39;: 15,
            &#39;Product Version&#39;: 16,
            &#39;Client Version Same as CS Version&#39;: 17,
            &#39;Days since client created&#39;: 18,
            &#39;Days since last backup&#39;: 19,
            &#39;SnapBackup clients&#39;: 20,
            &#39;Clients with attached storages&#39;: 21,
            &#39;Case manager hold clients&#39;: 22,
            &#39;MediaAgents for clients in group&#39;: 23,
            &#39;Client acts as proxy&#39;: 24,
            &#39;Backup activity enabled&#39;: 25,
            &#39;Restore activity enabled&#39;: 26,
            &#39;Client online (days)&#39;: 27,
            &#39;Inactive AD user as client owner&#39;: 28,
            &#39;Client excluded from SLA report&#39;: 29,
            &#39;Client uses storage policy&#39;: 30,
            &#39;Client is not ready&#39;: 31,
            &#39;Associated Storage Policy&#39;: 32,
            &#39;MediaAgent has Lucene Index Roles&#39;: 33,
            &#39;Client associated with plan&#39;: 34,
            &#39;Client by Schedule Interval&#39;: 35,
            &#39;Client needs Updates&#39;: 36,
            &#39;Subclient Name&#39;: 37,
            &#39;CommCell Psuedo Client&#39;: 38,
            &#39;Client Description&#39;: 39,
            &#39;Clients discovered using VSA Subclient&#39;: 40,
            &#39;Clients with no Archive Data&#39;: 41,
            &#39;User Client Provider Associations&#39;: 42,
            &#39;User Group Client Provider Associations&#39;: 43,
            &#39;Company Client Provider Associations&#39;: 44,
            &#39;Clients Meet SLA&#39;: 45,
            &#39;Index Servers&#39;: 46,
            &#39;Clients with OnePass enabled&#39;: 49,
            &#39;Clients by Role&#39;: 50,
            &#39;Clients by Permission&#39;: 51,
            &#39;User description contains&#39;: 52,
            &#39;User Group description contains&#39;: 53,
            &#39;Content Analyzer Cloud&#39;: 54,
            &#39;Company Installed Client Associations&#39;: 55,
            &#39;Client Online in Last 30 Days&#39;: 56,
            &#39;Clients With Subclients Having Associated Storage Policy&#39;: 60,
            &#39;Clients With Improperly Deconfigured Subclients&#39;: 61,
            &#39;Strikes count&#39;: 62,
            &#39;Clients With Backup Schedule&#39;: 63,
            &#39;Clients With Long Running Jobs&#39;: 64,
            &#39;Clients With Synthetic Full Backup N Days&#39;: 67,
            &#39;MediaAgents for clients in group list&#39;: 70,
            &#39;Associated Client Group List&#39;: 71,
            &#39;Timezone List&#39;: 72,
            &#39;MediaAgent has Lucene Index Role List&#39;: 73,
            &#39;Associated Storage Policy List&#39;: 74,
            &#39;Timezone Region List&#39;: 75,
            &#39;Clients With Encryption&#39;: 80,
            &#39;Client CIDR Address Range&#39;: 81,
            &#39;HAC Cluster&#39;: 85,
            &#39;Client Display Name&#39;: 116,
            &#39;Clients associated to any company&#39;: 158,
            &#39;VMs not in any Subclient Content&#39;: 166,
            &#39;Pseudo Clients&#39;: 115,
            }
        ptype_dict = {
            &#39;Name&#39;: 2,
            &#39;Client&#39;: 4,
            &#39;Agents Installed&#39;: 6,
            &#39;Associated Client Group&#39;: 4,
            &#39;Timezone&#39;: 4,
            &#39;Hostname&#39;: 2,
            &#39;Client Version&#39;: 4,
            &#39;OS Type&#39;: 4,
            &#39;Package Installed&#39;: 6,
            &#39;Client offline (days)&#39;: 3,
            &#39;User as client owner&#39;: 2,
            &#39;Local user group as client owner&#39;: 2,
            &#39;External group as client owner&#39;: 2,
            &#39;Associated library name&#39;: 2,
            &#39;OS Version&#39;: 2,
            &#39;Product Version&#39;: 2,
            &#39;Client Version Same as CS Version&#39;: 1,
            &#39;Days since client created&#39;: 3,
            &#39;Days since last backup&#39;: 3,
            &#39;SnapBackup clients&#39;: 1,
            &#39;Clients with attached storages&#39;: 1,
            &#39;Case manager hold clients&#39;: 1,
            &#39;MediaAgents for clients in group&#39;: 2,
            &#39;Client acts as proxy&#39;: 1,
            &#39;Backup activity enabled&#39;: 1,
            &#39;Restore activity enabled&#39;: 1,
            &#39;Client online (days)&#39;: 3,
            &#39;Inactive AD user as client owner&#39;: 1,
            &#39;Client excluded from SLA report&#39;: 1,
            &#39;Client uses storage policy&#39;: 2,
            &#39;Client is not ready&#39;: 1,
            &#39;Associated Storage Policy&#39;: 4,
            &#39;MediaAgent has Lucene Index Roles&#39;: 4,
            &#39;Client associated with plan&#39;: 2,
            &#39;Client by Schedule Interval&#39;: 4,
            &#39;Client needs Updates&#39;: 1,
            &#39;Subclient Name&#39;: 2,
            &#39;CommCell Psuedo Client&#39;: 1,
            &#39;Client Description&#39;: 2,
            &#39;Clients discovered using VSA Subclient&#39;: 6,
            &#39;Clients with no Archive Data&#39;: 1,
            &#39;User Client Provider Associations&#39;: 2,
            &#39;User Group Client Provider Associations&#39;: 2,
            &#39;Company Client Provider Associations&#39;: 4,
            &#39;Clients Meet SLA&#39;: 4,
            &#39;Index Servers&#39;: 1,
            &#39;Clients with OnePass enabled&#39;: 1,
            &#39;Clients by Role&#39;: 4,
            &#39;Clients by Permission&#39;: 4,
            &#39;User description contains&#39;: 2,
            &#39;User Group description contains&#39;: 2,
            &#39;Content Analyzer Cloud&#39;: 1,
            &#39;Company Installed Client Associations&#39;: 4,
            &#39;Client Online in Last 30 Days&#39;: 1,
            &#39;Clients With Subclients Having Associated Storage Policy&#39;: 1,
            &#39;Clients With Improperly Deconfigured Subclients&#39;: 1,
            &#39;Strikes count&#39;: 3,
            &#39;Clients With Backup Schedule&#39;: 1,
            &#39;Clients With Long Running Jobs&#39;: 3,
            &#39;Clients With Synthetic Full Backup N Days&#39;: 3,
            &#39;MediaAgents for clients in group list&#39;: 7,
            &#39;Associated Client Group List&#39;: 7,
            &#39;Timezone List&#39;: 7,
            &#39;MediaAgent has Lucene Index Role List&#39;: 7,
            &#39;Associated Storage Policy List&#39;: 7,
            &#39;Timezone Region List&#39;: 7,
            &#39;Clients With Encryption&#39;: 1,
            &#39;Client CIDR Address Range&#39;: 10,
            &#39;HAC Cluster&#39;: 1,
            &#39;Client Display Name&#39;: 2,
            &#39;Clients associated to any company&#39;: 1,
            &#39;VMs not in any Subclient Content&#39;: 1,
            &#39;Pseudo Clients&#39;: 1,
            }

        rule_mk = {
            &#34;rule&#34;: {
                &#34;filterID&#34;: filter_dict[filter_condition],
                &#34;secValue&#34;: filter_value,
                &#34;propID&#34;: prop_id_dict[filter_rule],
                &#34;propType&#34;: ptype_dict[filter_rule],
                &#34;value&#34;: value
            }
            }

        return rule_mk

    def merge_smart_rules(self, rule_list, op_value=&#39;all&#39;, scg_op=&#39;all&#39;):
        &#34;&#34;&#34;Merge multiple rules into (SCG) rule to create smart client group.

            Args:
                rule_list (list)  --  List of smart rules to be added in rule group

                op_value (str)--     condition to apply between smart rules
                ex: all, any,not any

                scg_op (str)--       condition to apply between smart rule groups (@group level)

            Returns:
               scg_rule (dict)    -   Rule group to create smart client group

        &#34;&#34;&#34;

        op_dict = {
            &#39;all&#39;: 0,
            &#39;any&#39;: 1,
            &#39;not any&#39;: 2
        }
        scg_rule = {
            &#34;op&#34;: op_dict[scg_op],
            &#34;rules&#34;: [
            ]
        }
        rules_dict = {
            &#34;rule&#34;: {
                &#34;op&#34;: op_dict[op_value],
                &#34;rules&#34;: [
                ]
            }
        }

        for each_rule in rule_list:
            rules_dict[&#34;rule&#34;][&#34;rules&#34;].append(each_rule)

        scg_rule[&#34;rules&#34;].append(rules_dict)
        return scg_rule

    def _create_scope_dict(self, client_scope, value=None):
        &#34;&#34;&#34;Creates required dictionary for given client scope

            Args:
                value (string)  --  Value to be selected for the client scope dropdown
                client_scope (string) -- Value of the client scope

            Accepted Values (client_scope) --
                Clients in this Commcell
                Clients of Companies
                Clients of User
                Clients of User Groups

            Returns:
                dictionary - Client Scope data required for the smart client group

            NOTE : Value is not required for client scope = &#34;Clients in this Commcell&#34;
            For this, value is automatically set to the Commserve Name
        &#34;&#34;&#34;
        scgscope = {
            &#34;entity&#34;: {}
        }
        if client_scope.lower() == &#39;clients in this commcell&#39;:
            scgscope[&#34;entity&#34;] = {
                &#34;commCellName&#34;: self._commcell_object.commserv_name,
                &#34;_type_&#34;: 1
            }
        elif client_scope.lower() == &#39;clients of companies&#39; and value is not None:
            scgscope[&#34;entity&#34;] = {
                &#34;providerDomainName&#34;: value,
                &#34;_type_&#34;: 61
            }
        elif client_scope.lower() == &#39;clients of user&#39; and value is not None:
            scgscope[&#34;entity&#34;] = {
                &#34;userName&#34;: value,
                &#34;_type_&#34;: 13
            }
        elif client_scope.lower() == &#39;clients of user group&#39; and value is not None:
            scgscope[&#34;entity&#34;] = {
                &#34;userGroupName&#34;: value,
                &#34;_type_&#34;: 15
            }
        return scgscope

    def add(self, clientgroup_name, clients=[], **kwargs):
        &#34;&#34;&#34;Adds a new Client Group to the Commcell.

            Args:
                clientgroup_name        (str)        --  name of the new client group to add

                clients                 (str/list)   --  &#39;,&#39; separated string of client names,
                                                             or a list of clients,
                                                             to be added under client group
                                                            default: []

                ** kwargs               (dict)       -- Key value pairs for supported arguments

                Supported:

                    clientgroup_description (str)        --  description of the client group
                                                                default: &#34;&#34;

                    enable_backup           (bool)       --  enable or disable backup
                                                                default: True

                    enable_restore          (bool)       --  enable or disable restore
                                                                default: True

                    enable_data_aging       (bool)       --  enable or disable data aging
                                                                default: True
                    scg_rule                (dict)       --  scg_rule required to create smart
                                                                client group

                    client_scope            (str)        --  Client scope for the Smart Client Group

                    client_scope_value      (str)        --  Client scope value for a particular scope

            Returns:
                object - instance of the ClientGroup class created by this method

            Raises:
                SDKException:
                    if type of client group name and description is not of type string

                    if clients argument is not of type list / string

                    if response is empty

                    if response is not success

                    if client group already exists with the given name
        &#34;&#34;&#34;
        if not (isinstance(clientgroup_name, str) and
                isinstance(kwargs.get(&#39;clientgroup_description&#39;, &#39;&#39;), str)):
            raise SDKException(&#39;ClientGroup&#39;, &#39;101&#39;)

        if not self.has_clientgroup(clientgroup_name):
            if isinstance(clients, list):
                clients = self._valid_clients(clients)
            elif isinstance(clients, str):
                clients = self._valid_clients(clients.split(&#39;,&#39;))
            else:
                raise SDKException(&#39;ClientGroup&#39;, &#39;101&#39;)

            clients_list = []

            for client in clients:
                clients_list.append({&#39;clientName&#39;: client})

            smart_client_group = bool(kwargs.get(&#39;scg_rule&#39;))
            if kwargs.get(&#39;scg_rule&#39;) is None:
                kwargs[&#39;scg_rule&#39;] = {}

            request_json = {
                &#34;clientGroupOperationType&#34;: 1,
                &#34;clientGroupDetail&#34;: {
                    &#34;description&#34;: kwargs.get(&#39;clientgroup_description&#39;, &#39;&#39;),
                    &#34;isSmartClientGroup&#34;: smart_client_group,
                    &#34;scgRule&#34;: kwargs.get(&#39;scg_rule&#39;),
                    &#34;clientGroup&#34;: {
                        &#34;clientGroupName&#34;: clientgroup_name
                    },
                    &#34;associatedClients&#34;: clients_list
                }
            }

            scg_scope = None
            if kwargs.get(&#34;client_scope&#34;) is not None:
                # Check if value is there or not
                if kwargs.get(&#34;client_scope&#34;).lower() == &#34;clients in this commcell&#34;:
                    scg_scope = [self._create_scope_dict(kwargs.get(&#34;client_scope&#34;))]
                else:
                    if kwargs.get(&#34;client_scope_value&#34;) is not None:
                        scg_scope = [self._create_scope_dict(kwargs.get(&#34;client_scope&#34;), kwargs.get(&#34;client_scope_value&#34;))]
                    else:
                        raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;,
                                           &#34;Client Scope {0} requires a value&#34;.format(kwargs.get(&#34;client_scope&#34;)))

            if scg_scope is not None:
                request_json[&#34;clientGroupDetail&#34;][&#34;scgScope&#34;] = scg_scope

            if kwargs.get(&#34;enable_backup&#34;) or kwargs.get(&#34;enable_data_aging&#34;) or kwargs.get(&#34;enable_restore&#34;):
                client_group_activity_control = {
                        &#34;activityControlOptions&#34;: [
                            {
                                &#34;activityType&#34;: 1,
                                &#34;enableAfterADelay&#34;: False,
                                &#34;enableActivityType&#34;: kwargs.get(&#39;enable_backup&#39;, True)
                            }, {
                                &#34;activityType&#34;: 16,
                                &#34;enableAfterADelay&#34;: False,
                                &#34;enableActivityType&#34;: kwargs.get(&#39;enable_data_aging&#39;, True)
                            }, {
                                &#34;activityType&#34;: 2,
                                &#34;enableAfterADelay&#34;: False,
                                &#34;enableActivityType&#34;: kwargs.get(&#39;enable_restore&#39;, True)
                            }
                        ]
                    }
                request_json[&#34;clientGroupDetail&#34;][&#34;clientGroupActivityControl&#34;] = client_group_activity_control

            flag, response = self._commcell_object._cvpysdk_object.make_request(
                &#39;POST&#39;, self._CLIENTGROUPS, request_json
            )

            if flag:
                if response.json():
                    error_message = None

                    if &#39;errorMessage&#39; in response.json():
                        error_message = response.json()[&#39;errorMessage&#39;]
                        o_str = &#39;Failed to create new ClientGroup\nError:&#34;{0}&#34;&#39;.format(
                            error_message
                        )
                        raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)
                    elif &#39;clientGroupDetail&#39; in response.json():
                        self.refresh()
                        clientgroup_id = response.json()[&#39;clientGroupDetail&#39;][
                            &#39;clientGroup&#39;][&#39;clientGroupId&#39;]

                        return ClientGroup(
                            self._commcell_object, clientgroup_name, clientgroup_id
                        )
                    else:
                        o_str = &#39;Failed to create new ClientGroup&#39;
                        raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                response_string = self._commcell_object._update_response_(response.text)
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        else:
            raise SDKException(
                &#39;ClientGroup&#39;, &#39;102&#39;, &#39;Client Group &#34;{0}&#34; already exists.&#39;.format(clientgroup_name)
            )

    def get(self, clientgroup_name):
        &#34;&#34;&#34;Returns a client group object of the specified client group name.

            Args:
                clientgroup_name (str)  --  name of the client group

            Returns:
                object - instance of the ClientGroup class for the given clientgroup name

            Raises:
                SDKException:
                    if type of the client group name argument is not string

                    if no client group exists with the given name
        &#34;&#34;&#34;
        if not isinstance(clientgroup_name, str):
            raise SDKException(&#39;ClientGroup&#39;, &#39;101&#39;)
        else:
            clientgroup_name = clientgroup_name.lower()

            if self.has_clientgroup(clientgroup_name):
                return ClientGroup(
                    self._commcell_object, clientgroup_name, self._clientgroups[clientgroup_name]
                )

            raise SDKException(
                &#39;ClientGroup&#39;,
                &#39;102&#39;,
                &#39;No ClientGroup exists with name: {0}&#39;.format(clientgroup_name)
            )

    def delete(self, clientgroup_name):
        &#34;&#34;&#34;Deletes the clientgroup from the commcell.

            Args:
                clientgroup_name (str)  --  name of the clientgroup

            Raises:
                SDKException:
                    if type of the clientgroup name argument is not string

                    if response is empty

                    if failed to delete the client group

                    if no clientgroup exists with the given name
        &#34;&#34;&#34;

        if not isinstance(clientgroup_name, str):
            raise SDKException(&#39;ClientGroup&#39;, &#39;101&#39;)
        else:
            clientgroup_name = clientgroup_name.lower()

            if self.has_clientgroup(clientgroup_name):
                clientgroup_id = self._clientgroups[clientgroup_name]

                delete_clientgroup_service = self._commcell_object._services[&#39;CLIENTGROUP&#39;]

                flag, response = self._commcell_object._cvpysdk_object.make_request(
                    &#39;DELETE&#39;, delete_clientgroup_service % clientgroup_id
                )

                if flag:
                    if response.json():
                        if &#39;errorCode&#39; in response.json():
                            error_code = str(response.json()[&#39;errorCode&#39;])
                            error_message = response.json()[&#39;errorMessage&#39;]

                            if error_code == &#39;0&#39;:
                                # initialize the clientgroups again
                                # so the clientgroups object has all the client groups
                                self.refresh()
                            else:
                                o_str = &#39;Failed to delete ClientGroup\nError: &#34;{0}&#34;&#39;.format(
                                    error_message
                                )
                                raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)
                        else:
                            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
                    else:
                        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
                else:
                    response_string = self._commcell_object._update_response_(response.text)
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
            else:
                raise SDKException(
                    &#39;ClientGroup&#39;,
                    &#39;102&#39;,
                    &#39;No ClientGroup exists with name: &#34;{0}&#34;&#39;.format(clientgroup_name)
                )

    def refresh(self, **kwargs):
        &#34;&#34;&#34;
        Refresh the list of client groups on this commcell.

            Args:
                **kwargs (dict):
                    mongodb (bool)  -- Flag to fetch client groups cache from MongoDB (default: False).
                    hard (bool)     -- Flag to hard refresh MongoDB cache for this entity (default: False).
        &#34;&#34;&#34;
        mongodb = kwargs.get(&#39;mongodb&#39;, False)
        hard = kwargs.get(&#39;hard&#39;, False)

        self._clientgroups = self._get_clientgroups()
        if mongodb:
            self._clientgroups_cache = self.get_client_groups_cache(hard=hard)

    @property
    def all_client_groups_prop(self)-&gt;list[dict]:
        &#34;&#34;&#34;
        Returns complete GET API response
        &#34;&#34;&#34;
        self._all_client_groups_prop = self._get_clientgroups(full_response=True).get(&#34;groups&#34;,[])
        return self._all_client_groups_prop</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.clientgroup.ClientGroups.all_client_groups_prop"><code class="name">var <span class="ident">all_client_groups_prop</span> : list[dict]</code></dt>
<dd>
<div class="desc"><p>Returns complete GET API response</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L1113-L1119" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def all_client_groups_prop(self)-&gt;list[dict]:
    &#34;&#34;&#34;
    Returns complete GET API response
    &#34;&#34;&#34;
    self._all_client_groups_prop = self._get_clientgroups(full_response=True).get(&#34;groups&#34;,[])
    return self._all_client_groups_prop</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroups.all_clientgroups"><code class="name">var <span class="ident">all_clientgroups</span></code></dt>
<dd>
<div class="desc"><p>Returns dict of all the clientgroups associated with this commcell</p>
<p>dict - consists of all clientgroups of the commcell
{
"clientgroup1_name": clientgroup1_id,
"clientgroup2_name": clientgroup2_id,
}</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L512-L522" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def all_clientgroups(self):
    &#34;&#34;&#34;Returns dict of all the clientgroups associated with this commcell

        dict - consists of all clientgroups of the commcell
                {
                     &#34;clientgroup1_name&#34;: clientgroup1_id,
                     &#34;clientgroup2_name&#34;: clientgroup2_id,
                }
    &#34;&#34;&#34;
    return self._clientgroups</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroups.all_clientgroups_cache"><code class="name">var <span class="ident">all_clientgroups_cache</span> : dict</code></dt>
<dd>
<div class="desc"><p>Returns dict of all the client groups and their info present in CommcellEntityCache in mongoDB</p>
<p>dict - consists of all client groups of the in the CommcellEntityCache
{
"clientgroup1_name": {
"id": clientgroup1_id,
"association": clientgroup1 association type,
"company": clientgroup1 company
"tags": clientgroup1 tags
},
"clientgroup2_name": {
"id": clientgroup2_id,
"association": clientgroup2 association type,
"company": clientgroup2 company
"tags": clientgroup2 tags
}
}</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L524-L546" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def all_clientgroups_cache(self) -&gt; dict:
    &#34;&#34;&#34;Returns dict of all the client groups and their info present in CommcellEntityCache in mongoDB

        dict - consists of all client groups of the in the CommcellEntityCache
                {
                     &#34;clientgroup1_name&#34;: {
                            &#34;id&#34;: clientgroup1_id,
                            &#34;association&#34;: clientgroup1 association type,
                            &#34;company&#34;: clientgroup1 company
                            &#34;tags&#34;: clientgroup1 tags
                            },
                     &#34;clientgroup2_name&#34;: {
                            &#34;id&#34;: clientgroup2_id,
                            &#34;association&#34;: clientgroup2 association type,
                            &#34;company&#34;: clientgroup2 company
                            &#34;tags&#34;: clientgroup2 tags
                            }
                }
    &#34;&#34;&#34;
    if not self._clientgroups_cache:
        self._clientgroups_cache = self.get_client_groups_cache()
    return self._clientgroups_cache</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.clientgroup.ClientGroups.add"><code class="name flex">
<span>def <span class="ident">add</span></span>(<span>self, clientgroup_name, clients=[], **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds a new Client Group to the Commcell.</p>
<h2 id="args">Args</h2>
<p>clientgroup_name
(str)
&ndash;
name of the new client group to add</p>
<p>clients
(str/list)
&ndash;
',' separated string of client names,
or a list of clients,
to be added under client group
default: []</p>
<p>** kwargs
(dict)
&ndash; Key value pairs for supported arguments</p>
<p>Supported:</p>
<pre><code>clientgroup_description (str)        --  description of the client group
                                            default: ""

enable_backup           (bool)       --  enable or disable backup
                                            default: True

enable_restore          (bool)       --  enable or disable restore
                                            default: True

enable_data_aging       (bool)       --  enable or disable data aging
                                            default: True
scg_rule                (dict)       --  scg_rule required to create smart
                                            client group

client_scope            (str)        --  Client scope for the Smart Client Group

client_scope_value      (str)        --  Client scope value for a particular scope
</code></pre>
<h2 id="returns">Returns</h2>
<p>object - instance of the ClientGroup class created by this method</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of client group name and description is not of type string</p>
<pre><code>if clients argument is not of type list / string

if response is empty

if response is not success

if client group already exists with the given name
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L854-L1004" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add(self, clientgroup_name, clients=[], **kwargs):
    &#34;&#34;&#34;Adds a new Client Group to the Commcell.

        Args:
            clientgroup_name        (str)        --  name of the new client group to add

            clients                 (str/list)   --  &#39;,&#39; separated string of client names,
                                                         or a list of clients,
                                                         to be added under client group
                                                        default: []

            ** kwargs               (dict)       -- Key value pairs for supported arguments

            Supported:

                clientgroup_description (str)        --  description of the client group
                                                            default: &#34;&#34;

                enable_backup           (bool)       --  enable or disable backup
                                                            default: True

                enable_restore          (bool)       --  enable or disable restore
                                                            default: True

                enable_data_aging       (bool)       --  enable or disable data aging
                                                            default: True
                scg_rule                (dict)       --  scg_rule required to create smart
                                                            client group

                client_scope            (str)        --  Client scope for the Smart Client Group

                client_scope_value      (str)        --  Client scope value for a particular scope

        Returns:
            object - instance of the ClientGroup class created by this method

        Raises:
            SDKException:
                if type of client group name and description is not of type string

                if clients argument is not of type list / string

                if response is empty

                if response is not success

                if client group already exists with the given name
    &#34;&#34;&#34;
    if not (isinstance(clientgroup_name, str) and
            isinstance(kwargs.get(&#39;clientgroup_description&#39;, &#39;&#39;), str)):
        raise SDKException(&#39;ClientGroup&#39;, &#39;101&#39;)

    if not self.has_clientgroup(clientgroup_name):
        if isinstance(clients, list):
            clients = self._valid_clients(clients)
        elif isinstance(clients, str):
            clients = self._valid_clients(clients.split(&#39;,&#39;))
        else:
            raise SDKException(&#39;ClientGroup&#39;, &#39;101&#39;)

        clients_list = []

        for client in clients:
            clients_list.append({&#39;clientName&#39;: client})

        smart_client_group = bool(kwargs.get(&#39;scg_rule&#39;))
        if kwargs.get(&#39;scg_rule&#39;) is None:
            kwargs[&#39;scg_rule&#39;] = {}

        request_json = {
            &#34;clientGroupOperationType&#34;: 1,
            &#34;clientGroupDetail&#34;: {
                &#34;description&#34;: kwargs.get(&#39;clientgroup_description&#39;, &#39;&#39;),
                &#34;isSmartClientGroup&#34;: smart_client_group,
                &#34;scgRule&#34;: kwargs.get(&#39;scg_rule&#39;),
                &#34;clientGroup&#34;: {
                    &#34;clientGroupName&#34;: clientgroup_name
                },
                &#34;associatedClients&#34;: clients_list
            }
        }

        scg_scope = None
        if kwargs.get(&#34;client_scope&#34;) is not None:
            # Check if value is there or not
            if kwargs.get(&#34;client_scope&#34;).lower() == &#34;clients in this commcell&#34;:
                scg_scope = [self._create_scope_dict(kwargs.get(&#34;client_scope&#34;))]
            else:
                if kwargs.get(&#34;client_scope_value&#34;) is not None:
                    scg_scope = [self._create_scope_dict(kwargs.get(&#34;client_scope&#34;), kwargs.get(&#34;client_scope_value&#34;))]
                else:
                    raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;,
                                       &#34;Client Scope {0} requires a value&#34;.format(kwargs.get(&#34;client_scope&#34;)))

        if scg_scope is not None:
            request_json[&#34;clientGroupDetail&#34;][&#34;scgScope&#34;] = scg_scope

        if kwargs.get(&#34;enable_backup&#34;) or kwargs.get(&#34;enable_data_aging&#34;) or kwargs.get(&#34;enable_restore&#34;):
            client_group_activity_control = {
                    &#34;activityControlOptions&#34;: [
                        {
                            &#34;activityType&#34;: 1,
                            &#34;enableAfterADelay&#34;: False,
                            &#34;enableActivityType&#34;: kwargs.get(&#39;enable_backup&#39;, True)
                        }, {
                            &#34;activityType&#34;: 16,
                            &#34;enableAfterADelay&#34;: False,
                            &#34;enableActivityType&#34;: kwargs.get(&#39;enable_data_aging&#39;, True)
                        }, {
                            &#34;activityType&#34;: 2,
                            &#34;enableAfterADelay&#34;: False,
                            &#34;enableActivityType&#34;: kwargs.get(&#39;enable_restore&#39;, True)
                        }
                    ]
                }
            request_json[&#34;clientGroupDetail&#34;][&#34;clientGroupActivityControl&#34;] = client_group_activity_control

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._CLIENTGROUPS, request_json
        )

        if flag:
            if response.json():
                error_message = None

                if &#39;errorMessage&#39; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Failed to create new ClientGroup\nError:&#34;{0}&#34;&#39;.format(
                        error_message
                    )
                    raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)
                elif &#39;clientGroupDetail&#39; in response.json():
                    self.refresh()
                    clientgroup_id = response.json()[&#39;clientGroupDetail&#39;][
                        &#39;clientGroup&#39;][&#39;clientGroupId&#39;]

                    return ClientGroup(
                        self._commcell_object, clientgroup_name, clientgroup_id
                    )
                else:
                    o_str = &#39;Failed to create new ClientGroup&#39;
                    raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
    else:
        raise SDKException(
            &#39;ClientGroup&#39;, &#39;102&#39;, &#39;Client Group &#34;{0}&#34; already exists.&#39;.format(clientgroup_name)
        )</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroups.create_smart_rule"><code class="name flex">
<span>def <span class="ident">create_smart_rule</span></span>(<span>self, filter_rule='OS Type', filter_condition='equal to', filter_value='Windows', value='1')</span>
</code></dt>
<dd>
<div class="desc"><p>Create/Prepare rules required for smart client group creation based on input parameters</p>
<h2 id="args">Args</h2>
<p>filter_rule (str)
&ndash;
Rule selection to match specific criterion</p>
<p>filter_condition (str) &ndash;
Filter value between selections in rule</p>
<p>filter_value(str)
&ndash;
Value of rule criterion</p>
<p>value(str)
&ndash;
value required to create rule</p>
<h2 id="returns">Returns</h2>
<pre><code>dict    -   consists of single rule based on inputs
</code></pre>
<p>{
"rule": {
"filterID": 100,
"secValue": 'Windows',
"propID": 8,
"propType": 4,
"value": '1'
}
}</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L566-L768" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def create_smart_rule(self,
                      filter_rule=&#39;OS Type&#39;,
                      filter_condition=&#39;equal to&#39;,
                      filter_value=&#39;Windows&#39;,
                      value=&#39;1&#39;):
    &#34;&#34;&#34;Create/Prepare rules required for smart client group creation based on input parameters

        Args:
            filter_rule (str)      --  Rule selection to match specific criterion

            filter_condition (str) --  Filter value between selections in rule

            filter_value(str)     --   Value of rule criterion

            value(str)            --   value required to create rule

        Returns:
                dict    -   consists of single rule based on inputs
            {
                &#34;rule&#34;: {
                    &#34;filterID&#34;: 100,
                    &#34;secValue&#34;: &#39;Windows&#39;,
                    &#34;propID&#34;: 8,
                    &#34;propType&#34;: 4,
                    &#34;value&#34;: &#39;1&#39;
                }
            }
    &#34;&#34;&#34;

    filter_dict = {
        &#39;equal to&#39;: 100,
        &#39;not equal&#39;: 101,
        &#39;any in selection&#39;: 108,
        &#39;not in selection&#39;: 109,
        &#39;is true&#39;: 1,
        &#39;is false&#39;: 2,
        &#39;contains&#39;: 10,
        &#39;starts with&#39;: 14,
        &#39;ends with&#39;: 15,
        &#39;does not contain&#39;: 11,
        }
    prop_id_dict = {
        &#39;Name&#39;: 1,
        &#39;Client&#39;: 2,
        &#39;Agents Installed&#39;: 3,
        &#39;Associated Client Group&#39;: 4,
        &#39;Timezone&#39;: 5,
        &#39;Hostname&#39;: 6,
        &#39;Client Version&#39;: 7,
        &#39;OS Type&#39;: 8,
        &#39;Package Installed&#39;: 9,
        &#39;Client offline (days)&#39;: 10,
        &#39;User as client owner&#39;: 11,
        &#39;Local user group as client owner&#39;: 12,
        &#39;External group as client owner&#39;: 13,
        &#39;Associated library name&#39;: 14,
        &#39;OS Version&#39;: 15,
        &#39;Product Version&#39;: 16,
        &#39;Client Version Same as CS Version&#39;: 17,
        &#39;Days since client created&#39;: 18,
        &#39;Days since last backup&#39;: 19,
        &#39;SnapBackup clients&#39;: 20,
        &#39;Clients with attached storages&#39;: 21,
        &#39;Case manager hold clients&#39;: 22,
        &#39;MediaAgents for clients in group&#39;: 23,
        &#39;Client acts as proxy&#39;: 24,
        &#39;Backup activity enabled&#39;: 25,
        &#39;Restore activity enabled&#39;: 26,
        &#39;Client online (days)&#39;: 27,
        &#39;Inactive AD user as client owner&#39;: 28,
        &#39;Client excluded from SLA report&#39;: 29,
        &#39;Client uses storage policy&#39;: 30,
        &#39;Client is not ready&#39;: 31,
        &#39;Associated Storage Policy&#39;: 32,
        &#39;MediaAgent has Lucene Index Roles&#39;: 33,
        &#39;Client associated with plan&#39;: 34,
        &#39;Client by Schedule Interval&#39;: 35,
        &#39;Client needs Updates&#39;: 36,
        &#39;Subclient Name&#39;: 37,
        &#39;CommCell Psuedo Client&#39;: 38,
        &#39;Client Description&#39;: 39,
        &#39;Clients discovered using VSA Subclient&#39;: 40,
        &#39;Clients with no Archive Data&#39;: 41,
        &#39;User Client Provider Associations&#39;: 42,
        &#39;User Group Client Provider Associations&#39;: 43,
        &#39;Company Client Provider Associations&#39;: 44,
        &#39;Clients Meet SLA&#39;: 45,
        &#39;Index Servers&#39;: 46,
        &#39;Clients with OnePass enabled&#39;: 49,
        &#39;Clients by Role&#39;: 50,
        &#39;Clients by Permission&#39;: 51,
        &#39;User description contains&#39;: 52,
        &#39;User Group description contains&#39;: 53,
        &#39;Content Analyzer Cloud&#39;: 54,
        &#39;Company Installed Client Associations&#39;: 55,
        &#39;Client Online in Last 30 Days&#39;: 56,
        &#39;Clients With Subclients Having Associated Storage Policy&#39;: 60,
        &#39;Clients With Improperly Deconfigured Subclients&#39;: 61,
        &#39;Strikes count&#39;: 62,
        &#39;Clients With Backup Schedule&#39;: 63,
        &#39;Clients With Long Running Jobs&#39;: 64,
        &#39;Clients With Synthetic Full Backup N Days&#39;: 67,
        &#39;MediaAgents for clients in group list&#39;: 70,
        &#39;Associated Client Group List&#39;: 71,
        &#39;Timezone List&#39;: 72,
        &#39;MediaAgent has Lucene Index Role List&#39;: 73,
        &#39;Associated Storage Policy List&#39;: 74,
        &#39;Timezone Region List&#39;: 75,
        &#39;Clients With Encryption&#39;: 80,
        &#39;Client CIDR Address Range&#39;: 81,
        &#39;HAC Cluster&#39;: 85,
        &#39;Client Display Name&#39;: 116,
        &#39;Clients associated to any company&#39;: 158,
        &#39;VMs not in any Subclient Content&#39;: 166,
        &#39;Pseudo Clients&#39;: 115,
        }
    ptype_dict = {
        &#39;Name&#39;: 2,
        &#39;Client&#39;: 4,
        &#39;Agents Installed&#39;: 6,
        &#39;Associated Client Group&#39;: 4,
        &#39;Timezone&#39;: 4,
        &#39;Hostname&#39;: 2,
        &#39;Client Version&#39;: 4,
        &#39;OS Type&#39;: 4,
        &#39;Package Installed&#39;: 6,
        &#39;Client offline (days)&#39;: 3,
        &#39;User as client owner&#39;: 2,
        &#39;Local user group as client owner&#39;: 2,
        &#39;External group as client owner&#39;: 2,
        &#39;Associated library name&#39;: 2,
        &#39;OS Version&#39;: 2,
        &#39;Product Version&#39;: 2,
        &#39;Client Version Same as CS Version&#39;: 1,
        &#39;Days since client created&#39;: 3,
        &#39;Days since last backup&#39;: 3,
        &#39;SnapBackup clients&#39;: 1,
        &#39;Clients with attached storages&#39;: 1,
        &#39;Case manager hold clients&#39;: 1,
        &#39;MediaAgents for clients in group&#39;: 2,
        &#39;Client acts as proxy&#39;: 1,
        &#39;Backup activity enabled&#39;: 1,
        &#39;Restore activity enabled&#39;: 1,
        &#39;Client online (days)&#39;: 3,
        &#39;Inactive AD user as client owner&#39;: 1,
        &#39;Client excluded from SLA report&#39;: 1,
        &#39;Client uses storage policy&#39;: 2,
        &#39;Client is not ready&#39;: 1,
        &#39;Associated Storage Policy&#39;: 4,
        &#39;MediaAgent has Lucene Index Roles&#39;: 4,
        &#39;Client associated with plan&#39;: 2,
        &#39;Client by Schedule Interval&#39;: 4,
        &#39;Client needs Updates&#39;: 1,
        &#39;Subclient Name&#39;: 2,
        &#39;CommCell Psuedo Client&#39;: 1,
        &#39;Client Description&#39;: 2,
        &#39;Clients discovered using VSA Subclient&#39;: 6,
        &#39;Clients with no Archive Data&#39;: 1,
        &#39;User Client Provider Associations&#39;: 2,
        &#39;User Group Client Provider Associations&#39;: 2,
        &#39;Company Client Provider Associations&#39;: 4,
        &#39;Clients Meet SLA&#39;: 4,
        &#39;Index Servers&#39;: 1,
        &#39;Clients with OnePass enabled&#39;: 1,
        &#39;Clients by Role&#39;: 4,
        &#39;Clients by Permission&#39;: 4,
        &#39;User description contains&#39;: 2,
        &#39;User Group description contains&#39;: 2,
        &#39;Content Analyzer Cloud&#39;: 1,
        &#39;Company Installed Client Associations&#39;: 4,
        &#39;Client Online in Last 30 Days&#39;: 1,
        &#39;Clients With Subclients Having Associated Storage Policy&#39;: 1,
        &#39;Clients With Improperly Deconfigured Subclients&#39;: 1,
        &#39;Strikes count&#39;: 3,
        &#39;Clients With Backup Schedule&#39;: 1,
        &#39;Clients With Long Running Jobs&#39;: 3,
        &#39;Clients With Synthetic Full Backup N Days&#39;: 3,
        &#39;MediaAgents for clients in group list&#39;: 7,
        &#39;Associated Client Group List&#39;: 7,
        &#39;Timezone List&#39;: 7,
        &#39;MediaAgent has Lucene Index Role List&#39;: 7,
        &#39;Associated Storage Policy List&#39;: 7,
        &#39;Timezone Region List&#39;: 7,
        &#39;Clients With Encryption&#39;: 1,
        &#39;Client CIDR Address Range&#39;: 10,
        &#39;HAC Cluster&#39;: 1,
        &#39;Client Display Name&#39;: 2,
        &#39;Clients associated to any company&#39;: 1,
        &#39;VMs not in any Subclient Content&#39;: 1,
        &#39;Pseudo Clients&#39;: 1,
        }

    rule_mk = {
        &#34;rule&#34;: {
            &#34;filterID&#34;: filter_dict[filter_condition],
            &#34;secValue&#34;: filter_value,
            &#34;propID&#34;: prop_id_dict[filter_rule],
            &#34;propType&#34;: ptype_dict[filter_rule],
            &#34;value&#34;: value
        }
        }

    return rule_mk</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroups.delete"><code class="name flex">
<span>def <span class="ident">delete</span></span>(<span>self, clientgroup_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes the clientgroup from the commcell.</p>
<h2 id="args">Args</h2>
<p>clientgroup_name (str)
&ndash;
name of the clientgroup</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the clientgroup name argument is not string</p>
<pre><code>if response is empty

if failed to delete the client group

if no clientgroup exists with the given name
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L1037-L1095" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete(self, clientgroup_name):
    &#34;&#34;&#34;Deletes the clientgroup from the commcell.

        Args:
            clientgroup_name (str)  --  name of the clientgroup

        Raises:
            SDKException:
                if type of the clientgroup name argument is not string

                if response is empty

                if failed to delete the client group

                if no clientgroup exists with the given name
    &#34;&#34;&#34;

    if not isinstance(clientgroup_name, str):
        raise SDKException(&#39;ClientGroup&#39;, &#39;101&#39;)
    else:
        clientgroup_name = clientgroup_name.lower()

        if self.has_clientgroup(clientgroup_name):
            clientgroup_id = self._clientgroups[clientgroup_name]

            delete_clientgroup_service = self._commcell_object._services[&#39;CLIENTGROUP&#39;]

            flag, response = self._commcell_object._cvpysdk_object.make_request(
                &#39;DELETE&#39;, delete_clientgroup_service % clientgroup_id
            )

            if flag:
                if response.json():
                    if &#39;errorCode&#39; in response.json():
                        error_code = str(response.json()[&#39;errorCode&#39;])
                        error_message = response.json()[&#39;errorMessage&#39;]

                        if error_code == &#39;0&#39;:
                            # initialize the clientgroups again
                            # so the clientgroups object has all the client groups
                            self.refresh()
                        else:
                            o_str = &#39;Failed to delete ClientGroup\nError: &#34;{0}&#34;&#39;.format(
                                error_message
                            )
                            raise SDKException(&#39;ClientGroup&#39;, &#39;102&#39;, o_str)
                    else:
                        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                response_string = self._commcell_object._update_response_(response.text)
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        else:
            raise SDKException(
                &#39;ClientGroup&#39;,
                &#39;102&#39;,
                &#39;No ClientGroup exists with name: &#34;{0}&#34;&#39;.format(clientgroup_name)
            )</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroups.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, clientgroup_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a client group object of the specified client group name.</p>
<h2 id="args">Args</h2>
<p>clientgroup_name (str)
&ndash;
name of the client group</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the ClientGroup class for the given clientgroup name</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the client group name argument is not string</p>
<pre><code>if no client group exists with the given name
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L1006-L1035" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, clientgroup_name):
    &#34;&#34;&#34;Returns a client group object of the specified client group name.

        Args:
            clientgroup_name (str)  --  name of the client group

        Returns:
            object - instance of the ClientGroup class for the given clientgroup name

        Raises:
            SDKException:
                if type of the client group name argument is not string

                if no client group exists with the given name
    &#34;&#34;&#34;
    if not isinstance(clientgroup_name, str):
        raise SDKException(&#39;ClientGroup&#39;, &#39;101&#39;)
    else:
        clientgroup_name = clientgroup_name.lower()

        if self.has_clientgroup(clientgroup_name):
            return ClientGroup(
                self._commcell_object, clientgroup_name, self._clientgroups[clientgroup_name]
            )

        raise SDKException(
            &#39;ClientGroup&#39;,
            &#39;102&#39;,
            &#39;No ClientGroup exists with name: {0}&#39;.format(clientgroup_name)
        )</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroups.get_client_groups_cache"><code class="name flex">
<span>def <span class="ident">get_client_groups_cache</span></span>(<span>self, hard: bool = False, **kwargs) ‑> dict</span>
</code></dt>
<dd>
<div class="desc"><p>Gets all the client groups present in CommcellEntityCache DB.</p>
<h2 id="args">Args</h2>
<p>hard
(bool)
&ndash;
Flag to perform hard refresh on client groups cache.
**kwargs (dict):
- fl (list)
&ndash;
List of columns to return in response (default: None).
- sort (list)
&ndash;
Contains the name of the column on which sorting will be performed and type of sort.
Valid sort type: 1 for ascending and -1 for descending
e.g. sort = ['columnName', '1'] (default: None).
- limit (list)
&ndash;
Contains the start and limit parameter value.
Default ['0', '100'].
- search (str)
&ndash;
Contains the string to search in the commcell entity cache (default: None).
- fq (list)
&ndash;
Contains the columnName, condition and value.
e.g. fq = [['name', 'contains', 'test'],
['association', 'eq', 'Manual']] (default: None).</p>
<h2 id="returns">Returns</h2>
<dl>
<dt><code>dict</code></dt>
<dd>Dictionary of all the properties present in response.</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L442-L510" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_client_groups_cache(self, hard: bool = False, **kwargs) -&gt; dict:
    &#34;&#34;&#34;
    Gets all the client groups present in CommcellEntityCache DB.

    Args:
        hard  (bool)        --   Flag to perform hard refresh on client groups cache.
        **kwargs (dict):
            - fl (list)     --   List of columns to return in response (default: None).
            - sort (list)   --   Contains the name of the column on which sorting will be performed and type of sort.
                                       Valid sort type: 1 for ascending and -1 for descending
                                       e.g. sort = [&#39;columnName&#39;, &#39;1&#39;] (default: None).
            - limit (list)  --   Contains the start and limit parameter value.
                                        Default [&#39;0&#39;, &#39;100&#39;].
            - search (str)  --   Contains the string to search in the commcell entity cache (default: None).
            - fq (list)     --   Contains the columnName, condition and value.
                                        e.g. fq = [[&#39;name&#39;, &#39;contains&#39;, &#39;test&#39;],
                                         [&#39;association&#39;, &#39;eq&#39;, &#39;Manual&#39;]] (default: None).

    Returns:
        dict: Dictionary of all the properties present in response.
    &#34;&#34;&#34;
    # computing params
    fl_parameters = self._get_fl_parameters(kwargs.get(&#39;fl&#39;, None))
    fq_parameters = self._get_fq_parameters(kwargs.get(&#39;fq&#39;, None))
    limit = kwargs.get(&#39;limit&#39;, None)
    limit_parameters = f&#39;start={limit[0]}&amp;limit={limit[1]}&#39; if limit else &#39;&#39;
    hard_refresh = &#39;&amp;hardRefresh=true&#39; if hard else &#39;&#39;
    sort_parameters = self._get_sort_parameters(kwargs.get(&#39;sort&#39;, None)) if kwargs.get(&#39;sort&#39;, None) else &#39;&#39;

    # Search operation can only be performed on limited columns, so filtering out the columns on which search works
    searchable_columns = [&#34;name&#34;,&#34;association&#34;,&#39;companyName&#39;]
    search_parameter = (f&#39;&amp;search={&#34;,&#34;.join(self.valid_columns[col] for col in searchable_columns)}:contains:&#39;
                        f&#39;{kwargs.get(&#34;search&#34;, None)}&#39;) if kwargs.get(&#39;search&#39;, None) else &#39;&#39;

    params = [
        limit_parameters,
        sort_parameters,
        fl_parameters,
        hard_refresh,
        search_parameter,
        fq_parameters
    ]
    request_url = f&#34;{self._CLIENTGROUPS}?&#34; + &#34;&#34;.join(params)
    flag, response = self._commcell_object._cvpysdk_object.make_request(&#34;GET&#34;, request_url)
    if not flag:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    client_group_cache = {}
    if response.json() and &#39;groups&#39; in response.json():
        self.filter_query_count = response.json().get(&#39;filterQueryCount&#39;,0)
        for group in response.json()[&#39;groups&#39;]:
            name = group.get(&#39;name&#39;)
            client_group_config = {
                &#39;name&#39;: name,
                &#39;id&#39;: group.get(&#39;Id&#39;),
                &#39;association&#39;: group.get(&#39;groupAssocType&#39;),
            }
            if &#39;clientGroup&#39; in group:
                if &#39;companyName&#39; in group.get(&#39;clientGroup&#39;, {}).get(&#39;entityInfo&#39;, {}):
                    client_group_config[&#39;companyName&#39;] = group.get(&#39;clientGroup&#39;, {}).get(&#39;entityInfo&#39;, {}).get(
                        &#39;companyName&#39;)
                if &#39;tags&#39; in group.get(&#39;clientGroup&#39;, {}):
                    client_group_config[&#39;tags&#39;] = group.get(&#39;clientGroup&#39;, {}).get(&#39;tags&#39;)
            client_group_cache[name] = client_group_config

        return client_group_cache
    else:
        raise SDKException(&#39;Response&#39;, &#39;102&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroups.has_clientgroup"><code class="name flex">
<span>def <span class="ident">has_clientgroup</span></span>(<span>self, clientgroup_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks if a client group exists in the commcell with the input client group name.</p>
<h2 id="args">Args</h2>
<p>clientgroup_name (str)
&ndash;
name of the client group</p>
<h2 id="returns">Returns</h2>
<p>bool - boolean output whether the client group exists in the commcell or not</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the client group name argument is not string</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L548-L564" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_clientgroup(self, clientgroup_name):
    &#34;&#34;&#34;Checks if a client group exists in the commcell with the input client group name.

        Args:
            clientgroup_name (str)  --  name of the client group

        Returns:
            bool - boolean output whether the client group exists in the commcell or not

        Raises:
            SDKException:
                if type of the client group name argument is not string
    &#34;&#34;&#34;
    if not isinstance(clientgroup_name, str):
        raise SDKException(&#39;ClientGroup&#39;, &#39;101&#39;)

    return self._clientgroups and clientgroup_name.lower() in self._clientgroups</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroups.merge_smart_rules"><code class="name flex">
<span>def <span class="ident">merge_smart_rules</span></span>(<span>self, rule_list, op_value='all', scg_op='all')</span>
</code></dt>
<dd>
<div class="desc"><p>Merge multiple rules into (SCG) rule to create smart client group.</p>
<h2 id="args">Args</h2>
<p>rule_list (list)
&ndash;
List of smart rules to be added in rule group</p>
<dl>
<dt>op_value (str)&ndash;
condition to apply between smart rules</dt>
<dt><strong><code>ex</code></strong></dt>
<dd>all, any,not any</dd>
</dl>
<p>scg_op (str)&ndash;
condition to apply between smart rule groups (@group level)</p>
<h2 id="returns">Returns</h2>
<p>scg_rule (dict)
-
Rule group to create smart client group</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L770-L808" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def merge_smart_rules(self, rule_list, op_value=&#39;all&#39;, scg_op=&#39;all&#39;):
    &#34;&#34;&#34;Merge multiple rules into (SCG) rule to create smart client group.

        Args:
            rule_list (list)  --  List of smart rules to be added in rule group

            op_value (str)--     condition to apply between smart rules
            ex: all, any,not any

            scg_op (str)--       condition to apply between smart rule groups (@group level)

        Returns:
           scg_rule (dict)    -   Rule group to create smart client group

    &#34;&#34;&#34;

    op_dict = {
        &#39;all&#39;: 0,
        &#39;any&#39;: 1,
        &#39;not any&#39;: 2
    }
    scg_rule = {
        &#34;op&#34;: op_dict[scg_op],
        &#34;rules&#34;: [
        ]
    }
    rules_dict = {
        &#34;rule&#34;: {
            &#34;op&#34;: op_dict[op_value],
            &#34;rules&#34;: [
            ]
        }
    }

    for each_rule in rule_list:
        rules_dict[&#34;rule&#34;][&#34;rules&#34;].append(each_rule)

    scg_rule[&#34;rules&#34;].append(rules_dict)
    return scg_rule</code></pre>
</details>
</dd>
<dt id="cvpysdk.clientgroup.ClientGroups.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the list of client groups on this commcell.</p>
<pre><code>Args:
    **kwargs (dict):
        mongodb (bool)  -- Flag to fetch client groups cache from MongoDB (default: False).
        hard (bool)     -- Flag to hard refresh MongoDB cache for this entity (default: False).
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/clientgroup.py#L1097-L1111" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self, **kwargs):
    &#34;&#34;&#34;
    Refresh the list of client groups on this commcell.

        Args:
            **kwargs (dict):
                mongodb (bool)  -- Flag to fetch client groups cache from MongoDB (default: False).
                hard (bool)     -- Flag to hard refresh MongoDB cache for this entity (default: False).
    &#34;&#34;&#34;
    mongodb = kwargs.get(&#39;mongodb&#39;, False)
    hard = kwargs.get(&#39;hard&#39;, False)

    self._clientgroups = self._get_clientgroups()
    if mongodb:
        self._clientgroups_cache = self.get_client_groups_cache(hard=hard)</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#clientgroups">ClientGroups:</a><ul>
<li><a href="#clientgroups-attributes">ClientGroups Attributes</a></li>
</ul>
</li>
<li><a href="#clientgroup">ClientGroup:</a><ul>
<li><a href="#clientgroup-attributes">ClientGroup Attributes</a></li>
</ul>
</li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.clientgroup.ClientGroup" href="#cvpysdk.clientgroup.ClientGroup">ClientGroup</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.clientgroup.ClientGroup.add_additional_setting" href="#cvpysdk.clientgroup.ClientGroup.add_additional_setting">add_additional_setting</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroup.add_clients" href="#cvpysdk.clientgroup.ClientGroup.add_clients">add_clients</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroup.associated_clients" href="#cvpysdk.clientgroup.ClientGroup.associated_clients">associated_clients</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroup.change_company" href="#cvpysdk.clientgroup.ClientGroup.change_company">change_company</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroup.client_group_filter" href="#cvpysdk.clientgroup.ClientGroup.client_group_filter">client_group_filter</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroup.clientgroup_id" href="#cvpysdk.clientgroup.ClientGroup.clientgroup_id">clientgroup_id</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroup.clientgroup_name" href="#cvpysdk.clientgroup.ClientGroup.clientgroup_name">clientgroup_name</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroup.company_name" href="#cvpysdk.clientgroup.ClientGroup.company_name">company_name</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroup.delete_additional_setting" href="#cvpysdk.clientgroup.ClientGroup.delete_additional_setting">delete_additional_setting</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroup.description" href="#cvpysdk.clientgroup.ClientGroup.description">description</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroup.disable_auto_discover" href="#cvpysdk.clientgroup.ClientGroup.disable_auto_discover">disable_auto_discover</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroup.disable_backup" href="#cvpysdk.clientgroup.ClientGroup.disable_backup">disable_backup</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroup.disable_data_aging" href="#cvpysdk.clientgroup.ClientGroup.disable_data_aging">disable_data_aging</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroup.disable_restore" href="#cvpysdk.clientgroup.ClientGroup.disable_restore">disable_restore</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroup.enable_auto_discover" href="#cvpysdk.clientgroup.ClientGroup.enable_auto_discover">enable_auto_discover</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroup.enable_backup" href="#cvpysdk.clientgroup.ClientGroup.enable_backup">enable_backup</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroup.enable_backup_at_time" href="#cvpysdk.clientgroup.ClientGroup.enable_backup_at_time">enable_backup_at_time</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroup.enable_data_aging" href="#cvpysdk.clientgroup.ClientGroup.enable_data_aging">enable_data_aging</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroup.enable_data_aging_at_time" href="#cvpysdk.clientgroup.ClientGroup.enable_data_aging_at_time">enable_data_aging_at_time</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroup.enable_restore" href="#cvpysdk.clientgroup.ClientGroup.enable_restore">enable_restore</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroup.enable_restore_at_time" href="#cvpysdk.clientgroup.ClientGroup.enable_restore_at_time">enable_restore_at_time</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroup.is_auto_discover_enabled" href="#cvpysdk.clientgroup.ClientGroup.is_auto_discover_enabled">is_auto_discover_enabled</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroup.is_backup_enabled" href="#cvpysdk.clientgroup.ClientGroup.is_backup_enabled">is_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroup.is_data_aging_enabled" href="#cvpysdk.clientgroup.ClientGroup.is_data_aging_enabled">is_data_aging_enabled</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroup.is_restore_enabled" href="#cvpysdk.clientgroup.ClientGroup.is_restore_enabled">is_restore_enabled</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroup.is_smart_client_group" href="#cvpysdk.clientgroup.ClientGroup.is_smart_client_group">is_smart_client_group</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroup.name" href="#cvpysdk.clientgroup.ClientGroup.name">name</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroup.network" href="#cvpysdk.clientgroup.ClientGroup.network">network</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroup.network_throttle" href="#cvpysdk.clientgroup.ClientGroup.network_throttle">network_throttle</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroup.properties" href="#cvpysdk.clientgroup.ClientGroup.properties">properties</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroup.push_network_config" href="#cvpysdk.clientgroup.ClientGroup.push_network_config">push_network_config</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroup.push_servicepack_and_hotfix" href="#cvpysdk.clientgroup.ClientGroup.push_servicepack_and_hotfix">push_servicepack_and_hotfix</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroup.refresh" href="#cvpysdk.clientgroup.ClientGroup.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroup.refresh_clients" href="#cvpysdk.clientgroup.ClientGroup.refresh_clients">refresh_clients</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroup.remove_all_clients" href="#cvpysdk.clientgroup.ClientGroup.remove_all_clients">remove_all_clients</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroup.remove_clients" href="#cvpysdk.clientgroup.ClientGroup.remove_clients">remove_clients</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroup.repair_software" href="#cvpysdk.clientgroup.ClientGroup.repair_software">repair_software</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroup.update_properties" href="#cvpysdk.clientgroup.ClientGroup.update_properties">update_properties</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.clientgroup.ClientGroups" href="#cvpysdk.clientgroup.ClientGroups">ClientGroups</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.clientgroup.ClientGroups.add" href="#cvpysdk.clientgroup.ClientGroups.add">add</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroups.all_client_groups_prop" href="#cvpysdk.clientgroup.ClientGroups.all_client_groups_prop">all_client_groups_prop</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroups.all_clientgroups" href="#cvpysdk.clientgroup.ClientGroups.all_clientgroups">all_clientgroups</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroups.all_clientgroups_cache" href="#cvpysdk.clientgroup.ClientGroups.all_clientgroups_cache">all_clientgroups_cache</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroups.create_smart_rule" href="#cvpysdk.clientgroup.ClientGroups.create_smart_rule">create_smart_rule</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroups.delete" href="#cvpysdk.clientgroup.ClientGroups.delete">delete</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroups.get" href="#cvpysdk.clientgroup.ClientGroups.get">get</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroups.get_client_groups_cache" href="#cvpysdk.clientgroup.ClientGroups.get_client_groups_cache">get_client_groups_cache</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroups.has_clientgroup" href="#cvpysdk.clientgroup.ClientGroups.has_clientgroup">has_clientgroup</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroups.merge_smart_rules" href="#cvpysdk.clientgroup.ClientGroups.merge_smart_rules">merge_smart_rules</a></code></li>
<li><code><a title="cvpysdk.clientgroup.ClientGroups.refresh" href="#cvpysdk.clientgroup.ClientGroups.refresh">refresh</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>