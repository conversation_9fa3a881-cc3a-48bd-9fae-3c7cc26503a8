<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.subclients.db2subclient API documentation</title>
<meta name="description" content="File for operating on a Db2 Subclient …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.subclients.db2subclient</code></h1>
</header>
<section id="section-intro">
<p>File for operating on a Db2 Subclient</p>
<p>DB2Subclient is the only class defined in this file.</p>
<p>DB2Subclient: Derived class from Subclient Base class, representing an db2 subclient,
and to perform operations on that subclient</p>
<h2 id="db2subclient">Db2Subclient</h2>
<p><strong>init</strong>()
&ndash;
constructor for the class</p>
<p>_get_subclient_properties()
&ndash;
gets the subclient related properties of
db2 subclient</p>
<p>_get_subclient_properties_json()
&ndash; gets subclient property json for db2</p>
<p>db2_use_dedupe_device()
&ndash; getter and setter for enabling dedupe device option for db2</p>
<p>db2_delete_log_files_after()
&ndash; getter and setter for enabling delete log files after option in db2</p>
<p>db2_backup_log_files()
&ndash; getter and setter for enabling backup log files option for db2</p>
<p>db2_delete_log_files_after()
&ndash; getter and setter for enabling delete log file after option for db2</p>
<p>is_backup_data_enabled()
&ndash; getter and setter for enabling backup data option</p>
<p>enable_backupdata()
&ndash; Method to enable backup data option at subclient level</p>
<p>disable_backupdata()
&ndash; Method to disable backup data option at subclient level</p>
<p>enable_table_level()
&ndash; Enable Table Level Browse</p>
<p>enable_acs_backup()
&ndash; Enable DB2 ACS snap backup</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/db2subclient.py#L1-L353" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;
File for operating on a Db2 Subclient

DB2Subclient is the only class defined in this file.

DB2Subclient: Derived class from Subclient Base class, representing an db2 subclient,
                        and to perform operations on that subclient

Db2Subclient:
    __init__()                          --  constructor for the class

    _get_subclient_properties()         --  gets the subclient related properties of
                                            db2 subclient

    _get_subclient_properties_json()    -- gets subclient property json for db2

    db2_use_dedupe_device()             -- getter and setter for enabling dedupe device option for db2

    db2_delete_log_files_after()        -- getter and setter for enabling delete log files after option in db2

    db2_backup_log_files()              -- getter and setter for enabling backup log files option for db2

    db2_delete_log_files_after()        -- getter and setter for enabling delete log file after option for db2

    is_backup_data_enabled()            -- getter and setter for enabling backup data option

    enable_backupdata()                 -- Method to enable backup data option at subclient level

    disable_backupdata()                -- Method to disable backup data option at subclient level

    enable_table_level()                -- Enable Table Level Browse

    enable_acs_backup()                 -- Enable DB2 ACS snap backup

&#34;&#34;&#34;
from __future__ import unicode_literals
from ..subclient import Subclient
from ..exception import SDKException


class DB2Subclient(Subclient):
    &#34;&#34;&#34;
        DB2Subclient is a class to work on DB2 subclients

    &#34;&#34;&#34;

    def __init__(self, backupset_object, subclient_name, subclient_id=None):
        &#34;&#34;&#34;
        Constructor for the class

        Args:
            backupset_object  (object)  -- instance of the Backupset class

            subclient_name    (str)     -- name of the subclient

            subclient_id      (str)     -- id of the subclient

        &#34;&#34;&#34;
        super(DB2Subclient, self).__init__(
            backupset_object, subclient_name, subclient_id)
        self._db2_subclient_properties = {}
        self._db2_backup_logfiles = {}
        self._db2_delete_logfiles_after = {}

    @property
    def db2_use_dedupe_device(self):
        &#34;&#34;&#34;
            Getter to fetch dedupe device option
            Returns:
             Bool - True if dedupe_device is enabled on the subclient. Else False

        &#34;&#34;&#34;
        return self._properties.get(&#39;db2SubclientProp&#39;, {}).get(&#39;db2UseDedupeDevice&#39;)

    @property
    def db2_delete_log_files_after(self):
        &#34;&#34;&#34;
        Getter to fetch status of delete log files option
        Returns:
        Bool - True if delete log files option is enabled on the subclient. Else False

        &#34;&#34;&#34;
        return self._subclient_properties.get(&#39;db2SubclientProp&#39;, {}).get(&#39;db2DeleteLogFilesAfter&#39;)

    @property
    def db2_backup_log_files(self):
        &#34;&#34;&#34;
        Getter to fetch backup logfiles option is enabled or not
        Returns:
        Bool - True if delete log files option is enabled on the subclient. Else False

        &#34;&#34;&#34;
        return self._subclient_properties.get(&#39;db2SubclientProp&#39;, {}).get(&#39;db2BackupLogFiles&#39;)

    @db2_backup_log_files.setter
    def db2_backup_log_files(self, value):
        &#34;&#34;&#34;
        To enable or disable log backup option
        Args:

            value   (bool)      --  to enable or disable log backup option for db2 subclient
        &#34;&#34;&#34;

        self._set_subclient_properties(
            &#34;_db2_subclient_properties[&#39;db2BackupLogFiles&#39;]&#34;, value)


    @db2_delete_log_files_after.setter
    def db2_delete_log_files_after(self, value):
        &#34;&#34;&#34;
        To enable or disable log backup option
        Args:

            value   (bool)      --  to enable or disable log backup option for db2 subclient
        &#34;&#34;&#34;

        self._set_subclient_properties(
            &#34;_db2_subclient_properties[&#39;db2DeleteLogFilesAfter&#39;]&#34;, value)

    @property
    def is_backup_data_enabled(self):
        &#34;&#34;&#34;
        Getter to fetch data backup status is enabled or disabled

        Returns:

            (bool)      -   boolean value is returned based on the status of data backup option

        &#34;&#34;&#34;

        return self._subclient_properties.get(&#34;db2SubclientProp&#34;, {}).get(&#39;db2BackupData&#39;, True)

    def enable_backupdata(self):
        &#34;&#34;&#34;
        To enable data backup

        &#34;&#34;&#34;

        self._set_subclient_properties(&#34;_db2_subclient_properties[&#39;db2BackupData&#39;]&#34;, True)

    def disable_backupdata(self):
        &#34;&#34;&#34;
        To disable data backup

        &#34;&#34;&#34;
        properties = self.properties
        properties[&#39;db2SubclientProp&#39;][&#34;db2BackupData&#34;] = False
        properties[&#39;db2SubclientProp&#39;][&#34;skipLogsInBackupImage&#34;] = 0
        properties[&#39;db2SubclientProp&#39;][&#34;db2BackupMode&#34;] = 0
        properties[&#39;db2SubclientProp&#39;][&#34;db2UseDedupeDevice&#34;] = True
        properties[&#39;db2SubclientProp&#39;][&#34;db2DeleteLogFilesAfter&#34;] = False
        properties[&#39;db2SubclientProp&#39;][&#34;db2BackupLogFiles&#34;] = True
        del properties[&#34;db2SubclientProp&#34;][&#34;db2BackupType&#34;]
        self.update_properties(properties_dict=properties)

    def enable_table_level(self):
        &#34;&#34;&#34;
        To enable table level browse

        &#34;&#34;&#34;
        properties = self.properties
        properties[&#39;db2SubclientProp&#39;][&#34;enableTableBrowse&#34;] = True
        self.update_properties(properties_dict=properties)

    def enable_acs_backup(self):
        &#34;&#34;&#34;
        To enable DB2 ACS backup
        &#34;&#34;&#34;
        properties = self.properties
        properties[&#39;commonProperties&#39;][&#34;snapCopyInfo&#34;][&#34;useDB2ACSInterface&#34;] = True
        self.update_properties(properties_dict=properties)

    @property
    def backup_mode_online(self):
        &#34;&#34;&#34;
                Getter to fetch online backup mode is enabled or disabled

                Returns:

                    (bool)      -   boolean value is returned based on the status of data backup option
                                    0 - online database , 1 - offline database
                &#34;&#34;&#34;

        return self._subclient_properties.get(&#34;db2SubclientProp&#34;, {}).get(&#39;db2BackupMode&#39;, 0)

    @backup_mode_online.setter
    def backup_mode_online(self, value):
        &#34;&#34;&#34;
        To enable or disable online backup mode

        Args:
            value (bool)    - to enable or disable online backup mode for db2 subclient

        &#34;&#34;&#34;
        self._set_subclient_properties(&#34;_db2_subclient_properties[&#39;db2BackupMode&#39;]&#34;, value)

    def _get_subclient_properties(self):
        &#34;&#34;&#34;Gets the subclient properties of this subclient.

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;

        super(DB2Subclient, self)._get_subclient_properties()
        if &#39;db2SubclientProp&#39; not in self._subclient_properties:
            self._subclient_properties[&#39;db2SubclientProp&#39;] = {}
        self._db2_subclient_properties = self._subclient_properties[&#39;db2SubclientProp&#39;]
        self._db2_delete_logfiles_after = self._db2_subclient_properties.get(
            &#39;db2DeleteLogFilesAfter&#39;)
        self._db2_backup_logfile = self._db2_subclient_properties.get(&#39;db2BackupLogFiles&#39;)
        self._subclient_properties.get(&#34;db2SubclientProp&#34;, {}).get(&#39;db2BackupData&#39;)

    def _get_subclient_properties_json(self):
        &#34;&#34;&#34;returns subclient property json for db2
                   Returns:
                        dict - all subclient properties put inside a dict
        &#34;&#34;&#34;
        &#39;&#39;&#39;subclient_json = {
            &#34;subClientProperties&#34;:{
                &#34;db2SubclientProp&#34;:
                    {
                        &#34;db2BackupData&#34;: None
                    }
            }
        }&#39;&#39;&#39;

        subclient_json = {&#34;subClientProperties&#34;:
                          {
                              &#34;commonProperties&#34;: self._commonProperties,
                              &#34;db2SubclientProp&#34;: self._db2_subclient_properties,
                              &#34;proxyClient&#34;: self._proxyClient,
                              &#34;subClientEntity&#34;: self._subClientEntity
                          }
                          }
        return subclient_json

    def _db2_backup_request_json(self,
                                 backup_level,
                                 **kwargs):
        &#34;&#34;&#34;
        Returns the JSON request to pass to the API as per the options selected by the user.

            Args:
               backup_level                     (list)  --  level of backup the user wish to run
                                                            Full / Incremental / Differential

               create_backup_copy_immediately   (bool)  --  Sybase snap job needs
                                                            this backup copy operation
                    default : False

               backup_copy_type                 (int)   --  backup copy job to be launched
                                                            based on below two options
                 default : 2,
                 possible values :
                            1 (USING_STORAGE_POLICY_RULE),
                            2( USING_LATEST_CYCLE)

            Returns:

                (dict) - JSON request to pass to the API

        &#34;&#34;&#34;
        request_json = self._backup_json(backup_level, False, &#34;BEFORE_SYNTH&#34;)
        create_backup_copy_immediately = kwargs.get(&#34;create_backup_copy_immediately&#34;, False)
        backup_copy_type = kwargs.get(&#34;backup_copy_type&#34;, 2)
        db2_options = dict()
        if create_backup_copy_immediately:
            sub_opt = {&#34;dataOpt&#34;:
                       {
                           &#34;createBackupCopyImmediately&#34;: create_backup_copy_immediately,
                           &#34;backupCopyType&#34;: backup_copy_type
                       }
                      }
            db2_options.update(sub_opt)
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;backupOpts&#34;].update(
            db2_options
        )
        return request_json

    def db2_backup(self,
                   backup_level=&#34;full&#34;,
                   **kwargs):
        &#34;&#34;&#34;
        Performs backup on DB2 subclient

        Args:
            backup_level                            (str)   --  Level of backup.
                                                                full|incremental|differential

            create_backup_copy_immediately          (bool)  --  Sybase snap job needs
                                                                this backup copy operation
                    default : False

            backup_copy_type                        (int)   --  backup copy job to be launched
                                                                based on below two options
             default : 2,
             possible values :
                        1 (USING_STORAGE_POLICY_RULE),
                        2( USING_LATEST_CYCLE)

        Returns:
            (object) - instance of Job class

        Raises:
            SDKException:
                if backup level is incorrect

                if response is empty

                if response does not succeed

        &#34;&#34;&#34;
        backup_level = backup_level.lower()

        if backup_level not in [&#39;full&#39;, &#39;incremental&#39;, &#39;differential&#39;]:
            raise SDKException(&#39;Subclient&#39;, &#39;103&#39;)

        create_backup_copy_immediately = kwargs.get(&#34;create_backup_copy_immediately&#34;, False)

        if create_backup_copy_immediately:
            if backup_level != &#39;full&#39;:
                raise SDKException(
                    &#39;Subclient&#39;, &#39;102&#39;, &#39;Backup Copy job is not valid for Incremental or Differential&#39;)

        request_json = self._db2_backup_request_json(backup_level, **kwargs)

        backup_service = self._commcell_object._services[&#39;CREATE_TASK&#39;]

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, backup_service, request_json
        )
        return self._process_backup_response(flag, response)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.subclients.db2subclient.DB2Subclient"><code class="flex name class">
<span>class <span class="ident">DB2Subclient</span></span>
<span>(</span><span>backupset_object, subclient_name, subclient_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>DB2Subclient is a class to work on DB2 subclients</p>
<p>Constructor for the class</p>
<h2 id="args">Args</h2>
<p>backupset_object
(object)
&ndash; instance of the Backupset class</p>
<p>subclient_name
(str)
&ndash; name of the subclient</p>
<p>subclient_id
(str)
&ndash; id of the subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/db2subclient.py#L59-L353" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class DB2Subclient(Subclient):
    &#34;&#34;&#34;
        DB2Subclient is a class to work on DB2 subclients

    &#34;&#34;&#34;

    def __init__(self, backupset_object, subclient_name, subclient_id=None):
        &#34;&#34;&#34;
        Constructor for the class

        Args:
            backupset_object  (object)  -- instance of the Backupset class

            subclient_name    (str)     -- name of the subclient

            subclient_id      (str)     -- id of the subclient

        &#34;&#34;&#34;
        super(DB2Subclient, self).__init__(
            backupset_object, subclient_name, subclient_id)
        self._db2_subclient_properties = {}
        self._db2_backup_logfiles = {}
        self._db2_delete_logfiles_after = {}

    @property
    def db2_use_dedupe_device(self):
        &#34;&#34;&#34;
            Getter to fetch dedupe device option
            Returns:
             Bool - True if dedupe_device is enabled on the subclient. Else False

        &#34;&#34;&#34;
        return self._properties.get(&#39;db2SubclientProp&#39;, {}).get(&#39;db2UseDedupeDevice&#39;)

    @property
    def db2_delete_log_files_after(self):
        &#34;&#34;&#34;
        Getter to fetch status of delete log files option
        Returns:
        Bool - True if delete log files option is enabled on the subclient. Else False

        &#34;&#34;&#34;
        return self._subclient_properties.get(&#39;db2SubclientProp&#39;, {}).get(&#39;db2DeleteLogFilesAfter&#39;)

    @property
    def db2_backup_log_files(self):
        &#34;&#34;&#34;
        Getter to fetch backup logfiles option is enabled or not
        Returns:
        Bool - True if delete log files option is enabled on the subclient. Else False

        &#34;&#34;&#34;
        return self._subclient_properties.get(&#39;db2SubclientProp&#39;, {}).get(&#39;db2BackupLogFiles&#39;)

    @db2_backup_log_files.setter
    def db2_backup_log_files(self, value):
        &#34;&#34;&#34;
        To enable or disable log backup option
        Args:

            value   (bool)      --  to enable or disable log backup option for db2 subclient
        &#34;&#34;&#34;

        self._set_subclient_properties(
            &#34;_db2_subclient_properties[&#39;db2BackupLogFiles&#39;]&#34;, value)


    @db2_delete_log_files_after.setter
    def db2_delete_log_files_after(self, value):
        &#34;&#34;&#34;
        To enable or disable log backup option
        Args:

            value   (bool)      --  to enable or disable log backup option for db2 subclient
        &#34;&#34;&#34;

        self._set_subclient_properties(
            &#34;_db2_subclient_properties[&#39;db2DeleteLogFilesAfter&#39;]&#34;, value)

    @property
    def is_backup_data_enabled(self):
        &#34;&#34;&#34;
        Getter to fetch data backup status is enabled or disabled

        Returns:

            (bool)      -   boolean value is returned based on the status of data backup option

        &#34;&#34;&#34;

        return self._subclient_properties.get(&#34;db2SubclientProp&#34;, {}).get(&#39;db2BackupData&#39;, True)

    def enable_backupdata(self):
        &#34;&#34;&#34;
        To enable data backup

        &#34;&#34;&#34;

        self._set_subclient_properties(&#34;_db2_subclient_properties[&#39;db2BackupData&#39;]&#34;, True)

    def disable_backupdata(self):
        &#34;&#34;&#34;
        To disable data backup

        &#34;&#34;&#34;
        properties = self.properties
        properties[&#39;db2SubclientProp&#39;][&#34;db2BackupData&#34;] = False
        properties[&#39;db2SubclientProp&#39;][&#34;skipLogsInBackupImage&#34;] = 0
        properties[&#39;db2SubclientProp&#39;][&#34;db2BackupMode&#34;] = 0
        properties[&#39;db2SubclientProp&#39;][&#34;db2UseDedupeDevice&#34;] = True
        properties[&#39;db2SubclientProp&#39;][&#34;db2DeleteLogFilesAfter&#34;] = False
        properties[&#39;db2SubclientProp&#39;][&#34;db2BackupLogFiles&#34;] = True
        del properties[&#34;db2SubclientProp&#34;][&#34;db2BackupType&#34;]
        self.update_properties(properties_dict=properties)

    def enable_table_level(self):
        &#34;&#34;&#34;
        To enable table level browse

        &#34;&#34;&#34;
        properties = self.properties
        properties[&#39;db2SubclientProp&#39;][&#34;enableTableBrowse&#34;] = True
        self.update_properties(properties_dict=properties)

    def enable_acs_backup(self):
        &#34;&#34;&#34;
        To enable DB2 ACS backup
        &#34;&#34;&#34;
        properties = self.properties
        properties[&#39;commonProperties&#39;][&#34;snapCopyInfo&#34;][&#34;useDB2ACSInterface&#34;] = True
        self.update_properties(properties_dict=properties)

    @property
    def backup_mode_online(self):
        &#34;&#34;&#34;
                Getter to fetch online backup mode is enabled or disabled

                Returns:

                    (bool)      -   boolean value is returned based on the status of data backup option
                                    0 - online database , 1 - offline database
                &#34;&#34;&#34;

        return self._subclient_properties.get(&#34;db2SubclientProp&#34;, {}).get(&#39;db2BackupMode&#39;, 0)

    @backup_mode_online.setter
    def backup_mode_online(self, value):
        &#34;&#34;&#34;
        To enable or disable online backup mode

        Args:
            value (bool)    - to enable or disable online backup mode for db2 subclient

        &#34;&#34;&#34;
        self._set_subclient_properties(&#34;_db2_subclient_properties[&#39;db2BackupMode&#39;]&#34;, value)

    def _get_subclient_properties(self):
        &#34;&#34;&#34;Gets the subclient properties of this subclient.

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;

        super(DB2Subclient, self)._get_subclient_properties()
        if &#39;db2SubclientProp&#39; not in self._subclient_properties:
            self._subclient_properties[&#39;db2SubclientProp&#39;] = {}
        self._db2_subclient_properties = self._subclient_properties[&#39;db2SubclientProp&#39;]
        self._db2_delete_logfiles_after = self._db2_subclient_properties.get(
            &#39;db2DeleteLogFilesAfter&#39;)
        self._db2_backup_logfile = self._db2_subclient_properties.get(&#39;db2BackupLogFiles&#39;)
        self._subclient_properties.get(&#34;db2SubclientProp&#34;, {}).get(&#39;db2BackupData&#39;)

    def _get_subclient_properties_json(self):
        &#34;&#34;&#34;returns subclient property json for db2
                   Returns:
                        dict - all subclient properties put inside a dict
        &#34;&#34;&#34;
        &#39;&#39;&#39;subclient_json = {
            &#34;subClientProperties&#34;:{
                &#34;db2SubclientProp&#34;:
                    {
                        &#34;db2BackupData&#34;: None
                    }
            }
        }&#39;&#39;&#39;

        subclient_json = {&#34;subClientProperties&#34;:
                          {
                              &#34;commonProperties&#34;: self._commonProperties,
                              &#34;db2SubclientProp&#34;: self._db2_subclient_properties,
                              &#34;proxyClient&#34;: self._proxyClient,
                              &#34;subClientEntity&#34;: self._subClientEntity
                          }
                          }
        return subclient_json

    def _db2_backup_request_json(self,
                                 backup_level,
                                 **kwargs):
        &#34;&#34;&#34;
        Returns the JSON request to pass to the API as per the options selected by the user.

            Args:
               backup_level                     (list)  --  level of backup the user wish to run
                                                            Full / Incremental / Differential

               create_backup_copy_immediately   (bool)  --  Sybase snap job needs
                                                            this backup copy operation
                    default : False

               backup_copy_type                 (int)   --  backup copy job to be launched
                                                            based on below two options
                 default : 2,
                 possible values :
                            1 (USING_STORAGE_POLICY_RULE),
                            2( USING_LATEST_CYCLE)

            Returns:

                (dict) - JSON request to pass to the API

        &#34;&#34;&#34;
        request_json = self._backup_json(backup_level, False, &#34;BEFORE_SYNTH&#34;)
        create_backup_copy_immediately = kwargs.get(&#34;create_backup_copy_immediately&#34;, False)
        backup_copy_type = kwargs.get(&#34;backup_copy_type&#34;, 2)
        db2_options = dict()
        if create_backup_copy_immediately:
            sub_opt = {&#34;dataOpt&#34;:
                       {
                           &#34;createBackupCopyImmediately&#34;: create_backup_copy_immediately,
                           &#34;backupCopyType&#34;: backup_copy_type
                       }
                      }
            db2_options.update(sub_opt)
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;backupOpts&#34;].update(
            db2_options
        )
        return request_json

    def db2_backup(self,
                   backup_level=&#34;full&#34;,
                   **kwargs):
        &#34;&#34;&#34;
        Performs backup on DB2 subclient

        Args:
            backup_level                            (str)   --  Level of backup.
                                                                full|incremental|differential

            create_backup_copy_immediately          (bool)  --  Sybase snap job needs
                                                                this backup copy operation
                    default : False

            backup_copy_type                        (int)   --  backup copy job to be launched
                                                                based on below two options
             default : 2,
             possible values :
                        1 (USING_STORAGE_POLICY_RULE),
                        2( USING_LATEST_CYCLE)

        Returns:
            (object) - instance of Job class

        Raises:
            SDKException:
                if backup level is incorrect

                if response is empty

                if response does not succeed

        &#34;&#34;&#34;
        backup_level = backup_level.lower()

        if backup_level not in [&#39;full&#39;, &#39;incremental&#39;, &#39;differential&#39;]:
            raise SDKException(&#39;Subclient&#39;, &#39;103&#39;)

        create_backup_copy_immediately = kwargs.get(&#34;create_backup_copy_immediately&#34;, False)

        if create_backup_copy_immediately:
            if backup_level != &#39;full&#39;:
                raise SDKException(
                    &#39;Subclient&#39;, &#39;102&#39;, &#39;Backup Copy job is not valid for Incremental or Differential&#39;)

        request_json = self._db2_backup_request_json(backup_level, **kwargs)

        backup_service = self._commcell_object._services[&#39;CREATE_TASK&#39;]

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, backup_service, request_json
        )
        return self._process_backup_response(flag, response)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.subclient.Subclient" href="../subclient.html#cvpysdk.subclient.Subclient">Subclient</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.subclients.db2subclient.DB2Subclient.backup_mode_online"><code class="name">var <span class="ident">backup_mode_online</span></code></dt>
<dd>
<div class="desc"><p>Getter to fetch online backup mode is enabled or disabled</p>
<h2 id="returns">Returns</h2>
<p>(bool)
-
boolean value is returned based on the status of data backup option
0 - online database , 1 - offline database</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/db2subclient.py#L191-L202" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def backup_mode_online(self):
    &#34;&#34;&#34;
            Getter to fetch online backup mode is enabled or disabled

            Returns:

                (bool)      -   boolean value is returned based on the status of data backup option
                                0 - online database , 1 - offline database
            &#34;&#34;&#34;

    return self._subclient_properties.get(&#34;db2SubclientProp&#34;, {}).get(&#39;db2BackupMode&#39;, 0)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.db2subclient.DB2Subclient.db2_backup_log_files"><code class="name">var <span class="ident">db2_backup_log_files</span></code></dt>
<dd>
<div class="desc"><p>Getter to fetch backup logfiles option is enabled or not
Returns:
Bool - True if delete log files option is enabled on the subclient. Else False</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/db2subclient.py#L103-L111" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def db2_backup_log_files(self):
    &#34;&#34;&#34;
    Getter to fetch backup logfiles option is enabled or not
    Returns:
    Bool - True if delete log files option is enabled on the subclient. Else False

    &#34;&#34;&#34;
    return self._subclient_properties.get(&#39;db2SubclientProp&#39;, {}).get(&#39;db2BackupLogFiles&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.db2subclient.DB2Subclient.db2_delete_log_files_after"><code class="name">var <span class="ident">db2_delete_log_files_after</span></code></dt>
<dd>
<div class="desc"><p>Getter to fetch status of delete log files option
Returns:
Bool - True if delete log files option is enabled on the subclient. Else False</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/db2subclient.py#L93-L101" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def db2_delete_log_files_after(self):
    &#34;&#34;&#34;
    Getter to fetch status of delete log files option
    Returns:
    Bool - True if delete log files option is enabled on the subclient. Else False

    &#34;&#34;&#34;
    return self._subclient_properties.get(&#39;db2SubclientProp&#39;, {}).get(&#39;db2DeleteLogFilesAfter&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.db2subclient.DB2Subclient.db2_use_dedupe_device"><code class="name">var <span class="ident">db2_use_dedupe_device</span></code></dt>
<dd>
<div class="desc"><p>Getter to fetch dedupe device option
Returns:
Bool - True if dedupe_device is enabled on the subclient. Else False</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/db2subclient.py#L83-L91" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def db2_use_dedupe_device(self):
    &#34;&#34;&#34;
        Getter to fetch dedupe device option
        Returns:
         Bool - True if dedupe_device is enabled on the subclient. Else False

    &#34;&#34;&#34;
    return self._properties.get(&#39;db2SubclientProp&#39;, {}).get(&#39;db2UseDedupeDevice&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.db2subclient.DB2Subclient.is_backup_data_enabled"><code class="name">var <span class="ident">is_backup_data_enabled</span></code></dt>
<dd>
<div class="desc"><p>Getter to fetch data backup status is enabled or disabled</p>
<h2 id="returns">Returns</h2>
<p>(bool)
-
boolean value is returned based on the status of data backup option</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/db2subclient.py#L138-L149" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_backup_data_enabled(self):
    &#34;&#34;&#34;
    Getter to fetch data backup status is enabled or disabled

    Returns:

        (bool)      -   boolean value is returned based on the status of data backup option

    &#34;&#34;&#34;

    return self._subclient_properties.get(&#34;db2SubclientProp&#34;, {}).get(&#39;db2BackupData&#39;, True)</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.subclients.db2subclient.DB2Subclient.db2_backup"><code class="name flex">
<span>def <span class="ident">db2_backup</span></span>(<span>self, backup_level='full', **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Performs backup on DB2 subclient</p>
<h2 id="args">Args</h2>
<p>backup_level
(str)
&ndash;
Level of backup.
full|incremental|differential</p>
<p>create_backup_copy_immediately
(bool)
&ndash;
Sybase snap job needs
this backup copy operation
default : False</p>
<p>backup_copy_type
(int)
&ndash;
backup copy job to be launched
based on below two options
default : 2,
possible values :
1 (USING_STORAGE_POLICY_RULE),
2( USING_LATEST_CYCLE)</p>
<h2 id="returns">Returns</h2>
<p>(object) - instance of Job class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if backup level is incorrect</p>
<pre><code>if response is empty

if response does not succeed
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/db2subclient.py#L301-L353" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def db2_backup(self,
               backup_level=&#34;full&#34;,
               **kwargs):
    &#34;&#34;&#34;
    Performs backup on DB2 subclient

    Args:
        backup_level                            (str)   --  Level of backup.
                                                            full|incremental|differential

        create_backup_copy_immediately          (bool)  --  Sybase snap job needs
                                                            this backup copy operation
                default : False

        backup_copy_type                        (int)   --  backup copy job to be launched
                                                            based on below two options
         default : 2,
         possible values :
                    1 (USING_STORAGE_POLICY_RULE),
                    2( USING_LATEST_CYCLE)

    Returns:
        (object) - instance of Job class

    Raises:
        SDKException:
            if backup level is incorrect

            if response is empty

            if response does not succeed

    &#34;&#34;&#34;
    backup_level = backup_level.lower()

    if backup_level not in [&#39;full&#39;, &#39;incremental&#39;, &#39;differential&#39;]:
        raise SDKException(&#39;Subclient&#39;, &#39;103&#39;)

    create_backup_copy_immediately = kwargs.get(&#34;create_backup_copy_immediately&#34;, False)

    if create_backup_copy_immediately:
        if backup_level != &#39;full&#39;:
            raise SDKException(
                &#39;Subclient&#39;, &#39;102&#39;, &#39;Backup Copy job is not valid for Incremental or Differential&#39;)

    request_json = self._db2_backup_request_json(backup_level, **kwargs)

    backup_service = self._commcell_object._services[&#39;CREATE_TASK&#39;]

    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, backup_service, request_json
    )
    return self._process_backup_response(flag, response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.db2subclient.DB2Subclient.disable_backupdata"><code class="name flex">
<span>def <span class="ident">disable_backupdata</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>To disable data backup</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/db2subclient.py#L159-L172" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def disable_backupdata(self):
    &#34;&#34;&#34;
    To disable data backup

    &#34;&#34;&#34;
    properties = self.properties
    properties[&#39;db2SubclientProp&#39;][&#34;db2BackupData&#34;] = False
    properties[&#39;db2SubclientProp&#39;][&#34;skipLogsInBackupImage&#34;] = 0
    properties[&#39;db2SubclientProp&#39;][&#34;db2BackupMode&#34;] = 0
    properties[&#39;db2SubclientProp&#39;][&#34;db2UseDedupeDevice&#34;] = True
    properties[&#39;db2SubclientProp&#39;][&#34;db2DeleteLogFilesAfter&#34;] = False
    properties[&#39;db2SubclientProp&#39;][&#34;db2BackupLogFiles&#34;] = True
    del properties[&#34;db2SubclientProp&#34;][&#34;db2BackupType&#34;]
    self.update_properties(properties_dict=properties)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.db2subclient.DB2Subclient.enable_acs_backup"><code class="name flex">
<span>def <span class="ident">enable_acs_backup</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>To enable DB2 ACS backup</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/db2subclient.py#L183-L189" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable_acs_backup(self):
    &#34;&#34;&#34;
    To enable DB2 ACS backup
    &#34;&#34;&#34;
    properties = self.properties
    properties[&#39;commonProperties&#39;][&#34;snapCopyInfo&#34;][&#34;useDB2ACSInterface&#34;] = True
    self.update_properties(properties_dict=properties)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.db2subclient.DB2Subclient.enable_backupdata"><code class="name flex">
<span>def <span class="ident">enable_backupdata</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>To enable data backup</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/db2subclient.py#L151-L157" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable_backupdata(self):
    &#34;&#34;&#34;
    To enable data backup

    &#34;&#34;&#34;

    self._set_subclient_properties(&#34;_db2_subclient_properties[&#39;db2BackupData&#39;]&#34;, True)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.db2subclient.DB2Subclient.enable_table_level"><code class="name flex">
<span>def <span class="ident">enable_table_level</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>To enable table level browse</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/db2subclient.py#L174-L181" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable_table_level(self):
    &#34;&#34;&#34;
    To enable table level browse

    &#34;&#34;&#34;
    properties = self.properties
    properties[&#39;db2SubclientProp&#39;][&#34;enableTableBrowse&#34;] = True
    self.update_properties(properties_dict=properties)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.subclient.Subclient" href="../subclient.html#cvpysdk.subclient.Subclient">Subclient</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.subclient.Subclient.allow_multiple_readers" href="../subclient.html#cvpysdk.subclient.Subclient.allow_multiple_readers">allow_multiple_readers</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.backup" href="../subclient.html#cvpysdk.subclient.Subclient.backup">backup</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.browse" href="../subclient.html#cvpysdk.subclient.Subclient.browse">browse</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.data_readers" href="../subclient.html#cvpysdk.subclient.Subclient.data_readers">data_readers</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.deduplication_options" href="../subclient.html#cvpysdk.subclient.Subclient.deduplication_options">deduplication_options</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.description" href="../subclient.html#cvpysdk.subclient.Subclient.description">description</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.disable_backup" href="../subclient.html#cvpysdk.subclient.Subclient.disable_backup">disable_backup</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.disable_intelli_snap" href="../subclient.html#cvpysdk.subclient.Subclient.disable_intelli_snap">disable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.display_name" href="../subclient.html#cvpysdk.subclient.Subclient.display_name">display_name</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_backup" href="../subclient.html#cvpysdk.subclient.Subclient.enable_backup">enable_backup</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_backup_at_time" href="../subclient.html#cvpysdk.subclient.Subclient.enable_backup_at_time">enable_backup_at_time</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_intelli_snap" href="../subclient.html#cvpysdk.subclient.Subclient.enable_intelli_snap">enable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_trueup" href="../subclient.html#cvpysdk.subclient.Subclient.enable_trueup">enable_trueup</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_trueup_days" href="../subclient.html#cvpysdk.subclient.Subclient.enable_trueup_days">enable_trueup_days</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.encryption_flag" href="../subclient.html#cvpysdk.subclient.Subclient.encryption_flag">encryption_flag</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.exclude_from_sla" href="../subclient.html#cvpysdk.subclient.Subclient.exclude_from_sla">exclude_from_sla</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.find" href="../subclient.html#cvpysdk.subclient.Subclient.find">find</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.find_latest_job" href="../subclient.html#cvpysdk.subclient.Subclient.find_latest_job">find_latest_job</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.get_ma_associated_storagepolicy" href="../subclient.html#cvpysdk.subclient.Subclient.get_ma_associated_storagepolicy">get_ma_associated_storagepolicy</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_backup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_backup_enabled">is_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_blocklevel_backup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_blocklevel_backup_enabled">is_blocklevel_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_default_subclient" href="../subclient.html#cvpysdk.subclient.Subclient.is_default_subclient">is_default_subclient</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_intelli_snap_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_intelli_snap_enabled">is_intelli_snap_enabled</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_on_demand_subclient" href="../subclient.html#cvpysdk.subclient.Subclient.is_on_demand_subclient">is_on_demand_subclient</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_trueup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_trueup_enabled">is_trueup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.last_backup_time" href="../subclient.html#cvpysdk.subclient.Subclient.last_backup_time">last_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.list_media" href="../subclient.html#cvpysdk.subclient.Subclient.list_media">list_media</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.name" href="../subclient.html#cvpysdk.subclient.Subclient.name">name</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.network_agent" href="../subclient.html#cvpysdk.subclient.Subclient.network_agent">network_agent</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.next_backup_time" href="../subclient.html#cvpysdk.subclient.Subclient.next_backup_time">next_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.plan" href="../subclient.html#cvpysdk.subclient.Subclient.plan">plan</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.properties" href="../subclient.html#cvpysdk.subclient.Subclient.properties">properties</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.read_buffer_size" href="../subclient.html#cvpysdk.subclient.Subclient.read_buffer_size">read_buffer_size</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.refresh" href="../subclient.html#cvpysdk.subclient.Subclient.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.restore_in_place" href="../subclient.html#cvpysdk.subclient.Subclient.restore_in_place">restore_in_place</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.restore_out_of_place" href="../subclient.html#cvpysdk.subclient.Subclient.restore_out_of_place">restore_out_of_place</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.run_content_indexing" href="../subclient.html#cvpysdk.subclient.Subclient.run_content_indexing">run_content_indexing</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.set_backup_nodes" href="../subclient.html#cvpysdk.subclient.Subclient.set_backup_nodes">set_backup_nodes</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.set_proxy_for_snap" href="../subclient.html#cvpysdk.subclient.Subclient.set_proxy_for_snap">set_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.snapshot_engine_name" href="../subclient.html#cvpysdk.subclient.Subclient.snapshot_engine_name">snapshot_engine_name</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.software_compression" href="../subclient.html#cvpysdk.subclient.Subclient.software_compression">software_compression</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.storage_ma" href="../subclient.html#cvpysdk.subclient.Subclient.storage_ma">storage_ma</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.storage_ma_id" href="../subclient.html#cvpysdk.subclient.Subclient.storage_ma_id">storage_ma_id</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.storage_policy" href="../subclient.html#cvpysdk.subclient.Subclient.storage_policy">storage_policy</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.subclient_guid" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_guid">subclient_guid</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.subclient_id" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_id">subclient_id</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.subclient_name" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_name">subclient_name</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.unset_proxy_for_snap" href="../subclient.html#cvpysdk.subclient.Subclient.unset_proxy_for_snap">unset_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.update_properties" href="../subclient.html#cvpysdk.subclient.Subclient.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.subclients" href="index.html">cvpysdk.subclients</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.subclients.db2subclient.DB2Subclient" href="#cvpysdk.subclients.db2subclient.DB2Subclient">DB2Subclient</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.subclients.db2subclient.DB2Subclient.backup_mode_online" href="#cvpysdk.subclients.db2subclient.DB2Subclient.backup_mode_online">backup_mode_online</a></code></li>
<li><code><a title="cvpysdk.subclients.db2subclient.DB2Subclient.db2_backup" href="#cvpysdk.subclients.db2subclient.DB2Subclient.db2_backup">db2_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.db2subclient.DB2Subclient.db2_backup_log_files" href="#cvpysdk.subclients.db2subclient.DB2Subclient.db2_backup_log_files">db2_backup_log_files</a></code></li>
<li><code><a title="cvpysdk.subclients.db2subclient.DB2Subclient.db2_delete_log_files_after" href="#cvpysdk.subclients.db2subclient.DB2Subclient.db2_delete_log_files_after">db2_delete_log_files_after</a></code></li>
<li><code><a title="cvpysdk.subclients.db2subclient.DB2Subclient.db2_use_dedupe_device" href="#cvpysdk.subclients.db2subclient.DB2Subclient.db2_use_dedupe_device">db2_use_dedupe_device</a></code></li>
<li><code><a title="cvpysdk.subclients.db2subclient.DB2Subclient.disable_backupdata" href="#cvpysdk.subclients.db2subclient.DB2Subclient.disable_backupdata">disable_backupdata</a></code></li>
<li><code><a title="cvpysdk.subclients.db2subclient.DB2Subclient.enable_acs_backup" href="#cvpysdk.subclients.db2subclient.DB2Subclient.enable_acs_backup">enable_acs_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.db2subclient.DB2Subclient.enable_backupdata" href="#cvpysdk.subclients.db2subclient.DB2Subclient.enable_backupdata">enable_backupdata</a></code></li>
<li><code><a title="cvpysdk.subclients.db2subclient.DB2Subclient.enable_table_level" href="#cvpysdk.subclients.db2subclient.DB2Subclient.enable_table_level">enable_table_level</a></code></li>
<li><code><a title="cvpysdk.subclients.db2subclient.DB2Subclient.is_backup_data_enabled" href="#cvpysdk.subclients.db2subclient.DB2Subclient.is_backup_data_enabled">is_backup_data_enabled</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>