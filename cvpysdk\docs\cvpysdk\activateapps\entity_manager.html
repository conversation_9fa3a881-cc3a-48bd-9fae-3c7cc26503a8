<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.activateapps.entity_manager API documentation</title>
<meta name="description" content="Main file for performing operations on entity manager app under Activate …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.activateapps.entity_manager</code></h1>
</header>
<section id="section-intro">
<p>Main file for performing operations on entity manager app under Activate.</p>
<p>'Classifiers', 'Classifier', 'Tags' , 'TagSet', 'Tag', 'EntityManagerTypes' , <code><a title="cvpysdk.activateapps.entity_manager.ActivateEntities" href="#cvpysdk.activateapps.entity_manager.ActivateEntities">ActivateEntities</a></code>, and <code><a title="cvpysdk.activateapps.entity_manager.ActivateEntity" href="#cvpysdk.activateapps.entity_manager.ActivateEntity">ActivateEntity</a></code> are 8 classes defined in this file.</p>
<p>ActivateEntities:
Class for representing all the regex entities in the commcell.</p>
<p>ActivateEntity:
Class for representing a single regex entity in the commcell.</p>
<p>EntityManagerTypes: Class to represent different entity types in entity manager</p>
<p>Tags:
Class to represent TagSets in the commcell</p>
<p>TagSet: Class to represent single Tagset entity in the commcell</p>
<p>Tag:
Class to represent tag inside a TagSet</p>
<p>Classifiers:
Class to represent Classifiers entities in the commcell</p>
<p>Classifier:
Class to represent a single classifier entity in the commcell</p>
<h2 id="tags">Tags</h2>
<p><strong>init</strong>()
&ndash;
initialise object of the Tags class</p>
<p>_response_not_success()
&ndash;
parses through the exception response, and raises SDKException</p>
<p>refresh()
&ndash;
refresh the TagSet associated with the commcell</p>
<p>_get_tag_sets_from_collections()
&ndash;
gets all the TagSet details from collection response</p>
<p>_get_all_tag_sets()
&ndash;
Returns dict consisting all TagSets associated with commcell</p>
<p>get_properties()
&ndash;
Returns the properties for the given TagSet name</p>
<p>has_tag_set()
&ndash;
Checks whether tagset with given name exists in commcell or not</p>
<p>add()
&ndash;
Creates new TagSet in the commcell</p>
<p>delete()
&ndash;
Deletes the Tagset in the commcell</p>
<p>get()
&ndash;
Returns the TagSet object for given tagset name</p>
<h2 id="tagset">Tagset</h2>
<p><strong>init</strong>()
&ndash;
initialize an object of TagSet Class with the given tagset
name and id</p>
<p>_response_not_success()
&ndash;
parses through the exception response, and raises SDKException</p>
<p>refresh()
&ndash;
refresh the properties of the TagSet</p>
<p>modify()
&ndash;
Modifies the tagset in the commcell</p>
<p>share()
&ndash;
Shares tagset with user or group in the commcell</p>
<p>add_tag()
&ndash;
Creates new tag inside this tagset container in commcell</p>
<p>get()
&ndash;
Returns the Tag class object for given tag name</p>
<p>has_tag()
&ndash;
checks whether given tag exists in tagset or not</p>
<p>get_tag_id()
&ndash;
Returns the tag id for given tag name</p>
<p>_get_tag_set_id()
&ndash;
Gets tag set container id for the given Tagset name</p>
<p>_get_tag_set_properties()
&ndash;
Gets all the details of associated Tagset</p>
<h2 id="tagset-attributes">Tagset Attributes</h2>
<pre><code>**guid**            --  returns container GUID of this tagset

**full_name**       --  returns the full name of tagset container

**comment**         --  returns the comment for this tagset

**owner**           --  returns the owner user name for this tagset

**owner_alias_name** -  returns the owner alias name for this tagset

**tags**            --  returns the tags present in this tagset

**tag_set_id**      --  returns the tagset id
</code></pre>
<h2 id="tag">Tag</h2>
<p><strong>init</strong>()
&ndash;
Initialise object of the Tag class</p>
<p>_response_not_success()
&ndash;
parses through the exception response, and raises SDKException</p>
<p>_get_tag_id()
&ndash;
Returns the tag id of the given tag name</p>
<p>_get_tag_properties()
&ndash;
Returns the properties of Tag</p>
<p>refresh()
&ndash;
refresh the tag details</p>
<p>modify()
&ndash;
modifies the name of the tag</p>
<h2 id="tag-attributes">Tag Attributes</h2>
<pre><code>**guid**            --  returns tag GUID of this tag

**full_name**       --  returns the full name of this tag

**tag_id**          --  returns the id of the tag
</code></pre>
<h2 id="activateentities">Activateentities</h2>
<p><strong>init</strong>(commcell_object)
&ndash;
initialise object of the ActivateEntities class</p>
<p>_response_not_success()
&ndash;
parses through the exception response, and raises SDKException</p>
<p>refresh()
&ndash;
refresh the regex entities associated with the commcell</p>
<p>get()
&ndash;
Returns an instance of ActivateEntity class for the given regex entity name</p>
<p>get_entity_ids()
&ndash;
Returns an list of entity ids for the given regex entity name list</p>
<p>get_entity_keys()
&ndash;
Returns an list of entity keys for the given regex entity name list</p>
<p>get_properties()
&ndash;
Returns the properties for the given regex entity name</p>
<p>_get_all_activate_entities()
&ndash;
Returns dict consisting all regex entities associated with commcell</p>
<p>_get_regex_entity_from_collections()&ndash;
gets all the regex entity details from collection response</p>
<p>has_entity()
&ndash;
Checks whether given regex entity exists in commcell or not</p>
<p>add()
&ndash;
adds the regex entity in the commcell</p>
<p>delete()
&ndash;
deletes the regex entity in the commcell for given entity name</p>
<p>_process_entity_containers()
&ndash; returns the container details for the entity</p>
<h2 id="activateentity">Activateentity</h2>
<p><strong>init</strong>(
commcell_object,
entity_name,
entity_id=None)
&ndash;
initialize an object of ActivateEntity Class with the given regex entity
name and id</p>
<p>_response_not_success()
&ndash;
parses through the exception response, and raises SDKException</p>
<p>refresh()
&ndash;
refresh the properties of the regex entity</p>
<p>_get_entity_id()
&ndash;
Gets entity id for the given regex entity name</p>
<p>_get_entity_properties()
&ndash;
Gets all the details of associated regex entity</p>
<p>modify()
&ndash;
Modifies the entity properties for the associated regex entity</p>
<h2 id="activateentity-attributes">Activateentity Attributes</h2>
<pre><code>**entity_id**         --  returns the id of the regex entity

**entity_key**        --  returns the key of the regex entity

**category_name**     --  returns the category name of the regex entity

**is_enabled**        --  returns the enabled flag of the regex entity

**display_name**      --  returns the display name of the regex entity

**entity_type**       --  returns the type of entity (1- NER 2-RER 3-Derived 4-Classifier)

**entity_xml**        --  returns the entity xml associated with this entity

**container_details** --  returns the container details for this entity
</code></pre>
<h2 id="classifiers">Classifiers</h2>
<p><strong>init</strong>(commcell_object)
&ndash;
initialise object of the Classifiers class</p>
<p>_response_not_success()
&ndash;
parses through the exception response, and raises SDKException</p>
<p>refresh()
&ndash;
refresh the classifiers associated with the commcell</p>
<p>get()
&ndash;
Returns an instance of Classifier class for the given classifier name</p>
<p>get_entity_ids()
&ndash;
Returns an list of entity ids for the given classifier name list</p>
<p>get_entity_keys()
&ndash;
Returns an list of entity keys for the given classifier name list</p>
<p>get_properties()
&ndash;
Returns the properties for the given classifier name</p>
<p>_get_all_classifier_entities()
&ndash;
Returns dict consisting all classifier associated with commcell</p>
<p>_get_classifier_entity_from_collections()&ndash; gets all the classifier details from collection response</p>
<p>has_classifier()
&ndash;
Checks whether given classifier exists in commcell or not</p>
<p>add()
&ndash;
adds the classifier in the commcell</p>
<p>delete()
&ndash;
deletes the classifier in the commcell</p>
<h2 id="classifier">Classifier</h2>
<p><strong>init</strong>(
commcell_object,
classifier_name,
entity_id=None)
&ndash;
initialize an object of Classifier Class with the given classifier
name and id</p>
<p>_response_not_success()
&ndash;
parses through the exception response, and raises SDKException</p>
<p>refresh()
&ndash;
refresh the properties of the classifier</p>
<p>_get_entity_id()
&ndash;
Gets entity id for the given classifier name</p>
<p>_get_entity_properties()
&ndash;
Gets all the properties of associated classifier</p>
<p>_get_upload_request_id()
&ndash;
returns the request id for uploading model data</p>
<p>_get_upload_api()
&ndash;
returns the upload API url</p>
<p>_validate_upload_response()
&ndash;
Validates the upload api response for given chunk size</p>
<p>_get_upload_flag_bit()
&ndash;
Returns the 7-byte flag used in upload API call</p>
<p>modify()
&ndash;
Modifies the properties for the associated classifier</p>
<p>upload_data()
&ndash;
Uploads the training model data for this classifier</p>
<p>start_training()
&ndash;
Starts training for this classifier</p>
<p>monitor_training()
&ndash;
Monitors training status for this classifier</p>
<p>cancel_training()
&ndash;
Cancels training for this classifier</p>
<h2 id="classifier-attributes">Classifier Attributes</h2>
<pre><code>**entity_id**               --  returns the id of the classifier

**entity_key**              --  returns the key of the classifier

**category_name**           --  returns the category name of the classifier

**is_enabled**              --  returns the enabled flag of the classifier

**display_name**            --  returns the display name of the classifier

**entity_type**             --  returns the type of entity (4-Classifier)

**entity_xml**              --  returns the entity xml associated with this classifier

**trained_ca_cloud_id**     -- returns the cloud id of the trained content analyzer

**training_status**         --  returns the training status for this classifier

**sycn_ca_client_id**       --  returns the list of synced CA client id for this classifier

**last_training_time**      --  returns the last training time for this classifier

**training_accuracy**       --  returns the training accuracy for this classifier

**sample_details**          --  returns dict containing model sample count details used for this classifier training
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L1-L2567" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Main file for performing operations on entity manager app under Activate.

&#39;Classifiers&#39;, &#39;Classifier&#39;, &#39;Tags&#39; , &#39;TagSet&#39;, &#39;Tag&#39;, &#39;EntityManagerTypes&#39; , `ActivateEntities`, and `ActivateEntity` are 8 classes defined in this file.

ActivateEntities:   Class for representing all the regex entities in the commcell.

ActivateEntity:     Class for representing a single regex entity in the commcell.

EntityManagerTypes: Class to represent different entity types in entity manager

Tags:   Class to represent TagSets in the commcell

TagSet: Class to represent single Tagset entity in the commcell

Tag:  Class to represent tag inside a TagSet

Classifiers:    Class to represent Classifiers entities in the commcell

Classifier:     Class to represent a single classifier entity in the commcell

Tags:

    __init__()                          --  initialise object of the Tags class

     _response_not_success()            --  parses through the exception response, and raises SDKException

    refresh()                           --  refresh the TagSet associated with the commcell

    _get_tag_sets_from_collections()    --  gets all the TagSet details from collection response

    _get_all_tag_sets()                 --  Returns dict consisting all TagSets associated with commcell

    get_properties()                    --  Returns the properties for the given TagSet name

    has_tag_set()                       --  Checks whether tagset with given name exists in commcell or not

    add()                               --  Creates new TagSet in the commcell

    delete()                            --  Deletes the Tagset in the commcell

    get()                               --  Returns the TagSet object for given tagset name


TagSet:

     __init__()                         --  initialize an object of TagSet Class with the given tagset
                                                name and id

     _response_not_success()            --  parses through the exception response, and raises SDKException

    refresh()                           --  refresh the properties of the TagSet

    modify()                            --  Modifies the tagset in the commcell

    share()                             --  Shares tagset with user or group in the commcell

    add_tag()                           --  Creates new tag inside this tagset container in commcell

    get()                               --  Returns the Tag class object for given tag name

    has_tag()                           --  checks whether given tag exists in tagset or not

    get_tag_id()                        --  Returns the tag id for given tag name

    _get_tag_set_id()                   --  Gets tag set container id for the given Tagset name

    _get_tag_set_properties()           --  Gets all the details of associated Tagset

TagSet Attributes
-----------------

    **guid**            --  returns container GUID of this tagset

    **full_name**       --  returns the full name of tagset container

    **comment**         --  returns the comment for this tagset

    **owner**           --  returns the owner user name for this tagset

    **owner_alias_name** -  returns the owner alias name for this tagset

    **tags**            --  returns the tags present in this tagset

    **tag_set_id**      --  returns the tagset id

Tag:

    __init__()                  --  Initialise object of the Tag class

    _response_not_success()     --  parses through the exception response, and raises SDKException

    _get_tag_id()               --  Returns the tag id of the given tag name

    _get_tag_properties()       --  Returns the properties of Tag

    refresh()                   --  refresh the tag details

    modify()                    --  modifies the name of the tag

Tag Attributes
-----------------

    **guid**            --  returns tag GUID of this tag

    **full_name**       --  returns the full name of this tag

    **tag_id**          --  returns the id of the tag


ActivateEntities:

    __init__(commcell_object)           --  initialise object of the ActivateEntities class

     _response_not_success()            --  parses through the exception response, and raises SDKException

    refresh()                           --  refresh the regex entities associated with the commcell

    get()                               --  Returns an instance of ActivateEntity class for the given regex entity name

    get_entity_ids()                    --  Returns an list of entity ids for the given regex entity name list

    get_entity_keys()                   --  Returns an list of entity keys for the given regex entity name list

    get_properties()                    --  Returns the properties for the given regex entity name

    _get_all_activate_entities()        --  Returns dict consisting all regex entities associated with commcell

    _get_regex_entity_from_collections()--  gets all the regex entity details from collection response

    has_entity()                        --  Checks whether given regex entity exists in commcell or not

    add()                               --  adds the regex entity in the commcell

    delete()                            --  deletes the regex entity in the commcell for given entity name

    _process_entity_containers()        -- returns the container details for the entity

ActivateEntity:

    __init__(
        commcell_object,
        entity_name,
        entity_id=None)             --  initialize an object of ActivateEntity Class with the given regex entity
                                                name and id

     _response_not_success()            --  parses through the exception response, and raises SDKException

    refresh()                           --  refresh the properties of the regex entity

    _get_entity_id()                    --  Gets entity id for the given regex entity name

    _get_entity_properties()            --  Gets all the details of associated regex entity

    modify()                            --  Modifies the entity properties for the associated regex entity


ActivateEntity Attributes
-----------------

    **entity_id**         --  returns the id of the regex entity

    **entity_key**        --  returns the key of the regex entity

    **category_name**     --  returns the category name of the regex entity

    **is_enabled**        --  returns the enabled flag of the regex entity

    **display_name**      --  returns the display name of the regex entity

    **entity_type**       --  returns the type of entity (1- NER 2-RER 3-Derived 4-Classifier)

    **entity_xml**        --  returns the entity xml associated with this entity

    **container_details** --  returns the container details for this entity

Classifiers:

    __init__(commcell_object)           --  initialise object of the Classifiers class

     _response_not_success()            --  parses through the exception response, and raises SDKException

    refresh()                           --  refresh the classifiers associated with the commcell

    get()                               --  Returns an instance of Classifier class for the given classifier name

    get_entity_ids()                    --  Returns an list of entity ids for the given classifier name list

    get_entity_keys()                   --  Returns an list of entity keys for the given classifier name list

    get_properties()                    --  Returns the properties for the given classifier name

    _get_all_classifier_entities()      --  Returns dict consisting all classifier associated with commcell

    _get_classifier_entity_from_collections()-- gets all the classifier details from collection response

    has_classifier()                    --  Checks whether given classifier exists in commcell or not

    add()                               --  adds the classifier in the commcell

    delete()                            --  deletes the classifier in the commcell



Classifier:

    __init__(
        commcell_object,
        classifier_name,
        entity_id=None)             --  initialize an object of Classifier Class with the given classifier
                                                name and id

     _response_not_success()            --  parses through the exception response, and raises SDKException

    refresh()                           --  refresh the properties of the classifier

    _get_entity_id()                    --  Gets entity id for the given classifier name

    _get_entity_properties()            --  Gets all the properties of associated classifier

    _get_upload_request_id()            --  returns the request id for uploading model data

    _get_upload_api()                   --  returns the upload API url

    _validate_upload_response()         --  Validates the upload api response for given chunk size

    _get_upload_flag_bit()              --  Returns the 7-byte flag used in upload API call

    modify()                            --  Modifies the properties for the associated classifier

    upload_data()                       --  Uploads the training model data for this classifier

    start_training()                    --  Starts training for this classifier

    monitor_training()                  --  Monitors training status for this classifier

    cancel_training()                   --  Cancels training for this classifier


Classifier Attributes
-----------------

    **entity_id**               --  returns the id of the classifier

    **entity_key**              --  returns the key of the classifier

    **category_name**           --  returns the category name of the classifier

    **is_enabled**              --  returns the enabled flag of the classifier

    **display_name**            --  returns the display name of the classifier

    **entity_type**             --  returns the type of entity (4-Classifier)

    **entity_xml**              --  returns the entity xml associated with this classifier

    **trained_ca_cloud_id**     -- returns the cloud id of the trained content analyzer

    **training_status**         --  returns the training status for this classifier

    **sycn_ca_client_id**       --  returns the list of synced CA client id for this classifier

    **last_training_time**      --  returns the last training time for this classifier

    **training_accuracy**       --  returns the training accuracy for this classifier

    **sample_details**          --  returns dict containing model sample count details used for this classifier training

&#34;&#34;&#34;
import copy
import os
import time
from enum import Enum

from ..exception import SDKException
from .constants import ActivateEntityConstants, ClassifierConstants, TrainingStatus
from .constants import TagConstants


class EntityManagerTypes(Enum):
    &#34;&#34;&#34;Class to represent different entity types in entity manager&#34;&#34;&#34;
    ENTITIES = &#34;Entities&#34;
    CLASSIFIERS = &#34;Classifiers&#34;
    TAGS = &#34;Tags&#34;


class ActivateEntities(object):
    &#34;&#34;&#34;Class for representing all the regex entities in the commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initializes an instance of the ActivateEntities class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

            Returns:
                object  -   instance of the ActivateEntities class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._regex_entities = None
        self._entities_containers = None
        self._api_get_all_regex_entities = self._services[&#39;ACTIVATE_ENTITIES&#39;]
        self._api_get_containers = self._services[&#39;ACTIVATE_ENTITY_CONTAINER&#39;]
        self._api_create_regex_entity = self._api_get_all_regex_entities
        self._api_delete_regex_entity = self._services[&#39;ACTIVATE_ENTITY&#39;]
        self.refresh()

    def add(self, entity_name, entity_regex, entity_keywords, entity_flag, is_derived=False, parent_entity=None):
        &#34;&#34;&#34;Adds the specified regex entity name in the commcell

                    Args:
                        entity_name (str)      --  name of the regex entity

                        entity_regex (str)     --  Regex for the entity

                        entity_keywords (str)  --  Keywords for the entity

                        entity_flag (int)      --  Sensitivity flag value for entity
                                                        5-Highly sensitive
                                                        3-Moderate sensitive
                                                        1-Low sensitive

                        is_derived (bool)      --  represents whether it is derived entity or not

                        parent_entity(int)     -- entity id of the parent entity in case of derived entity

                    Returns:
                        None

                    Raises:
                        SDKException:

                                if response is empty

                                if response is not success

                                if unable to add regex entity in commcell

                                if entity_flag is not in proper allowed values [1,3,5]

                                if input data type is not valid
                &#34;&#34;&#34;
        if not isinstance(entity_name, str) or not isinstance(entity_regex, str) \
                or not isinstance(entity_keywords, str):
            raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)
        if entity_flag not in [1, 3, 5]:
            raise SDKException(&#39;ActivateEntity&#39;, &#39;102&#39;, &#39;Unsupported entity flag value&#39;)
        request_json = ActivateEntityConstants.REQUEST_JSON
        request_json[&#39;regularExpression&#39;] = &#34;{\&#34;entity_key\&#34;:\&#34;&#34; + \
            entity_name + &#34;\&#34;,\&#34;entity_regex\&#34;:\&#34;&#34; + entity_regex + &#34;\&#34;}&#34;
        request_json[&#39;flags&#39;] = entity_flag
        request_json[&#39;entityName&#39;] = entity_name
        request_json[&#39;entityXML&#39;][&#39;keywords&#39;] = entity_keywords
        if is_derived:
            request_json[&#39;regularExpression&#39;] = &#34;{\&#34;entity_key\&#34;:\&#34;&#34; + entity_name + &#34;\&#34;}&#34;
            request_json[&#39;entityType&#39;] = 3
            if isinstance(parent_entity, int):
                request_json[&#39;parentEntityId&#39;] = parent_entity
            elif isinstance(parent_entity, str):
                request_json[&#39;parentEntityId&#39;] = self._regex_entities[parent_entity][&#39;entityId&#39;]
            else:
                raise SDKException(&#39;ActivateEntity&#39;, &#39;102&#39;, &#39;Unsupported parent entity id type provided&#39;)

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._api_create_regex_entity, request_json
        )

        if flag:
            if response.json() and &#39;entityDetails&#39; in response.json() and &#39;err&#39; not in response.json():
                self.refresh()
                return
            raise SDKException(&#39;ActivateEntity&#39;, &#39;104&#39;)
        self._response_not_success(response)

    def delete(self, entity_name):
        &#34;&#34;&#34;deletes the specified regex entity name in the commcell

                    Args:
                        entity_name (str)      --  name of the regex entity

                    Returns:
                        None

                    Raises:
                        SDKException:

                                if response is empty

                                if response is not success

                                if unable to delete regex entity in commcell

                                if unable to find entity name in the commcell

                                if data type of entity_name is invalid


                &#34;&#34;&#34;
        if not isinstance(entity_name, str):
            raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)
        if entity_name not in self._regex_entities:
            raise SDKException(&#39;ActivateEntity&#39;, &#39;102&#39;, &#39;Unable to find given regex entity name in the commcell&#39;)

        flag, response = self._cvpysdk_object.make_request(
            &#39;DELETE&#39;, self._api_delete_regex_entity % self._regex_entities[entity_name][&#39;entityId&#39;]
        )

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json() and response.json()[&#39;errorCode&#39;] == 0:
                self.refresh()
                return
            raise SDKException(&#39;ActivateEntity&#39;, &#39;105&#39;)
        self._response_not_success(response)

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def get_properties(self, entity_name):
        &#34;&#34;&#34;Returns a properties of the specified regex entity name.

            Args:
                entity_name (str)  --  name of the regex entity

            Returns:
                dict -  properties for the given regex entity name


        &#34;&#34;&#34;
        return self._regex_entities[entity_name]

    def _get_all_activate_entities(self):
        &#34;&#34;&#34;Gets the list of all regex entities associated with this commcell.

            Returns:
                dict    -   dictionary consisting of dictionaries, where each dictionary stores the
                                details of a single regex entity

                    {
                        &#34;entityDetails&#34;: [
                            {
                                &#34;displayName&#34;: &#34;US Social Security number&#34;,
                                &#34;flags&#34;: 5,
                                &#34;description&#34;: &#34;&#34;,
                                &#34;categoryName&#34;: &#34;US&#34;,
                                &#34;enabled&#34;: true,
                                &#34;entityName&#34;: &#34;SSN&#34;,
                                &#34;attribute&#34;: 3,
                                &#34;entityType&#34;: 2,
                                &#34;entityKey&#34;: &#34;ssn&#34;,
                                &#34;entityId&#34;: 1111,
                                &#34;entityXML&#34;: {
                                    &#34;keywords&#34;: &#34;Social Security,Social Security#,Soc Sec,SSN,SSNS,SSN#,SS#,SSID&#34;,
                                    &#34;entityKey&#34;: &#34;ssn&#34;,
                                    &#34;isSystemDefinedEntity&#34;: true,
                                    &#34;inheritBaseWords&#34;: false
                                }
                            },
                            {
                                &#34;displayName&#34;: &#34;Person Name&#34;,
                                &#34;flags&#34;: 1,
                                &#34;description&#34;: &#34;Name of a person.&#34;,
                                &#34;categoryName&#34;: &#34;Generic&#34;,
                                &#34;enabled&#34;: true,
                                &#34;entityName&#34;: &#34;Person&#34;,
                                &#34;attribute&#34;: 3,
                                &#34;entityType&#34;: 1,
                                &#34;entityKey&#34;: &#34;person&#34;,
                                &#34;entityId&#34;: 1112,
                                &#34;entityXML&#34;: {
                                    &#34;keywords&#34;: &#34;&#34;,
                                    &#34;entityKey&#34;: &#34;person&#34;,
                                    &#34;inheritBaseWords&#34;: false
                                }
                            }
                            ]
                            }

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._api_get_containers
        )
        if flag:
            if response.json() and &#39;containerTypesList&#39; in response.json():
                self._entities_containers = response.json()[&#39;containerTypesList&#39;]
            else:
                raise SDKException(&#39;ActivateEntity&#39;, &#39;107&#39;)
        else:
            self._response_not_success(response)

        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._api_get_all_regex_entities
        )

        if flag:
            if response.json() and &#39;entityDetails&#39; in response.json():
                return self._get_regex_entity_from_collections(response.json())
            raise SDKException(&#39;ActivateEntity&#39;, &#39;103&#39;)
        self._response_not_success(response)

    def _process_entity_containers(self, entity_name, container_name):
        &#34;&#34;&#34;Returns container details for given entity name &amp; container name

            Args:

                entity_name         (str)       --  Entity name

                container_name      (str)       --  Container name

            Returns:

                dict    --  Container details of entity
        &#34;&#34;&#34;
        output = {}
        for dept in self._entities_containers:
            items_list = dept[&#39;tagSetsAndItems&#39;]
            for country in items_list:
                tags_list = country.get(&#39;tags&#39;,{})
                for tag in tags_list:
                    if entity_name.lower() == tag[&#39;entityDetail&#39;][&#39;entityName&#39;].lower() and \
                            container_name.lower() == country[&#39;container&#39;][&#39;containerName&#39;].lower():
                        output[&#39;tags&#39;] = [tag]
                        output[&#39;container&#39;] = country[&#39;container&#39;]
                        return output
        return output

    def _get_regex_entity_from_collections(self, collections):
        &#34;&#34;&#34;Extracts all the regex entities, and their details from the list of collections given,
            and returns the dictionary of all regex entities

            Args:
                collections     (list)  --  list of all collections

            Returns:
                dict    -   dictionary consisting of dictionaries, where each dictionary stores the
                                details of a single regex entity

        &#34;&#34;&#34;
        _regex_entity = {}
        for regex_entity in collections[&#39;entityDetails&#39;]:
            regex_entity_dict = {}
            regex_entity_dict[&#39;displayName&#39;] = regex_entity.get(&#39;displayName&#39;, &#34;&#34;)
            regex_entity_dict[&#39;entityKey&#39;] = regex_entity.get(&#39;entityKey&#39;, &#34;&#34;)
            regex_entity_dict[&#39;categoryName&#39;] = regex_entity.get(&#39;categoryName&#39;, &#34;&#34;)
            if regex_entity_dict[&#39;categoryName&#39;] is not None:
                regex_entity_dict[&#39;containerDetails&#39;] = self._process_entity_containers(
                    entity_name=regex_entity_dict[&#39;displayName&#39;], container_name=regex_entity_dict[&#39;categoryName&#39;])
            regex_entity_dict[&#39;entityXML&#39;] = regex_entity.get(&#39;entityXML&#39;, &#34;&#34;)
            regex_entity_dict[&#39;entityId&#39;] = regex_entity.get(&#39;entityId&#39;, 0)
            regex_entity_dict[&#39;flags&#39;] = regex_entity.get(&#39;flags&#39;, 0)
            regex_entity_dict[&#39;entityType&#39;] = regex_entity.get(&#39;entityType&#39;, 0)
            regex_entity_dict[&#39;enabled&#39;] = regex_entity.get(&#39;enabled&#39;, False)
            _regex_entity[regex_entity[&#39;entityName&#39;]] = regex_entity_dict
        return _regex_entity

    def refresh(self):
        &#34;&#34;&#34;Refresh the activate regex entities associated with the commcell.&#34;&#34;&#34;
        self._regex_entities = self._get_all_activate_entities()

    def get(self, entity_name):
        &#34;&#34;&#34;Returns a ActivateEntity object for the given regex entity name.

            Args:
                entity_name (str)  --  name of the regex entity

            Returns:

                obj                 -- Object of ActivateEntity class

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

                    if entity_name is not of type string


        &#34;&#34;&#34;
        if not isinstance(entity_name, str):
            raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)

        if self.has_entity(entity_name):
            entity_id = self._regex_entities[entity_name][&#39;entityId&#39;]
            return ActivateEntity(self._commcell_object, entity_name, entity_id)
        raise SDKException(&#39;ActivateEntity&#39;, &#39;102&#39;, &#34;Unable to get ActivateEntity class object&#34;)

    def get_entity_ids(self, entity_name):
        &#34;&#34;&#34;Returns a list of entity id for the given regex entity name list.

            Args:
                entity_name (list)  --  names of the regex entity

            Returns:

                list                -- entity id&#39;s for the given entity names


        &#34;&#34;&#34;
        if not isinstance(entity_name, list):
            raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)
        entity_ids = []
        for regex_entity in entity_name:
            if regex_entity in self._regex_entities:
                entity_ids.append(self._regex_entities[regex_entity][&#39;entityId&#39;])
            else:
                raise SDKException(
                    &#39;ActivateEntity&#39;, &#39;102&#39;, f&#34;Unable to find entity id for given entity name :{regex_entity}&#34;)
        return entity_ids

    def get_entity_keys(self, entity_name):
        &#34;&#34;&#34;Returns a list of entity keys for the given regex entity name list.

            Args:
                entity_name (list)  --  names of the regex entity

            Returns:

                list                -- entity keys for the given entity names


        &#34;&#34;&#34;
        if not isinstance(entity_name, list):
            raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)
        entity_keys = []
        for regex_entity in entity_name:
            if regex_entity in self._regex_entities:
                entity_keys.append(self._regex_entities[regex_entity][&#39;entityKey&#39;])
            else:
                raise SDKException(
                    &#39;ActivateEntity&#39;, &#39;102&#39;, f&#34;Unable to find entity keys for given entity name :{regex_entity}&#34;)
        return entity_keys

    def has_entity(self, entity_name):
        &#34;&#34;&#34;Checks if a regex entity exists in the commcell with the input name.

            Args:
                entity_name (str)  --  name of the regex entity

            Returns:
                bool - boolean output whether the regex entity exists in the commcell or not

            Raises:
                SDKException:
                    if type of the regex entity name argument is not string

        &#34;&#34;&#34;
        if not isinstance(entity_name, str):
            raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)

        return self._regex_entities and entity_name.lower() in map(str.lower, self._regex_entities)


class ActivateEntity(object):
    &#34;&#34;&#34;Class for performing operations on a single regex entity&#34;&#34;&#34;

    def __init__(self, commcell_object, entity_name, entity_id=None):
        &#34;&#34;&#34;Initialize an object of the ActivateEntity class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

                entity_name     (str)           --  name of the regex entity

                entity_id       (str)           --  id of the regex entity
                    default: None

            Returns:
                object  -   instance of the ActivateEntity class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._services = commcell_object._services
        self._cvpysdk_obj = self._commcell_object._cvpysdk_object
        self._entity_name = entity_name
        self._entity_id = None
        self._display_name = None
        self._entity_type = None
        self._is_enabled = None
        self._entity_key = None
        self._entity_xml = None
        self._category_name = None
        self._container_details = None
        if entity_id is None:
            self._entity_id = self._get_entity_id(entity_name)
        else:
            self._entity_id = entity_id
        self.refresh()
        self._api_modify_regex_entity = self._services[&#39;ACTIVATE_ENTITY&#39;]

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def modify(self, entity_regex, entity_keywords, entity_flag, is_derived=False, parent_entity=None):
        &#34;&#34;&#34;Modifies the specified regex entity details

                    Args:

                        entity_regex (str)     --  Regex for the entity

                        entity_keywords (str)  --  Keywords for the entity

                        entity_flag (int)      --  Sensitivity flag value for entity
                                                        5-Highly sensitive
                                                        3-Moderate sensitive
                                                        1-Low sensitive

                        is_derived (bool)      --  represents whether it is derived entity or not

                        parent_entity(int)     -- entity id of the parent entity in case of derived entity

                    Returns:
                        None

                    Raises:
                        SDKException:

                                if response is empty

                                if response is not success

                                if unable to modify regex entity in commcell

                                if input entity_keywords &amp; entity_regex is not string

                                if entity_flag value is not in allowed values[1,3,5]


                &#34;&#34;&#34;
        if not isinstance(entity_regex, str) \
                or not isinstance(entity_keywords, str):
            raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)
        if entity_flag not in [1, 3, 5]:
            raise SDKException(&#39;ActivateEntity&#39;, &#39;102&#39;, &#39;Unsupported entity flag value&#39;)
        request_json = ActivateEntityConstants.REQUEST_JSON
        request_json[&#39;regularExpression&#39;] = &#34;{\&#34;entity_key\&#34;:\&#34;&#34; + \
            self._entity_name + &#34;\&#34;,\&#34;entity_regex\&#34;:\&#34;&#34; + entity_regex + &#34;\&#34;}&#34;
        request_json[&#39;flags&#39;] = entity_flag
        request_json[&#39;entityName&#39;] = self._entity_name
        request_json[&#39;entityXML&#39;][&#39;keywords&#39;] = entity_keywords
        if is_derived:
            request_json[&#39;regularExpression&#39;] = &#34;{\&#34;entity_key\&#34;:\&#34;&#34; + self._entity_name + &#34;\&#34;}&#34;
            request_json[&#39;entityType&#39;] = 3
            if isinstance(parent_entity, int):
                request_json[&#39;parentEntityId&#39;] = parent_entity
            elif isinstance(parent_entity, str):
                request_json[&#39;parentEntityId&#39;] = ActivateEntities.get(
                    self._commcell_object, entity_name=parent_entity).entity_id
            else:
                raise SDKException(&#39;ActivateEntity&#39;, &#39;102&#39;, &#39;Unsupported parent entity id type provided&#39;)

        flag, response = self._cvpysdk_obj.make_request(
            &#39;PUT&#39;, (self._api_modify_regex_entity % self.entity_id), request_json
        )

        if flag:
            if response.json() and &#39;entityDetails&#39; in response.json() and &#39;err&#39; not in response.json():
                return
            raise SDKException(&#39;ActivateEntity&#39;, &#39;106&#39;)
        self._response_not_success(response)

    def _get_entity_id(self, entity_name):
        &#34;&#34;&#34; Get regex entity id for given entity name
                Args:

                    entity_name (str)  -- Name of the regex entity

                Returns:

                    int                -- id of the regex entity

        &#34;&#34;&#34;

        return self._commcell_object.activate.entity_manager().get(entity_name).entity_id

    def _get_entity_properties(self):
        &#34;&#34;&#34; Get regex entity properties for given entity name
                Args:

                    None

                Returns:

                    None

        &#34;&#34;&#34;

        regex_entity_dict = self._commcell_object.activate.entity_manager().get_properties(self._entity_name)
        self._display_name = regex_entity_dict[&#39;displayName&#39;]
        self._category_name = regex_entity_dict[&#39;categoryName&#39;]
        self._entity_id = regex_entity_dict[&#39;entityId&#39;]
        self._is_enabled = regex_entity_dict[&#39;enabled&#39;]
        self._entity_key = regex_entity_dict[&#39;entityKey&#39;]
        self._entity_type = regex_entity_dict[&#39;entityType&#39;]
        self._entity_xml = regex_entity_dict[&#39;entityXML&#39;]
        self._container_details = regex_entity_dict[&#39;containerDetails&#39;]
        return regex_entity_dict

    @property
    def container_details(self):
        &#34;&#34;&#34;Returns the container details for this entity&#34;&#34;&#34;
        return self._container_details

    @property
    def entity_id(self):
        &#34;&#34;&#34;Returns the value of the regex entity id attribute.&#34;&#34;&#34;
        return self._entity_id

    @property
    def entity_type(self):
        &#34;&#34;&#34;Returns the entity type attribute.&#34;&#34;&#34;
        return self._entity_type

    @property
    def category_name(self):
        &#34;&#34;&#34;Returns the entity category name attribute.&#34;&#34;&#34;
        return self._category_name

    @property
    def display_name(self):
        &#34;&#34;&#34;Returns the entity display name attribute.&#34;&#34;&#34;
        return self._display_name

    @property
    def is_enabled(self):
        &#34;&#34;&#34;Returns the entity isenabled attribute.&#34;&#34;&#34;
        return self._is_enabled

    @property
    def entity_key(self):
        &#34;&#34;&#34;Returns the entity key attribute.&#34;&#34;&#34;
        return self._entity_key

    @property
    def entity_xml(self):
        &#34;&#34;&#34;Returns the entity xml attribute.&#34;&#34;&#34;
        return self._entity_xml

    def refresh(self):
        &#34;&#34;&#34;Refresh the regex entity details for associated object&#34;&#34;&#34;
        self._get_entity_properties()


class Tags(object):
    &#34;&#34;&#34;Class for representing all the Tagsets in the commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initializes an instance of the Tags class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

            Returns:
                object  -   instance of the Tags class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._tag_set_entities = None
        self._api_get_all_tag_sets = self._services[&#39;GET_TAGS&#39;]
        self._api_add_tag_set = self._services[&#39;ADD_CONTAINER&#39;]
        self._api_delete_tag_set = self._services[&#39;DELETE_CONTAINER&#39;]
        self.refresh()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    @staticmethod
    def _get_tag_sets_from_collections(collections):
        &#34;&#34;&#34;Extracts all the tagsets, and their details from the list of collections given,
            and returns the dictionary of all tagsets

            Args:
                collections     (list)  --  list of all tagsets

            Returns:
                dict    -   dictionary consisting of dictionaries, where each dictionary stores the
                                details of a single tagset

        &#34;&#34;&#34;
        _tag_set_entity = {}
        for tagset in collections[&#39;listOftagSetList&#39;]:
            tagset = tagset[&#39;tagSetsAndItems&#39;]
            container = tagset[0][&#39;container&#39;]
            owner_info = tagset[0][&#39;container&#39;][&#39;ownerInfo&#39;]
            tagset_dict = {}
            tagset_dict[&#39;containerName&#39;] = container.get(&#39;containerName&#39;, &#34;&#34;)
            tagset_dict[&#39;containerFullName&#39;] = container.get(&#39;containerFullName&#39;, &#34;&#34;)
            tagset_dict[&#39;containerId&#39;] = container.get(&#39;containerId&#39;, &#34;&#34;)
            tagset_dict[&#39;containerGuid&#39;] = container.get(&#39;containerGuid&#39;, &#34;&#34;)
            tagset_dict[&#39;comment&#39;] = container.get(&#39;comment&#39;, &#34;&#34;)
            tagset_dict[&#39;owneruserName&#39;] = owner_info.get(&#39;userName&#39;, &#34;&#34;)
            tagset_dict[&#39;owneruserGuid&#39;] = owner_info.get(&#39;userGuid&#39;, &#34;&#34;)
            tagset_dict[&#39;owneraliasName&#39;] = owner_info.get(&#39;aliasName&#39;, &#34;&#34;)
            container_tags = []
            tag_ids = []
            tag_dict = {}
            # process tags only if it is present
            if &#39;tags&#39; in tagset[0]:
                tags = tagset[0][&#39;tags&#39;]
                for tag in tags:
                    tag_dict[tag[&#39;name&#39;].lower()] = tag
                    container_tags.append(tag[&#39;name&#39;].lower())
                    tag_ids.append(tag[&#39;tagId&#39;])
            tagset_dict[&#39;tags&#39;] = container_tags
            tagset_dict[&#39;tagsIds&#39;] = tag_ids
            tagset_dict[&#39;tagsDetails&#39;] = tag_dict
            _tag_set_entity[tagset_dict[&#39;containerName&#39;].lower()] = tagset_dict
        return _tag_set_entity

    def _get_all_tag_sets(self):
        &#34;&#34;&#34;Gets the list of all tagsets associated with this commcell.

            Returns:
                dict    -   dictionary consisting of dictionaries, where each dictionary stores the
                                details of a single tagset entity

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._api_get_all_tag_sets
        )

        if flag:
            if response.json() and &#39;listOftagSetList&#39; in response.json():
                return self._get_tag_sets_from_collections(response.json())
            raise SDKException(&#39;Tags&#39;, &#39;103&#39;)
        self._response_not_success(response)

    def get(self, tag_set_name):
        &#34;&#34;&#34;Returns a TagSet object for the given Tagset name.

            Args:
                tag_set_name (str)  --  name of the TagSet

            Returns:

                obj                 -- Object of TagSet class

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

                    if tag_set_name is not of type string


        &#34;&#34;&#34;
        if not isinstance(tag_set_name, str):
            raise SDKException(&#39;Tags&#39;, &#39;101&#39;)

        if self.has_tag_set(tag_set_name):
            tag_set_id = self._tag_set_entities[tag_set_name.lower()][&#39;containerId&#39;]
            return TagSet(self._commcell_object, tag_set_name=tag_set_name, tag_set_id=tag_set_id)
        raise SDKException(&#39;Tags&#39;, &#39;102&#39;, &#34;Unable to get TagSet class object&#34;)

    def has_tag_set(self, tag_set_name):
        &#34;&#34;&#34;Checks if a tagset exists in the commcell with the input name or not

            Args:
                tag_set_name (str)  --  name of the TagSet

            Returns:
                bool - boolean output whether the TagSet exists in the commcell or not

            Raises:
                SDKException:
                    if type of the TagSet name argument is not string

        &#34;&#34;&#34;
        if not isinstance(tag_set_name, str):
            raise SDKException(&#39;Tags&#39;, &#39;101&#39;)
        return self._tag_set_entities and tag_set_name.lower() in map(str.lower, self._tag_set_entities)

    def get_properties(self, tag_set_name):
        &#34;&#34;&#34;Returns a properties of the specified TagSet name.

            Args:
                tag_set_name (str)  --  name of the TagSet

            Returns:
                dict -  properties for the given TagSet name


                Example : {
                              &#34;containerName&#34;: &#34;cvpysdk1&#34;,
                              &#34;containerFullName&#34;: &#34;cvpysdk1&#34;,
                              &#34;containerId&#34;: 65931,
                              &#34;containerGuid&#34;: &#34;6B870271-543A-4B76-955D-CDEB3807D68E&#34;,
                              &#34;comment&#34;: &#34;Created from CvPySDK&#34;,
                              &#34;owneruserName&#34;: &#34;xxx&#34;,
                              &#34;owneruserGuid&#34;: &#34;C31C1194-AA5C-47C3-B5B0-9087EF429B6B&#34;,
                              &#34;owneraliasName&#34;: &#34;xx&#34;,
                              &#34;tags&#34;: [
                                &#34;p10&#34;
                              ],
                              &#34;tagsIds&#34;: [
                                15865
                              ],
                              &#34;tagsDetails&#34;: {
                                &#34;p10&#34;: {
                                  &#34;tagOwnerType&#34;: 1,
                                  &#34;tagId&#34;: 15865,
                                  &#34;name&#34;: &#34;p10&#34;,
                                  &#34;flags&#34;: 0,
                                  &#34;fullName&#34;: &#34;cvpysdk1\\p10&#34;,
                                  &#34;description&#34;: &#34;&#34;,
                                  &#34;id&#34;: &#34;C9E229D0-B895-4653-9DA7-C9C6BD999121&#34;,
                                  &#34;attribute&#34;: {}
                                }
                              }
                            }


        &#34;&#34;&#34;
        return self._tag_set_entities[tag_set_name.lower()]

    def delete(self, tag_set_name):
        &#34;&#34;&#34;Deletes the specified tagset from the commcell

                Args:

                    tag_set_name    (str)       --  Name of the Tagset

                Returns:

                    None

                Raises:

                    SDKException:

                                if response is empty

                                if response is not success

                                if unable to delete TagSet entity in commcell

                                if input data type is not valid

                                if unable to find TagSet entity in commcell
        &#34;&#34;&#34;
        if not isinstance(tag_set_name, str):
            raise SDKException(&#39;Tags&#39;, &#39;101&#39;)
        if not self.has_tag_set(tag_set_name=tag_set_name):
            raise SDKException(&#39;Tags&#39;, &#39;102&#39;, &#34;Tagset not found&#34;)
        request_json = copy.deepcopy(TagConstants.TAG_SET_DELETE_REQUEST_JSON)
        request_json[&#39;containers&#39;][0][&#39;containerId&#39;] = self._tag_set_entities[tag_set_name.lower()][&#39;containerId&#39;]
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._api_delete_tag_set, request_json
        )
        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                if int(response.json()[&#39;errorCode&#39;]) != 0:
                    raise SDKException(&#39;Tags&#39;, &#39;102&#39;, response.json()[&#39;errorMessage&#39;])
            elif &#39;errList&#39; in response.json():
                raise SDKException(&#39;Tags&#39;, &#39;102&#39;, response.json()[&#39;errList&#39;][0][&#39;errLogMessage&#39;])
            self.refresh()
            return
        self._response_not_success(response)

    def add(self, tag_set_name, comment=&#34;Created from CvPySDK&#34;):
        &#34;&#34;&#34;Adds the specified TagSet name in the commcell

                    Args:
                        tag_set_name (str)     --  name of the TagSet

                        comment (str)         --  Comment for this TagSet

                    Returns:

                        object      --  Object of TagSet class

                    Raises:
                        SDKException:

                                if response is empty

                                if response is not success

                                if unable to add TagSet entity in commcell

                                if input data type is not valid
                &#34;&#34;&#34;
        if not isinstance(tag_set_name, str) or not isinstance(comment, str):
            raise SDKException(&#39;Tags&#39;, &#39;101&#39;)
        request_json = copy.deepcopy(TagConstants.TAG_SET_ADD_REQUEST_JSON)
        request_json[&#39;container&#39;][&#39;containerName&#39;] = tag_set_name
        request_json[&#39;container&#39;][&#39;comment&#39;] = comment
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._api_add_tag_set, request_json
        )
        if flag:
            if response.json():
                if &#39;errList&#39; in response.json():
                    raise SDKException(&#39;Tags&#39;, &#39;102&#39;, response.json()[&#39;errList&#39;][0][&#39;errLogMessage&#39;])
                elif &#39;container&#39; in response.json():
                    self.refresh()
                    return TagSet(commcell_object=self._commcell_object, tag_set_name=tag_set_name)
            raise SDKException(&#39;Tags&#39;, &#39;104&#39;)
        self._response_not_success(response)

    def refresh(self):
        &#34;&#34;&#34;Refresh the TagSet entities associated with the commcell.&#34;&#34;&#34;
        self._tag_set_entities = self._get_all_tag_sets()


class TagSet(object):
    &#34;&#34;&#34;Class for performing operations on a TagSet&#34;&#34;&#34;

    def __init__(self, commcell_object, tag_set_name, tag_set_id=None):
        &#34;&#34;&#34;Initialize an object of the TagSet class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

                tag_set_name     (str)          --  name of the TagSet

                tag_set_id       (str)          --  Container id of the TagSet
                                                        default: None

            Returns:
                object  -   instance of the Tagset class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._services = commcell_object._services
        self._cvpysdk_obj = self._commcell_object._cvpysdk_object
        self._tag_set_name = tag_set_name
        self._tag_set_id = None
        self._tag_set_props = None
        if tag_set_id is None:
            self._tag_set_id = self._get_tag_set_id(tag_set_name)
        else:
            self._tag_set_id = tag_set_id
        self._container_guid = None
        self._owner = None
        self._full_name = None
        self._comment = None
        self._tags = None
        self._tag_ids = None
        self._owner_alias_name = None
        self._api_modify_tag_set = self._services[&#39;ADD_CONTAINER&#39;]
        self._api_add_tag = self._services[&#39;GET_TAGS&#39;]
        self._api_security = self._services[&#39;SECURITY_ASSOCIATION&#39;]
        self.refresh()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def has_tag(self, tag_name):
        &#34;&#34;&#34;Returns whether tag exists with given name or not in tagset

                    Args:
                        tag_name (str)      --  name of the Tag

                    Returns:

                        bool    --  True if it exists or else false

                    Raises:
                        SDKException:

                            if tag_name is not of type string


        &#34;&#34;&#34;
        if not isinstance(tag_name, str):
            raise SDKException(&#39;Tags&#39;, &#39;101&#39;)
        if tag_name.lower() in self.tags:
            return True
        return False

    def get(self, tag_name):
        &#34;&#34;&#34;Returns a Tag object for the given Tag name.

            Args:
                tag_name (str)      --  name of the Tag

            Returns:

                obj                 -- Object of Tag class

            Raises:
                SDKException:

                    if unable to create Tag object

                    if tag_name is not of type string


        &#34;&#34;&#34;
        if not isinstance(tag_name, str):
            raise SDKException(&#39;Tags&#39;, &#39;101&#39;)
        if self.has_tag(tag_name):
            return Tag(self._commcell_object, tag_set_name=self._tag_set_name, tag_name=tag_name)
        raise SDKException(&#39;Tags&#39;, &#39;102&#39;, &#34;Unable to get Tag class object&#34;)

    def get_tag_id(self, tag_name):
        &#34;&#34;&#34;Returns the tag id for the given tag name

                Args:

                    tag_name        (str)       --  Name of the tag

                Returns:

                    int     --  Tag id

                Raises:

                    SDKExeption:

                        if input tag name is not found in this tagset

        &#34;&#34;&#34;
        if tag_name.lower() not in self.tags:
            raise SDKException(&#39;Tags&#39;, &#39;106&#39;)
        index = self.tags.index(tag_name.lower())
        return self._tag_ids[index]

    def add_tag(self, tag_name):
        &#34;&#34;&#34;Adds the specified tag name in the tagset container in commcell

                           Args:
                               tag_name (str)     --  name of the Tag

                           Returns:

                               object      --  Object of Tag class

                           Raises:
                               SDKException:

                                       if response is empty

                                       if response is not success

                                       if unable to add Tag inside Tagset in commcell

                                       if input data type is not valid
                       &#34;&#34;&#34;
        if not isinstance(tag_name, str):
            raise SDKException(&#39;Tags&#39;, &#39;101&#39;)
        request_json = copy.deepcopy(TagConstants.TAG_ADD_REQUEST_JSON)
        request_json[&#39;container&#39;][&#39;containerId&#39;] = self._tag_set_id
        request_json[&#39;tags&#39;][0][&#39;name&#39;] = tag_name
        flag, response = self._cvpysdk_obj.make_request(
            &#39;POST&#39;, self._api_add_tag, request_json
        )
        if flag:
            if response.json():
                if &#39;errList&#39; in response.json():
                    raise SDKException(&#39;Tags&#39;, &#39;102&#39;, response.json()[&#39;errList&#39;][0][&#39;errLogMessage&#39;])
                elif &#39;tag&#39; in response.json():
                    self.refresh()
                    return Tag(commcell_object=self._commcell_object, tag_set_name=self._tag_set_name,
                               tag_name=tag_name)
            raise SDKException(&#39;Tags&#39;, &#39;104&#39;)
        self._response_not_success(response)

    def share(self, user_or_group_name, allow_edit_permission=False, is_user=True, ops_type=1):
        &#34;&#34;&#34;Shares tagset with given user or group in commcell

                Args:

                    user_or_group_name      (str)       --  Name of user or group

                    is_user                 (bool)      --  Denotes whether this is user or group name
                                                                default : True(User)

                    allow_edit_permission   (bool)      --  whether to give edit permission or not to user or group

                    ops_type                (int)       --  Operation type

                                                            Default : 1 (Add)

                                                            Supported : 1 (Add)
                                                                        2 (Modify)
                                                                        3 (Delete)

                Returns:

                    None

                Raises:

                    SDKException:

                            if unable to update security associations

                            if response is empty or not success
        &#34;&#34;&#34;
        if not isinstance(user_or_group_name, str):
            raise SDKException(&#39;Tags&#39;, &#39;101&#39;)
        request_json = copy.deepcopy(TagConstants.TAG_SET_SHARE_REQUEST_JSON)
        external_user = False
        if &#39;\\&#39; in user_or_group_name:
            external_user = True

        if is_user:
            user_obj = self._commcell_object.users.get(user_or_group_name)
            user_id = user_obj.user_id
            user_or_group_name = f&#34;\\{user_or_group_name}&#34;
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;userId&#39;] = int(user_id)
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;_type_&#39;] = &#34;13&#34;
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;userName&#39;] = user_or_group_name
        elif external_user:
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;groupId&#39;] = 0
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;_type_&#39;] = &#34;62&#34;
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;externalGroupName&#39;] = user_or_group_name
        else:
            grp_obj = self._commcell_object.user_groups.get(user_or_group_name)
            grp_id = grp_obj.user_group_id
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;userGroupId&#39;] = int(grp_id)
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;_type_&#39;] = &#34;15&#34;
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;userGroupName&#39;] = user_or_group_name

        request_json[&#39;entityAssociated&#39;][&#39;entity&#39;][0][&#39;tagId&#39;] = self._tag_set_id
        request_json[&#39;securityAssociations&#39;][&#39;associationsOperationType&#39;] = ops_type

        if allow_edit_permission:
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;properties&#39;][&#39;permissions&#39;].append(
                TagConstants.ADD_PERMISSION)
        flag, response = self._cvpysdk_obj.make_request(
            &#39;POST&#39;, self._api_security, request_json
        )
        if flag:
            if response.json() and &#39;response&#39; in response.json():
                response_json = response.json()[&#39;response&#39;][0]
                error_code = response_json[&#39;errorCode&#39;]
                if error_code != 0:
                    error_message = response_json[&#39;errorString&#39;]
                    raise SDKException(
                        &#39;Tags&#39;,
                        &#39;102&#39;, error_message)
            else:
                raise SDKException(&#39;Tags&#39;, &#39;107&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def modify(self, new_name=None, comment=&#34;Modified from CvPySDK&#34;):
        &#34;&#34;&#34;Modifies the specified tagset in the commcell

                Args:

                    new_name        (str)       --  New name for Tagset

                    comment         (str)       --  New comment which needs to be added for Tagset

                Returns:

                    None

                Raises:

                    SDKException:

                                if response is empty

                                if response is not success

                                if unable to modify TagSet entity in commcell

                                if input is not a valid data type of string

        &#34;&#34;&#34;
        if not isinstance(new_name, str) or not isinstance(comment, str):
            raise SDKException(&#39;Tags&#39;, &#39;101&#39;)
        request_json = copy.deepcopy(TagConstants.TAG_SET_MODIFY_REQUEST_JSON)
        request_json[&#39;container&#39;][&#39;containerName&#39;] = new_name
        request_json[&#39;container&#39;][&#39;comment&#39;] = comment
        request_json[&#39;container&#39;][&#39;containerId&#39;] = self._tag_set_id
        flag, response = self._cvpysdk_obj.make_request(
            &#39;POST&#39;, self._api_modify_tag_set, request_json
        )
        if flag:
            if response.json():
                if &#39;errList&#39; in response.json():
                    raise SDKException(&#39;Tags&#39;, &#39;102&#39;, response.json()[&#39;errList&#39;][0][&#39;errLogMessage&#39;])
                elif &#39;container&#39; in response.json():
                    self._tag_set_name = new_name
                    self.refresh()
                    return
            raise SDKException(&#39;Tags&#39;, &#39;105&#39;)
        self._response_not_success(response)

    def refresh(self):
        &#34;&#34;&#34;Refresh the TagSet details for associated object&#34;&#34;&#34;
        self._tag_set_props = self._get_tag_set_properties()

    def _get_tag_set_properties(self):
        &#34;&#34;&#34; Get TagSet properties for this associated object
                Args:

                    None

                Returns:

                    dict    --  Containing tagset properties

        &#34;&#34;&#34;
        tags = self._commcell_object.activate.entity_manager(
            EntityManagerTypes.TAGS)
        # call refresh before fetching properties
        tags.refresh()
        tag_set_dict = tags.get_properties(self._tag_set_name)
        self._full_name = tag_set_dict[&#39;containerFullName&#39;]
        self._owner = tag_set_dict[&#39;owneruserName&#39;]
        self._comment = tag_set_dict[&#39;comment&#39;]
        self._container_guid = tag_set_dict[&#39;containerGuid&#39;]
        self._tags = tag_set_dict[&#39;tags&#39;]
        self._tag_ids = tag_set_dict[&#39;tagsIds&#39;]
        self._owner_alias_name = tag_set_dict[&#39;owneraliasName&#39;]
        return tag_set_dict

    def _get_tag_set_id(self, tag_set_name):
        &#34;&#34;&#34; Get TagSet container id for given tag set name
                Args:

                    tag_set_name (str)  -- Name of the TagSet

                Returns:

                    int                --  TagSet container Id

        &#34;&#34;&#34;

        return self._commcell_object.activate.entity_manager(
            entity_type=EntityManagerTypes.TAGS).get(tag_set_name).tag_set_id

    @property
    def guid(self):
        &#34;&#34;&#34;Returns the container guid of this Tagset&#34;&#34;&#34;
        return self._container_guid

    @property
    def full_name(self):
        &#34;&#34;&#34;Returns the full name of this Tagset&#34;&#34;&#34;
        return self._full_name

    @property
    def comment(self):
        &#34;&#34;&#34;Returns the comment provided for this Tagset&#34;&#34;&#34;
        return self._comment

    @property
    def owner(self):
        &#34;&#34;&#34;Returns the owner username for this Tagset&#34;&#34;&#34;
        return self._owner

    @property
    def owner_alias_name(self):
        &#34;&#34;&#34;Returns the owner alias name for this Tagset&#34;&#34;&#34;
        return self._owner_alias_name

    @property
    def tags(self):
        &#34;&#34;&#34;Returns the tags present in this tagset&#34;&#34;&#34;
        return self._tags

    @property
    def tag_set_id(self):
        &#34;&#34;&#34;returns the container id for this tagset&#34;&#34;&#34;
        return self._tag_set_id


class Tag(object):
    &#34;&#34;&#34;Class for performing operations on a single Tag&#34;&#34;&#34;

    def __init__(self, commcell_object, tag_set_name, tag_name, tag_id=None):
        &#34;&#34;&#34;Initialize an object of the Tag class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

                tag_set_name     (str)          --  name of the TagSet

                tag_name         (str)          --  Name of tag inside TagSet container

                tag_id       (str)              --  id for tag
                                                        default: None

            Returns:
                object  -   instance of the Tag class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._services = commcell_object._services
        self._cvpysdk_obj = self._commcell_object._cvpysdk_object
        self._tag_name = tag_name
        self._tag_set_name = tag_set_name
        self._tag_id = None
        self._tag_props = None
        self._api_modify_tag = self._services[&#39;GET_TAGS&#39;]
        if tag_id is None:
            self._tag_id = self._get_tag_id(tag_set_name, tag_name)
        else:
            self._tag_id = tag_id
        self.refresh()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _get_tag_id(self, tag_set_name, tag_name):
        &#34;&#34;&#34; Get Tag id for given tag name
                Args:

                    tag_set_name    (str)   --  Name of the TagSet

                    tag_name        (str)   --  Name of the Tag

                Returns:

                    int                --  Tag id

        &#34;&#34;&#34;
        tags = self._commcell_object.activate.entity_manager(
            entity_type=EntityManagerTypes.TAGS)
        # we need this refresh so that tags gets refreshed after adding new tag inside tagset
        tags.refresh()
        tag_set = tags.get(tag_set_name)
        return tag_set.get_tag_id(tag_name=tag_name)

    def _get_tag_properties(self):
        &#34;&#34;&#34; Get Tag properties for this associated tag object
                Args:

                    None

                Returns:

                    dict    --  containing tag properties

                        Example : {
                                      &#34;tagOwnerType&#34;: 1,
                                      &#34;tagId&#34;: 15865,
                                      &#34;name&#34;: &#34;p10&#34;,
                                      &#34;flags&#34;: 0,
                                      &#34;fullName&#34;: &#34;cvpysdk1\\p10&#34;,
                                      &#34;description&#34;: &#34;&#34;,
                                      &#34;id&#34;: &#34;C9E229D0-B895-4653-9DA7-C9C6BD999121&#34;,
                                      &#34;attribute&#34;: {}
                                    }

        &#34;&#34;&#34;

        tags = self._commcell_object.activate.entity_manager(
            entity_type=EntityManagerTypes.TAGS)
        # we need this refresh so that tags gets refreshed after adding new tag inside tagset
        tags.refresh()
        tags.get(self._tag_set_name).refresh()
        tag_set_dict = tags.get_properties(self._tag_set_name)
        tag_dict = tag_set_dict[&#39;tagsDetails&#39;][self._tag_name.lower()]
        return tag_dict

    def modify(self, new_name):
        &#34;&#34;&#34;Modifies the tag name in the tagset

                Args:

                    new_name        (str)       --  New name for Tag

                Returns:

                    None

                Raises:

                    SDKException:

                                if response is empty

                                if response is not success

                                if unable to modify Tag name

                                if input data type is not valid

        &#34;&#34;&#34;
        if not isinstance(new_name, str):
            raise SDKException(&#39;Tags&#39;, &#39;101&#39;)
        tags = self._commcell_object.activate.entity_manager(
            entity_type=EntityManagerTypes.TAGS)
        tag_set = tags.get(self._tag_set_name)
        request_json = copy.deepcopy(TagConstants.TAG_MODIFY_REQUEST_JSON)
        request_json[&#39;container&#39;][&#39;containerId&#39;] = tag_set.tag_set_id
        request_json[&#39;tags&#39;][0][&#39;tagId&#39;] = self._tag_id
        request_json[&#39;tags&#39;][0][&#39;name&#39;] = new_name
        flag, response = self._cvpysdk_obj.make_request(
            &#39;PUT&#39;, self._api_modify_tag, request_json
        )
        if flag:
            if response.json():
                if &#39;errList&#39; in response.json():
                    raise SDKException(&#39;Tags&#39;, &#39;102&#39;, response.json()[&#39;errList&#39;][0][&#39;errLogMessage&#39;])
                elif &#39;tag&#39; in response.json():
                    self._tag_name = new_name
                    self.refresh()
                    return
            raise SDKException(&#39;Tags&#39;, &#39;105&#39;)
        self._response_not_success(response)

    def refresh(self):
        &#34;&#34;&#34;Refresh the TagSet details for associated object&#34;&#34;&#34;
        self._tag_props = self._get_tag_properties()

    @property
    def full_name(self):
        &#34;&#34;&#34;Returns the full name of the tag inside tagset&#34;&#34;&#34;
        return self._tag_props[&#39;fullName&#39;]

    @property
    def guid(self):
        &#34;&#34;&#34;Returns the tag guid value&#34;&#34;&#34;
        return self._tag_props[&#39;id&#39;]

    @property
    def tag_id(self):
        &#34;&#34;&#34;Returns the id of the tag&#34;&#34;&#34;
        return self._tag_id


class Classifiers(object):
    &#34;&#34;&#34;Class for representing all the Classifier entities in the commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initializes an instance of the Classifiers class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

            Returns:
                object  -   instance of the Classifiers class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._classifiers = None
        self._api_create_classifier = self._services[&#39;ACTIVATE_ENTITIES&#39;]
        self._api_get_classifier = self._services[&#39;GET_CLASSIFIERS&#39;]
        self._api_delete_classifier = self._services[&#39;ACTIVATE_ENTITY&#39;]
        self.refresh()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def get_properties(self, classifier_name):
        &#34;&#34;&#34;Returns a properties of the specified classifier name.

            Args:
                classifier_name (str)  --  name of the classifier

            Returns:
                dict -  properties for the given classifier name


        &#34;&#34;&#34;
        return self._classifiers[classifier_name.lower()]

    def _get_all_classifier_entities(self):
        &#34;&#34;&#34;Gets the list of all classifier associated with this commcell.

            Returns:
                dict    -   dictionary consisting of dictionaries, where each dictionary stores the
                                details of a single classifier

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._api_get_classifier
        )

        if flag:
            if response.json() and &#39;entityDetails&#39; in response.json():
                return self._get_classifier_entity_from_collections(response.json())
            raise SDKException(&#39;Classifier&#39;, &#39;103&#39;)
        self._response_not_success(response)

    @staticmethod
    def _get_classifier_entity_from_collections(collections):
        &#34;&#34;&#34;Extracts all the classifier entities, and their details from the list of collections given,
            and returns the dictionary of all classifier

            Args:
                collections     (list)  --  list of all collections

            Returns:
                dict    -   dictionary consisting of dictionaries, where each dictionary stores the
                                details of a single classifier

        &#34;&#34;&#34;
        _regex_entity = {}
        for regex_entity in collections[&#39;entityDetails&#39;]:
            regex_entity_dict = {}
            regex_entity_dict[&#39;displayName&#39;] = regex_entity.get(&#39;displayName&#39;, &#34;&#34;)
            regex_entity_dict[&#39;entityKey&#39;] = regex_entity.get(&#39;entityKey&#39;, &#34;&#34;)
            regex_entity_dict[&#39;categoryName&#39;] = regex_entity.get(&#39;categoryName&#39;, &#34;&#34;)
            regex_entity_dict[&#39;entityXML&#39;] = regex_entity.get(&#39;entityXML&#39;, &#34;&#34;)
            regex_entity_dict[&#39;entityId&#39;] = regex_entity.get(&#39;entityId&#39;, 0)
            regex_entity_dict[&#39;flags&#39;] = regex_entity.get(&#39;flags&#39;, 0)
            regex_entity_dict[&#39;entityType&#39;] = regex_entity.get(&#39;entityType&#39;, 0)
            regex_entity_dict[&#39;enabled&#39;] = regex_entity.get(&#39;enabled&#39;, False)
            _regex_entity[regex_entity[&#39;entityName&#39;].lower()] = regex_entity_dict
        return _regex_entity

    def refresh(self):
        &#34;&#34;&#34;Refresh the classifier associated with the commcell.&#34;&#34;&#34;
        self._classifiers = self._get_all_classifier_entities()

    def delete(self, classifier_name):
        &#34;&#34;&#34;deletes the specified classifier in the commcell

                    Args:

                        classifier_name (str)      --  name of the classifier

                    Returns:
                        None

                    Raises:

                        SDKException:

                                if response is empty

                                if response is not success

                                if unable to delete classifier in commcell

                                if unable to find classifier in the commcell

                                if data type of input is invalid


                &#34;&#34;&#34;
        if not isinstance(classifier_name, str):
            raise SDKException(&#39;Classifier&#39;, &#39;101&#39;)
        if classifier_name.lower() not in self._classifiers:
            raise SDKException(&#39;Classifier&#39;, &#39;102&#39;, &#39;Unable to find given classifier name in the commcell&#39;)

        flag, response = self._cvpysdk_object.make_request(
            &#39;DELETE&#39;, self._api_delete_classifier % self._classifiers[classifier_name.lower()][&#39;entityId&#39;]
        )

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json() and response.json()[&#39;errorCode&#39;] == 0:
                self.refresh()
                return
            raise SDKException(&#39;Classifier&#39;, &#39;105&#39;)
        self._response_not_success(response)

    def add(self, classifier_name, content_analyzer, description=&#34;Created from CvPySDK&#34;, training_zip_data_file=None):
        &#34;&#34;&#34;Creates new classifier with given name in the commcell

                Args:

                    classifier_name     (str)       --      Name of the classifier

                    content_analyzer    (str)       --      Content Analyzer cloud name

                    description         (str)       --      Description for classifier

                    training_zip_data_file  (str)   --      Zip file path containing training data files

                Returns:

                    object      --  returns object of Classifier class

                Raises:

                    SDKException:

                        if input data type is not valid

                        if response is empty or not success

                        if failed to create classifier

                        if failed to find content analyzer cloud details

        &#34;&#34;&#34;

        if not isinstance(classifier_name, str) or not isinstance(content_analyzer, str):
            raise SDKException(&#39;Classifier&#39;, &#39;101&#39;)
        if not self._commcell_object.content_analyzers.has_client(content_analyzer):
            raise SDKException(&#39;Classifier&#39;, &#39;102&#39;, &#34;Given CA cloud doesn&#39;t exists on this commcell&#34;)
        ca_obj = self._commcell_object.content_analyzers.get(content_analyzer)
        request_json = copy.deepcopy(ClassifierConstants.CREATE_REQUEST_JSON)
        request_json[&#39;description&#39;] = description
        request_json[&#39;entityName&#39;] = classifier_name
        request_json[&#39;entityKey&#39;] = classifier_name.replace(&#34; &#34;, &#34;_&#34;).lower()
        ca_details_json = request_json[&#39;entityXML&#39;][&#39;classifierDetails&#39;][&#39;CAUsedInTraining&#39;]
        ca_details_json[&#39;caUrl&#39;] = ca_obj.cloud_url
        ca_details_json[&#39;clientId&#39;] = int(ca_obj.client_id)
        ca_details_json[&#39;cloudName&#39;] = content_analyzer
        port_no = int(ca_obj.cloud_url.split(&#34;:&#34;)[2])
        # update if it is not default port no of 22000
        if port_no != 22000:
            request_json[&#39;entityXML&#39;][&#39;classifierDetails&#39;][&#39;trainDatasetURI&#39;] = \
                request_json[&#39;entityXML&#39;][&#39;classifierDetails&#39;][&#39;trainDatasetURI&#39;].replace(&#34;22000&#34;, str(port_no))
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._api_create_classifier, request_json
        )

        if flag:
            if response.json() and &#39;entityDetails&#39; in response.json() and &#39;err&#39; not in response.json():
                entity_id = response.json()[&#39;entityDetails&#39;][0][&#39;entityId&#39;]
                self.refresh()
                classifier_obj = Classifier(
                    commcell_object=self._commcell_object,
                    classifier_name=classifier_name,
                    entity_id=entity_id)
                if training_zip_data_file:
                    classifier_obj.upload_data(zip_file=training_zip_data_file, start_training=True)
                return classifier_obj
            raise SDKException(&#39;Classifier&#39;, &#39;104&#39;)
        self._response_not_success(response)

    def get(self, classifier_name):
        &#34;&#34;&#34;Returns a Classifier object for the given classifier name.

            Args:
                classifier_name (str)  --  name of the classifier

            Returns:

                obj                 -- Object of Classifier class

            Raises:
                SDKException:

                    if unable to find classifier info in commcell

                    if classifier_name is not of type string


        &#34;&#34;&#34;
        if not isinstance(classifier_name, str):
            raise SDKException(&#39;Classifier&#39;, &#39;101&#39;)

        if self.has_classifier(classifier_name.lower()):
            entity_id = self._classifiers[classifier_name.lower()][&#39;entityId&#39;]
            return Classifier(self._commcell_object, classifier_name.lower(), entity_id)
        raise SDKException(&#39;Classifier&#39;, &#39;102&#39;, &#34;Unable to get Classifier class object&#34;)

    def get_entity_ids(self, classifier_name):
        &#34;&#34;&#34;Returns a list of entity id for the given classifier name list.

            Args:
                classifier_name (list)  --  names of the classifier

            Returns:

                list                -- entity id&#39;s for the given classifier names

            Raises:
                SDKException:

                    if classifier_name is not of type list

                    if unable to find entity id for classifier


        &#34;&#34;&#34;
        if not isinstance(classifier_name, list):
            raise SDKException(&#39;Classifier&#39;, &#39;101&#39;)
        entity_ids = []
        for classifier in classifier_name:
            classifier = classifier.lower()
            if classifier in self._classifiers:
                entity_ids.append(self._classifiers[classifier][&#39;entityId&#39;])
            else:
                raise SDKException(
                    &#39;Classifier&#39;, &#39;102&#39;, f&#34;Unable to find entity id for given classifier name :{classifier}&#34;)
        return entity_ids

    def get_entity_keys(self, classifier_name):
        &#34;&#34;&#34;Returns a list of entity keys for the given classifier name list.

            Args:
                classifier_name (list)  --  names of the classifier

            Returns:

                list                -- entity keys for the given classifier names

            Raises:

                SDKException:

                    if classifier_name is not of type list

                    if unable to find entity key for classifier


        &#34;&#34;&#34;
        if not isinstance(classifier_name, list):
            raise SDKException(&#39;Classifier&#39;, &#39;101&#39;)
        entity_keys = []
        for classifier in classifier_name:
            classifier = classifier.lower()
            if classifier in self._classifiers:
                entity_keys.append(self._classifiers[classifier][&#39;entityKey&#39;])
            else:
                raise SDKException(
                    &#39;Classifier&#39;, &#39;102&#39;, f&#34;Unable to find entity keys for given classifier name :{classifier}&#34;)
        return entity_keys

    def has_classifier(self, classifier_name):
        &#34;&#34;&#34;Checks if a classifier entity exists in the commcell with the input name.

            Args:
                classifier_name (str)  --  name of the classifier

            Returns:
                bool - boolean output to denote whether classifier exists in the commcell or not

            Raises:
                SDKException:

                    if type of the classifier name argument is not string

        &#34;&#34;&#34;
        if not isinstance(classifier_name, str):
            raise SDKException(&#39;Classifier&#39;, &#39;101&#39;)

        return self._classifiers and classifier_name.lower() in map(str.lower, self._classifiers)


class Classifier(object):
    &#34;&#34;&#34;Class for performing operations on a single classifier entity&#34;&#34;&#34;

    def __init__(self, commcell_object, classifier_name, entity_id=None):
        &#34;&#34;&#34;Initialize an object of the Classifier class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

                classifier_name     (str)       --  name of the classifier

                entity_id       (str)           --  id of the classifier
                    default: None

            Returns:
                object  -   instance of the Classifier class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._services = commcell_object._services
        self._cvpysdk_obj = self._commcell_object._cvpysdk_object
        self._classifier_name = classifier_name
        self._entity_id = None
        self._display_name = None
        self._entity_type = None
        self._is_enabled = None
        self._entity_key = None
        self._entity_xml = None
        self._category_name = None
        self._trained_ca_cloud_id = None
        self._sync_ca_client_id = []
        self._last_training_time = None
        self._training_accuracy = None
        self._training_status = TrainingStatus.NOT_APPLICABLE.value
        self._sample_details = {}
        self._api_upload = self._services[&#39;CA_UPLOAD_FILE&#39;]
        self._api_start_training = self._services[&#39;START_TRAINING&#39;]
        self._api_cancel_training = self._services[&#39;CANCEL_TRAINING&#39;]
        self._api_entity = self._services[&#39;ACTIVATE_ENTITY&#39;]
        # flags used in upload API call Format : [Version Bit,Message Bit(1-Header, 2-Data Chunk),EOF File Flag]
        self._header_chunk = [1, 1, 0]
        self._data_chunk = [1, 2, 0]
        self._data_chunk_eof = [1, 2, 1]
        if entity_id is None:
            self._entity_id = self._get_entity_id(classifier_name)
        else:
            self._entity_id = entity_id
        self.refresh()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _get_entity_id(self, classifier_name):
        &#34;&#34;&#34; Get entity id for given classifier name
                Args:

                    classifier_name (str)  -- Name of the classifier

                Returns:

                    int                -- entity id of the classifier

        &#34;&#34;&#34;

        return self._commcell_object.activate.entity_manager(
            EntityManagerTypes.CLASSIFIERS).get(classifier_name.lower()).entity_id

    def _get_upload_api(self):
        &#34;&#34;&#34;Returns the upload api for this classifier

                Args:
                    None

                Returns:

                    Str     --  Upload API url

        &#34;&#34;&#34;
        api_params = f&#34;?entityId={self.entity_id}&amp;entityKey={self.entity_key}&amp;waitForCopy=true&#34;
        upload_api = self._api_upload % self.trained_ca_cloud_id
        return f&#34;{upload_api}{api_params}&#34;

    def _get_upload_request_id(self, zip_file):
        &#34;&#34;&#34;gets the upload request id

                Args:

                    zip_file        (str)       --  Zip file path

                Returns:

                    str     --  Request id for this upload request

                Raises

                    SDKException:

                            if failed to get request id for this upload request

                            if zip file doesn&#39;t exists on specified path

                            if it is not a valid zip file

        &#34;&#34;&#34;
        request_id = None
        if not zip_file.lower().endswith(&#34;.zip&#34;):
            raise SDKException(&#39;Classifier&#39;, &#39;102&#39;, &#34;Only Zip files are allowed for model trainings&#34;)
        if not os.path.exists(zip_file):
            raise SDKException(&#39;Classifier&#39;, &#39;107&#39;)
        file_stat = os.stat(zip_file)
        xml_req = f&#34;&lt;?xml version=&#39;1.0&#39; encoding=&#39;UTF-8&#39;?&gt;&lt;DM2ContentIndexing_UploadFileReq&gt;&#34; \
                  f&#34;&lt;destEntity clientId=\&#34;0\&#34; _type_=\&#34;3\&#34;/&gt;&#34; \
                  f&#34;&lt;fileContent fileSize=\&#34;{file_stat.st_size}\&#34; fileName=\&#34;{os.path.basename(zip_file)}\&#34;/&gt;&#34; \
                  f&#34;&lt;fileMetaData modifiedTime=\&#34;{str(file_stat.st_mtime).split(&#39;.&#39;,1)[0]}\&#34; dataType=\&#34;1\&#34;/&gt;&#34; \
                  f&#34;&lt;/DM2ContentIndexing_UploadFileReq&gt;&#34;
        req_length = len(xml_req)
        flag_byte = self._get_upload_flag_bit(flags=self._header_chunk, request_data_length=req_length)
        xml_byte = bytearray(xml_req, &#39;utf-8&#39;)
        flag, response = self._cvpysdk_obj.make_request(
            method=&#39;POST&#39;, url=self._get_upload_api(), payload=flag_byte + xml_byte)
        if flag:
            if response.json():
                if &#39;errorCode&#39; in response.json():
                    error_code = int(response.json()[&#39;errorCode&#39;])

                    if error_code != 0:
                        error_string = response.json()[&#39;errorString&#39;]
                        raise SDKException(
                            &#39;Classifier&#39;, &#39;102&#39;, &#39;Failed to get upload request id : {0}&#39;.format(
                                error_string
                            )
                        )

                if &#39;requestId&#39; in response.json():
                    request_id = response.json()[&#39;requestId&#39;]
                return request_id
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _validate_upload_response(self, flag, response, size):
        &#34;&#34;&#34;Validates the upload response for given chunk size

            Args:

                flag        (bool)      --  flag returned from make_request call for upload API

                response     (resp)     --  response from make_request call for upload API

                size        (int)       --  Chunk size to be checked in response

            Raises:

                SDKException:

                    if response is empty or not success

                    if response size is not required input size

        &#34;&#34;&#34;

        if flag:
            if response.json():
                if &#39;errorCode&#39; in response.json():
                    error_code = int(response.json()[&#39;errorCode&#39;])

                    if error_code != 0:
                        error_string = response.json()[&#39;errorString&#39;]
                        raise SDKException(
                            &#39;Classifier&#39;, &#39;102&#39;, &#39;Failed to Upload Full file data : {0}&#39;.format(
                                error_string
                            )
                        )

                if &#39;chunkOffset&#39; in response.json():
                    chunk_offset = response.json()[&#39;chunkOffset&#39;]
                    if chunk_offset != size:
                        raise SDKException(
                            &#39;Classifier&#39;, &#39;102&#39;, &#39;Chunk Offset not matched after upload. Please retry&#39;)
                    return
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _get_upload_flag_bit(self, flags, request_data_length):
        &#34;&#34;&#34;Returns the 7-byte flag used in upload request API

                Args:

                    flags       (list)       --  list containing flag values

                    request_data    (int)   --  length of the request data

                Returns:

                    bytes   --  bytes array representing flags used in upload api call


        &#34;&#34;&#34;
        # we need to put request length of upload api call into 4 byte flag buffer
        flag_byte = bytearray(flags)
        data_size_bytes = request_data_length.to_bytes(4, byteorder=&#39;big&#39;)
        flag_byte = flag_byte + data_size_bytes[::-1]
        return flag_byte

    def monitor_training(self, timeout=30):
        &#34;&#34;&#34;Monitor the training status on this classifier

                Args:

                    timeout     (int)   --  minutes after which the training will not be monitored,

                                                     default: 30

                Returns:

                    bool        --  to denote whether training got completed or not.


        &#34;&#34;&#34;
        start_time = time.time()
        time_limit = start_time + (timeout * 60)
        training_status = None
        while time_limit &gt; start_time:
            start_time = time.time()
            flag, response = self._cvpysdk_obj.make_request(
                &#39;GET&#39;, self._api_entity % self.entity_id)
            if flag:
                if response.json() and &#39;entityDetails&#39; in response.json() and &#39;err&#39; not in response.json():
                    entity_details = response.json()[&#39;entityDetails&#39;][0][&#39;entityXML&#39;]
                    if &#39;classifierDetails&#39; not in entity_details:
                        raise SDKException(&#39;Classifier&#39;, &#39;102&#39;, &#34;Unable to fetch Classifier training details&#34;)
                    training_status = entity_details[&#39;classifierDetails&#39;][&#39;trainingStatus&#39;]
                    if training_status == TrainingStatus.COMPLETED.value:
                        return True
                    elif training_status in [TrainingStatus.NOT_APPLICABLE.value, TrainingStatus.CANCELLED.value,
                                             TrainingStatus.FAILED.value, TrainingStatus.NOT_USABLE.value]:
                        return False
                elif &#39;err&#39; in response.json() and &#39;errLogMessage&#39; in response.json()[&#39;err&#39;]:
                    raise SDKException(&#39;Classifier&#39;, &#39;102&#39;, response.json()[&#39;err&#39;][&#39;errLogMessage&#39;])
                else:
                    raise SDKException(&#39;Classifier&#39;, &#39;108&#39;)
            else:
                self._response_not_success(response)
            time.sleep(30)
        return False

    def start_training(self, wait_for=True):
        &#34;&#34;&#34;Starts training on this classifier

                Args:

                    wait_for            (bool)      --  Specifies whether we need to wait or not for training completion

                Returns:

                    None

                Raises:

                    SDKException:

                        if failed to start training

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_obj.make_request(
            &#39;POST&#39;, self._api_start_training % (self.trained_ca_cloud_id, self.entity_id))
        if flag:
            if response.json() and &#39;entityDetails&#39; in response.json() and &#39;err&#39; not in response.json():
                if wait_for and not self.monitor_training():
                    raise SDKException(&#39;Classifier&#39;, &#39;102&#39;, &#34;Training ended in Error&#34;)
                self.refresh()
                return
            elif &#39;err&#39; in response.json() and &#39;errLogMessage&#39; in response.json()[&#39;err&#39;]:
                raise SDKException(&#39;Classifier&#39;, &#39;102&#39;, response.json()[&#39;err&#39;][&#39;errLogMessage&#39;])
            raise SDKException(&#39;Classifier&#39;, &#39;108&#39;)
        self._response_not_success(response)

    def cancel_training(self):
        &#34;&#34;&#34;Cancels training on this classifier

                Args:

                   None

                Returns:

                    None

                Raises:

                    SDKException:

                        if failed to Cancel training

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_obj.make_request(
            &#39;POST&#39;, self._api_cancel_training % (self.trained_ca_cloud_id, self.entity_id))
        if flag:
            if response.json() and &#39;entityDetails&#39; in response.json() and &#39;err&#39; not in response.json():
                entity_details = response.json()[&#39;entityDetails&#39;][0][&#39;entityXML&#39;]
                if &#39;classifierDetails&#39; not in entity_details:
                    raise SDKException(&#39;Classifier&#39;, &#39;102&#39;, &#34;Unable to fetch Classifier training details&#34;)
                training_status = entity_details[&#39;classifierDetails&#39;].get(&#39;trainingStatus&#39;, 0)
                if training_status != TrainingStatus.CANCELLED.value:
                    raise SDKException(
                        &#39;Classifier&#39;,
                        &#39;102&#39;,
                        f&#34;Wrong training status even after cancelling : {training_status}&#34;)
                self.refresh()
                return
            elif &#39;err&#39; in response.json() and &#39;errLogMessage&#39; in response.json()[&#39;err&#39;]:
                raise SDKException(&#39;Classifier&#39;, &#39;102&#39;, response.json()[&#39;err&#39;][&#39;errLogMessage&#39;])
            raise SDKException(&#39;Classifier&#39;, &#39;109&#39;)
        self._response_not_success(response)

    def modify(self, classifier_new_name=None, description=&#34;Modified from CvPySDK&#34;, enabled=True):
        &#34;&#34;&#34;Modifies the classifier entity

                Args:

                    classifier_new_name         (str)       --  New name for classifier

                    description                 (str)       --  Description string for classifier

                    enabled                     (bool)      --  flag to denote whether classifier is enabled or disabled

                Returns:

                    None

                Raises:

                    SDKException:

                            if failed to modify the classifier

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_obj.make_request(
            &#39;GET&#39;, self._api_entity % self.entity_id)
        if flag:
            if response.json() and &#39;entityDetails&#39; in response.json() and &#39;err&#39; not in response.json():
                request = copy.deepcopy(response.json()[&#39;entityDetails&#39;][0])
                if classifier_new_name:
                    request[&#39;entityName&#39;] = classifier_new_name
                    self._classifier_name = classifier_new_name
                if description:
                    request[&#39;description&#39;] = description
                if enabled:
                    request[&#39;enabled&#39;] = True
                else:
                    request[&#39;enabled&#39;] = False
                flag, response = self._cvpysdk_obj.make_request(
                    &#39;PUT&#39;, self._api_entity % self.entity_id, request)
                if flag:
                    if response.json() and &#39;entityDetails&#39; in response.json() and &#39;err&#39; not in response.json():
                        self.refresh()
                        return
                    elif &#39;err&#39; in response.json() and &#39;errLogMessage&#39; in response.json()[&#39;err&#39;]:
                        raise SDKException(&#39;Classifier&#39;, &#39;102&#39;, response.json()[&#39;err&#39;][&#39;errLogMessage&#39;])

                    raise SDKException(&#39;Classifier&#39;, &#39;106&#39;)

                self._response_not_success(response)
            elif &#39;err&#39; in response.json() and &#39;errLogMessage&#39; in response.json()[&#39;err&#39;]:
                raise SDKException(&#39;Classifier&#39;, &#39;102&#39;, response.json()[&#39;err&#39;][&#39;errLogMessage&#39;])

            raise SDKException(&#39;Classifier&#39;, &#39;106&#39;)

        self._response_not_success(response)

    def upload_data(self, zip_file, start_training=False):
        &#34;&#34;&#34;Uploads the model training data set zip file to content analyzer machine

                Args:

                    zip_file        (str)       --      Zip file path

                    start_training  (bool)      --      Denotes whether to start training on classifier or not

                Returns:

                    None

                Raises

                    SDKException:

                            if failed to upload the file


        &#34;&#34;&#34;
        chunk_size = 1048576  # 1MB
        request_id = self._get_upload_request_id(zip_file=zip_file)
        file_stat = os.stat(zip_file)
        req_length = len(request_id)
        file_byte = open(zip_file, &#39;rb&#39;)
        flag_byte = self._get_upload_flag_bit(flags=self._data_chunk_eof, request_data_length=req_length)
        xml_byte = bytearray(request_id, &#39;utf-8&#39;)
        if file_stat.st_size &lt;= chunk_size:
            # full file upload
            data_byte = file_byte.read()
            file_byte.close()
            payload = flag_byte + xml_byte + data_byte
            flag, response = self._cvpysdk_obj.make_request(
                method=&#39;POST&#39;, url=self._get_upload_api(), payload=payload)
            self._validate_upload_response(flag=flag, response=response, size=file_stat.st_size)
        else:
            # chunk based upload
            file_size = file_stat.st_size
            chunk_count = 1
            while file_size &gt; chunk_size:
                flag_byte = self._get_upload_flag_bit(flags=self._data_chunk, request_data_length=req_length)
                file_size = file_size - chunk_size
                data_byte = file_byte.read(chunk_size)
                payload = flag_byte + xml_byte + data_byte
                flag, response = self._cvpysdk_obj.make_request(
                    method=&#39;POST&#39;, url=self._get_upload_api(), payload=payload)
                self._validate_upload_response(flag=flag, response=response, size=chunk_size * chunk_count)
                chunk_count = chunk_count + 1
            flag_byte = self._get_upload_flag_bit(flags=self._data_chunk_eof, request_data_length=req_length)
            data_byte = file_byte.read(file_size)
            flag, response = self._cvpysdk_obj.make_request(
                method=&#39;POST&#39;, url=self._get_upload_api(), payload=flag_byte + xml_byte + data_byte)
            self._validate_upload_response(flag=flag, response=response, size=file_stat.st_size)
            file_byte.close()
        if start_training:
            self.start_training(wait_for=True)

    def _get_entity_properties(self):
        &#34;&#34;&#34; Get classifier entity properties
                Args:

                    None

                Returns:

                    None

        &#34;&#34;&#34;
        classifier_obj = self._commcell_object.activate.entity_manager(
            EntityManagerTypes.CLASSIFIERS)
        # Refresh before refetching properties
        classifier_obj.refresh()
        regex_entity_dict = classifier_obj.get_properties(
            self._classifier_name.lower())
        self._display_name = regex_entity_dict[&#39;displayName&#39;]
        self._category_name = regex_entity_dict[&#39;categoryName&#39;]
        self._entity_id = regex_entity_dict[&#39;entityId&#39;]
        self._is_enabled = regex_entity_dict[&#39;enabled&#39;]
        self._entity_key = regex_entity_dict[&#39;entityKey&#39;]
        self._entity_type = regex_entity_dict[&#39;entityType&#39;]
        self._entity_xml = regex_entity_dict[&#39;entityXML&#39;]
        if &#39;classifierDetails&#39; in self._entity_xml:
            self._training_status = int(self._entity_xml[&#39;classifierDetails&#39;].get(&#39;trainingStatus&#39;, 0))
            self._training_accuracy = self._entity_xml[&#39;classifierDetails&#39;].get(&#39;classifierAccuracy&#39;, 0)
            self._sample_details[&#39;totalSamples&#39;] = self._entity_xml[&#39;classifierDetails&#39;].get(&#39;totalSamples&#39;, 0)
            self._sample_details[&#39;trainingSamplesUsed&#39;] = self._entity_xml[&#39;classifierDetails&#39;].get(
                &#39;trainingSamplesUsed&#39;, 0)
            self._sample_details[&#39;validationSamplesUsed&#39;] = self._entity_xml[&#39;classifierDetails&#39;].get(
                &#39;validationSamplesUsed&#39;, 0)
            if &#39;CAUsedInTraining&#39; in self._entity_xml[&#39;classifierDetails&#39;]:
                trained_ca = self._entity_xml[&#39;classifierDetails&#39;][&#39;CAUsedInTraining&#39;]
                # Mandatory trained CA fields. so no need to use dict.get()
                self._trained_ca_cloud_id = int(trained_ca[&#39;clientId&#39;])
                self._last_training_time = trained_ca.get(&#39;lastModelTrainTime&#39;, 0)
            if &#39;syncedContentAnalyzers&#39; in self._entity_xml[&#39;classifierDetails&#39;] and self._entity_xml[
                    &#39;classifierDetails&#39;][&#39;syncedContentAnalyzers&#39;] is not None:
                sync_ca = self._entity_xml[&#39;classifierDetails&#39;][&#39;syncedContentAnalyzers&#39;].get(&#39;contentAnalyzerList&#39;, [])
                for content_analyzer in sync_ca:
                    self._sync_ca_client_id.append(content_analyzer[&#39;clientId&#39;])
        return regex_entity_dict

    @property
    def entity_id(self):
        &#34;&#34;&#34;Returns the value of the entity id attribute.&#34;&#34;&#34;
        return self._entity_id

    @property
    def entity_type(self):
        &#34;&#34;&#34;Returns the entity type attribute.&#34;&#34;&#34;
        return self._entity_type

    @property
    def category_name(self):
        &#34;&#34;&#34;Returns the entity category name attribute.&#34;&#34;&#34;
        return self._category_name

    @property
    def display_name(self):
        &#34;&#34;&#34;Returns the entity display name attribute.&#34;&#34;&#34;
        return self._display_name

    @property
    def is_enabled(self):
        &#34;&#34;&#34;Returns the entity isenabled attribute.&#34;&#34;&#34;
        return self._is_enabled

    @property
    def entity_key(self):
        &#34;&#34;&#34;Returns the entity key attribute.&#34;&#34;&#34;
        return self._entity_key

    @property
    def entity_xml(self):
        &#34;&#34;&#34;Returns the entity xml attribute.&#34;&#34;&#34;
        return self._entity_xml

    @property
    def trained_ca_cloud_id(self):
        &#34;&#34;&#34;Returns the cloudid attribute for trained CA&#34;&#34;&#34;
        return self._trained_ca_cloud_id

    @property
    def training_status(self):
        &#34;&#34;&#34;Returns the training status attribute&#34;&#34;&#34;
        return TrainingStatus(self._training_status).name

    @property
    def training_accuracy(self):
        &#34;&#34;&#34;Returns the training accuracy attribute&#34;&#34;&#34;
        return self._training_accuracy * 100

    @property
    def last_training_time(self):
        &#34;&#34;&#34;Returns the last training time attribute&#34;&#34;&#34;
        return self._last_training_time

    @property
    def sycn_ca_client_id(self):
        &#34;&#34;&#34;Returns list of Model synced CA client id attribute&#34;&#34;&#34;
        return self._sync_ca_client_id

    @property
    def sample_details(self):
        &#34;&#34;&#34;Returns dict containing model sample count details used for this classifier training&#34;&#34;&#34;
        return self._sample_details

    def refresh(self):
        &#34;&#34;&#34;Refresh the classifier details for associated object&#34;&#34;&#34;
        self._get_entity_properties()</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.activateapps.entity_manager.ActivateEntities"><code class="flex name class">
<span>class <span class="ident">ActivateEntities</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing all the regex entities in the commcell.</p>
<p>Initializes an instance of the ActivateEntities class.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the commcell class</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the ActivateEntities class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L304-L685" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class ActivateEntities(object):
    &#34;&#34;&#34;Class for representing all the regex entities in the commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initializes an instance of the ActivateEntities class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

            Returns:
                object  -   instance of the ActivateEntities class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._regex_entities = None
        self._entities_containers = None
        self._api_get_all_regex_entities = self._services[&#39;ACTIVATE_ENTITIES&#39;]
        self._api_get_containers = self._services[&#39;ACTIVATE_ENTITY_CONTAINER&#39;]
        self._api_create_regex_entity = self._api_get_all_regex_entities
        self._api_delete_regex_entity = self._services[&#39;ACTIVATE_ENTITY&#39;]
        self.refresh()

    def add(self, entity_name, entity_regex, entity_keywords, entity_flag, is_derived=False, parent_entity=None):
        &#34;&#34;&#34;Adds the specified regex entity name in the commcell

                    Args:
                        entity_name (str)      --  name of the regex entity

                        entity_regex (str)     --  Regex for the entity

                        entity_keywords (str)  --  Keywords for the entity

                        entity_flag (int)      --  Sensitivity flag value for entity
                                                        5-Highly sensitive
                                                        3-Moderate sensitive
                                                        1-Low sensitive

                        is_derived (bool)      --  represents whether it is derived entity or not

                        parent_entity(int)     -- entity id of the parent entity in case of derived entity

                    Returns:
                        None

                    Raises:
                        SDKException:

                                if response is empty

                                if response is not success

                                if unable to add regex entity in commcell

                                if entity_flag is not in proper allowed values [1,3,5]

                                if input data type is not valid
                &#34;&#34;&#34;
        if not isinstance(entity_name, str) or not isinstance(entity_regex, str) \
                or not isinstance(entity_keywords, str):
            raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)
        if entity_flag not in [1, 3, 5]:
            raise SDKException(&#39;ActivateEntity&#39;, &#39;102&#39;, &#39;Unsupported entity flag value&#39;)
        request_json = ActivateEntityConstants.REQUEST_JSON
        request_json[&#39;regularExpression&#39;] = &#34;{\&#34;entity_key\&#34;:\&#34;&#34; + \
            entity_name + &#34;\&#34;,\&#34;entity_regex\&#34;:\&#34;&#34; + entity_regex + &#34;\&#34;}&#34;
        request_json[&#39;flags&#39;] = entity_flag
        request_json[&#39;entityName&#39;] = entity_name
        request_json[&#39;entityXML&#39;][&#39;keywords&#39;] = entity_keywords
        if is_derived:
            request_json[&#39;regularExpression&#39;] = &#34;{\&#34;entity_key\&#34;:\&#34;&#34; + entity_name + &#34;\&#34;}&#34;
            request_json[&#39;entityType&#39;] = 3
            if isinstance(parent_entity, int):
                request_json[&#39;parentEntityId&#39;] = parent_entity
            elif isinstance(parent_entity, str):
                request_json[&#39;parentEntityId&#39;] = self._regex_entities[parent_entity][&#39;entityId&#39;]
            else:
                raise SDKException(&#39;ActivateEntity&#39;, &#39;102&#39;, &#39;Unsupported parent entity id type provided&#39;)

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._api_create_regex_entity, request_json
        )

        if flag:
            if response.json() and &#39;entityDetails&#39; in response.json() and &#39;err&#39; not in response.json():
                self.refresh()
                return
            raise SDKException(&#39;ActivateEntity&#39;, &#39;104&#39;)
        self._response_not_success(response)

    def delete(self, entity_name):
        &#34;&#34;&#34;deletes the specified regex entity name in the commcell

                    Args:
                        entity_name (str)      --  name of the regex entity

                    Returns:
                        None

                    Raises:
                        SDKException:

                                if response is empty

                                if response is not success

                                if unable to delete regex entity in commcell

                                if unable to find entity name in the commcell

                                if data type of entity_name is invalid


                &#34;&#34;&#34;
        if not isinstance(entity_name, str):
            raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)
        if entity_name not in self._regex_entities:
            raise SDKException(&#39;ActivateEntity&#39;, &#39;102&#39;, &#39;Unable to find given regex entity name in the commcell&#39;)

        flag, response = self._cvpysdk_object.make_request(
            &#39;DELETE&#39;, self._api_delete_regex_entity % self._regex_entities[entity_name][&#39;entityId&#39;]
        )

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json() and response.json()[&#39;errorCode&#39;] == 0:
                self.refresh()
                return
            raise SDKException(&#39;ActivateEntity&#39;, &#39;105&#39;)
        self._response_not_success(response)

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def get_properties(self, entity_name):
        &#34;&#34;&#34;Returns a properties of the specified regex entity name.

            Args:
                entity_name (str)  --  name of the regex entity

            Returns:
                dict -  properties for the given regex entity name


        &#34;&#34;&#34;
        return self._regex_entities[entity_name]

    def _get_all_activate_entities(self):
        &#34;&#34;&#34;Gets the list of all regex entities associated with this commcell.

            Returns:
                dict    -   dictionary consisting of dictionaries, where each dictionary stores the
                                details of a single regex entity

                    {
                        &#34;entityDetails&#34;: [
                            {
                                &#34;displayName&#34;: &#34;US Social Security number&#34;,
                                &#34;flags&#34;: 5,
                                &#34;description&#34;: &#34;&#34;,
                                &#34;categoryName&#34;: &#34;US&#34;,
                                &#34;enabled&#34;: true,
                                &#34;entityName&#34;: &#34;SSN&#34;,
                                &#34;attribute&#34;: 3,
                                &#34;entityType&#34;: 2,
                                &#34;entityKey&#34;: &#34;ssn&#34;,
                                &#34;entityId&#34;: 1111,
                                &#34;entityXML&#34;: {
                                    &#34;keywords&#34;: &#34;Social Security,Social Security#,Soc Sec,SSN,SSNS,SSN#,SS#,SSID&#34;,
                                    &#34;entityKey&#34;: &#34;ssn&#34;,
                                    &#34;isSystemDefinedEntity&#34;: true,
                                    &#34;inheritBaseWords&#34;: false
                                }
                            },
                            {
                                &#34;displayName&#34;: &#34;Person Name&#34;,
                                &#34;flags&#34;: 1,
                                &#34;description&#34;: &#34;Name of a person.&#34;,
                                &#34;categoryName&#34;: &#34;Generic&#34;,
                                &#34;enabled&#34;: true,
                                &#34;entityName&#34;: &#34;Person&#34;,
                                &#34;attribute&#34;: 3,
                                &#34;entityType&#34;: 1,
                                &#34;entityKey&#34;: &#34;person&#34;,
                                &#34;entityId&#34;: 1112,
                                &#34;entityXML&#34;: {
                                    &#34;keywords&#34;: &#34;&#34;,
                                    &#34;entityKey&#34;: &#34;person&#34;,
                                    &#34;inheritBaseWords&#34;: false
                                }
                            }
                            ]
                            }

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._api_get_containers
        )
        if flag:
            if response.json() and &#39;containerTypesList&#39; in response.json():
                self._entities_containers = response.json()[&#39;containerTypesList&#39;]
            else:
                raise SDKException(&#39;ActivateEntity&#39;, &#39;107&#39;)
        else:
            self._response_not_success(response)

        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._api_get_all_regex_entities
        )

        if flag:
            if response.json() and &#39;entityDetails&#39; in response.json():
                return self._get_regex_entity_from_collections(response.json())
            raise SDKException(&#39;ActivateEntity&#39;, &#39;103&#39;)
        self._response_not_success(response)

    def _process_entity_containers(self, entity_name, container_name):
        &#34;&#34;&#34;Returns container details for given entity name &amp; container name

            Args:

                entity_name         (str)       --  Entity name

                container_name      (str)       --  Container name

            Returns:

                dict    --  Container details of entity
        &#34;&#34;&#34;
        output = {}
        for dept in self._entities_containers:
            items_list = dept[&#39;tagSetsAndItems&#39;]
            for country in items_list:
                tags_list = country.get(&#39;tags&#39;,{})
                for tag in tags_list:
                    if entity_name.lower() == tag[&#39;entityDetail&#39;][&#39;entityName&#39;].lower() and \
                            container_name.lower() == country[&#39;container&#39;][&#39;containerName&#39;].lower():
                        output[&#39;tags&#39;] = [tag]
                        output[&#39;container&#39;] = country[&#39;container&#39;]
                        return output
        return output

    def _get_regex_entity_from_collections(self, collections):
        &#34;&#34;&#34;Extracts all the regex entities, and their details from the list of collections given,
            and returns the dictionary of all regex entities

            Args:
                collections     (list)  --  list of all collections

            Returns:
                dict    -   dictionary consisting of dictionaries, where each dictionary stores the
                                details of a single regex entity

        &#34;&#34;&#34;
        _regex_entity = {}
        for regex_entity in collections[&#39;entityDetails&#39;]:
            regex_entity_dict = {}
            regex_entity_dict[&#39;displayName&#39;] = regex_entity.get(&#39;displayName&#39;, &#34;&#34;)
            regex_entity_dict[&#39;entityKey&#39;] = regex_entity.get(&#39;entityKey&#39;, &#34;&#34;)
            regex_entity_dict[&#39;categoryName&#39;] = regex_entity.get(&#39;categoryName&#39;, &#34;&#34;)
            if regex_entity_dict[&#39;categoryName&#39;] is not None:
                regex_entity_dict[&#39;containerDetails&#39;] = self._process_entity_containers(
                    entity_name=regex_entity_dict[&#39;displayName&#39;], container_name=regex_entity_dict[&#39;categoryName&#39;])
            regex_entity_dict[&#39;entityXML&#39;] = regex_entity.get(&#39;entityXML&#39;, &#34;&#34;)
            regex_entity_dict[&#39;entityId&#39;] = regex_entity.get(&#39;entityId&#39;, 0)
            regex_entity_dict[&#39;flags&#39;] = regex_entity.get(&#39;flags&#39;, 0)
            regex_entity_dict[&#39;entityType&#39;] = regex_entity.get(&#39;entityType&#39;, 0)
            regex_entity_dict[&#39;enabled&#39;] = regex_entity.get(&#39;enabled&#39;, False)
            _regex_entity[regex_entity[&#39;entityName&#39;]] = regex_entity_dict
        return _regex_entity

    def refresh(self):
        &#34;&#34;&#34;Refresh the activate regex entities associated with the commcell.&#34;&#34;&#34;
        self._regex_entities = self._get_all_activate_entities()

    def get(self, entity_name):
        &#34;&#34;&#34;Returns a ActivateEntity object for the given regex entity name.

            Args:
                entity_name (str)  --  name of the regex entity

            Returns:

                obj                 -- Object of ActivateEntity class

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

                    if entity_name is not of type string


        &#34;&#34;&#34;
        if not isinstance(entity_name, str):
            raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)

        if self.has_entity(entity_name):
            entity_id = self._regex_entities[entity_name][&#39;entityId&#39;]
            return ActivateEntity(self._commcell_object, entity_name, entity_id)
        raise SDKException(&#39;ActivateEntity&#39;, &#39;102&#39;, &#34;Unable to get ActivateEntity class object&#34;)

    def get_entity_ids(self, entity_name):
        &#34;&#34;&#34;Returns a list of entity id for the given regex entity name list.

            Args:
                entity_name (list)  --  names of the regex entity

            Returns:

                list                -- entity id&#39;s for the given entity names


        &#34;&#34;&#34;
        if not isinstance(entity_name, list):
            raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)
        entity_ids = []
        for regex_entity in entity_name:
            if regex_entity in self._regex_entities:
                entity_ids.append(self._regex_entities[regex_entity][&#39;entityId&#39;])
            else:
                raise SDKException(
                    &#39;ActivateEntity&#39;, &#39;102&#39;, f&#34;Unable to find entity id for given entity name :{regex_entity}&#34;)
        return entity_ids

    def get_entity_keys(self, entity_name):
        &#34;&#34;&#34;Returns a list of entity keys for the given regex entity name list.

            Args:
                entity_name (list)  --  names of the regex entity

            Returns:

                list                -- entity keys for the given entity names


        &#34;&#34;&#34;
        if not isinstance(entity_name, list):
            raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)
        entity_keys = []
        for regex_entity in entity_name:
            if regex_entity in self._regex_entities:
                entity_keys.append(self._regex_entities[regex_entity][&#39;entityKey&#39;])
            else:
                raise SDKException(
                    &#39;ActivateEntity&#39;, &#39;102&#39;, f&#34;Unable to find entity keys for given entity name :{regex_entity}&#34;)
        return entity_keys

    def has_entity(self, entity_name):
        &#34;&#34;&#34;Checks if a regex entity exists in the commcell with the input name.

            Args:
                entity_name (str)  --  name of the regex entity

            Returns:
                bool - boolean output whether the regex entity exists in the commcell or not

            Raises:
                SDKException:
                    if type of the regex entity name argument is not string

        &#34;&#34;&#34;
        if not isinstance(entity_name, str):
            raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)

        return self._regex_entities and entity_name.lower() in map(str.lower, self._regex_entities)</code></pre>
</details>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.activateapps.entity_manager.ActivateEntities.add"><code class="name flex">
<span>def <span class="ident">add</span></span>(<span>self, entity_name, entity_regex, entity_keywords, entity_flag, is_derived=False, parent_entity=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds the specified regex entity name in the commcell</p>
<h2 id="args">Args</h2>
<p>entity_name (str)
&ndash;
name of the regex entity</p>
<p>entity_regex (str)
&ndash;
Regex for the entity</p>
<p>entity_keywords (str)
&ndash;
Keywords for the entity</p>
<p>entity_flag (int)
&ndash;
Sensitivity flag value for entity
5-Highly sensitive
3-Moderate sensitive
1-Low sensitive</p>
<p>is_derived (bool)
&ndash;
represents whether it is derived entity or not</p>
<p>parent_entity(int)
&ndash; entity id of the parent entity in case of derived entity</p>
<h2 id="returns">Returns</h2>
<p>None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if response is empty

    if response is not success

    if unable to add regex entity in commcell

    if entity_flag is not in proper allowed values [1,3,5]

    if input data type is not valid
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L329-L394" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add(self, entity_name, entity_regex, entity_keywords, entity_flag, is_derived=False, parent_entity=None):
    &#34;&#34;&#34;Adds the specified regex entity name in the commcell

                Args:
                    entity_name (str)      --  name of the regex entity

                    entity_regex (str)     --  Regex for the entity

                    entity_keywords (str)  --  Keywords for the entity

                    entity_flag (int)      --  Sensitivity flag value for entity
                                                    5-Highly sensitive
                                                    3-Moderate sensitive
                                                    1-Low sensitive

                    is_derived (bool)      --  represents whether it is derived entity or not

                    parent_entity(int)     -- entity id of the parent entity in case of derived entity

                Returns:
                    None

                Raises:
                    SDKException:

                            if response is empty

                            if response is not success

                            if unable to add regex entity in commcell

                            if entity_flag is not in proper allowed values [1,3,5]

                            if input data type is not valid
            &#34;&#34;&#34;
    if not isinstance(entity_name, str) or not isinstance(entity_regex, str) \
            or not isinstance(entity_keywords, str):
        raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)
    if entity_flag not in [1, 3, 5]:
        raise SDKException(&#39;ActivateEntity&#39;, &#39;102&#39;, &#39;Unsupported entity flag value&#39;)
    request_json = ActivateEntityConstants.REQUEST_JSON
    request_json[&#39;regularExpression&#39;] = &#34;{\&#34;entity_key\&#34;:\&#34;&#34; + \
        entity_name + &#34;\&#34;,\&#34;entity_regex\&#34;:\&#34;&#34; + entity_regex + &#34;\&#34;}&#34;
    request_json[&#39;flags&#39;] = entity_flag
    request_json[&#39;entityName&#39;] = entity_name
    request_json[&#39;entityXML&#39;][&#39;keywords&#39;] = entity_keywords
    if is_derived:
        request_json[&#39;regularExpression&#39;] = &#34;{\&#34;entity_key\&#34;:\&#34;&#34; + entity_name + &#34;\&#34;}&#34;
        request_json[&#39;entityType&#39;] = 3
        if isinstance(parent_entity, int):
            request_json[&#39;parentEntityId&#39;] = parent_entity
        elif isinstance(parent_entity, str):
            request_json[&#39;parentEntityId&#39;] = self._regex_entities[parent_entity][&#39;entityId&#39;]
        else:
            raise SDKException(&#39;ActivateEntity&#39;, &#39;102&#39;, &#39;Unsupported parent entity id type provided&#39;)

    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._api_create_regex_entity, request_json
    )

    if flag:
        if response.json() and &#39;entityDetails&#39; in response.json() and &#39;err&#39; not in response.json():
            self.refresh()
            return
        raise SDKException(&#39;ActivateEntity&#39;, &#39;104&#39;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.ActivateEntities.delete"><code class="name flex">
<span>def <span class="ident">delete</span></span>(<span>self, entity_name)</span>
</code></dt>
<dd>
<div class="desc"><p>deletes the specified regex entity name in the commcell</p>
<h2 id="args">Args</h2>
<p>entity_name (str)
&ndash;
name of the regex entity</p>
<h2 id="returns">Returns</h2>
<p>None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if response is empty

    if response is not success

    if unable to delete regex entity in commcell

    if unable to find entity name in the commcell

    if data type of entity_name is invalid
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L396-L434" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete(self, entity_name):
    &#34;&#34;&#34;deletes the specified regex entity name in the commcell

                Args:
                    entity_name (str)      --  name of the regex entity

                Returns:
                    None

                Raises:
                    SDKException:

                            if response is empty

                            if response is not success

                            if unable to delete regex entity in commcell

                            if unable to find entity name in the commcell

                            if data type of entity_name is invalid


            &#34;&#34;&#34;
    if not isinstance(entity_name, str):
        raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)
    if entity_name not in self._regex_entities:
        raise SDKException(&#39;ActivateEntity&#39;, &#39;102&#39;, &#39;Unable to find given regex entity name in the commcell&#39;)

    flag, response = self._cvpysdk_object.make_request(
        &#39;DELETE&#39;, self._api_delete_regex_entity % self._regex_entities[entity_name][&#39;entityId&#39;]
    )

    if flag:
        if response.json() and &#39;errorCode&#39; in response.json() and response.json()[&#39;errorCode&#39;] == 0:
            self.refresh()
            return
        raise SDKException(&#39;ActivateEntity&#39;, &#39;105&#39;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.ActivateEntities.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, entity_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a ActivateEntity object for the given regex entity name.</p>
<h2 id="args">Args</h2>
<p>entity_name (str)
&ndash;
name of the regex entity</p>
<h2 id="returns">Returns</h2>
<p>obj
&ndash; Object of ActivateEntity class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if response is empty</p>
<pre><code>if response is not success

if entity_name is not of type string
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L594-L620" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, entity_name):
    &#34;&#34;&#34;Returns a ActivateEntity object for the given regex entity name.

        Args:
            entity_name (str)  --  name of the regex entity

        Returns:

            obj                 -- Object of ActivateEntity class

        Raises:
            SDKException:
                if response is empty

                if response is not success

                if entity_name is not of type string


    &#34;&#34;&#34;
    if not isinstance(entity_name, str):
        raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)

    if self.has_entity(entity_name):
        entity_id = self._regex_entities[entity_name][&#39;entityId&#39;]
        return ActivateEntity(self._commcell_object, entity_name, entity_id)
    raise SDKException(&#39;ActivateEntity&#39;, &#39;102&#39;, &#34;Unable to get ActivateEntity class object&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.ActivateEntities.get_entity_ids"><code class="name flex">
<span>def <span class="ident">get_entity_ids</span></span>(<span>self, entity_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a list of entity id for the given regex entity name list.</p>
<h2 id="args">Args</h2>
<p>entity_name (list)
&ndash;
names of the regex entity</p>
<h2 id="returns">Returns</h2>
<p>list
&ndash; entity id's for the given entity names</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L622-L643" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_entity_ids(self, entity_name):
    &#34;&#34;&#34;Returns a list of entity id for the given regex entity name list.

        Args:
            entity_name (list)  --  names of the regex entity

        Returns:

            list                -- entity id&#39;s for the given entity names


    &#34;&#34;&#34;
    if not isinstance(entity_name, list):
        raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)
    entity_ids = []
    for regex_entity in entity_name:
        if regex_entity in self._regex_entities:
            entity_ids.append(self._regex_entities[regex_entity][&#39;entityId&#39;])
        else:
            raise SDKException(
                &#39;ActivateEntity&#39;, &#39;102&#39;, f&#34;Unable to find entity id for given entity name :{regex_entity}&#34;)
    return entity_ids</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.ActivateEntities.get_entity_keys"><code class="name flex">
<span>def <span class="ident">get_entity_keys</span></span>(<span>self, entity_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a list of entity keys for the given regex entity name list.</p>
<h2 id="args">Args</h2>
<p>entity_name (list)
&ndash;
names of the regex entity</p>
<h2 id="returns">Returns</h2>
<p>list
&ndash; entity keys for the given entity names</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L645-L666" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_entity_keys(self, entity_name):
    &#34;&#34;&#34;Returns a list of entity keys for the given regex entity name list.

        Args:
            entity_name (list)  --  names of the regex entity

        Returns:

            list                -- entity keys for the given entity names


    &#34;&#34;&#34;
    if not isinstance(entity_name, list):
        raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)
    entity_keys = []
    for regex_entity in entity_name:
        if regex_entity in self._regex_entities:
            entity_keys.append(self._regex_entities[regex_entity][&#39;entityKey&#39;])
        else:
            raise SDKException(
                &#39;ActivateEntity&#39;, &#39;102&#39;, f&#34;Unable to find entity keys for given entity name :{regex_entity}&#34;)
    return entity_keys</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.ActivateEntities.get_properties"><code class="name flex">
<span>def <span class="ident">get_properties</span></span>(<span>self, entity_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a properties of the specified regex entity name.</p>
<h2 id="args">Args</h2>
<p>entity_name (str)
&ndash;
name of the regex entity</p>
<h2 id="returns">Returns</h2>
<p>dict -
properties for the given regex entity name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L447-L458" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_properties(self, entity_name):
    &#34;&#34;&#34;Returns a properties of the specified regex entity name.

        Args:
            entity_name (str)  --  name of the regex entity

        Returns:
            dict -  properties for the given regex entity name


    &#34;&#34;&#34;
    return self._regex_entities[entity_name]</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.ActivateEntities.has_entity"><code class="name flex">
<span>def <span class="ident">has_entity</span></span>(<span>self, entity_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks if a regex entity exists in the commcell with the input name.</p>
<h2 id="args">Args</h2>
<p>entity_name (str)
&ndash;
name of the regex entity</p>
<h2 id="returns">Returns</h2>
<p>bool - boolean output whether the regex entity exists in the commcell or not</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the regex entity name argument is not string</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L668-L685" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_entity(self, entity_name):
    &#34;&#34;&#34;Checks if a regex entity exists in the commcell with the input name.

        Args:
            entity_name (str)  --  name of the regex entity

        Returns:
            bool - boolean output whether the regex entity exists in the commcell or not

        Raises:
            SDKException:
                if type of the regex entity name argument is not string

    &#34;&#34;&#34;
    if not isinstance(entity_name, str):
        raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)

    return self._regex_entities and entity_name.lower() in map(str.lower, self._regex_entities)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.ActivateEntities.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the activate regex entities associated with the commcell.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L590-L592" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the activate regex entities associated with the commcell.&#34;&#34;&#34;
    self._regex_entities = self._get_all_activate_entities()</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.ActivateEntity"><code class="flex name class">
<span>class <span class="ident">ActivateEntity</span></span>
<span>(</span><span>commcell_object, entity_name, entity_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for performing operations on a single regex entity</p>
<p>Initialize an object of the ActivateEntity class.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the commcell class</p>
<p>entity_name
(str)
&ndash;
name of the regex entity</p>
<p>entity_id
(str)
&ndash;
id of the regex entity
default: None</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the ActivateEntity class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L688-L883" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class ActivateEntity(object):
    &#34;&#34;&#34;Class for performing operations on a single regex entity&#34;&#34;&#34;

    def __init__(self, commcell_object, entity_name, entity_id=None):
        &#34;&#34;&#34;Initialize an object of the ActivateEntity class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

                entity_name     (str)           --  name of the regex entity

                entity_id       (str)           --  id of the regex entity
                    default: None

            Returns:
                object  -   instance of the ActivateEntity class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._services = commcell_object._services
        self._cvpysdk_obj = self._commcell_object._cvpysdk_object
        self._entity_name = entity_name
        self._entity_id = None
        self._display_name = None
        self._entity_type = None
        self._is_enabled = None
        self._entity_key = None
        self._entity_xml = None
        self._category_name = None
        self._container_details = None
        if entity_id is None:
            self._entity_id = self._get_entity_id(entity_name)
        else:
            self._entity_id = entity_id
        self.refresh()
        self._api_modify_regex_entity = self._services[&#39;ACTIVATE_ENTITY&#39;]

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def modify(self, entity_regex, entity_keywords, entity_flag, is_derived=False, parent_entity=None):
        &#34;&#34;&#34;Modifies the specified regex entity details

                    Args:

                        entity_regex (str)     --  Regex for the entity

                        entity_keywords (str)  --  Keywords for the entity

                        entity_flag (int)      --  Sensitivity flag value for entity
                                                        5-Highly sensitive
                                                        3-Moderate sensitive
                                                        1-Low sensitive

                        is_derived (bool)      --  represents whether it is derived entity or not

                        parent_entity(int)     -- entity id of the parent entity in case of derived entity

                    Returns:
                        None

                    Raises:
                        SDKException:

                                if response is empty

                                if response is not success

                                if unable to modify regex entity in commcell

                                if input entity_keywords &amp; entity_regex is not string

                                if entity_flag value is not in allowed values[1,3,5]


                &#34;&#34;&#34;
        if not isinstance(entity_regex, str) \
                or not isinstance(entity_keywords, str):
            raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)
        if entity_flag not in [1, 3, 5]:
            raise SDKException(&#39;ActivateEntity&#39;, &#39;102&#39;, &#39;Unsupported entity flag value&#39;)
        request_json = ActivateEntityConstants.REQUEST_JSON
        request_json[&#39;regularExpression&#39;] = &#34;{\&#34;entity_key\&#34;:\&#34;&#34; + \
            self._entity_name + &#34;\&#34;,\&#34;entity_regex\&#34;:\&#34;&#34; + entity_regex + &#34;\&#34;}&#34;
        request_json[&#39;flags&#39;] = entity_flag
        request_json[&#39;entityName&#39;] = self._entity_name
        request_json[&#39;entityXML&#39;][&#39;keywords&#39;] = entity_keywords
        if is_derived:
            request_json[&#39;regularExpression&#39;] = &#34;{\&#34;entity_key\&#34;:\&#34;&#34; + self._entity_name + &#34;\&#34;}&#34;
            request_json[&#39;entityType&#39;] = 3
            if isinstance(parent_entity, int):
                request_json[&#39;parentEntityId&#39;] = parent_entity
            elif isinstance(parent_entity, str):
                request_json[&#39;parentEntityId&#39;] = ActivateEntities.get(
                    self._commcell_object, entity_name=parent_entity).entity_id
            else:
                raise SDKException(&#39;ActivateEntity&#39;, &#39;102&#39;, &#39;Unsupported parent entity id type provided&#39;)

        flag, response = self._cvpysdk_obj.make_request(
            &#39;PUT&#39;, (self._api_modify_regex_entity % self.entity_id), request_json
        )

        if flag:
            if response.json() and &#39;entityDetails&#39; in response.json() and &#39;err&#39; not in response.json():
                return
            raise SDKException(&#39;ActivateEntity&#39;, &#39;106&#39;)
        self._response_not_success(response)

    def _get_entity_id(self, entity_name):
        &#34;&#34;&#34; Get regex entity id for given entity name
                Args:

                    entity_name (str)  -- Name of the regex entity

                Returns:

                    int                -- id of the regex entity

        &#34;&#34;&#34;

        return self._commcell_object.activate.entity_manager().get(entity_name).entity_id

    def _get_entity_properties(self):
        &#34;&#34;&#34; Get regex entity properties for given entity name
                Args:

                    None

                Returns:

                    None

        &#34;&#34;&#34;

        regex_entity_dict = self._commcell_object.activate.entity_manager().get_properties(self._entity_name)
        self._display_name = regex_entity_dict[&#39;displayName&#39;]
        self._category_name = regex_entity_dict[&#39;categoryName&#39;]
        self._entity_id = regex_entity_dict[&#39;entityId&#39;]
        self._is_enabled = regex_entity_dict[&#39;enabled&#39;]
        self._entity_key = regex_entity_dict[&#39;entityKey&#39;]
        self._entity_type = regex_entity_dict[&#39;entityType&#39;]
        self._entity_xml = regex_entity_dict[&#39;entityXML&#39;]
        self._container_details = regex_entity_dict[&#39;containerDetails&#39;]
        return regex_entity_dict

    @property
    def container_details(self):
        &#34;&#34;&#34;Returns the container details for this entity&#34;&#34;&#34;
        return self._container_details

    @property
    def entity_id(self):
        &#34;&#34;&#34;Returns the value of the regex entity id attribute.&#34;&#34;&#34;
        return self._entity_id

    @property
    def entity_type(self):
        &#34;&#34;&#34;Returns the entity type attribute.&#34;&#34;&#34;
        return self._entity_type

    @property
    def category_name(self):
        &#34;&#34;&#34;Returns the entity category name attribute.&#34;&#34;&#34;
        return self._category_name

    @property
    def display_name(self):
        &#34;&#34;&#34;Returns the entity display name attribute.&#34;&#34;&#34;
        return self._display_name

    @property
    def is_enabled(self):
        &#34;&#34;&#34;Returns the entity isenabled attribute.&#34;&#34;&#34;
        return self._is_enabled

    @property
    def entity_key(self):
        &#34;&#34;&#34;Returns the entity key attribute.&#34;&#34;&#34;
        return self._entity_key

    @property
    def entity_xml(self):
        &#34;&#34;&#34;Returns the entity xml attribute.&#34;&#34;&#34;
        return self._entity_xml

    def refresh(self):
        &#34;&#34;&#34;Refresh the regex entity details for associated object&#34;&#34;&#34;
        self._get_entity_properties()</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.activateapps.entity_manager.ActivateEntity.category_name"><code class="name">var <span class="ident">category_name</span></code></dt>
<dd>
<div class="desc"><p>Returns the entity category name attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L856-L859" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def category_name(self):
    &#34;&#34;&#34;Returns the entity category name attribute.&#34;&#34;&#34;
    return self._category_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.ActivateEntity.container_details"><code class="name">var <span class="ident">container_details</span></code></dt>
<dd>
<div class="desc"><p>Returns the container details for this entity</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L841-L844" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def container_details(self):
    &#34;&#34;&#34;Returns the container details for this entity&#34;&#34;&#34;
    return self._container_details</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.ActivateEntity.display_name"><code class="name">var <span class="ident">display_name</span></code></dt>
<dd>
<div class="desc"><p>Returns the entity display name attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L861-L864" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def display_name(self):
    &#34;&#34;&#34;Returns the entity display name attribute.&#34;&#34;&#34;
    return self._display_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.ActivateEntity.entity_id"><code class="name">var <span class="ident">entity_id</span></code></dt>
<dd>
<div class="desc"><p>Returns the value of the regex entity id attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L846-L849" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def entity_id(self):
    &#34;&#34;&#34;Returns the value of the regex entity id attribute.&#34;&#34;&#34;
    return self._entity_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.ActivateEntity.entity_key"><code class="name">var <span class="ident">entity_key</span></code></dt>
<dd>
<div class="desc"><p>Returns the entity key attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L871-L874" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def entity_key(self):
    &#34;&#34;&#34;Returns the entity key attribute.&#34;&#34;&#34;
    return self._entity_key</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.ActivateEntity.entity_type"><code class="name">var <span class="ident">entity_type</span></code></dt>
<dd>
<div class="desc"><p>Returns the entity type attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L851-L854" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def entity_type(self):
    &#34;&#34;&#34;Returns the entity type attribute.&#34;&#34;&#34;
    return self._entity_type</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.ActivateEntity.entity_xml"><code class="name">var <span class="ident">entity_xml</span></code></dt>
<dd>
<div class="desc"><p>Returns the entity xml attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L876-L879" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def entity_xml(self):
    &#34;&#34;&#34;Returns the entity xml attribute.&#34;&#34;&#34;
    return self._entity_xml</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.ActivateEntity.is_enabled"><code class="name">var <span class="ident">is_enabled</span></code></dt>
<dd>
<div class="desc"><p>Returns the entity isenabled attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L866-L869" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_enabled(self):
    &#34;&#34;&#34;Returns the entity isenabled attribute.&#34;&#34;&#34;
    return self._is_enabled</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.activateapps.entity_manager.ActivateEntity.modify"><code class="name flex">
<span>def <span class="ident">modify</span></span>(<span>self, entity_regex, entity_keywords, entity_flag, is_derived=False, parent_entity=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Modifies the specified regex entity details</p>
<h2 id="args">Args</h2>
<p>entity_regex (str)
&ndash;
Regex for the entity</p>
<p>entity_keywords (str)
&ndash;
Keywords for the entity</p>
<p>entity_flag (int)
&ndash;
Sensitivity flag value for entity
5-Highly sensitive
3-Moderate sensitive
1-Low sensitive</p>
<p>is_derived (bool)
&ndash;
represents whether it is derived entity or not</p>
<p>parent_entity(int)
&ndash; entity id of the parent entity in case of derived entity</p>
<h2 id="returns">Returns</h2>
<p>None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if response is empty

    if response is not success

    if unable to modify regex entity in commcell

    if input entity_keywords &amp; entity_regex is not string

    if entity_flag value is not in allowed values[1,3,5]
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L736-L802" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def modify(self, entity_regex, entity_keywords, entity_flag, is_derived=False, parent_entity=None):
    &#34;&#34;&#34;Modifies the specified regex entity details

                Args:

                    entity_regex (str)     --  Regex for the entity

                    entity_keywords (str)  --  Keywords for the entity

                    entity_flag (int)      --  Sensitivity flag value for entity
                                                    5-Highly sensitive
                                                    3-Moderate sensitive
                                                    1-Low sensitive

                    is_derived (bool)      --  represents whether it is derived entity or not

                    parent_entity(int)     -- entity id of the parent entity in case of derived entity

                Returns:
                    None

                Raises:
                    SDKException:

                            if response is empty

                            if response is not success

                            if unable to modify regex entity in commcell

                            if input entity_keywords &amp; entity_regex is not string

                            if entity_flag value is not in allowed values[1,3,5]


            &#34;&#34;&#34;
    if not isinstance(entity_regex, str) \
            or not isinstance(entity_keywords, str):
        raise SDKException(&#39;ActivateEntity&#39;, &#39;101&#39;)
    if entity_flag not in [1, 3, 5]:
        raise SDKException(&#39;ActivateEntity&#39;, &#39;102&#39;, &#39;Unsupported entity flag value&#39;)
    request_json = ActivateEntityConstants.REQUEST_JSON
    request_json[&#39;regularExpression&#39;] = &#34;{\&#34;entity_key\&#34;:\&#34;&#34; + \
        self._entity_name + &#34;\&#34;,\&#34;entity_regex\&#34;:\&#34;&#34; + entity_regex + &#34;\&#34;}&#34;
    request_json[&#39;flags&#39;] = entity_flag
    request_json[&#39;entityName&#39;] = self._entity_name
    request_json[&#39;entityXML&#39;][&#39;keywords&#39;] = entity_keywords
    if is_derived:
        request_json[&#39;regularExpression&#39;] = &#34;{\&#34;entity_key\&#34;:\&#34;&#34; + self._entity_name + &#34;\&#34;}&#34;
        request_json[&#39;entityType&#39;] = 3
        if isinstance(parent_entity, int):
            request_json[&#39;parentEntityId&#39;] = parent_entity
        elif isinstance(parent_entity, str):
            request_json[&#39;parentEntityId&#39;] = ActivateEntities.get(
                self._commcell_object, entity_name=parent_entity).entity_id
        else:
            raise SDKException(&#39;ActivateEntity&#39;, &#39;102&#39;, &#39;Unsupported parent entity id type provided&#39;)

    flag, response = self._cvpysdk_obj.make_request(
        &#39;PUT&#39;, (self._api_modify_regex_entity % self.entity_id), request_json
    )

    if flag:
        if response.json() and &#39;entityDetails&#39; in response.json() and &#39;err&#39; not in response.json():
            return
        raise SDKException(&#39;ActivateEntity&#39;, &#39;106&#39;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.ActivateEntity.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the regex entity details for associated object</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L881-L883" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the regex entity details for associated object&#34;&#34;&#34;
    self._get_entity_properties()</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.Classifier"><code class="flex name class">
<span>class <span class="ident">Classifier</span></span>
<span>(</span><span>commcell_object, classifier_name, entity_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for performing operations on a single classifier entity</p>
<p>Initialize an object of the Classifier class.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the commcell class</p>
<p>classifier_name
(str)
&ndash;
name of the classifier</p>
<p>entity_id
(str)
&ndash;
id of the classifier
default: None</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the Classifier class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L2021-L2567" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Classifier(object):
    &#34;&#34;&#34;Class for performing operations on a single classifier entity&#34;&#34;&#34;

    def __init__(self, commcell_object, classifier_name, entity_id=None):
        &#34;&#34;&#34;Initialize an object of the Classifier class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

                classifier_name     (str)       --  name of the classifier

                entity_id       (str)           --  id of the classifier
                    default: None

            Returns:
                object  -   instance of the Classifier class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._services = commcell_object._services
        self._cvpysdk_obj = self._commcell_object._cvpysdk_object
        self._classifier_name = classifier_name
        self._entity_id = None
        self._display_name = None
        self._entity_type = None
        self._is_enabled = None
        self._entity_key = None
        self._entity_xml = None
        self._category_name = None
        self._trained_ca_cloud_id = None
        self._sync_ca_client_id = []
        self._last_training_time = None
        self._training_accuracy = None
        self._training_status = TrainingStatus.NOT_APPLICABLE.value
        self._sample_details = {}
        self._api_upload = self._services[&#39;CA_UPLOAD_FILE&#39;]
        self._api_start_training = self._services[&#39;START_TRAINING&#39;]
        self._api_cancel_training = self._services[&#39;CANCEL_TRAINING&#39;]
        self._api_entity = self._services[&#39;ACTIVATE_ENTITY&#39;]
        # flags used in upload API call Format : [Version Bit,Message Bit(1-Header, 2-Data Chunk),EOF File Flag]
        self._header_chunk = [1, 1, 0]
        self._data_chunk = [1, 2, 0]
        self._data_chunk_eof = [1, 2, 1]
        if entity_id is None:
            self._entity_id = self._get_entity_id(classifier_name)
        else:
            self._entity_id = entity_id
        self.refresh()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _get_entity_id(self, classifier_name):
        &#34;&#34;&#34; Get entity id for given classifier name
                Args:

                    classifier_name (str)  -- Name of the classifier

                Returns:

                    int                -- entity id of the classifier

        &#34;&#34;&#34;

        return self._commcell_object.activate.entity_manager(
            EntityManagerTypes.CLASSIFIERS).get(classifier_name.lower()).entity_id

    def _get_upload_api(self):
        &#34;&#34;&#34;Returns the upload api for this classifier

                Args:
                    None

                Returns:

                    Str     --  Upload API url

        &#34;&#34;&#34;
        api_params = f&#34;?entityId={self.entity_id}&amp;entityKey={self.entity_key}&amp;waitForCopy=true&#34;
        upload_api = self._api_upload % self.trained_ca_cloud_id
        return f&#34;{upload_api}{api_params}&#34;

    def _get_upload_request_id(self, zip_file):
        &#34;&#34;&#34;gets the upload request id

                Args:

                    zip_file        (str)       --  Zip file path

                Returns:

                    str     --  Request id for this upload request

                Raises

                    SDKException:

                            if failed to get request id for this upload request

                            if zip file doesn&#39;t exists on specified path

                            if it is not a valid zip file

        &#34;&#34;&#34;
        request_id = None
        if not zip_file.lower().endswith(&#34;.zip&#34;):
            raise SDKException(&#39;Classifier&#39;, &#39;102&#39;, &#34;Only Zip files are allowed for model trainings&#34;)
        if not os.path.exists(zip_file):
            raise SDKException(&#39;Classifier&#39;, &#39;107&#39;)
        file_stat = os.stat(zip_file)
        xml_req = f&#34;&lt;?xml version=&#39;1.0&#39; encoding=&#39;UTF-8&#39;?&gt;&lt;DM2ContentIndexing_UploadFileReq&gt;&#34; \
                  f&#34;&lt;destEntity clientId=\&#34;0\&#34; _type_=\&#34;3\&#34;/&gt;&#34; \
                  f&#34;&lt;fileContent fileSize=\&#34;{file_stat.st_size}\&#34; fileName=\&#34;{os.path.basename(zip_file)}\&#34;/&gt;&#34; \
                  f&#34;&lt;fileMetaData modifiedTime=\&#34;{str(file_stat.st_mtime).split(&#39;.&#39;,1)[0]}\&#34; dataType=\&#34;1\&#34;/&gt;&#34; \
                  f&#34;&lt;/DM2ContentIndexing_UploadFileReq&gt;&#34;
        req_length = len(xml_req)
        flag_byte = self._get_upload_flag_bit(flags=self._header_chunk, request_data_length=req_length)
        xml_byte = bytearray(xml_req, &#39;utf-8&#39;)
        flag, response = self._cvpysdk_obj.make_request(
            method=&#39;POST&#39;, url=self._get_upload_api(), payload=flag_byte + xml_byte)
        if flag:
            if response.json():
                if &#39;errorCode&#39; in response.json():
                    error_code = int(response.json()[&#39;errorCode&#39;])

                    if error_code != 0:
                        error_string = response.json()[&#39;errorString&#39;]
                        raise SDKException(
                            &#39;Classifier&#39;, &#39;102&#39;, &#39;Failed to get upload request id : {0}&#39;.format(
                                error_string
                            )
                        )

                if &#39;requestId&#39; in response.json():
                    request_id = response.json()[&#39;requestId&#39;]
                return request_id
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _validate_upload_response(self, flag, response, size):
        &#34;&#34;&#34;Validates the upload response for given chunk size

            Args:

                flag        (bool)      --  flag returned from make_request call for upload API

                response     (resp)     --  response from make_request call for upload API

                size        (int)       --  Chunk size to be checked in response

            Raises:

                SDKException:

                    if response is empty or not success

                    if response size is not required input size

        &#34;&#34;&#34;

        if flag:
            if response.json():
                if &#39;errorCode&#39; in response.json():
                    error_code = int(response.json()[&#39;errorCode&#39;])

                    if error_code != 0:
                        error_string = response.json()[&#39;errorString&#39;]
                        raise SDKException(
                            &#39;Classifier&#39;, &#39;102&#39;, &#39;Failed to Upload Full file data : {0}&#39;.format(
                                error_string
                            )
                        )

                if &#39;chunkOffset&#39; in response.json():
                    chunk_offset = response.json()[&#39;chunkOffset&#39;]
                    if chunk_offset != size:
                        raise SDKException(
                            &#39;Classifier&#39;, &#39;102&#39;, &#39;Chunk Offset not matched after upload. Please retry&#39;)
                    return
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _get_upload_flag_bit(self, flags, request_data_length):
        &#34;&#34;&#34;Returns the 7-byte flag used in upload request API

                Args:

                    flags       (list)       --  list containing flag values

                    request_data    (int)   --  length of the request data

                Returns:

                    bytes   --  bytes array representing flags used in upload api call


        &#34;&#34;&#34;
        # we need to put request length of upload api call into 4 byte flag buffer
        flag_byte = bytearray(flags)
        data_size_bytes = request_data_length.to_bytes(4, byteorder=&#39;big&#39;)
        flag_byte = flag_byte + data_size_bytes[::-1]
        return flag_byte

    def monitor_training(self, timeout=30):
        &#34;&#34;&#34;Monitor the training status on this classifier

                Args:

                    timeout     (int)   --  minutes after which the training will not be monitored,

                                                     default: 30

                Returns:

                    bool        --  to denote whether training got completed or not.


        &#34;&#34;&#34;
        start_time = time.time()
        time_limit = start_time + (timeout * 60)
        training_status = None
        while time_limit &gt; start_time:
            start_time = time.time()
            flag, response = self._cvpysdk_obj.make_request(
                &#39;GET&#39;, self._api_entity % self.entity_id)
            if flag:
                if response.json() and &#39;entityDetails&#39; in response.json() and &#39;err&#39; not in response.json():
                    entity_details = response.json()[&#39;entityDetails&#39;][0][&#39;entityXML&#39;]
                    if &#39;classifierDetails&#39; not in entity_details:
                        raise SDKException(&#39;Classifier&#39;, &#39;102&#39;, &#34;Unable to fetch Classifier training details&#34;)
                    training_status = entity_details[&#39;classifierDetails&#39;][&#39;trainingStatus&#39;]
                    if training_status == TrainingStatus.COMPLETED.value:
                        return True
                    elif training_status in [TrainingStatus.NOT_APPLICABLE.value, TrainingStatus.CANCELLED.value,
                                             TrainingStatus.FAILED.value, TrainingStatus.NOT_USABLE.value]:
                        return False
                elif &#39;err&#39; in response.json() and &#39;errLogMessage&#39; in response.json()[&#39;err&#39;]:
                    raise SDKException(&#39;Classifier&#39;, &#39;102&#39;, response.json()[&#39;err&#39;][&#39;errLogMessage&#39;])
                else:
                    raise SDKException(&#39;Classifier&#39;, &#39;108&#39;)
            else:
                self._response_not_success(response)
            time.sleep(30)
        return False

    def start_training(self, wait_for=True):
        &#34;&#34;&#34;Starts training on this classifier

                Args:

                    wait_for            (bool)      --  Specifies whether we need to wait or not for training completion

                Returns:

                    None

                Raises:

                    SDKException:

                        if failed to start training

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_obj.make_request(
            &#39;POST&#39;, self._api_start_training % (self.trained_ca_cloud_id, self.entity_id))
        if flag:
            if response.json() and &#39;entityDetails&#39; in response.json() and &#39;err&#39; not in response.json():
                if wait_for and not self.monitor_training():
                    raise SDKException(&#39;Classifier&#39;, &#39;102&#39;, &#34;Training ended in Error&#34;)
                self.refresh()
                return
            elif &#39;err&#39; in response.json() and &#39;errLogMessage&#39; in response.json()[&#39;err&#39;]:
                raise SDKException(&#39;Classifier&#39;, &#39;102&#39;, response.json()[&#39;err&#39;][&#39;errLogMessage&#39;])
            raise SDKException(&#39;Classifier&#39;, &#39;108&#39;)
        self._response_not_success(response)

    def cancel_training(self):
        &#34;&#34;&#34;Cancels training on this classifier

                Args:

                   None

                Returns:

                    None

                Raises:

                    SDKException:

                        if failed to Cancel training

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_obj.make_request(
            &#39;POST&#39;, self._api_cancel_training % (self.trained_ca_cloud_id, self.entity_id))
        if flag:
            if response.json() and &#39;entityDetails&#39; in response.json() and &#39;err&#39; not in response.json():
                entity_details = response.json()[&#39;entityDetails&#39;][0][&#39;entityXML&#39;]
                if &#39;classifierDetails&#39; not in entity_details:
                    raise SDKException(&#39;Classifier&#39;, &#39;102&#39;, &#34;Unable to fetch Classifier training details&#34;)
                training_status = entity_details[&#39;classifierDetails&#39;].get(&#39;trainingStatus&#39;, 0)
                if training_status != TrainingStatus.CANCELLED.value:
                    raise SDKException(
                        &#39;Classifier&#39;,
                        &#39;102&#39;,
                        f&#34;Wrong training status even after cancelling : {training_status}&#34;)
                self.refresh()
                return
            elif &#39;err&#39; in response.json() and &#39;errLogMessage&#39; in response.json()[&#39;err&#39;]:
                raise SDKException(&#39;Classifier&#39;, &#39;102&#39;, response.json()[&#39;err&#39;][&#39;errLogMessage&#39;])
            raise SDKException(&#39;Classifier&#39;, &#39;109&#39;)
        self._response_not_success(response)

    def modify(self, classifier_new_name=None, description=&#34;Modified from CvPySDK&#34;, enabled=True):
        &#34;&#34;&#34;Modifies the classifier entity

                Args:

                    classifier_new_name         (str)       --  New name for classifier

                    description                 (str)       --  Description string for classifier

                    enabled                     (bool)      --  flag to denote whether classifier is enabled or disabled

                Returns:

                    None

                Raises:

                    SDKException:

                            if failed to modify the classifier

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_obj.make_request(
            &#39;GET&#39;, self._api_entity % self.entity_id)
        if flag:
            if response.json() and &#39;entityDetails&#39; in response.json() and &#39;err&#39; not in response.json():
                request = copy.deepcopy(response.json()[&#39;entityDetails&#39;][0])
                if classifier_new_name:
                    request[&#39;entityName&#39;] = classifier_new_name
                    self._classifier_name = classifier_new_name
                if description:
                    request[&#39;description&#39;] = description
                if enabled:
                    request[&#39;enabled&#39;] = True
                else:
                    request[&#39;enabled&#39;] = False
                flag, response = self._cvpysdk_obj.make_request(
                    &#39;PUT&#39;, self._api_entity % self.entity_id, request)
                if flag:
                    if response.json() and &#39;entityDetails&#39; in response.json() and &#39;err&#39; not in response.json():
                        self.refresh()
                        return
                    elif &#39;err&#39; in response.json() and &#39;errLogMessage&#39; in response.json()[&#39;err&#39;]:
                        raise SDKException(&#39;Classifier&#39;, &#39;102&#39;, response.json()[&#39;err&#39;][&#39;errLogMessage&#39;])

                    raise SDKException(&#39;Classifier&#39;, &#39;106&#39;)

                self._response_not_success(response)
            elif &#39;err&#39; in response.json() and &#39;errLogMessage&#39; in response.json()[&#39;err&#39;]:
                raise SDKException(&#39;Classifier&#39;, &#39;102&#39;, response.json()[&#39;err&#39;][&#39;errLogMessage&#39;])

            raise SDKException(&#39;Classifier&#39;, &#39;106&#39;)

        self._response_not_success(response)

    def upload_data(self, zip_file, start_training=False):
        &#34;&#34;&#34;Uploads the model training data set zip file to content analyzer machine

                Args:

                    zip_file        (str)       --      Zip file path

                    start_training  (bool)      --      Denotes whether to start training on classifier or not

                Returns:

                    None

                Raises

                    SDKException:

                            if failed to upload the file


        &#34;&#34;&#34;
        chunk_size = 1048576  # 1MB
        request_id = self._get_upload_request_id(zip_file=zip_file)
        file_stat = os.stat(zip_file)
        req_length = len(request_id)
        file_byte = open(zip_file, &#39;rb&#39;)
        flag_byte = self._get_upload_flag_bit(flags=self._data_chunk_eof, request_data_length=req_length)
        xml_byte = bytearray(request_id, &#39;utf-8&#39;)
        if file_stat.st_size &lt;= chunk_size:
            # full file upload
            data_byte = file_byte.read()
            file_byte.close()
            payload = flag_byte + xml_byte + data_byte
            flag, response = self._cvpysdk_obj.make_request(
                method=&#39;POST&#39;, url=self._get_upload_api(), payload=payload)
            self._validate_upload_response(flag=flag, response=response, size=file_stat.st_size)
        else:
            # chunk based upload
            file_size = file_stat.st_size
            chunk_count = 1
            while file_size &gt; chunk_size:
                flag_byte = self._get_upload_flag_bit(flags=self._data_chunk, request_data_length=req_length)
                file_size = file_size - chunk_size
                data_byte = file_byte.read(chunk_size)
                payload = flag_byte + xml_byte + data_byte
                flag, response = self._cvpysdk_obj.make_request(
                    method=&#39;POST&#39;, url=self._get_upload_api(), payload=payload)
                self._validate_upload_response(flag=flag, response=response, size=chunk_size * chunk_count)
                chunk_count = chunk_count + 1
            flag_byte = self._get_upload_flag_bit(flags=self._data_chunk_eof, request_data_length=req_length)
            data_byte = file_byte.read(file_size)
            flag, response = self._cvpysdk_obj.make_request(
                method=&#39;POST&#39;, url=self._get_upload_api(), payload=flag_byte + xml_byte + data_byte)
            self._validate_upload_response(flag=flag, response=response, size=file_stat.st_size)
            file_byte.close()
        if start_training:
            self.start_training(wait_for=True)

    def _get_entity_properties(self):
        &#34;&#34;&#34; Get classifier entity properties
                Args:

                    None

                Returns:

                    None

        &#34;&#34;&#34;
        classifier_obj = self._commcell_object.activate.entity_manager(
            EntityManagerTypes.CLASSIFIERS)
        # Refresh before refetching properties
        classifier_obj.refresh()
        regex_entity_dict = classifier_obj.get_properties(
            self._classifier_name.lower())
        self._display_name = regex_entity_dict[&#39;displayName&#39;]
        self._category_name = regex_entity_dict[&#39;categoryName&#39;]
        self._entity_id = regex_entity_dict[&#39;entityId&#39;]
        self._is_enabled = regex_entity_dict[&#39;enabled&#39;]
        self._entity_key = regex_entity_dict[&#39;entityKey&#39;]
        self._entity_type = regex_entity_dict[&#39;entityType&#39;]
        self._entity_xml = regex_entity_dict[&#39;entityXML&#39;]
        if &#39;classifierDetails&#39; in self._entity_xml:
            self._training_status = int(self._entity_xml[&#39;classifierDetails&#39;].get(&#39;trainingStatus&#39;, 0))
            self._training_accuracy = self._entity_xml[&#39;classifierDetails&#39;].get(&#39;classifierAccuracy&#39;, 0)
            self._sample_details[&#39;totalSamples&#39;] = self._entity_xml[&#39;classifierDetails&#39;].get(&#39;totalSamples&#39;, 0)
            self._sample_details[&#39;trainingSamplesUsed&#39;] = self._entity_xml[&#39;classifierDetails&#39;].get(
                &#39;trainingSamplesUsed&#39;, 0)
            self._sample_details[&#39;validationSamplesUsed&#39;] = self._entity_xml[&#39;classifierDetails&#39;].get(
                &#39;validationSamplesUsed&#39;, 0)
            if &#39;CAUsedInTraining&#39; in self._entity_xml[&#39;classifierDetails&#39;]:
                trained_ca = self._entity_xml[&#39;classifierDetails&#39;][&#39;CAUsedInTraining&#39;]
                # Mandatory trained CA fields. so no need to use dict.get()
                self._trained_ca_cloud_id = int(trained_ca[&#39;clientId&#39;])
                self._last_training_time = trained_ca.get(&#39;lastModelTrainTime&#39;, 0)
            if &#39;syncedContentAnalyzers&#39; in self._entity_xml[&#39;classifierDetails&#39;] and self._entity_xml[
                    &#39;classifierDetails&#39;][&#39;syncedContentAnalyzers&#39;] is not None:
                sync_ca = self._entity_xml[&#39;classifierDetails&#39;][&#39;syncedContentAnalyzers&#39;].get(&#39;contentAnalyzerList&#39;, [])
                for content_analyzer in sync_ca:
                    self._sync_ca_client_id.append(content_analyzer[&#39;clientId&#39;])
        return regex_entity_dict

    @property
    def entity_id(self):
        &#34;&#34;&#34;Returns the value of the entity id attribute.&#34;&#34;&#34;
        return self._entity_id

    @property
    def entity_type(self):
        &#34;&#34;&#34;Returns the entity type attribute.&#34;&#34;&#34;
        return self._entity_type

    @property
    def category_name(self):
        &#34;&#34;&#34;Returns the entity category name attribute.&#34;&#34;&#34;
        return self._category_name

    @property
    def display_name(self):
        &#34;&#34;&#34;Returns the entity display name attribute.&#34;&#34;&#34;
        return self._display_name

    @property
    def is_enabled(self):
        &#34;&#34;&#34;Returns the entity isenabled attribute.&#34;&#34;&#34;
        return self._is_enabled

    @property
    def entity_key(self):
        &#34;&#34;&#34;Returns the entity key attribute.&#34;&#34;&#34;
        return self._entity_key

    @property
    def entity_xml(self):
        &#34;&#34;&#34;Returns the entity xml attribute.&#34;&#34;&#34;
        return self._entity_xml

    @property
    def trained_ca_cloud_id(self):
        &#34;&#34;&#34;Returns the cloudid attribute for trained CA&#34;&#34;&#34;
        return self._trained_ca_cloud_id

    @property
    def training_status(self):
        &#34;&#34;&#34;Returns the training status attribute&#34;&#34;&#34;
        return TrainingStatus(self._training_status).name

    @property
    def training_accuracy(self):
        &#34;&#34;&#34;Returns the training accuracy attribute&#34;&#34;&#34;
        return self._training_accuracy * 100

    @property
    def last_training_time(self):
        &#34;&#34;&#34;Returns the last training time attribute&#34;&#34;&#34;
        return self._last_training_time

    @property
    def sycn_ca_client_id(self):
        &#34;&#34;&#34;Returns list of Model synced CA client id attribute&#34;&#34;&#34;
        return self._sync_ca_client_id

    @property
    def sample_details(self):
        &#34;&#34;&#34;Returns dict containing model sample count details used for this classifier training&#34;&#34;&#34;
        return self._sample_details

    def refresh(self):
        &#34;&#34;&#34;Refresh the classifier details for associated object&#34;&#34;&#34;
        self._get_entity_properties()</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.activateapps.entity_manager.Classifier.category_name"><code class="name">var <span class="ident">category_name</span></code></dt>
<dd>
<div class="desc"><p>Returns the entity category name attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L2510-L2513" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def category_name(self):
    &#34;&#34;&#34;Returns the entity category name attribute.&#34;&#34;&#34;
    return self._category_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.Classifier.display_name"><code class="name">var <span class="ident">display_name</span></code></dt>
<dd>
<div class="desc"><p>Returns the entity display name attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L2515-L2518" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def display_name(self):
    &#34;&#34;&#34;Returns the entity display name attribute.&#34;&#34;&#34;
    return self._display_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.Classifier.entity_id"><code class="name">var <span class="ident">entity_id</span></code></dt>
<dd>
<div class="desc"><p>Returns the value of the entity id attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L2500-L2503" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def entity_id(self):
    &#34;&#34;&#34;Returns the value of the entity id attribute.&#34;&#34;&#34;
    return self._entity_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.Classifier.entity_key"><code class="name">var <span class="ident">entity_key</span></code></dt>
<dd>
<div class="desc"><p>Returns the entity key attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L2525-L2528" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def entity_key(self):
    &#34;&#34;&#34;Returns the entity key attribute.&#34;&#34;&#34;
    return self._entity_key</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.Classifier.entity_type"><code class="name">var <span class="ident">entity_type</span></code></dt>
<dd>
<div class="desc"><p>Returns the entity type attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L2505-L2508" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def entity_type(self):
    &#34;&#34;&#34;Returns the entity type attribute.&#34;&#34;&#34;
    return self._entity_type</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.Classifier.entity_xml"><code class="name">var <span class="ident">entity_xml</span></code></dt>
<dd>
<div class="desc"><p>Returns the entity xml attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L2530-L2533" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def entity_xml(self):
    &#34;&#34;&#34;Returns the entity xml attribute.&#34;&#34;&#34;
    return self._entity_xml</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.Classifier.is_enabled"><code class="name">var <span class="ident">is_enabled</span></code></dt>
<dd>
<div class="desc"><p>Returns the entity isenabled attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L2520-L2523" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_enabled(self):
    &#34;&#34;&#34;Returns the entity isenabled attribute.&#34;&#34;&#34;
    return self._is_enabled</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.Classifier.last_training_time"><code class="name">var <span class="ident">last_training_time</span></code></dt>
<dd>
<div class="desc"><p>Returns the last training time attribute</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L2550-L2553" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def last_training_time(self):
    &#34;&#34;&#34;Returns the last training time attribute&#34;&#34;&#34;
    return self._last_training_time</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.Classifier.sample_details"><code class="name">var <span class="ident">sample_details</span></code></dt>
<dd>
<div class="desc"><p>Returns dict containing model sample count details used for this classifier training</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L2560-L2563" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def sample_details(self):
    &#34;&#34;&#34;Returns dict containing model sample count details used for this classifier training&#34;&#34;&#34;
    return self._sample_details</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.Classifier.sycn_ca_client_id"><code class="name">var <span class="ident">sycn_ca_client_id</span></code></dt>
<dd>
<div class="desc"><p>Returns list of Model synced CA client id attribute</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L2555-L2558" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def sycn_ca_client_id(self):
    &#34;&#34;&#34;Returns list of Model synced CA client id attribute&#34;&#34;&#34;
    return self._sync_ca_client_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.Classifier.trained_ca_cloud_id"><code class="name">var <span class="ident">trained_ca_cloud_id</span></code></dt>
<dd>
<div class="desc"><p>Returns the cloudid attribute for trained CA</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L2535-L2538" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def trained_ca_cloud_id(self):
    &#34;&#34;&#34;Returns the cloudid attribute for trained CA&#34;&#34;&#34;
    return self._trained_ca_cloud_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.Classifier.training_accuracy"><code class="name">var <span class="ident">training_accuracy</span></code></dt>
<dd>
<div class="desc"><p>Returns the training accuracy attribute</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L2545-L2548" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def training_accuracy(self):
    &#34;&#34;&#34;Returns the training accuracy attribute&#34;&#34;&#34;
    return self._training_accuracy * 100</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.Classifier.training_status"><code class="name">var <span class="ident">training_status</span></code></dt>
<dd>
<div class="desc"><p>Returns the training status attribute</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L2540-L2543" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def training_status(self):
    &#34;&#34;&#34;Returns the training status attribute&#34;&#34;&#34;
    return TrainingStatus(self._training_status).name</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.activateapps.entity_manager.Classifier.cancel_training"><code class="name flex">
<span>def <span class="ident">cancel_training</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Cancels training on this classifier</p>
<h2 id="args">Args</h2>
<p>None</p>
<h2 id="returns">Returns</h2>
<p>None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if failed to Cancel training
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L2305-L2341" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def cancel_training(self):
    &#34;&#34;&#34;Cancels training on this classifier

            Args:

               None

            Returns:

                None

            Raises:

                SDKException:

                    if failed to Cancel training

    &#34;&#34;&#34;
    flag, response = self._cvpysdk_obj.make_request(
        &#39;POST&#39;, self._api_cancel_training % (self.trained_ca_cloud_id, self.entity_id))
    if flag:
        if response.json() and &#39;entityDetails&#39; in response.json() and &#39;err&#39; not in response.json():
            entity_details = response.json()[&#39;entityDetails&#39;][0][&#39;entityXML&#39;]
            if &#39;classifierDetails&#39; not in entity_details:
                raise SDKException(&#39;Classifier&#39;, &#39;102&#39;, &#34;Unable to fetch Classifier training details&#34;)
            training_status = entity_details[&#39;classifierDetails&#39;].get(&#39;trainingStatus&#39;, 0)
            if training_status != TrainingStatus.CANCELLED.value:
                raise SDKException(
                    &#39;Classifier&#39;,
                    &#39;102&#39;,
                    f&#34;Wrong training status even after cancelling : {training_status}&#34;)
            self.refresh()
            return
        elif &#39;err&#39; in response.json() and &#39;errLogMessage&#39; in response.json()[&#39;err&#39;]:
            raise SDKException(&#39;Classifier&#39;, &#39;102&#39;, response.json()[&#39;err&#39;][&#39;errLogMessage&#39;])
        raise SDKException(&#39;Classifier&#39;, &#39;109&#39;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.Classifier.modify"><code class="name flex">
<span>def <span class="ident">modify</span></span>(<span>self, classifier_new_name=None, description='Modified from CvPySDK', enabled=True)</span>
</code></dt>
<dd>
<div class="desc"><p>Modifies the classifier entity</p>
<h2 id="args">Args</h2>
<p>classifier_new_name
(str)
&ndash;
New name for classifier</p>
<p>description
(str)
&ndash;
Description string for classifier</p>
<p>enabled
(bool)
&ndash;
flag to denote whether classifier is enabled or disabled</p>
<h2 id="returns">Returns</h2>
<p>None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if failed to modify the classifier
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L2343-L2396" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def modify(self, classifier_new_name=None, description=&#34;Modified from CvPySDK&#34;, enabled=True):
    &#34;&#34;&#34;Modifies the classifier entity

            Args:

                classifier_new_name         (str)       --  New name for classifier

                description                 (str)       --  Description string for classifier

                enabled                     (bool)      --  flag to denote whether classifier is enabled or disabled

            Returns:

                None

            Raises:

                SDKException:

                        if failed to modify the classifier

    &#34;&#34;&#34;
    flag, response = self._cvpysdk_obj.make_request(
        &#39;GET&#39;, self._api_entity % self.entity_id)
    if flag:
        if response.json() and &#39;entityDetails&#39; in response.json() and &#39;err&#39; not in response.json():
            request = copy.deepcopy(response.json()[&#39;entityDetails&#39;][0])
            if classifier_new_name:
                request[&#39;entityName&#39;] = classifier_new_name
                self._classifier_name = classifier_new_name
            if description:
                request[&#39;description&#39;] = description
            if enabled:
                request[&#39;enabled&#39;] = True
            else:
                request[&#39;enabled&#39;] = False
            flag, response = self._cvpysdk_obj.make_request(
                &#39;PUT&#39;, self._api_entity % self.entity_id, request)
            if flag:
                if response.json() and &#39;entityDetails&#39; in response.json() and &#39;err&#39; not in response.json():
                    self.refresh()
                    return
                elif &#39;err&#39; in response.json() and &#39;errLogMessage&#39; in response.json()[&#39;err&#39;]:
                    raise SDKException(&#39;Classifier&#39;, &#39;102&#39;, response.json()[&#39;err&#39;][&#39;errLogMessage&#39;])

                raise SDKException(&#39;Classifier&#39;, &#39;106&#39;)

            self._response_not_success(response)
        elif &#39;err&#39; in response.json() and &#39;errLogMessage&#39; in response.json()[&#39;err&#39;]:
            raise SDKException(&#39;Classifier&#39;, &#39;102&#39;, response.json()[&#39;err&#39;][&#39;errLogMessage&#39;])

        raise SDKException(&#39;Classifier&#39;, &#39;106&#39;)

    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.Classifier.monitor_training"><code class="name flex">
<span>def <span class="ident">monitor_training</span></span>(<span>self, timeout=30)</span>
</code></dt>
<dd>
<div class="desc"><p>Monitor the training status on this classifier</p>
<h2 id="args">Args</h2>
<p>timeout
(int)
&ndash;
minutes after which the training will not be monitored,</p>
<pre><code>                             default: 30
</code></pre>
<h2 id="returns">Returns</h2>
<p>bool
&ndash;
to denote whether training got completed or not.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L2232-L2272" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def monitor_training(self, timeout=30):
    &#34;&#34;&#34;Monitor the training status on this classifier

            Args:

                timeout     (int)   --  minutes after which the training will not be monitored,

                                                 default: 30

            Returns:

                bool        --  to denote whether training got completed or not.


    &#34;&#34;&#34;
    start_time = time.time()
    time_limit = start_time + (timeout * 60)
    training_status = None
    while time_limit &gt; start_time:
        start_time = time.time()
        flag, response = self._cvpysdk_obj.make_request(
            &#39;GET&#39;, self._api_entity % self.entity_id)
        if flag:
            if response.json() and &#39;entityDetails&#39; in response.json() and &#39;err&#39; not in response.json():
                entity_details = response.json()[&#39;entityDetails&#39;][0][&#39;entityXML&#39;]
                if &#39;classifierDetails&#39; not in entity_details:
                    raise SDKException(&#39;Classifier&#39;, &#39;102&#39;, &#34;Unable to fetch Classifier training details&#34;)
                training_status = entity_details[&#39;classifierDetails&#39;][&#39;trainingStatus&#39;]
                if training_status == TrainingStatus.COMPLETED.value:
                    return True
                elif training_status in [TrainingStatus.NOT_APPLICABLE.value, TrainingStatus.CANCELLED.value,
                                         TrainingStatus.FAILED.value, TrainingStatus.NOT_USABLE.value]:
                    return False
            elif &#39;err&#39; in response.json() and &#39;errLogMessage&#39; in response.json()[&#39;err&#39;]:
                raise SDKException(&#39;Classifier&#39;, &#39;102&#39;, response.json()[&#39;err&#39;][&#39;errLogMessage&#39;])
            else:
                raise SDKException(&#39;Classifier&#39;, &#39;108&#39;)
        else:
            self._response_not_success(response)
        time.sleep(30)
    return False</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.Classifier.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the classifier details for associated object</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L2565-L2567" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the classifier details for associated object&#34;&#34;&#34;
    self._get_entity_properties()</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.Classifier.start_training"><code class="name flex">
<span>def <span class="ident">start_training</span></span>(<span>self, wait_for=True)</span>
</code></dt>
<dd>
<div class="desc"><p>Starts training on this classifier</p>
<h2 id="args">Args</h2>
<p>wait_for
(bool)
&ndash;
Specifies whether we need to wait or not for training completion</p>
<h2 id="returns">Returns</h2>
<p>None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if failed to start training
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L2274-L2303" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def start_training(self, wait_for=True):
    &#34;&#34;&#34;Starts training on this classifier

            Args:

                wait_for            (bool)      --  Specifies whether we need to wait or not for training completion

            Returns:

                None

            Raises:

                SDKException:

                    if failed to start training

    &#34;&#34;&#34;
    flag, response = self._cvpysdk_obj.make_request(
        &#39;POST&#39;, self._api_start_training % (self.trained_ca_cloud_id, self.entity_id))
    if flag:
        if response.json() and &#39;entityDetails&#39; in response.json() and &#39;err&#39; not in response.json():
            if wait_for and not self.monitor_training():
                raise SDKException(&#39;Classifier&#39;, &#39;102&#39;, &#34;Training ended in Error&#34;)
            self.refresh()
            return
        elif &#39;err&#39; in response.json() and &#39;errLogMessage&#39; in response.json()[&#39;err&#39;]:
            raise SDKException(&#39;Classifier&#39;, &#39;102&#39;, response.json()[&#39;err&#39;][&#39;errLogMessage&#39;])
        raise SDKException(&#39;Classifier&#39;, &#39;108&#39;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.Classifier.upload_data"><code class="name flex">
<span>def <span class="ident">upload_data</span></span>(<span>self, zip_file, start_training=False)</span>
</code></dt>
<dd>
<div class="desc"><p>Uploads the model training data set zip file to content analyzer machine</p>
<h2 id="args">Args</h2>
<p>zip_file
(str)
&ndash;
Zip file path</p>
<p>start_training
(bool)
&ndash;
Denotes whether to start training on classifier or not</p>
<h2 id="returns">Returns</h2>
<p>None
Raises</p>
<pre><code>SDKException:

        if failed to upload the file
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L2398-L2454" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def upload_data(self, zip_file, start_training=False):
    &#34;&#34;&#34;Uploads the model training data set zip file to content analyzer machine

            Args:

                zip_file        (str)       --      Zip file path

                start_training  (bool)      --      Denotes whether to start training on classifier or not

            Returns:

                None

            Raises

                SDKException:

                        if failed to upload the file


    &#34;&#34;&#34;
    chunk_size = 1048576  # 1MB
    request_id = self._get_upload_request_id(zip_file=zip_file)
    file_stat = os.stat(zip_file)
    req_length = len(request_id)
    file_byte = open(zip_file, &#39;rb&#39;)
    flag_byte = self._get_upload_flag_bit(flags=self._data_chunk_eof, request_data_length=req_length)
    xml_byte = bytearray(request_id, &#39;utf-8&#39;)
    if file_stat.st_size &lt;= chunk_size:
        # full file upload
        data_byte = file_byte.read()
        file_byte.close()
        payload = flag_byte + xml_byte + data_byte
        flag, response = self._cvpysdk_obj.make_request(
            method=&#39;POST&#39;, url=self._get_upload_api(), payload=payload)
        self._validate_upload_response(flag=flag, response=response, size=file_stat.st_size)
    else:
        # chunk based upload
        file_size = file_stat.st_size
        chunk_count = 1
        while file_size &gt; chunk_size:
            flag_byte = self._get_upload_flag_bit(flags=self._data_chunk, request_data_length=req_length)
            file_size = file_size - chunk_size
            data_byte = file_byte.read(chunk_size)
            payload = flag_byte + xml_byte + data_byte
            flag, response = self._cvpysdk_obj.make_request(
                method=&#39;POST&#39;, url=self._get_upload_api(), payload=payload)
            self._validate_upload_response(flag=flag, response=response, size=chunk_size * chunk_count)
            chunk_count = chunk_count + 1
        flag_byte = self._get_upload_flag_bit(flags=self._data_chunk_eof, request_data_length=req_length)
        data_byte = file_byte.read(file_size)
        flag, response = self._cvpysdk_obj.make_request(
            method=&#39;POST&#39;, url=self._get_upload_api(), payload=flag_byte + xml_byte + data_byte)
        self._validate_upload_response(flag=flag, response=response, size=file_stat.st_size)
        file_byte.close()
    if start_training:
        self.start_training(wait_for=True)</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.Classifiers"><code class="flex name class">
<span>class <span class="ident">Classifiers</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing all the Classifier entities in the commcell.</p>
<p>Initializes an instance of the Classifiers class.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the commcell class</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the Classifiers class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L1699-L2018" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Classifiers(object):
    &#34;&#34;&#34;Class for representing all the Classifier entities in the commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initializes an instance of the Classifiers class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

            Returns:
                object  -   instance of the Classifiers class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._classifiers = None
        self._api_create_classifier = self._services[&#39;ACTIVATE_ENTITIES&#39;]
        self._api_get_classifier = self._services[&#39;GET_CLASSIFIERS&#39;]
        self._api_delete_classifier = self._services[&#39;ACTIVATE_ENTITY&#39;]
        self.refresh()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def get_properties(self, classifier_name):
        &#34;&#34;&#34;Returns a properties of the specified classifier name.

            Args:
                classifier_name (str)  --  name of the classifier

            Returns:
                dict -  properties for the given classifier name


        &#34;&#34;&#34;
        return self._classifiers[classifier_name.lower()]

    def _get_all_classifier_entities(self):
        &#34;&#34;&#34;Gets the list of all classifier associated with this commcell.

            Returns:
                dict    -   dictionary consisting of dictionaries, where each dictionary stores the
                                details of a single classifier

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._api_get_classifier
        )

        if flag:
            if response.json() and &#39;entityDetails&#39; in response.json():
                return self._get_classifier_entity_from_collections(response.json())
            raise SDKException(&#39;Classifier&#39;, &#39;103&#39;)
        self._response_not_success(response)

    @staticmethod
    def _get_classifier_entity_from_collections(collections):
        &#34;&#34;&#34;Extracts all the classifier entities, and their details from the list of collections given,
            and returns the dictionary of all classifier

            Args:
                collections     (list)  --  list of all collections

            Returns:
                dict    -   dictionary consisting of dictionaries, where each dictionary stores the
                                details of a single classifier

        &#34;&#34;&#34;
        _regex_entity = {}
        for regex_entity in collections[&#39;entityDetails&#39;]:
            regex_entity_dict = {}
            regex_entity_dict[&#39;displayName&#39;] = regex_entity.get(&#39;displayName&#39;, &#34;&#34;)
            regex_entity_dict[&#39;entityKey&#39;] = regex_entity.get(&#39;entityKey&#39;, &#34;&#34;)
            regex_entity_dict[&#39;categoryName&#39;] = regex_entity.get(&#39;categoryName&#39;, &#34;&#34;)
            regex_entity_dict[&#39;entityXML&#39;] = regex_entity.get(&#39;entityXML&#39;, &#34;&#34;)
            regex_entity_dict[&#39;entityId&#39;] = regex_entity.get(&#39;entityId&#39;, 0)
            regex_entity_dict[&#39;flags&#39;] = regex_entity.get(&#39;flags&#39;, 0)
            regex_entity_dict[&#39;entityType&#39;] = regex_entity.get(&#39;entityType&#39;, 0)
            regex_entity_dict[&#39;enabled&#39;] = regex_entity.get(&#39;enabled&#39;, False)
            _regex_entity[regex_entity[&#39;entityName&#39;].lower()] = regex_entity_dict
        return _regex_entity

    def refresh(self):
        &#34;&#34;&#34;Refresh the classifier associated with the commcell.&#34;&#34;&#34;
        self._classifiers = self._get_all_classifier_entities()

    def delete(self, classifier_name):
        &#34;&#34;&#34;deletes the specified classifier in the commcell

                    Args:

                        classifier_name (str)      --  name of the classifier

                    Returns:
                        None

                    Raises:

                        SDKException:

                                if response is empty

                                if response is not success

                                if unable to delete classifier in commcell

                                if unable to find classifier in the commcell

                                if data type of input is invalid


                &#34;&#34;&#34;
        if not isinstance(classifier_name, str):
            raise SDKException(&#39;Classifier&#39;, &#39;101&#39;)
        if classifier_name.lower() not in self._classifiers:
            raise SDKException(&#39;Classifier&#39;, &#39;102&#39;, &#39;Unable to find given classifier name in the commcell&#39;)

        flag, response = self._cvpysdk_object.make_request(
            &#39;DELETE&#39;, self._api_delete_classifier % self._classifiers[classifier_name.lower()][&#39;entityId&#39;]
        )

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json() and response.json()[&#39;errorCode&#39;] == 0:
                self.refresh()
                return
            raise SDKException(&#39;Classifier&#39;, &#39;105&#39;)
        self._response_not_success(response)

    def add(self, classifier_name, content_analyzer, description=&#34;Created from CvPySDK&#34;, training_zip_data_file=None):
        &#34;&#34;&#34;Creates new classifier with given name in the commcell

                Args:

                    classifier_name     (str)       --      Name of the classifier

                    content_analyzer    (str)       --      Content Analyzer cloud name

                    description         (str)       --      Description for classifier

                    training_zip_data_file  (str)   --      Zip file path containing training data files

                Returns:

                    object      --  returns object of Classifier class

                Raises:

                    SDKException:

                        if input data type is not valid

                        if response is empty or not success

                        if failed to create classifier

                        if failed to find content analyzer cloud details

        &#34;&#34;&#34;

        if not isinstance(classifier_name, str) or not isinstance(content_analyzer, str):
            raise SDKException(&#39;Classifier&#39;, &#39;101&#39;)
        if not self._commcell_object.content_analyzers.has_client(content_analyzer):
            raise SDKException(&#39;Classifier&#39;, &#39;102&#39;, &#34;Given CA cloud doesn&#39;t exists on this commcell&#34;)
        ca_obj = self._commcell_object.content_analyzers.get(content_analyzer)
        request_json = copy.deepcopy(ClassifierConstants.CREATE_REQUEST_JSON)
        request_json[&#39;description&#39;] = description
        request_json[&#39;entityName&#39;] = classifier_name
        request_json[&#39;entityKey&#39;] = classifier_name.replace(&#34; &#34;, &#34;_&#34;).lower()
        ca_details_json = request_json[&#39;entityXML&#39;][&#39;classifierDetails&#39;][&#39;CAUsedInTraining&#39;]
        ca_details_json[&#39;caUrl&#39;] = ca_obj.cloud_url
        ca_details_json[&#39;clientId&#39;] = int(ca_obj.client_id)
        ca_details_json[&#39;cloudName&#39;] = content_analyzer
        port_no = int(ca_obj.cloud_url.split(&#34;:&#34;)[2])
        # update if it is not default port no of 22000
        if port_no != 22000:
            request_json[&#39;entityXML&#39;][&#39;classifierDetails&#39;][&#39;trainDatasetURI&#39;] = \
                request_json[&#39;entityXML&#39;][&#39;classifierDetails&#39;][&#39;trainDatasetURI&#39;].replace(&#34;22000&#34;, str(port_no))
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._api_create_classifier, request_json
        )

        if flag:
            if response.json() and &#39;entityDetails&#39; in response.json() and &#39;err&#39; not in response.json():
                entity_id = response.json()[&#39;entityDetails&#39;][0][&#39;entityId&#39;]
                self.refresh()
                classifier_obj = Classifier(
                    commcell_object=self._commcell_object,
                    classifier_name=classifier_name,
                    entity_id=entity_id)
                if training_zip_data_file:
                    classifier_obj.upload_data(zip_file=training_zip_data_file, start_training=True)
                return classifier_obj
            raise SDKException(&#39;Classifier&#39;, &#39;104&#39;)
        self._response_not_success(response)

    def get(self, classifier_name):
        &#34;&#34;&#34;Returns a Classifier object for the given classifier name.

            Args:
                classifier_name (str)  --  name of the classifier

            Returns:

                obj                 -- Object of Classifier class

            Raises:
                SDKException:

                    if unable to find classifier info in commcell

                    if classifier_name is not of type string


        &#34;&#34;&#34;
        if not isinstance(classifier_name, str):
            raise SDKException(&#39;Classifier&#39;, &#39;101&#39;)

        if self.has_classifier(classifier_name.lower()):
            entity_id = self._classifiers[classifier_name.lower()][&#39;entityId&#39;]
            return Classifier(self._commcell_object, classifier_name.lower(), entity_id)
        raise SDKException(&#39;Classifier&#39;, &#39;102&#39;, &#34;Unable to get Classifier class object&#34;)

    def get_entity_ids(self, classifier_name):
        &#34;&#34;&#34;Returns a list of entity id for the given classifier name list.

            Args:
                classifier_name (list)  --  names of the classifier

            Returns:

                list                -- entity id&#39;s for the given classifier names

            Raises:
                SDKException:

                    if classifier_name is not of type list

                    if unable to find entity id for classifier


        &#34;&#34;&#34;
        if not isinstance(classifier_name, list):
            raise SDKException(&#39;Classifier&#39;, &#39;101&#39;)
        entity_ids = []
        for classifier in classifier_name:
            classifier = classifier.lower()
            if classifier in self._classifiers:
                entity_ids.append(self._classifiers[classifier][&#39;entityId&#39;])
            else:
                raise SDKException(
                    &#39;Classifier&#39;, &#39;102&#39;, f&#34;Unable to find entity id for given classifier name :{classifier}&#34;)
        return entity_ids

    def get_entity_keys(self, classifier_name):
        &#34;&#34;&#34;Returns a list of entity keys for the given classifier name list.

            Args:
                classifier_name (list)  --  names of the classifier

            Returns:

                list                -- entity keys for the given classifier names

            Raises:

                SDKException:

                    if classifier_name is not of type list

                    if unable to find entity key for classifier


        &#34;&#34;&#34;
        if not isinstance(classifier_name, list):
            raise SDKException(&#39;Classifier&#39;, &#39;101&#39;)
        entity_keys = []
        for classifier in classifier_name:
            classifier = classifier.lower()
            if classifier in self._classifiers:
                entity_keys.append(self._classifiers[classifier][&#39;entityKey&#39;])
            else:
                raise SDKException(
                    &#39;Classifier&#39;, &#39;102&#39;, f&#34;Unable to find entity keys for given classifier name :{classifier}&#34;)
        return entity_keys

    def has_classifier(self, classifier_name):
        &#34;&#34;&#34;Checks if a classifier entity exists in the commcell with the input name.

            Args:
                classifier_name (str)  --  name of the classifier

            Returns:
                bool - boolean output to denote whether classifier exists in the commcell or not

            Raises:
                SDKException:

                    if type of the classifier name argument is not string

        &#34;&#34;&#34;
        if not isinstance(classifier_name, str):
            raise SDKException(&#39;Classifier&#39;, &#39;101&#39;)

        return self._classifiers and classifier_name.lower() in map(str.lower, self._classifiers)</code></pre>
</details>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.activateapps.entity_manager.Classifiers.add"><code class="name flex">
<span>def <span class="ident">add</span></span>(<span>self, classifier_name, content_analyzer, description='Created from CvPySDK', training_zip_data_file=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Creates new classifier with given name in the commcell</p>
<h2 id="args">Args</h2>
<p>classifier_name
(str)
&ndash;
Name of the classifier</p>
<p>content_analyzer
(str)
&ndash;
Content Analyzer cloud name</p>
<p>description
(str)
&ndash;
Description for classifier</p>
<p>training_zip_data_file
(str)
&ndash;
Zip file path containing training data files</p>
<h2 id="returns">Returns</h2>
<p>object
&ndash;
returns object of Classifier class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if input data type is not valid

if response is empty or not success

if failed to create classifier

if failed to find content analyzer cloud details
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L1843-L1908" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add(self, classifier_name, content_analyzer, description=&#34;Created from CvPySDK&#34;, training_zip_data_file=None):
    &#34;&#34;&#34;Creates new classifier with given name in the commcell

            Args:

                classifier_name     (str)       --      Name of the classifier

                content_analyzer    (str)       --      Content Analyzer cloud name

                description         (str)       --      Description for classifier

                training_zip_data_file  (str)   --      Zip file path containing training data files

            Returns:

                object      --  returns object of Classifier class

            Raises:

                SDKException:

                    if input data type is not valid

                    if response is empty or not success

                    if failed to create classifier

                    if failed to find content analyzer cloud details

    &#34;&#34;&#34;

    if not isinstance(classifier_name, str) or not isinstance(content_analyzer, str):
        raise SDKException(&#39;Classifier&#39;, &#39;101&#39;)
    if not self._commcell_object.content_analyzers.has_client(content_analyzer):
        raise SDKException(&#39;Classifier&#39;, &#39;102&#39;, &#34;Given CA cloud doesn&#39;t exists on this commcell&#34;)
    ca_obj = self._commcell_object.content_analyzers.get(content_analyzer)
    request_json = copy.deepcopy(ClassifierConstants.CREATE_REQUEST_JSON)
    request_json[&#39;description&#39;] = description
    request_json[&#39;entityName&#39;] = classifier_name
    request_json[&#39;entityKey&#39;] = classifier_name.replace(&#34; &#34;, &#34;_&#34;).lower()
    ca_details_json = request_json[&#39;entityXML&#39;][&#39;classifierDetails&#39;][&#39;CAUsedInTraining&#39;]
    ca_details_json[&#39;caUrl&#39;] = ca_obj.cloud_url
    ca_details_json[&#39;clientId&#39;] = int(ca_obj.client_id)
    ca_details_json[&#39;cloudName&#39;] = content_analyzer
    port_no = int(ca_obj.cloud_url.split(&#34;:&#34;)[2])
    # update if it is not default port no of 22000
    if port_no != 22000:
        request_json[&#39;entityXML&#39;][&#39;classifierDetails&#39;][&#39;trainDatasetURI&#39;] = \
            request_json[&#39;entityXML&#39;][&#39;classifierDetails&#39;][&#39;trainDatasetURI&#39;].replace(&#34;22000&#34;, str(port_no))
    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._api_create_classifier, request_json
    )

    if flag:
        if response.json() and &#39;entityDetails&#39; in response.json() and &#39;err&#39; not in response.json():
            entity_id = response.json()[&#39;entityDetails&#39;][0][&#39;entityId&#39;]
            self.refresh()
            classifier_obj = Classifier(
                commcell_object=self._commcell_object,
                classifier_name=classifier_name,
                entity_id=entity_id)
            if training_zip_data_file:
                classifier_obj.upload_data(zip_file=training_zip_data_file, start_training=True)
            return classifier_obj
        raise SDKException(&#39;Classifier&#39;, &#39;104&#39;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.Classifiers.delete"><code class="name flex">
<span>def <span class="ident">delete</span></span>(<span>self, classifier_name)</span>
</code></dt>
<dd>
<div class="desc"><p>deletes the specified classifier in the commcell</p>
<h2 id="args">Args</h2>
<p>classifier_name (str)
&ndash;
name of the classifier</p>
<h2 id="returns">Returns</h2>
<p>None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if response is empty

    if response is not success

    if unable to delete classifier in commcell

    if unable to find classifier in the commcell

    if data type of input is invalid
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L1801-L1841" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete(self, classifier_name):
    &#34;&#34;&#34;deletes the specified classifier in the commcell

                Args:

                    classifier_name (str)      --  name of the classifier

                Returns:
                    None

                Raises:

                    SDKException:

                            if response is empty

                            if response is not success

                            if unable to delete classifier in commcell

                            if unable to find classifier in the commcell

                            if data type of input is invalid


            &#34;&#34;&#34;
    if not isinstance(classifier_name, str):
        raise SDKException(&#39;Classifier&#39;, &#39;101&#39;)
    if classifier_name.lower() not in self._classifiers:
        raise SDKException(&#39;Classifier&#39;, &#39;102&#39;, &#39;Unable to find given classifier name in the commcell&#39;)

    flag, response = self._cvpysdk_object.make_request(
        &#39;DELETE&#39;, self._api_delete_classifier % self._classifiers[classifier_name.lower()][&#39;entityId&#39;]
    )

    if flag:
        if response.json() and &#39;errorCode&#39; in response.json() and response.json()[&#39;errorCode&#39;] == 0:
            self.refresh()
            return
        raise SDKException(&#39;Classifier&#39;, &#39;105&#39;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.Classifiers.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, classifier_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a Classifier object for the given classifier name.</p>
<h2 id="args">Args</h2>
<p>classifier_name (str)
&ndash;
name of the classifier</p>
<h2 id="returns">Returns</h2>
<p>obj
&ndash; Object of Classifier class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if unable to find classifier info in commcell

if classifier_name is not of type string
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L1910-L1935" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, classifier_name):
    &#34;&#34;&#34;Returns a Classifier object for the given classifier name.

        Args:
            classifier_name (str)  --  name of the classifier

        Returns:

            obj                 -- Object of Classifier class

        Raises:
            SDKException:

                if unable to find classifier info in commcell

                if classifier_name is not of type string


    &#34;&#34;&#34;
    if not isinstance(classifier_name, str):
        raise SDKException(&#39;Classifier&#39;, &#39;101&#39;)

    if self.has_classifier(classifier_name.lower()):
        entity_id = self._classifiers[classifier_name.lower()][&#39;entityId&#39;]
        return Classifier(self._commcell_object, classifier_name.lower(), entity_id)
    raise SDKException(&#39;Classifier&#39;, &#39;102&#39;, &#34;Unable to get Classifier class object&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.Classifiers.get_entity_ids"><code class="name flex">
<span>def <span class="ident">get_entity_ids</span></span>(<span>self, classifier_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a list of entity id for the given classifier name list.</p>
<h2 id="args">Args</h2>
<p>classifier_name (list)
&ndash;
names of the classifier</p>
<h2 id="returns">Returns</h2>
<p>list
&ndash; entity id's for the given classifier names</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if classifier_name is not of type list

if unable to find entity id for classifier
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L1937-L1966" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_entity_ids(self, classifier_name):
    &#34;&#34;&#34;Returns a list of entity id for the given classifier name list.

        Args:
            classifier_name (list)  --  names of the classifier

        Returns:

            list                -- entity id&#39;s for the given classifier names

        Raises:
            SDKException:

                if classifier_name is not of type list

                if unable to find entity id for classifier


    &#34;&#34;&#34;
    if not isinstance(classifier_name, list):
        raise SDKException(&#39;Classifier&#39;, &#39;101&#39;)
    entity_ids = []
    for classifier in classifier_name:
        classifier = classifier.lower()
        if classifier in self._classifiers:
            entity_ids.append(self._classifiers[classifier][&#39;entityId&#39;])
        else:
            raise SDKException(
                &#39;Classifier&#39;, &#39;102&#39;, f&#34;Unable to find entity id for given classifier name :{classifier}&#34;)
    return entity_ids</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.Classifiers.get_entity_keys"><code class="name flex">
<span>def <span class="ident">get_entity_keys</span></span>(<span>self, classifier_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a list of entity keys for the given classifier name list.</p>
<h2 id="args">Args</h2>
<p>classifier_name (list)
&ndash;
names of the classifier</p>
<h2 id="returns">Returns</h2>
<p>list
&ndash; entity keys for the given classifier names</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if classifier_name is not of type list

if unable to find entity key for classifier
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L1968-L1998" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_entity_keys(self, classifier_name):
    &#34;&#34;&#34;Returns a list of entity keys for the given classifier name list.

        Args:
            classifier_name (list)  --  names of the classifier

        Returns:

            list                -- entity keys for the given classifier names

        Raises:

            SDKException:

                if classifier_name is not of type list

                if unable to find entity key for classifier


    &#34;&#34;&#34;
    if not isinstance(classifier_name, list):
        raise SDKException(&#39;Classifier&#39;, &#39;101&#39;)
    entity_keys = []
    for classifier in classifier_name:
        classifier = classifier.lower()
        if classifier in self._classifiers:
            entity_keys.append(self._classifiers[classifier][&#39;entityKey&#39;])
        else:
            raise SDKException(
                &#39;Classifier&#39;, &#39;102&#39;, f&#34;Unable to find entity keys for given classifier name :{classifier}&#34;)
    return entity_keys</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.Classifiers.get_properties"><code class="name flex">
<span>def <span class="ident">get_properties</span></span>(<span>self, classifier_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a properties of the specified classifier name.</p>
<h2 id="args">Args</h2>
<p>classifier_name (str)
&ndash;
name of the classifier</p>
<h2 id="returns">Returns</h2>
<p>dict -
properties for the given classifier name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L1733-L1744" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_properties(self, classifier_name):
    &#34;&#34;&#34;Returns a properties of the specified classifier name.

        Args:
            classifier_name (str)  --  name of the classifier

        Returns:
            dict -  properties for the given classifier name


    &#34;&#34;&#34;
    return self._classifiers[classifier_name.lower()]</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.Classifiers.has_classifier"><code class="name flex">
<span>def <span class="ident">has_classifier</span></span>(<span>self, classifier_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks if a classifier entity exists in the commcell with the input name.</p>
<h2 id="args">Args</h2>
<p>classifier_name (str)
&ndash;
name of the classifier</p>
<h2 id="returns">Returns</h2>
<p>bool - boolean output to denote whether classifier exists in the commcell or not</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if type of the classifier name argument is not string
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L2000-L2018" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_classifier(self, classifier_name):
    &#34;&#34;&#34;Checks if a classifier entity exists in the commcell with the input name.

        Args:
            classifier_name (str)  --  name of the classifier

        Returns:
            bool - boolean output to denote whether classifier exists in the commcell or not

        Raises:
            SDKException:

                if type of the classifier name argument is not string

    &#34;&#34;&#34;
    if not isinstance(classifier_name, str):
        raise SDKException(&#39;Classifier&#39;, &#39;101&#39;)

    return self._classifiers and classifier_name.lower() in map(str.lower, self._classifiers)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.Classifiers.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the classifier associated with the commcell.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L1797-L1799" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the classifier associated with the commcell.&#34;&#34;&#34;
    self._classifiers = self._get_all_classifier_entities()</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.EntityManagerTypes"><code class="flex name class">
<span>class <span class="ident">EntityManagerTypes</span></span>
<span>(</span><span>value, names=None, *, module=None, qualname=None, type=None, start=1)</span>
</code></dt>
<dd>
<div class="desc"><p>Class to represent different entity types in entity manager</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L297-L301" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class EntityManagerTypes(Enum):
    &#34;&#34;&#34;Class to represent different entity types in entity manager&#34;&#34;&#34;
    ENTITIES = &#34;Entities&#34;
    CLASSIFIERS = &#34;Classifiers&#34;
    TAGS = &#34;Tags&#34;</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li>enum.Enum</li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.activateapps.entity_manager.EntityManagerTypes.CLASSIFIERS"><code class="name">var <span class="ident">CLASSIFIERS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.EntityManagerTypes.ENTITIES"><code class="name">var <span class="ident">ENTITIES</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.EntityManagerTypes.TAGS"><code class="name">var <span class="ident">TAGS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.Tag"><code class="flex name class">
<span>class <span class="ident">Tag</span></span>
<span>(</span><span>commcell_object, tag_set_name, tag_name, tag_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for performing operations on a single Tag</p>
<p>Initialize an object of the Tag class.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the commcell class</p>
<p>tag_set_name
(str)
&ndash;
name of the TagSet</p>
<p>tag_name
(str)
&ndash;
Name of tag inside TagSet container</p>
<p>tag_id
(str)
&ndash;
id for tag
default: None</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the Tag class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L1535-L1696" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Tag(object):
    &#34;&#34;&#34;Class for performing operations on a single Tag&#34;&#34;&#34;

    def __init__(self, commcell_object, tag_set_name, tag_name, tag_id=None):
        &#34;&#34;&#34;Initialize an object of the Tag class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

                tag_set_name     (str)          --  name of the TagSet

                tag_name         (str)          --  Name of tag inside TagSet container

                tag_id       (str)              --  id for tag
                                                        default: None

            Returns:
                object  -   instance of the Tag class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._services = commcell_object._services
        self._cvpysdk_obj = self._commcell_object._cvpysdk_object
        self._tag_name = tag_name
        self._tag_set_name = tag_set_name
        self._tag_id = None
        self._tag_props = None
        self._api_modify_tag = self._services[&#39;GET_TAGS&#39;]
        if tag_id is None:
            self._tag_id = self._get_tag_id(tag_set_name, tag_name)
        else:
            self._tag_id = tag_id
        self.refresh()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _get_tag_id(self, tag_set_name, tag_name):
        &#34;&#34;&#34; Get Tag id for given tag name
                Args:

                    tag_set_name    (str)   --  Name of the TagSet

                    tag_name        (str)   --  Name of the Tag

                Returns:

                    int                --  Tag id

        &#34;&#34;&#34;
        tags = self._commcell_object.activate.entity_manager(
            entity_type=EntityManagerTypes.TAGS)
        # we need this refresh so that tags gets refreshed after adding new tag inside tagset
        tags.refresh()
        tag_set = tags.get(tag_set_name)
        return tag_set.get_tag_id(tag_name=tag_name)

    def _get_tag_properties(self):
        &#34;&#34;&#34; Get Tag properties for this associated tag object
                Args:

                    None

                Returns:

                    dict    --  containing tag properties

                        Example : {
                                      &#34;tagOwnerType&#34;: 1,
                                      &#34;tagId&#34;: 15865,
                                      &#34;name&#34;: &#34;p10&#34;,
                                      &#34;flags&#34;: 0,
                                      &#34;fullName&#34;: &#34;cvpysdk1\\p10&#34;,
                                      &#34;description&#34;: &#34;&#34;,
                                      &#34;id&#34;: &#34;C9E229D0-B895-4653-9DA7-C9C6BD999121&#34;,
                                      &#34;attribute&#34;: {}
                                    }

        &#34;&#34;&#34;

        tags = self._commcell_object.activate.entity_manager(
            entity_type=EntityManagerTypes.TAGS)
        # we need this refresh so that tags gets refreshed after adding new tag inside tagset
        tags.refresh()
        tags.get(self._tag_set_name).refresh()
        tag_set_dict = tags.get_properties(self._tag_set_name)
        tag_dict = tag_set_dict[&#39;tagsDetails&#39;][self._tag_name.lower()]
        return tag_dict

    def modify(self, new_name):
        &#34;&#34;&#34;Modifies the tag name in the tagset

                Args:

                    new_name        (str)       --  New name for Tag

                Returns:

                    None

                Raises:

                    SDKException:

                                if response is empty

                                if response is not success

                                if unable to modify Tag name

                                if input data type is not valid

        &#34;&#34;&#34;
        if not isinstance(new_name, str):
            raise SDKException(&#39;Tags&#39;, &#39;101&#39;)
        tags = self._commcell_object.activate.entity_manager(
            entity_type=EntityManagerTypes.TAGS)
        tag_set = tags.get(self._tag_set_name)
        request_json = copy.deepcopy(TagConstants.TAG_MODIFY_REQUEST_JSON)
        request_json[&#39;container&#39;][&#39;containerId&#39;] = tag_set.tag_set_id
        request_json[&#39;tags&#39;][0][&#39;tagId&#39;] = self._tag_id
        request_json[&#39;tags&#39;][0][&#39;name&#39;] = new_name
        flag, response = self._cvpysdk_obj.make_request(
            &#39;PUT&#39;, self._api_modify_tag, request_json
        )
        if flag:
            if response.json():
                if &#39;errList&#39; in response.json():
                    raise SDKException(&#39;Tags&#39;, &#39;102&#39;, response.json()[&#39;errList&#39;][0][&#39;errLogMessage&#39;])
                elif &#39;tag&#39; in response.json():
                    self._tag_name = new_name
                    self.refresh()
                    return
            raise SDKException(&#39;Tags&#39;, &#39;105&#39;)
        self._response_not_success(response)

    def refresh(self):
        &#34;&#34;&#34;Refresh the TagSet details for associated object&#34;&#34;&#34;
        self._tag_props = self._get_tag_properties()

    @property
    def full_name(self):
        &#34;&#34;&#34;Returns the full name of the tag inside tagset&#34;&#34;&#34;
        return self._tag_props[&#39;fullName&#39;]

    @property
    def guid(self):
        &#34;&#34;&#34;Returns the tag guid value&#34;&#34;&#34;
        return self._tag_props[&#39;id&#39;]

    @property
    def tag_id(self):
        &#34;&#34;&#34;Returns the id of the tag&#34;&#34;&#34;
        return self._tag_id</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.activateapps.entity_manager.Tag.full_name"><code class="name">var <span class="ident">full_name</span></code></dt>
<dd>
<div class="desc"><p>Returns the full name of the tag inside tagset</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L1683-L1686" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def full_name(self):
    &#34;&#34;&#34;Returns the full name of the tag inside tagset&#34;&#34;&#34;
    return self._tag_props[&#39;fullName&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.Tag.guid"><code class="name">var <span class="ident">guid</span></code></dt>
<dd>
<div class="desc"><p>Returns the tag guid value</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L1688-L1691" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def guid(self):
    &#34;&#34;&#34;Returns the tag guid value&#34;&#34;&#34;
    return self._tag_props[&#39;id&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.Tag.tag_id"><code class="name">var <span class="ident">tag_id</span></code></dt>
<dd>
<div class="desc"><p>Returns the id of the tag</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L1693-L1696" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def tag_id(self):
    &#34;&#34;&#34;Returns the id of the tag&#34;&#34;&#34;
    return self._tag_id</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.activateapps.entity_manager.Tag.modify"><code class="name flex">
<span>def <span class="ident">modify</span></span>(<span>self, new_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Modifies the tag name in the tagset</p>
<h2 id="args">Args</h2>
<p>new_name
(str)
&ndash;
New name for Tag</p>
<h2 id="returns">Returns</h2>
<p>None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>        if response is empty

        if response is not success

        if unable to modify Tag name

        if input data type is not valid
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L1632-L1677" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def modify(self, new_name):
    &#34;&#34;&#34;Modifies the tag name in the tagset

            Args:

                new_name        (str)       --  New name for Tag

            Returns:

                None

            Raises:

                SDKException:

                            if response is empty

                            if response is not success

                            if unable to modify Tag name

                            if input data type is not valid

    &#34;&#34;&#34;
    if not isinstance(new_name, str):
        raise SDKException(&#39;Tags&#39;, &#39;101&#39;)
    tags = self._commcell_object.activate.entity_manager(
        entity_type=EntityManagerTypes.TAGS)
    tag_set = tags.get(self._tag_set_name)
    request_json = copy.deepcopy(TagConstants.TAG_MODIFY_REQUEST_JSON)
    request_json[&#39;container&#39;][&#39;containerId&#39;] = tag_set.tag_set_id
    request_json[&#39;tags&#39;][0][&#39;tagId&#39;] = self._tag_id
    request_json[&#39;tags&#39;][0][&#39;name&#39;] = new_name
    flag, response = self._cvpysdk_obj.make_request(
        &#39;PUT&#39;, self._api_modify_tag, request_json
    )
    if flag:
        if response.json():
            if &#39;errList&#39; in response.json():
                raise SDKException(&#39;Tags&#39;, &#39;102&#39;, response.json()[&#39;errList&#39;][0][&#39;errLogMessage&#39;])
            elif &#39;tag&#39; in response.json():
                self._tag_name = new_name
                self.refresh()
                return
        raise SDKException(&#39;Tags&#39;, &#39;105&#39;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.Tag.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the TagSet details for associated object</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L1679-L1681" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the TagSet details for associated object&#34;&#34;&#34;
    self._tag_props = self._get_tag_properties()</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.TagSet"><code class="flex name class">
<span>class <span class="ident">TagSet</span></span>
<span>(</span><span>commcell_object, tag_set_name, tag_set_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for performing operations on a TagSet</p>
<p>Initialize an object of the TagSet class.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the commcell class</p>
<p>tag_set_name
(str)
&ndash;
name of the TagSet</p>
<p>tag_set_id
(str)
&ndash;
Container id of the TagSet
default: None</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the Tagset class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L1166-L1532" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class TagSet(object):
    &#34;&#34;&#34;Class for performing operations on a TagSet&#34;&#34;&#34;

    def __init__(self, commcell_object, tag_set_name, tag_set_id=None):
        &#34;&#34;&#34;Initialize an object of the TagSet class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

                tag_set_name     (str)          --  name of the TagSet

                tag_set_id       (str)          --  Container id of the TagSet
                                                        default: None

            Returns:
                object  -   instance of the Tagset class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._services = commcell_object._services
        self._cvpysdk_obj = self._commcell_object._cvpysdk_object
        self._tag_set_name = tag_set_name
        self._tag_set_id = None
        self._tag_set_props = None
        if tag_set_id is None:
            self._tag_set_id = self._get_tag_set_id(tag_set_name)
        else:
            self._tag_set_id = tag_set_id
        self._container_guid = None
        self._owner = None
        self._full_name = None
        self._comment = None
        self._tags = None
        self._tag_ids = None
        self._owner_alias_name = None
        self._api_modify_tag_set = self._services[&#39;ADD_CONTAINER&#39;]
        self._api_add_tag = self._services[&#39;GET_TAGS&#39;]
        self._api_security = self._services[&#39;SECURITY_ASSOCIATION&#39;]
        self.refresh()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def has_tag(self, tag_name):
        &#34;&#34;&#34;Returns whether tag exists with given name or not in tagset

                    Args:
                        tag_name (str)      --  name of the Tag

                    Returns:

                        bool    --  True if it exists or else false

                    Raises:
                        SDKException:

                            if tag_name is not of type string


        &#34;&#34;&#34;
        if not isinstance(tag_name, str):
            raise SDKException(&#39;Tags&#39;, &#39;101&#39;)
        if tag_name.lower() in self.tags:
            return True
        return False

    def get(self, tag_name):
        &#34;&#34;&#34;Returns a Tag object for the given Tag name.

            Args:
                tag_name (str)      --  name of the Tag

            Returns:

                obj                 -- Object of Tag class

            Raises:
                SDKException:

                    if unable to create Tag object

                    if tag_name is not of type string


        &#34;&#34;&#34;
        if not isinstance(tag_name, str):
            raise SDKException(&#39;Tags&#39;, &#39;101&#39;)
        if self.has_tag(tag_name):
            return Tag(self._commcell_object, tag_set_name=self._tag_set_name, tag_name=tag_name)
        raise SDKException(&#39;Tags&#39;, &#39;102&#39;, &#34;Unable to get Tag class object&#34;)

    def get_tag_id(self, tag_name):
        &#34;&#34;&#34;Returns the tag id for the given tag name

                Args:

                    tag_name        (str)       --  Name of the tag

                Returns:

                    int     --  Tag id

                Raises:

                    SDKExeption:

                        if input tag name is not found in this tagset

        &#34;&#34;&#34;
        if tag_name.lower() not in self.tags:
            raise SDKException(&#39;Tags&#39;, &#39;106&#39;)
        index = self.tags.index(tag_name.lower())
        return self._tag_ids[index]

    def add_tag(self, tag_name):
        &#34;&#34;&#34;Adds the specified tag name in the tagset container in commcell

                           Args:
                               tag_name (str)     --  name of the Tag

                           Returns:

                               object      --  Object of Tag class

                           Raises:
                               SDKException:

                                       if response is empty

                                       if response is not success

                                       if unable to add Tag inside Tagset in commcell

                                       if input data type is not valid
                       &#34;&#34;&#34;
        if not isinstance(tag_name, str):
            raise SDKException(&#39;Tags&#39;, &#39;101&#39;)
        request_json = copy.deepcopy(TagConstants.TAG_ADD_REQUEST_JSON)
        request_json[&#39;container&#39;][&#39;containerId&#39;] = self._tag_set_id
        request_json[&#39;tags&#39;][0][&#39;name&#39;] = tag_name
        flag, response = self._cvpysdk_obj.make_request(
            &#39;POST&#39;, self._api_add_tag, request_json
        )
        if flag:
            if response.json():
                if &#39;errList&#39; in response.json():
                    raise SDKException(&#39;Tags&#39;, &#39;102&#39;, response.json()[&#39;errList&#39;][0][&#39;errLogMessage&#39;])
                elif &#39;tag&#39; in response.json():
                    self.refresh()
                    return Tag(commcell_object=self._commcell_object, tag_set_name=self._tag_set_name,
                               tag_name=tag_name)
            raise SDKException(&#39;Tags&#39;, &#39;104&#39;)
        self._response_not_success(response)

    def share(self, user_or_group_name, allow_edit_permission=False, is_user=True, ops_type=1):
        &#34;&#34;&#34;Shares tagset with given user or group in commcell

                Args:

                    user_or_group_name      (str)       --  Name of user or group

                    is_user                 (bool)      --  Denotes whether this is user or group name
                                                                default : True(User)

                    allow_edit_permission   (bool)      --  whether to give edit permission or not to user or group

                    ops_type                (int)       --  Operation type

                                                            Default : 1 (Add)

                                                            Supported : 1 (Add)
                                                                        2 (Modify)
                                                                        3 (Delete)

                Returns:

                    None

                Raises:

                    SDKException:

                            if unable to update security associations

                            if response is empty or not success
        &#34;&#34;&#34;
        if not isinstance(user_or_group_name, str):
            raise SDKException(&#39;Tags&#39;, &#39;101&#39;)
        request_json = copy.deepcopy(TagConstants.TAG_SET_SHARE_REQUEST_JSON)
        external_user = False
        if &#39;\\&#39; in user_or_group_name:
            external_user = True

        if is_user:
            user_obj = self._commcell_object.users.get(user_or_group_name)
            user_id = user_obj.user_id
            user_or_group_name = f&#34;\\{user_or_group_name}&#34;
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;userId&#39;] = int(user_id)
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;_type_&#39;] = &#34;13&#34;
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;userName&#39;] = user_or_group_name
        elif external_user:
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;groupId&#39;] = 0
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;_type_&#39;] = &#34;62&#34;
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;externalGroupName&#39;] = user_or_group_name
        else:
            grp_obj = self._commcell_object.user_groups.get(user_or_group_name)
            grp_id = grp_obj.user_group_id
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;userGroupId&#39;] = int(grp_id)
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;_type_&#39;] = &#34;15&#34;
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;userGroupName&#39;] = user_or_group_name

        request_json[&#39;entityAssociated&#39;][&#39;entity&#39;][0][&#39;tagId&#39;] = self._tag_set_id
        request_json[&#39;securityAssociations&#39;][&#39;associationsOperationType&#39;] = ops_type

        if allow_edit_permission:
            request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;properties&#39;][&#39;permissions&#39;].append(
                TagConstants.ADD_PERMISSION)
        flag, response = self._cvpysdk_obj.make_request(
            &#39;POST&#39;, self._api_security, request_json
        )
        if flag:
            if response.json() and &#39;response&#39; in response.json():
                response_json = response.json()[&#39;response&#39;][0]
                error_code = response_json[&#39;errorCode&#39;]
                if error_code != 0:
                    error_message = response_json[&#39;errorString&#39;]
                    raise SDKException(
                        &#39;Tags&#39;,
                        &#39;102&#39;, error_message)
            else:
                raise SDKException(&#39;Tags&#39;, &#39;107&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def modify(self, new_name=None, comment=&#34;Modified from CvPySDK&#34;):
        &#34;&#34;&#34;Modifies the specified tagset in the commcell

                Args:

                    new_name        (str)       --  New name for Tagset

                    comment         (str)       --  New comment which needs to be added for Tagset

                Returns:

                    None

                Raises:

                    SDKException:

                                if response is empty

                                if response is not success

                                if unable to modify TagSet entity in commcell

                                if input is not a valid data type of string

        &#34;&#34;&#34;
        if not isinstance(new_name, str) or not isinstance(comment, str):
            raise SDKException(&#39;Tags&#39;, &#39;101&#39;)
        request_json = copy.deepcopy(TagConstants.TAG_SET_MODIFY_REQUEST_JSON)
        request_json[&#39;container&#39;][&#39;containerName&#39;] = new_name
        request_json[&#39;container&#39;][&#39;comment&#39;] = comment
        request_json[&#39;container&#39;][&#39;containerId&#39;] = self._tag_set_id
        flag, response = self._cvpysdk_obj.make_request(
            &#39;POST&#39;, self._api_modify_tag_set, request_json
        )
        if flag:
            if response.json():
                if &#39;errList&#39; in response.json():
                    raise SDKException(&#39;Tags&#39;, &#39;102&#39;, response.json()[&#39;errList&#39;][0][&#39;errLogMessage&#39;])
                elif &#39;container&#39; in response.json():
                    self._tag_set_name = new_name
                    self.refresh()
                    return
            raise SDKException(&#39;Tags&#39;, &#39;105&#39;)
        self._response_not_success(response)

    def refresh(self):
        &#34;&#34;&#34;Refresh the TagSet details for associated object&#34;&#34;&#34;
        self._tag_set_props = self._get_tag_set_properties()

    def _get_tag_set_properties(self):
        &#34;&#34;&#34; Get TagSet properties for this associated object
                Args:

                    None

                Returns:

                    dict    --  Containing tagset properties

        &#34;&#34;&#34;
        tags = self._commcell_object.activate.entity_manager(
            EntityManagerTypes.TAGS)
        # call refresh before fetching properties
        tags.refresh()
        tag_set_dict = tags.get_properties(self._tag_set_name)
        self._full_name = tag_set_dict[&#39;containerFullName&#39;]
        self._owner = tag_set_dict[&#39;owneruserName&#39;]
        self._comment = tag_set_dict[&#39;comment&#39;]
        self._container_guid = tag_set_dict[&#39;containerGuid&#39;]
        self._tags = tag_set_dict[&#39;tags&#39;]
        self._tag_ids = tag_set_dict[&#39;tagsIds&#39;]
        self._owner_alias_name = tag_set_dict[&#39;owneraliasName&#39;]
        return tag_set_dict

    def _get_tag_set_id(self, tag_set_name):
        &#34;&#34;&#34; Get TagSet container id for given tag set name
                Args:

                    tag_set_name (str)  -- Name of the TagSet

                Returns:

                    int                --  TagSet container Id

        &#34;&#34;&#34;

        return self._commcell_object.activate.entity_manager(
            entity_type=EntityManagerTypes.TAGS).get(tag_set_name).tag_set_id

    @property
    def guid(self):
        &#34;&#34;&#34;Returns the container guid of this Tagset&#34;&#34;&#34;
        return self._container_guid

    @property
    def full_name(self):
        &#34;&#34;&#34;Returns the full name of this Tagset&#34;&#34;&#34;
        return self._full_name

    @property
    def comment(self):
        &#34;&#34;&#34;Returns the comment provided for this Tagset&#34;&#34;&#34;
        return self._comment

    @property
    def owner(self):
        &#34;&#34;&#34;Returns the owner username for this Tagset&#34;&#34;&#34;
        return self._owner

    @property
    def owner_alias_name(self):
        &#34;&#34;&#34;Returns the owner alias name for this Tagset&#34;&#34;&#34;
        return self._owner_alias_name

    @property
    def tags(self):
        &#34;&#34;&#34;Returns the tags present in this tagset&#34;&#34;&#34;
        return self._tags

    @property
    def tag_set_id(self):
        &#34;&#34;&#34;returns the container id for this tagset&#34;&#34;&#34;
        return self._tag_set_id</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.activateapps.entity_manager.TagSet.comment"><code class="name">var <span class="ident">comment</span></code></dt>
<dd>
<div class="desc"><p>Returns the comment provided for this Tagset</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L1509-L1512" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def comment(self):
    &#34;&#34;&#34;Returns the comment provided for this Tagset&#34;&#34;&#34;
    return self._comment</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.TagSet.full_name"><code class="name">var <span class="ident">full_name</span></code></dt>
<dd>
<div class="desc"><p>Returns the full name of this Tagset</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L1504-L1507" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def full_name(self):
    &#34;&#34;&#34;Returns the full name of this Tagset&#34;&#34;&#34;
    return self._full_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.TagSet.guid"><code class="name">var <span class="ident">guid</span></code></dt>
<dd>
<div class="desc"><p>Returns the container guid of this Tagset</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L1499-L1502" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def guid(self):
    &#34;&#34;&#34;Returns the container guid of this Tagset&#34;&#34;&#34;
    return self._container_guid</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.TagSet.owner"><code class="name">var <span class="ident">owner</span></code></dt>
<dd>
<div class="desc"><p>Returns the owner username for this Tagset</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L1514-L1517" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def owner(self):
    &#34;&#34;&#34;Returns the owner username for this Tagset&#34;&#34;&#34;
    return self._owner</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.TagSet.owner_alias_name"><code class="name">var <span class="ident">owner_alias_name</span></code></dt>
<dd>
<div class="desc"><p>Returns the owner alias name for this Tagset</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L1519-L1522" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def owner_alias_name(self):
    &#34;&#34;&#34;Returns the owner alias name for this Tagset&#34;&#34;&#34;
    return self._owner_alias_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.TagSet.tag_set_id"><code class="name">var <span class="ident">tag_set_id</span></code></dt>
<dd>
<div class="desc"><p>returns the container id for this tagset</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L1529-L1532" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def tag_set_id(self):
    &#34;&#34;&#34;returns the container id for this tagset&#34;&#34;&#34;
    return self._tag_set_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.TagSet.tags"><code class="name">var <span class="ident">tags</span></code></dt>
<dd>
<div class="desc"><p>Returns the tags present in this tagset</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L1524-L1527" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def tags(self):
    &#34;&#34;&#34;Returns the tags present in this tagset&#34;&#34;&#34;
    return self._tags</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.activateapps.entity_manager.TagSet.add_tag"><code class="name flex">
<span>def <span class="ident">add_tag</span></span>(<span>self, tag_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds the specified tag name in the tagset container in commcell</p>
<h2 id="args">Args</h2>
<p>tag_name (str)
&ndash;
name of the Tag</p>
<h2 id="returns">Returns</h2>
<p>object
&ndash;
Object of Tag class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if response is empty

    if response is not success

    if unable to add Tag inside Tagset in commcell

    if input data type is not valid
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L1288-L1326" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_tag(self, tag_name):
    &#34;&#34;&#34;Adds the specified tag name in the tagset container in commcell

                       Args:
                           tag_name (str)     --  name of the Tag

                       Returns:

                           object      --  Object of Tag class

                       Raises:
                           SDKException:

                                   if response is empty

                                   if response is not success

                                   if unable to add Tag inside Tagset in commcell

                                   if input data type is not valid
                   &#34;&#34;&#34;
    if not isinstance(tag_name, str):
        raise SDKException(&#39;Tags&#39;, &#39;101&#39;)
    request_json = copy.deepcopy(TagConstants.TAG_ADD_REQUEST_JSON)
    request_json[&#39;container&#39;][&#39;containerId&#39;] = self._tag_set_id
    request_json[&#39;tags&#39;][0][&#39;name&#39;] = tag_name
    flag, response = self._cvpysdk_obj.make_request(
        &#39;POST&#39;, self._api_add_tag, request_json
    )
    if flag:
        if response.json():
            if &#39;errList&#39; in response.json():
                raise SDKException(&#39;Tags&#39;, &#39;102&#39;, response.json()[&#39;errList&#39;][0][&#39;errLogMessage&#39;])
            elif &#39;tag&#39; in response.json():
                self.refresh()
                return Tag(commcell_object=self._commcell_object, tag_set_name=self._tag_set_name,
                           tag_name=tag_name)
        raise SDKException(&#39;Tags&#39;, &#39;104&#39;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.TagSet.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, tag_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a Tag object for the given Tag name.</p>
<h2 id="args">Args</h2>
<p>tag_name (str)
&ndash;
name of the Tag</p>
<h2 id="returns">Returns</h2>
<p>obj
&ndash; Object of Tag class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if unable to create Tag object

if tag_name is not of type string
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L1240-L1263" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, tag_name):
    &#34;&#34;&#34;Returns a Tag object for the given Tag name.

        Args:
            tag_name (str)      --  name of the Tag

        Returns:

            obj                 -- Object of Tag class

        Raises:
            SDKException:

                if unable to create Tag object

                if tag_name is not of type string


    &#34;&#34;&#34;
    if not isinstance(tag_name, str):
        raise SDKException(&#39;Tags&#39;, &#39;101&#39;)
    if self.has_tag(tag_name):
        return Tag(self._commcell_object, tag_set_name=self._tag_set_name, tag_name=tag_name)
    raise SDKException(&#39;Tags&#39;, &#39;102&#39;, &#34;Unable to get Tag class object&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.TagSet.get_tag_id"><code class="name flex">
<span>def <span class="ident">get_tag_id</span></span>(<span>self, tag_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the tag id for the given tag name</p>
<h2 id="args">Args</h2>
<p>tag_name
(str)
&ndash;
Name of the tag</p>
<h2 id="returns">Returns</h2>
<p>int
&ndash;
Tag id</p>
<h2 id="raises">Raises</h2>
<p>SDKExeption:</p>
<pre><code>if input tag name is not found in this tagset
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L1265-L1286" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_tag_id(self, tag_name):
    &#34;&#34;&#34;Returns the tag id for the given tag name

            Args:

                tag_name        (str)       --  Name of the tag

            Returns:

                int     --  Tag id

            Raises:

                SDKExeption:

                    if input tag name is not found in this tagset

    &#34;&#34;&#34;
    if tag_name.lower() not in self.tags:
        raise SDKException(&#39;Tags&#39;, &#39;106&#39;)
    index = self.tags.index(tag_name.lower())
    return self._tag_ids[index]</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.TagSet.has_tag"><code class="name flex">
<span>def <span class="ident">has_tag</span></span>(<span>self, tag_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns whether tag exists with given name or not in tagset</p>
<h2 id="args">Args</h2>
<p>tag_name (str)
&ndash;
name of the Tag</p>
<h2 id="returns">Returns</h2>
<p>bool
&ndash;
True if it exists or else false</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if tag_name is not of type string
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L1217-L1238" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_tag(self, tag_name):
    &#34;&#34;&#34;Returns whether tag exists with given name or not in tagset

                Args:
                    tag_name (str)      --  name of the Tag

                Returns:

                    bool    --  True if it exists or else false

                Raises:
                    SDKException:

                        if tag_name is not of type string


    &#34;&#34;&#34;
    if not isinstance(tag_name, str):
        raise SDKException(&#39;Tags&#39;, &#39;101&#39;)
    if tag_name.lower() in self.tags:
        return True
    return False</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.TagSet.modify"><code class="name flex">
<span>def <span class="ident">modify</span></span>(<span>self, new_name=None, comment='Modified from CvPySDK')</span>
</code></dt>
<dd>
<div class="desc"><p>Modifies the specified tagset in the commcell</p>
<h2 id="args">Args</h2>
<p>new_name
(str)
&ndash;
New name for Tagset</p>
<p>comment
(str)
&ndash;
New comment which needs to be added for Tagset</p>
<h2 id="returns">Returns</h2>
<p>None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>        if response is empty

        if response is not success

        if unable to modify TagSet entity in commcell

        if input is not a valid data type of string
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L1409-L1453" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def modify(self, new_name=None, comment=&#34;Modified from CvPySDK&#34;):
    &#34;&#34;&#34;Modifies the specified tagset in the commcell

            Args:

                new_name        (str)       --  New name for Tagset

                comment         (str)       --  New comment which needs to be added for Tagset

            Returns:

                None

            Raises:

                SDKException:

                            if response is empty

                            if response is not success

                            if unable to modify TagSet entity in commcell

                            if input is not a valid data type of string

    &#34;&#34;&#34;
    if not isinstance(new_name, str) or not isinstance(comment, str):
        raise SDKException(&#39;Tags&#39;, &#39;101&#39;)
    request_json = copy.deepcopy(TagConstants.TAG_SET_MODIFY_REQUEST_JSON)
    request_json[&#39;container&#39;][&#39;containerName&#39;] = new_name
    request_json[&#39;container&#39;][&#39;comment&#39;] = comment
    request_json[&#39;container&#39;][&#39;containerId&#39;] = self._tag_set_id
    flag, response = self._cvpysdk_obj.make_request(
        &#39;POST&#39;, self._api_modify_tag_set, request_json
    )
    if flag:
        if response.json():
            if &#39;errList&#39; in response.json():
                raise SDKException(&#39;Tags&#39;, &#39;102&#39;, response.json()[&#39;errList&#39;][0][&#39;errLogMessage&#39;])
            elif &#39;container&#39; in response.json():
                self._tag_set_name = new_name
                self.refresh()
                return
        raise SDKException(&#39;Tags&#39;, &#39;105&#39;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.TagSet.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the TagSet details for associated object</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L1455-L1457" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the TagSet details for associated object&#34;&#34;&#34;
    self._tag_set_props = self._get_tag_set_properties()</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.TagSet.share"><code class="name flex">
<span>def <span class="ident">share</span></span>(<span>self, user_or_group_name, allow_edit_permission=False, is_user=True, ops_type=1)</span>
</code></dt>
<dd>
<div class="desc"><p>Shares tagset with given user or group in commcell</p>
<h2 id="args">Args</h2>
<p>user_or_group_name
(str)
&ndash;
Name of user or group</p>
<p>is_user
(bool)
&ndash;
Denotes whether this is user or group name
default : True(User)</p>
<p>allow_edit_permission
(bool)
&ndash;
whether to give edit permission or not to user or group</p>
<p>ops_type
(int)
&ndash;
Operation type</p>
<pre><code>                                    Default : 1 (Add)

                                    Supported : 1 (Add)
                                                2 (Modify)
                                                3 (Delete)
</code></pre>
<h2 id="returns">Returns</h2>
<p>None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if unable to update security associations

    if response is empty or not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L1328-L1407" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def share(self, user_or_group_name, allow_edit_permission=False, is_user=True, ops_type=1):
    &#34;&#34;&#34;Shares tagset with given user or group in commcell

            Args:

                user_or_group_name      (str)       --  Name of user or group

                is_user                 (bool)      --  Denotes whether this is user or group name
                                                            default : True(User)

                allow_edit_permission   (bool)      --  whether to give edit permission or not to user or group

                ops_type                (int)       --  Operation type

                                                        Default : 1 (Add)

                                                        Supported : 1 (Add)
                                                                    2 (Modify)
                                                                    3 (Delete)

            Returns:

                None

            Raises:

                SDKException:

                        if unable to update security associations

                        if response is empty or not success
    &#34;&#34;&#34;
    if not isinstance(user_or_group_name, str):
        raise SDKException(&#39;Tags&#39;, &#39;101&#39;)
    request_json = copy.deepcopy(TagConstants.TAG_SET_SHARE_REQUEST_JSON)
    external_user = False
    if &#39;\\&#39; in user_or_group_name:
        external_user = True

    if is_user:
        user_obj = self._commcell_object.users.get(user_or_group_name)
        user_id = user_obj.user_id
        user_or_group_name = f&#34;\\{user_or_group_name}&#34;
        request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;userId&#39;] = int(user_id)
        request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;_type_&#39;] = &#34;13&#34;
        request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;userName&#39;] = user_or_group_name
    elif external_user:
        request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;groupId&#39;] = 0
        request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;_type_&#39;] = &#34;62&#34;
        request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;externalGroupName&#39;] = user_or_group_name
    else:
        grp_obj = self._commcell_object.user_groups.get(user_or_group_name)
        grp_id = grp_obj.user_group_id
        request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;userGroupId&#39;] = int(grp_id)
        request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;_type_&#39;] = &#34;15&#34;
        request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;userOrGroup&#39;][0][&#39;userGroupName&#39;] = user_or_group_name

    request_json[&#39;entityAssociated&#39;][&#39;entity&#39;][0][&#39;tagId&#39;] = self._tag_set_id
    request_json[&#39;securityAssociations&#39;][&#39;associationsOperationType&#39;] = ops_type

    if allow_edit_permission:
        request_json[&#39;securityAssociations&#39;][&#39;associations&#39;][0][&#39;properties&#39;][&#39;permissions&#39;].append(
            TagConstants.ADD_PERMISSION)
    flag, response = self._cvpysdk_obj.make_request(
        &#39;POST&#39;, self._api_security, request_json
    )
    if flag:
        if response.json() and &#39;response&#39; in response.json():
            response_json = response.json()[&#39;response&#39;][0]
            error_code = response_json[&#39;errorCode&#39;]
            if error_code != 0:
                error_message = response_json[&#39;errorString&#39;]
                raise SDKException(
                    &#39;Tags&#39;,
                    &#39;102&#39;, error_message)
        else:
            raise SDKException(&#39;Tags&#39;, &#39;107&#39;)
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.Tags"><code class="flex name class">
<span>class <span class="ident">Tags</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing all the Tagsets in the commcell.</p>
<p>Initializes an instance of the Tags class.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the commcell class</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the Tags class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L886-L1163" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Tags(object):
    &#34;&#34;&#34;Class for representing all the Tagsets in the commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initializes an instance of the Tags class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

            Returns:
                object  -   instance of the Tags class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._tag_set_entities = None
        self._api_get_all_tag_sets = self._services[&#39;GET_TAGS&#39;]
        self._api_add_tag_set = self._services[&#39;ADD_CONTAINER&#39;]
        self._api_delete_tag_set = self._services[&#39;DELETE_CONTAINER&#39;]
        self.refresh()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    @staticmethod
    def _get_tag_sets_from_collections(collections):
        &#34;&#34;&#34;Extracts all the tagsets, and their details from the list of collections given,
            and returns the dictionary of all tagsets

            Args:
                collections     (list)  --  list of all tagsets

            Returns:
                dict    -   dictionary consisting of dictionaries, where each dictionary stores the
                                details of a single tagset

        &#34;&#34;&#34;
        _tag_set_entity = {}
        for tagset in collections[&#39;listOftagSetList&#39;]:
            tagset = tagset[&#39;tagSetsAndItems&#39;]
            container = tagset[0][&#39;container&#39;]
            owner_info = tagset[0][&#39;container&#39;][&#39;ownerInfo&#39;]
            tagset_dict = {}
            tagset_dict[&#39;containerName&#39;] = container.get(&#39;containerName&#39;, &#34;&#34;)
            tagset_dict[&#39;containerFullName&#39;] = container.get(&#39;containerFullName&#39;, &#34;&#34;)
            tagset_dict[&#39;containerId&#39;] = container.get(&#39;containerId&#39;, &#34;&#34;)
            tagset_dict[&#39;containerGuid&#39;] = container.get(&#39;containerGuid&#39;, &#34;&#34;)
            tagset_dict[&#39;comment&#39;] = container.get(&#39;comment&#39;, &#34;&#34;)
            tagset_dict[&#39;owneruserName&#39;] = owner_info.get(&#39;userName&#39;, &#34;&#34;)
            tagset_dict[&#39;owneruserGuid&#39;] = owner_info.get(&#39;userGuid&#39;, &#34;&#34;)
            tagset_dict[&#39;owneraliasName&#39;] = owner_info.get(&#39;aliasName&#39;, &#34;&#34;)
            container_tags = []
            tag_ids = []
            tag_dict = {}
            # process tags only if it is present
            if &#39;tags&#39; in tagset[0]:
                tags = tagset[0][&#39;tags&#39;]
                for tag in tags:
                    tag_dict[tag[&#39;name&#39;].lower()] = tag
                    container_tags.append(tag[&#39;name&#39;].lower())
                    tag_ids.append(tag[&#39;tagId&#39;])
            tagset_dict[&#39;tags&#39;] = container_tags
            tagset_dict[&#39;tagsIds&#39;] = tag_ids
            tagset_dict[&#39;tagsDetails&#39;] = tag_dict
            _tag_set_entity[tagset_dict[&#39;containerName&#39;].lower()] = tagset_dict
        return _tag_set_entity

    def _get_all_tag_sets(self):
        &#34;&#34;&#34;Gets the list of all tagsets associated with this commcell.

            Returns:
                dict    -   dictionary consisting of dictionaries, where each dictionary stores the
                                details of a single tagset entity

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._api_get_all_tag_sets
        )

        if flag:
            if response.json() and &#39;listOftagSetList&#39; in response.json():
                return self._get_tag_sets_from_collections(response.json())
            raise SDKException(&#39;Tags&#39;, &#39;103&#39;)
        self._response_not_success(response)

    def get(self, tag_set_name):
        &#34;&#34;&#34;Returns a TagSet object for the given Tagset name.

            Args:
                tag_set_name (str)  --  name of the TagSet

            Returns:

                obj                 -- Object of TagSet class

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

                    if tag_set_name is not of type string


        &#34;&#34;&#34;
        if not isinstance(tag_set_name, str):
            raise SDKException(&#39;Tags&#39;, &#39;101&#39;)

        if self.has_tag_set(tag_set_name):
            tag_set_id = self._tag_set_entities[tag_set_name.lower()][&#39;containerId&#39;]
            return TagSet(self._commcell_object, tag_set_name=tag_set_name, tag_set_id=tag_set_id)
        raise SDKException(&#39;Tags&#39;, &#39;102&#39;, &#34;Unable to get TagSet class object&#34;)

    def has_tag_set(self, tag_set_name):
        &#34;&#34;&#34;Checks if a tagset exists in the commcell with the input name or not

            Args:
                tag_set_name (str)  --  name of the TagSet

            Returns:
                bool - boolean output whether the TagSet exists in the commcell or not

            Raises:
                SDKException:
                    if type of the TagSet name argument is not string

        &#34;&#34;&#34;
        if not isinstance(tag_set_name, str):
            raise SDKException(&#39;Tags&#39;, &#39;101&#39;)
        return self._tag_set_entities and tag_set_name.lower() in map(str.lower, self._tag_set_entities)

    def get_properties(self, tag_set_name):
        &#34;&#34;&#34;Returns a properties of the specified TagSet name.

            Args:
                tag_set_name (str)  --  name of the TagSet

            Returns:
                dict -  properties for the given TagSet name


                Example : {
                              &#34;containerName&#34;: &#34;cvpysdk1&#34;,
                              &#34;containerFullName&#34;: &#34;cvpysdk1&#34;,
                              &#34;containerId&#34;: 65931,
                              &#34;containerGuid&#34;: &#34;6B870271-543A-4B76-955D-CDEB3807D68E&#34;,
                              &#34;comment&#34;: &#34;Created from CvPySDK&#34;,
                              &#34;owneruserName&#34;: &#34;xxx&#34;,
                              &#34;owneruserGuid&#34;: &#34;C31C1194-AA5C-47C3-B5B0-9087EF429B6B&#34;,
                              &#34;owneraliasName&#34;: &#34;xx&#34;,
                              &#34;tags&#34;: [
                                &#34;p10&#34;
                              ],
                              &#34;tagsIds&#34;: [
                                15865
                              ],
                              &#34;tagsDetails&#34;: {
                                &#34;p10&#34;: {
                                  &#34;tagOwnerType&#34;: 1,
                                  &#34;tagId&#34;: 15865,
                                  &#34;name&#34;: &#34;p10&#34;,
                                  &#34;flags&#34;: 0,
                                  &#34;fullName&#34;: &#34;cvpysdk1\\p10&#34;,
                                  &#34;description&#34;: &#34;&#34;,
                                  &#34;id&#34;: &#34;C9E229D0-B895-4653-9DA7-C9C6BD999121&#34;,
                                  &#34;attribute&#34;: {}
                                }
                              }
                            }


        &#34;&#34;&#34;
        return self._tag_set_entities[tag_set_name.lower()]

    def delete(self, tag_set_name):
        &#34;&#34;&#34;Deletes the specified tagset from the commcell

                Args:

                    tag_set_name    (str)       --  Name of the Tagset

                Returns:

                    None

                Raises:

                    SDKException:

                                if response is empty

                                if response is not success

                                if unable to delete TagSet entity in commcell

                                if input data type is not valid

                                if unable to find TagSet entity in commcell
        &#34;&#34;&#34;
        if not isinstance(tag_set_name, str):
            raise SDKException(&#39;Tags&#39;, &#39;101&#39;)
        if not self.has_tag_set(tag_set_name=tag_set_name):
            raise SDKException(&#39;Tags&#39;, &#39;102&#39;, &#34;Tagset not found&#34;)
        request_json = copy.deepcopy(TagConstants.TAG_SET_DELETE_REQUEST_JSON)
        request_json[&#39;containers&#39;][0][&#39;containerId&#39;] = self._tag_set_entities[tag_set_name.lower()][&#39;containerId&#39;]
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._api_delete_tag_set, request_json
        )
        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                if int(response.json()[&#39;errorCode&#39;]) != 0:
                    raise SDKException(&#39;Tags&#39;, &#39;102&#39;, response.json()[&#39;errorMessage&#39;])
            elif &#39;errList&#39; in response.json():
                raise SDKException(&#39;Tags&#39;, &#39;102&#39;, response.json()[&#39;errList&#39;][0][&#39;errLogMessage&#39;])
            self.refresh()
            return
        self._response_not_success(response)

    def add(self, tag_set_name, comment=&#34;Created from CvPySDK&#34;):
        &#34;&#34;&#34;Adds the specified TagSet name in the commcell

                    Args:
                        tag_set_name (str)     --  name of the TagSet

                        comment (str)         --  Comment for this TagSet

                    Returns:

                        object      --  Object of TagSet class

                    Raises:
                        SDKException:

                                if response is empty

                                if response is not success

                                if unable to add TagSet entity in commcell

                                if input data type is not valid
                &#34;&#34;&#34;
        if not isinstance(tag_set_name, str) or not isinstance(comment, str):
            raise SDKException(&#39;Tags&#39;, &#39;101&#39;)
        request_json = copy.deepcopy(TagConstants.TAG_SET_ADD_REQUEST_JSON)
        request_json[&#39;container&#39;][&#39;containerName&#39;] = tag_set_name
        request_json[&#39;container&#39;][&#39;comment&#39;] = comment
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._api_add_tag_set, request_json
        )
        if flag:
            if response.json():
                if &#39;errList&#39; in response.json():
                    raise SDKException(&#39;Tags&#39;, &#39;102&#39;, response.json()[&#39;errList&#39;][0][&#39;errLogMessage&#39;])
                elif &#39;container&#39; in response.json():
                    self.refresh()
                    return TagSet(commcell_object=self._commcell_object, tag_set_name=tag_set_name)
            raise SDKException(&#39;Tags&#39;, &#39;104&#39;)
        self._response_not_success(response)

    def refresh(self):
        &#34;&#34;&#34;Refresh the TagSet entities associated with the commcell.&#34;&#34;&#34;
        self._tag_set_entities = self._get_all_tag_sets()</code></pre>
</details>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.activateapps.entity_manager.Tags.add"><code class="name flex">
<span>def <span class="ident">add</span></span>(<span>self, tag_set_name, comment='Created from CvPySDK')</span>
</code></dt>
<dd>
<div class="desc"><p>Adds the specified TagSet name in the commcell</p>
<h2 id="args">Args</h2>
<p>tag_set_name (str)
&ndash;
name of the TagSet</p>
<p>comment (str)
&ndash;
Comment for this TagSet</p>
<h2 id="returns">Returns</h2>
<p>object
&ndash;
Object of TagSet class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if response is empty

    if response is not success

    if unable to add TagSet entity in commcell

    if input data type is not valid
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L1120-L1159" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add(self, tag_set_name, comment=&#34;Created from CvPySDK&#34;):
    &#34;&#34;&#34;Adds the specified TagSet name in the commcell

                Args:
                    tag_set_name (str)     --  name of the TagSet

                    comment (str)         --  Comment for this TagSet

                Returns:

                    object      --  Object of TagSet class

                Raises:
                    SDKException:

                            if response is empty

                            if response is not success

                            if unable to add TagSet entity in commcell

                            if input data type is not valid
            &#34;&#34;&#34;
    if not isinstance(tag_set_name, str) or not isinstance(comment, str):
        raise SDKException(&#39;Tags&#39;, &#39;101&#39;)
    request_json = copy.deepcopy(TagConstants.TAG_SET_ADD_REQUEST_JSON)
    request_json[&#39;container&#39;][&#39;containerName&#39;] = tag_set_name
    request_json[&#39;container&#39;][&#39;comment&#39;] = comment
    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._api_add_tag_set, request_json
    )
    if flag:
        if response.json():
            if &#39;errList&#39; in response.json():
                raise SDKException(&#39;Tags&#39;, &#39;102&#39;, response.json()[&#39;errList&#39;][0][&#39;errLogMessage&#39;])
            elif &#39;container&#39; in response.json():
                self.refresh()
                return TagSet(commcell_object=self._commcell_object, tag_set_name=tag_set_name)
        raise SDKException(&#39;Tags&#39;, &#39;104&#39;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.Tags.delete"><code class="name flex">
<span>def <span class="ident">delete</span></span>(<span>self, tag_set_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes the specified tagset from the commcell</p>
<h2 id="args">Args</h2>
<p>tag_set_name
(str)
&ndash;
Name of the Tagset</p>
<h2 id="returns">Returns</h2>
<p>None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>        if response is empty

        if response is not success

        if unable to delete TagSet entity in commcell

        if input data type is not valid

        if unable to find TagSet entity in commcell
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L1076-L1118" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete(self, tag_set_name):
    &#34;&#34;&#34;Deletes the specified tagset from the commcell

            Args:

                tag_set_name    (str)       --  Name of the Tagset

            Returns:

                None

            Raises:

                SDKException:

                            if response is empty

                            if response is not success

                            if unable to delete TagSet entity in commcell

                            if input data type is not valid

                            if unable to find TagSet entity in commcell
    &#34;&#34;&#34;
    if not isinstance(tag_set_name, str):
        raise SDKException(&#39;Tags&#39;, &#39;101&#39;)
    if not self.has_tag_set(tag_set_name=tag_set_name):
        raise SDKException(&#39;Tags&#39;, &#39;102&#39;, &#34;Tagset not found&#34;)
    request_json = copy.deepcopy(TagConstants.TAG_SET_DELETE_REQUEST_JSON)
    request_json[&#39;containers&#39;][0][&#39;containerId&#39;] = self._tag_set_entities[tag_set_name.lower()][&#39;containerId&#39;]
    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._api_delete_tag_set, request_json
    )
    if flag:
        if response.json() and &#39;errorCode&#39; in response.json():
            if int(response.json()[&#39;errorCode&#39;]) != 0:
                raise SDKException(&#39;Tags&#39;, &#39;102&#39;, response.json()[&#39;errorMessage&#39;])
        elif &#39;errList&#39; in response.json():
            raise SDKException(&#39;Tags&#39;, &#39;102&#39;, response.json()[&#39;errList&#39;][0][&#39;errLogMessage&#39;])
        self.refresh()
        return
    self._response_not_success(response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.Tags.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, tag_set_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a TagSet object for the given Tagset name.</p>
<h2 id="args">Args</h2>
<p>tag_set_name (str)
&ndash;
name of the TagSet</p>
<h2 id="returns">Returns</h2>
<p>obj
&ndash; Object of TagSet class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if response is empty</p>
<pre><code>if response is not success

if tag_set_name is not of type string
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L987-L1013" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, tag_set_name):
    &#34;&#34;&#34;Returns a TagSet object for the given Tagset name.

        Args:
            tag_set_name (str)  --  name of the TagSet

        Returns:

            obj                 -- Object of TagSet class

        Raises:
            SDKException:
                if response is empty

                if response is not success

                if tag_set_name is not of type string


    &#34;&#34;&#34;
    if not isinstance(tag_set_name, str):
        raise SDKException(&#39;Tags&#39;, &#39;101&#39;)

    if self.has_tag_set(tag_set_name):
        tag_set_id = self._tag_set_entities[tag_set_name.lower()][&#39;containerId&#39;]
        return TagSet(self._commcell_object, tag_set_name=tag_set_name, tag_set_id=tag_set_id)
    raise SDKException(&#39;Tags&#39;, &#39;102&#39;, &#34;Unable to get TagSet class object&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.Tags.get_properties"><code class="name flex">
<span>def <span class="ident">get_properties</span></span>(<span>self, tag_set_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a properties of the specified TagSet name.</p>
<h2 id="args">Args</h2>
<p>tag_set_name (str)
&ndash;
name of the TagSet</p>
<h2 id="returns">Returns</h2>
<p>dict -
properties for the given TagSet name</p>
<dl>
<dt><code>Example </code></dt>
<dd>{
"containerName": "cvpysdk1",
"containerFullName": "cvpysdk1",
"containerId": 65931,
"containerGuid": "6B870271-543A-4B76-955D-CDEB3807D68E",
"comment": "Created from CvPySDK",
"owneruserName": "xxx",
"owneruserGuid": "C31C1194-AA5C-47C3-B5B0-9087EF429B6B",
"owneraliasName": "xx",
"tags": [
"p10"
],
"tagsIds": [
15865
],
"tagsDetails": {
"p10": {
"tagOwnerType": 1,
"tagId": 15865,
"name": "p10",
"flags": 0,
"fullName": "cvpysdk1\p10",
"description": "",
"id": "C9E229D0-B895-4653-9DA7-C9C6BD999121",
"attribute": {}
}
}
}</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L1033-L1074" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_properties(self, tag_set_name):
    &#34;&#34;&#34;Returns a properties of the specified TagSet name.

        Args:
            tag_set_name (str)  --  name of the TagSet

        Returns:
            dict -  properties for the given TagSet name


            Example : {
                          &#34;containerName&#34;: &#34;cvpysdk1&#34;,
                          &#34;containerFullName&#34;: &#34;cvpysdk1&#34;,
                          &#34;containerId&#34;: 65931,
                          &#34;containerGuid&#34;: &#34;6B870271-543A-4B76-955D-CDEB3807D68E&#34;,
                          &#34;comment&#34;: &#34;Created from CvPySDK&#34;,
                          &#34;owneruserName&#34;: &#34;xxx&#34;,
                          &#34;owneruserGuid&#34;: &#34;C31C1194-AA5C-47C3-B5B0-9087EF429B6B&#34;,
                          &#34;owneraliasName&#34;: &#34;xx&#34;,
                          &#34;tags&#34;: [
                            &#34;p10&#34;
                          ],
                          &#34;tagsIds&#34;: [
                            15865
                          ],
                          &#34;tagsDetails&#34;: {
                            &#34;p10&#34;: {
                              &#34;tagOwnerType&#34;: 1,
                              &#34;tagId&#34;: 15865,
                              &#34;name&#34;: &#34;p10&#34;,
                              &#34;flags&#34;: 0,
                              &#34;fullName&#34;: &#34;cvpysdk1\\p10&#34;,
                              &#34;description&#34;: &#34;&#34;,
                              &#34;id&#34;: &#34;C9E229D0-B895-4653-9DA7-C9C6BD999121&#34;,
                              &#34;attribute&#34;: {}
                            }
                          }
                        }


    &#34;&#34;&#34;
    return self._tag_set_entities[tag_set_name.lower()]</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.Tags.has_tag_set"><code class="name flex">
<span>def <span class="ident">has_tag_set</span></span>(<span>self, tag_set_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks if a tagset exists in the commcell with the input name or not</p>
<h2 id="args">Args</h2>
<p>tag_set_name (str)
&ndash;
name of the TagSet</p>
<h2 id="returns">Returns</h2>
<p>bool - boolean output whether the TagSet exists in the commcell or not</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the TagSet name argument is not string</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L1015-L1031" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_tag_set(self, tag_set_name):
    &#34;&#34;&#34;Checks if a tagset exists in the commcell with the input name or not

        Args:
            tag_set_name (str)  --  name of the TagSet

        Returns:
            bool - boolean output whether the TagSet exists in the commcell or not

        Raises:
            SDKException:
                if type of the TagSet name argument is not string

    &#34;&#34;&#34;
    if not isinstance(tag_set_name, str):
        raise SDKException(&#39;Tags&#39;, &#39;101&#39;)
    return self._tag_set_entities and tag_set_name.lower() in map(str.lower, self._tag_set_entities)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.entity_manager.Tags.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the TagSet entities associated with the commcell.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/entity_manager.py#L1161-L1163" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the TagSet entities associated with the commcell.&#34;&#34;&#34;
    self._tag_set_entities = self._get_all_tag_sets()</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#tagset-attributes">TagSet Attributes</a></li>
<li><a href="#tag-attributes">Tag Attributes</a></li>
<li><a href="#activateentity-attributes">ActivateEntity Attributes</a></li>
<li><a href="#classifier-attributes">Classifier Attributes</a></li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.activateapps" href="index.html">cvpysdk.activateapps</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.activateapps.entity_manager.ActivateEntities" href="#cvpysdk.activateapps.entity_manager.ActivateEntities">ActivateEntities</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.activateapps.entity_manager.ActivateEntities.add" href="#cvpysdk.activateapps.entity_manager.ActivateEntities.add">add</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.ActivateEntities.delete" href="#cvpysdk.activateapps.entity_manager.ActivateEntities.delete">delete</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.ActivateEntities.get" href="#cvpysdk.activateapps.entity_manager.ActivateEntities.get">get</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.ActivateEntities.get_entity_ids" href="#cvpysdk.activateapps.entity_manager.ActivateEntities.get_entity_ids">get_entity_ids</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.ActivateEntities.get_entity_keys" href="#cvpysdk.activateapps.entity_manager.ActivateEntities.get_entity_keys">get_entity_keys</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.ActivateEntities.get_properties" href="#cvpysdk.activateapps.entity_manager.ActivateEntities.get_properties">get_properties</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.ActivateEntities.has_entity" href="#cvpysdk.activateapps.entity_manager.ActivateEntities.has_entity">has_entity</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.ActivateEntities.refresh" href="#cvpysdk.activateapps.entity_manager.ActivateEntities.refresh">refresh</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.activateapps.entity_manager.ActivateEntity" href="#cvpysdk.activateapps.entity_manager.ActivateEntity">ActivateEntity</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.activateapps.entity_manager.ActivateEntity.category_name" href="#cvpysdk.activateapps.entity_manager.ActivateEntity.category_name">category_name</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.ActivateEntity.container_details" href="#cvpysdk.activateapps.entity_manager.ActivateEntity.container_details">container_details</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.ActivateEntity.display_name" href="#cvpysdk.activateapps.entity_manager.ActivateEntity.display_name">display_name</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.ActivateEntity.entity_id" href="#cvpysdk.activateapps.entity_manager.ActivateEntity.entity_id">entity_id</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.ActivateEntity.entity_key" href="#cvpysdk.activateapps.entity_manager.ActivateEntity.entity_key">entity_key</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.ActivateEntity.entity_type" href="#cvpysdk.activateapps.entity_manager.ActivateEntity.entity_type">entity_type</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.ActivateEntity.entity_xml" href="#cvpysdk.activateapps.entity_manager.ActivateEntity.entity_xml">entity_xml</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.ActivateEntity.is_enabled" href="#cvpysdk.activateapps.entity_manager.ActivateEntity.is_enabled">is_enabled</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.ActivateEntity.modify" href="#cvpysdk.activateapps.entity_manager.ActivateEntity.modify">modify</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.ActivateEntity.refresh" href="#cvpysdk.activateapps.entity_manager.ActivateEntity.refresh">refresh</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.activateapps.entity_manager.Classifier" href="#cvpysdk.activateapps.entity_manager.Classifier">Classifier</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.activateapps.entity_manager.Classifier.cancel_training" href="#cvpysdk.activateapps.entity_manager.Classifier.cancel_training">cancel_training</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.Classifier.category_name" href="#cvpysdk.activateapps.entity_manager.Classifier.category_name">category_name</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.Classifier.display_name" href="#cvpysdk.activateapps.entity_manager.Classifier.display_name">display_name</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.Classifier.entity_id" href="#cvpysdk.activateapps.entity_manager.Classifier.entity_id">entity_id</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.Classifier.entity_key" href="#cvpysdk.activateapps.entity_manager.Classifier.entity_key">entity_key</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.Classifier.entity_type" href="#cvpysdk.activateapps.entity_manager.Classifier.entity_type">entity_type</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.Classifier.entity_xml" href="#cvpysdk.activateapps.entity_manager.Classifier.entity_xml">entity_xml</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.Classifier.is_enabled" href="#cvpysdk.activateapps.entity_manager.Classifier.is_enabled">is_enabled</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.Classifier.last_training_time" href="#cvpysdk.activateapps.entity_manager.Classifier.last_training_time">last_training_time</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.Classifier.modify" href="#cvpysdk.activateapps.entity_manager.Classifier.modify">modify</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.Classifier.monitor_training" href="#cvpysdk.activateapps.entity_manager.Classifier.monitor_training">monitor_training</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.Classifier.refresh" href="#cvpysdk.activateapps.entity_manager.Classifier.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.Classifier.sample_details" href="#cvpysdk.activateapps.entity_manager.Classifier.sample_details">sample_details</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.Classifier.start_training" href="#cvpysdk.activateapps.entity_manager.Classifier.start_training">start_training</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.Classifier.sycn_ca_client_id" href="#cvpysdk.activateapps.entity_manager.Classifier.sycn_ca_client_id">sycn_ca_client_id</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.Classifier.trained_ca_cloud_id" href="#cvpysdk.activateapps.entity_manager.Classifier.trained_ca_cloud_id">trained_ca_cloud_id</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.Classifier.training_accuracy" href="#cvpysdk.activateapps.entity_manager.Classifier.training_accuracy">training_accuracy</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.Classifier.training_status" href="#cvpysdk.activateapps.entity_manager.Classifier.training_status">training_status</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.Classifier.upload_data" href="#cvpysdk.activateapps.entity_manager.Classifier.upload_data">upload_data</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.activateapps.entity_manager.Classifiers" href="#cvpysdk.activateapps.entity_manager.Classifiers">Classifiers</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.activateapps.entity_manager.Classifiers.add" href="#cvpysdk.activateapps.entity_manager.Classifiers.add">add</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.Classifiers.delete" href="#cvpysdk.activateapps.entity_manager.Classifiers.delete">delete</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.Classifiers.get" href="#cvpysdk.activateapps.entity_manager.Classifiers.get">get</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.Classifiers.get_entity_ids" href="#cvpysdk.activateapps.entity_manager.Classifiers.get_entity_ids">get_entity_ids</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.Classifiers.get_entity_keys" href="#cvpysdk.activateapps.entity_manager.Classifiers.get_entity_keys">get_entity_keys</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.Classifiers.get_properties" href="#cvpysdk.activateapps.entity_manager.Classifiers.get_properties">get_properties</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.Classifiers.has_classifier" href="#cvpysdk.activateapps.entity_manager.Classifiers.has_classifier">has_classifier</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.Classifiers.refresh" href="#cvpysdk.activateapps.entity_manager.Classifiers.refresh">refresh</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.activateapps.entity_manager.EntityManagerTypes" href="#cvpysdk.activateapps.entity_manager.EntityManagerTypes">EntityManagerTypes</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.activateapps.entity_manager.EntityManagerTypes.CLASSIFIERS" href="#cvpysdk.activateapps.entity_manager.EntityManagerTypes.CLASSIFIERS">CLASSIFIERS</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.EntityManagerTypes.ENTITIES" href="#cvpysdk.activateapps.entity_manager.EntityManagerTypes.ENTITIES">ENTITIES</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.EntityManagerTypes.TAGS" href="#cvpysdk.activateapps.entity_manager.EntityManagerTypes.TAGS">TAGS</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.activateapps.entity_manager.Tag" href="#cvpysdk.activateapps.entity_manager.Tag">Tag</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.activateapps.entity_manager.Tag.full_name" href="#cvpysdk.activateapps.entity_manager.Tag.full_name">full_name</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.Tag.guid" href="#cvpysdk.activateapps.entity_manager.Tag.guid">guid</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.Tag.modify" href="#cvpysdk.activateapps.entity_manager.Tag.modify">modify</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.Tag.refresh" href="#cvpysdk.activateapps.entity_manager.Tag.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.Tag.tag_id" href="#cvpysdk.activateapps.entity_manager.Tag.tag_id">tag_id</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.activateapps.entity_manager.TagSet" href="#cvpysdk.activateapps.entity_manager.TagSet">TagSet</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.activateapps.entity_manager.TagSet.add_tag" href="#cvpysdk.activateapps.entity_manager.TagSet.add_tag">add_tag</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.TagSet.comment" href="#cvpysdk.activateapps.entity_manager.TagSet.comment">comment</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.TagSet.full_name" href="#cvpysdk.activateapps.entity_manager.TagSet.full_name">full_name</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.TagSet.get" href="#cvpysdk.activateapps.entity_manager.TagSet.get">get</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.TagSet.get_tag_id" href="#cvpysdk.activateapps.entity_manager.TagSet.get_tag_id">get_tag_id</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.TagSet.guid" href="#cvpysdk.activateapps.entity_manager.TagSet.guid">guid</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.TagSet.has_tag" href="#cvpysdk.activateapps.entity_manager.TagSet.has_tag">has_tag</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.TagSet.modify" href="#cvpysdk.activateapps.entity_manager.TagSet.modify">modify</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.TagSet.owner" href="#cvpysdk.activateapps.entity_manager.TagSet.owner">owner</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.TagSet.owner_alias_name" href="#cvpysdk.activateapps.entity_manager.TagSet.owner_alias_name">owner_alias_name</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.TagSet.refresh" href="#cvpysdk.activateapps.entity_manager.TagSet.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.TagSet.share" href="#cvpysdk.activateapps.entity_manager.TagSet.share">share</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.TagSet.tag_set_id" href="#cvpysdk.activateapps.entity_manager.TagSet.tag_set_id">tag_set_id</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.TagSet.tags" href="#cvpysdk.activateapps.entity_manager.TagSet.tags">tags</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.activateapps.entity_manager.Tags" href="#cvpysdk.activateapps.entity_manager.Tags">Tags</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.activateapps.entity_manager.Tags.add" href="#cvpysdk.activateapps.entity_manager.Tags.add">add</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.Tags.delete" href="#cvpysdk.activateapps.entity_manager.Tags.delete">delete</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.Tags.get" href="#cvpysdk.activateapps.entity_manager.Tags.get">get</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.Tags.get_properties" href="#cvpysdk.activateapps.entity_manager.Tags.get_properties">get_properties</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.Tags.has_tag_set" href="#cvpysdk.activateapps.entity_manager.Tags.has_tag_set">has_tag_set</a></code></li>
<li><code><a title="cvpysdk.activateapps.entity_manager.Tags.refresh" href="#cvpysdk.activateapps.entity_manager.Tags.refresh">refresh</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>