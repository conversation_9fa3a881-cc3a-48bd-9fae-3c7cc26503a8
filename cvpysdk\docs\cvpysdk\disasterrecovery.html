<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.disasterrecovery API documentation</title>
<meta name="description" content="main file for performing disaster recovery operations on commcell …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.disasterrecovery</code></h1>
</header>
<section id="section-intro">
<p>main file for performing disaster recovery operations on commcell</p>
<p>DisasterRecovery
:
Class for performing DR backup with various options.</p>
<p>DisasterRecoveryManagement
:
Class for performing disaster recovery management operations.</p>
<h1 id="disasterrecovery">DisasterRecovery:</h1>
<pre><code>__init__()                    --    initializes DisasterRecovery class object

reset_to_defaults()           --    resets the properties to default values

disaster_recovery_backup()    --    function to run DR backup

_process_drbackup_response()  --    process the disaster recovery backup request

restore_out_of_place()        --    function to run DR restore operation

_advanced_dr_backup()         --    includes advance dr backup options

_generatedrbackupjson()       --    Generate JSON corresponds to DR backup job

_process_createtask_response()--    Runs the CreateTask API with the request JSON
                                    provided for DR backup.

_filter_paths()               --    Filters the paths based on the Operating System and Agent.
</code></pre>
<h1 id="disasterrecovery-attributes">DisasterRecovery Attributes</h1>
<pre><code>**backuptype**                --    set or get backup type
**is_compression_enabled**    --    set or get compression property
**is_history_db_enabled**     --    set or get history db property
**is_workflow_db_enabled**    --    set or get workflow db property
**is_appstudio_db_enabled**   --    set or get appstudio db property
**is_cvcloud_db_enabled**     --    set or get cvcloud db property
**is_dm2_db_enabled**         --    set or get dm2db property
**client_list**               --    set or get client list property.
</code></pre>
<h1 id="disasterrecoverymanagement">DisasterRecoveryManagement:</h1>
<pre><code>__init__()                          --   initializes DisasterRecoveryManagement class object

_get_dr_properties()                --   Executes get request on server and retrives the dr settings

_set_dr_properties()                --   Executes post request on server and sets the dr settings

refresh()                           --   retrives the latest dr settings

set_local_dr_path                   --   sets the local dr path

set_network_dr_path                 --   sets the unc path

upload_metdata_to_commvault_cloud   --   sets ths account to be used for commvault cloud backup.

upload_metdata_to_cloud_library     --   sets the libarary to be used for cloud backup.

impersonate_user                    --   account to be used for execution of pre/post scripts

use_impersonate_user                --  gets the setting use_impersonate_user
</code></pre>
<h1 id="disasterrecoverymanagement-attributes">DisasterRecoveryManagement Attributes:</h1>
<pre><code>**number_of_metadata**                  --  set or get number of metadata to be retained property
**use_vss**                             --  set or get use vss property
**wild_card_settings**                  --  set or get wild card settings property
**backup_metadata_folder**              --  get backup metadata folder property
**upload_backup_metadata_to_cloud**     --  get upload backup metadata to cloud property
**upload_backup_metadata_to_cloud_lib** --  get upload backup metadata to cloud lib.
**dr_storage_policy**                   --  set or get dr storage policy property
**pre_scan_process**                    --  set or get pre scan process
**post_scan_process**                   --  set or get post scan process
**pre_backup_process**                  --  set or get pre backup process
**post_backup_process**                 --  set or get post backup process
**run_post_scan_process**               --  set or get run post scan process
**run_post_backup_process**             --  set or get run post backup process
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/disasterrecovery.py#L1-L1256" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-
# pylint: disable=W0212,W0201
# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;main file for performing disaster recovery operations on commcell

DisasterRecovery                :   Class for performing DR backup with various options.

DisasterRecoveryManagement      :   Class for performing disaster recovery management operations.

DisasterRecovery:
=================

    __init__()                    --    initializes DisasterRecovery class object

    reset_to_defaults()           --    resets the properties to default values

    disaster_recovery_backup()    --    function to run DR backup

    _process_drbackup_response()  --    process the disaster recovery backup request

    restore_out_of_place()        --    function to run DR restore operation

    _advanced_dr_backup()         --    includes advance dr backup options

    _generatedrbackupjson()       --    Generate JSON corresponds to DR backup job

    _process_createtask_response()--    Runs the CreateTask API with the request JSON
                                        provided for DR backup.

    _filter_paths()               --    Filters the paths based on the Operating System and Agent.


DisasterRecovery Attributes
==========================

    **backuptype**                --    set or get backup type
    **is_compression_enabled**    --    set or get compression property
    **is_history_db_enabled**     --    set or get history db property
    **is_workflow_db_enabled**    --    set or get workflow db property
    **is_appstudio_db_enabled**   --    set or get appstudio db property
    **is_cvcloud_db_enabled**     --    set or get cvcloud db property
    **is_dm2_db_enabled**         --    set or get dm2db property
    **client_list**               --    set or get client list property.

DisasterRecoveryManagement:
==========================

    __init__()                          --   initializes DisasterRecoveryManagement class object

    _get_dr_properties()                --   Executes get request on server and retrives the dr settings

    _set_dr_properties()                --   Executes post request on server and sets the dr settings

    refresh()                           --   retrives the latest dr settings

    set_local_dr_path                   --   sets the local dr path

    set_network_dr_path                 --   sets the unc path

    upload_metdata_to_commvault_cloud   --   sets ths account to be used for commvault cloud backup.

    upload_metdata_to_cloud_library     --   sets the libarary to be used for cloud backup.

    impersonate_user                    --   account to be used for execution of pre/post scripts

    use_impersonate_user                --  gets the setting use_impersonate_user

DisasterRecoveryManagement Attributes:
=====================================

    **number_of_metadata**                  --  set or get number of metadata to be retained property
    **use_vss**                             --  set or get use vss property
    **wild_card_settings**                  --  set or get wild card settings property
    **backup_metadata_folder**              --  get backup metadata folder property
    **upload_backup_metadata_to_cloud**     --  get upload backup metadata to cloud property
    **upload_backup_metadata_to_cloud_lib** --  get upload backup metadata to cloud lib.
    **dr_storage_policy**                   --  set or get dr storage policy property
    **pre_scan_process**                    --  set or get pre scan process
    **post_scan_process**                   --  set or get post scan process
    **pre_backup_process**                  --  set or get pre backup process
    **post_backup_process**                 --  set or get post backup process
    **run_post_scan_process**               --  set or get run post scan process
    **run_post_backup_process**             --  set or get run post backup process

&#34;&#34;&#34;
from base64 import b64encode
from cvpysdk.policies.storage_policies import StoragePolicy
from cvpysdk.storage import DiskLibrary
from .job import Job
from .exception import SDKException
from .client import Client
from .constants import AppIDAType


class DisasterRecovery(object):
    &#34;&#34;&#34;Class to perform all the disaster recovery operations on commcell&#34;&#34;&#34;

    def __init__(self, commcell):
        &#34;&#34;&#34;Initializes DisasterRecovery object

            Args:
                commcell            (object)    --  instance of commcell

        &#34;&#34;&#34;
        self.commcell = commcell
        self.client = Client(self.commcell, self.commcell.commserv_name)
        self.path = self.client.install_directory
        self._RESTORE = self.commcell._services[&#39;RESTORE&#39;]
        self._CREATE_TASK = self.commcell._services[&#39;CREATE_TASK&#39;]
        self.advbackup = False
        self._disaster_recovery_management = None
        self.reset_to_defaults()

    def reset_to_defaults(self):
        &#34;&#34;&#34;
        Resets the instance variables to default values

            Returns:
                 None
        &#34;&#34;&#34;
        self._backup_type = &#34;full&#34;
        self._is_compression_enabled = True
        self._is_history_db_enabled = True
        self._is_workflow_db_enabled = True
        self._is_appstudio_db_enabled = True
        self._is_cvcloud_db_enabled = True
        self._is_dm2_db_enabled = True
        self._client_list = None
        self.advanced_job_options = None

    def disaster_recovery_backup(self):
        &#34;&#34;&#34;Runs a DR job for Commserv

            Returns:
                object - instance of the Job class for this backup job

            Raises:
                SDKException:
                    if backup level specified is not correct

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;

        if self._backup_type.lower() not in [&#39;full&#39;, &#39;differential&#39;]:
            raise SDKException(&#39;Response&#39;, &#39;103&#39;)
        backuptypes = {&#34;full&#34;: 1, &#34;differential&#34;: 3}
        if self.advbackup:
            self._backup_type = backuptypes[self._backup_type.lower()]
            return self._advanced_dr_backup()
        dr_service = self.commcell._services[&#39;DRBACKUP&#39;]
        request_json = {&#34;isCompressionEnabled&#34;: self._is_compression_enabled,
                        &#34;jobType&#34;: 1, &#34;backupType&#34;: backuptypes[self.backup_type.lower()]}
        flag, response = self.commcell._cvpysdk_object.make_request(
            &#39;POST&#39;, dr_service, request_json
        )
        return self._process_drbackup_response(flag, response)

    def _process_drbackup_response(self, flag, response):
        &#34;&#34;&#34;DR Backup response will be processed.

            Args:
                flag, response  (str)  --  results of DR backup JSON request

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if job initialization failed

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if flag:
            if response.json():
                if &#34;jobIds&#34; in response.json():
                    return Job(self.commcell, response.json()[&#39;jobIds&#39;][0])
                if &#34;errorCode&#34; in response.json():
                    o_str = &#39;Initializing backup failed\nError: &#34;{0}&#34;&#39;.format(
                        response.json()[&#39;errorMessage&#39;]
                    )
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;, o_str)
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        response_string = self.commcell._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def restore_out_of_place(
            self,
            client,
            destination_path,
            overwrite=True,
            restore_data_and_acl=True,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            fs_options=None,
            restore_jobs=[]):
        &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the input client,
            at the specified destionation location.

            Args:
                client                (str/object) --  either the name of the client or
                                                           the instance of the Client

                destination_path      (str)        --  full path of the restore location on client

                paths                 (list)       --  list of full paths of
                                                           files/folders to restore

                overwrite             (bool)       --  unconditional overwrite files during restore
                    default: True

                restore_data_and_acl  (bool)       --  restore data and ACL files
                    default: True

                copy_precedence         (int)   --  copy precedence value of storage policy copy
                    default: None

                from_time           (str)       --  time to retore the contents after
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time           (str)         --  time to retore the contents before
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                fs_options      (dict)          -- dictionary that includes all advanced options
                    options:
                        preserve_level      : preserve level option to set in restore
                        proxy_client        : proxy that needed to be used for restore
                        impersonate_user    : Impersonate user options for restore
                        impersonate_password: Impersonate password option for restore
                                                in base64 encoded form
                        all_versions        : if set to True restores all the versions of the
                                                specified file
                        versions            : list of version numbers to be backed up

                restore_jobs    (list)      --  list of jobs to be restored if the job is index free restore

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if client is not a string or Client instance

                    if destination_path is not a string

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if not ((isinstance(client, (str, Client))
                 and isinstance(destination_path, str)
                 and isinstance(overwrite, bool) and isinstance(restore_data_and_acl, bool))):
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)

        if fs_options is None:
            fs_options = {}

        if isinstance(client, Client):
            client = client
        elif isinstance(client, str):
            client = Client(self.commcell, client)
        else:
            raise SDKException(&#39;Response&#39;, &#39;105&#39;)

        agent_obj = client.agents.get(&#34;File System&#34;)
        drpath = self.path + &#34;\\CommserveDR&#34;
        destination_path = self._filter_paths([destination_path], True, agent_id=agent_obj.agent_id)
        drpath = [self._filter_paths([drpath], True, agent_id=agent_obj.agent_id)]
        if not drpath:
            raise SDKException(&#39;Response&#39;, &#39;104&#39;)
        instance_obj = agent_obj.instances.get(&#34;DefaultInstanceName&#34;)

        instance_obj._restore_association = {
            &#34;type&#34;: &#34;0&#34;,
            &#34;backupsetName&#34;: &#34;DR-BackupSet&#34;,
            &#34;instanceName&#34;: &#34;DefaultInstanceName&#34;,
            &#34;appName&#34;: &#34;CommServe Management&#34;,
            &#34;clientName&#34;: self.commcell.commserv_name,
            &#34;consumeLicense&#34;: True,
            &#34;clientSidePackage&#34;: True,
            &#34;subclientName&#34;: &#34;&#34;
        }
        return instance_obj._restore_out_of_place(
            client,
            destination_path,
            paths=drpath,
            overwrite=overwrite,
            restore_data_and_acl=restore_data_and_acl,
            copy_precedence=copy_precedence,
            from_time=from_time,
            to_time=to_time,
            fs_options=fs_options,
            restore_jobs=restore_jobs)

    def _advanced_dr_backup(self):
        &#34;&#34;&#34;Runs a DR job with JSON input

            Returns:
                object - instance of the Job class for this backup job

            Raises:
                SDKException:
                    if backup level specified is not correct

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        request_json = self._generatedrbackupjson()
        return self._process_createtask_response(request_json)

    def _generatedrbackupjson(self):
        &#34;&#34;&#34;
        Generate JSON corresponds to DR backup job
        &#34;&#34;&#34;
        try:
            self._task = {
                &#34;taskFlags&#34;: {&#34;disabled&#34;: False},
                &#34;policyType&#34;: &#34;DATA_PROTECTION&#34;,
                &#34;taskType&#34;: &#34;IMMEDIATE&#34;,
                &#34;initiatedFrom&#34;: 1
            }
            self._subtask = {
                &#34;subTaskType&#34;: &#34;ADMIN&#34;,
                &#34;operationType&#34;: &#34;DRBACKUP&#34;
            }
            clientdict = []
            if self._client_list is not None:
                for client in self._client_list:
                    client = {
                        &#34;type&#34;: 0,
                        &#34;clientName&#34;: client,
                        &#34;clientSidePackage&#34;: True,
                        &#34;consumeLicense&#34;: True}
                    clientdict.append(client)

            common_opts = None
            if self.advanced_job_options:
                common_opts = {
                    &#34;startUpOpts&#34;: {
                        &#34;priority&#34;: self.advanced_job_options.get(&#34;priority&#34;, 66),
                        &#34;startInSuspendedState&#34;: self.advanced_job_options.get(&#34;start_in_suspended_state&#34;, False),
                        &#34;startWhenActivityIsLow&#34;: self.advanced_job_options.get(&#34;start_when_activity_is_low&#34;, False),
                        &#34;useDefaultPriority&#34;: self.advanced_job_options.get(&#34;use_default_priority&#34;, True)
                    },
                    &#34;jobRetryOpts&#34;: {
                        &#34;runningTime&#34;: {
                            &#34;enableTotalRunningTime&#34;: self.advanced_job_options.get(
                                &#34;enable_total_running_time&#34;, False),
                            &#34;totalRunningTime&#34;: self.advanced_job_options.get(&#34;total_running_time&#34;, 3600)
                        },
                        &#34;enableNumberOfRetries&#34;: self.advanced_job_options.get(&#34;enable_number_of_retries&#34;, False),
                        &#34;killRunningJobWhenTotalRunningTimeExpires&#34;: self.advanced_job_options.get(
                            &#34;kill_running_job_when_total_running_time_expires&#34;, False),
                        &#34;numberOfRetries&#34;: self.advanced_job_options.get(&#34;number_of_retries&#34;, 0)
                    },
                    &#34;jobDescription&#34;: self.advanced_job_options.get(&#34;job_description&#34;, &#34;&#34;)
                }

            self._droptions = {
                &#34;drbackupType&#34;: self._backup_type, &#34;dbName&#34;: &#34;commserv&#34;,
                &#34;backupHistoryDataBase&#34;: self.is_history_db_enabled,
                &#34;backupWFEngineDataBase&#34;: self.is_workflow_db_enabled,
                &#34;backupAppStudioDataBase&#34;: self.is_appstudio_db_enabled,
                &#34;backupCVCloudDataBase&#34;: self.is_cvcloud_db_enabled,
                &#34;backupDM2DataBase&#34;: self.is_dm2_db_enabled,
                &#34;enableDatabasesBackupCompression&#34;: self.is_compression_enabled,
                &#34;client&#34;: clientdict

            }

            request_json = {
                &#34;taskInfo&#34;:
                {
                    &#34;task&#34;: self._task,
                    &#34;subTasks&#34;:
                    [{
                        &#34;subTaskOperation&#34;: 1,
                        &#34;subTask&#34;: self._subtask,
                        &#34;options&#34;:
                        {
                            &#34;adminOpts&#34;:
                            {
                                &#34;drBackupOption&#34;: self._droptions,
                                &#34;contentIndexingOption&#34;:
                                {
                                    &#34;subClientBasedAnalytics&#34;: False
                                }
                            },
                            &#34;restoreOptions&#34;:
                            {
                                &#34;virtualServerRstOption&#34;:
                                {
                                    &#34;isBlockLevelReplication&#34;: False
                                }
                            }
                        }
                    }
                    ]
                }
            }

            if self.advanced_job_options:
                request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;commonOpts&#34;] = common_opts

            return request_json
        except Exception as err:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, err)

    def _process_createtask_response(self, request_json):
        &#34;&#34;&#34;Runs the CreateTask API with the request JSON provided for DR backup,
            and returns the contents after parsing the response.

            Args:
                request_json    (dict)  --  JSON request to run for the API

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if restore job failed

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self.commcell._cvpysdk_object.make_request(
            &#39;POST&#39;, self._CREATE_TASK, request_json
        )
        if flag:
            if response.json():
                if &#34;jobIds&#34; in response.json():
                    return Job(self.commcell, response.json()[&#39;jobIds&#39;][0])
                if &#34;errorCode&#34; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]

                    o_str = &#39;DR backup job failed\nError: &#34;{0}&#34;&#39;.format(
                        error_message)
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;, o_str)
                raise SDKException(
                    &#39;Response&#39;, &#39;102&#39;, &#39;Failed to run the DR backup job&#39;)
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        response_string = self.commcell._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _filter_paths(self, paths, is_single_path=False, agent_id=None):
        &#34;&#34;&#34;Filters the paths based on the Operating System, and Agent.

            Args:
                paths           (list)  --  list containing paths to be filtered

                is_single_path  (bool)  --  boolean specifying whether to return a single path
                                                or the entire list
                agent_id        (str)   --   File system agent id
            Returns:
                list    -   if the boolean is_single_path is set to False

                str     -   if the boolean is_single_path is set to True
        &#34;&#34;&#34;
        for index, path in enumerate(paths):
            # &#34;if&#34; condition is default i.e. if client is not provided
            if agent_id is None or int(agent_id) == AppIDAType.WINDOWS_FILE_SYSTEM.value:
                path = path.strip(&#39;\\&#39;).strip(&#39;/&#39;)
                if path:
                    path = path.replace(&#39;/&#39;, &#39;\\&#39;)
                else:
                    path = &#39;\\&#39;
            elif int(agent_id) == AppIDAType.LINUX_FILE_SYSTEM.value:
                path = path.strip(&#39;\\&#39;).strip(&#39;/&#39;)
                if path:
                    path = path.replace(&#39;\\&#39;, &#39;/&#39;)
                else:
                    path = &#39;\\&#39;
                path = &#39;/&#39; + path
            paths[index] = path

        if is_single_path:
            return paths[0]
        return paths

    @property
    def client_list(self):
        &#34;&#34;&#34;Treats the client_list as a read-only attribute.&#34;&#34;&#34;
        return self._client_list

    @client_list.setter
    def client_list(self, value):
        &#34;&#34;&#34;Treats the client_list as a read-only attribute.&#34;&#34;&#34;
        if isinstance(value, list):
            self._client_list = value
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    @property
    def is_compression_enabled(self):
        &#34;&#34;&#34;Treats the iscompressionenabled as a read-only attribute.&#34;&#34;&#34;
        return self._is_compression_enabled

    @is_compression_enabled.setter
    def is_compression_enabled(self, value):
        &#34;&#34;&#34;Treats the iscompressionenabled as a read-only attribute.&#34;&#34;&#34;
        if isinstance(value, bool):
            self._is_compression_enabled = value
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    @property
    def backup_type(self):
        &#34;&#34;&#34;Treats the backup_type as a read-only attribute.&#34;&#34;&#34;
        return self._backup_type

    @backup_type.setter
    def backup_type(self, value):
        &#34;&#34;&#34;Treats the backup_type as a read-only attribute.&#34;&#34;&#34;
        if isinstance(value, str):
            self._backup_type = value
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    @property
    def is_history_db_enabled(self):
        &#34;&#34;&#34;Treats the historydb as a read-only attribute.&#34;&#34;&#34;
        return self._is_history_db_enabled

    @is_history_db_enabled.setter
    def is_history_db_enabled(self, value):
        &#34;&#34;&#34;sets the value of ishistorydb

            Args:
                value   (bool)      --  True/False
         &#34;&#34;&#34;
        if isinstance(value, bool):
            self._is_history_db_enabled = value
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    @property
    def is_workflow_db_enabled(self):
        &#34;&#34;&#34;Treats the workflowdb as a read-only attribute.&#34;&#34;&#34;
        return self._is_workflow_db_enabled

    @is_workflow_db_enabled.setter
    def is_workflow_db_enabled(self, value):
        &#34;&#34;&#34;
        sets the value of isworkflowdb

            Args:
                value   (bool)      --  True/False
         &#34;&#34;&#34;
        if isinstance(value, bool):
            self._is_workflow_db_enabled = value
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    @property
    def is_appstudio_db_enabled(self):
        &#34;&#34;&#34;Treats the workflowdb as a read-only attribute.&#34;&#34;&#34;
        return self._is_appstudio_db_enabled

    @is_appstudio_db_enabled.setter
    def is_appstudio_db_enabled(self, value):
        &#34;&#34;&#34;
        sets the value of isappstudiodb

            Args:
                value   (bool)      --  True/False
         &#34;&#34;&#34;
        if isinstance(value, bool):
            self._is_appstudio_db_enabled = value
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    @property
    def is_cvcloud_db_enabled(self):
        &#34;&#34;&#34;Treats the cvclouddb as a read-only attribute.&#34;&#34;&#34;
        return self._is_cvcloud_db_enabled

    @is_cvcloud_db_enabled.setter
    def is_cvcloud_db_enabled(self, value):
        &#34;&#34;&#34;
        sets the value of iscvclouddb

            Args:
                value   (bool)      --  True/False
         &#34;&#34;&#34;
        if isinstance(value, bool):
            self._is_cvcloud_db_enabled = value
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    @property
    def is_dm2_db_enabled(self):
        &#34;&#34;&#34;Treats the dm2db as a read-only attribute.&#34;&#34;&#34;
        return self._is_dm2_db_enabled

    @is_dm2_db_enabled.setter
    def is_dm2_db_enabled(self, value):
        &#34;&#34;&#34;
        sets the value of isdm2db

            Args:
                value   (bool)      --  True/False
         &#34;&#34;&#34;
        if isinstance(value, bool):
            self._is_dm2_db_enabled = value
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    @property
    def disaster_recovery_management(self):
        &#34;&#34;&#34;
        Returns the instance of the DisasterRecoveryManagement class
        &#34;&#34;&#34;
        if self._disaster_recovery_management is None:
            self._disaster_recovery_management = DisasterRecoveryManagement(self.commcell)
        return self._disaster_recovery_management


class DisasterRecoveryManagement(object):
    &#34;&#34;&#34;Class to perform all the disaster recovery management operations on commcell&#34;&#34;&#34;

    def __init__(self, commcell):
        &#34;&#34;&#34;Initializes DisasterRecoveryManagement object

            Args:
            commcell    (object)    --  instance of commcell

        &#34;&#34;&#34;
        self._commcell = commcell
        self.services = self._commcell._services
        self._service = self.services[&#39;DISASTER_RECOVERY_PROPERTIES&#39;]
        self._cvpysdk_object = self._commcell._cvpysdk_object
        self.refresh()

    def _get_dr_properties(self):
        &#34;&#34;&#34;
        Executes a request on the server to get the settings of disaster recovery Backup.

            Returns:
                None

            Raises:
                SDKException
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(method=&#39;GET&#39;, url=self._service)
        if flag:
            if response and response.json():
                self._settings_dict = response.json()
                if self._settings_dict.get(&#39;errorCode&#39;, 0) != 0:
                    raise SDKException(&#39;Job&#39;, &#39;102&#39;, &#39;Failed to get dr management properties. \nError: {0}&#39;.format(
                        self._settings_dict.get(&#39;errorMessage&#39;, &#39;&#39;)))
                if &#39;drBackupInfo&#39; in self._settings_dict:
                    self._prepost_settings = self._settings_dict.get(&#39;drBackupInfo&#39;).get(&#39;prePostProcessSettings&#39;, {})
                    self._export_settings = self._settings_dict.get(&#39;drBackupInfo&#39;).get(&#39;exportSettings&#39;, {})
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _set_dr_properties(self):
        &#34;&#34;&#34;
        Executes a request on the server, to set the dr settings.

         Returns:
               None
         Raises:
              SDKException:
                    if given inputs are invalid.

        &#34;&#34;&#34;

        flag, response = self._cvpysdk_object.make_request(method=&#39;POST&#39;, url=self._service,
                                                           payload=self._settings_dict)
        if flag:
            if response and response.json():
                if response.json().get(&#39;response&#39;) and response.json().get(&#39;response&#39;)[0].get(&#39;errorCode&#39;) != 0:
                    raise SDKException(&#39;DisasterRecovery&#39;, &#39;102&#39;, &#39;Failed to set dr properties. Error: {0}&#39;.format(
                        response.json().get(&#39;response&#39;)[0].get(&#39;errorString&#39;)
                    ))
                self.refresh()
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)

    def _get_drbackup_options(self):
        &#34;&#34;&#34;
        Returns : dict of dr backup options
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            method=&#39;GET&#39;,
            url=self.services[&#39;DISASTER_RECOVERY_OPTIONS&#39;]
        )
        if flag:
            if response and response.json():
                return response.json()
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)

    def get_cloud_regions(self):
        &#34;&#34;&#34;
        Returns : dict of available dr backup regions.
                    Ex:
                    {
                        &#34;defaultRegion&#34;: &#34;southindia&#34;,
                        &#34;regions&#34;: [
                            {
                                &#34;regionCode&#34;: &#34;eastus2&#34;,
                                &#34;displayName&#34;: &#34;East US 2&#34;
                            }
                            {
                                &#34;regionCode&#34;: &#34;southindia&#34;,
                                &#34;displayName&#34;: &#34;(Asia Pacific) South India&#34;
                            }
                        ]
                    }
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            method=&#39;GET&#39;,
            url=self.services[&#39;DRBACKUP_REGIONS&#39;]
        )
        if flag:
            if response and response.json():
                return response.json()
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)

    def _set_commvault_cloud_upload(self, flag, region=None):
        &#34;&#34;&#34;
        Executes a request on the server, to set the dr settings for commvault cloud upload.
         Args:
             flag   (bool)   : True to enable commvault cloud upload, False to disable
             region (str)    : To select the region for the DR backup to be uploaded to.
                                None will leave the region to be in default region.
         Returns:
               None
         Raises:
              SDKException:
                    if given inputs are invalid.

        &#34;&#34;&#34;
        current_options = self._get_drbackup_options()
        current_options[&#39;properties&#39;][&#39;uploadBackupMetadataToCloud&#39;] = flag
        if flag:
            current_options[&#39;properties&#39;][&#39;region&#39;] = region if region else self.get_cloud_regions()[&#39;defaultRegion&#39;]

        flag, response = self._cvpysdk_object.make_request(
                                    method=&#39;POST&#39;,
                                    url=self.services[&#39;DISASTER_RECOVERY_OPTIONS&#39;],
                                    payload=current_options
                                )
        if flag:
            if response and response.json():
                if response.json().get(&#39;errorCode&#39;) != 0:
                    raise SDKException(&#39;DisasterRecovery&#39;, &#39;102&#39;, &#39;Failed to set dr properties. Error: {0}&#39;.format(
                        response.json().get(&#39;errorMessage&#39;)
                    ))
                self.refresh()
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)


    def refresh(self):
        &#34;&#34;&#34;
        refreshs the dr settings associated with commcell.

        Returns:
            None
        &#34;&#34;&#34;
        self._prepost_settings = None
        self._export_settings = None
        self._get_dr_properties()

    def set_local_dr_path(self, path):
        &#34;&#34;&#34;
        Sets local DR path

            Args:
                 path       (str)       --         local path.

            Returns:
                None
        &#34;&#34;&#34;
        if isinstance(path, str):
            self._export_settings[&#39;backupMetadataFolder&#39;] = path
            self._set_dr_properties()
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    def set_network_dr_path(self, path, username, password):
        &#34;&#34;&#34;
        Sets network DR path

            Args:
                 path       (str)       --      UNC path.

                 username   (str)       --      username with admin privileges of the remote machine.

                 password   (str)       --      password.

            Returns:
                None
        &#34;&#34;&#34;
        if isinstance(path, str) and isinstance(username, str) and isinstance(password, str):
            self._export_settings[&#39;backupMetadataFolder&#39;] = path
            self._export_settings[&#39;networkUserAccount&#39;][&#39;userName&#39;] = username
            self._export_settings[&#39;networkUserAccount&#39;][&#39;password&#39;] = b64encode(password.encode()).decode()
            self._set_dr_properties()
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    def upload_metdata_to_commvault_cloud(self, flag, username=None, password=None, region=None):
        &#34;&#34;&#34;
        Enable/Disable upload metadata to commvault cloud setting.

            Args:
                 flag       (bool)      --      True/False.

                 username   (str)       --      username of the commvault cloud.

                 password   (str)       --      password of the commvault cloud.

                 region     (str)       --      region to upload the DRBackup, None will set to default region

            Returns:
                 None
        &#34;&#34;&#34;
        if isinstance(flag, bool):
            self._export_settings[&#39;uploadBackupMetadataToCloud&#39;] = flag
            if flag:
                if isinstance(username, str) and isinstance(password, str):
                    self._export_settings[&#39;cloudCredentials&#39;][&#39;userName&#39;] = username
                    self._export_settings[&#39;cloudCredentials&#39;][&#39;password&#39;] = b64encode(password.encode()).decode()
                else:
                    raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)
            self._set_commvault_cloud_upload(flag)
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    def upload_metdata_to_cloud_library(self, flag, libraryname=None):
        &#34;&#34;&#34;
        Enable/Disable upload metadata to cloud library

            Args:
                 flag       (bool)      --      True/False.

                 libraryname   (str/object)    --      Third party cloud library name/disklibrary object.

            Returns:
                None
        &#34;&#34;&#34;
        if isinstance(flag, bool):
            self._export_settings[&#39;uploadBackupMetadataToCloudLib&#39;] = flag
            if flag:
                if isinstance(libraryname, str):
                    cloud_lib_obj = DiskLibrary(self._commcell, library_name=libraryname)
                elif isinstance(libraryname, DiskLibrary):
                    cloud_lib_obj = libraryname
                else:
                    raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)
                self._export_settings[&#39;cloudLibrary&#39;][&#39;libraryName&#39;] = cloud_lib_obj.name
                self._export_settings[&#39;cloudLibrary&#39;][&#39;libraryId&#39;] = int(cloud_lib_obj.library_id)
            self._set_dr_properties()
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    def impersonate_user(self, flag, username, password):
        &#34;&#34;&#34;
        Enable/Disable Impersonate user option for pre/post scripts.

            Args:
                flag        (bool)      --  True/False.

                username    (str)       --  username with admin privileges.

                password    (str)       --  password for the account.

            Returns:
                None
        &#34;&#34;&#34;
        if isinstance(flag, bool):
            self._prepost_settings[&#39;useImpersonateUser&#39;] = flag
            if flag:
                if isinstance(username, str) and isinstance(password, str):
                    self._prepost_settings[&#39;impersonateUser&#39;][&#39;userName&#39;] = username
                    self._prepost_settings[&#39;impersonateUser&#39;][&#39;password&#39;] = b64encode(password.encode()).decode()
                else:
                    raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)
            self._set_dr_properties()
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    @property
    def use_impersonate_user(self):
        &#34;&#34;&#34;
        gets the impersonate user(True/False)

            Returns:
                  True/False
        &#34;&#34;&#34;
        return self._prepost_settings.get(&#39;useImpersonateUser&#39;)

    @property
    def region(self):
        &#34;&#34;&#34;
        gets the current region set to upload DRBackups

            Returns:
                region (str)
        &#34;&#34;&#34;
        return self._get_drbackup_options()[&#39;properties&#39;][&#39;region&#39;]

    @property
    def number_of_metadata(self):
        &#34;&#34;&#34;
         gets the value, Number of metadata folders to be retained.

            Returns:
                number of metadata     (int)
        &#34;&#34;&#34;
        return self._export_settings.get(&#39;numberOfMetadata&#39;)

    @number_of_metadata.setter
    def number_of_metadata(self, value):
        &#34;&#34;&#34;
        Sets the value, Number of metadata folders to be retained.

            Args:
                value       (int)       --      number of metadata folders to be retained.

            Returns:
                None
        &#34;&#34;&#34;
        if isinstance(value, int):
            self._export_settings[&#39;numberOfMetadata&#39;] = value
            self._set_dr_properties()
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    @property
    def use_vss(self):
        &#34;&#34;&#34;
        gets the value, use vss()

            Returns:
                True/False
        &#34;&#34;&#34;
        return self._export_settings.get(&#39;isUseVSS&#39;)

    @use_vss.setter
    def use_vss(self, flag):
        &#34;&#34;&#34;
        sets the value, use vss

            Args:
                 flag   (bool)      --      True/Flase

            Returns:
                None
        &#34;&#34;&#34;
        if isinstance(flag, bool):
            self._export_settings[&#39;isUseVSS&#39;] = flag
            self._set_dr_properties()
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    @property
    def wild_card_settings(self):
        &#34;&#34;&#34;
        gets the wild card settings

            Returns:
                (str)       --     client logs that are to be backed up
        &#34;&#34;&#34;
        return self._export_settings.get(&#39;wildCardSetting&#39;)

    @wild_card_settings.setter
    def wild_card_settings(self, logs):
        &#34;&#34;&#34;
        sets the wild card setting

            Args:
                 logs    (list)      --      log file names

            Returns:
                  None
        &#34;&#34;&#34;
        mandatory = &#34;cvd;SIDBPrune;SIDBEngine;CVMA&#34;
        if isinstance(logs, list):
            temp = &#39;&#39;
            for log in logs:
                temp = temp + &#39;;&#39; + log
        else:
            raise Exception(&#39;Pass log names in list&#39;)
        self._export_settings[&#39;wildCardSetting&#39;] = mandatory + temp
        self._set_dr_properties()

    @property
    def backup_metadata_folder(self):
        &#34;&#34;&#34;
        gets the backup metadata folder

            Returns:
                 (str)      --      Backup metadata folder
        &#34;&#34;&#34;
        return self._export_settings.get(&#39;backupMetadataFolder&#39;)

    @property
    def upload_backup_metadata_to_cloud(self):
        &#34;&#34;&#34;
        gets the upload backup metadata to cloud setting

            Returns:
                 True/False
        &#34;&#34;&#34;
        return self._export_settings.get(&#39;uploadBackupMetadataToCloud&#39;)

    @property
    def upload_backup_metadata_to_cloud_lib(self):
        &#34;&#34;&#34;
        gets the upload metadata to cloud lib

            Returns:
                True/False
        &#34;&#34;&#34;
        return self._export_settings.get(&#39;uploadBackupMetadataToCloudLib&#39;)

    @property
    def dr_storage_policy(self):
        &#34;&#34;&#34;
        gets the storage policy name, that is being used for DR backups

            Returns:
                (str)       --      Name of the storage policy
        &#34;&#34;&#34;
        return self._export_settings.get(&#39;storagePolicy&#39;).get(&#39;storagePolicyName&#39;)

    @dr_storage_policy.setter
    def dr_storage_policy(self, storage_policy_object):
        &#34;&#34;&#34;
        sets the storage policy for DR jobs

            Args:
                storage_policy_object       (object)        --      object of the storage policy

            Returns:
                None
        &#34;&#34;&#34;
        if isinstance(storage_policy_object, StoragePolicy):        # add str
            self._export_settings[&#39;storagePolicy&#39;][&#39;storagePolicyName&#39;] = storage_policy_object.name
            self._export_settings[&#39;storagePolicy&#39;][&#39;storagePolicyId&#39;] = int(storage_policy_object.storage_policy_id)
            self._set_dr_properties()
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    @property
    def pre_scan_process(self):
        &#34;&#34;&#34;
        gets the script path of the pre scan process

            Returns:
                (str)       --      script path
        &#34;&#34;&#34;
        return self._prepost_settings.get(&#39;preScanProcess&#39;)

    @pre_scan_process.setter
    def pre_scan_process(self, path):
        &#34;&#34;&#34;
        sets the pre scan process.

            Args:
                 path   (str)      --   path of the pre scan script

            Returns:
                None
        &#34;&#34;&#34;
        if isinstance(path, str):
            self._prepost_settings[&#39;preScanProcess&#39;] = path
            self._set_dr_properties()
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    @property
    def post_scan_process(self):
        &#34;&#34;&#34;
        gets the script path of the post scan process

            Returns:
                (str)       --      script path
        &#34;&#34;&#34;
        return self._prepost_settings.get(&#39;postScanProcess&#39;)

    @post_scan_process.setter
    def post_scan_process(self, path):
        &#34;&#34;&#34;
         sets the post scan process.

            Args:
                 path   (str)      --   path of the post scan script

            Returns:
                None
        &#34;&#34;&#34;
        if isinstance(path, str):
            self._prepost_settings[&#39;postScanProcess&#39;] = path
            self._set_dr_properties()
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    @property
    def pre_backup_process(self):
        &#34;&#34;&#34;
        gets the script path of the pre backup process

            Returns:
                (str)       --      script path
        &#34;&#34;&#34;
        return self._prepost_settings.get(&#39;preBackupProcess&#39;)

    @pre_backup_process.setter
    def pre_backup_process(self, path):
        &#34;&#34;&#34;
         sets the pre backup process.

            Args:
                 path   (str)      --   path of the pre backup script

            Returns:
                None
        &#34;&#34;&#34;
        if isinstance(path, str):
            self._prepost_settings[&#39;preBackupProcess&#39;] = path
            self._set_dr_properties()
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    @property
    def post_backup_process(self):
        &#34;&#34;&#34;
        gets the script path of the post backup process

            Returns:
                (str)       --      script path
        &#34;&#34;&#34;
        return self._prepost_settings.get(&#39;postBackupProcess&#39;)

    @post_backup_process.setter
    def post_backup_process(self, path):
        &#34;&#34;&#34;
         sets the post backup process.

            Args:
                 path   (str)      --   path of the post backup script

            Returns:
                None
        &#34;&#34;&#34;
        if isinstance(path, str):
            self._prepost_settings[&#39;postBackupProcess&#39;] = path
            self._set_dr_properties()
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    @property
    def run_post_scan_process(self):
        &#34;&#34;&#34;
        gets the value, run post scan process

            Returns:
                 True/False
        &#34;&#34;&#34;
        return self._prepost_settings.get(&#39;runPostScanProcess&#39;)

    @run_post_scan_process.setter
    def run_post_scan_process(self, flag):
        &#34;&#34;&#34;
        sets the value, run post scan process

            Args:
                 flag      (bool)   --      True/False

            Returns:
                None
        &#34;&#34;&#34;
        if isinstance(flag, bool):
            self._prepost_settings[&#39;runPostScanProcess&#39;] = flag
            self._set_dr_properties()
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    @property
    def run_post_backup_process(self):
        &#34;&#34;&#34;
         gets the value, run post backup process

            Returns:
                 True/False
        &#34;&#34;&#34;
        return self._prepost_settings.get(&#39;runPostBackupProcess&#39;)

    @run_post_backup_process.setter
    def run_post_backup_process(self, flag):
        &#34;&#34;&#34;
        sets the value, run post backup process

            Args:
                 flag      (bool)   --      True/False

            Returns:
                None
        &#34;&#34;&#34;
        if isinstance(flag, bool):
            self._prepost_settings[&#39;runPostBackupProcess&#39;] = flag
            self._set_dr_properties()
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.disasterrecovery.DisasterRecovery"><code class="flex name class">
<span>class <span class="ident">DisasterRecovery</span></span>
<span>(</span><span>commcell)</span>
</code></dt>
<dd>
<div class="desc"><p>Class to perform all the disaster recovery operations on commcell</p>
<p>Initializes DisasterRecovery object</p>
<h2 id="args">Args</h2>
<p>commcell
(object)
&ndash;
instance of commcell</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/disasterrecovery.py#L110-L644" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class DisasterRecovery(object):
    &#34;&#34;&#34;Class to perform all the disaster recovery operations on commcell&#34;&#34;&#34;

    def __init__(self, commcell):
        &#34;&#34;&#34;Initializes DisasterRecovery object

            Args:
                commcell            (object)    --  instance of commcell

        &#34;&#34;&#34;
        self.commcell = commcell
        self.client = Client(self.commcell, self.commcell.commserv_name)
        self.path = self.client.install_directory
        self._RESTORE = self.commcell._services[&#39;RESTORE&#39;]
        self._CREATE_TASK = self.commcell._services[&#39;CREATE_TASK&#39;]
        self.advbackup = False
        self._disaster_recovery_management = None
        self.reset_to_defaults()

    def reset_to_defaults(self):
        &#34;&#34;&#34;
        Resets the instance variables to default values

            Returns:
                 None
        &#34;&#34;&#34;
        self._backup_type = &#34;full&#34;
        self._is_compression_enabled = True
        self._is_history_db_enabled = True
        self._is_workflow_db_enabled = True
        self._is_appstudio_db_enabled = True
        self._is_cvcloud_db_enabled = True
        self._is_dm2_db_enabled = True
        self._client_list = None
        self.advanced_job_options = None

    def disaster_recovery_backup(self):
        &#34;&#34;&#34;Runs a DR job for Commserv

            Returns:
                object - instance of the Job class for this backup job

            Raises:
                SDKException:
                    if backup level specified is not correct

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;

        if self._backup_type.lower() not in [&#39;full&#39;, &#39;differential&#39;]:
            raise SDKException(&#39;Response&#39;, &#39;103&#39;)
        backuptypes = {&#34;full&#34;: 1, &#34;differential&#34;: 3}
        if self.advbackup:
            self._backup_type = backuptypes[self._backup_type.lower()]
            return self._advanced_dr_backup()
        dr_service = self.commcell._services[&#39;DRBACKUP&#39;]
        request_json = {&#34;isCompressionEnabled&#34;: self._is_compression_enabled,
                        &#34;jobType&#34;: 1, &#34;backupType&#34;: backuptypes[self.backup_type.lower()]}
        flag, response = self.commcell._cvpysdk_object.make_request(
            &#39;POST&#39;, dr_service, request_json
        )
        return self._process_drbackup_response(flag, response)

    def _process_drbackup_response(self, flag, response):
        &#34;&#34;&#34;DR Backup response will be processed.

            Args:
                flag, response  (str)  --  results of DR backup JSON request

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if job initialization failed

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if flag:
            if response.json():
                if &#34;jobIds&#34; in response.json():
                    return Job(self.commcell, response.json()[&#39;jobIds&#39;][0])
                if &#34;errorCode&#34; in response.json():
                    o_str = &#39;Initializing backup failed\nError: &#34;{0}&#34;&#39;.format(
                        response.json()[&#39;errorMessage&#39;]
                    )
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;, o_str)
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        response_string = self.commcell._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def restore_out_of_place(
            self,
            client,
            destination_path,
            overwrite=True,
            restore_data_and_acl=True,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            fs_options=None,
            restore_jobs=[]):
        &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the input client,
            at the specified destionation location.

            Args:
                client                (str/object) --  either the name of the client or
                                                           the instance of the Client

                destination_path      (str)        --  full path of the restore location on client

                paths                 (list)       --  list of full paths of
                                                           files/folders to restore

                overwrite             (bool)       --  unconditional overwrite files during restore
                    default: True

                restore_data_and_acl  (bool)       --  restore data and ACL files
                    default: True

                copy_precedence         (int)   --  copy precedence value of storage policy copy
                    default: None

                from_time           (str)       --  time to retore the contents after
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time           (str)         --  time to retore the contents before
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                fs_options      (dict)          -- dictionary that includes all advanced options
                    options:
                        preserve_level      : preserve level option to set in restore
                        proxy_client        : proxy that needed to be used for restore
                        impersonate_user    : Impersonate user options for restore
                        impersonate_password: Impersonate password option for restore
                                                in base64 encoded form
                        all_versions        : if set to True restores all the versions of the
                                                specified file
                        versions            : list of version numbers to be backed up

                restore_jobs    (list)      --  list of jobs to be restored if the job is index free restore

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if client is not a string or Client instance

                    if destination_path is not a string

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if not ((isinstance(client, (str, Client))
                 and isinstance(destination_path, str)
                 and isinstance(overwrite, bool) and isinstance(restore_data_and_acl, bool))):
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)

        if fs_options is None:
            fs_options = {}

        if isinstance(client, Client):
            client = client
        elif isinstance(client, str):
            client = Client(self.commcell, client)
        else:
            raise SDKException(&#39;Response&#39;, &#39;105&#39;)

        agent_obj = client.agents.get(&#34;File System&#34;)
        drpath = self.path + &#34;\\CommserveDR&#34;
        destination_path = self._filter_paths([destination_path], True, agent_id=agent_obj.agent_id)
        drpath = [self._filter_paths([drpath], True, agent_id=agent_obj.agent_id)]
        if not drpath:
            raise SDKException(&#39;Response&#39;, &#39;104&#39;)
        instance_obj = agent_obj.instances.get(&#34;DefaultInstanceName&#34;)

        instance_obj._restore_association = {
            &#34;type&#34;: &#34;0&#34;,
            &#34;backupsetName&#34;: &#34;DR-BackupSet&#34;,
            &#34;instanceName&#34;: &#34;DefaultInstanceName&#34;,
            &#34;appName&#34;: &#34;CommServe Management&#34;,
            &#34;clientName&#34;: self.commcell.commserv_name,
            &#34;consumeLicense&#34;: True,
            &#34;clientSidePackage&#34;: True,
            &#34;subclientName&#34;: &#34;&#34;
        }
        return instance_obj._restore_out_of_place(
            client,
            destination_path,
            paths=drpath,
            overwrite=overwrite,
            restore_data_and_acl=restore_data_and_acl,
            copy_precedence=copy_precedence,
            from_time=from_time,
            to_time=to_time,
            fs_options=fs_options,
            restore_jobs=restore_jobs)

    def _advanced_dr_backup(self):
        &#34;&#34;&#34;Runs a DR job with JSON input

            Returns:
                object - instance of the Job class for this backup job

            Raises:
                SDKException:
                    if backup level specified is not correct

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        request_json = self._generatedrbackupjson()
        return self._process_createtask_response(request_json)

    def _generatedrbackupjson(self):
        &#34;&#34;&#34;
        Generate JSON corresponds to DR backup job
        &#34;&#34;&#34;
        try:
            self._task = {
                &#34;taskFlags&#34;: {&#34;disabled&#34;: False},
                &#34;policyType&#34;: &#34;DATA_PROTECTION&#34;,
                &#34;taskType&#34;: &#34;IMMEDIATE&#34;,
                &#34;initiatedFrom&#34;: 1
            }
            self._subtask = {
                &#34;subTaskType&#34;: &#34;ADMIN&#34;,
                &#34;operationType&#34;: &#34;DRBACKUP&#34;
            }
            clientdict = []
            if self._client_list is not None:
                for client in self._client_list:
                    client = {
                        &#34;type&#34;: 0,
                        &#34;clientName&#34;: client,
                        &#34;clientSidePackage&#34;: True,
                        &#34;consumeLicense&#34;: True}
                    clientdict.append(client)

            common_opts = None
            if self.advanced_job_options:
                common_opts = {
                    &#34;startUpOpts&#34;: {
                        &#34;priority&#34;: self.advanced_job_options.get(&#34;priority&#34;, 66),
                        &#34;startInSuspendedState&#34;: self.advanced_job_options.get(&#34;start_in_suspended_state&#34;, False),
                        &#34;startWhenActivityIsLow&#34;: self.advanced_job_options.get(&#34;start_when_activity_is_low&#34;, False),
                        &#34;useDefaultPriority&#34;: self.advanced_job_options.get(&#34;use_default_priority&#34;, True)
                    },
                    &#34;jobRetryOpts&#34;: {
                        &#34;runningTime&#34;: {
                            &#34;enableTotalRunningTime&#34;: self.advanced_job_options.get(
                                &#34;enable_total_running_time&#34;, False),
                            &#34;totalRunningTime&#34;: self.advanced_job_options.get(&#34;total_running_time&#34;, 3600)
                        },
                        &#34;enableNumberOfRetries&#34;: self.advanced_job_options.get(&#34;enable_number_of_retries&#34;, False),
                        &#34;killRunningJobWhenTotalRunningTimeExpires&#34;: self.advanced_job_options.get(
                            &#34;kill_running_job_when_total_running_time_expires&#34;, False),
                        &#34;numberOfRetries&#34;: self.advanced_job_options.get(&#34;number_of_retries&#34;, 0)
                    },
                    &#34;jobDescription&#34;: self.advanced_job_options.get(&#34;job_description&#34;, &#34;&#34;)
                }

            self._droptions = {
                &#34;drbackupType&#34;: self._backup_type, &#34;dbName&#34;: &#34;commserv&#34;,
                &#34;backupHistoryDataBase&#34;: self.is_history_db_enabled,
                &#34;backupWFEngineDataBase&#34;: self.is_workflow_db_enabled,
                &#34;backupAppStudioDataBase&#34;: self.is_appstudio_db_enabled,
                &#34;backupCVCloudDataBase&#34;: self.is_cvcloud_db_enabled,
                &#34;backupDM2DataBase&#34;: self.is_dm2_db_enabled,
                &#34;enableDatabasesBackupCompression&#34;: self.is_compression_enabled,
                &#34;client&#34;: clientdict

            }

            request_json = {
                &#34;taskInfo&#34;:
                {
                    &#34;task&#34;: self._task,
                    &#34;subTasks&#34;:
                    [{
                        &#34;subTaskOperation&#34;: 1,
                        &#34;subTask&#34;: self._subtask,
                        &#34;options&#34;:
                        {
                            &#34;adminOpts&#34;:
                            {
                                &#34;drBackupOption&#34;: self._droptions,
                                &#34;contentIndexingOption&#34;:
                                {
                                    &#34;subClientBasedAnalytics&#34;: False
                                }
                            },
                            &#34;restoreOptions&#34;:
                            {
                                &#34;virtualServerRstOption&#34;:
                                {
                                    &#34;isBlockLevelReplication&#34;: False
                                }
                            }
                        }
                    }
                    ]
                }
            }

            if self.advanced_job_options:
                request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;commonOpts&#34;] = common_opts

            return request_json
        except Exception as err:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, err)

    def _process_createtask_response(self, request_json):
        &#34;&#34;&#34;Runs the CreateTask API with the request JSON provided for DR backup,
            and returns the contents after parsing the response.

            Args:
                request_json    (dict)  --  JSON request to run for the API

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if restore job failed

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self.commcell._cvpysdk_object.make_request(
            &#39;POST&#39;, self._CREATE_TASK, request_json
        )
        if flag:
            if response.json():
                if &#34;jobIds&#34; in response.json():
                    return Job(self.commcell, response.json()[&#39;jobIds&#39;][0])
                if &#34;errorCode&#34; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]

                    o_str = &#39;DR backup job failed\nError: &#34;{0}&#34;&#39;.format(
                        error_message)
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;, o_str)
                raise SDKException(
                    &#39;Response&#39;, &#39;102&#39;, &#39;Failed to run the DR backup job&#39;)
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        response_string = self.commcell._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _filter_paths(self, paths, is_single_path=False, agent_id=None):
        &#34;&#34;&#34;Filters the paths based on the Operating System, and Agent.

            Args:
                paths           (list)  --  list containing paths to be filtered

                is_single_path  (bool)  --  boolean specifying whether to return a single path
                                                or the entire list
                agent_id        (str)   --   File system agent id
            Returns:
                list    -   if the boolean is_single_path is set to False

                str     -   if the boolean is_single_path is set to True
        &#34;&#34;&#34;
        for index, path in enumerate(paths):
            # &#34;if&#34; condition is default i.e. if client is not provided
            if agent_id is None or int(agent_id) == AppIDAType.WINDOWS_FILE_SYSTEM.value:
                path = path.strip(&#39;\\&#39;).strip(&#39;/&#39;)
                if path:
                    path = path.replace(&#39;/&#39;, &#39;\\&#39;)
                else:
                    path = &#39;\\&#39;
            elif int(agent_id) == AppIDAType.LINUX_FILE_SYSTEM.value:
                path = path.strip(&#39;\\&#39;).strip(&#39;/&#39;)
                if path:
                    path = path.replace(&#39;\\&#39;, &#39;/&#39;)
                else:
                    path = &#39;\\&#39;
                path = &#39;/&#39; + path
            paths[index] = path

        if is_single_path:
            return paths[0]
        return paths

    @property
    def client_list(self):
        &#34;&#34;&#34;Treats the client_list as a read-only attribute.&#34;&#34;&#34;
        return self._client_list

    @client_list.setter
    def client_list(self, value):
        &#34;&#34;&#34;Treats the client_list as a read-only attribute.&#34;&#34;&#34;
        if isinstance(value, list):
            self._client_list = value
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    @property
    def is_compression_enabled(self):
        &#34;&#34;&#34;Treats the iscompressionenabled as a read-only attribute.&#34;&#34;&#34;
        return self._is_compression_enabled

    @is_compression_enabled.setter
    def is_compression_enabled(self, value):
        &#34;&#34;&#34;Treats the iscompressionenabled as a read-only attribute.&#34;&#34;&#34;
        if isinstance(value, bool):
            self._is_compression_enabled = value
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    @property
    def backup_type(self):
        &#34;&#34;&#34;Treats the backup_type as a read-only attribute.&#34;&#34;&#34;
        return self._backup_type

    @backup_type.setter
    def backup_type(self, value):
        &#34;&#34;&#34;Treats the backup_type as a read-only attribute.&#34;&#34;&#34;
        if isinstance(value, str):
            self._backup_type = value
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    @property
    def is_history_db_enabled(self):
        &#34;&#34;&#34;Treats the historydb as a read-only attribute.&#34;&#34;&#34;
        return self._is_history_db_enabled

    @is_history_db_enabled.setter
    def is_history_db_enabled(self, value):
        &#34;&#34;&#34;sets the value of ishistorydb

            Args:
                value   (bool)      --  True/False
         &#34;&#34;&#34;
        if isinstance(value, bool):
            self._is_history_db_enabled = value
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    @property
    def is_workflow_db_enabled(self):
        &#34;&#34;&#34;Treats the workflowdb as a read-only attribute.&#34;&#34;&#34;
        return self._is_workflow_db_enabled

    @is_workflow_db_enabled.setter
    def is_workflow_db_enabled(self, value):
        &#34;&#34;&#34;
        sets the value of isworkflowdb

            Args:
                value   (bool)      --  True/False
         &#34;&#34;&#34;
        if isinstance(value, bool):
            self._is_workflow_db_enabled = value
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    @property
    def is_appstudio_db_enabled(self):
        &#34;&#34;&#34;Treats the workflowdb as a read-only attribute.&#34;&#34;&#34;
        return self._is_appstudio_db_enabled

    @is_appstudio_db_enabled.setter
    def is_appstudio_db_enabled(self, value):
        &#34;&#34;&#34;
        sets the value of isappstudiodb

            Args:
                value   (bool)      --  True/False
         &#34;&#34;&#34;
        if isinstance(value, bool):
            self._is_appstudio_db_enabled = value
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    @property
    def is_cvcloud_db_enabled(self):
        &#34;&#34;&#34;Treats the cvclouddb as a read-only attribute.&#34;&#34;&#34;
        return self._is_cvcloud_db_enabled

    @is_cvcloud_db_enabled.setter
    def is_cvcloud_db_enabled(self, value):
        &#34;&#34;&#34;
        sets the value of iscvclouddb

            Args:
                value   (bool)      --  True/False
         &#34;&#34;&#34;
        if isinstance(value, bool):
            self._is_cvcloud_db_enabled = value
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    @property
    def is_dm2_db_enabled(self):
        &#34;&#34;&#34;Treats the dm2db as a read-only attribute.&#34;&#34;&#34;
        return self._is_dm2_db_enabled

    @is_dm2_db_enabled.setter
    def is_dm2_db_enabled(self, value):
        &#34;&#34;&#34;
        sets the value of isdm2db

            Args:
                value   (bool)      --  True/False
         &#34;&#34;&#34;
        if isinstance(value, bool):
            self._is_dm2_db_enabled = value
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    @property
    def disaster_recovery_management(self):
        &#34;&#34;&#34;
        Returns the instance of the DisasterRecoveryManagement class
        &#34;&#34;&#34;
        if self._disaster_recovery_management is None:
            self._disaster_recovery_management = DisasterRecoveryManagement(self.commcell)
        return self._disaster_recovery_management</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.disasterrecovery.DisasterRecovery.backup_type"><code class="name">var <span class="ident">backup_type</span></code></dt>
<dd>
<div class="desc"><p>Treats the backup_type as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/disasterrecovery.py#L535-L538" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def backup_type(self):
    &#34;&#34;&#34;Treats the backup_type as a read-only attribute.&#34;&#34;&#34;
    return self._backup_type</code></pre>
</details>
</dd>
<dt id="cvpysdk.disasterrecovery.DisasterRecovery.client_list"><code class="name">var <span class="ident">client_list</span></code></dt>
<dd>
<div class="desc"><p>Treats the client_list as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/disasterrecovery.py#L509-L512" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def client_list(self):
    &#34;&#34;&#34;Treats the client_list as a read-only attribute.&#34;&#34;&#34;
    return self._client_list</code></pre>
</details>
</dd>
<dt id="cvpysdk.disasterrecovery.DisasterRecovery.disaster_recovery_management"><code class="name">var <span class="ident">disaster_recovery_management</span></code></dt>
<dd>
<div class="desc"><p>Returns the instance of the DisasterRecoveryManagement class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/disasterrecovery.py#L637-L644" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def disaster_recovery_management(self):
    &#34;&#34;&#34;
    Returns the instance of the DisasterRecoveryManagement class
    &#34;&#34;&#34;
    if self._disaster_recovery_management is None:
        self._disaster_recovery_management = DisasterRecoveryManagement(self.commcell)
    return self._disaster_recovery_management</code></pre>
</details>
</dd>
<dt id="cvpysdk.disasterrecovery.DisasterRecovery.is_appstudio_db_enabled"><code class="name">var <span class="ident">is_appstudio_db_enabled</span></code></dt>
<dd>
<div class="desc"><p>Treats the workflowdb as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/disasterrecovery.py#L583-L586" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_appstudio_db_enabled(self):
    &#34;&#34;&#34;Treats the workflowdb as a read-only attribute.&#34;&#34;&#34;
    return self._is_appstudio_db_enabled</code></pre>
</details>
</dd>
<dt id="cvpysdk.disasterrecovery.DisasterRecovery.is_compression_enabled"><code class="name">var <span class="ident">is_compression_enabled</span></code></dt>
<dd>
<div class="desc"><p>Treats the iscompressionenabled as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/disasterrecovery.py#L522-L525" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_compression_enabled(self):
    &#34;&#34;&#34;Treats the iscompressionenabled as a read-only attribute.&#34;&#34;&#34;
    return self._is_compression_enabled</code></pre>
</details>
</dd>
<dt id="cvpysdk.disasterrecovery.DisasterRecovery.is_cvcloud_db_enabled"><code class="name">var <span class="ident">is_cvcloud_db_enabled</span></code></dt>
<dd>
<div class="desc"><p>Treats the cvclouddb as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/disasterrecovery.py#L601-L604" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_cvcloud_db_enabled(self):
    &#34;&#34;&#34;Treats the cvclouddb as a read-only attribute.&#34;&#34;&#34;
    return self._is_cvcloud_db_enabled</code></pre>
</details>
</dd>
<dt id="cvpysdk.disasterrecovery.DisasterRecovery.is_dm2_db_enabled"><code class="name">var <span class="ident">is_dm2_db_enabled</span></code></dt>
<dd>
<div class="desc"><p>Treats the dm2db as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/disasterrecovery.py#L619-L622" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_dm2_db_enabled(self):
    &#34;&#34;&#34;Treats the dm2db as a read-only attribute.&#34;&#34;&#34;
    return self._is_dm2_db_enabled</code></pre>
</details>
</dd>
<dt id="cvpysdk.disasterrecovery.DisasterRecovery.is_history_db_enabled"><code class="name">var <span class="ident">is_history_db_enabled</span></code></dt>
<dd>
<div class="desc"><p>Treats the historydb as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/disasterrecovery.py#L548-L551" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_history_db_enabled(self):
    &#34;&#34;&#34;Treats the historydb as a read-only attribute.&#34;&#34;&#34;
    return self._is_history_db_enabled</code></pre>
</details>
</dd>
<dt id="cvpysdk.disasterrecovery.DisasterRecovery.is_workflow_db_enabled"><code class="name">var <span class="ident">is_workflow_db_enabled</span></code></dt>
<dd>
<div class="desc"><p>Treats the workflowdb as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/disasterrecovery.py#L565-L568" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_workflow_db_enabled(self):
    &#34;&#34;&#34;Treats the workflowdb as a read-only attribute.&#34;&#34;&#34;
    return self._is_workflow_db_enabled</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.disasterrecovery.DisasterRecovery.disaster_recovery_backup"><code class="name flex">
<span>def <span class="ident">disaster_recovery_backup</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Runs a DR job for Commserv</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this backup job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if backup level specified is not correct</p>
<pre><code>if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/disasterrecovery.py#L146-L173" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def disaster_recovery_backup(self):
    &#34;&#34;&#34;Runs a DR job for Commserv

        Returns:
            object - instance of the Job class for this backup job

        Raises:
            SDKException:
                if backup level specified is not correct

                if response is empty

                if response is not success
    &#34;&#34;&#34;

    if self._backup_type.lower() not in [&#39;full&#39;, &#39;differential&#39;]:
        raise SDKException(&#39;Response&#39;, &#39;103&#39;)
    backuptypes = {&#34;full&#34;: 1, &#34;differential&#34;: 3}
    if self.advbackup:
        self._backup_type = backuptypes[self._backup_type.lower()]
        return self._advanced_dr_backup()
    dr_service = self.commcell._services[&#39;DRBACKUP&#39;]
    request_json = {&#34;isCompressionEnabled&#34;: self._is_compression_enabled,
                    &#34;jobType&#34;: 1, &#34;backupType&#34;: backuptypes[self.backup_type.lower()]}
    flag, response = self.commcell._cvpysdk_object.make_request(
        &#39;POST&#39;, dr_service, request_json
    )
    return self._process_drbackup_response(flag, response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.disasterrecovery.DisasterRecovery.reset_to_defaults"><code class="name flex">
<span>def <span class="ident">reset_to_defaults</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Resets the instance variables to default values</p>
<pre><code>Returns:
     None
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/disasterrecovery.py#L129-L144" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def reset_to_defaults(self):
    &#34;&#34;&#34;
    Resets the instance variables to default values

        Returns:
             None
    &#34;&#34;&#34;
    self._backup_type = &#34;full&#34;
    self._is_compression_enabled = True
    self._is_history_db_enabled = True
    self._is_workflow_db_enabled = True
    self._is_appstudio_db_enabled = True
    self._is_cvcloud_db_enabled = True
    self._is_dm2_db_enabled = True
    self._client_list = None
    self.advanced_job_options = None</code></pre>
</details>
</dd>
<dt id="cvpysdk.disasterrecovery.DisasterRecovery.restore_out_of_place"><code class="name flex">
<span>def <span class="ident">restore_out_of_place</span></span>(<span>self, client, destination_path, overwrite=True, restore_data_and_acl=True, copy_precedence=None, from_time=None, to_time=None, fs_options=None, restore_jobs=[])</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the files/folders specified in the input paths list to the input client,
at the specified destionation location.</p>
<h2 id="args">Args</h2>
<p>client
(str/object) &ndash;
either the name of the client or
the instance of the Client</p>
<p>destination_path
(str)
&ndash;
full path of the restore location on client</p>
<p>paths
(list)
&ndash;
list of full paths of
files/folders to restore</p>
<p>overwrite
(bool)
&ndash;
unconditional overwrite files during restore
default: True</p>
<p>restore_data_and_acl
(bool)
&ndash;
restore data and ACL files
default: True</p>
<p>copy_precedence
(int)
&ndash;
copy precedence value of storage policy copy
default: None</p>
<p>from_time
(str)
&ndash;
time to retore the contents after
format: YYYY-MM-DD HH:MM:SS</p>
<pre><code>default: None
</code></pre>
<p>to_time
(str)
&ndash;
time to retore the contents before
format: YYYY-MM-DD HH:MM:SS</p>
<pre><code>default: None
</code></pre>
<p>fs_options
(dict)
&ndash; dictionary that includes all advanced options
options:
preserve_level
: preserve level option to set in restore
proxy_client
: proxy that needed to be used for restore
impersonate_user
: Impersonate user options for restore
impersonate_password: Impersonate password option for restore
in base64 encoded form
all_versions
: if set to True restores all the versions of the
specified file
versions
: list of version numbers to be backed up</p>
<p>restore_jobs
(list)
&ndash;
list of jobs to be restored if the job is index free restore</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if client is not a string or Client instance</p>
<pre><code>if destination_path is not a string

if paths is not a list

if failed to initialize job

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/disasterrecovery.py#L205-L320" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_out_of_place(
        self,
        client,
        destination_path,
        overwrite=True,
        restore_data_and_acl=True,
        copy_precedence=None,
        from_time=None,
        to_time=None,
        fs_options=None,
        restore_jobs=[]):
    &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the input client,
        at the specified destionation location.

        Args:
            client                (str/object) --  either the name of the client or
                                                       the instance of the Client

            destination_path      (str)        --  full path of the restore location on client

            paths                 (list)       --  list of full paths of
                                                       files/folders to restore

            overwrite             (bool)       --  unconditional overwrite files during restore
                default: True

            restore_data_and_acl  (bool)       --  restore data and ACL files
                default: True

            copy_precedence         (int)   --  copy precedence value of storage policy copy
                default: None

            from_time           (str)       --  time to retore the contents after
                    format: YYYY-MM-DD HH:MM:SS

                default: None

            to_time           (str)         --  time to retore the contents before
                    format: YYYY-MM-DD HH:MM:SS

                default: None

            fs_options      (dict)          -- dictionary that includes all advanced options
                options:
                    preserve_level      : preserve level option to set in restore
                    proxy_client        : proxy that needed to be used for restore
                    impersonate_user    : Impersonate user options for restore
                    impersonate_password: Impersonate password option for restore
                                            in base64 encoded form
                    all_versions        : if set to True restores all the versions of the
                                            specified file
                    versions            : list of version numbers to be backed up

            restore_jobs    (list)      --  list of jobs to be restored if the job is index free restore

        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:
                if client is not a string or Client instance

                if destination_path is not a string

                if paths is not a list

                if failed to initialize job

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    if not ((isinstance(client, (str, Client))
             and isinstance(destination_path, str)
             and isinstance(overwrite, bool) and isinstance(restore_data_and_acl, bool))):
        raise SDKException(&#39;Response&#39;, &#39;101&#39;)

    if fs_options is None:
        fs_options = {}

    if isinstance(client, Client):
        client = client
    elif isinstance(client, str):
        client = Client(self.commcell, client)
    else:
        raise SDKException(&#39;Response&#39;, &#39;105&#39;)

    agent_obj = client.agents.get(&#34;File System&#34;)
    drpath = self.path + &#34;\\CommserveDR&#34;
    destination_path = self._filter_paths([destination_path], True, agent_id=agent_obj.agent_id)
    drpath = [self._filter_paths([drpath], True, agent_id=agent_obj.agent_id)]
    if not drpath:
        raise SDKException(&#39;Response&#39;, &#39;104&#39;)
    instance_obj = agent_obj.instances.get(&#34;DefaultInstanceName&#34;)

    instance_obj._restore_association = {
        &#34;type&#34;: &#34;0&#34;,
        &#34;backupsetName&#34;: &#34;DR-BackupSet&#34;,
        &#34;instanceName&#34;: &#34;DefaultInstanceName&#34;,
        &#34;appName&#34;: &#34;CommServe Management&#34;,
        &#34;clientName&#34;: self.commcell.commserv_name,
        &#34;consumeLicense&#34;: True,
        &#34;clientSidePackage&#34;: True,
        &#34;subclientName&#34;: &#34;&#34;
    }
    return instance_obj._restore_out_of_place(
        client,
        destination_path,
        paths=drpath,
        overwrite=overwrite,
        restore_data_and_acl=restore_data_and_acl,
        copy_precedence=copy_precedence,
        from_time=from_time,
        to_time=to_time,
        fs_options=fs_options,
        restore_jobs=restore_jobs)</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.disasterrecovery.DisasterRecoveryManagement"><code class="flex name class">
<span>class <span class="ident">DisasterRecoveryManagement</span></span>
<span>(</span><span>commcell)</span>
</code></dt>
<dd>
<div class="desc"><p>Class to perform all the disaster recovery management operations on commcell</p>
<p>Initializes DisasterRecoveryManagement object</p>
<p>Args:
commcell
(object)
&ndash;
instance of commcell</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/disasterrecovery.py#L647-L1256" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class DisasterRecoveryManagement(object):
    &#34;&#34;&#34;Class to perform all the disaster recovery management operations on commcell&#34;&#34;&#34;

    def __init__(self, commcell):
        &#34;&#34;&#34;Initializes DisasterRecoveryManagement object

            Args:
            commcell    (object)    --  instance of commcell

        &#34;&#34;&#34;
        self._commcell = commcell
        self.services = self._commcell._services
        self._service = self.services[&#39;DISASTER_RECOVERY_PROPERTIES&#39;]
        self._cvpysdk_object = self._commcell._cvpysdk_object
        self.refresh()

    def _get_dr_properties(self):
        &#34;&#34;&#34;
        Executes a request on the server to get the settings of disaster recovery Backup.

            Returns:
                None

            Raises:
                SDKException
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(method=&#39;GET&#39;, url=self._service)
        if flag:
            if response and response.json():
                self._settings_dict = response.json()
                if self._settings_dict.get(&#39;errorCode&#39;, 0) != 0:
                    raise SDKException(&#39;Job&#39;, &#39;102&#39;, &#39;Failed to get dr management properties. \nError: {0}&#39;.format(
                        self._settings_dict.get(&#39;errorMessage&#39;, &#39;&#39;)))
                if &#39;drBackupInfo&#39; in self._settings_dict:
                    self._prepost_settings = self._settings_dict.get(&#39;drBackupInfo&#39;).get(&#39;prePostProcessSettings&#39;, {})
                    self._export_settings = self._settings_dict.get(&#39;drBackupInfo&#39;).get(&#39;exportSettings&#39;, {})
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _set_dr_properties(self):
        &#34;&#34;&#34;
        Executes a request on the server, to set the dr settings.

         Returns:
               None
         Raises:
              SDKException:
                    if given inputs are invalid.

        &#34;&#34;&#34;

        flag, response = self._cvpysdk_object.make_request(method=&#39;POST&#39;, url=self._service,
                                                           payload=self._settings_dict)
        if flag:
            if response and response.json():
                if response.json().get(&#39;response&#39;) and response.json().get(&#39;response&#39;)[0].get(&#39;errorCode&#39;) != 0:
                    raise SDKException(&#39;DisasterRecovery&#39;, &#39;102&#39;, &#39;Failed to set dr properties. Error: {0}&#39;.format(
                        response.json().get(&#39;response&#39;)[0].get(&#39;errorString&#39;)
                    ))
                self.refresh()
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)

    def _get_drbackup_options(self):
        &#34;&#34;&#34;
        Returns : dict of dr backup options
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            method=&#39;GET&#39;,
            url=self.services[&#39;DISASTER_RECOVERY_OPTIONS&#39;]
        )
        if flag:
            if response and response.json():
                return response.json()
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)

    def get_cloud_regions(self):
        &#34;&#34;&#34;
        Returns : dict of available dr backup regions.
                    Ex:
                    {
                        &#34;defaultRegion&#34;: &#34;southindia&#34;,
                        &#34;regions&#34;: [
                            {
                                &#34;regionCode&#34;: &#34;eastus2&#34;,
                                &#34;displayName&#34;: &#34;East US 2&#34;
                            }
                            {
                                &#34;regionCode&#34;: &#34;southindia&#34;,
                                &#34;displayName&#34;: &#34;(Asia Pacific) South India&#34;
                            }
                        ]
                    }
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            method=&#39;GET&#39;,
            url=self.services[&#39;DRBACKUP_REGIONS&#39;]
        )
        if flag:
            if response and response.json():
                return response.json()
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)

    def _set_commvault_cloud_upload(self, flag, region=None):
        &#34;&#34;&#34;
        Executes a request on the server, to set the dr settings for commvault cloud upload.
         Args:
             flag   (bool)   : True to enable commvault cloud upload, False to disable
             region (str)    : To select the region for the DR backup to be uploaded to.
                                None will leave the region to be in default region.
         Returns:
               None
         Raises:
              SDKException:
                    if given inputs are invalid.

        &#34;&#34;&#34;
        current_options = self._get_drbackup_options()
        current_options[&#39;properties&#39;][&#39;uploadBackupMetadataToCloud&#39;] = flag
        if flag:
            current_options[&#39;properties&#39;][&#39;region&#39;] = region if region else self.get_cloud_regions()[&#39;defaultRegion&#39;]

        flag, response = self._cvpysdk_object.make_request(
                                    method=&#39;POST&#39;,
                                    url=self.services[&#39;DISASTER_RECOVERY_OPTIONS&#39;],
                                    payload=current_options
                                )
        if flag:
            if response and response.json():
                if response.json().get(&#39;errorCode&#39;) != 0:
                    raise SDKException(&#39;DisasterRecovery&#39;, &#39;102&#39;, &#39;Failed to set dr properties. Error: {0}&#39;.format(
                        response.json().get(&#39;errorMessage&#39;)
                    ))
                self.refresh()
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)


    def refresh(self):
        &#34;&#34;&#34;
        refreshs the dr settings associated with commcell.

        Returns:
            None
        &#34;&#34;&#34;
        self._prepost_settings = None
        self._export_settings = None
        self._get_dr_properties()

    def set_local_dr_path(self, path):
        &#34;&#34;&#34;
        Sets local DR path

            Args:
                 path       (str)       --         local path.

            Returns:
                None
        &#34;&#34;&#34;
        if isinstance(path, str):
            self._export_settings[&#39;backupMetadataFolder&#39;] = path
            self._set_dr_properties()
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    def set_network_dr_path(self, path, username, password):
        &#34;&#34;&#34;
        Sets network DR path

            Args:
                 path       (str)       --      UNC path.

                 username   (str)       --      username with admin privileges of the remote machine.

                 password   (str)       --      password.

            Returns:
                None
        &#34;&#34;&#34;
        if isinstance(path, str) and isinstance(username, str) and isinstance(password, str):
            self._export_settings[&#39;backupMetadataFolder&#39;] = path
            self._export_settings[&#39;networkUserAccount&#39;][&#39;userName&#39;] = username
            self._export_settings[&#39;networkUserAccount&#39;][&#39;password&#39;] = b64encode(password.encode()).decode()
            self._set_dr_properties()
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    def upload_metdata_to_commvault_cloud(self, flag, username=None, password=None, region=None):
        &#34;&#34;&#34;
        Enable/Disable upload metadata to commvault cloud setting.

            Args:
                 flag       (bool)      --      True/False.

                 username   (str)       --      username of the commvault cloud.

                 password   (str)       --      password of the commvault cloud.

                 region     (str)       --      region to upload the DRBackup, None will set to default region

            Returns:
                 None
        &#34;&#34;&#34;
        if isinstance(flag, bool):
            self._export_settings[&#39;uploadBackupMetadataToCloud&#39;] = flag
            if flag:
                if isinstance(username, str) and isinstance(password, str):
                    self._export_settings[&#39;cloudCredentials&#39;][&#39;userName&#39;] = username
                    self._export_settings[&#39;cloudCredentials&#39;][&#39;password&#39;] = b64encode(password.encode()).decode()
                else:
                    raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)
            self._set_commvault_cloud_upload(flag)
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    def upload_metdata_to_cloud_library(self, flag, libraryname=None):
        &#34;&#34;&#34;
        Enable/Disable upload metadata to cloud library

            Args:
                 flag       (bool)      --      True/False.

                 libraryname   (str/object)    --      Third party cloud library name/disklibrary object.

            Returns:
                None
        &#34;&#34;&#34;
        if isinstance(flag, bool):
            self._export_settings[&#39;uploadBackupMetadataToCloudLib&#39;] = flag
            if flag:
                if isinstance(libraryname, str):
                    cloud_lib_obj = DiskLibrary(self._commcell, library_name=libraryname)
                elif isinstance(libraryname, DiskLibrary):
                    cloud_lib_obj = libraryname
                else:
                    raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)
                self._export_settings[&#39;cloudLibrary&#39;][&#39;libraryName&#39;] = cloud_lib_obj.name
                self._export_settings[&#39;cloudLibrary&#39;][&#39;libraryId&#39;] = int(cloud_lib_obj.library_id)
            self._set_dr_properties()
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    def impersonate_user(self, flag, username, password):
        &#34;&#34;&#34;
        Enable/Disable Impersonate user option for pre/post scripts.

            Args:
                flag        (bool)      --  True/False.

                username    (str)       --  username with admin privileges.

                password    (str)       --  password for the account.

            Returns:
                None
        &#34;&#34;&#34;
        if isinstance(flag, bool):
            self._prepost_settings[&#39;useImpersonateUser&#39;] = flag
            if flag:
                if isinstance(username, str) and isinstance(password, str):
                    self._prepost_settings[&#39;impersonateUser&#39;][&#39;userName&#39;] = username
                    self._prepost_settings[&#39;impersonateUser&#39;][&#39;password&#39;] = b64encode(password.encode()).decode()
                else:
                    raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)
            self._set_dr_properties()
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    @property
    def use_impersonate_user(self):
        &#34;&#34;&#34;
        gets the impersonate user(True/False)

            Returns:
                  True/False
        &#34;&#34;&#34;
        return self._prepost_settings.get(&#39;useImpersonateUser&#39;)

    @property
    def region(self):
        &#34;&#34;&#34;
        gets the current region set to upload DRBackups

            Returns:
                region (str)
        &#34;&#34;&#34;
        return self._get_drbackup_options()[&#39;properties&#39;][&#39;region&#39;]

    @property
    def number_of_metadata(self):
        &#34;&#34;&#34;
         gets the value, Number of metadata folders to be retained.

            Returns:
                number of metadata     (int)
        &#34;&#34;&#34;
        return self._export_settings.get(&#39;numberOfMetadata&#39;)

    @number_of_metadata.setter
    def number_of_metadata(self, value):
        &#34;&#34;&#34;
        Sets the value, Number of metadata folders to be retained.

            Args:
                value       (int)       --      number of metadata folders to be retained.

            Returns:
                None
        &#34;&#34;&#34;
        if isinstance(value, int):
            self._export_settings[&#39;numberOfMetadata&#39;] = value
            self._set_dr_properties()
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    @property
    def use_vss(self):
        &#34;&#34;&#34;
        gets the value, use vss()

            Returns:
                True/False
        &#34;&#34;&#34;
        return self._export_settings.get(&#39;isUseVSS&#39;)

    @use_vss.setter
    def use_vss(self, flag):
        &#34;&#34;&#34;
        sets the value, use vss

            Args:
                 flag   (bool)      --      True/Flase

            Returns:
                None
        &#34;&#34;&#34;
        if isinstance(flag, bool):
            self._export_settings[&#39;isUseVSS&#39;] = flag
            self._set_dr_properties()
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    @property
    def wild_card_settings(self):
        &#34;&#34;&#34;
        gets the wild card settings

            Returns:
                (str)       --     client logs that are to be backed up
        &#34;&#34;&#34;
        return self._export_settings.get(&#39;wildCardSetting&#39;)

    @wild_card_settings.setter
    def wild_card_settings(self, logs):
        &#34;&#34;&#34;
        sets the wild card setting

            Args:
                 logs    (list)      --      log file names

            Returns:
                  None
        &#34;&#34;&#34;
        mandatory = &#34;cvd;SIDBPrune;SIDBEngine;CVMA&#34;
        if isinstance(logs, list):
            temp = &#39;&#39;
            for log in logs:
                temp = temp + &#39;;&#39; + log
        else:
            raise Exception(&#39;Pass log names in list&#39;)
        self._export_settings[&#39;wildCardSetting&#39;] = mandatory + temp
        self._set_dr_properties()

    @property
    def backup_metadata_folder(self):
        &#34;&#34;&#34;
        gets the backup metadata folder

            Returns:
                 (str)      --      Backup metadata folder
        &#34;&#34;&#34;
        return self._export_settings.get(&#39;backupMetadataFolder&#39;)

    @property
    def upload_backup_metadata_to_cloud(self):
        &#34;&#34;&#34;
        gets the upload backup metadata to cloud setting

            Returns:
                 True/False
        &#34;&#34;&#34;
        return self._export_settings.get(&#39;uploadBackupMetadataToCloud&#39;)

    @property
    def upload_backup_metadata_to_cloud_lib(self):
        &#34;&#34;&#34;
        gets the upload metadata to cloud lib

            Returns:
                True/False
        &#34;&#34;&#34;
        return self._export_settings.get(&#39;uploadBackupMetadataToCloudLib&#39;)

    @property
    def dr_storage_policy(self):
        &#34;&#34;&#34;
        gets the storage policy name, that is being used for DR backups

            Returns:
                (str)       --      Name of the storage policy
        &#34;&#34;&#34;
        return self._export_settings.get(&#39;storagePolicy&#39;).get(&#39;storagePolicyName&#39;)

    @dr_storage_policy.setter
    def dr_storage_policy(self, storage_policy_object):
        &#34;&#34;&#34;
        sets the storage policy for DR jobs

            Args:
                storage_policy_object       (object)        --      object of the storage policy

            Returns:
                None
        &#34;&#34;&#34;
        if isinstance(storage_policy_object, StoragePolicy):        # add str
            self._export_settings[&#39;storagePolicy&#39;][&#39;storagePolicyName&#39;] = storage_policy_object.name
            self._export_settings[&#39;storagePolicy&#39;][&#39;storagePolicyId&#39;] = int(storage_policy_object.storage_policy_id)
            self._set_dr_properties()
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    @property
    def pre_scan_process(self):
        &#34;&#34;&#34;
        gets the script path of the pre scan process

            Returns:
                (str)       --      script path
        &#34;&#34;&#34;
        return self._prepost_settings.get(&#39;preScanProcess&#39;)

    @pre_scan_process.setter
    def pre_scan_process(self, path):
        &#34;&#34;&#34;
        sets the pre scan process.

            Args:
                 path   (str)      --   path of the pre scan script

            Returns:
                None
        &#34;&#34;&#34;
        if isinstance(path, str):
            self._prepost_settings[&#39;preScanProcess&#39;] = path
            self._set_dr_properties()
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    @property
    def post_scan_process(self):
        &#34;&#34;&#34;
        gets the script path of the post scan process

            Returns:
                (str)       --      script path
        &#34;&#34;&#34;
        return self._prepost_settings.get(&#39;postScanProcess&#39;)

    @post_scan_process.setter
    def post_scan_process(self, path):
        &#34;&#34;&#34;
         sets the post scan process.

            Args:
                 path   (str)      --   path of the post scan script

            Returns:
                None
        &#34;&#34;&#34;
        if isinstance(path, str):
            self._prepost_settings[&#39;postScanProcess&#39;] = path
            self._set_dr_properties()
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    @property
    def pre_backup_process(self):
        &#34;&#34;&#34;
        gets the script path of the pre backup process

            Returns:
                (str)       --      script path
        &#34;&#34;&#34;
        return self._prepost_settings.get(&#39;preBackupProcess&#39;)

    @pre_backup_process.setter
    def pre_backup_process(self, path):
        &#34;&#34;&#34;
         sets the pre backup process.

            Args:
                 path   (str)      --   path of the pre backup script

            Returns:
                None
        &#34;&#34;&#34;
        if isinstance(path, str):
            self._prepost_settings[&#39;preBackupProcess&#39;] = path
            self._set_dr_properties()
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    @property
    def post_backup_process(self):
        &#34;&#34;&#34;
        gets the script path of the post backup process

            Returns:
                (str)       --      script path
        &#34;&#34;&#34;
        return self._prepost_settings.get(&#39;postBackupProcess&#39;)

    @post_backup_process.setter
    def post_backup_process(self, path):
        &#34;&#34;&#34;
         sets the post backup process.

            Args:
                 path   (str)      --   path of the post backup script

            Returns:
                None
        &#34;&#34;&#34;
        if isinstance(path, str):
            self._prepost_settings[&#39;postBackupProcess&#39;] = path
            self._set_dr_properties()
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    @property
    def run_post_scan_process(self):
        &#34;&#34;&#34;
        gets the value, run post scan process

            Returns:
                 True/False
        &#34;&#34;&#34;
        return self._prepost_settings.get(&#39;runPostScanProcess&#39;)

    @run_post_scan_process.setter
    def run_post_scan_process(self, flag):
        &#34;&#34;&#34;
        sets the value, run post scan process

            Args:
                 flag      (bool)   --      True/False

            Returns:
                None
        &#34;&#34;&#34;
        if isinstance(flag, bool):
            self._prepost_settings[&#39;runPostScanProcess&#39;] = flag
            self._set_dr_properties()
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)

    @property
    def run_post_backup_process(self):
        &#34;&#34;&#34;
         gets the value, run post backup process

            Returns:
                 True/False
        &#34;&#34;&#34;
        return self._prepost_settings.get(&#39;runPostBackupProcess&#39;)

    @run_post_backup_process.setter
    def run_post_backup_process(self, flag):
        &#34;&#34;&#34;
        sets the value, run post backup process

            Args:
                 flag      (bool)   --      True/False

            Returns:
                None
        &#34;&#34;&#34;
        if isinstance(flag, bool):
            self._prepost_settings[&#39;runPostBackupProcess&#39;] = flag
            self._set_dr_properties()
        else:
            raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.disasterrecovery.DisasterRecoveryManagement.backup_metadata_folder"><code class="name">var <span class="ident">backup_metadata_folder</span></code></dt>
<dd>
<div class="desc"><p>gets the backup metadata folder</p>
<pre><code>Returns:
     (str)      --      Backup metadata folder
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/disasterrecovery.py#L1038-L1046" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def backup_metadata_folder(self):
    &#34;&#34;&#34;
    gets the backup metadata folder

        Returns:
             (str)      --      Backup metadata folder
    &#34;&#34;&#34;
    return self._export_settings.get(&#39;backupMetadataFolder&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.disasterrecovery.DisasterRecoveryManagement.dr_storage_policy"><code class="name">var <span class="ident">dr_storage_policy</span></code></dt>
<dd>
<div class="desc"><p>gets the storage policy name, that is being used for DR backups</p>
<pre><code>Returns:
    (str)       --      Name of the storage policy
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/disasterrecovery.py#L1068-L1076" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def dr_storage_policy(self):
    &#34;&#34;&#34;
    gets the storage policy name, that is being used for DR backups

        Returns:
            (str)       --      Name of the storage policy
    &#34;&#34;&#34;
    return self._export_settings.get(&#39;storagePolicy&#39;).get(&#39;storagePolicyName&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.disasterrecovery.DisasterRecoveryManagement.number_of_metadata"><code class="name">var <span class="ident">number_of_metadata</span></code></dt>
<dd>
<div class="desc"><p>gets the value, Number of metadata folders to be retained.</p>
<p>Returns:
number of metadata
(int)</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/disasterrecovery.py#L953-L961" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def number_of_metadata(self):
    &#34;&#34;&#34;
     gets the value, Number of metadata folders to be retained.

        Returns:
            number of metadata     (int)
    &#34;&#34;&#34;
    return self._export_settings.get(&#39;numberOfMetadata&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.disasterrecovery.DisasterRecoveryManagement.post_backup_process"><code class="name">var <span class="ident">post_backup_process</span></code></dt>
<dd>
<div class="desc"><p>gets the script path of the post backup process</p>
<pre><code>Returns:
    (str)       --      script path
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/disasterrecovery.py#L1177-L1185" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def post_backup_process(self):
    &#34;&#34;&#34;
    gets the script path of the post backup process

        Returns:
            (str)       --      script path
    &#34;&#34;&#34;
    return self._prepost_settings.get(&#39;postBackupProcess&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.disasterrecovery.DisasterRecoveryManagement.post_scan_process"><code class="name">var <span class="ident">post_scan_process</span></code></dt>
<dd>
<div class="desc"><p>gets the script path of the post scan process</p>
<pre><code>Returns:
    (str)       --      script path
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/disasterrecovery.py#L1123-L1131" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def post_scan_process(self):
    &#34;&#34;&#34;
    gets the script path of the post scan process

        Returns:
            (str)       --      script path
    &#34;&#34;&#34;
    return self._prepost_settings.get(&#39;postScanProcess&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.disasterrecovery.DisasterRecoveryManagement.pre_backup_process"><code class="name">var <span class="ident">pre_backup_process</span></code></dt>
<dd>
<div class="desc"><p>gets the script path of the pre backup process</p>
<pre><code>Returns:
    (str)       --      script path
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/disasterrecovery.py#L1150-L1158" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def pre_backup_process(self):
    &#34;&#34;&#34;
    gets the script path of the pre backup process

        Returns:
            (str)       --      script path
    &#34;&#34;&#34;
    return self._prepost_settings.get(&#39;preBackupProcess&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.disasterrecovery.DisasterRecoveryManagement.pre_scan_process"><code class="name">var <span class="ident">pre_scan_process</span></code></dt>
<dd>
<div class="desc"><p>gets the script path of the pre scan process</p>
<pre><code>Returns:
    (str)       --      script path
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/disasterrecovery.py#L1096-L1104" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def pre_scan_process(self):
    &#34;&#34;&#34;
    gets the script path of the pre scan process

        Returns:
            (str)       --      script path
    &#34;&#34;&#34;
    return self._prepost_settings.get(&#39;preScanProcess&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.disasterrecovery.DisasterRecoveryManagement.region"><code class="name">var <span class="ident">region</span></code></dt>
<dd>
<div class="desc"><p>gets the current region set to upload DRBackups</p>
<pre><code>Returns:
    region (str)
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/disasterrecovery.py#L943-L951" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def region(self):
    &#34;&#34;&#34;
    gets the current region set to upload DRBackups

        Returns:
            region (str)
    &#34;&#34;&#34;
    return self._get_drbackup_options()[&#39;properties&#39;][&#39;region&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.disasterrecovery.DisasterRecoveryManagement.run_post_backup_process"><code class="name">var <span class="ident">run_post_backup_process</span></code></dt>
<dd>
<div class="desc"><p>gets the value, run post backup process</p>
<p>Returns:
True/False</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/disasterrecovery.py#L1231-L1239" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def run_post_backup_process(self):
    &#34;&#34;&#34;
     gets the value, run post backup process

        Returns:
             True/False
    &#34;&#34;&#34;
    return self._prepost_settings.get(&#39;runPostBackupProcess&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.disasterrecovery.DisasterRecoveryManagement.run_post_scan_process"><code class="name">var <span class="ident">run_post_scan_process</span></code></dt>
<dd>
<div class="desc"><p>gets the value, run post scan process</p>
<pre><code>Returns:
     True/False
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/disasterrecovery.py#L1204-L1212" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def run_post_scan_process(self):
    &#34;&#34;&#34;
    gets the value, run post scan process

        Returns:
             True/False
    &#34;&#34;&#34;
    return self._prepost_settings.get(&#39;runPostScanProcess&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.disasterrecovery.DisasterRecoveryManagement.upload_backup_metadata_to_cloud"><code class="name">var <span class="ident">upload_backup_metadata_to_cloud</span></code></dt>
<dd>
<div class="desc"><p>gets the upload backup metadata to cloud setting</p>
<pre><code>Returns:
     True/False
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/disasterrecovery.py#L1048-L1056" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def upload_backup_metadata_to_cloud(self):
    &#34;&#34;&#34;
    gets the upload backup metadata to cloud setting

        Returns:
             True/False
    &#34;&#34;&#34;
    return self._export_settings.get(&#39;uploadBackupMetadataToCloud&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.disasterrecovery.DisasterRecoveryManagement.upload_backup_metadata_to_cloud_lib"><code class="name">var <span class="ident">upload_backup_metadata_to_cloud_lib</span></code></dt>
<dd>
<div class="desc"><p>gets the upload metadata to cloud lib</p>
<pre><code>Returns:
    True/False
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/disasterrecovery.py#L1058-L1066" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def upload_backup_metadata_to_cloud_lib(self):
    &#34;&#34;&#34;
    gets the upload metadata to cloud lib

        Returns:
            True/False
    &#34;&#34;&#34;
    return self._export_settings.get(&#39;uploadBackupMetadataToCloudLib&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.disasterrecovery.DisasterRecoveryManagement.use_impersonate_user"><code class="name">var <span class="ident">use_impersonate_user</span></code></dt>
<dd>
<div class="desc"><p>gets the impersonate user(True/False)</p>
<pre><code>Returns:
      True/False
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/disasterrecovery.py#L933-L941" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def use_impersonate_user(self):
    &#34;&#34;&#34;
    gets the impersonate user(True/False)

        Returns:
              True/False
    &#34;&#34;&#34;
    return self._prepost_settings.get(&#39;useImpersonateUser&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.disasterrecovery.DisasterRecoveryManagement.use_vss"><code class="name">var <span class="ident">use_vss</span></code></dt>
<dd>
<div class="desc"><p>gets the value, use vss()</p>
<pre><code>Returns:
    True/False
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/disasterrecovery.py#L980-L988" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def use_vss(self):
    &#34;&#34;&#34;
    gets the value, use vss()

        Returns:
            True/False
    &#34;&#34;&#34;
    return self._export_settings.get(&#39;isUseVSS&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.disasterrecovery.DisasterRecoveryManagement.wild_card_settings"><code class="name">var <span class="ident">wild_card_settings</span></code></dt>
<dd>
<div class="desc"><p>gets the wild card settings</p>
<pre><code>Returns:
    (str)       --     client logs that are to be backed up
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/disasterrecovery.py#L1007-L1015" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def wild_card_settings(self):
    &#34;&#34;&#34;
    gets the wild card settings

        Returns:
            (str)       --     client logs that are to be backed up
    &#34;&#34;&#34;
    return self._export_settings.get(&#39;wildCardSetting&#39;)</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.disasterrecovery.DisasterRecoveryManagement.get_cloud_regions"><code class="name flex">
<span>def <span class="ident">get_cloud_regions</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns : dict of available dr backup regions.
Ex:
{
"defaultRegion": "southindia",
"regions": [
{
"regionCode": "eastus2",
"displayName": "East US 2"
}
{
"regionCode": "southindia",
"displayName": "(Asia Pacific) South India"
}
]
}</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/disasterrecovery.py#L736-L764" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_cloud_regions(self):
    &#34;&#34;&#34;
    Returns : dict of available dr backup regions.
                Ex:
                {
                    &#34;defaultRegion&#34;: &#34;southindia&#34;,
                    &#34;regions&#34;: [
                        {
                            &#34;regionCode&#34;: &#34;eastus2&#34;,
                            &#34;displayName&#34;: &#34;East US 2&#34;
                        }
                        {
                            &#34;regionCode&#34;: &#34;southindia&#34;,
                            &#34;displayName&#34;: &#34;(Asia Pacific) South India&#34;
                        }
                    ]
                }
    &#34;&#34;&#34;
    flag, response = self._cvpysdk_object.make_request(
        method=&#39;GET&#39;,
        url=self.services[&#39;DRBACKUP_REGIONS&#39;]
    )
    if flag:
        if response and response.json():
            return response.json()
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.disasterrecovery.DisasterRecoveryManagement.impersonate_user"><code class="name flex">
<span>def <span class="ident">impersonate_user</span></span>(<span>self, flag, username, password)</span>
</code></dt>
<dd>
<div class="desc"><p>Enable/Disable Impersonate user option for pre/post scripts.</p>
<pre><code>Args:
    flag        (bool)      --  True/False.

    username    (str)       --  username with admin privileges.

    password    (str)       --  password for the account.

Returns:
    None
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/disasterrecovery.py#L907-L931" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def impersonate_user(self, flag, username, password):
    &#34;&#34;&#34;
    Enable/Disable Impersonate user option for pre/post scripts.

        Args:
            flag        (bool)      --  True/False.

            username    (str)       --  username with admin privileges.

            password    (str)       --  password for the account.

        Returns:
            None
    &#34;&#34;&#34;
    if isinstance(flag, bool):
        self._prepost_settings[&#39;useImpersonateUser&#39;] = flag
        if flag:
            if isinstance(username, str) and isinstance(password, str):
                self._prepost_settings[&#39;impersonateUser&#39;][&#39;userName&#39;] = username
                self._prepost_settings[&#39;impersonateUser&#39;][&#39;password&#39;] = b64encode(password.encode()).decode()
            else:
                raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)
        self._set_dr_properties()
    else:
        raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.disasterrecovery.DisasterRecoveryManagement.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>refreshs the dr settings associated with commcell.</p>
<h2 id="returns">Returns</h2>
<p>None</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/disasterrecovery.py#L803-L812" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;
    refreshs the dr settings associated with commcell.

    Returns:
        None
    &#34;&#34;&#34;
    self._prepost_settings = None
    self._export_settings = None
    self._get_dr_properties()</code></pre>
</details>
</dd>
<dt id="cvpysdk.disasterrecovery.DisasterRecoveryManagement.set_local_dr_path"><code class="name flex">
<span>def <span class="ident">set_local_dr_path</span></span>(<span>self, path)</span>
</code></dt>
<dd>
<div class="desc"><p>Sets local DR path</p>
<pre><code>Args:
     path       (str)       --         local path.

Returns:
    None
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/disasterrecovery.py#L814-L828" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_local_dr_path(self, path):
    &#34;&#34;&#34;
    Sets local DR path

        Args:
             path       (str)       --         local path.

        Returns:
            None
    &#34;&#34;&#34;
    if isinstance(path, str):
        self._export_settings[&#39;backupMetadataFolder&#39;] = path
        self._set_dr_properties()
    else:
        raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.disasterrecovery.DisasterRecoveryManagement.set_network_dr_path"><code class="name flex">
<span>def <span class="ident">set_network_dr_path</span></span>(<span>self, path, username, password)</span>
</code></dt>
<dd>
<div class="desc"><p>Sets network DR path</p>
<pre><code>Args:
     path       (str)       --      UNC path.

     username   (str)       --      username with admin privileges of the remote machine.

     password   (str)       --      password.

Returns:
    None
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/disasterrecovery.py#L830-L850" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_network_dr_path(self, path, username, password):
    &#34;&#34;&#34;
    Sets network DR path

        Args:
             path       (str)       --      UNC path.

             username   (str)       --      username with admin privileges of the remote machine.

             password   (str)       --      password.

        Returns:
            None
    &#34;&#34;&#34;
    if isinstance(path, str) and isinstance(username, str) and isinstance(password, str):
        self._export_settings[&#39;backupMetadataFolder&#39;] = path
        self._export_settings[&#39;networkUserAccount&#39;][&#39;userName&#39;] = username
        self._export_settings[&#39;networkUserAccount&#39;][&#39;password&#39;] = b64encode(password.encode()).decode()
        self._set_dr_properties()
    else:
        raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.disasterrecovery.DisasterRecoveryManagement.upload_metdata_to_cloud_library"><code class="name flex">
<span>def <span class="ident">upload_metdata_to_cloud_library</span></span>(<span>self, flag, libraryname=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Enable/Disable upload metadata to cloud library</p>
<pre><code>Args:
     flag       (bool)      --      True/False.

     libraryname   (str/object)    --      Third party cloud library name/disklibrary object.

Returns:
    None
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/disasterrecovery.py#L880-L905" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def upload_metdata_to_cloud_library(self, flag, libraryname=None):
    &#34;&#34;&#34;
    Enable/Disable upload metadata to cloud library

        Args:
             flag       (bool)      --      True/False.

             libraryname   (str/object)    --      Third party cloud library name/disklibrary object.

        Returns:
            None
    &#34;&#34;&#34;
    if isinstance(flag, bool):
        self._export_settings[&#39;uploadBackupMetadataToCloudLib&#39;] = flag
        if flag:
            if isinstance(libraryname, str):
                cloud_lib_obj = DiskLibrary(self._commcell, library_name=libraryname)
            elif isinstance(libraryname, DiskLibrary):
                cloud_lib_obj = libraryname
            else:
                raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)
            self._export_settings[&#39;cloudLibrary&#39;][&#39;libraryName&#39;] = cloud_lib_obj.name
            self._export_settings[&#39;cloudLibrary&#39;][&#39;libraryId&#39;] = int(cloud_lib_obj.library_id)
        self._set_dr_properties()
    else:
        raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.disasterrecovery.DisasterRecoveryManagement.upload_metdata_to_commvault_cloud"><code class="name flex">
<span>def <span class="ident">upload_metdata_to_commvault_cloud</span></span>(<span>self, flag, username=None, password=None, region=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Enable/Disable upload metadata to commvault cloud setting.</p>
<pre><code>Args:
     flag       (bool)      --      True/False.

     username   (str)       --      username of the commvault cloud.

     password   (str)       --      password of the commvault cloud.

     region     (str)       --      region to upload the DRBackup, None will set to default region

Returns:
     None
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/disasterrecovery.py#L852-L878" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def upload_metdata_to_commvault_cloud(self, flag, username=None, password=None, region=None):
    &#34;&#34;&#34;
    Enable/Disable upload metadata to commvault cloud setting.

        Args:
             flag       (bool)      --      True/False.

             username   (str)       --      username of the commvault cloud.

             password   (str)       --      password of the commvault cloud.

             region     (str)       --      region to upload the DRBackup, None will set to default region

        Returns:
             None
    &#34;&#34;&#34;
    if isinstance(flag, bool):
        self._export_settings[&#39;uploadBackupMetadataToCloud&#39;] = flag
        if flag:
            if isinstance(username, str) and isinstance(password, str):
                self._export_settings[&#39;cloudCredentials&#39;][&#39;userName&#39;] = username
                self._export_settings[&#39;cloudCredentials&#39;][&#39;password&#39;] = b64encode(password.encode()).decode()
            else:
                raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)
        self._set_commvault_cloud_upload(flag)
    else:
        raise SDKException(&#39;DisasterRecovery&#39;, &#39;101&#39;)</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#disasterrecovery">DisasterRecovery:</a></li>
<li><a href="#disasterrecovery-attributes">DisasterRecovery Attributes</a></li>
<li><a href="#disasterrecoverymanagement">DisasterRecoveryManagement:</a></li>
<li><a href="#disasterrecoverymanagement-attributes">DisasterRecoveryManagement Attributes:</a></li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.disasterrecovery.DisasterRecovery" href="#cvpysdk.disasterrecovery.DisasterRecovery">DisasterRecovery</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.disasterrecovery.DisasterRecovery.backup_type" href="#cvpysdk.disasterrecovery.DisasterRecovery.backup_type">backup_type</a></code></li>
<li><code><a title="cvpysdk.disasterrecovery.DisasterRecovery.client_list" href="#cvpysdk.disasterrecovery.DisasterRecovery.client_list">client_list</a></code></li>
<li><code><a title="cvpysdk.disasterrecovery.DisasterRecovery.disaster_recovery_backup" href="#cvpysdk.disasterrecovery.DisasterRecovery.disaster_recovery_backup">disaster_recovery_backup</a></code></li>
<li><code><a title="cvpysdk.disasterrecovery.DisasterRecovery.disaster_recovery_management" href="#cvpysdk.disasterrecovery.DisasterRecovery.disaster_recovery_management">disaster_recovery_management</a></code></li>
<li><code><a title="cvpysdk.disasterrecovery.DisasterRecovery.is_appstudio_db_enabled" href="#cvpysdk.disasterrecovery.DisasterRecovery.is_appstudio_db_enabled">is_appstudio_db_enabled</a></code></li>
<li><code><a title="cvpysdk.disasterrecovery.DisasterRecovery.is_compression_enabled" href="#cvpysdk.disasterrecovery.DisasterRecovery.is_compression_enabled">is_compression_enabled</a></code></li>
<li><code><a title="cvpysdk.disasterrecovery.DisasterRecovery.is_cvcloud_db_enabled" href="#cvpysdk.disasterrecovery.DisasterRecovery.is_cvcloud_db_enabled">is_cvcloud_db_enabled</a></code></li>
<li><code><a title="cvpysdk.disasterrecovery.DisasterRecovery.is_dm2_db_enabled" href="#cvpysdk.disasterrecovery.DisasterRecovery.is_dm2_db_enabled">is_dm2_db_enabled</a></code></li>
<li><code><a title="cvpysdk.disasterrecovery.DisasterRecovery.is_history_db_enabled" href="#cvpysdk.disasterrecovery.DisasterRecovery.is_history_db_enabled">is_history_db_enabled</a></code></li>
<li><code><a title="cvpysdk.disasterrecovery.DisasterRecovery.is_workflow_db_enabled" href="#cvpysdk.disasterrecovery.DisasterRecovery.is_workflow_db_enabled">is_workflow_db_enabled</a></code></li>
<li><code><a title="cvpysdk.disasterrecovery.DisasterRecovery.reset_to_defaults" href="#cvpysdk.disasterrecovery.DisasterRecovery.reset_to_defaults">reset_to_defaults</a></code></li>
<li><code><a title="cvpysdk.disasterrecovery.DisasterRecovery.restore_out_of_place" href="#cvpysdk.disasterrecovery.DisasterRecovery.restore_out_of_place">restore_out_of_place</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.disasterrecovery.DisasterRecoveryManagement" href="#cvpysdk.disasterrecovery.DisasterRecoveryManagement">DisasterRecoveryManagement</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.disasterrecovery.DisasterRecoveryManagement.backup_metadata_folder" href="#cvpysdk.disasterrecovery.DisasterRecoveryManagement.backup_metadata_folder">backup_metadata_folder</a></code></li>
<li><code><a title="cvpysdk.disasterrecovery.DisasterRecoveryManagement.dr_storage_policy" href="#cvpysdk.disasterrecovery.DisasterRecoveryManagement.dr_storage_policy">dr_storage_policy</a></code></li>
<li><code><a title="cvpysdk.disasterrecovery.DisasterRecoveryManagement.get_cloud_regions" href="#cvpysdk.disasterrecovery.DisasterRecoveryManagement.get_cloud_regions">get_cloud_regions</a></code></li>
<li><code><a title="cvpysdk.disasterrecovery.DisasterRecoveryManagement.impersonate_user" href="#cvpysdk.disasterrecovery.DisasterRecoveryManagement.impersonate_user">impersonate_user</a></code></li>
<li><code><a title="cvpysdk.disasterrecovery.DisasterRecoveryManagement.number_of_metadata" href="#cvpysdk.disasterrecovery.DisasterRecoveryManagement.number_of_metadata">number_of_metadata</a></code></li>
<li><code><a title="cvpysdk.disasterrecovery.DisasterRecoveryManagement.post_backup_process" href="#cvpysdk.disasterrecovery.DisasterRecoveryManagement.post_backup_process">post_backup_process</a></code></li>
<li><code><a title="cvpysdk.disasterrecovery.DisasterRecoveryManagement.post_scan_process" href="#cvpysdk.disasterrecovery.DisasterRecoveryManagement.post_scan_process">post_scan_process</a></code></li>
<li><code><a title="cvpysdk.disasterrecovery.DisasterRecoveryManagement.pre_backup_process" href="#cvpysdk.disasterrecovery.DisasterRecoveryManagement.pre_backup_process">pre_backup_process</a></code></li>
<li><code><a title="cvpysdk.disasterrecovery.DisasterRecoveryManagement.pre_scan_process" href="#cvpysdk.disasterrecovery.DisasterRecoveryManagement.pre_scan_process">pre_scan_process</a></code></li>
<li><code><a title="cvpysdk.disasterrecovery.DisasterRecoveryManagement.refresh" href="#cvpysdk.disasterrecovery.DisasterRecoveryManagement.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.disasterrecovery.DisasterRecoveryManagement.region" href="#cvpysdk.disasterrecovery.DisasterRecoveryManagement.region">region</a></code></li>
<li><code><a title="cvpysdk.disasterrecovery.DisasterRecoveryManagement.run_post_backup_process" href="#cvpysdk.disasterrecovery.DisasterRecoveryManagement.run_post_backup_process">run_post_backup_process</a></code></li>
<li><code><a title="cvpysdk.disasterrecovery.DisasterRecoveryManagement.run_post_scan_process" href="#cvpysdk.disasterrecovery.DisasterRecoveryManagement.run_post_scan_process">run_post_scan_process</a></code></li>
<li><code><a title="cvpysdk.disasterrecovery.DisasterRecoveryManagement.set_local_dr_path" href="#cvpysdk.disasterrecovery.DisasterRecoveryManagement.set_local_dr_path">set_local_dr_path</a></code></li>
<li><code><a title="cvpysdk.disasterrecovery.DisasterRecoveryManagement.set_network_dr_path" href="#cvpysdk.disasterrecovery.DisasterRecoveryManagement.set_network_dr_path">set_network_dr_path</a></code></li>
<li><code><a title="cvpysdk.disasterrecovery.DisasterRecoveryManagement.upload_backup_metadata_to_cloud" href="#cvpysdk.disasterrecovery.DisasterRecoveryManagement.upload_backup_metadata_to_cloud">upload_backup_metadata_to_cloud</a></code></li>
<li><code><a title="cvpysdk.disasterrecovery.DisasterRecoveryManagement.upload_backup_metadata_to_cloud_lib" href="#cvpysdk.disasterrecovery.DisasterRecoveryManagement.upload_backup_metadata_to_cloud_lib">upload_backup_metadata_to_cloud_lib</a></code></li>
<li><code><a title="cvpysdk.disasterrecovery.DisasterRecoveryManagement.upload_metdata_to_cloud_library" href="#cvpysdk.disasterrecovery.DisasterRecoveryManagement.upload_metdata_to_cloud_library">upload_metdata_to_cloud_library</a></code></li>
<li><code><a title="cvpysdk.disasterrecovery.DisasterRecoveryManagement.upload_metdata_to_commvault_cloud" href="#cvpysdk.disasterrecovery.DisasterRecoveryManagement.upload_metdata_to_commvault_cloud">upload_metdata_to_commvault_cloud</a></code></li>
<li><code><a title="cvpysdk.disasterrecovery.DisasterRecoveryManagement.use_impersonate_user" href="#cvpysdk.disasterrecovery.DisasterRecoveryManagement.use_impersonate_user">use_impersonate_user</a></code></li>
<li><code><a title="cvpysdk.disasterrecovery.DisasterRecoveryManagement.use_vss" href="#cvpysdk.disasterrecovery.DisasterRecoveryManagement.use_vss">use_vss</a></code></li>
<li><code><a title="cvpysdk.disasterrecovery.DisasterRecoveryManagement.wild_card_settings" href="#cvpysdk.disasterrecovery.DisasterRecoveryManagement.wild_card_settings">wild_card_settings</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>