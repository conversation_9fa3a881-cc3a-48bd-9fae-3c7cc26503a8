<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.instances.hanainstance API documentation</title>
<meta name="description" content="File for operating on a SAP HANA Instance …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.instances.hanainstance</code></h1>
</header>
<section id="section-intro">
<p>File for operating on a SAP HANA Instance.</p>
<p>SAPHANAInstance is the only class defined in this file.</p>
<p>SAPHANAInstance: Derived class from Instance Base class, representing a hana server instance,
and to perform operations on that instance</p>
<h2 id="saphanainstance">Saphanainstance</h2>
<p>sps_version()
&ndash;
returns the SPS version of the instance</p>
<p>instance_number()
&ndash;
returns the instance number of SAP HANA</p>
<p>sql_location_directory()
&ndash;
returns the SQL location directory of the Instance</p>
<p>instance_db_username()
&ndash;
returns the db username of the instance</p>
<p>db_instance_client()
&ndash;
returns the SAP HANA client associated with the instance</p>
<p>hdb_user_storekey()
&ndash;
returns the HDB user store key if its set</p>
<p>_restore_request_json()
&ndash;
returns the restore request json</p>
<p>_get_hana_restore_options()
&ndash;
returns the dict containing destination SAP HANA instance
names for the given client</p>
<p>restore()
&ndash;
runs the restore job for specified instance</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/hanainstance.py#L1-L496" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;File for operating on a SAP HANA Instance.

SAPHANAInstance is the only class defined in this file.

SAPHANAInstance: Derived class from Instance Base class, representing a hana server instance,
                       and to perform operations on that instance

SAPHANAInstance:

    sps_version()                   --  returns the SPS version of the instance

    instance_number()               --  returns the instance number of SAP HANA

    sql_location_directory()        --  returns the SQL location directory of the Instance

    instance_db_username()          --  returns the db username of the instance

    db_instance_client()            --  returns the SAP HANA client associated with the instance

    hdb_user_storekey()             --  returns the HDB user store key if its set

    _restore_request_json()         --  returns the restore request json

    _get_hana_restore_options()     --  returns the dict containing destination SAP HANA instance
                                            names for the given client

    restore()                       --  runs the restore job for specified instance

&#34;&#34;&#34;

from __future__ import absolute_import
from __future__ import unicode_literals

import threading

from ..instance import Instance
from ..exception import SDKException


class SAPHANAInstance(Instance):
    &#34;&#34;&#34;Derived class from Instance Base class, representing a SAP HANA instance,
        and to perform operations on that Instance.&#34;&#34;&#34;

    def __init__(self, agent_object, instance_name, instance_id=None):
        &#34;&#34;&#34;Initialize the subclient object

        Args:
            agent_object    (object):       instance of the agent class

            instance_name   (str):   name of the instance

            instance_id     (int):          ID of the instance

        &#34;&#34;&#34;
        super(SAPHANAInstance, self).__init__(agent_object, instance_name, instance_id)
        self.destination_instances_dict = {}

    @property
    def sps_version(self):
        &#34;&#34;&#34;
        Returns the sps version of the HANA instance

        Returns:
            sps version
        &#34;&#34;&#34;
        return self._properties[&#39;saphanaInstance&#39;][&#39;spsVersion&#39;]

    @property
    def instance_number(self):
        &#34;&#34;&#34;
        Returns the instance number of the HANA instance

        Returns:
            instance number
        &#34;&#34;&#34;
        return self._properties[&#39;saphanaInstance&#39;][&#39;dbInstanceNumber&#39;]

    @property
    def sql_location_directory(self):
        &#34;&#34;&#34;
        Returns the isql location directory of the HANA instance

        Returns:
            SQL location directory
        &#34;&#34;&#34;
        return self._properties[&#39;saphanaInstance&#39;][&#39;hdbsqlLocationDirectory&#39;]

    @property
    def instance_db_username(self):
        &#34;&#34;&#34;
        Returns the username of the HANA instance database

        Returns:
            instance db username
        &#34;&#34;&#34;
        return self._properties[&#39;saphanaInstance&#39;][&#39;dbUser&#39;][&#39;userName&#39;]

    @property
    def db_instance_client(self):
        &#34;&#34;&#34;
        Returns the client name of the HANA instance

        Returns:
            db instance client name
        &#34;&#34;&#34;
        return self._properties[&#39;saphanaInstance&#39;][&#39;DBInstances&#39;][0]

    @property
    def hdb_user_storekey(self):
        &#34;&#34;&#34;
        Returns the hdb user store key of the HANA instance

        Returns:
            hdb user store key
        &#34;&#34;&#34;
        return self._properties[&#39;saphanaInstance&#39;][&#39;hdbuserstorekey&#39;]

    def _restore_request_json(
            self,
            destination_client,
            destination_instance,
            backupset_name=&#34;default&#34;,
            backup_prefix=None,
            point_in_time=None,
            initialize_log_area=False,
            use_hardware_revert=False,
            clone_env=False,
            check_access=False,
            destination_instance_dir=None,
            ignore_delta_backups=False,
            no_of_streams=2,
            catalog_time=None):
        &#34;&#34;&#34;Returns the JSON request to pass to the API as per the options selected by the user.

            Args:
                destination_client          (str)   --  HANA client to restore the database at

                destination_instance        (str)   --  destination instance to restore the db at

                backupset_name              (str)   --  backupset name of the instance to be
                                                            restored. If the instance is a single
                                                            DB instance then the backupset name is
                                                            ``default``.
                    default: default

                backup_prefix               (str)   --  prefix of the backup job
                    default: None

                point_in_time               (str)   --  time to which db should be restored to
                    default: None

                initialize_log_area         (bool)  --  boolean to specify whether to initialize
                                                            the new log area after restore
                    default: False

                use_hardware_revert         (bool)  --  boolean to specify whether to do a
                                                            hardware revert in restore
                    default: False

                clone_env                   (bool)  --  boolean to specify whether the database
                                                            should be cloned or not
                    default: False

                check_access                (bool)  --  check access during restore or not
                    default: True

                destination_instance_dir    (str)   --  HANA data directory for snap cross instance
                                                            restore or cross machine restores
                    default: None

                ignore_delta_backups        (bool)  --  whether to ignore delta backups during
                                                            restore or not
                    default: True

                no_of_streams               (int)   --  number of streams to be used for restore

                    default: 2

                catalog_time                (str)   --  catalog time to which should be used to restore

                    default: None

            Returns:
                dict    -   JSON request to pass to the API

        &#34;&#34;&#34;
        self._get_hana_restore_options(destination_client)

        if destination_instance is None:
            destination_instance = self.instance_name
        else:
            if destination_instance not in self.destination_instances_dict:
                raise SDKException(
                    &#39;Instance&#39;, &#39;102&#39;, &#39;No Instance exists with name: {0}&#39;.format(
                        destination_instance
                    )
                )

        destination_hana_client = self.destination_instances_dict[destination_instance][
            &#39;destHANAClient&#39;]

        if backup_prefix is None:
            backup_prefix = &#34;&#34;

        databases = []

        if backupset_name != &#34;default&#34;:
            databases.append(backupset_name)

        if point_in_time is None:
            recover_time = 0
            point_in_time = {}
        else:
            if not isinstance(point_in_time, str):
                raise SDKException(&#39;Instance&#39;, 103)

            point_in_time = {
                &#39;time&#39;: int(point_in_time)
            }
            recover_time = 1

        request_json = {
            &#34;taskInfo&#34;: {
                &#34;associations&#34;: [{
                    &#34;clientName&#34;: self._agent_object._client_object.client_name,
                    &#34;appName&#34;: self._agent_object.agent_name,
                    &#34;instanceName&#34;: self.instance_name.upper(),
                    &#34;backupsetName&#34;: backupset_name,
                    &#34;suclientName&#34;: &#34;&#34;
                }],
                &#34;task&#34;: {
                    &#34;initiatedFrom&#34;: 1,
                    &#34;taskType&#34;: 1
                },
                &#34;subTasks&#34;: [{
                    &#34;subTask&#34;: {
                        &#34;subTaskType&#34;: 3,
                        &#34;operationType&#34;: 1001
                    },
                    &#34;options&#34;: {
                        &#34;restoreOptions&#34;: {
                            &#34;hanaOpt&#34;: {
                                &#34;initializeLogArea&#34;: initialize_log_area,
                                &#34;useHardwareRevert&#34;: use_hardware_revert,
                                &#34;cloneEnv&#34;: clone_env,
                                &#34;checkAccess&#34;: check_access,
                                &#34;backupPrefix&#34;: backup_prefix,
                                &#34;destDbName&#34;: destination_instance.upper(),
                                &#34;destPseudoClientName&#34;: str(destination_client),
                                &#34;ignoreDeltaBackups&#34;: ignore_delta_backups,
                                &#34;destClientName&#34;: destination_hana_client,
                                &#34;databases&#34;: databases,
                                &#34;recoverTime&#34;: recover_time,
                                &#34;pointInTime&#34;: point_in_time
                            },
                            &#34;destination&#34;: {
                                &#34;destinationInstance&#34;: {
                                    &#34;clientName&#34;: destination_client,
                                    &#34;appName&#34;: self._agent_object.agent_name,
                                    &#34;instanceName&#34;: destination_instance
                                },
                                &#34;destClient&#34;: {
                                    &#34;clientName&#34;: destination_hana_client
                                },
                                &#34;noOfStreams&#34;: no_of_streams
                            },
                            &#34;browseOption&#34;: {
                                &#34;backupset&#34;: {
                                    &#34;clientName&#34;: self._agent_object._client_object.client_name
                                }
                            }
                        }
                    }
                }]
            }
        }
        if catalog_time:
            if not isinstance(catalog_time, str):
                raise SDKException(&#39;Instance&#39;, 103)
            catalog_time = {
                &#39;time&#39;: int(catalog_time)
            }
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][
                &#39;hanaOpt&#39;][&#39;catalogPointInTime&#39;] = catalog_time
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][
                &#39;hanaOpt&#39;][&#39;catalogRecoverTime&#39;] = 1

        if destination_instance_dir is not None:
            instance_dir = {
                &#39;destinationInstanceDir&#39;: destination_instance_dir
            }

            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][
                &#39;hanaOpt&#39;].update(instance_dir)

        return request_json

    def _get_hana_restore_options(self, destination_client_name):
        &#34;&#34;&#34;Runs the /GetDestinationsToRestore API,
            and returns the contents after parsing the response.

            Args:
                destination_client_name     (str)   --  destination client to restore to

            Returns:
                dict    -   dictionary consisting of the HANA destination server options

            Raises:
                SDKException:
                    if failed to get HANA clients

                    if no client exits on commcell

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        webservice = self._commcell_object._services[&#39;RESTORE_OPTIONS&#39;] % (
            self._agent_object.agent_id
        )

        flag, response = self._commcell_object._cvpysdk_object.make_request(&#34;GET&#34;, webservice)

        destination_clients_dict = {}

        if flag:
            if response.json():
                if &#39;genericEntityList&#39; in response.json():
                    generic_entity_list = response.json()[&#39;genericEntityList&#39;]

                    for client_entity in generic_entity_list:
                        clients_dict = {
                            client_entity[&#39;clientName&#39;].lower(): {
                                &#34;clientId&#34;: client_entity[&#39;clientId&#39;]
                            }
                        }
                        destination_clients_dict.update(clients_dict)
                elif &#39;error&#39; in response.json():
                    if &#39;errorMessage&#39; in response.json()[&#39;error&#39;]:
                        error_message = response.json()[&#39;error&#39;][&#39;errorMessage&#39;]
                        raise SDKException(&#39;Client&#39;, &#39;102&#39;, error_message)
                    else:
                        raise SDKException(&#39;Client&#39;, &#39;102&#39;, &#39;No client exists on commcell&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        webservice = self._commcell_object._services[&#39;GET_ALL_INSTANCES&#39;] % (
            destination_clients_dict[destination_client_name][&#39;clientId&#39;]
        )

        flag, response = self._commcell_object._cvpysdk_object.make_request(&#34;GET&#34;, webservice)

        if flag:
            if response.json():
                if &#39;instanceProperties&#39; in response.json():
                    for instance in response.json()[&#39;instanceProperties&#39;]:
                        instances_dict = {
                            instance[&#39;instance&#39;][&#39;instanceName&#39;].lower(): {
                                &#34;clientId&#34;: instance[&#39;instance&#39;][&#39;clientId&#39;],
                                &#34;instanceId&#34;: instance[&#39;instance&#39;][&#39;instanceId&#39;],
                                &#34;destHANAClient&#34;: instance[&#39;saphanaInstance&#39;][
                                    &#39;DBInstances&#39;][0][&#39;clientName&#39;]
                            }
                        }
                        self.destination_instances_dict.update(instances_dict)
                elif &#39;error&#39; in response.json():
                    if &#39;errorMessage&#39; in response.json()[&#39;error&#39;]:
                        error_message = response.json()[&#39;error&#39;][&#39;errorMessage&#39;]
                        raise SDKException(&#39;Instance&#39;, &#39;102&#39;, error_message)
                    else:
                        raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;No Instance exists on commcell&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def restore(
            self,
            pseudo_client,
            instance,
            backupset_name=&#34;default&#34;,
            backup_prefix=None,
            point_in_time=None,
            initialize_log_area=False,
            use_hardware_revert=False,
            clone_env=False,
            check_access=True,
            destination_instance_dir=None,
            ignore_delta_backups=True,
            no_of_streams=2,
            catalog_time=None):
        &#34;&#34;&#34;Restores the databases specified in the input paths list.

            Args:
                pseudo_client               (str)   --  HANA client to restore the database at

                instance                    (str)   --  destination instance to restore the db at

                backupset_name              (str)   --  backupset name of the instance to be
                                                            restored. If the instance is a single
                                                            DB instance then the backupset name is
                                                            ``default``.
                    default: default

                backup_prefix               (str)   --  prefix of the backup job
                    default: None

                point_in_time               (str)   --  time to which db should be restored to
                    default: None

                initialize_log_area         (bool)  --  boolean to specify whether to initialize
                                                            the new log area after restore
                    default: False

                use_hardware_revert         (bool)  --  boolean to specify whether to do a
                                                            hardware revert in restore
                    default: False

                clone_env                   (bool)  --  boolean to specify whether the database
                                                            should be cloned or not
                    default: False

                check_access                (bool)  --  check access during restore or not
                    default: True

                destination_instance_dir    (str)   --  HANA data directory for snap cross instance
                                                            restore or cross machine restores
                    default: None

                ignore_delta_backups        (bool)  --  whether to ignore delta backups during
                                                            restore or not
                    default: True

                no_of_streams               (int)   --  number of streams to be used for restore

                    default: 2

                catalog_time                (str)   --  catalog time to which should be used to restore

                    default: None

            Returns:
                object  -   instance of the Job class for this restore job

            Raises:
                SDKException:
                    if instance is not a string or object

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        if not isinstance(instance, (str, Instance)):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        request_json = self._restore_request_json(
            pseudo_client,
            instance,
            backupset_name,
            backup_prefix,
            point_in_time,
            initialize_log_area,
            use_hardware_revert,
            clone_env,
            check_access,
            destination_instance_dir,
            ignore_delta_backups,
            no_of_streams,
            catalog_time
        )

        return self._process_restore_response(request_json)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.instances.hanainstance.SAPHANAInstance"><code class="flex name class">
<span>class <span class="ident">SAPHANAInstance</span></span>
<span>(</span><span>agent_object, instance_name, instance_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Derived class from Instance Base class, representing a SAP HANA instance,
and to perform operations on that Instance.</p>
<p>Initialize the subclient object</p>
<h2 id="args">Args</h2>
<p>agent_object
(object):
instance of the agent class</p>
<p>instance_name
(str):
name of the instance</p>
<p>instance_id
(int):
ID of the instance</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/hanainstance.py#L58-L496" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class SAPHANAInstance(Instance):
    &#34;&#34;&#34;Derived class from Instance Base class, representing a SAP HANA instance,
        and to perform operations on that Instance.&#34;&#34;&#34;

    def __init__(self, agent_object, instance_name, instance_id=None):
        &#34;&#34;&#34;Initialize the subclient object

        Args:
            agent_object    (object):       instance of the agent class

            instance_name   (str):   name of the instance

            instance_id     (int):          ID of the instance

        &#34;&#34;&#34;
        super(SAPHANAInstance, self).__init__(agent_object, instance_name, instance_id)
        self.destination_instances_dict = {}

    @property
    def sps_version(self):
        &#34;&#34;&#34;
        Returns the sps version of the HANA instance

        Returns:
            sps version
        &#34;&#34;&#34;
        return self._properties[&#39;saphanaInstance&#39;][&#39;spsVersion&#39;]

    @property
    def instance_number(self):
        &#34;&#34;&#34;
        Returns the instance number of the HANA instance

        Returns:
            instance number
        &#34;&#34;&#34;
        return self._properties[&#39;saphanaInstance&#39;][&#39;dbInstanceNumber&#39;]

    @property
    def sql_location_directory(self):
        &#34;&#34;&#34;
        Returns the isql location directory of the HANA instance

        Returns:
            SQL location directory
        &#34;&#34;&#34;
        return self._properties[&#39;saphanaInstance&#39;][&#39;hdbsqlLocationDirectory&#39;]

    @property
    def instance_db_username(self):
        &#34;&#34;&#34;
        Returns the username of the HANA instance database

        Returns:
            instance db username
        &#34;&#34;&#34;
        return self._properties[&#39;saphanaInstance&#39;][&#39;dbUser&#39;][&#39;userName&#39;]

    @property
    def db_instance_client(self):
        &#34;&#34;&#34;
        Returns the client name of the HANA instance

        Returns:
            db instance client name
        &#34;&#34;&#34;
        return self._properties[&#39;saphanaInstance&#39;][&#39;DBInstances&#39;][0]

    @property
    def hdb_user_storekey(self):
        &#34;&#34;&#34;
        Returns the hdb user store key of the HANA instance

        Returns:
            hdb user store key
        &#34;&#34;&#34;
        return self._properties[&#39;saphanaInstance&#39;][&#39;hdbuserstorekey&#39;]

    def _restore_request_json(
            self,
            destination_client,
            destination_instance,
            backupset_name=&#34;default&#34;,
            backup_prefix=None,
            point_in_time=None,
            initialize_log_area=False,
            use_hardware_revert=False,
            clone_env=False,
            check_access=False,
            destination_instance_dir=None,
            ignore_delta_backups=False,
            no_of_streams=2,
            catalog_time=None):
        &#34;&#34;&#34;Returns the JSON request to pass to the API as per the options selected by the user.

            Args:
                destination_client          (str)   --  HANA client to restore the database at

                destination_instance        (str)   --  destination instance to restore the db at

                backupset_name              (str)   --  backupset name of the instance to be
                                                            restored. If the instance is a single
                                                            DB instance then the backupset name is
                                                            ``default``.
                    default: default

                backup_prefix               (str)   --  prefix of the backup job
                    default: None

                point_in_time               (str)   --  time to which db should be restored to
                    default: None

                initialize_log_area         (bool)  --  boolean to specify whether to initialize
                                                            the new log area after restore
                    default: False

                use_hardware_revert         (bool)  --  boolean to specify whether to do a
                                                            hardware revert in restore
                    default: False

                clone_env                   (bool)  --  boolean to specify whether the database
                                                            should be cloned or not
                    default: False

                check_access                (bool)  --  check access during restore or not
                    default: True

                destination_instance_dir    (str)   --  HANA data directory for snap cross instance
                                                            restore or cross machine restores
                    default: None

                ignore_delta_backups        (bool)  --  whether to ignore delta backups during
                                                            restore or not
                    default: True

                no_of_streams               (int)   --  number of streams to be used for restore

                    default: 2

                catalog_time                (str)   --  catalog time to which should be used to restore

                    default: None

            Returns:
                dict    -   JSON request to pass to the API

        &#34;&#34;&#34;
        self._get_hana_restore_options(destination_client)

        if destination_instance is None:
            destination_instance = self.instance_name
        else:
            if destination_instance not in self.destination_instances_dict:
                raise SDKException(
                    &#39;Instance&#39;, &#39;102&#39;, &#39;No Instance exists with name: {0}&#39;.format(
                        destination_instance
                    )
                )

        destination_hana_client = self.destination_instances_dict[destination_instance][
            &#39;destHANAClient&#39;]

        if backup_prefix is None:
            backup_prefix = &#34;&#34;

        databases = []

        if backupset_name != &#34;default&#34;:
            databases.append(backupset_name)

        if point_in_time is None:
            recover_time = 0
            point_in_time = {}
        else:
            if not isinstance(point_in_time, str):
                raise SDKException(&#39;Instance&#39;, 103)

            point_in_time = {
                &#39;time&#39;: int(point_in_time)
            }
            recover_time = 1

        request_json = {
            &#34;taskInfo&#34;: {
                &#34;associations&#34;: [{
                    &#34;clientName&#34;: self._agent_object._client_object.client_name,
                    &#34;appName&#34;: self._agent_object.agent_name,
                    &#34;instanceName&#34;: self.instance_name.upper(),
                    &#34;backupsetName&#34;: backupset_name,
                    &#34;suclientName&#34;: &#34;&#34;
                }],
                &#34;task&#34;: {
                    &#34;initiatedFrom&#34;: 1,
                    &#34;taskType&#34;: 1
                },
                &#34;subTasks&#34;: [{
                    &#34;subTask&#34;: {
                        &#34;subTaskType&#34;: 3,
                        &#34;operationType&#34;: 1001
                    },
                    &#34;options&#34;: {
                        &#34;restoreOptions&#34;: {
                            &#34;hanaOpt&#34;: {
                                &#34;initializeLogArea&#34;: initialize_log_area,
                                &#34;useHardwareRevert&#34;: use_hardware_revert,
                                &#34;cloneEnv&#34;: clone_env,
                                &#34;checkAccess&#34;: check_access,
                                &#34;backupPrefix&#34;: backup_prefix,
                                &#34;destDbName&#34;: destination_instance.upper(),
                                &#34;destPseudoClientName&#34;: str(destination_client),
                                &#34;ignoreDeltaBackups&#34;: ignore_delta_backups,
                                &#34;destClientName&#34;: destination_hana_client,
                                &#34;databases&#34;: databases,
                                &#34;recoverTime&#34;: recover_time,
                                &#34;pointInTime&#34;: point_in_time
                            },
                            &#34;destination&#34;: {
                                &#34;destinationInstance&#34;: {
                                    &#34;clientName&#34;: destination_client,
                                    &#34;appName&#34;: self._agent_object.agent_name,
                                    &#34;instanceName&#34;: destination_instance
                                },
                                &#34;destClient&#34;: {
                                    &#34;clientName&#34;: destination_hana_client
                                },
                                &#34;noOfStreams&#34;: no_of_streams
                            },
                            &#34;browseOption&#34;: {
                                &#34;backupset&#34;: {
                                    &#34;clientName&#34;: self._agent_object._client_object.client_name
                                }
                            }
                        }
                    }
                }]
            }
        }
        if catalog_time:
            if not isinstance(catalog_time, str):
                raise SDKException(&#39;Instance&#39;, 103)
            catalog_time = {
                &#39;time&#39;: int(catalog_time)
            }
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][
                &#39;hanaOpt&#39;][&#39;catalogPointInTime&#39;] = catalog_time
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][
                &#39;hanaOpt&#39;][&#39;catalogRecoverTime&#39;] = 1

        if destination_instance_dir is not None:
            instance_dir = {
                &#39;destinationInstanceDir&#39;: destination_instance_dir
            }

            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][
                &#39;hanaOpt&#39;].update(instance_dir)

        return request_json

    def _get_hana_restore_options(self, destination_client_name):
        &#34;&#34;&#34;Runs the /GetDestinationsToRestore API,
            and returns the contents after parsing the response.

            Args:
                destination_client_name     (str)   --  destination client to restore to

            Returns:
                dict    -   dictionary consisting of the HANA destination server options

            Raises:
                SDKException:
                    if failed to get HANA clients

                    if no client exits on commcell

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        webservice = self._commcell_object._services[&#39;RESTORE_OPTIONS&#39;] % (
            self._agent_object.agent_id
        )

        flag, response = self._commcell_object._cvpysdk_object.make_request(&#34;GET&#34;, webservice)

        destination_clients_dict = {}

        if flag:
            if response.json():
                if &#39;genericEntityList&#39; in response.json():
                    generic_entity_list = response.json()[&#39;genericEntityList&#39;]

                    for client_entity in generic_entity_list:
                        clients_dict = {
                            client_entity[&#39;clientName&#39;].lower(): {
                                &#34;clientId&#34;: client_entity[&#39;clientId&#39;]
                            }
                        }
                        destination_clients_dict.update(clients_dict)
                elif &#39;error&#39; in response.json():
                    if &#39;errorMessage&#39; in response.json()[&#39;error&#39;]:
                        error_message = response.json()[&#39;error&#39;][&#39;errorMessage&#39;]
                        raise SDKException(&#39;Client&#39;, &#39;102&#39;, error_message)
                    else:
                        raise SDKException(&#39;Client&#39;, &#39;102&#39;, &#39;No client exists on commcell&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        webservice = self._commcell_object._services[&#39;GET_ALL_INSTANCES&#39;] % (
            destination_clients_dict[destination_client_name][&#39;clientId&#39;]
        )

        flag, response = self._commcell_object._cvpysdk_object.make_request(&#34;GET&#34;, webservice)

        if flag:
            if response.json():
                if &#39;instanceProperties&#39; in response.json():
                    for instance in response.json()[&#39;instanceProperties&#39;]:
                        instances_dict = {
                            instance[&#39;instance&#39;][&#39;instanceName&#39;].lower(): {
                                &#34;clientId&#34;: instance[&#39;instance&#39;][&#39;clientId&#39;],
                                &#34;instanceId&#34;: instance[&#39;instance&#39;][&#39;instanceId&#39;],
                                &#34;destHANAClient&#34;: instance[&#39;saphanaInstance&#39;][
                                    &#39;DBInstances&#39;][0][&#39;clientName&#39;]
                            }
                        }
                        self.destination_instances_dict.update(instances_dict)
                elif &#39;error&#39; in response.json():
                    if &#39;errorMessage&#39; in response.json()[&#39;error&#39;]:
                        error_message = response.json()[&#39;error&#39;][&#39;errorMessage&#39;]
                        raise SDKException(&#39;Instance&#39;, &#39;102&#39;, error_message)
                    else:
                        raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;No Instance exists on commcell&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def restore(
            self,
            pseudo_client,
            instance,
            backupset_name=&#34;default&#34;,
            backup_prefix=None,
            point_in_time=None,
            initialize_log_area=False,
            use_hardware_revert=False,
            clone_env=False,
            check_access=True,
            destination_instance_dir=None,
            ignore_delta_backups=True,
            no_of_streams=2,
            catalog_time=None):
        &#34;&#34;&#34;Restores the databases specified in the input paths list.

            Args:
                pseudo_client               (str)   --  HANA client to restore the database at

                instance                    (str)   --  destination instance to restore the db at

                backupset_name              (str)   --  backupset name of the instance to be
                                                            restored. If the instance is a single
                                                            DB instance then the backupset name is
                                                            ``default``.
                    default: default

                backup_prefix               (str)   --  prefix of the backup job
                    default: None

                point_in_time               (str)   --  time to which db should be restored to
                    default: None

                initialize_log_area         (bool)  --  boolean to specify whether to initialize
                                                            the new log area after restore
                    default: False

                use_hardware_revert         (bool)  --  boolean to specify whether to do a
                                                            hardware revert in restore
                    default: False

                clone_env                   (bool)  --  boolean to specify whether the database
                                                            should be cloned or not
                    default: False

                check_access                (bool)  --  check access during restore or not
                    default: True

                destination_instance_dir    (str)   --  HANA data directory for snap cross instance
                                                            restore or cross machine restores
                    default: None

                ignore_delta_backups        (bool)  --  whether to ignore delta backups during
                                                            restore or not
                    default: True

                no_of_streams               (int)   --  number of streams to be used for restore

                    default: 2

                catalog_time                (str)   --  catalog time to which should be used to restore

                    default: None

            Returns:
                object  -   instance of the Job class for this restore job

            Raises:
                SDKException:
                    if instance is not a string or object

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        if not isinstance(instance, (str, Instance)):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        request_json = self._restore_request_json(
            pseudo_client,
            instance,
            backupset_name,
            backup_prefix,
            point_in_time,
            initialize_log_area,
            use_hardware_revert,
            clone_env,
            check_access,
            destination_instance_dir,
            ignore_delta_backups,
            no_of_streams,
            catalog_time
        )

        return self._process_restore_response(request_json)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.instance.Instance" href="../instance.html#cvpysdk.instance.Instance">Instance</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.instances.hanainstance.SAPHANAInstance.db_instance_client"><code class="name">var <span class="ident">db_instance_client</span></code></dt>
<dd>
<div class="desc"><p>Returns the client name of the HANA instance</p>
<h2 id="returns">Returns</h2>
<p>db instance client name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/hanainstance.py#L116-L124" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def db_instance_client(self):
    &#34;&#34;&#34;
    Returns the client name of the HANA instance

    Returns:
        db instance client name
    &#34;&#34;&#34;
    return self._properties[&#39;saphanaInstance&#39;][&#39;DBInstances&#39;][0]</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.hanainstance.SAPHANAInstance.hdb_user_storekey"><code class="name">var <span class="ident">hdb_user_storekey</span></code></dt>
<dd>
<div class="desc"><p>Returns the hdb user store key of the HANA instance</p>
<h2 id="returns">Returns</h2>
<p>hdb user store key</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/hanainstance.py#L126-L134" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def hdb_user_storekey(self):
    &#34;&#34;&#34;
    Returns the hdb user store key of the HANA instance

    Returns:
        hdb user store key
    &#34;&#34;&#34;
    return self._properties[&#39;saphanaInstance&#39;][&#39;hdbuserstorekey&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.hanainstance.SAPHANAInstance.instance_db_username"><code class="name">var <span class="ident">instance_db_username</span></code></dt>
<dd>
<div class="desc"><p>Returns the username of the HANA instance database</p>
<h2 id="returns">Returns</h2>
<p>instance db username</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/hanainstance.py#L106-L114" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def instance_db_username(self):
    &#34;&#34;&#34;
    Returns the username of the HANA instance database

    Returns:
        instance db username
    &#34;&#34;&#34;
    return self._properties[&#39;saphanaInstance&#39;][&#39;dbUser&#39;][&#39;userName&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.hanainstance.SAPHANAInstance.instance_number"><code class="name">var <span class="ident">instance_number</span></code></dt>
<dd>
<div class="desc"><p>Returns the instance number of the HANA instance</p>
<h2 id="returns">Returns</h2>
<p>instance number</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/hanainstance.py#L86-L94" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def instance_number(self):
    &#34;&#34;&#34;
    Returns the instance number of the HANA instance

    Returns:
        instance number
    &#34;&#34;&#34;
    return self._properties[&#39;saphanaInstance&#39;][&#39;dbInstanceNumber&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.hanainstance.SAPHANAInstance.sps_version"><code class="name">var <span class="ident">sps_version</span></code></dt>
<dd>
<div class="desc"><p>Returns the sps version of the HANA instance</p>
<h2 id="returns">Returns</h2>
<p>sps version</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/hanainstance.py#L76-L84" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def sps_version(self):
    &#34;&#34;&#34;
    Returns the sps version of the HANA instance

    Returns:
        sps version
    &#34;&#34;&#34;
    return self._properties[&#39;saphanaInstance&#39;][&#39;spsVersion&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.hanainstance.SAPHANAInstance.sql_location_directory"><code class="name">var <span class="ident">sql_location_directory</span></code></dt>
<dd>
<div class="desc"><p>Returns the isql location directory of the HANA instance</p>
<h2 id="returns">Returns</h2>
<p>SQL location directory</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/hanainstance.py#L96-L104" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def sql_location_directory(self):
    &#34;&#34;&#34;
    Returns the isql location directory of the HANA instance

    Returns:
        SQL location directory
    &#34;&#34;&#34;
    return self._properties[&#39;saphanaInstance&#39;][&#39;hdbsqlLocationDirectory&#39;]</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.instances.hanainstance.SAPHANAInstance.restore"><code class="name flex">
<span>def <span class="ident">restore</span></span>(<span>self, pseudo_client, instance, backupset_name='default', backup_prefix=None, point_in_time=None, initialize_log_area=False, use_hardware_revert=False, clone_env=False, check_access=True, destination_instance_dir=None, ignore_delta_backups=True, no_of_streams=2, catalog_time=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the databases specified in the input paths list.</p>
<h2 id="args">Args</h2>
<p>pseudo_client
(str)
&ndash;
HANA client to restore the database at</p>
<p>instance
(str)
&ndash;
destination instance to restore the db at</p>
<p>backupset_name
(str)
&ndash;
backupset name of the instance to be
restored. If the instance is a single
DB instance then the backupset name is
<code>default</code>.
default: default</p>
<p>backup_prefix
(str)
&ndash;
prefix of the backup job
default: None</p>
<p>point_in_time
(str)
&ndash;
time to which db should be restored to
default: None</p>
<p>initialize_log_area
(bool)
&ndash;
boolean to specify whether to initialize
the new log area after restore
default: False</p>
<p>use_hardware_revert
(bool)
&ndash;
boolean to specify whether to do a
hardware revert in restore
default: False</p>
<p>clone_env
(bool)
&ndash;
boolean to specify whether the database
should be cloned or not
default: False</p>
<p>check_access
(bool)
&ndash;
check access during restore or not
default: True</p>
<p>destination_instance_dir
(str)
&ndash;
HANA data directory for snap cross instance
restore or cross machine restores
default: None</p>
<p>ignore_delta_backups
(bool)
&ndash;
whether to ignore delta backups during
restore or not
default: True</p>
<p>no_of_streams
(int)
&ndash;
number of streams to be used for restore</p>
<pre><code>default: 2
</code></pre>
<p>catalog_time
(str)
&ndash;
catalog time to which should be used to restore</p>
<pre><code>default: None
</code></pre>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if instance is not a string or object</p>
<pre><code>if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/hanainstance.py#L400-L496" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore(
        self,
        pseudo_client,
        instance,
        backupset_name=&#34;default&#34;,
        backup_prefix=None,
        point_in_time=None,
        initialize_log_area=False,
        use_hardware_revert=False,
        clone_env=False,
        check_access=True,
        destination_instance_dir=None,
        ignore_delta_backups=True,
        no_of_streams=2,
        catalog_time=None):
    &#34;&#34;&#34;Restores the databases specified in the input paths list.

        Args:
            pseudo_client               (str)   --  HANA client to restore the database at

            instance                    (str)   --  destination instance to restore the db at

            backupset_name              (str)   --  backupset name of the instance to be
                                                        restored. If the instance is a single
                                                        DB instance then the backupset name is
                                                        ``default``.
                default: default

            backup_prefix               (str)   --  prefix of the backup job
                default: None

            point_in_time               (str)   --  time to which db should be restored to
                default: None

            initialize_log_area         (bool)  --  boolean to specify whether to initialize
                                                        the new log area after restore
                default: False

            use_hardware_revert         (bool)  --  boolean to specify whether to do a
                                                        hardware revert in restore
                default: False

            clone_env                   (bool)  --  boolean to specify whether the database
                                                        should be cloned or not
                default: False

            check_access                (bool)  --  check access during restore or not
                default: True

            destination_instance_dir    (str)   --  HANA data directory for snap cross instance
                                                        restore or cross machine restores
                default: None

            ignore_delta_backups        (bool)  --  whether to ignore delta backups during
                                                        restore or not
                default: True

            no_of_streams               (int)   --  number of streams to be used for restore

                default: 2

            catalog_time                (str)   --  catalog time to which should be used to restore

                default: None

        Returns:
            object  -   instance of the Job class for this restore job

        Raises:
            SDKException:
                if instance is not a string or object

                if response is empty

                if response is not success

    &#34;&#34;&#34;
    if not isinstance(instance, (str, Instance)):
        raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

    request_json = self._restore_request_json(
        pseudo_client,
        instance,
        backupset_name,
        backup_prefix,
        point_in_time,
        initialize_log_area,
        use_hardware_revert,
        clone_env,
        check_access,
        destination_instance_dir,
        ignore_delta_backups,
        no_of_streams,
        catalog_time
    )

    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.instance.Instance" href="../instance.html#cvpysdk.instance.Instance">Instance</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.instance.Instance.backupsets" href="../instance.html#cvpysdk.instance.Instance.backupsets">backupsets</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.browse" href="../instance.html#cvpysdk.instance.Instance.browse">browse</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.credentials" href="../instance.html#cvpysdk.instance.Instance.credentials">credentials</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.find" href="../instance.html#cvpysdk.instance.Instance.find">find</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.instance_id" href="../instance.html#cvpysdk.instance.Instance.instance_id">instance_id</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.instance_name" href="../instance.html#cvpysdk.instance.Instance.instance_name">instance_name</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.name" href="../instance.html#cvpysdk.instance.Instance.name">name</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.properties" href="../instance.html#cvpysdk.instance.Instance.properties">properties</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.refresh" href="../instance.html#cvpysdk.instance.Instance.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.subclients" href="../instance.html#cvpysdk.instance.Instance.subclients">subclients</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.update_properties" href="../instance.html#cvpysdk.instance.Instance.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.instances" href="index.html">cvpysdk.instances</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.instances.hanainstance.SAPHANAInstance" href="#cvpysdk.instances.hanainstance.SAPHANAInstance">SAPHANAInstance</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.instances.hanainstance.SAPHANAInstance.db_instance_client" href="#cvpysdk.instances.hanainstance.SAPHANAInstance.db_instance_client">db_instance_client</a></code></li>
<li><code><a title="cvpysdk.instances.hanainstance.SAPHANAInstance.hdb_user_storekey" href="#cvpysdk.instances.hanainstance.SAPHANAInstance.hdb_user_storekey">hdb_user_storekey</a></code></li>
<li><code><a title="cvpysdk.instances.hanainstance.SAPHANAInstance.instance_db_username" href="#cvpysdk.instances.hanainstance.SAPHANAInstance.instance_db_username">instance_db_username</a></code></li>
<li><code><a title="cvpysdk.instances.hanainstance.SAPHANAInstance.instance_number" href="#cvpysdk.instances.hanainstance.SAPHANAInstance.instance_number">instance_number</a></code></li>
<li><code><a title="cvpysdk.instances.hanainstance.SAPHANAInstance.restore" href="#cvpysdk.instances.hanainstance.SAPHANAInstance.restore">restore</a></code></li>
<li><code><a title="cvpysdk.instances.hanainstance.SAPHANAInstance.sps_version" href="#cvpysdk.instances.hanainstance.SAPHANAInstance.sps_version">sps_version</a></code></li>
<li><code><a title="cvpysdk.instances.hanainstance.SAPHANAInstance.sql_location_directory" href="#cvpysdk.instances.hanainstance.SAPHANAInstance.sql_location_directory">sql_location_directory</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>