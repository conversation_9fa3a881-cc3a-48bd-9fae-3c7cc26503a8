<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.alert API documentation</title>
<meta name="description" content="Main file for performing alert operations …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.alert</code></h1>
</header>
<section id="section-intro">
<p>Main file for performing alert operations.</p>
<p>Alerts and Alert are 2 classes defined in this file.</p>
<p>Alerts: Class for representing all the Alerts</p>
<p>Alert: Class for a single alert selected</p>
<h2 id="alerts">Alerts</h2>
<p><strong>init</strong>(commcell_object)
&ndash;
initialise object of Alerts class associated with
the specified commcell</p>
<p><strong>str</strong>()
&ndash;
returns all the alerts associated with the commcell</p>
<p><strong>repr</strong>()
&ndash;
returns the string for the instance of the Alerts class</p>
<p><strong>len</strong>()
&ndash;
returns the number of alerts configured on the Commcell</p>
<p><strong>getitem</strong>()
&ndash;
returns the name of the alert for the given alert Id or the
details for the given alert name</p>
<p>_get_alerts()
&ndash;
gets all the alerts associated with the commcell specified</p>
<p>_get_entities()
&ndash;
returns the list of associations for an alert</p>
<p>_get_alert_json()
&ndash;
returns the dict/json required to create an alert</p>
<p>get_alert_sender()
&ndash; returns the mail sender name as set in the Email server</p>
<p>create_alert(alert_name)
&ndash;
returns the instance of Alert class for created alert</p>
<p>has_alert(alert_name)
&ndash;
checks whether the alert exists or not</p>
<p>get(alert_name)
&ndash;
returns the alert class object of the input alert name</p>
<p>delete(alert_name)
&ndash;
removes the alerts from the commcell of the specified alert</p>
<p>console_alerts()
&ndash;
returns the list of all console alerts</p>
<p>console_alert()
&ndash; returns console alert details for a console alert with given livefeedid</p>
<p>refresh()
&ndash;
refresh the alerts associated with the commcell</p>
<h2 id="alerts-attributes">Alerts Attributes</h2>
<pre><code>**all_alerts**              --  returns the dict of all the alerts associated
with the commcell and their information such as name, id and category
</code></pre>
<h2 id="alert">Alert</h2>
<p><strong>init</strong>(commcell_object,
alert_name,
alert_id=None)
&ndash;
initialise object of alert with the specified commcell name
and id, and associated to the specified commcell</p>
<p><strong>repr</strong>()
&ndash;
return the alert name with description and category,
the alert is associated with</p>
<p>_get_alert_id()
&ndash;
method to get the alert id, if not specified in <strong>init</strong></p>
<p>_get_alert_properties()
&ndash;
get the properties of this alert</p>
<p>_get_alert_category()
&ndash;
return the category of the alert</p>
<p>_modify_alert_properties
&ndash;
modifies the alert properties</p>
<p>alert_name(name)
&ndash;
sets the alert name</p>
<p>alert_severity(severity)
&ndash;
sets the alert severity</p>
<p>notification_types(list)
&ndash;
sets the notifications types</p>
<p>entities(entities_dict)
&ndash;
sets the entities/associations</p>
<p>enable()
&ndash;
enables the alert</p>
<p>disable()
&ndash;
disables the alert</p>
<p>enable_notification_type()
&ndash;
enables notification type of alert</p>
<p>disable_notification_type()
&ndash;
disables notification type of alert</p>
<p>refresh()
&ndash;
refresh the properties of the Alert</p>
<h2 id="alert-attributes">Alert Attributes</h2>
<pre><code>**alert_id**                --  returns the id of an alert

**alert_name**              --  gets the name of an alert

**alert_type**              --  returns the type of an alert

**alert_category**          --  returns the category of an alert

**alert_severity**          --  returns the severity of an alert

**alert_criteria**          --  returns the criteria of an alert

**notification_types**      --  returns the notification types of an alert

**description**             --  returns the description of an alert

**users_list**              --  returns the list of users associated with the alert

**user_group_list**         --  returns the list of user groups associated with the alert

**entities**                --  returns the list of entities associated with an alert

**email_recipients**        --  returns the list of email recipients associated to the alert
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/alert.py#L1-L1311" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Main file for performing alert operations.

Alerts and Alert are 2 classes defined in this file.

Alerts: Class for representing all the Alerts

Alert: Class for a single alert selected


Alerts:
    __init__(commcell_object)   --  initialise object of Alerts class associated with
    the specified commcell

    __str__()                   --  returns all the alerts associated with the commcell

    __repr__()                  --  returns the string for the instance of the Alerts class

    __len__()                   --  returns the number of alerts configured on the Commcell

    __getitem__()               --  returns the name of the alert for the given alert Id or the
    details for the given alert name

    _get_alerts()               --  gets all the alerts associated with the commcell specified

    _get_entities()             --  returns the list of associations for an alert

    _get_alert_json()           --  returns the dict/json required to create an alert

    get_alert_sender()          -- returns the mail sender name as set in the Email server

    create_alert(alert_name)    --  returns the instance of Alert class for created alert

    has_alert(alert_name)       --  checks whether the alert exists or not

    get(alert_name)             --  returns the alert class object of the input alert name

    delete(alert_name)          --  removes the alerts from the commcell of the specified alert

    console_alerts()            --  returns the list of all console alerts

    console_alert()             -- returns console alert details for a console alert with given livefeedid

    refresh()                   --   refresh the alerts associated with the commcell

Alerts Attributes
------------------
    **all_alerts**              --  returns the dict of all the alerts associated
    with the commcell and their information such as name, id and category


Alert:
    __init__(commcell_object,
             alert_name,
             alert_id=None)       --  initialise object of alert with the specified commcell name
    and id, and associated to the specified commcell

    __repr__()                    --  return the alert name with description and category,
    the alert is associated with

    _get_alert_id()               --  method to get the alert id, if not specified in __init__

    _get_alert_properties()       --  get the properties of this alert

    _get_alert_category()         --  return the category of the alert

    _modify_alert_properties      --  modifies the alert properties

    alert_name(name)              --  sets the alert name

    alert_severity(severity)      --  sets the alert severity

    notification_types(list)      --  sets the notifications types

    entities(entities_dict)       --  sets the entities/associations

    enable()                      --  enables the alert

    disable()                     --  disables the alert

    enable_notification_type()    --  enables notification type of alert

    disable_notification_type()   --  disables notification type of alert

    refresh()                     --  refresh the properties of the Alert

Alert Attributes
------------------
    **alert_id**                --  returns the id of an alert

    **alert_name**              --  gets the name of an alert

    **alert_type**              --  returns the type of an alert

    **alert_category**          --  returns the category of an alert

    **alert_severity**          --  returns the severity of an alert

    **alert_criteria**          --  returns the criteria of an alert

    **notification_types**      --  returns the notification types of an alert

    **description**             --  returns the description of an alert

    **users_list**              --  returns the list of users associated with the alert

    **user_group_list**         --  returns the list of user groups associated with the alert

    **entities**                --  returns the list of entities associated with an alert

    **email_recipients**        --  returns the list of email recipients associated to the alert
&#34;&#34;&#34;

from __future__ import absolute_import
from __future__ import unicode_literals
import xml.etree.ElementTree as ET
from .exception import SDKException


class Alerts(object):
    &#34;&#34;&#34;Class for getting all the Alerts associated with the commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the Alerts class.

            Args:
                commcell_object (object)  --  instance of the Commcell class

            Returns:
                object - instance of the Alerts class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._ALERTS = commcell_object._services[&#39;GET_ALL_ALERTS&#39;]
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_
        self._alerts = None

        self._notification_types = {
            &#39;email&#39;: 1,
            &#39;snmp&#39;: 4,
            &#39;event viewer&#39;: 8,
            &#39;save to disk&#39;: 512,
            &#39;rss feeds&#39;: 1024,
            &#39;console alerts&#39;: 8192,
            &#39;scom&#39;: 32768,
            &#39;workflow&#39;: 65536,
            &#39;content indexing&#39;: 131072
        }
        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all alerts of the Commcell.

            Returns:
                str - string of all the alerts for a commcell
        &#34;&#34;&#34;
        representation_string = &#34;{:^5}\t{:^50}\t{:^80}\t{:^30}\n\n&#34;.format(
            &#39;S. No.&#39;, &#39;Alert&#39;, &#39;Description&#39;, &#39;Category&#39;
        )

        for index, alert_name in enumerate(self._alerts):
            alert_description = self._alerts[alert_name][&#39;description&#39;]
            alert_category = self._alerts[alert_name][&#39;category&#39;]
            sub_str = &#39;{:^5}\t{:50}\t{:80}\t{:30}\n&#39;.format(
                index + 1,
                alert_name,
                alert_description,
                alert_category
            )
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the Alerts class.&#34;&#34;&#34;
        return &#34;Alerts class instance for Commcell&#34;

    def __len__(self):
        &#34;&#34;&#34;Returns the number of the alerts configured on the Commcell.&#34;&#34;&#34;
        return len(self.all_alerts)

    def __getitem__(self, value):
        &#34;&#34;&#34;Returns the name of the alert for the given alert ID or
            the details of the alert for given alert Name.

            Args:
                value   (str / int)     --  Name or ID of the alert

            Returns:
                str     -   name of the alert, if the alert id was given

                dict    -   dict of details of the alert, if alert name was given

            Raises:
                IndexError:
                    no alert exists with the given Name / Id

        &#34;&#34;&#34;
        value = str(value)

        if value in self.all_alerts:
            return self.all_alerts[value]
        else:
            try:
                return list(filter(lambda x: x[1][&#39;id&#39;] == value, self.all_alerts.items()))[0][0]
            except IndexError:
                raise IndexError(&#39;No alert exists with the given Name / Id&#39;)

    def _get_alerts(self):
        &#34;&#34;&#34;Gets all the alerts associated with the commcell

            Returns:
                dict - consists of all alerts of the commcell
                    {
                         &#34;alert1_name&#34;: {
                             &#34;id&#34;: alert1_id,
                             &#34;category&#34;: alert1_category
                         },
                         &#34;alert2_name&#34;: {
                             &#34;id&#34;: alert2_id,
                             &#34;category&#34;: alert2_category
                         }
                    }

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._ALERTS)

        if flag:
            if response.json() and &#39;alertList&#39; in response.json():
                alerts_dict = {}

                for dictionary in response.json()[&#39;alertList&#39;]:
                    temp_dict = {}

                    temp_name = dictionary[&#39;alert&#39;][&#39;name&#39;].lower()
                    temp_id = str(dictionary[&#39;alert&#39;][&#39;id&#39;]).lower()
                    temp_description = dictionary[&#39;description&#39;].lower()
                    temp_category = dictionary[&#39;alertCategory&#39;][&#39;name&#39;].lower()

                    temp_dict[&#39;id&#39;] = temp_id
                    temp_dict[&#39;description&#39;] = temp_description
                    temp_dict[&#39;category&#39;] = temp_category

                    alerts_dict[temp_name] = temp_dict

                    del temp_dict

                return alerts_dict
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)


    def _get_entities(self, entities):
        &#34;&#34;&#34;Returns the list of entities associations for an alert

        Args:
            entities    (dict)  --  dictionary of entities for an alert

        Raise:
            SDKException:
                if entities is not an instance of dictionary

        Returns:
            list  -  a list of associations for an alert

        &#34;&#34;&#34;
        if not isinstance(entities, dict):
            raise SDKException(&#39;Alert&#39;, &#39;101&#39;)

        # policies not handled as

        entity_dict = {
            &#34;clients&#34;: {
                &#34;clientName&#34;: &#34;client_name&#34;,
                &#34;clientId&#34;: &#34;client_id&#34;,
                &#34;_type_&#34;: 3
            },
            &#34;client_groups&#34;: {
                &#34;clientGroupName&#34;: &#34;clientgroup_name&#34;,
                &#34;clientGroupId&#34;: &#34;clientgroup_id&#34;,
                &#34;_type_&#34;: 28
            },
            &#34;users&#34;: {
                &#34;userName&#34;: &#34;user_name&#34;,
                &#34;userId&#34;: &#34;user_id&#34;,
                &#34;_type_&#34;: 13
            },
            &#34;user_groups&#34;: {
                &#34;userGroupName&#34;: &#34;user_group_name&#34;,
                &#34;userGroupId&#34;: &#34;user_group_id&#34;,
                &#34;_type_&#34;: 15
            },
            &#34;disk_libraries&#34;: {
                &#34;libraryName&#34;: &#34;library_name&#34;,
                &#34;libraryId&#34;: &#34;library_id&#34;,
                &#34;_type_&#34;: 9
            },
            &#34;media_agents&#34;: {
                &#34;mediaAgentName&#34;: &#34;media_agent_name&#34;,
                &#34;mediaAgentId&#34;: &#34;media_agent_id&#34;,
                &#34;_type_&#34;: 11
            },
            &#34;storage_policies&#34;: {
                &#34;storagePolicyName&#34;: &#34;storage_policy_name&#34;,
                &#34;storagePolicyId&#34;: &#34;storage_policy_id&#34;,
                &#34;_type_&#34;: 17
            }
        }

        associations = []

        for entity, values in entities.items():
            if entity == &#34;entity_type_names&#34;:
                for value in values:
                    if value == &#34;ALL_CLIENT_GROUPS_ENTITY&#34;:
                        entity_type = 27
                    else:
                        entity_type = 2
                    temp_dict = {
                        &#34;entityTypeName&#34;: value,
                        &#34;_type_&#34;: entity_type
                    }
                    associations.append(temp_dict)
            else:
                entity_obj = getattr(self._commcell_object, entity)

                # this will allows us to loop through even for single item
                values = values.split() if not isinstance(values, list) else values

                for value in values:
                    temp_dict = entity_dict[entity].copy()
                    for name, entity_attr in temp_dict.items():
                        if name != &#34;_type_&#34;:
                            try: # to convert the string values to int types
                                temp_dict[name] = int(getattr(entity_obj.get(value), entity_attr))
                            except ValueError:
                                temp_dict[name] = getattr(entity_obj.get(value), entity_attr)
                    associations.append(temp_dict)

        return associations


    def _get_alert_json(self, alert_json):
        &#34;&#34;&#34;To form the json required to create an alert

        Args:
            alert_json    (dict)  --  a dictionary to create an alert

        Returns:
            dict  -  a constructed dictionary needed to create an alert
        &#34;&#34;&#34;
        alert_detail = {
            &#34;alertDetail&#34;: {
                &#34;alertType&#34;: alert_json.get(&#34;alert_type&#34;),
                &#34;notifType&#34;: [n_type for n_type in alert_json.get(&#34;notif_type&#34;, [8192])],
                &#34;notifTypeListOperationType&#34;: alert_json.get(&#34;notifTypeListOperationType&#34;, 0),
                &#34;alertSeverity&#34;: alert_json.get(&#34;alertSeverity&#34;, 0),
                &#34;EscnonGalaxyUserList&#34;:{
                    &#34;nonGalaxyUserOperationType&#34;: alert_json.get(&#34;nonGalaxyUserOperationType&#34;, 0)
                },
                &#34;locale&#34;:{
                    &#34;localeID&#34;:alert_json.get(&#34;localeID&#34;, 0)
                },
                &#34;EscUserList&#34;:{
                    &#34;userListOperationType&#34;:alert_json.get(&#34;userListOperationType&#34;, 0)
                },
                &#34;EscUserGroupList&#34;:{
                    &#34;userGroupListOperationType&#34;: alert_json.get(&#34;userGroupListOperationType&#34;, 0)
                },
                &#34;alertrule&#34;:{
                    &#34;alertName&#34;: alert_json.get(&#34;alert_name&#34;)
                },
                &#34;criteria&#34;:{
                    &#34;criteria&#34;: alert_json.get(&#34;criteria&#34;)
                },
                &#34;userList&#34;:{
                    &#34;userListOperationType&#34;:alert_json.get(&#34;userListOperationType&#34;, 0),
                    &#34;userList&#34;:[{&#34;userName&#34;:user} for user in alert_json.get(&#34;users&#34;, [&#34;admin&#34;])]
                },
                &#34;EntityList&#34;:{
                    &#34;associationsOperationType&#34;:alert_json.get(&#34;associationsOperationType&#34;, 0),
                    &#34;associations&#34;: self._get_entities(alert_json.get(&#34;entities&#34;, dict()))
                }
            }
        }

        # Check if paramsList is present or not
        if alert_json.get(&#34;paramsList&#34;):
            alert_detail[&#34;alertDetail&#34;][&#34;criteria&#34;][&#34;paramsList&#34;] = alert_json.get(&#34;paramsList&#34;)

        # Check if additonal mail recipents exist
        if alert_json.get(&#34;nonGalaxyList&#34;):
            alert_detail[&#34;alertDetail&#34;][&#34;nonGalaxyList&#34;] = alert_json.get(&#34;nonGalaxyList&#34;)

        if alert_json.get(&#34;user_groups&#34;):
            alert_detail[&#34;alertDetail&#34;][&#34;userGroupList&#34;] = {
                &#34;userGroupListOperationType&#34;:alert_json.get(&#34;userGroupListOperationType&#34;, 0),
                &#34;userGroupList&#34;:[
                    {
                        &#34;userGroupName&#34;:user_grp
                    } for user_grp in alert_json.get(&#34;user_groups&#34;)
                ]
            }

        return alert_detail

    def get_alert_sender(self):
        &#34;&#34;&#34;
            Returns the Alert Sender name
        &#34;&#34;&#34;
        get_alert = self._services[&#39;EMAIL_SERVER&#39;]
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, get_alert)
        if flag:
            if response.json():
                sender = response.json()[&#34;senderInfo&#34;][&#39;senderName&#39;]
                if not sender:
                    sender = response.json()[&#34;senderInfo&#34;][&#39;senderAddress&#39;]
                return sender
            else:
                raise SDKException(&#39;Alert&#39;, &#39;102&#39;, &#34;Failed to get sender address&#34;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)


    def create_alert(self, alert_dict):
        &#34;&#34;&#34;Creates a new Alert for CommCell

        Args:
            alert_dict    (dict)  --  dictionary required to create an alert

        Returns:
            object  -  instance of the Alert class for this new alert

        Raises:
            SDKException:
                if input argument is not an instance of dict

                if alert with given name already exists

                if failed to create an alert

                if response is not success

                if response is empty
        &#34;&#34;&#34;
        if not isinstance(alert_dict, dict):
            raise SDKException(&#39;Alert&#39;, &#39;101&#39;)

        # required alert json
        alert_json = self._get_alert_json(alert_dict)
        alert_name = alert_json[&#34;alertDetail&#34;][&#34;alertrule&#34;][&#34;alertName&#34;]
        if self.has_alert(alert_name):
            raise SDKException(&#39;Alert&#39;, &#39;102&#39;, &#39;Alert &#34;{0}&#34; already exists.&#39;.
                               format(alert_name))

        post_alert = self._services[&#39;GET_ALL_ALERTS&#39;]
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, post_alert, alert_json)

        if flag:
            if response.json():
                error_dict = response.json()[&#34;errorResp&#34;]
                error_code = str(error_dict[&#34;errorCode&#34;])

                if error_code == &#34;0&#34;:
                    self.refresh()
                    return self.get(alert_name)
                else:
                    error_message = &#34;&#34;

                    if &#39;errorMessage&#39; in error_dict:
                        error_message = error_dict[&#39;errorMessage&#39;]

                    if error_message:
                        raise SDKException(
                            &#39;Alert&#39;, &#39;102&#39;, &#39;Failed to create Alert\nError: &#34;{}&#34;&#39;.format(
                                error_message
                            )
                        )
                    else:
                        raise SDKException(
                            &#39;Alert&#39;, &#39;102&#39;, &#34;Failed to create Alert&#34;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)


    @property
    def all_alerts(self):
        &#34;&#34;&#34;Returns the dict of all the alerts configured on this commcell

            dict - consists of all alerts of the commcell
                    {
                         &#34;alert1_name&#34;: {
                             &#34;id&#34;: alert1_id,
                             &#34;category&#34;: alert1_category
                         },
                         &#34;alert2_name&#34;: {
                             &#34;id&#34;: alert2_id,
                             &#34;category&#34;: alert2_category
                         }
                    }
        &#34;&#34;&#34;
        return self._alerts

    def has_alert(self, alert_name):
        &#34;&#34;&#34;Checks if a alert exists for the commcell with the input alert name.

            Args:
                alert_name (str)  --  name of the alert

            Returns:
                bool - boolean output whether the alert exists for the commcell or not

            Raises:
                SDKException:
                    if type of the alert name argument is not string
        &#34;&#34;&#34;
        if not isinstance(alert_name, str):
            raise SDKException(&#39;Alert&#39;, &#39;101&#39;)

        return self._alerts and alert_name.lower() in self._alerts

    def get(self, alert_name):
        &#34;&#34;&#34;Returns a alert object of the specified alert name.

            Args:
                alert_name (str)  --  name of the alert

            Returns:
                object - instance of the Alert class for the given alert name

            Raises:
                SDKException:
                    if type of the alert name argument is not string

                    if no alert exists with the given name
        &#34;&#34;&#34;
        if not isinstance(alert_name, str):
            raise SDKException(&#39;Alert&#39;, &#39;101&#39;)
        else:
            alert_name = alert_name.lower()

            if self.has_alert(alert_name):
                return Alert(
                    self._commcell_object, alert_name,
                    self._alerts[alert_name][&#39;id&#39;],
                    self._alerts[alert_name][&#39;category&#39;]
                )

            raise SDKException(&#39;Alert&#39;, &#39;102&#39;, &#39;No Alert exists with name: {0}&#39;.format(alert_name))

    def console_alerts(self, page_number=1, page_count=1):
        &#34;&#34;&#34;Returns the console alerts from page_number to the number of pages asked for page_count

            Args:
                page_number (int)  --  page number to get the alerts from

                page_count  (int)  --  number of pages to get the alerts of

            Raises:
                SDKException:
                    if type of the page number and page count argument is not int

                    if response is empty

                    if response is not success

            Returns:
                str - String representation of console alerts if version is less than SP23
                object - json response object for console alerts if version greater than or equal to SP23
        &#34;&#34;&#34;
        if not (isinstance(page_number, int) and isinstance(page_count, int)):
            raise SDKException(&#39;Alert&#39;, &#39;101&#39;)

        console_alerts = self._services[&#39;GET_ALL_CONSOLE_ALERTS&#39;] % (
            page_number, page_count)

        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, console_alerts)

        if flag:
            if response.json() and &#39;totalNoOfAlerts&#39; in response.json():
                if self._commcell_object.commserv_version &gt;= 23:
                    return response.json()

                o_str = &#34;Total Console Alerts found: {0}&#34;.format(
                    response.json()[&#39;totalNoOfAlerts&#39;]
                )

                o_str += &#34;\n{:^5}\t{:^50}\t{:^50}\t{:^50}\n\n&#34;.format(
                    &#39;S. No.&#39;, &#39;Alert&#39;, &#39;Type&#39;, &#39;Criteria&#39;
                )

                for index, dictionary in enumerate(response.json()[&#39;feedsList&#39;]):
                    o_str += &#39;{:^5}\t{:50}\t{:^50}\t{:^50}\n&#39;.format(
                        index + 1,
                        dictionary[&#39;alertName&#39;],
                        dictionary[&#39;alertType&#39;],
                        dictionary[&#39;alertcriteria&#39;]
                    )

                return o_str
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def console_alert(self, live_feed_id):
        &#34;&#34;&#34;Returns the console console alert with given live_feed_id

            Args:
                live_feed_id (int)  --  Live feed ID of console alert to fetch

            Raises:
                SDKException:
                    if type of the live_feed_id argument is not int

                    if response is empty

                    if response is not success

            Returns:
                object - Console alert json object for given live_feed_id
        &#34;&#34;&#34;
        if not (isinstance(live_feed_id, int)):
            raise SDKException(&#39;Alert&#39;, &#39;101&#39;)

        console_alerts = self._services[&#39;GET_CONSOLE_ALERT&#39;] % (
            live_feed_id)

        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, console_alerts)

        if flag:
            if response and response.json() and &#39;description&#39; in response.json():
                return response.json()
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def delete(self, alert_name):
        &#34;&#34;&#34;Deletes the alert from the commcell.

            Args:
                alert_name (str)  --  name of the alert

            Raises:
                SDKException:
                    if type of the alert name argument is not string

                    if failed to delete the alert

                    if no alert exists with the given name
        &#34;&#34;&#34;
        if not isinstance(alert_name, str):
            raise SDKException(&#39;Alert&#39;, &#39;101&#39;)
        alert_name = alert_name.lower()

        if self.has_alert(alert_name):
            alert_id = self._alerts[alert_name][&#39;id&#39;]
            alert = self._services[&#39;ALERT&#39;] % (alert_id)

            flag, response = self._cvpysdk_object.make_request(
                &#39;DELETE&#39;, alert
            )

            if flag:
                if response.json():
                    if &#39;errorCode&#39; in response.json():
                        if response.json()[&#39;errorCode&#39;] == 0:
                            # initialize the alerts again
                            # to refresh with the latest alerts
                            self.refresh()
                        else:
                            raise SDKException(&#39;Alert&#39;, &#39;102&#39;, response.json()[&#39;errorMessage&#39;])
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                response_string = self._update_response_(response.text)
                exception_message = &#39;Failed to delete alert\nError: &#34;{0}&#34;&#39;.format(
                    response_string
                )

                raise SDKException(&#39;Alert&#39;, &#39;102&#39;, exception_message)
        else:
            raise SDKException(
                &#39;Alert&#39;, &#39;102&#39;, &#39;No alert exists with name: {0}&#39;.format(alert_name)
            )

    def refresh(self):
        &#34;&#34;&#34;Refresh the alerts associated with the Commcell.&#34;&#34;&#34;
        self._alerts = self._get_alerts()


class Alert(object):
    &#34;&#34;&#34;Class for performing operations for a specific alert.&#34;&#34;&#34;

    def __init__(self, commcell_object, alert_name, alert_id=None, alert_category=None):
        &#34;&#34;&#34;Initialise the Alert class instance.

            Args:
                commcell_object (object)  --  instance of the Commcell class

                alert_name      (str)     --  name of the alert

                alert_id        (str)     --  id of the alert
                    default: None

                alert_category  (str)     --  name of the alert category
                    default: None

            Returns:
                object - instance of the ALert class
        &#34;&#34;&#34;

        self._commcell_object = commcell_object
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_

        self._alerts_obj = Alerts(self._commcell_object)
        self._alert_name = alert_name.lower()
        self._alert_detail = None
        if alert_id:
            self._alert_id = str(alert_id)
        else:
            self._alert_id = self._get_alert_id()

        if alert_category:
            self._alert_category = alert_category
        else:
            self._alert_category = self._get_alert_category()

        self._ALERT = self._services[&#39;ALERT&#39;] % (self.alert_id)
        self._all_notification_types = {
            &#39;email&#39;: 1,
            &#39;snmp&#39;: 4,
            &#39;event viewer&#39;: 8,
            &#39;save to disk&#39;: 512,
            &#39;rss feeds&#39;: 1024,
            &#39;console alerts&#39;: 8192,
            &#39;scom&#39;: 32768,
            &#39;workflow&#39;: 65536,
            &#39;content indexing&#39;: 131072
        }

        self._alert_severity = None
        self._alert_type = None
        self._alert_type_id = None
        self._description = None
        self._criteria = []
        self._entities_list = []
        self._users_list = []
        self._user_group_list = []
        self._notification_types = []

        self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        representation_string = &#39;Alert class instance for Alert: &#34;{0}&#34;, Alert Type: &#34;{1}&#34;&#39;
        return representation_string.format(self.alert_name, self._alert_type)

    def _get_alert_id(self):
        &#34;&#34;&#34;Gets the alert id associated with this alert.

            Returns:
                str - id associated with this alert
        &#34;&#34;&#34;
        return self._alerts_obj.get(self.alert_name).alert_id

    def _get_alert_category(self):
        &#34;&#34;&#34;Gets the alert category associated with this alert.

            Returns:
                str - alert category name associated with this alert
        &#34;&#34;&#34;
        return self._alerts_obj.get(self.alert_name).alert_category

    def _get_alert_properties(self):
        &#34;&#34;&#34;Gets the alert properties of this alert.

            Returns:
                dict - dictionary consisting of the properties of this alert

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._ALERT)

        if flag:
            if response.json() and &#39;alertDetail&#39; in response.json().keys():
                self._alert_detail = response.json()[&#39;alertDetail&#39;]
                if &#39;alertSeverity&#39; in self._alert_detail:
                    self._alert_severity = self._alert_detail[&#39;alertSeverity&#39;]

                if &#39;criteria&#39; in self._alert_detail:
                    criterias = self._alert_detail[&#39;criteria&#39;]
                    for criteria in criterias:
                        self._criteria.append({
                            &#39;criteria_value&#39;: criteria[&#39;value&#39;] if &#39;value&#39; in criteria else None,
                            &#39;criteria_id&#39;:
                            str(criteria[&#39;criteriaId&#39;]) if &#39;criteriaId&#39; in criteria else None,
                            &#39;esclation_level&#39;:
                            criteria[&#39;esclationLevel&#39;] if &#39;esclationLevel&#39; in criteria else None
                        })

                if &#39;alert&#39; in self._alert_detail:
                    alert = self._alert_detail[&#39;alert&#39;]

                    if &#39;description&#39; in alert:
                        self._description = alert[&#39;description&#39;]

                    if &#39;alertType&#39; in alert and &#39;name&#39; in alert[&#39;alertType&#39;]:
                        self._alert_type = alert[&#39;alertType&#39;][&#39;name&#39;]
                        self._alert_type_id = alert[&#39;alertType&#39;][&#39;id&#39;]

                if &#39;xmlEntityList&#39; in self._alert_detail:
                    entity_xml = ET.fromstring(self._alert_detail[&#39;xmlEntityList&#39;])
                    self._entities_list = []
                    for entity in entity_xml.findall(&#34;associations&#34;):
                        if entity.find(&#34;flags&#34;) is not None:
                            if entity.find(&#34;flags&#34;).attrib[&#34;exclude&#34;] != &#34;1&#34;:
                                self._entities_list.append(entity.attrib)
                        else:
                            self._entities_list.append(entity.attrib)

                # to convert the ids to int type
                for entity in self._entities_list:
                    for key, value in entity.items():
                        try:
                            entity[key] = int(value) # to change the ids to type int
                        except ValueError:
                            pass

                if &#39;regularNotifications&#39; in self._alert_detail:
                    self._notification_types = self._alert_detail[&#34;regularNotifications&#34;]

                if &#39;userList&#39; in self._alert_detail:
                    self._users_list = [user[&#39;name&#39;] for user in self._alert_detail[&#39;userList&#39;]]
                else:
                    self._users_list = []

                if &#39;userGroupList&#39; in self._alert_detail:
                    self._user_group_list = [grp[&#39;name&#39;] for grp in self._alert_detail[&#39;userGroupList&#39;]]
                else:
                    self._user_group_list = []

                self._email_recipients = []
                if &#39;nonGalaxyUserList&#39; in self._alert_detail:
                    self._email_recipients = [email[&#39;name&#39;] for email in self._alert_detail[&#39;nonGalaxyUserList&#39;]]

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _modify_alert_properties(self):
        &#34;&#34;&#34;
        modifies the properties of an alert
        Exception:
            if modification of the alert failed
        &#34;&#34;&#34;

        request_json = {
            &#34;alertDetail&#34;:{
                &#34;alertDetail&#34;: {
                    &#34;alertType&#34;: self._alert_type_id, # this should not be changed
                    &#34;notifType&#34;: self._notification_types,
                    &#34;alertSeverity&#34;: self._alert_severity,
                    &#34;alertrule&#34;: {
                        &#34;alertName&#34;: self._alert_name
                    },
                    &#34;criteria&#34;: {
                        &#34;criteria&#34;: int(self._criteria[0][&#39;criteria_id&#39;])
                    },
                    &#34;userList&#34;: {
                        &#34;userListOperationType&#34;: 1,
                        &#34;userList&#34;: [{&#34;userName&#34;: user} for user in self._users_list]
                    },
                    &#34;userGroupList&#34;: {
                        &#34;userGroupListOperationType&#34;: 1,
                        &#34;userGroupList&#34;: [{&#34;userGroupName&#34;: user} for user in self._user_group_list]
                    },
                    &#34;nonGalaxyList&#34;: {
                        &#34;nonGalaxyUserList&#34;: [{&#34;nonGalaxyUser&#34;: email} for email in self._email_recipients]
                    },
                    &#34;EntityList&#34;: {
                        &#34;associations&#34;: self._entities_list
                    }
                }
            }
        }

        modify_alert = self._services[&#39;MODIFY_ALERT&#39;] % (self.alert_id)
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, modify_alert, request_json
        )

        if flag:
            if response.json():
                error_code = str(response.json()[&#39;errorCode&#39;])
                if error_code == &#39;0&#39;:
                    self.refresh()
                    return
                else:
                    o_str = &#39;Failed to update properties of Alert\nError: &#34;{0}&#34;&#39;
                    o_str = o_str.format(response.json()[&#39;errorMessage&#39;])
                    raise SDKException(&#39;Alert&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)



    @property
    def name(self):
        &#34;&#34;&#34;Returns the Alert display name &#34;&#34;&#34;
        return self._alert_detail[&#39;alert&#39;][&#39;alert&#39;][&#39;name&#39;]

    @property
    def alert_name(self):
        &#34;&#34;&#34;Treats the alert name as a read-only attribute.&#34;&#34;&#34;
        return self._alert_name

    @alert_name.setter
    def alert_name(self, name):
        &#34;&#34;&#34;Modifies the Alert name&#34;&#34;&#34;
        if not isinstance(name, str):
            raise SDKException(&#39;Alert&#39;, &#39;101&#39;)

        self._alert_name = name
        self._modify_alert_properties()

    @property
    def alert_id(self):
        &#34;&#34;&#34;Treats the alert id as a read-only attribute.&#34;&#34;&#34;
        return self._alert_id

    @property
    def alert_type(self):
        &#34;&#34;&#34;Treats the alert type as a read-only attribute.&#34;&#34;&#34;
        return self._alert_type

    @property
    def alert_category(self):
        &#34;&#34;&#34;Treats the alert category type id as a read-only attribute. &#34;&#34;&#34;
        return self._alert_category.title()

    @property
    def alert_severity(self):
        &#34;&#34;&#34;Treats the alert severity type id as a read-only attribute. &#34;&#34;&#34;
        return self._alert_severity

    @alert_severity.setter
    def alert_severity(self, severity):
        &#34;&#34;&#34;Modifies the Alert severity&#34;&#34;&#34;
        if not isinstance(severity, int):
            raise SDKException(&#39;Alert&#39;, &#39;101&#39;)

        self._alert_severity = severity
        self._modify_alert_properties()

    @property
    def alert_criteria(self):
        &#34;&#34;&#34;Treats the alert criteria as a read-only attribute.&#34;&#34;&#34;
        return &#34;\n&#34;.join([criteria[&#34;criteria_value&#34;] for criteria in self._criteria])

    @property
    def notification_types(self):
        &#34;&#34;&#34;Treats the alert notif types as a read-only attribute.&#34;&#34;&#34;
        notif_types = []
        for notif, notif_id in self._all_notification_types.items():
            if notif_id in self._notification_types:
                notif_types.append((notif_id, notif.title()))
        return notif_types

    @notification_types.setter
    def notification_types(self, notif_types):
        &#34;&#34;&#34;Treats the alert notif types as a read-only attribute.&#34;&#34;&#34;
        if not isinstance(notif_types, list):
            raise SDKException(&#39;Alert&#39;, &#39;102&#39;)
        try:
            ntypes = [self._all_notification_types[ntype.lower()] for ntype in notif_types]
        except KeyError as notif_type:
            raise SDKException(
                &#39;Alert&#39;,
                &#39;102&#39;,
                &#39;No notification type with name {0} exists&#39;.format(notif_type)
            )
        self._notification_types = ntypes
        self._modify_alert_properties()

    @property
    def entities(self):
        &#34;&#34;&#34;Treats the alert associations as a read-only attribute. &#34;&#34;&#34;
        return self._entities_list

    @entities.setter
    def entities(self, entity_json):
        &#34;&#34;&#34;Modifies the Alert entities&#34;&#34;&#34;
        if not isinstance(entity_json, dict):
            raise SDKException(&#39;Alert&#39;, &#39;101&#39;)

        self._entities_list = self._alerts_obj._get_entities(entity_json)
        self._modify_alert_properties()

    @property
    def email_recipients(self):
        &#34;&#34;&#34;returns the email recipients associated to the alert&#34;&#34;&#34;
        return self._email_recipients

    @email_recipients.setter
    def email_recipients(self, email_recipients):
        &#34;&#34;&#34;Modifies the email_recipients for the alert&#34;&#34;&#34;
        self._email_recipients.extend(email_recipients)
        self._modify_alert_properties()

    @property
    def description(self):
        &#34;&#34;&#34;Treats the alert description as a read-only attribute.&#34;&#34;&#34;
        return self._description

    @description.setter
    def description(self, description):
        &#34;&#34;&#34;Modifies the Alert description&#34;&#34;&#34;
        self._description = description
        self._modify_alert_properties()
    
    @property
    def users_list(self):
        &#34;&#34;&#34;Treats the users list as a read-only attribute.&#34;&#34;&#34;
        return self._users_list

    @users_list.setter
    def users_list(self, users_list):
        &#34;&#34;&#34;Modifies the users list&#34;&#34;&#34;
        self._users_list = users_list
        self._modify_alert_properties()
    
    @property
    def user_group_list(self):
        &#34;&#34;&#34;Treats the user group list as a read-only attribute.&#34;&#34;&#34;
        return self._user_group_list

    @user_group_list.setter
    def user_group_list(self, user_group_list):
        &#34;&#34;&#34;Modifies the user group list&#34;&#34;&#34;
        self._user_group_list = user_group_list
        self._modify_alert_properties()

    def enable_notification_type(self, alert_notification_type):
        &#34;&#34;&#34;Enable the notification type.

            Args:
                alert_notification_type (str)  --  alert notification to enable

            Raises:
                SDKException:
                    if type of alert notification argument is not string

                    if failed to enable notification type

                    if response is empty

                    if response is not success

                    if no notification type exists with the name provided
        &#34;&#34;&#34;
        if not isinstance(alert_notification_type, str):
            raise SDKException(&#39;Alert&#39;, &#39;101&#39;)

        if alert_notification_type.lower() in self._all_notification_types:
            alert_notification_type_id = self._all_notification_types[
                alert_notification_type.lower()]

            enable_request = self._services[&#39;ENABLE_ALERT_NOTIFICATION&#39;] % (
                self.alert_id, alert_notification_type_id
            )

            flag, response = self._cvpysdk_object.make_request(
                &#39;POST&#39;, enable_request
            )

            if flag:
                if response.json():
                    error_code = str(response.json()[&#39;errorCode&#39;])
                    if error_code == &#39;0&#39;:
                        return
                    else:
                        raise SDKException(&#39;Alert&#39;, &#39;102&#39;, response.json()[&#39;errorMessage&#39;])
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                response_string = self._update_response_(response.text)
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        else:
            raise SDKException(
                &#39;Alert&#39;,
                &#39;102&#39;,
                &#39;No notification type with name {0} exists&#39;.format(alert_notification_type)
            )

    def disable_notification_type(self, alert_notification_type):
        &#34;&#34;&#34;Disable the notification type.

            Args:
                alert_notification_type (str)  --  alert notification to disable

            Raises:
                SDKException:
                    if type of alert notification argument is not string

                    if failed to disable notification type

                    if response is empty

                    if response is not success

                    if no notification type exists with the name provided
        &#34;&#34;&#34;
        if not isinstance(alert_notification_type, str):
            raise SDKException(&#39;Alert&#39;, &#39;101&#39;)

        if alert_notification_type.lower() in self._all_notification_types:
            alert_notification_type_id = self._all_notification_types[
                alert_notification_type.lower()]

            disable_request = self._services[&#39;DISABLE_ALERT_NOTIFICATION&#39;] % (
                self.alert_id, alert_notification_type_id
            )

            flag, response = self._cvpysdk_object.make_request(
                &#39;POST&#39;, disable_request
            )

            if flag:
                if response.json():
                    error_code = str(response.json()[&#39;errorCode&#39;])
                    if error_code == &#39;0&#39;:
                        return
                    else:
                        raise SDKException(&#39;Alert&#39;, &#39;102&#39;, response.json()[&#39;errorMessage&#39;])
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                response_string = self._update_response_(response.text)
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        else:
            raise SDKException(
                &#39;Alert&#39;,
                &#39;102&#39;,
                &#39;No notification type with name {0} exists&#39;.format(alert_notification_type)
            )

    def enable(self):
        &#34;&#34;&#34;Enable an alert.

            Raises:
                SDKException:
                    if failed to enable alert

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        enable_request = self._services[&#39;ENABLE_ALERT&#39;] % (self.alert_id)

        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, enable_request)

        if flag:
            if response.json():
                error_code = str(response.json()[&#39;errorCode&#39;])

                if error_code == &#34;0&#34;:
                    return
                else:
                    error_message = &#34;&#34;

                    if &#39;errorMessage&#39; in response.json():
                        error_message = response.json()[&#39;errorMessage&#39;]

                    if error_message:
                        raise SDKException(
                            &#39;Alert&#39;, &#39;102&#39;, &#39;Failed to enable Alert\nError: &#34;{0}&#34;&#39;.format(
                                error_message
                            )
                        )
                    else:
                        raise SDKException(&#39;Alert&#39;, &#39;102&#39;, &#34;Failed to enable Alert&#34;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def disable(self):
        &#34;&#34;&#34;Disable an alert.

            Raises:
                SDKException:
                    if failed to disable alert

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        disable_request = self._services[&#39;DISABLE_ALERT&#39;] % (self.alert_id)

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, disable_request
        )

        if flag:
            if response.json():
                error_code = str(response.json()[&#39;errorCode&#39;])

                if error_code == &#34;0&#34;:
                    return
                else:
                    error_message = &#34;&#34;

                    if &#39;errorMessage&#39; in response.json():
                        error_message = response.json()[&#39;errorMessage&#39;]

                    if error_message:
                        raise SDKException(
                            &#39;Alert&#39;, &#39;102&#39;, &#39;Failed to disable Alert\nError: &#34;{0}&#34;&#39;.format(
                                error_message
                            )
                        )
                    else:
                        raise SDKException(&#39;Alert&#39;, &#39;102&#39;, &#34;Failed to disable Alert&#34;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the Alert.&#34;&#34;&#34;
        self._get_alert_properties()

    def trigger_test_alert(self):
        &#34;&#34;&#34;
        Method to trigger the test alert

        Raises:
            SDKException:
                if failed to trigger test alert

                if response is empty

                if response is not success
        &#34;&#34;&#34;
        test_request = self._services[&#39;ALERT_TEST&#39;] % (self.alert_id)

        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, test_request)

        if not flag:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        if not response.json():
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        error_code = response.json().get(&#39;errorCode&#39;, -1)

        if error_code != 0:
            error_message = response.json().get(&#39;errorMessage&#39;, &#39;&#39;)

            raise SDKException(
                &#39;Alert&#39;, &#39;102&#39;, f&#39;Failed to trigger the test Alert. Error: [&#34;{error_message}&#34;]&#39;
            )</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.alert.Alert"><code class="flex name class">
<span>class <span class="ident">Alert</span></span>
<span>(</span><span>commcell_object, alert_name, alert_id=None, alert_category=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for performing operations for a specific alert.</p>
<p>Initialise the Alert class instance.</p>
<h2 id="args">Args</h2>
<p>commcell_object (object)
&ndash;
instance of the Commcell class</p>
<p>alert_name
(str)
&ndash;
name of the alert</p>
<p>alert_id
(str)
&ndash;
id of the alert
default: None</p>
<p>alert_category
(str)
&ndash;
name of the alert category
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the ALert class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/alert.py#L726-L1311" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Alert(object):
    &#34;&#34;&#34;Class for performing operations for a specific alert.&#34;&#34;&#34;

    def __init__(self, commcell_object, alert_name, alert_id=None, alert_category=None):
        &#34;&#34;&#34;Initialise the Alert class instance.

            Args:
                commcell_object (object)  --  instance of the Commcell class

                alert_name      (str)     --  name of the alert

                alert_id        (str)     --  id of the alert
                    default: None

                alert_category  (str)     --  name of the alert category
                    default: None

            Returns:
                object - instance of the ALert class
        &#34;&#34;&#34;

        self._commcell_object = commcell_object
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_

        self._alerts_obj = Alerts(self._commcell_object)
        self._alert_name = alert_name.lower()
        self._alert_detail = None
        if alert_id:
            self._alert_id = str(alert_id)
        else:
            self._alert_id = self._get_alert_id()

        if alert_category:
            self._alert_category = alert_category
        else:
            self._alert_category = self._get_alert_category()

        self._ALERT = self._services[&#39;ALERT&#39;] % (self.alert_id)
        self._all_notification_types = {
            &#39;email&#39;: 1,
            &#39;snmp&#39;: 4,
            &#39;event viewer&#39;: 8,
            &#39;save to disk&#39;: 512,
            &#39;rss feeds&#39;: 1024,
            &#39;console alerts&#39;: 8192,
            &#39;scom&#39;: 32768,
            &#39;workflow&#39;: 65536,
            &#39;content indexing&#39;: 131072
        }

        self._alert_severity = None
        self._alert_type = None
        self._alert_type_id = None
        self._description = None
        self._criteria = []
        self._entities_list = []
        self._users_list = []
        self._user_group_list = []
        self._notification_types = []

        self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        representation_string = &#39;Alert class instance for Alert: &#34;{0}&#34;, Alert Type: &#34;{1}&#34;&#39;
        return representation_string.format(self.alert_name, self._alert_type)

    def _get_alert_id(self):
        &#34;&#34;&#34;Gets the alert id associated with this alert.

            Returns:
                str - id associated with this alert
        &#34;&#34;&#34;
        return self._alerts_obj.get(self.alert_name).alert_id

    def _get_alert_category(self):
        &#34;&#34;&#34;Gets the alert category associated with this alert.

            Returns:
                str - alert category name associated with this alert
        &#34;&#34;&#34;
        return self._alerts_obj.get(self.alert_name).alert_category

    def _get_alert_properties(self):
        &#34;&#34;&#34;Gets the alert properties of this alert.

            Returns:
                dict - dictionary consisting of the properties of this alert

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._ALERT)

        if flag:
            if response.json() and &#39;alertDetail&#39; in response.json().keys():
                self._alert_detail = response.json()[&#39;alertDetail&#39;]
                if &#39;alertSeverity&#39; in self._alert_detail:
                    self._alert_severity = self._alert_detail[&#39;alertSeverity&#39;]

                if &#39;criteria&#39; in self._alert_detail:
                    criterias = self._alert_detail[&#39;criteria&#39;]
                    for criteria in criterias:
                        self._criteria.append({
                            &#39;criteria_value&#39;: criteria[&#39;value&#39;] if &#39;value&#39; in criteria else None,
                            &#39;criteria_id&#39;:
                            str(criteria[&#39;criteriaId&#39;]) if &#39;criteriaId&#39; in criteria else None,
                            &#39;esclation_level&#39;:
                            criteria[&#39;esclationLevel&#39;] if &#39;esclationLevel&#39; in criteria else None
                        })

                if &#39;alert&#39; in self._alert_detail:
                    alert = self._alert_detail[&#39;alert&#39;]

                    if &#39;description&#39; in alert:
                        self._description = alert[&#39;description&#39;]

                    if &#39;alertType&#39; in alert and &#39;name&#39; in alert[&#39;alertType&#39;]:
                        self._alert_type = alert[&#39;alertType&#39;][&#39;name&#39;]
                        self._alert_type_id = alert[&#39;alertType&#39;][&#39;id&#39;]

                if &#39;xmlEntityList&#39; in self._alert_detail:
                    entity_xml = ET.fromstring(self._alert_detail[&#39;xmlEntityList&#39;])
                    self._entities_list = []
                    for entity in entity_xml.findall(&#34;associations&#34;):
                        if entity.find(&#34;flags&#34;) is not None:
                            if entity.find(&#34;flags&#34;).attrib[&#34;exclude&#34;] != &#34;1&#34;:
                                self._entities_list.append(entity.attrib)
                        else:
                            self._entities_list.append(entity.attrib)

                # to convert the ids to int type
                for entity in self._entities_list:
                    for key, value in entity.items():
                        try:
                            entity[key] = int(value) # to change the ids to type int
                        except ValueError:
                            pass

                if &#39;regularNotifications&#39; in self._alert_detail:
                    self._notification_types = self._alert_detail[&#34;regularNotifications&#34;]

                if &#39;userList&#39; in self._alert_detail:
                    self._users_list = [user[&#39;name&#39;] for user in self._alert_detail[&#39;userList&#39;]]
                else:
                    self._users_list = []

                if &#39;userGroupList&#39; in self._alert_detail:
                    self._user_group_list = [grp[&#39;name&#39;] for grp in self._alert_detail[&#39;userGroupList&#39;]]
                else:
                    self._user_group_list = []

                self._email_recipients = []
                if &#39;nonGalaxyUserList&#39; in self._alert_detail:
                    self._email_recipients = [email[&#39;name&#39;] for email in self._alert_detail[&#39;nonGalaxyUserList&#39;]]

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _modify_alert_properties(self):
        &#34;&#34;&#34;
        modifies the properties of an alert
        Exception:
            if modification of the alert failed
        &#34;&#34;&#34;

        request_json = {
            &#34;alertDetail&#34;:{
                &#34;alertDetail&#34;: {
                    &#34;alertType&#34;: self._alert_type_id, # this should not be changed
                    &#34;notifType&#34;: self._notification_types,
                    &#34;alertSeverity&#34;: self._alert_severity,
                    &#34;alertrule&#34;: {
                        &#34;alertName&#34;: self._alert_name
                    },
                    &#34;criteria&#34;: {
                        &#34;criteria&#34;: int(self._criteria[0][&#39;criteria_id&#39;])
                    },
                    &#34;userList&#34;: {
                        &#34;userListOperationType&#34;: 1,
                        &#34;userList&#34;: [{&#34;userName&#34;: user} for user in self._users_list]
                    },
                    &#34;userGroupList&#34;: {
                        &#34;userGroupListOperationType&#34;: 1,
                        &#34;userGroupList&#34;: [{&#34;userGroupName&#34;: user} for user in self._user_group_list]
                    },
                    &#34;nonGalaxyList&#34;: {
                        &#34;nonGalaxyUserList&#34;: [{&#34;nonGalaxyUser&#34;: email} for email in self._email_recipients]
                    },
                    &#34;EntityList&#34;: {
                        &#34;associations&#34;: self._entities_list
                    }
                }
            }
        }

        modify_alert = self._services[&#39;MODIFY_ALERT&#39;] % (self.alert_id)
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, modify_alert, request_json
        )

        if flag:
            if response.json():
                error_code = str(response.json()[&#39;errorCode&#39;])
                if error_code == &#39;0&#39;:
                    self.refresh()
                    return
                else:
                    o_str = &#39;Failed to update properties of Alert\nError: &#34;{0}&#34;&#39;
                    o_str = o_str.format(response.json()[&#39;errorMessage&#39;])
                    raise SDKException(&#39;Alert&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)



    @property
    def name(self):
        &#34;&#34;&#34;Returns the Alert display name &#34;&#34;&#34;
        return self._alert_detail[&#39;alert&#39;][&#39;alert&#39;][&#39;name&#39;]

    @property
    def alert_name(self):
        &#34;&#34;&#34;Treats the alert name as a read-only attribute.&#34;&#34;&#34;
        return self._alert_name

    @alert_name.setter
    def alert_name(self, name):
        &#34;&#34;&#34;Modifies the Alert name&#34;&#34;&#34;
        if not isinstance(name, str):
            raise SDKException(&#39;Alert&#39;, &#39;101&#39;)

        self._alert_name = name
        self._modify_alert_properties()

    @property
    def alert_id(self):
        &#34;&#34;&#34;Treats the alert id as a read-only attribute.&#34;&#34;&#34;
        return self._alert_id

    @property
    def alert_type(self):
        &#34;&#34;&#34;Treats the alert type as a read-only attribute.&#34;&#34;&#34;
        return self._alert_type

    @property
    def alert_category(self):
        &#34;&#34;&#34;Treats the alert category type id as a read-only attribute. &#34;&#34;&#34;
        return self._alert_category.title()

    @property
    def alert_severity(self):
        &#34;&#34;&#34;Treats the alert severity type id as a read-only attribute. &#34;&#34;&#34;
        return self._alert_severity

    @alert_severity.setter
    def alert_severity(self, severity):
        &#34;&#34;&#34;Modifies the Alert severity&#34;&#34;&#34;
        if not isinstance(severity, int):
            raise SDKException(&#39;Alert&#39;, &#39;101&#39;)

        self._alert_severity = severity
        self._modify_alert_properties()

    @property
    def alert_criteria(self):
        &#34;&#34;&#34;Treats the alert criteria as a read-only attribute.&#34;&#34;&#34;
        return &#34;\n&#34;.join([criteria[&#34;criteria_value&#34;] for criteria in self._criteria])

    @property
    def notification_types(self):
        &#34;&#34;&#34;Treats the alert notif types as a read-only attribute.&#34;&#34;&#34;
        notif_types = []
        for notif, notif_id in self._all_notification_types.items():
            if notif_id in self._notification_types:
                notif_types.append((notif_id, notif.title()))
        return notif_types

    @notification_types.setter
    def notification_types(self, notif_types):
        &#34;&#34;&#34;Treats the alert notif types as a read-only attribute.&#34;&#34;&#34;
        if not isinstance(notif_types, list):
            raise SDKException(&#39;Alert&#39;, &#39;102&#39;)
        try:
            ntypes = [self._all_notification_types[ntype.lower()] for ntype in notif_types]
        except KeyError as notif_type:
            raise SDKException(
                &#39;Alert&#39;,
                &#39;102&#39;,
                &#39;No notification type with name {0} exists&#39;.format(notif_type)
            )
        self._notification_types = ntypes
        self._modify_alert_properties()

    @property
    def entities(self):
        &#34;&#34;&#34;Treats the alert associations as a read-only attribute. &#34;&#34;&#34;
        return self._entities_list

    @entities.setter
    def entities(self, entity_json):
        &#34;&#34;&#34;Modifies the Alert entities&#34;&#34;&#34;
        if not isinstance(entity_json, dict):
            raise SDKException(&#39;Alert&#39;, &#39;101&#39;)

        self._entities_list = self._alerts_obj._get_entities(entity_json)
        self._modify_alert_properties()

    @property
    def email_recipients(self):
        &#34;&#34;&#34;returns the email recipients associated to the alert&#34;&#34;&#34;
        return self._email_recipients

    @email_recipients.setter
    def email_recipients(self, email_recipients):
        &#34;&#34;&#34;Modifies the email_recipients for the alert&#34;&#34;&#34;
        self._email_recipients.extend(email_recipients)
        self._modify_alert_properties()

    @property
    def description(self):
        &#34;&#34;&#34;Treats the alert description as a read-only attribute.&#34;&#34;&#34;
        return self._description

    @description.setter
    def description(self, description):
        &#34;&#34;&#34;Modifies the Alert description&#34;&#34;&#34;
        self._description = description
        self._modify_alert_properties()
    
    @property
    def users_list(self):
        &#34;&#34;&#34;Treats the users list as a read-only attribute.&#34;&#34;&#34;
        return self._users_list

    @users_list.setter
    def users_list(self, users_list):
        &#34;&#34;&#34;Modifies the users list&#34;&#34;&#34;
        self._users_list = users_list
        self._modify_alert_properties()
    
    @property
    def user_group_list(self):
        &#34;&#34;&#34;Treats the user group list as a read-only attribute.&#34;&#34;&#34;
        return self._user_group_list

    @user_group_list.setter
    def user_group_list(self, user_group_list):
        &#34;&#34;&#34;Modifies the user group list&#34;&#34;&#34;
        self._user_group_list = user_group_list
        self._modify_alert_properties()

    def enable_notification_type(self, alert_notification_type):
        &#34;&#34;&#34;Enable the notification type.

            Args:
                alert_notification_type (str)  --  alert notification to enable

            Raises:
                SDKException:
                    if type of alert notification argument is not string

                    if failed to enable notification type

                    if response is empty

                    if response is not success

                    if no notification type exists with the name provided
        &#34;&#34;&#34;
        if not isinstance(alert_notification_type, str):
            raise SDKException(&#39;Alert&#39;, &#39;101&#39;)

        if alert_notification_type.lower() in self._all_notification_types:
            alert_notification_type_id = self._all_notification_types[
                alert_notification_type.lower()]

            enable_request = self._services[&#39;ENABLE_ALERT_NOTIFICATION&#39;] % (
                self.alert_id, alert_notification_type_id
            )

            flag, response = self._cvpysdk_object.make_request(
                &#39;POST&#39;, enable_request
            )

            if flag:
                if response.json():
                    error_code = str(response.json()[&#39;errorCode&#39;])
                    if error_code == &#39;0&#39;:
                        return
                    else:
                        raise SDKException(&#39;Alert&#39;, &#39;102&#39;, response.json()[&#39;errorMessage&#39;])
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                response_string = self._update_response_(response.text)
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        else:
            raise SDKException(
                &#39;Alert&#39;,
                &#39;102&#39;,
                &#39;No notification type with name {0} exists&#39;.format(alert_notification_type)
            )

    def disable_notification_type(self, alert_notification_type):
        &#34;&#34;&#34;Disable the notification type.

            Args:
                alert_notification_type (str)  --  alert notification to disable

            Raises:
                SDKException:
                    if type of alert notification argument is not string

                    if failed to disable notification type

                    if response is empty

                    if response is not success

                    if no notification type exists with the name provided
        &#34;&#34;&#34;
        if not isinstance(alert_notification_type, str):
            raise SDKException(&#39;Alert&#39;, &#39;101&#39;)

        if alert_notification_type.lower() in self._all_notification_types:
            alert_notification_type_id = self._all_notification_types[
                alert_notification_type.lower()]

            disable_request = self._services[&#39;DISABLE_ALERT_NOTIFICATION&#39;] % (
                self.alert_id, alert_notification_type_id
            )

            flag, response = self._cvpysdk_object.make_request(
                &#39;POST&#39;, disable_request
            )

            if flag:
                if response.json():
                    error_code = str(response.json()[&#39;errorCode&#39;])
                    if error_code == &#39;0&#39;:
                        return
                    else:
                        raise SDKException(&#39;Alert&#39;, &#39;102&#39;, response.json()[&#39;errorMessage&#39;])
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                response_string = self._update_response_(response.text)
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        else:
            raise SDKException(
                &#39;Alert&#39;,
                &#39;102&#39;,
                &#39;No notification type with name {0} exists&#39;.format(alert_notification_type)
            )

    def enable(self):
        &#34;&#34;&#34;Enable an alert.

            Raises:
                SDKException:
                    if failed to enable alert

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        enable_request = self._services[&#39;ENABLE_ALERT&#39;] % (self.alert_id)

        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, enable_request)

        if flag:
            if response.json():
                error_code = str(response.json()[&#39;errorCode&#39;])

                if error_code == &#34;0&#34;:
                    return
                else:
                    error_message = &#34;&#34;

                    if &#39;errorMessage&#39; in response.json():
                        error_message = response.json()[&#39;errorMessage&#39;]

                    if error_message:
                        raise SDKException(
                            &#39;Alert&#39;, &#39;102&#39;, &#39;Failed to enable Alert\nError: &#34;{0}&#34;&#39;.format(
                                error_message
                            )
                        )
                    else:
                        raise SDKException(&#39;Alert&#39;, &#39;102&#39;, &#34;Failed to enable Alert&#34;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def disable(self):
        &#34;&#34;&#34;Disable an alert.

            Raises:
                SDKException:
                    if failed to disable alert

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        disable_request = self._services[&#39;DISABLE_ALERT&#39;] % (self.alert_id)

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, disable_request
        )

        if flag:
            if response.json():
                error_code = str(response.json()[&#39;errorCode&#39;])

                if error_code == &#34;0&#34;:
                    return
                else:
                    error_message = &#34;&#34;

                    if &#39;errorMessage&#39; in response.json():
                        error_message = response.json()[&#39;errorMessage&#39;]

                    if error_message:
                        raise SDKException(
                            &#39;Alert&#39;, &#39;102&#39;, &#39;Failed to disable Alert\nError: &#34;{0}&#34;&#39;.format(
                                error_message
                            )
                        )
                    else:
                        raise SDKException(&#39;Alert&#39;, &#39;102&#39;, &#34;Failed to disable Alert&#34;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the Alert.&#34;&#34;&#34;
        self._get_alert_properties()

    def trigger_test_alert(self):
        &#34;&#34;&#34;
        Method to trigger the test alert

        Raises:
            SDKException:
                if failed to trigger test alert

                if response is empty

                if response is not success
        &#34;&#34;&#34;
        test_request = self._services[&#39;ALERT_TEST&#39;] % (self.alert_id)

        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, test_request)

        if not flag:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        if not response.json():
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        error_code = response.json().get(&#39;errorCode&#39;, -1)

        if error_code != 0:
            error_message = response.json().get(&#39;errorMessage&#39;, &#39;&#39;)

            raise SDKException(
                &#39;Alert&#39;, &#39;102&#39;, f&#39;Failed to trigger the test Alert. Error: [&#34;{error_message}&#34;]&#39;
            )</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.alert.Alert.alert_category"><code class="name">var <span class="ident">alert_category</span></code></dt>
<dd>
<div class="desc"><p>Treats the alert category type id as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/alert.py#L982-L985" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def alert_category(self):
    &#34;&#34;&#34;Treats the alert category type id as a read-only attribute. &#34;&#34;&#34;
    return self._alert_category.title()</code></pre>
</details>
</dd>
<dt id="cvpysdk.alert.Alert.alert_criteria"><code class="name">var <span class="ident">alert_criteria</span></code></dt>
<dd>
<div class="desc"><p>Treats the alert criteria as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/alert.py#L1001-L1004" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def alert_criteria(self):
    &#34;&#34;&#34;Treats the alert criteria as a read-only attribute.&#34;&#34;&#34;
    return &#34;\n&#34;.join([criteria[&#34;criteria_value&#34;] for criteria in self._criteria])</code></pre>
</details>
</dd>
<dt id="cvpysdk.alert.Alert.alert_id"><code class="name">var <span class="ident">alert_id</span></code></dt>
<dd>
<div class="desc"><p>Treats the alert id as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/alert.py#L972-L975" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def alert_id(self):
    &#34;&#34;&#34;Treats the alert id as a read-only attribute.&#34;&#34;&#34;
    return self._alert_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.alert.Alert.alert_name"><code class="name">var <span class="ident">alert_name</span></code></dt>
<dd>
<div class="desc"><p>Treats the alert name as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/alert.py#L958-L961" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def alert_name(self):
    &#34;&#34;&#34;Treats the alert name as a read-only attribute.&#34;&#34;&#34;
    return self._alert_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.alert.Alert.alert_severity"><code class="name">var <span class="ident">alert_severity</span></code></dt>
<dd>
<div class="desc"><p>Treats the alert severity type id as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/alert.py#L987-L990" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def alert_severity(self):
    &#34;&#34;&#34;Treats the alert severity type id as a read-only attribute. &#34;&#34;&#34;
    return self._alert_severity</code></pre>
</details>
</dd>
<dt id="cvpysdk.alert.Alert.alert_type"><code class="name">var <span class="ident">alert_type</span></code></dt>
<dd>
<div class="desc"><p>Treats the alert type as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/alert.py#L977-L980" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def alert_type(self):
    &#34;&#34;&#34;Treats the alert type as a read-only attribute.&#34;&#34;&#34;
    return self._alert_type</code></pre>
</details>
</dd>
<dt id="cvpysdk.alert.Alert.description"><code class="name">var <span class="ident">description</span></code></dt>
<dd>
<div class="desc"><p>Treats the alert description as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/alert.py#L1056-L1059" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def description(self):
    &#34;&#34;&#34;Treats the alert description as a read-only attribute.&#34;&#34;&#34;
    return self._description</code></pre>
</details>
</dd>
<dt id="cvpysdk.alert.Alert.email_recipients"><code class="name">var <span class="ident">email_recipients</span></code></dt>
<dd>
<div class="desc"><p>returns the email recipients associated to the alert</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/alert.py#L1045-L1048" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def email_recipients(self):
    &#34;&#34;&#34;returns the email recipients associated to the alert&#34;&#34;&#34;
    return self._email_recipients</code></pre>
</details>
</dd>
<dt id="cvpysdk.alert.Alert.entities"><code class="name">var <span class="ident">entities</span></code></dt>
<dd>
<div class="desc"><p>Treats the alert associations as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/alert.py#L1031-L1034" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def entities(self):
    &#34;&#34;&#34;Treats the alert associations as a read-only attribute. &#34;&#34;&#34;
    return self._entities_list</code></pre>
</details>
</dd>
<dt id="cvpysdk.alert.Alert.name"><code class="name">var <span class="ident">name</span></code></dt>
<dd>
<div class="desc"><p>Returns the Alert display name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/alert.py#L953-L956" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def name(self):
    &#34;&#34;&#34;Returns the Alert display name &#34;&#34;&#34;
    return self._alert_detail[&#39;alert&#39;][&#39;alert&#39;][&#39;name&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.alert.Alert.notification_types"><code class="name">var <span class="ident">notification_types</span></code></dt>
<dd>
<div class="desc"><p>Treats the alert notif types as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/alert.py#L1006-L1013" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def notification_types(self):
    &#34;&#34;&#34;Treats the alert notif types as a read-only attribute.&#34;&#34;&#34;
    notif_types = []
    for notif, notif_id in self._all_notification_types.items():
        if notif_id in self._notification_types:
            notif_types.append((notif_id, notif.title()))
    return notif_types</code></pre>
</details>
</dd>
<dt id="cvpysdk.alert.Alert.user_group_list"><code class="name">var <span class="ident">user_group_list</span></code></dt>
<dd>
<div class="desc"><p>Treats the user group list as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/alert.py#L1078-L1081" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def user_group_list(self):
    &#34;&#34;&#34;Treats the user group list as a read-only attribute.&#34;&#34;&#34;
    return self._user_group_list</code></pre>
</details>
</dd>
<dt id="cvpysdk.alert.Alert.users_list"><code class="name">var <span class="ident">users_list</span></code></dt>
<dd>
<div class="desc"><p>Treats the users list as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/alert.py#L1067-L1070" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def users_list(self):
    &#34;&#34;&#34;Treats the users list as a read-only attribute.&#34;&#34;&#34;
    return self._users_list</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.alert.Alert.disable"><code class="name flex">
<span>def <span class="ident">disable</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Disable an alert.</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to disable alert</p>
<pre><code>if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/alert.py#L1234-L1275" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def disable(self):
    &#34;&#34;&#34;Disable an alert.

        Raises:
            SDKException:
                if failed to disable alert

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    disable_request = self._services[&#39;DISABLE_ALERT&#39;] % (self.alert_id)

    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, disable_request
    )

    if flag:
        if response.json():
            error_code = str(response.json()[&#39;errorCode&#39;])

            if error_code == &#34;0&#34;:
                return
            else:
                error_message = &#34;&#34;

                if &#39;errorMessage&#39; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]

                if error_message:
                    raise SDKException(
                        &#39;Alert&#39;, &#39;102&#39;, &#39;Failed to disable Alert\nError: &#34;{0}&#34;&#39;.format(
                            error_message
                        )
                    )
                else:
                    raise SDKException(&#39;Alert&#39;, &#39;102&#39;, &#34;Failed to disable Alert&#34;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.alert.Alert.disable_notification_type"><code class="name flex">
<span>def <span class="ident">disable_notification_type</span></span>(<span>self, alert_notification_type)</span>
</code></dt>
<dd>
<div class="desc"><p>Disable the notification type.</p>
<h2 id="args">Args</h2>
<p>alert_notification_type (str)
&ndash;
alert notification to disable</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of alert notification argument is not string</p>
<pre><code>if failed to disable notification type

if response is empty

if response is not success

if no notification type exists with the name provided
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/alert.py#L1141-L1191" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def disable_notification_type(self, alert_notification_type):
    &#34;&#34;&#34;Disable the notification type.

        Args:
            alert_notification_type (str)  --  alert notification to disable

        Raises:
            SDKException:
                if type of alert notification argument is not string

                if failed to disable notification type

                if response is empty

                if response is not success

                if no notification type exists with the name provided
    &#34;&#34;&#34;
    if not isinstance(alert_notification_type, str):
        raise SDKException(&#39;Alert&#39;, &#39;101&#39;)

    if alert_notification_type.lower() in self._all_notification_types:
        alert_notification_type_id = self._all_notification_types[
            alert_notification_type.lower()]

        disable_request = self._services[&#39;DISABLE_ALERT_NOTIFICATION&#39;] % (
            self.alert_id, alert_notification_type_id
        )

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, disable_request
        )

        if flag:
            if response.json():
                error_code = str(response.json()[&#39;errorCode&#39;])
                if error_code == &#39;0&#39;:
                    return
                else:
                    raise SDKException(&#39;Alert&#39;, &#39;102&#39;, response.json()[&#39;errorMessage&#39;])
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
    else:
        raise SDKException(
            &#39;Alert&#39;,
            &#39;102&#39;,
            &#39;No notification type with name {0} exists&#39;.format(alert_notification_type)
        )</code></pre>
</details>
</dd>
<dt id="cvpysdk.alert.Alert.enable"><code class="name flex">
<span>def <span class="ident">enable</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Enable an alert.</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to enable alert</p>
<pre><code>if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/alert.py#L1193-L1232" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable(self):
    &#34;&#34;&#34;Enable an alert.

        Raises:
            SDKException:
                if failed to enable alert

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    enable_request = self._services[&#39;ENABLE_ALERT&#39;] % (self.alert_id)

    flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, enable_request)

    if flag:
        if response.json():
            error_code = str(response.json()[&#39;errorCode&#39;])

            if error_code == &#34;0&#34;:
                return
            else:
                error_message = &#34;&#34;

                if &#39;errorMessage&#39; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]

                if error_message:
                    raise SDKException(
                        &#39;Alert&#39;, &#39;102&#39;, &#39;Failed to enable Alert\nError: &#34;{0}&#34;&#39;.format(
                            error_message
                        )
                    )
                else:
                    raise SDKException(&#39;Alert&#39;, &#39;102&#39;, &#34;Failed to enable Alert&#34;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.alert.Alert.enable_notification_type"><code class="name flex">
<span>def <span class="ident">enable_notification_type</span></span>(<span>self, alert_notification_type)</span>
</code></dt>
<dd>
<div class="desc"><p>Enable the notification type.</p>
<h2 id="args">Args</h2>
<p>alert_notification_type (str)
&ndash;
alert notification to enable</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of alert notification argument is not string</p>
<pre><code>if failed to enable notification type

if response is empty

if response is not success

if no notification type exists with the name provided
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/alert.py#L1089-L1139" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable_notification_type(self, alert_notification_type):
    &#34;&#34;&#34;Enable the notification type.

        Args:
            alert_notification_type (str)  --  alert notification to enable

        Raises:
            SDKException:
                if type of alert notification argument is not string

                if failed to enable notification type

                if response is empty

                if response is not success

                if no notification type exists with the name provided
    &#34;&#34;&#34;
    if not isinstance(alert_notification_type, str):
        raise SDKException(&#39;Alert&#39;, &#39;101&#39;)

    if alert_notification_type.lower() in self._all_notification_types:
        alert_notification_type_id = self._all_notification_types[
            alert_notification_type.lower()]

        enable_request = self._services[&#39;ENABLE_ALERT_NOTIFICATION&#39;] % (
            self.alert_id, alert_notification_type_id
        )

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, enable_request
        )

        if flag:
            if response.json():
                error_code = str(response.json()[&#39;errorCode&#39;])
                if error_code == &#39;0&#39;:
                    return
                else:
                    raise SDKException(&#39;Alert&#39;, &#39;102&#39;, response.json()[&#39;errorMessage&#39;])
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
    else:
        raise SDKException(
            &#39;Alert&#39;,
            &#39;102&#39;,
            &#39;No notification type with name {0} exists&#39;.format(alert_notification_type)
        )</code></pre>
</details>
</dd>
<dt id="cvpysdk.alert.Alert.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the properties of the Alert.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/alert.py#L1277-L1279" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the properties of the Alert.&#34;&#34;&#34;
    self._get_alert_properties()</code></pre>
</details>
</dd>
<dt id="cvpysdk.alert.Alert.trigger_test_alert"><code class="name flex">
<span>def <span class="ident">trigger_test_alert</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to trigger the test alert</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to trigger test alert</p>
<pre><code>if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/alert.py#L1281-L1311" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def trigger_test_alert(self):
    &#34;&#34;&#34;
    Method to trigger the test alert

    Raises:
        SDKException:
            if failed to trigger test alert

            if response is empty

            if response is not success
    &#34;&#34;&#34;
    test_request = self._services[&#39;ALERT_TEST&#39;] % (self.alert_id)

    flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, test_request)

    if not flag:
        response_string = self._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    if not response.json():
        raise SDKException(&#39;Response&#39;, &#39;102&#39;)

    error_code = response.json().get(&#39;errorCode&#39;, -1)

    if error_code != 0:
        error_message = response.json().get(&#39;errorMessage&#39;, &#39;&#39;)

        raise SDKException(
            &#39;Alert&#39;, &#39;102&#39;, f&#39;Failed to trigger the test Alert. Error: [&#34;{error_message}&#34;]&#39;
        )</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.alert.Alerts"><code class="flex name class">
<span>class <span class="ident">Alerts</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for getting all the Alerts associated with the commcell.</p>
<p>Initialize object of the Alerts class.</p>
<h2 id="args">Args</h2>
<p>commcell_object (object)
&ndash;
instance of the Commcell class</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Alerts class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/alert.py#L137-L723" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Alerts(object):
    &#34;&#34;&#34;Class for getting all the Alerts associated with the commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the Alerts class.

            Args:
                commcell_object (object)  --  instance of the Commcell class

            Returns:
                object - instance of the Alerts class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._ALERTS = commcell_object._services[&#39;GET_ALL_ALERTS&#39;]
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_
        self._alerts = None

        self._notification_types = {
            &#39;email&#39;: 1,
            &#39;snmp&#39;: 4,
            &#39;event viewer&#39;: 8,
            &#39;save to disk&#39;: 512,
            &#39;rss feeds&#39;: 1024,
            &#39;console alerts&#39;: 8192,
            &#39;scom&#39;: 32768,
            &#39;workflow&#39;: 65536,
            &#39;content indexing&#39;: 131072
        }
        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all alerts of the Commcell.

            Returns:
                str - string of all the alerts for a commcell
        &#34;&#34;&#34;
        representation_string = &#34;{:^5}\t{:^50}\t{:^80}\t{:^30}\n\n&#34;.format(
            &#39;S. No.&#39;, &#39;Alert&#39;, &#39;Description&#39;, &#39;Category&#39;
        )

        for index, alert_name in enumerate(self._alerts):
            alert_description = self._alerts[alert_name][&#39;description&#39;]
            alert_category = self._alerts[alert_name][&#39;category&#39;]
            sub_str = &#39;{:^5}\t{:50}\t{:80}\t{:30}\n&#39;.format(
                index + 1,
                alert_name,
                alert_description,
                alert_category
            )
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the Alerts class.&#34;&#34;&#34;
        return &#34;Alerts class instance for Commcell&#34;

    def __len__(self):
        &#34;&#34;&#34;Returns the number of the alerts configured on the Commcell.&#34;&#34;&#34;
        return len(self.all_alerts)

    def __getitem__(self, value):
        &#34;&#34;&#34;Returns the name of the alert for the given alert ID or
            the details of the alert for given alert Name.

            Args:
                value   (str / int)     --  Name or ID of the alert

            Returns:
                str     -   name of the alert, if the alert id was given

                dict    -   dict of details of the alert, if alert name was given

            Raises:
                IndexError:
                    no alert exists with the given Name / Id

        &#34;&#34;&#34;
        value = str(value)

        if value in self.all_alerts:
            return self.all_alerts[value]
        else:
            try:
                return list(filter(lambda x: x[1][&#39;id&#39;] == value, self.all_alerts.items()))[0][0]
            except IndexError:
                raise IndexError(&#39;No alert exists with the given Name / Id&#39;)

    def _get_alerts(self):
        &#34;&#34;&#34;Gets all the alerts associated with the commcell

            Returns:
                dict - consists of all alerts of the commcell
                    {
                         &#34;alert1_name&#34;: {
                             &#34;id&#34;: alert1_id,
                             &#34;category&#34;: alert1_category
                         },
                         &#34;alert2_name&#34;: {
                             &#34;id&#34;: alert2_id,
                             &#34;category&#34;: alert2_category
                         }
                    }

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._ALERTS)

        if flag:
            if response.json() and &#39;alertList&#39; in response.json():
                alerts_dict = {}

                for dictionary in response.json()[&#39;alertList&#39;]:
                    temp_dict = {}

                    temp_name = dictionary[&#39;alert&#39;][&#39;name&#39;].lower()
                    temp_id = str(dictionary[&#39;alert&#39;][&#39;id&#39;]).lower()
                    temp_description = dictionary[&#39;description&#39;].lower()
                    temp_category = dictionary[&#39;alertCategory&#39;][&#39;name&#39;].lower()

                    temp_dict[&#39;id&#39;] = temp_id
                    temp_dict[&#39;description&#39;] = temp_description
                    temp_dict[&#39;category&#39;] = temp_category

                    alerts_dict[temp_name] = temp_dict

                    del temp_dict

                return alerts_dict
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)


    def _get_entities(self, entities):
        &#34;&#34;&#34;Returns the list of entities associations for an alert

        Args:
            entities    (dict)  --  dictionary of entities for an alert

        Raise:
            SDKException:
                if entities is not an instance of dictionary

        Returns:
            list  -  a list of associations for an alert

        &#34;&#34;&#34;
        if not isinstance(entities, dict):
            raise SDKException(&#39;Alert&#39;, &#39;101&#39;)

        # policies not handled as

        entity_dict = {
            &#34;clients&#34;: {
                &#34;clientName&#34;: &#34;client_name&#34;,
                &#34;clientId&#34;: &#34;client_id&#34;,
                &#34;_type_&#34;: 3
            },
            &#34;client_groups&#34;: {
                &#34;clientGroupName&#34;: &#34;clientgroup_name&#34;,
                &#34;clientGroupId&#34;: &#34;clientgroup_id&#34;,
                &#34;_type_&#34;: 28
            },
            &#34;users&#34;: {
                &#34;userName&#34;: &#34;user_name&#34;,
                &#34;userId&#34;: &#34;user_id&#34;,
                &#34;_type_&#34;: 13
            },
            &#34;user_groups&#34;: {
                &#34;userGroupName&#34;: &#34;user_group_name&#34;,
                &#34;userGroupId&#34;: &#34;user_group_id&#34;,
                &#34;_type_&#34;: 15
            },
            &#34;disk_libraries&#34;: {
                &#34;libraryName&#34;: &#34;library_name&#34;,
                &#34;libraryId&#34;: &#34;library_id&#34;,
                &#34;_type_&#34;: 9
            },
            &#34;media_agents&#34;: {
                &#34;mediaAgentName&#34;: &#34;media_agent_name&#34;,
                &#34;mediaAgentId&#34;: &#34;media_agent_id&#34;,
                &#34;_type_&#34;: 11
            },
            &#34;storage_policies&#34;: {
                &#34;storagePolicyName&#34;: &#34;storage_policy_name&#34;,
                &#34;storagePolicyId&#34;: &#34;storage_policy_id&#34;,
                &#34;_type_&#34;: 17
            }
        }

        associations = []

        for entity, values in entities.items():
            if entity == &#34;entity_type_names&#34;:
                for value in values:
                    if value == &#34;ALL_CLIENT_GROUPS_ENTITY&#34;:
                        entity_type = 27
                    else:
                        entity_type = 2
                    temp_dict = {
                        &#34;entityTypeName&#34;: value,
                        &#34;_type_&#34;: entity_type
                    }
                    associations.append(temp_dict)
            else:
                entity_obj = getattr(self._commcell_object, entity)

                # this will allows us to loop through even for single item
                values = values.split() if not isinstance(values, list) else values

                for value in values:
                    temp_dict = entity_dict[entity].copy()
                    for name, entity_attr in temp_dict.items():
                        if name != &#34;_type_&#34;:
                            try: # to convert the string values to int types
                                temp_dict[name] = int(getattr(entity_obj.get(value), entity_attr))
                            except ValueError:
                                temp_dict[name] = getattr(entity_obj.get(value), entity_attr)
                    associations.append(temp_dict)

        return associations


    def _get_alert_json(self, alert_json):
        &#34;&#34;&#34;To form the json required to create an alert

        Args:
            alert_json    (dict)  --  a dictionary to create an alert

        Returns:
            dict  -  a constructed dictionary needed to create an alert
        &#34;&#34;&#34;
        alert_detail = {
            &#34;alertDetail&#34;: {
                &#34;alertType&#34;: alert_json.get(&#34;alert_type&#34;),
                &#34;notifType&#34;: [n_type for n_type in alert_json.get(&#34;notif_type&#34;, [8192])],
                &#34;notifTypeListOperationType&#34;: alert_json.get(&#34;notifTypeListOperationType&#34;, 0),
                &#34;alertSeverity&#34;: alert_json.get(&#34;alertSeverity&#34;, 0),
                &#34;EscnonGalaxyUserList&#34;:{
                    &#34;nonGalaxyUserOperationType&#34;: alert_json.get(&#34;nonGalaxyUserOperationType&#34;, 0)
                },
                &#34;locale&#34;:{
                    &#34;localeID&#34;:alert_json.get(&#34;localeID&#34;, 0)
                },
                &#34;EscUserList&#34;:{
                    &#34;userListOperationType&#34;:alert_json.get(&#34;userListOperationType&#34;, 0)
                },
                &#34;EscUserGroupList&#34;:{
                    &#34;userGroupListOperationType&#34;: alert_json.get(&#34;userGroupListOperationType&#34;, 0)
                },
                &#34;alertrule&#34;:{
                    &#34;alertName&#34;: alert_json.get(&#34;alert_name&#34;)
                },
                &#34;criteria&#34;:{
                    &#34;criteria&#34;: alert_json.get(&#34;criteria&#34;)
                },
                &#34;userList&#34;:{
                    &#34;userListOperationType&#34;:alert_json.get(&#34;userListOperationType&#34;, 0),
                    &#34;userList&#34;:[{&#34;userName&#34;:user} for user in alert_json.get(&#34;users&#34;, [&#34;admin&#34;])]
                },
                &#34;EntityList&#34;:{
                    &#34;associationsOperationType&#34;:alert_json.get(&#34;associationsOperationType&#34;, 0),
                    &#34;associations&#34;: self._get_entities(alert_json.get(&#34;entities&#34;, dict()))
                }
            }
        }

        # Check if paramsList is present or not
        if alert_json.get(&#34;paramsList&#34;):
            alert_detail[&#34;alertDetail&#34;][&#34;criteria&#34;][&#34;paramsList&#34;] = alert_json.get(&#34;paramsList&#34;)

        # Check if additonal mail recipents exist
        if alert_json.get(&#34;nonGalaxyList&#34;):
            alert_detail[&#34;alertDetail&#34;][&#34;nonGalaxyList&#34;] = alert_json.get(&#34;nonGalaxyList&#34;)

        if alert_json.get(&#34;user_groups&#34;):
            alert_detail[&#34;alertDetail&#34;][&#34;userGroupList&#34;] = {
                &#34;userGroupListOperationType&#34;:alert_json.get(&#34;userGroupListOperationType&#34;, 0),
                &#34;userGroupList&#34;:[
                    {
                        &#34;userGroupName&#34;:user_grp
                    } for user_grp in alert_json.get(&#34;user_groups&#34;)
                ]
            }

        return alert_detail

    def get_alert_sender(self):
        &#34;&#34;&#34;
            Returns the Alert Sender name
        &#34;&#34;&#34;
        get_alert = self._services[&#39;EMAIL_SERVER&#39;]
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, get_alert)
        if flag:
            if response.json():
                sender = response.json()[&#34;senderInfo&#34;][&#39;senderName&#39;]
                if not sender:
                    sender = response.json()[&#34;senderInfo&#34;][&#39;senderAddress&#39;]
                return sender
            else:
                raise SDKException(&#39;Alert&#39;, &#39;102&#39;, &#34;Failed to get sender address&#34;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)


    def create_alert(self, alert_dict):
        &#34;&#34;&#34;Creates a new Alert for CommCell

        Args:
            alert_dict    (dict)  --  dictionary required to create an alert

        Returns:
            object  -  instance of the Alert class for this new alert

        Raises:
            SDKException:
                if input argument is not an instance of dict

                if alert with given name already exists

                if failed to create an alert

                if response is not success

                if response is empty
        &#34;&#34;&#34;
        if not isinstance(alert_dict, dict):
            raise SDKException(&#39;Alert&#39;, &#39;101&#39;)

        # required alert json
        alert_json = self._get_alert_json(alert_dict)
        alert_name = alert_json[&#34;alertDetail&#34;][&#34;alertrule&#34;][&#34;alertName&#34;]
        if self.has_alert(alert_name):
            raise SDKException(&#39;Alert&#39;, &#39;102&#39;, &#39;Alert &#34;{0}&#34; already exists.&#39;.
                               format(alert_name))

        post_alert = self._services[&#39;GET_ALL_ALERTS&#39;]
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, post_alert, alert_json)

        if flag:
            if response.json():
                error_dict = response.json()[&#34;errorResp&#34;]
                error_code = str(error_dict[&#34;errorCode&#34;])

                if error_code == &#34;0&#34;:
                    self.refresh()
                    return self.get(alert_name)
                else:
                    error_message = &#34;&#34;

                    if &#39;errorMessage&#39; in error_dict:
                        error_message = error_dict[&#39;errorMessage&#39;]

                    if error_message:
                        raise SDKException(
                            &#39;Alert&#39;, &#39;102&#39;, &#39;Failed to create Alert\nError: &#34;{}&#34;&#39;.format(
                                error_message
                            )
                        )
                    else:
                        raise SDKException(
                            &#39;Alert&#39;, &#39;102&#39;, &#34;Failed to create Alert&#34;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)


    @property
    def all_alerts(self):
        &#34;&#34;&#34;Returns the dict of all the alerts configured on this commcell

            dict - consists of all alerts of the commcell
                    {
                         &#34;alert1_name&#34;: {
                             &#34;id&#34;: alert1_id,
                             &#34;category&#34;: alert1_category
                         },
                         &#34;alert2_name&#34;: {
                             &#34;id&#34;: alert2_id,
                             &#34;category&#34;: alert2_category
                         }
                    }
        &#34;&#34;&#34;
        return self._alerts

    def has_alert(self, alert_name):
        &#34;&#34;&#34;Checks if a alert exists for the commcell with the input alert name.

            Args:
                alert_name (str)  --  name of the alert

            Returns:
                bool - boolean output whether the alert exists for the commcell or not

            Raises:
                SDKException:
                    if type of the alert name argument is not string
        &#34;&#34;&#34;
        if not isinstance(alert_name, str):
            raise SDKException(&#39;Alert&#39;, &#39;101&#39;)

        return self._alerts and alert_name.lower() in self._alerts

    def get(self, alert_name):
        &#34;&#34;&#34;Returns a alert object of the specified alert name.

            Args:
                alert_name (str)  --  name of the alert

            Returns:
                object - instance of the Alert class for the given alert name

            Raises:
                SDKException:
                    if type of the alert name argument is not string

                    if no alert exists with the given name
        &#34;&#34;&#34;
        if not isinstance(alert_name, str):
            raise SDKException(&#39;Alert&#39;, &#39;101&#39;)
        else:
            alert_name = alert_name.lower()

            if self.has_alert(alert_name):
                return Alert(
                    self._commcell_object, alert_name,
                    self._alerts[alert_name][&#39;id&#39;],
                    self._alerts[alert_name][&#39;category&#39;]
                )

            raise SDKException(&#39;Alert&#39;, &#39;102&#39;, &#39;No Alert exists with name: {0}&#39;.format(alert_name))

    def console_alerts(self, page_number=1, page_count=1):
        &#34;&#34;&#34;Returns the console alerts from page_number to the number of pages asked for page_count

            Args:
                page_number (int)  --  page number to get the alerts from

                page_count  (int)  --  number of pages to get the alerts of

            Raises:
                SDKException:
                    if type of the page number and page count argument is not int

                    if response is empty

                    if response is not success

            Returns:
                str - String representation of console alerts if version is less than SP23
                object - json response object for console alerts if version greater than or equal to SP23
        &#34;&#34;&#34;
        if not (isinstance(page_number, int) and isinstance(page_count, int)):
            raise SDKException(&#39;Alert&#39;, &#39;101&#39;)

        console_alerts = self._services[&#39;GET_ALL_CONSOLE_ALERTS&#39;] % (
            page_number, page_count)

        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, console_alerts)

        if flag:
            if response.json() and &#39;totalNoOfAlerts&#39; in response.json():
                if self._commcell_object.commserv_version &gt;= 23:
                    return response.json()

                o_str = &#34;Total Console Alerts found: {0}&#34;.format(
                    response.json()[&#39;totalNoOfAlerts&#39;]
                )

                o_str += &#34;\n{:^5}\t{:^50}\t{:^50}\t{:^50}\n\n&#34;.format(
                    &#39;S. No.&#39;, &#39;Alert&#39;, &#39;Type&#39;, &#39;Criteria&#39;
                )

                for index, dictionary in enumerate(response.json()[&#39;feedsList&#39;]):
                    o_str += &#39;{:^5}\t{:50}\t{:^50}\t{:^50}\n&#39;.format(
                        index + 1,
                        dictionary[&#39;alertName&#39;],
                        dictionary[&#39;alertType&#39;],
                        dictionary[&#39;alertcriteria&#39;]
                    )

                return o_str
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def console_alert(self, live_feed_id):
        &#34;&#34;&#34;Returns the console console alert with given live_feed_id

            Args:
                live_feed_id (int)  --  Live feed ID of console alert to fetch

            Raises:
                SDKException:
                    if type of the live_feed_id argument is not int

                    if response is empty

                    if response is not success

            Returns:
                object - Console alert json object for given live_feed_id
        &#34;&#34;&#34;
        if not (isinstance(live_feed_id, int)):
            raise SDKException(&#39;Alert&#39;, &#39;101&#39;)

        console_alerts = self._services[&#39;GET_CONSOLE_ALERT&#39;] % (
            live_feed_id)

        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, console_alerts)

        if flag:
            if response and response.json() and &#39;description&#39; in response.json():
                return response.json()
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def delete(self, alert_name):
        &#34;&#34;&#34;Deletes the alert from the commcell.

            Args:
                alert_name (str)  --  name of the alert

            Raises:
                SDKException:
                    if type of the alert name argument is not string

                    if failed to delete the alert

                    if no alert exists with the given name
        &#34;&#34;&#34;
        if not isinstance(alert_name, str):
            raise SDKException(&#39;Alert&#39;, &#39;101&#39;)
        alert_name = alert_name.lower()

        if self.has_alert(alert_name):
            alert_id = self._alerts[alert_name][&#39;id&#39;]
            alert = self._services[&#39;ALERT&#39;] % (alert_id)

            flag, response = self._cvpysdk_object.make_request(
                &#39;DELETE&#39;, alert
            )

            if flag:
                if response.json():
                    if &#39;errorCode&#39; in response.json():
                        if response.json()[&#39;errorCode&#39;] == 0:
                            # initialize the alerts again
                            # to refresh with the latest alerts
                            self.refresh()
                        else:
                            raise SDKException(&#39;Alert&#39;, &#39;102&#39;, response.json()[&#39;errorMessage&#39;])
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                response_string = self._update_response_(response.text)
                exception_message = &#39;Failed to delete alert\nError: &#34;{0}&#34;&#39;.format(
                    response_string
                )

                raise SDKException(&#39;Alert&#39;, &#39;102&#39;, exception_message)
        else:
            raise SDKException(
                &#39;Alert&#39;, &#39;102&#39;, &#39;No alert exists with name: {0}&#39;.format(alert_name)
            )

    def refresh(self):
        &#34;&#34;&#34;Refresh the alerts associated with the Commcell.&#34;&#34;&#34;
        self._alerts = self._get_alerts()</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.alert.Alerts.all_alerts"><code class="name">var <span class="ident">all_alerts</span></code></dt>
<dd>
<div class="desc"><p>Returns the dict of all the alerts configured on this commcell</p>
<p>dict - consists of all alerts of the commcell
{
"alert1_name": {
"id": alert1_id,
"category": alert1_category
},
"alert2_name": {
"id": alert2_id,
"category": alert2_category
}
}</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/alert.py#L517-L533" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def all_alerts(self):
    &#34;&#34;&#34;Returns the dict of all the alerts configured on this commcell

        dict - consists of all alerts of the commcell
                {
                     &#34;alert1_name&#34;: {
                         &#34;id&#34;: alert1_id,
                         &#34;category&#34;: alert1_category
                     },
                     &#34;alert2_name&#34;: {
                         &#34;id&#34;: alert2_id,
                         &#34;category&#34;: alert2_category
                     }
                }
    &#34;&#34;&#34;
    return self._alerts</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.alert.Alerts.console_alert"><code class="name flex">
<span>def <span class="ident">console_alert</span></span>(<span>self, live_feed_id)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the console console alert with given live_feed_id</p>
<h2 id="args">Args</h2>
<p>live_feed_id (int)
&ndash;
Live feed ID of console alert to fetch</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the live_feed_id argument is not int</p>
<pre><code>if response is empty

if response is not success
</code></pre>
<h2 id="returns">Returns</h2>
<p>object - Console alert json object for given live_feed_id</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/alert.py#L638-L670" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def console_alert(self, live_feed_id):
    &#34;&#34;&#34;Returns the console console alert with given live_feed_id

        Args:
            live_feed_id (int)  --  Live feed ID of console alert to fetch

        Raises:
            SDKException:
                if type of the live_feed_id argument is not int

                if response is empty

                if response is not success

        Returns:
            object - Console alert json object for given live_feed_id
    &#34;&#34;&#34;
    if not (isinstance(live_feed_id, int)):
        raise SDKException(&#39;Alert&#39;, &#39;101&#39;)

    console_alerts = self._services[&#39;GET_CONSOLE_ALERT&#39;] % (
        live_feed_id)

    flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, console_alerts)

    if flag:
        if response and response.json() and &#39;description&#39; in response.json():
            return response.json()
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.alert.Alerts.console_alerts"><code class="name flex">
<span>def <span class="ident">console_alerts</span></span>(<span>self, page_number=1, page_count=1)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the console alerts from page_number to the number of pages asked for page_count</p>
<h2 id="args">Args</h2>
<p>page_number (int)
&ndash;
page number to get the alerts from</p>
<p>page_count
(int)
&ndash;
number of pages to get the alerts of</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the page number and page count argument is not int</p>
<pre><code>if response is empty

if response is not success
</code></pre>
<h2 id="returns">Returns</h2>
<p>str - String representation of console alerts if version is less than SP23
object - json response object for console alerts if version greater than or equal to SP23</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/alert.py#L582-L636" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def console_alerts(self, page_number=1, page_count=1):
    &#34;&#34;&#34;Returns the console alerts from page_number to the number of pages asked for page_count

        Args:
            page_number (int)  --  page number to get the alerts from

            page_count  (int)  --  number of pages to get the alerts of

        Raises:
            SDKException:
                if type of the page number and page count argument is not int

                if response is empty

                if response is not success

        Returns:
            str - String representation of console alerts if version is less than SP23
            object - json response object for console alerts if version greater than or equal to SP23
    &#34;&#34;&#34;
    if not (isinstance(page_number, int) and isinstance(page_count, int)):
        raise SDKException(&#39;Alert&#39;, &#39;101&#39;)

    console_alerts = self._services[&#39;GET_ALL_CONSOLE_ALERTS&#39;] % (
        page_number, page_count)

    flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, console_alerts)

    if flag:
        if response.json() and &#39;totalNoOfAlerts&#39; in response.json():
            if self._commcell_object.commserv_version &gt;= 23:
                return response.json()

            o_str = &#34;Total Console Alerts found: {0}&#34;.format(
                response.json()[&#39;totalNoOfAlerts&#39;]
            )

            o_str += &#34;\n{:^5}\t{:^50}\t{:^50}\t{:^50}\n\n&#34;.format(
                &#39;S. No.&#39;, &#39;Alert&#39;, &#39;Type&#39;, &#39;Criteria&#39;
            )

            for index, dictionary in enumerate(response.json()[&#39;feedsList&#39;]):
                o_str += &#39;{:^5}\t{:50}\t{:^50}\t{:^50}\n&#39;.format(
                    index + 1,
                    dictionary[&#39;alertName&#39;],
                    dictionary[&#39;alertType&#39;],
                    dictionary[&#39;alertcriteria&#39;]
                )

            return o_str
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.alert.Alerts.create_alert"><code class="name flex">
<span>def <span class="ident">create_alert</span></span>(<span>self, alert_dict)</span>
</code></dt>
<dd>
<div class="desc"><p>Creates a new Alert for CommCell</p>
<h2 id="args">Args</h2>
<p>alert_dict
(dict)
&ndash;
dictionary required to create an alert</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the Alert class for this new alert</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if input argument is not an instance of dict</p>
<pre><code>if alert with given name already exists

if failed to create an alert

if response is not success

if response is empty
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/alert.py#L452-L514" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def create_alert(self, alert_dict):
    &#34;&#34;&#34;Creates a new Alert for CommCell

    Args:
        alert_dict    (dict)  --  dictionary required to create an alert

    Returns:
        object  -  instance of the Alert class for this new alert

    Raises:
        SDKException:
            if input argument is not an instance of dict

            if alert with given name already exists

            if failed to create an alert

            if response is not success

            if response is empty
    &#34;&#34;&#34;
    if not isinstance(alert_dict, dict):
        raise SDKException(&#39;Alert&#39;, &#39;101&#39;)

    # required alert json
    alert_json = self._get_alert_json(alert_dict)
    alert_name = alert_json[&#34;alertDetail&#34;][&#34;alertrule&#34;][&#34;alertName&#34;]
    if self.has_alert(alert_name):
        raise SDKException(&#39;Alert&#39;, &#39;102&#39;, &#39;Alert &#34;{0}&#34; already exists.&#39;.
                           format(alert_name))

    post_alert = self._services[&#39;GET_ALL_ALERTS&#39;]
    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, post_alert, alert_json)

    if flag:
        if response.json():
            error_dict = response.json()[&#34;errorResp&#34;]
            error_code = str(error_dict[&#34;errorCode&#34;])

            if error_code == &#34;0&#34;:
                self.refresh()
                return self.get(alert_name)
            else:
                error_message = &#34;&#34;

                if &#39;errorMessage&#39; in error_dict:
                    error_message = error_dict[&#39;errorMessage&#39;]

                if error_message:
                    raise SDKException(
                        &#39;Alert&#39;, &#39;102&#39;, &#39;Failed to create Alert\nError: &#34;{}&#34;&#39;.format(
                            error_message
                        )
                    )
                else:
                    raise SDKException(
                        &#39;Alert&#39;, &#39;102&#39;, &#34;Failed to create Alert&#34;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.alert.Alerts.delete"><code class="name flex">
<span>def <span class="ident">delete</span></span>(<span>self, alert_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes the alert from the commcell.</p>
<h2 id="args">Args</h2>
<p>alert_name (str)
&ndash;
name of the alert</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the alert name argument is not string</p>
<pre><code>if failed to delete the alert

if no alert exists with the given name
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/alert.py#L672-L719" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete(self, alert_name):
    &#34;&#34;&#34;Deletes the alert from the commcell.

        Args:
            alert_name (str)  --  name of the alert

        Raises:
            SDKException:
                if type of the alert name argument is not string

                if failed to delete the alert

                if no alert exists with the given name
    &#34;&#34;&#34;
    if not isinstance(alert_name, str):
        raise SDKException(&#39;Alert&#39;, &#39;101&#39;)
    alert_name = alert_name.lower()

    if self.has_alert(alert_name):
        alert_id = self._alerts[alert_name][&#39;id&#39;]
        alert = self._services[&#39;ALERT&#39;] % (alert_id)

        flag, response = self._cvpysdk_object.make_request(
            &#39;DELETE&#39;, alert
        )

        if flag:
            if response.json():
                if &#39;errorCode&#39; in response.json():
                    if response.json()[&#39;errorCode&#39;] == 0:
                        # initialize the alerts again
                        # to refresh with the latest alerts
                        self.refresh()
                    else:
                        raise SDKException(&#39;Alert&#39;, &#39;102&#39;, response.json()[&#39;errorMessage&#39;])
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._update_response_(response.text)
            exception_message = &#39;Failed to delete alert\nError: &#34;{0}&#34;&#39;.format(
                response_string
            )

            raise SDKException(&#39;Alert&#39;, &#39;102&#39;, exception_message)
    else:
        raise SDKException(
            &#39;Alert&#39;, &#39;102&#39;, &#39;No alert exists with name: {0}&#39;.format(alert_name)
        )</code></pre>
</details>
</dd>
<dt id="cvpysdk.alert.Alerts.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, alert_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a alert object of the specified alert name.</p>
<h2 id="args">Args</h2>
<p>alert_name (str)
&ndash;
name of the alert</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Alert class for the given alert name</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the alert name argument is not string</p>
<pre><code>if no alert exists with the given name
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/alert.py#L553-L580" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, alert_name):
    &#34;&#34;&#34;Returns a alert object of the specified alert name.

        Args:
            alert_name (str)  --  name of the alert

        Returns:
            object - instance of the Alert class for the given alert name

        Raises:
            SDKException:
                if type of the alert name argument is not string

                if no alert exists with the given name
    &#34;&#34;&#34;
    if not isinstance(alert_name, str):
        raise SDKException(&#39;Alert&#39;, &#39;101&#39;)
    else:
        alert_name = alert_name.lower()

        if self.has_alert(alert_name):
            return Alert(
                self._commcell_object, alert_name,
                self._alerts[alert_name][&#39;id&#39;],
                self._alerts[alert_name][&#39;category&#39;]
            )

        raise SDKException(&#39;Alert&#39;, &#39;102&#39;, &#39;No Alert exists with name: {0}&#39;.format(alert_name))</code></pre>
</details>
</dd>
<dt id="cvpysdk.alert.Alerts.get_alert_sender"><code class="name flex">
<span>def <span class="ident">get_alert_sender</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the Alert Sender name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/alert.py#L433-L449" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_alert_sender(self):
    &#34;&#34;&#34;
        Returns the Alert Sender name
    &#34;&#34;&#34;
    get_alert = self._services[&#39;EMAIL_SERVER&#39;]
    flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, get_alert)
    if flag:
        if response.json():
            sender = response.json()[&#34;senderInfo&#34;][&#39;senderName&#39;]
            if not sender:
                sender = response.json()[&#34;senderInfo&#34;][&#39;senderAddress&#39;]
            return sender
        else:
            raise SDKException(&#39;Alert&#39;, &#39;102&#39;, &#34;Failed to get sender address&#34;)
    else:
        response_string = self._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.alert.Alerts.has_alert"><code class="name flex">
<span>def <span class="ident">has_alert</span></span>(<span>self, alert_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks if a alert exists for the commcell with the input alert name.</p>
<h2 id="args">Args</h2>
<p>alert_name (str)
&ndash;
name of the alert</p>
<h2 id="returns">Returns</h2>
<p>bool - boolean output whether the alert exists for the commcell or not</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the alert name argument is not string</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/alert.py#L535-L551" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_alert(self, alert_name):
    &#34;&#34;&#34;Checks if a alert exists for the commcell with the input alert name.

        Args:
            alert_name (str)  --  name of the alert

        Returns:
            bool - boolean output whether the alert exists for the commcell or not

        Raises:
            SDKException:
                if type of the alert name argument is not string
    &#34;&#34;&#34;
    if not isinstance(alert_name, str):
        raise SDKException(&#39;Alert&#39;, &#39;101&#39;)

    return self._alerts and alert_name.lower() in self._alerts</code></pre>
</details>
</dd>
<dt id="cvpysdk.alert.Alerts.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the alerts associated with the Commcell.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/alert.py#L721-L723" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the alerts associated with the Commcell.&#34;&#34;&#34;
    self._alerts = self._get_alerts()</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#alerts-attributes">Alerts Attributes</a></li>
<li><a href="#alert-attributes">Alert Attributes</a></li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.alert.Alert" href="#cvpysdk.alert.Alert">Alert</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.alert.Alert.alert_category" href="#cvpysdk.alert.Alert.alert_category">alert_category</a></code></li>
<li><code><a title="cvpysdk.alert.Alert.alert_criteria" href="#cvpysdk.alert.Alert.alert_criteria">alert_criteria</a></code></li>
<li><code><a title="cvpysdk.alert.Alert.alert_id" href="#cvpysdk.alert.Alert.alert_id">alert_id</a></code></li>
<li><code><a title="cvpysdk.alert.Alert.alert_name" href="#cvpysdk.alert.Alert.alert_name">alert_name</a></code></li>
<li><code><a title="cvpysdk.alert.Alert.alert_severity" href="#cvpysdk.alert.Alert.alert_severity">alert_severity</a></code></li>
<li><code><a title="cvpysdk.alert.Alert.alert_type" href="#cvpysdk.alert.Alert.alert_type">alert_type</a></code></li>
<li><code><a title="cvpysdk.alert.Alert.description" href="#cvpysdk.alert.Alert.description">description</a></code></li>
<li><code><a title="cvpysdk.alert.Alert.disable" href="#cvpysdk.alert.Alert.disable">disable</a></code></li>
<li><code><a title="cvpysdk.alert.Alert.disable_notification_type" href="#cvpysdk.alert.Alert.disable_notification_type">disable_notification_type</a></code></li>
<li><code><a title="cvpysdk.alert.Alert.email_recipients" href="#cvpysdk.alert.Alert.email_recipients">email_recipients</a></code></li>
<li><code><a title="cvpysdk.alert.Alert.enable" href="#cvpysdk.alert.Alert.enable">enable</a></code></li>
<li><code><a title="cvpysdk.alert.Alert.enable_notification_type" href="#cvpysdk.alert.Alert.enable_notification_type">enable_notification_type</a></code></li>
<li><code><a title="cvpysdk.alert.Alert.entities" href="#cvpysdk.alert.Alert.entities">entities</a></code></li>
<li><code><a title="cvpysdk.alert.Alert.name" href="#cvpysdk.alert.Alert.name">name</a></code></li>
<li><code><a title="cvpysdk.alert.Alert.notification_types" href="#cvpysdk.alert.Alert.notification_types">notification_types</a></code></li>
<li><code><a title="cvpysdk.alert.Alert.refresh" href="#cvpysdk.alert.Alert.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.alert.Alert.trigger_test_alert" href="#cvpysdk.alert.Alert.trigger_test_alert">trigger_test_alert</a></code></li>
<li><code><a title="cvpysdk.alert.Alert.user_group_list" href="#cvpysdk.alert.Alert.user_group_list">user_group_list</a></code></li>
<li><code><a title="cvpysdk.alert.Alert.users_list" href="#cvpysdk.alert.Alert.users_list">users_list</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.alert.Alerts" href="#cvpysdk.alert.Alerts">Alerts</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.alert.Alerts.all_alerts" href="#cvpysdk.alert.Alerts.all_alerts">all_alerts</a></code></li>
<li><code><a title="cvpysdk.alert.Alerts.console_alert" href="#cvpysdk.alert.Alerts.console_alert">console_alert</a></code></li>
<li><code><a title="cvpysdk.alert.Alerts.console_alerts" href="#cvpysdk.alert.Alerts.console_alerts">console_alerts</a></code></li>
<li><code><a title="cvpysdk.alert.Alerts.create_alert" href="#cvpysdk.alert.Alerts.create_alert">create_alert</a></code></li>
<li><code><a title="cvpysdk.alert.Alerts.delete" href="#cvpysdk.alert.Alerts.delete">delete</a></code></li>
<li><code><a title="cvpysdk.alert.Alerts.get" href="#cvpysdk.alert.Alerts.get">get</a></code></li>
<li><code><a title="cvpysdk.alert.Alerts.get_alert_sender" href="#cvpysdk.alert.Alerts.get_alert_sender">get_alert_sender</a></code></li>
<li><code><a title="cvpysdk.alert.Alerts.has_alert" href="#cvpysdk.alert.Alerts.has_alert">has_alert</a></code></li>
<li><code><a title="cvpysdk.alert.Alerts.refresh" href="#cvpysdk.alert.Alerts.refresh">refresh</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>