<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.drorchestration.dr_orchestration_job_phase API documentation</title>
<meta name="description" content="This file provides dicts.
One is from job phase code to text, and the other is from enum to code." />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.drorchestration.dr_orchestration_job_phase</code></h1>
</header>
<section id="section-intro">
<p>This file provides dicts.
One is from job phase code to text, and the other is from enum to code.</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/dr_orchestration_job_phase.py#L1-L121" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-
#
# This is generated by
#     \vaultcx\Source\tools\Automation\VirtualServer\VSAUtils\GenerateEnumFromDotX.py.
# Generated Time: 2020-02-12 16:09:03.897000.

&#34;&#34;&#34;This file provides dicts.
One is from job phase code to text, and the other is from enum to code.
&#34;&#34;&#34;
from enum import Enum, unique


@unique
class DRJobPhases(Enum):
    SCRIPT_EXECUTION = 0
    POWER_ON = 1
    POWER_OFF = 2
    REBOOT = 3
    GUEST_TOOLS_STATUS_CHECK = 4
    CREATE_NETWORK_SWITCH = 5
    DELETE_NETWORK_SWITCH = 6
    REPLICATION = 7
    BACKUP = 8
    CONFIGURE_NETWORK_SWITCH = 9
    EXTERNAL_PORT_CONFIG = 10
    IP_MASQUERADE = 11
    STATIC_IP_MAPPING = 12
    INTIALIZATION = 13
    WAIT_ON_IP_ASSIGNMENT = 14
    DISABLE_SYNC = 15
    ENABLE_SYNC = 16
    CREATE_SNAPSHOT = 17
    DELETE_SNAPSHOT = 18
    REVERT_SNAPSHOT = 19
    DISABLE_NETWORK_ADAPTER = 20
    AUX_COPY = 21
    POST_OPERATION = 22
    SNAP_TO_TAPE = 23
    SHUTDOWN = 24
    STORAGE_OPERATION = 25
    FINALIZE = 26
    DR_APPROVAL = 27
    DELETE_VM = 28
    CREATE_VM = 29
    GET_VM_INFO = 30
    CREATE_DR_VM = 31
    POST_VM_FAILOVER = 32
    MOUNT_SNAP_PRIMARY = 33
    DR_VM_SNAP_MOUNT = 34
    BREAK_VOLUME_RELATIONSHIP = 35
    RESYNC_VOLUME_RELATIONSHIP = 36
    DR_VM_SNAP_UNMOUNT = 37
    DR_TEST_VM_SNAP_MOUNT = 38
    DR_TEST_VM_SNAP_UNMOUNT = 39
    DELETE_DR_VM = 40
    POST_VM_FAILOVER_SNAP = 41
    CLONE_VM = 42
    REFRESH_VM = 43
    CHECK_VM_BOOT_STATUS = 44
    LIVE_MOUNT = 48
    INIT_UNDO_FAILOVER = 49
    POST_UNDO_FAILOVER = 50
    RESTORE_VM = 51
    PRE_SCRIPT_EXECUTION = 52
    POST_SCRIPT_EXECUTION = 53
    VM_LEVEL = 54


class DRJobPhaseToText(Enum):
    SCRIPT_EXECUTION = &#34;Script Execution&#34;
    POWER_ON = &#34;Power On&#34;
    POWER_OFF = &#34;Power Off&#34;
    REBOOT = &#34;Reboot&#34;
    GUEST_TOOLS_STATUS_CHECK = &#34;Guest Tools Status Check&#34;
    CREATE_NETWORK_SWITCH = &#34;Create Network Switch&#34;
    DELETE_NETWORK_SWITCH = &#34;Delete Network Switch&#34;
    REPLICATION = &#34;Replication&#34;
    BACKUP = &#34;Backup&#34;
    CONFIGURE_NETWORK_SWITCH = &#34;Configure Network Switch&#34;
    EXTERNAL_PORT_CONFIG = &#34;Port Configuration&#34;
    IP_MASQUERADE = &#34;IP Masquerading&#34;
    STATIC_IP_MAPPING = &#34;Static IP Mapping&#34;
    INTIALIZATION = &#34;Initialization&#34;
    WAIT_ON_IP_ASSIGNMENT = &#34;Waiting on IP Assignment&#34;
    DISABLE_SYNC = &#34;Disable Sync&#34;
    ENABLE_SYNC = &#34;Enable Sync&#34;
    CREATE_SNAPSHOT = &#34;Create Snapshot&#34;
    DELETE_SNAPSHOT = &#34;Delete Snapshot&#34;
    REVERT_SNAPSHOT = &#34;Revert Snapshot&#34;
    DISABLE_NETWORK_ADAPTER = &#34;Disable Network Adapter&#34;
    AUX_COPY = &#34;Auxiliary Copy&#34;
    POST_OPERATION = &#34;Post Operation&#34;
    SNAP_TO_TAPE = &#34;Backup Copy&#34;
    SHUTDOWN = &#34;Shutdown&#34;
    STORAGE_OPERATION = &#34;Storage Operation&#34;
    FINALIZE = &#34;Finalize&#34;
    DR_APPROVAL = &#34;DR Approval&#34;
    DELETE_VM = &#34;Delete VM&#34;
    CREATE_VM = &#34;Create VM&#34;
    GET_VM_INFO = &#34;Get VM Information&#34;
    CREATE_DR_VM = &#34;Create DR VM&#34;
    POST_VM_FAILOVER = &#34;Post VM Failover&#34;
    MOUNT_SNAP_PRIMARY = &#34;Mount Snap Primary&#34;
    DR_VM_SNAP_MOUNT = &#34;DR VM Snap Mount&#34;
    BREAK_VOLUME_RELATIONSHIP = &#34;Break Volume Relationship&#34;
    RESYNC_VOLUME_RELATIONSHIP = &#34;Resync Volume Relationship&#34;
    DR_VM_SNAP_UNMOUNT = &#34;DR VM Snap Unmount&#34;
    DR_TEST_VM_SNAP_MOUNT = &#34;DR Test VM Snap Mount&#34;
    DR_TEST_VM_SNAP_UNMOUNT = &#34;DR Test VM Snap Unmount&#34;
    DELETE_DR_VM = &#34;Delete DR VM&#34;
    POST_VM_FAILOVER_SNAP = &#34;Post VM Failover&#34;
    CLONE_VM = &#34;Clone VM&#34;
    REFRESH_VM = &#34;Refresh VM&#34;
    CHECK_VM_BOOT_STATUS = &#34;Check VM Boot Status&#34;
    LIVE_MOUNT = &#34;Live Mount&#34;
    INIT_UNDO_FAILOVER = &#34;Initialize Undo Failover&#34;
    POST_UNDO_FAILOVER = &#34;Post Undo Failover&#34;
    RESTORE_VM = &#34;Restore VM&#34;
    PRE_SCRIPT_EXECUTION = &#34;Pre Script Execution&#34;
    POST_SCRIPT_EXECUTION = &#34;Post Script Execution&#34;
    VM_LEVEL = &#34;VM Level&#34;</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText"><code class="flex name class">
<span>class <span class="ident">DRJobPhaseToText</span></span>
<span>(</span><span>value, names=None, *, module=None, qualname=None, type=None, start=1)</span>
</code></dt>
<dd>
<div class="desc"><p>An enumeration.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/dr_orchestration_job_phase.py#L69-L121" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class DRJobPhaseToText(Enum):
    SCRIPT_EXECUTION = &#34;Script Execution&#34;
    POWER_ON = &#34;Power On&#34;
    POWER_OFF = &#34;Power Off&#34;
    REBOOT = &#34;Reboot&#34;
    GUEST_TOOLS_STATUS_CHECK = &#34;Guest Tools Status Check&#34;
    CREATE_NETWORK_SWITCH = &#34;Create Network Switch&#34;
    DELETE_NETWORK_SWITCH = &#34;Delete Network Switch&#34;
    REPLICATION = &#34;Replication&#34;
    BACKUP = &#34;Backup&#34;
    CONFIGURE_NETWORK_SWITCH = &#34;Configure Network Switch&#34;
    EXTERNAL_PORT_CONFIG = &#34;Port Configuration&#34;
    IP_MASQUERADE = &#34;IP Masquerading&#34;
    STATIC_IP_MAPPING = &#34;Static IP Mapping&#34;
    INTIALIZATION = &#34;Initialization&#34;
    WAIT_ON_IP_ASSIGNMENT = &#34;Waiting on IP Assignment&#34;
    DISABLE_SYNC = &#34;Disable Sync&#34;
    ENABLE_SYNC = &#34;Enable Sync&#34;
    CREATE_SNAPSHOT = &#34;Create Snapshot&#34;
    DELETE_SNAPSHOT = &#34;Delete Snapshot&#34;
    REVERT_SNAPSHOT = &#34;Revert Snapshot&#34;
    DISABLE_NETWORK_ADAPTER = &#34;Disable Network Adapter&#34;
    AUX_COPY = &#34;Auxiliary Copy&#34;
    POST_OPERATION = &#34;Post Operation&#34;
    SNAP_TO_TAPE = &#34;Backup Copy&#34;
    SHUTDOWN = &#34;Shutdown&#34;
    STORAGE_OPERATION = &#34;Storage Operation&#34;
    FINALIZE = &#34;Finalize&#34;
    DR_APPROVAL = &#34;DR Approval&#34;
    DELETE_VM = &#34;Delete VM&#34;
    CREATE_VM = &#34;Create VM&#34;
    GET_VM_INFO = &#34;Get VM Information&#34;
    CREATE_DR_VM = &#34;Create DR VM&#34;
    POST_VM_FAILOVER = &#34;Post VM Failover&#34;
    MOUNT_SNAP_PRIMARY = &#34;Mount Snap Primary&#34;
    DR_VM_SNAP_MOUNT = &#34;DR VM Snap Mount&#34;
    BREAK_VOLUME_RELATIONSHIP = &#34;Break Volume Relationship&#34;
    RESYNC_VOLUME_RELATIONSHIP = &#34;Resync Volume Relationship&#34;
    DR_VM_SNAP_UNMOUNT = &#34;DR VM Snap Unmount&#34;
    DR_TEST_VM_SNAP_MOUNT = &#34;DR Test VM Snap Mount&#34;
    DR_TEST_VM_SNAP_UNMOUNT = &#34;DR Test VM Snap Unmount&#34;
    DELETE_DR_VM = &#34;Delete DR VM&#34;
    POST_VM_FAILOVER_SNAP = &#34;Post VM Failover&#34;
    CLONE_VM = &#34;Clone VM&#34;
    REFRESH_VM = &#34;Refresh VM&#34;
    CHECK_VM_BOOT_STATUS = &#34;Check VM Boot Status&#34;
    LIVE_MOUNT = &#34;Live Mount&#34;
    INIT_UNDO_FAILOVER = &#34;Initialize Undo Failover&#34;
    POST_UNDO_FAILOVER = &#34;Post Undo Failover&#34;
    RESTORE_VM = &#34;Restore VM&#34;
    PRE_SCRIPT_EXECUTION = &#34;Pre Script Execution&#34;
    POST_SCRIPT_EXECUTION = &#34;Post Script Execution&#34;
    VM_LEVEL = &#34;VM Level&#34;</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li>enum.Enum</li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.AUX_COPY"><code class="name">var <span class="ident">AUX_COPY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.BACKUP"><code class="name">var <span class="ident">BACKUP</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.BREAK_VOLUME_RELATIONSHIP"><code class="name">var <span class="ident">BREAK_VOLUME_RELATIONSHIP</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.CHECK_VM_BOOT_STATUS"><code class="name">var <span class="ident">CHECK_VM_BOOT_STATUS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.CLONE_VM"><code class="name">var <span class="ident">CLONE_VM</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.CONFIGURE_NETWORK_SWITCH"><code class="name">var <span class="ident">CONFIGURE_NETWORK_SWITCH</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.CREATE_DR_VM"><code class="name">var <span class="ident">CREATE_DR_VM</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.CREATE_NETWORK_SWITCH"><code class="name">var <span class="ident">CREATE_NETWORK_SWITCH</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.CREATE_SNAPSHOT"><code class="name">var <span class="ident">CREATE_SNAPSHOT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.CREATE_VM"><code class="name">var <span class="ident">CREATE_VM</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.DELETE_DR_VM"><code class="name">var <span class="ident">DELETE_DR_VM</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.DELETE_NETWORK_SWITCH"><code class="name">var <span class="ident">DELETE_NETWORK_SWITCH</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.DELETE_SNAPSHOT"><code class="name">var <span class="ident">DELETE_SNAPSHOT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.DELETE_VM"><code class="name">var <span class="ident">DELETE_VM</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.DISABLE_NETWORK_ADAPTER"><code class="name">var <span class="ident">DISABLE_NETWORK_ADAPTER</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.DISABLE_SYNC"><code class="name">var <span class="ident">DISABLE_SYNC</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.DR_APPROVAL"><code class="name">var <span class="ident">DR_APPROVAL</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.DR_TEST_VM_SNAP_MOUNT"><code class="name">var <span class="ident">DR_TEST_VM_SNAP_MOUNT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.DR_TEST_VM_SNAP_UNMOUNT"><code class="name">var <span class="ident">DR_TEST_VM_SNAP_UNMOUNT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.DR_VM_SNAP_MOUNT"><code class="name">var <span class="ident">DR_VM_SNAP_MOUNT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.DR_VM_SNAP_UNMOUNT"><code class="name">var <span class="ident">DR_VM_SNAP_UNMOUNT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.ENABLE_SYNC"><code class="name">var <span class="ident">ENABLE_SYNC</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.EXTERNAL_PORT_CONFIG"><code class="name">var <span class="ident">EXTERNAL_PORT_CONFIG</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.FINALIZE"><code class="name">var <span class="ident">FINALIZE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.GET_VM_INFO"><code class="name">var <span class="ident">GET_VM_INFO</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.GUEST_TOOLS_STATUS_CHECK"><code class="name">var <span class="ident">GUEST_TOOLS_STATUS_CHECK</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.INIT_UNDO_FAILOVER"><code class="name">var <span class="ident">INIT_UNDO_FAILOVER</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.INTIALIZATION"><code class="name">var <span class="ident">INTIALIZATION</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.IP_MASQUERADE"><code class="name">var <span class="ident">IP_MASQUERADE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.LIVE_MOUNT"><code class="name">var <span class="ident">LIVE_MOUNT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.MOUNT_SNAP_PRIMARY"><code class="name">var <span class="ident">MOUNT_SNAP_PRIMARY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.POST_OPERATION"><code class="name">var <span class="ident">POST_OPERATION</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.POST_SCRIPT_EXECUTION"><code class="name">var <span class="ident">POST_SCRIPT_EXECUTION</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.POST_UNDO_FAILOVER"><code class="name">var <span class="ident">POST_UNDO_FAILOVER</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.POST_VM_FAILOVER"><code class="name">var <span class="ident">POST_VM_FAILOVER</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.POST_VM_FAILOVER_SNAP"><code class="name">var <span class="ident">POST_VM_FAILOVER_SNAP</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.POWER_OFF"><code class="name">var <span class="ident">POWER_OFF</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.POWER_ON"><code class="name">var <span class="ident">POWER_ON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.PRE_SCRIPT_EXECUTION"><code class="name">var <span class="ident">PRE_SCRIPT_EXECUTION</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.REBOOT"><code class="name">var <span class="ident">REBOOT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.REFRESH_VM"><code class="name">var <span class="ident">REFRESH_VM</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.REPLICATION"><code class="name">var <span class="ident">REPLICATION</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.RESTORE_VM"><code class="name">var <span class="ident">RESTORE_VM</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.RESYNC_VOLUME_RELATIONSHIP"><code class="name">var <span class="ident">RESYNC_VOLUME_RELATIONSHIP</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.REVERT_SNAPSHOT"><code class="name">var <span class="ident">REVERT_SNAPSHOT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.SCRIPT_EXECUTION"><code class="name">var <span class="ident">SCRIPT_EXECUTION</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.SHUTDOWN"><code class="name">var <span class="ident">SHUTDOWN</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.SNAP_TO_TAPE"><code class="name">var <span class="ident">SNAP_TO_TAPE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.STATIC_IP_MAPPING"><code class="name">var <span class="ident">STATIC_IP_MAPPING</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.STORAGE_OPERATION"><code class="name">var <span class="ident">STORAGE_OPERATION</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.VM_LEVEL"><code class="name">var <span class="ident">VM_LEVEL</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.WAIT_ON_IP_ASSIGNMENT"><code class="name">var <span class="ident">WAIT_ON_IP_ASSIGNMENT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases"><code class="flex name class">
<span>class <span class="ident">DRJobPhases</span></span>
<span>(</span><span>value, names=None, *, module=None, qualname=None, type=None, start=1)</span>
</code></dt>
<dd>
<div class="desc"><p>An enumeration.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/dr_orchestration_job_phase.py#L13-L66" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@unique
class DRJobPhases(Enum):
    SCRIPT_EXECUTION = 0
    POWER_ON = 1
    POWER_OFF = 2
    REBOOT = 3
    GUEST_TOOLS_STATUS_CHECK = 4
    CREATE_NETWORK_SWITCH = 5
    DELETE_NETWORK_SWITCH = 6
    REPLICATION = 7
    BACKUP = 8
    CONFIGURE_NETWORK_SWITCH = 9
    EXTERNAL_PORT_CONFIG = 10
    IP_MASQUERADE = 11
    STATIC_IP_MAPPING = 12
    INTIALIZATION = 13
    WAIT_ON_IP_ASSIGNMENT = 14
    DISABLE_SYNC = 15
    ENABLE_SYNC = 16
    CREATE_SNAPSHOT = 17
    DELETE_SNAPSHOT = 18
    REVERT_SNAPSHOT = 19
    DISABLE_NETWORK_ADAPTER = 20
    AUX_COPY = 21
    POST_OPERATION = 22
    SNAP_TO_TAPE = 23
    SHUTDOWN = 24
    STORAGE_OPERATION = 25
    FINALIZE = 26
    DR_APPROVAL = 27
    DELETE_VM = 28
    CREATE_VM = 29
    GET_VM_INFO = 30
    CREATE_DR_VM = 31
    POST_VM_FAILOVER = 32
    MOUNT_SNAP_PRIMARY = 33
    DR_VM_SNAP_MOUNT = 34
    BREAK_VOLUME_RELATIONSHIP = 35
    RESYNC_VOLUME_RELATIONSHIP = 36
    DR_VM_SNAP_UNMOUNT = 37
    DR_TEST_VM_SNAP_MOUNT = 38
    DR_TEST_VM_SNAP_UNMOUNT = 39
    DELETE_DR_VM = 40
    POST_VM_FAILOVER_SNAP = 41
    CLONE_VM = 42
    REFRESH_VM = 43
    CHECK_VM_BOOT_STATUS = 44
    LIVE_MOUNT = 48
    INIT_UNDO_FAILOVER = 49
    POST_UNDO_FAILOVER = 50
    RESTORE_VM = 51
    PRE_SCRIPT_EXECUTION = 52
    POST_SCRIPT_EXECUTION = 53
    VM_LEVEL = 54</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li>enum.Enum</li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.AUX_COPY"><code class="name">var <span class="ident">AUX_COPY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.BACKUP"><code class="name">var <span class="ident">BACKUP</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.BREAK_VOLUME_RELATIONSHIP"><code class="name">var <span class="ident">BREAK_VOLUME_RELATIONSHIP</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.CHECK_VM_BOOT_STATUS"><code class="name">var <span class="ident">CHECK_VM_BOOT_STATUS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.CLONE_VM"><code class="name">var <span class="ident">CLONE_VM</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.CONFIGURE_NETWORK_SWITCH"><code class="name">var <span class="ident">CONFIGURE_NETWORK_SWITCH</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.CREATE_DR_VM"><code class="name">var <span class="ident">CREATE_DR_VM</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.CREATE_NETWORK_SWITCH"><code class="name">var <span class="ident">CREATE_NETWORK_SWITCH</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.CREATE_SNAPSHOT"><code class="name">var <span class="ident">CREATE_SNAPSHOT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.CREATE_VM"><code class="name">var <span class="ident">CREATE_VM</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.DELETE_DR_VM"><code class="name">var <span class="ident">DELETE_DR_VM</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.DELETE_NETWORK_SWITCH"><code class="name">var <span class="ident">DELETE_NETWORK_SWITCH</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.DELETE_SNAPSHOT"><code class="name">var <span class="ident">DELETE_SNAPSHOT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.DELETE_VM"><code class="name">var <span class="ident">DELETE_VM</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.DISABLE_NETWORK_ADAPTER"><code class="name">var <span class="ident">DISABLE_NETWORK_ADAPTER</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.DISABLE_SYNC"><code class="name">var <span class="ident">DISABLE_SYNC</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.DR_APPROVAL"><code class="name">var <span class="ident">DR_APPROVAL</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.DR_TEST_VM_SNAP_MOUNT"><code class="name">var <span class="ident">DR_TEST_VM_SNAP_MOUNT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.DR_TEST_VM_SNAP_UNMOUNT"><code class="name">var <span class="ident">DR_TEST_VM_SNAP_UNMOUNT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.DR_VM_SNAP_MOUNT"><code class="name">var <span class="ident">DR_VM_SNAP_MOUNT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.DR_VM_SNAP_UNMOUNT"><code class="name">var <span class="ident">DR_VM_SNAP_UNMOUNT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.ENABLE_SYNC"><code class="name">var <span class="ident">ENABLE_SYNC</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.EXTERNAL_PORT_CONFIG"><code class="name">var <span class="ident">EXTERNAL_PORT_CONFIG</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.FINALIZE"><code class="name">var <span class="ident">FINALIZE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.GET_VM_INFO"><code class="name">var <span class="ident">GET_VM_INFO</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.GUEST_TOOLS_STATUS_CHECK"><code class="name">var <span class="ident">GUEST_TOOLS_STATUS_CHECK</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.INIT_UNDO_FAILOVER"><code class="name">var <span class="ident">INIT_UNDO_FAILOVER</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.INTIALIZATION"><code class="name">var <span class="ident">INTIALIZATION</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.IP_MASQUERADE"><code class="name">var <span class="ident">IP_MASQUERADE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.LIVE_MOUNT"><code class="name">var <span class="ident">LIVE_MOUNT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.MOUNT_SNAP_PRIMARY"><code class="name">var <span class="ident">MOUNT_SNAP_PRIMARY</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.POST_OPERATION"><code class="name">var <span class="ident">POST_OPERATION</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.POST_SCRIPT_EXECUTION"><code class="name">var <span class="ident">POST_SCRIPT_EXECUTION</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.POST_UNDO_FAILOVER"><code class="name">var <span class="ident">POST_UNDO_FAILOVER</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.POST_VM_FAILOVER"><code class="name">var <span class="ident">POST_VM_FAILOVER</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.POST_VM_FAILOVER_SNAP"><code class="name">var <span class="ident">POST_VM_FAILOVER_SNAP</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.POWER_OFF"><code class="name">var <span class="ident">POWER_OFF</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.POWER_ON"><code class="name">var <span class="ident">POWER_ON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.PRE_SCRIPT_EXECUTION"><code class="name">var <span class="ident">PRE_SCRIPT_EXECUTION</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.REBOOT"><code class="name">var <span class="ident">REBOOT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.REFRESH_VM"><code class="name">var <span class="ident">REFRESH_VM</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.REPLICATION"><code class="name">var <span class="ident">REPLICATION</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.RESTORE_VM"><code class="name">var <span class="ident">RESTORE_VM</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.RESYNC_VOLUME_RELATIONSHIP"><code class="name">var <span class="ident">RESYNC_VOLUME_RELATIONSHIP</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.REVERT_SNAPSHOT"><code class="name">var <span class="ident">REVERT_SNAPSHOT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.SCRIPT_EXECUTION"><code class="name">var <span class="ident">SCRIPT_EXECUTION</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.SHUTDOWN"><code class="name">var <span class="ident">SHUTDOWN</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.SNAP_TO_TAPE"><code class="name">var <span class="ident">SNAP_TO_TAPE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.STATIC_IP_MAPPING"><code class="name">var <span class="ident">STATIC_IP_MAPPING</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.STORAGE_OPERATION"><code class="name">var <span class="ident">STORAGE_OPERATION</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.VM_LEVEL"><code class="name">var <span class="ident">VM_LEVEL</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.WAIT_ON_IP_ASSIGNMENT"><code class="name">var <span class="ident">WAIT_ON_IP_ASSIGNMENT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.drorchestration" href="index.html">cvpysdk.drorchestration</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText">DRJobPhaseToText</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.AUX_COPY" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.AUX_COPY">AUX_COPY</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.BACKUP" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.BACKUP">BACKUP</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.BREAK_VOLUME_RELATIONSHIP" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.BREAK_VOLUME_RELATIONSHIP">BREAK_VOLUME_RELATIONSHIP</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.CHECK_VM_BOOT_STATUS" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.CHECK_VM_BOOT_STATUS">CHECK_VM_BOOT_STATUS</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.CLONE_VM" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.CLONE_VM">CLONE_VM</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.CONFIGURE_NETWORK_SWITCH" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.CONFIGURE_NETWORK_SWITCH">CONFIGURE_NETWORK_SWITCH</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.CREATE_DR_VM" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.CREATE_DR_VM">CREATE_DR_VM</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.CREATE_NETWORK_SWITCH" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.CREATE_NETWORK_SWITCH">CREATE_NETWORK_SWITCH</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.CREATE_SNAPSHOT" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.CREATE_SNAPSHOT">CREATE_SNAPSHOT</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.CREATE_VM" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.CREATE_VM">CREATE_VM</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.DELETE_DR_VM" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.DELETE_DR_VM">DELETE_DR_VM</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.DELETE_NETWORK_SWITCH" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.DELETE_NETWORK_SWITCH">DELETE_NETWORK_SWITCH</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.DELETE_SNAPSHOT" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.DELETE_SNAPSHOT">DELETE_SNAPSHOT</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.DELETE_VM" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.DELETE_VM">DELETE_VM</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.DISABLE_NETWORK_ADAPTER" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.DISABLE_NETWORK_ADAPTER">DISABLE_NETWORK_ADAPTER</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.DISABLE_SYNC" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.DISABLE_SYNC">DISABLE_SYNC</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.DR_APPROVAL" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.DR_APPROVAL">DR_APPROVAL</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.DR_TEST_VM_SNAP_MOUNT" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.DR_TEST_VM_SNAP_MOUNT">DR_TEST_VM_SNAP_MOUNT</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.DR_TEST_VM_SNAP_UNMOUNT" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.DR_TEST_VM_SNAP_UNMOUNT">DR_TEST_VM_SNAP_UNMOUNT</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.DR_VM_SNAP_MOUNT" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.DR_VM_SNAP_MOUNT">DR_VM_SNAP_MOUNT</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.DR_VM_SNAP_UNMOUNT" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.DR_VM_SNAP_UNMOUNT">DR_VM_SNAP_UNMOUNT</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.ENABLE_SYNC" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.ENABLE_SYNC">ENABLE_SYNC</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.EXTERNAL_PORT_CONFIG" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.EXTERNAL_PORT_CONFIG">EXTERNAL_PORT_CONFIG</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.FINALIZE" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.FINALIZE">FINALIZE</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.GET_VM_INFO" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.GET_VM_INFO">GET_VM_INFO</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.GUEST_TOOLS_STATUS_CHECK" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.GUEST_TOOLS_STATUS_CHECK">GUEST_TOOLS_STATUS_CHECK</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.INIT_UNDO_FAILOVER" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.INIT_UNDO_FAILOVER">INIT_UNDO_FAILOVER</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.INTIALIZATION" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.INTIALIZATION">INTIALIZATION</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.IP_MASQUERADE" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.IP_MASQUERADE">IP_MASQUERADE</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.LIVE_MOUNT" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.LIVE_MOUNT">LIVE_MOUNT</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.MOUNT_SNAP_PRIMARY" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.MOUNT_SNAP_PRIMARY">MOUNT_SNAP_PRIMARY</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.POST_OPERATION" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.POST_OPERATION">POST_OPERATION</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.POST_SCRIPT_EXECUTION" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.POST_SCRIPT_EXECUTION">POST_SCRIPT_EXECUTION</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.POST_UNDO_FAILOVER" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.POST_UNDO_FAILOVER">POST_UNDO_FAILOVER</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.POST_VM_FAILOVER" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.POST_VM_FAILOVER">POST_VM_FAILOVER</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.POST_VM_FAILOVER_SNAP" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.POST_VM_FAILOVER_SNAP">POST_VM_FAILOVER_SNAP</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.POWER_OFF" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.POWER_OFF">POWER_OFF</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.POWER_ON" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.POWER_ON">POWER_ON</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.PRE_SCRIPT_EXECUTION" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.PRE_SCRIPT_EXECUTION">PRE_SCRIPT_EXECUTION</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.REBOOT" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.REBOOT">REBOOT</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.REFRESH_VM" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.REFRESH_VM">REFRESH_VM</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.REPLICATION" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.REPLICATION">REPLICATION</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.RESTORE_VM" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.RESTORE_VM">RESTORE_VM</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.RESYNC_VOLUME_RELATIONSHIP" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.RESYNC_VOLUME_RELATIONSHIP">RESYNC_VOLUME_RELATIONSHIP</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.REVERT_SNAPSHOT" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.REVERT_SNAPSHOT">REVERT_SNAPSHOT</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.SCRIPT_EXECUTION" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.SCRIPT_EXECUTION">SCRIPT_EXECUTION</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.SHUTDOWN" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.SHUTDOWN">SHUTDOWN</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.SNAP_TO_TAPE" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.SNAP_TO_TAPE">SNAP_TO_TAPE</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.STATIC_IP_MAPPING" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.STATIC_IP_MAPPING">STATIC_IP_MAPPING</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.STORAGE_OPERATION" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.STORAGE_OPERATION">STORAGE_OPERATION</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.VM_LEVEL" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.VM_LEVEL">VM_LEVEL</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.WAIT_ON_IP_ASSIGNMENT" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhaseToText.WAIT_ON_IP_ASSIGNMENT">WAIT_ON_IP_ASSIGNMENT</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases">DRJobPhases</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.AUX_COPY" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.AUX_COPY">AUX_COPY</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.BACKUP" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.BACKUP">BACKUP</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.BREAK_VOLUME_RELATIONSHIP" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.BREAK_VOLUME_RELATIONSHIP">BREAK_VOLUME_RELATIONSHIP</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.CHECK_VM_BOOT_STATUS" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.CHECK_VM_BOOT_STATUS">CHECK_VM_BOOT_STATUS</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.CLONE_VM" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.CLONE_VM">CLONE_VM</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.CONFIGURE_NETWORK_SWITCH" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.CONFIGURE_NETWORK_SWITCH">CONFIGURE_NETWORK_SWITCH</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.CREATE_DR_VM" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.CREATE_DR_VM">CREATE_DR_VM</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.CREATE_NETWORK_SWITCH" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.CREATE_NETWORK_SWITCH">CREATE_NETWORK_SWITCH</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.CREATE_SNAPSHOT" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.CREATE_SNAPSHOT">CREATE_SNAPSHOT</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.CREATE_VM" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.CREATE_VM">CREATE_VM</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.DELETE_DR_VM" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.DELETE_DR_VM">DELETE_DR_VM</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.DELETE_NETWORK_SWITCH" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.DELETE_NETWORK_SWITCH">DELETE_NETWORK_SWITCH</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.DELETE_SNAPSHOT" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.DELETE_SNAPSHOT">DELETE_SNAPSHOT</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.DELETE_VM" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.DELETE_VM">DELETE_VM</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.DISABLE_NETWORK_ADAPTER" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.DISABLE_NETWORK_ADAPTER">DISABLE_NETWORK_ADAPTER</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.DISABLE_SYNC" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.DISABLE_SYNC">DISABLE_SYNC</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.DR_APPROVAL" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.DR_APPROVAL">DR_APPROVAL</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.DR_TEST_VM_SNAP_MOUNT" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.DR_TEST_VM_SNAP_MOUNT">DR_TEST_VM_SNAP_MOUNT</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.DR_TEST_VM_SNAP_UNMOUNT" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.DR_TEST_VM_SNAP_UNMOUNT">DR_TEST_VM_SNAP_UNMOUNT</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.DR_VM_SNAP_MOUNT" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.DR_VM_SNAP_MOUNT">DR_VM_SNAP_MOUNT</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.DR_VM_SNAP_UNMOUNT" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.DR_VM_SNAP_UNMOUNT">DR_VM_SNAP_UNMOUNT</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.ENABLE_SYNC" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.ENABLE_SYNC">ENABLE_SYNC</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.EXTERNAL_PORT_CONFIG" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.EXTERNAL_PORT_CONFIG">EXTERNAL_PORT_CONFIG</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.FINALIZE" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.FINALIZE">FINALIZE</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.GET_VM_INFO" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.GET_VM_INFO">GET_VM_INFO</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.GUEST_TOOLS_STATUS_CHECK" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.GUEST_TOOLS_STATUS_CHECK">GUEST_TOOLS_STATUS_CHECK</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.INIT_UNDO_FAILOVER" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.INIT_UNDO_FAILOVER">INIT_UNDO_FAILOVER</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.INTIALIZATION" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.INTIALIZATION">INTIALIZATION</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.IP_MASQUERADE" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.IP_MASQUERADE">IP_MASQUERADE</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.LIVE_MOUNT" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.LIVE_MOUNT">LIVE_MOUNT</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.MOUNT_SNAP_PRIMARY" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.MOUNT_SNAP_PRIMARY">MOUNT_SNAP_PRIMARY</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.POST_OPERATION" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.POST_OPERATION">POST_OPERATION</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.POST_SCRIPT_EXECUTION" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.POST_SCRIPT_EXECUTION">POST_SCRIPT_EXECUTION</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.POST_UNDO_FAILOVER" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.POST_UNDO_FAILOVER">POST_UNDO_FAILOVER</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.POST_VM_FAILOVER" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.POST_VM_FAILOVER">POST_VM_FAILOVER</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.POST_VM_FAILOVER_SNAP" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.POST_VM_FAILOVER_SNAP">POST_VM_FAILOVER_SNAP</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.POWER_OFF" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.POWER_OFF">POWER_OFF</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.POWER_ON" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.POWER_ON">POWER_ON</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.PRE_SCRIPT_EXECUTION" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.PRE_SCRIPT_EXECUTION">PRE_SCRIPT_EXECUTION</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.REBOOT" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.REBOOT">REBOOT</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.REFRESH_VM" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.REFRESH_VM">REFRESH_VM</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.REPLICATION" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.REPLICATION">REPLICATION</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.RESTORE_VM" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.RESTORE_VM">RESTORE_VM</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.RESYNC_VOLUME_RELATIONSHIP" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.RESYNC_VOLUME_RELATIONSHIP">RESYNC_VOLUME_RELATIONSHIP</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.REVERT_SNAPSHOT" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.REVERT_SNAPSHOT">REVERT_SNAPSHOT</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.SCRIPT_EXECUTION" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.SCRIPT_EXECUTION">SCRIPT_EXECUTION</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.SHUTDOWN" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.SHUTDOWN">SHUTDOWN</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.SNAP_TO_TAPE" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.SNAP_TO_TAPE">SNAP_TO_TAPE</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.STATIC_IP_MAPPING" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.STATIC_IP_MAPPING">STATIC_IP_MAPPING</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.STORAGE_OPERATION" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.STORAGE_OPERATION">STORAGE_OPERATION</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.VM_LEVEL" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.VM_LEVEL">VM_LEVEL</a></code></li>
<li><code><a title="cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.WAIT_ON_IP_ASSIGNMENT" href="#cvpysdk.drorchestration.dr_orchestration_job_phase.DRJobPhases.WAIT_ON_IP_ASSIGNMENT">WAIT_ON_IP_ASSIGNMENT</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>