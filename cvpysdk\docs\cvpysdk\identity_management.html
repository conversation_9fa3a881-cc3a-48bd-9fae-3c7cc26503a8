<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.identity_management API documentation</title>
<meta name="description" content="Main file for performing identity management operations …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.identity_management</code></h1>
</header>
<section id="section-intro">
<p>Main file for performing identity management operations</p>
<p>IdentityManagementApps, IdentityManagementApp and SamlApp are the classes defined in this file</p>
<p>IdentityManagementApps: Class for representing all the identity management apps in the commcell</p>
<p>IdentityManagementApp: Class for representing a single identity management app in the commcell</p>
<p>SamlApp: class for representing a single saml app in commcell</p>
<h1 id="identitymanagementapps">IdentityManagementApps</h1>
<pre><code>__init__(commcell_object)       --  initialise object of identity management apps
class of the commcell

__str__()                       --  returns all the apps identity management apps
in the commcell

__repr__()                      --  returns the string for the instance of the
identity management apps

_get_apps()                     --  gets all the identity management appsin the commcell

get_local_identity_app          --  gets the local identity app of the commcell

get_commcell_identity_apps      --  gets the list of commcell identity apps of the commcell

delete_identity_app()           --  deletes the specified local identity app

delete_saml_app()               --  deletes the specified saml app

get_saml()                      --  returns instance of SamlApp class

configure_saml_app()            --  creates a saml app

configure_local_identity_app()  --  sets up the local identity app for the specified commcell

configure_commcell_app()        --  creates a commcell identity app for the specified commcell

configure_openid_app()          --  creates a OpenID app for the specified commcell

refresh()                       --  refresh the apps in the commcell
</code></pre>
<h1 id="identitymanagementapp">IdentityManagementApp</h1>
<pre><code>__init__()                  --  initialize instance of the IdentityManagementApp instance

__repr__()                  -- return the appname name, the instance it is associated with

_get_app_key()              -- gets the app key

_get_app_details()          --  gets the details of the identity management app

get_app_props()             -- returns a dict containing the properties of a third party app

refresh()                   -- refresh the details of the app
</code></pre>
<h1 id="samlapp">SamlApp</h1>
<pre><code>__init__()                   --  initialize instance of the SamlApp instance

__repr__()                   -- return the appname name, the instance it is associated with

_get_saml_details()          -- gets details of saml app

modify_saml_app()            --  modifies saml app

refresh()                    -- refresh the details of the saml app

saml_app_details()           --  gets saml app details in dict

get_saml_user_redirect_url() -- gets redirect url of saml user

set_auto_redirect_to_idp()   -- Enable/Disable auto redirect to IDP
</code></pre>
<h1 id="samlapp-instance-attributes">SamlApp instance Attributes</h1>
<pre><code>**is_saml_app_enabled**         --  returns True if saml app is enabled, False otherwise

**is_auto_create_user**         --  returns True if auto create user flag is enabled, False otherwise

**saml_app_default_user_groups** -  returns list of dict of default usergroups of saml app

**saml_app_nameid_attribute**   --  returns value of NameId attribute of saml app

**saml_app_attribute_mappings** --  returns attribute mappings of saml app

**saml_app_identity_provider_metadata** -   returns IDP metadata of saml app

**saml_app_service_provider_metadata**  -   returns SP metadata of saml app

**saml_app_associations**       --  returns saml app associations

**is_company_saml_app**         -- returns True if saml app is created for a company, False otherwise
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L1-L1129" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Main file for performing identity management operations

IdentityManagementApps, IdentityManagementApp and SamlApp are the classes defined in this file

IdentityManagementApps: Class for representing all the identity management apps in the commcell

IdentityManagementApp: Class for representing a single identity management app in the commcell

SamlApp: class for representing a single saml app in commcell

IdentityManagementApps
======================

    __init__(commcell_object)       --  initialise object of identity management apps
    class of the commcell

    __str__()                       --  returns all the apps identity management apps
    in the commcell

    __repr__()                      --  returns the string for the instance of the
    identity management apps

    _get_apps()                     --  gets all the identity management appsin the commcell

    get_local_identity_app          --  gets the local identity app of the commcell

    get_commcell_identity_apps      --  gets the list of commcell identity apps of the commcell

    delete_identity_app()           --  deletes the specified local identity app

    delete_saml_app()               --  deletes the specified saml app

    get_saml()                      --  returns instance of SamlApp class

    configure_saml_app()            --  creates a saml app

    configure_local_identity_app()  --  sets up the local identity app for the specified commcell

    configure_commcell_app()        --  creates a commcell identity app for the specified commcell

    configure_openid_app()          --  creates a OpenID app for the specified commcell

    refresh()                       --  refresh the apps in the commcell


IdentityManagementApp
======================
    __init__()                  --  initialize instance of the IdentityManagementApp instance

    __repr__()                  -- return the appname name, the instance it is associated with

    _get_app_key()              -- gets the app key

    _get_app_details()          --  gets the details of the identity management app

    get_app_props()             -- returns a dict containing the properties of a third party app

    refresh()                   -- refresh the details of the app


SamlApp
======================
    __init__()                   --  initialize instance of the SamlApp instance

    __repr__()                   -- return the appname name, the instance it is associated with

    _get_saml_details()          -- gets details of saml app

    modify_saml_app()            --  modifies saml app

    refresh()                    -- refresh the details of the saml app

    saml_app_details()           --  gets saml app details in dict

    get_saml_user_redirect_url() -- gets redirect url of saml user

    set_auto_redirect_to_idp()   -- Enable/Disable auto redirect to IDP


SamlApp instance Attributes
============================

    **is_saml_app_enabled**         --  returns True if saml app is enabled, False otherwise

    **is_auto_create_user**         --  returns True if auto create user flag is enabled, False otherwise

    **saml_app_default_user_groups** -  returns list of dict of default usergroups of saml app

    **saml_app_nameid_attribute**   --  returns value of NameId attribute of saml app

    **saml_app_attribute_mappings** --  returns attribute mappings of saml app

    **saml_app_identity_provider_metadata** -   returns IDP metadata of saml app

    **saml_app_service_provider_metadata**  -   returns SP metadata of saml app

    **saml_app_associations**       --  returns saml app associations

    **is_company_saml_app**         -- returns True if saml app is created for a company, False otherwise
&#34;&#34;&#34;

import xml.etree.ElementTree as ET
from .exception import SDKException


class IdentityManagementApps(object):
    &#34;&#34;&#34;Class for representing third party apps in the commcell&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of third party apps class.

            Args:
                commcell_object    (object)    --  instance of the Commcell class

            Returns:
                object - instance of ThirdPartyApps class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._update_response_ = commcell_object._update_response_
        self._APPS = commcell_object._services[&#39;IDENTITY_APPS&#39;]
        self._ADD_SAML = commcell_object._services[&#39;ADD_OR_GET_SAML&#39;]
        self._SAML = commcell_object._services[&#39;EDIT_SAML&#39;]
        self._apps = None
        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all identity management apps of the Commcell.

            Returns:
                str -   string of all the identity management apps in a commcell
        &#34;&#34;&#34;
        representation_string = &#34;{:^5}\t{:^50}\n\n&#34;.format(&#39;S. No.&#39;, &#39;App&#39;)

        for index, app in enumerate(self._apps):
            sub_str = &#39;{:^5}\t{:30}\n&#39;.format(index + 1, app)
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the IdentityManagementApps class.&#34;&#34;&#34;
        return &#34;IdentityManagementApps class instance for Commcell&#34;

    def __len__(self):
        &#34;&#34;&#34;Returns the number of the app added to the Commcell.&#34;&#34;&#34;
        return len(self.all_apps)

    def _get_apps(self, hard=False):
        &#34;&#34;&#34;Gets list of all third party apps.

            Returns:
                dict - consisits of all thrid party apps in the commcell
                        {
                            &#39;app1_name&#39;: {
                                &#39;appKey&#39;: app1_key,
                                &#39;appType&#39;: app1_type,
                                &#39;appDescription&#39;: &#39;app1_description&#39;,
                                &#39;flags&#39;: &#39;app1_flags&#39;,
                                &#39;isEnabled&#39;: &#39;app1_isEnabled&#39;
                            },
                            &#39;app2_name&#39;: {
                                &#39;appKey&#39;: app2_key,
                                &#39;appType&#39;: app2_type,
                                &#39;appDescription&#39;: &#39;app1_description&#39;,
                                &#39;flags&#39;: &#39;app1_flags&#39;,
                                &#39;isEnabled&#39;: &#39;app1_isEnabled&#39;
                            }
                        }

            Raises:
                SDKException:
                        if response is not success
        &#34;&#34;&#34;
        if hard:
            self._cvpysdk_object.make_request(
                &#39;GET&#39;, self._APPS + &#39;?hardRefresh=true&#39;
            )
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._APPS
        )

        if flag:
            apps = {}

            if response.json() and &#39;clientThirdPartyApps&#39; in response.json():
                response_value = response.json()[&#39;clientThirdPartyApps&#39;]

                for app in response_value:
                    apps[app[&#39;appName&#39;].lower()] = {
                        &#39;appKey&#39;: app[&#39;appKey&#39;],
                        &#39;appType&#39;: app[&#39;appType&#39;],
                        &#39;appDescription&#39;: app[&#39;appDescription&#39;],
                        &#39;flags&#39;: app[&#39;flags&#39;],
                        &#39;isEnabled&#39;: app[&#39;isEnabled&#39;]
                    }
                return apps
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def get(self, app_name):
        &#34;&#34;&#34;Returns a identitymanagementapp object of the specified app name

            Args:
                app_name    (str)   --  name of the app

            Returns:
                object  -   instance of IdentityManagementApp class for the given app name

            Raises:
                SDKException:
                    if type of the app name argument is not string
        &#34;&#34;&#34;
        if not isinstance(app_name, str):
            raise SDKException(&#39;IdentityManagement&#39;, &#39;101&#39;)
        else:
            app_name = app_name.lower()
            if self.has_identity_app(app_name):
                return IdentityManagementApp(
                    self._commcell_object,
                    app_name,
                    self._apps[app_name]
                )

            raise SDKException(&#39;IdentityManagement&#39;, &#39;102&#39;)

    def get_saml(self, app_name):
        &#34;&#34;&#34;Returns a SamlApp object of the specified app name

            Args:
                app_name    (str)   --  name of the saml app

            Returns:
                object  -   instance of SamlApp class for the given app name

            Raises:
                SDKException:
                    if type of the app name argument is not string
        &#34;&#34;&#34;
        if not isinstance(app_name, str):
            raise SDKException(&#39;IdentityManagement&#39;, &#39;101&#39;)
        else:
            app_name = app_name.lower()
            if self.has_identity_app(app_name):
                return SamlApp(
                    self._commcell_object,
                    app_name
                )

            raise SDKException(&#39;IdentityManagement&#39;, &#39;102&#39;)

    @property
    def get_local_identity_app(self):
        &#34;&#34;&#34;Returns the local identity app details for IDP commcell

            Returns:
                object    -   object of IdentityManangementApp class
        &#34;&#34;&#34;
        if self._apps:
            for app in self._apps:
                if self._apps[app][&#39;appType&#39;] == 4:
                    return self.get(app)

    @property
    def get_commcell_identity_apps(self):
        &#34;&#34;&#34;Returns a list of commcell apps for the local commcell

            Returns:
                list    -   List containing commcell apps in the SP commcell

                    [
                        app1_obj,
                        app2_obj
                    ]
        &#34;&#34;&#34;
        commcell_apps = []
        if self._apps:
            for app in self._apps:
                if self._apps[app][&#39;appType&#39;] == 3:
                    commcell_apps.append(self.get(app))
            return commcell_apps

    @property
    def all_apps(self):
        &#34;&#34;&#34;Returns the dictionary consisting of all the ID apps added to the Commcell.

            dict - consists of all the apps configured on the commcell

                {
                    &#34;app1_name&#34;: app1_id,

                    &#34;app2_name&#34;: app2_id
                }

        &#34;&#34;&#34;
        return self._apps

    def delete_identity_app(self, app_name):
        &#34;&#34;&#34;Deletes the specified local identity app

            Args:
                app_name     (str)      -- name of the app to be deleted

            Returns:
                bool    -   True if operation succeeds

            Raises:
                SDKException:
                    if passed app not found

                    if failure in response
        &#34;&#34;&#34;
        draft_json = self._apps.get(app_name)

        if draft_json:
            req_json = {
                &#39;opType&#39;: 2,
                &#39;clientThirdPartyApps&#39;: [
                    draft_json
                ]
            }
        else:
            raise SDKException(&#39;IdentityManagement&#39;, &#39;102&#39;)

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._APPS, req_json
        )
        if flag:
            if response.json() and &#39;error&#39; in response.json():
                if response.json()[&#39;error&#39;][&#39;errorCode&#39;] == 0:
                    self.refresh()
                else:
                    raise SDKException(
                        &#39;Response&#39;,
                        &#39;101&#39;,
                        response.json()[&#39;error&#39;][&#39;warningMessage&#39;]
                    )
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def delete_saml_app(self, app_name):
        &#34;&#34;&#34;Deletes the specified saml app
            Args:
                app_name       (string) name of the saml app

            Raises :
                SDK Exception :
                    if failure in response
                    if invalid response
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            &#39;DELETE&#39;, self._SAML % app_name
        )
        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                if response.json()[&#39;errorCode&#39;] == 0:
                    self.refresh()
                else:
                    raise SDKException(
                        &#39;IdentityManagement&#39;,
                        &#39;104&#39;,
                        &#39; - error {0}&#39;.format(response.json()[&#39;errorMessage&#39;])
                    )
            else:
                raise SDKException(&#39;Response&#39;, &#39;500&#39; + &#39;Invalid Response Returned&#39;)

        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def configure_saml_app(self, app_name, desc, idp_metadata, sp_metadata, associations):
        &#34;&#34;&#34;Creates a saml app

            Args:
                app_name        (string)    saml app name

                desc            (string)    saml app description

                idp_metadata   (dict)  idp_metadata = {
                                            &#39;entityId&#39; : &#39;&#39;,
                                            &#39;redirectUrl&#39; : &#39;&#39;,
                                            &#39;logoutUrl&#39; : &#39;&#39;,
                                            &#39;certificateData&#39;: &#39;&#39;,
                                            &#39;SAMLProtocolVersion&#39; : &#34;urn:oasis:names:tc:SAML:2.0:metadata&#34;
                                        }
                sp_metadata      (dict)  dict of serviceProviderEndpoint, autoGenerateSPMetaData, jksFileContents
                                        sp_metadata = {
                                            &#34;serviceProviderEndpoint&#34;: &#34;https://test.mydomain:443/webconsole&#34;,
                                            &#34;autoGenerateSPMetaData&#34;: true,
                                            &#34;jksFileContents&#34;:[]
                                        }
                associations    (dict)  dict of email suffixes, companies, domains and usergroups
                                        associations = {
                                            &#39;emails&#39; = [&#39;a.com&#39;, b.com&#39;],
                                            &#39;companies&#39; = [],
                                            &#39;domains&#39; = [],
                                            &#39;usergroups&#39;= []
                                        }

            Returns:
                object - returns object of SamlApp class

            Raises:
                SDKException:   if failure in response
                                if invalid response
        &#34;&#34;&#34;
        req_body = {
            &#34;name&#34;: app_name,
            &#34;description&#34;: desc,
            &#34;identityProviderMetaData&#34;: idp_metadata,
            &#34;serviceProviderMetaData&#34;: sp_metadata,
            &#34;associations&#34;: associations
        }
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._ADD_SAML, req_body
        )

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                if response.json()[&#39;errorCode&#39;] == 0:
                    self.refresh()
                    return SamlApp(
                        self._commcell_object,
                        app_name
                    )
                else:
                    raise SDKException(
                        &#39;IdentityManagement&#39;,
                        &#39;103&#39;,
                        &#39; - error {0}&#39;.format(response.json()[&#39;errorMessage&#39;])
                    )
            else:
                raise SDKException(&#39;Response&#39;, &#39;500&#39; + &#39;Invalid Response Returned&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def configure_local_identity_app(self, user_list=None, optype=1):
        &#34;&#34;&#34;Creates a local identity app by associating speccified users

            Args:
                user_list      (list)     --  list of names of users to be associated
                                              with identity server
                optype         (int)      --  operation type, 1 is &#39;create&#39;, 3 is &#39;overwrite&#39;

            Returns:
                object  -   returns object of IdentityManagementApp class

            Raises:
                SDKException:
                    if failed to configure identity app
        &#34;&#34;&#34;
        users_list = []
        if user_list.lower() == &#39;all&#39;:
            users_list += [{&#39;_type_&#39;: 12}]
        else:
            users_list += [
                {&#39;userId&#39;: self._commcell_object.users.all_users[user_name], &#39;_type_&#39;: 13}
                for user_name in user_list
            ]
        third_party_json = {
            &#39;opType&#39;: optype,
            &#39;clientThirdPartyApps&#39;: [
                {
                    &#39;appType&#39;: 4,
                    &#39;isEnabled&#39;: True,
                    &#39;assocTree&#39;: users_list
                }
            ]
        }

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._APPS, third_party_json
        )

        if flag:
            if response.json() and &#39;error&#39; in response.json():
                if response.json()[&#39;error&#39;][&#39;errorCode&#39;] == 0:
                    self.refresh()
                    return self.get_local_identity_app
                else:
                    raise SDKException(
                        &#39;IdentityManagement&#39;,
                        &#39;103&#39;,
                        &#39; - error {0}&#39;.format(response.json()[&#39;error&#39;][&#39;errorString&#39;])
                    )
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;&#39;, &#39;101&#39;, response_string)

    def configure_commcell_app(self,
                               idp_props,
                               app_name,
                               app_display_name,
                               app_description=&#39;&#39;,
                               user_assoc_list=None,
                               user_mappings=None):
        &#34;&#34;&#34;Creates a commcell app by associating speccified users

            Args:
                idp_props      (list)     --  dict containing properties of the IDP&#39;s identity app

                    [
                        {
                            &#34;name&#34;: &#34;SP Certificate Data&#34;,
                            &#34;value: &#34;certificate1_str&#34;
                        },
                        {
                            &#34;name&#34;: &#34;JKS Private Key&#34;,
                            &#34;value: &#34;key1_str&#34;
                        },
                        {
                            &#34;name&#34;: &#34;CommcellId&#34;,
                            &#34;value&#34;: &#34;id1&#34;
                        },
                        {
                            &#34;name&#34;: &#34;RedirectUrl&#34;,
                            &#34;value&#34;: &#34;url1&#34;
                        }
                    ]

                app_name       (str)      --  GUID for the app

                app_display_name (str)    --  display name for the app

                app_description  (str)    --  description for the app

                user_assoc_list (list)    --  list of users for association

                user_mappings  (dict)     --  dict containing mapping of IDP user to local user

                    {
                        &#34;idp1_user&#34;:  &#34;sp1_user&#34;,

                        &#34;idp2_user&#34;:  &#34;sp2_user&#34;
                    }

            Returns:
                object  -   returns object of IdentityManagementApp class

            Raises:
                SDKException:
                    if failed to configure identity app
        &#34;&#34;&#34;
        third_party_json = {
            &#39;opType&#39;: 1,
            &#39;clientThirdPartyApps&#39;: [
                {
                    &#39;appName&#39;: app_name,
                    &#39;appDisplayName&#39;: app_display_name,
                    &#39;appDescription&#39;: app_description,
                    &#39;flags&#39;: 0,
                    &#39;appType&#39;: 3,
                    &#39;isEnabled&#39;: True,
                    &#39;UserMappings&#39;: {
                        &#39;opType&#39;: 2,
                        &#39;userslist&#39;: [
                            {
                                &#39;userfromToken&#39;: spuser,
                                &#34;localuser&#34;: {
                                    &#34;userId&#34;: self._commcell_object.users.all_users[
                                        user_mappings[spuser]
                                    ]
                                }
                            } for spuser in user_mappings
                        ]
                    },
                    &#39;props&#39;: {
                        &#39;nameValues&#39;: idp_props
                    },
                    &#39;assocTree&#39;: [
                        {
                            &#39;userId&#39;: self._commcell_object.users.all_users[user_name],
                            &#39;_type_&#39;: 13
                        } for user_name in user_assoc_list
                    ]
                }
            ]
        }

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._APPS, third_party_json
        )

        if flag:
            if response.json() and &#39;error&#39; in response.json():
                if response.json()[&#39;error&#39;][&#39;errorCode&#39;] == 0:
                    self.refresh()
                    return self.get_commcell_identity_apps
                else:
                    raise SDKException(
                        &#39;IdentityManagement&#39;,
                        &#39;103&#39;,
                        &#39; - error {0}&#39;.format(response.json()[&#39;error&#39;][&#39;errorString&#39;])
                    )
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;&#39;, &#39;101&#39;, response_string)

    def configure_openid_app(self, appname, props, user_to_be_added):
        &#34;&#34;&#34;
        Adding OpenID app

        Args:
            appname (str)           :       Name of the app to be created

            props      (list)       :  dict containing properties of the IDP&#39;s identity app

                    [
                        {
                                &#34;name&#34;: &#34;clientId&#34;,
                                &#34;value&#34;: &#34;13445&#34;
                            },
                            {
                                &#34;name&#34;: &#34;clientSecret&#34;,
                                &#34;value&#34;: &#34;ABC13567&#34;
                            },
                            {
                                &#34;name&#34;: &#34;endPointUrl&#34;,
                                &#34;value&#34;: &#34;https://test.okta.com/.well-known/openid-configuration&#34;
                            },
                            {
                                &#34;name&#34;: &#34;webConsoleUrls&#34;,
                                &#34;values&#34;: [
                                    https://mydomain:443/webconsole
                                ]
                            }
                    ]

            user_to_be_added   (list) :   list of users for association

        Raises:
            SDKException:
                if failed to configure identity app

        &#34;&#34;&#34;
        third_party_json = {
            &#34;App_SetClientThirdPartyAppPropReq&#34;: {
                &#34;opType&#34;: 1,
                &#34;clientThirdPartyApps&#34;: [
                    {
                        &#34;appName&#34;: appname,
                        &#34;flags&#34;: 0,
                        &#34;appType&#34;: 5,
                        &#34;isEnabled&#34;: 1,
                        &#34;props&#34;: {
                            &#34;nameValues&#34;: props
                        },
                        &#34;assocTree&#34;: [
                            {
                                &#34;_type_&#34;: 13,
                                &#34;userName&#34;: user_name
                            } for user_name in user_to_be_added
                        ]
                    }
                ]
            }
        }

        response_json = self._commcell_object.qoperation_execute(third_party_json)

        if response_json.get(&#39;errorCode&#39;, 0) != 0:
            raise SDKException(
                &#39;IdentityManagement&#39;,
                &#39;103&#39;,
                &#39;Error: &#34;{}&#34;&#39;.format(response_json[&#39;errorMessage&#39;])
            )
        else:
            self.refresh()

    def has_identity_app(self, app_name):
        &#34;&#34;&#34;Checks if an identity app exits in the commcell

            Args:
                app_name    (str)   --  name of the identity app

            Returns:
                bool    -   boolean output whether the app exists in the commcell or not

            Raises:
                SDKException:
                    if type of the app name argument is not string
        &#34;&#34;&#34;
        if not isinstance(app_name, str):
            raise SDKException(&#39;IdentityManagement&#39;, &#39;102&#39;)

        return self._apps and app_name.lower() in self._apps

    def refresh(self, hard=False):
        &#34;&#34;&#34;Refresh the apps associated with the Commcell.
        
            Args:
                hard    (bool)  --  perform a hard refresh of the cache
                
        &#34;&#34;&#34;
        self._apps = self._get_apps(hard)


class IdentityManagementApp(object):
    &#34;&#34;&#34;Class for performing operations on a specific identity management app&#34;&#34;&#34;

    def __init__(self, commcell_object, app_name, app_dict=None):
        &#34;&#34;&#34;Initialize the app class

            Args:
                commcell_object     (object)    --  instance of the commcell class

                app_name            (str)       --  name of the app

                app_dict            (dict)     -- dict containing the properties of the app. default: None

            Returns:
                object - instance of the IdentityManagementApp class
        &#34;&#34;&#34;
        self._properties = None
        self._commcell_object = commcell_object
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._update_response_ = commcell_object._update_response_

        self._app_name = app_name
        self._app_description = None
        self._flags = None
        self._app_type = None
        self._app_type_dict = {
            1: &#39;Regular&#39;,
            2: &#39;SAML&#39;,
            3: &#39;CommCell&#39;,
            4: &#39;Local Identity&#39;,
            5: &#39;OpenId Connect&#39;
        }
        self._is_enabled = None
        self._app_displayname = None
        self._app_dict = app_dict

        if app_dict:
            self._app_key = app_dict[&#39;appKey&#39;]
        else:
            self._app_key = self._get_app_key()

        self._APPS = commcell_object._services[&#39;IDENTITY_APPS&#39;]

        self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        representation_string = &#39;IdentityManagementApp class instance for app: \
                                &#34;{0}&#34;, of Commcell: &#34;{1}&#34;&#39;

        return representation_string.format(
            self._app_name, self._commcell_object.commserv_name
        )

    def _get_app_key(self):
        &#34;&#34;&#34;Gets the key of app associated to this object

            Returns:
                str - key associated with this app
        &#34;&#34;&#34;
        apps = IdentityManagementApps(self._commcell_object)
        return apps.get(self.app_name).app_key

    def _get_app_details(self):
        &#34;&#34;&#34;Returns a dict containing the details of a third party app.

            Returns:
                dict    -   details of the identity app

            Raises:
                SDKException:
                        if response is not success
        &#34;&#34;&#34;
        if self._app_dict:
            return self._app_dict

        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._APPS
        )
        if flag:
            if response.json() and &#39;clientThirdPartyApps&#39; in response.json():
                response_value = response.json()[&#39;clientThirdPartyApps&#39;]
                for app in response_value:
                    if app[&#39;appKey&#39;] == self._app_key:
                        self._app_description = app.get(&#39;appDescription&#39;)
                        self._flags = app.get(&#39;flags&#39;)
                        self._app_type = self._app_type_dict[app.get(&#39;appType&#39;)]
                        self._is_enabled = app.get(&#39;isEnabled&#39;)
                        return app
            else:
                raise SDKException(&#39;IdentityManagement&#39;, &#39;101&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def get_app_props(self):
        &#34;&#34;&#34;Returns a dict containing the properties of a third party app.

            Returns:
                dict    -   properties of the identity app

            Raises:
                SDKException:
                    if response is not success
        &#34;&#34;&#34;
        req_xml = &#34;&#34;&#34;&lt;App_GetClientThirdPartyAppPropReq propLevel=&#39;30&#39;&gt;
                        &lt;appKeys val=&#39;{0}&#39;/&gt;
                    &lt;/App_GetClientThirdPartyAppPropReq&gt;&#34;&#34;&#34;.format(self.app_key)
        response = self._commcell_object._qoperation_execute(req_xml)
        if &#39;clientThirdPartyApps&#39; in response:
            return response[&#39;clientThirdPartyApps&#39;][0][&#39;props&#39;][&#39;nameValues&#39;]
        else:
            raise SDKException(&#39;IdentityManagement&#39;, &#39;102&#39;)

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the app.&#34;&#34;&#34;
        self._properties = self._get_app_details()

    @property
    def app_name(self):
        &#34;&#34;&#34;Treats the app name as a read-only attribute.&#34;&#34;&#34;
        return self._app_name

    @property
    def app_key(self):
        &#34;&#34;&#34;Treats the app key as a read-only attribute.&#34;&#34;&#34;
        return self._app_key

    @property
    def app_description(self):
        &#34;&#34;&#34;Treats the app description as a read-only attribute.&#34;&#34;&#34;
        return self._app_description

    @property
    def app_type(self):
        &#34;&#34;&#34;Treats the app type as a read-only attribute.&#34;&#34;&#34;
        return self._app_type

    @property
    def is_enabled(self):
        &#34;&#34;&#34;Treats the enabled peroperty as a read-only attribute.&#34;&#34;&#34;
        return self._is_enabled

    @property
    def flags(self):
        &#34;&#34;&#34;Treats the app flags as a read-only attribute.&#34;&#34;&#34;
        return self._flags


class SamlApp(object):
    &#34;&#34;&#34;Class for performing operations on a specific saml app&#34;&#34;&#34;

    def __init__(self, commcell, appname, properties=None):
        &#34;&#34;&#34;Initialise SamlApp class
            Args:
                commcell            (object)        instance of commcell class

                appname             (string)        saml app name

                properties          (dict)          dict containing properties of saml app. Default: None

            Returns:
                object - instance of the SamlApp class
        &#34;&#34;&#34;

        self._commcell = commcell
        self._cvpysdk_object = commcell._cvpysdk_object
        self._update_response_ = commcell._update_response_
        self._appname = appname
        self._properties = None
        self._SAML = commcell._services[&#39;EDIT_SAML&#39;]
        self._redirecturl = commcell._services[&#39;POLL_REQUEST_ROUTER&#39;]
        self._APPS = commcell._services[&#39;IDENTITY_APPS&#39;]
        self._SAML_PROP = commcell._services[&#39;GET_SAML_PROP&#39;]

        if properties:
            self._properties = properties
        else:
            self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        representation_string = &#39;SamlApp class instance for app: \
                                &#34;{0}&#34;, of Commcell: &#34;{1}&#34;&#39;

        return representation_string.format(
            self._appname, self._commcell.commserv_name
        )

    def refresh(self):
        &#34;&#34;&#34;Refresh the saml app properties&#34;&#34;&#34;
        self._properties = self._get_saml_app_details()

    def _get_saml_app_details(self):
        &#34;&#34;&#34;gets the properties of a saml app
        Returns:
                        prop        (dict)      properties of a saml app

        Raises:
                SDK Exception:
                    if saml app is not found
                    if request is not successful
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._SAML % self._appname
        )
        if flag:
            if response.json() and &#39;name&#39; in response.json():
                if response.json()[&#39;name&#39;] == self._appname:
                    return response.json()
                else:
                    raise SDKException(
                        &#39;IdentityManagement&#39;,
                        &#39;102&#39;,
                        &#39; - error {0}&#39;.format(response.json()[&#39;errorMessage&#39;])
                    )
            else:
                raise SDKException(&#39;Response&#39;, &#39;500&#39; + &#39;Invalid Response Returned&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def get_all_saml_app_prop(self) -&gt; dict:
        &#34;&#34;&#34;Returns a dict containing the properties of SAML app.

            Returns:
                dict    -   properties of the saml app

            Raises:
                SDKException:
                    if response is not success
        &#34;&#34;&#34;
        req_json = {
            &#34;appKeys&#34;: [self._properties[&#39;appKey&#39;]],
            &#34;propLevel&#34;: 30
        }
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._SAML_PROP, req_json)
        if flag:
            if response.json() and &#39;clientThirdPartyApps&#39; in response.json():
                return response.json()[&#39;clientThirdPartyApps&#39;][0]
            else:
                raise SDKException(
                    &#39;IdentityManagement&#39;,
                    &#39;102&#39;,
                    &#39; - error {0}&#39;.format(response.text)
                )
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)


    def modify_saml_app(self, req_body):
        &#34;&#34;&#34;Modifies a saml app
            Args:
                req_body  (json)       saml app properties in json format

            Raises:
                SDKException:
                    if failed to modify saml app
                    if request is not successful

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            &#39;PUT&#39;, self._SAML % self._appname, req_body
        )
        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                if response.json()[&#39;errorCode&#39;] == 0:
                    self.refresh()
                else:
                    raise SDKException(
                        &#39;IdentityManagement&#39;,
                        &#39;105&#39;,
                        &#39; - error {0}&#39;.format(response.json()[&#39;errorMessage&#39;])
                    )
            else:
                raise SDKException(&#39;Response&#39;, &#39;500&#39; + &#39;Invalid Response Returned&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def set_auto_redirect_to_idp(self, enable:bool=True) -&gt; None:
        &#34;&#34;&#34;Enable/Disable auto redirection to IDP

        Args:
            enable (bool)   :   True to enable, False to disable

        Raises:
            SDKException:
                if request is not successful
        &#34;&#34;&#34;
        # preserve the state of other flags
        flags = self.get_all_saml_app_prop()[&#39;flags&#39;]
        if enable:
            flags |= 1
        else:
            flags &amp;= ~1

        req_json = {
                        &#34;opType&#34;: 3,
                        &#34;clientThirdPartyApps&#34;: [
                            {
                                &#34;flags&#34;: flags,
                                &#34;appType&#34;: 2,
                                &#34;appKey&#34;: self._properties[&#39;appKey&#39;]
                            }
                        ]
                    }
        flag, response = self._cvpysdk_object.make_request(&#39;PUT&#39;, self._APPS, req_json)
        if flag:
            if response.json():
                error_code = response.json().get(&#39;errorCode&#39;, 0)
                if error_code != 0:
                    raise SDKException(
                        &#39;Response&#39;, &#39;101&#39;,
                        self._update_response_(response.text)
                    )
                return
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        raise SDKException(
            &#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def get_saml_user_redirect_url(self, user_email):
        &#34;&#34;&#34;Get Redirect Url of SAML User
        Args:
            user_email         (str)        user email

        Returns :
                redirect url of user, None if redirect url is not found for the user
        Raises:
                SDKException:
                    if failed to get redirect url
                    if request is not successful
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._redirecturl % user_email
        )
        if flag:
            if response.json():
                if &#39;AvailableRedirects&#39; in response.json():
                    if len(response.json()[&#39;AvailableRedirects&#39;]) &gt; 0:
                        return response.json()[&#39;AvailableRedirects&#39;][0].get(&#39;redirectUrl&#39;)
                    else:
                        return None
                else:
                    raise SDKException(
                        &#39;IdentityManagement&#39;,
                        &#39;106&#39;,
                        &#39; - error {0}&#39;.format(response.json()[&#39;error&#39;])
                    )
            else:
                raise SDKException(&#39;Response&#39;, &#39;500&#39; + &#39;Invalid Response Returned&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def saml_app_description(self):
        &#34;&#34;&#34;Treats the saml_app_description as a read-only attribute.&#34;&#34;&#34;
        return self._properties.get(&#39;description&#39;)

    @property
    def is_saml_app_enabled(self):
        &#34;&#34;&#34;Treats the is_saml_app_enabled as a read-only attribute.&#34;&#34;&#34;
        return self._properties.get(&#39;enabled&#39;)

    @property
    def is_auto_create_user(self):
        &#34;&#34;&#34;Treats the is_auto_create_user as a read-only attribute.&#34;&#34;&#34;
        return self._properties.get(&#39;autoCreateUser&#39;)

    @property
    def saml_app_default_user_groups(self):
        &#34;&#34;&#34;Treats the saml_app_default_user_groups as a read-only attribute.&#34;&#34;&#34;
        return self._properties.get(&#39;userGroups&#39;)

    @property
    def saml_app_nameid_attribute(self):
        &#34;&#34;&#34;Treats the saml_app_nameid_attribute as a read-only attribute.&#34;&#34;&#34;
        return self._properties.get(&#39;nameIDAttribute&#39;)

    @property
    def saml_app_attribute_mappings(self):
        &#34;&#34;&#34;Treats the saml_app_attribute_mappings as a read-only attribute.&#34;&#34;&#34;
        return self._properties.get(&#39;attributeMappings&#39;)

    @property
    def saml_app_identity_provider_metadata(self):
        &#34;&#34;&#34;Treats the saml_app_identity_provider_metadata as a read-only attribute.&#34;&#34;&#34;
        return self._properties.get(&#39;identityProviderMetaData&#39;)

    @property
    def saml_app_service_provider_metadata(self):
        &#34;&#34;&#34;Treats the saml_app_service_provider_metadata as a read-only attribute.&#34;&#34;&#34;
        return self._properties.get(&#39;serviceProviderMetaData&#39;)

    @property
    def saml_app_associations(self):
        &#34;&#34;&#34;Treats the saml_app_associations as a read-only attribute.&#34;&#34;&#34;
        return self._properties.get(&#39;associations&#39;)

    @property
    def is_company_saml_app(self):
        &#34;&#34;&#34;Treats the is_company_saml_app as a read-only attribute.
            Returns
                    True if saml app is created for a company, False otherwise
        &#34;&#34;&#34;
        if self._properties.get(&#39;createdForCompany&#39;):
            return True
        else:
            return False</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.identity_management.IdentityManagementApp"><code class="flex name class">
<span>class <span class="ident">IdentityManagementApp</span></span>
<span>(</span><span>commcell_object, app_name, app_dict=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for performing operations on a specific identity management app</p>
<p>Initialize the app class</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the commcell class</p>
<p>app_name
(str)
&ndash;
name of the app</p>
<p>app_dict
(dict)
&ndash; dict containing the properties of the app. default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the IdentityManagementApp class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L719-L865" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class IdentityManagementApp(object):
    &#34;&#34;&#34;Class for performing operations on a specific identity management app&#34;&#34;&#34;

    def __init__(self, commcell_object, app_name, app_dict=None):
        &#34;&#34;&#34;Initialize the app class

            Args:
                commcell_object     (object)    --  instance of the commcell class

                app_name            (str)       --  name of the app

                app_dict            (dict)     -- dict containing the properties of the app. default: None

            Returns:
                object - instance of the IdentityManagementApp class
        &#34;&#34;&#34;
        self._properties = None
        self._commcell_object = commcell_object
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._update_response_ = commcell_object._update_response_

        self._app_name = app_name
        self._app_description = None
        self._flags = None
        self._app_type = None
        self._app_type_dict = {
            1: &#39;Regular&#39;,
            2: &#39;SAML&#39;,
            3: &#39;CommCell&#39;,
            4: &#39;Local Identity&#39;,
            5: &#39;OpenId Connect&#39;
        }
        self._is_enabled = None
        self._app_displayname = None
        self._app_dict = app_dict

        if app_dict:
            self._app_key = app_dict[&#39;appKey&#39;]
        else:
            self._app_key = self._get_app_key()

        self._APPS = commcell_object._services[&#39;IDENTITY_APPS&#39;]

        self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        representation_string = &#39;IdentityManagementApp class instance for app: \
                                &#34;{0}&#34;, of Commcell: &#34;{1}&#34;&#39;

        return representation_string.format(
            self._app_name, self._commcell_object.commserv_name
        )

    def _get_app_key(self):
        &#34;&#34;&#34;Gets the key of app associated to this object

            Returns:
                str - key associated with this app
        &#34;&#34;&#34;
        apps = IdentityManagementApps(self._commcell_object)
        return apps.get(self.app_name).app_key

    def _get_app_details(self):
        &#34;&#34;&#34;Returns a dict containing the details of a third party app.

            Returns:
                dict    -   details of the identity app

            Raises:
                SDKException:
                        if response is not success
        &#34;&#34;&#34;
        if self._app_dict:
            return self._app_dict

        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._APPS
        )
        if flag:
            if response.json() and &#39;clientThirdPartyApps&#39; in response.json():
                response_value = response.json()[&#39;clientThirdPartyApps&#39;]
                for app in response_value:
                    if app[&#39;appKey&#39;] == self._app_key:
                        self._app_description = app.get(&#39;appDescription&#39;)
                        self._flags = app.get(&#39;flags&#39;)
                        self._app_type = self._app_type_dict[app.get(&#39;appType&#39;)]
                        self._is_enabled = app.get(&#39;isEnabled&#39;)
                        return app
            else:
                raise SDKException(&#39;IdentityManagement&#39;, &#39;101&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def get_app_props(self):
        &#34;&#34;&#34;Returns a dict containing the properties of a third party app.

            Returns:
                dict    -   properties of the identity app

            Raises:
                SDKException:
                    if response is not success
        &#34;&#34;&#34;
        req_xml = &#34;&#34;&#34;&lt;App_GetClientThirdPartyAppPropReq propLevel=&#39;30&#39;&gt;
                        &lt;appKeys val=&#39;{0}&#39;/&gt;
                    &lt;/App_GetClientThirdPartyAppPropReq&gt;&#34;&#34;&#34;.format(self.app_key)
        response = self._commcell_object._qoperation_execute(req_xml)
        if &#39;clientThirdPartyApps&#39; in response:
            return response[&#39;clientThirdPartyApps&#39;][0][&#39;props&#39;][&#39;nameValues&#39;]
        else:
            raise SDKException(&#39;IdentityManagement&#39;, &#39;102&#39;)

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the app.&#34;&#34;&#34;
        self._properties = self._get_app_details()

    @property
    def app_name(self):
        &#34;&#34;&#34;Treats the app name as a read-only attribute.&#34;&#34;&#34;
        return self._app_name

    @property
    def app_key(self):
        &#34;&#34;&#34;Treats the app key as a read-only attribute.&#34;&#34;&#34;
        return self._app_key

    @property
    def app_description(self):
        &#34;&#34;&#34;Treats the app description as a read-only attribute.&#34;&#34;&#34;
        return self._app_description

    @property
    def app_type(self):
        &#34;&#34;&#34;Treats the app type as a read-only attribute.&#34;&#34;&#34;
        return self._app_type

    @property
    def is_enabled(self):
        &#34;&#34;&#34;Treats the enabled peroperty as a read-only attribute.&#34;&#34;&#34;
        return self._is_enabled

    @property
    def flags(self):
        &#34;&#34;&#34;Treats the app flags as a read-only attribute.&#34;&#34;&#34;
        return self._flags</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.identity_management.IdentityManagementApp.app_description"><code class="name">var <span class="ident">app_description</span></code></dt>
<dd>
<div class="desc"><p>Treats the app description as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L847-L850" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def app_description(self):
    &#34;&#34;&#34;Treats the app description as a read-only attribute.&#34;&#34;&#34;
    return self._app_description</code></pre>
</details>
</dd>
<dt id="cvpysdk.identity_management.IdentityManagementApp.app_key"><code class="name">var <span class="ident">app_key</span></code></dt>
<dd>
<div class="desc"><p>Treats the app key as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L842-L845" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def app_key(self):
    &#34;&#34;&#34;Treats the app key as a read-only attribute.&#34;&#34;&#34;
    return self._app_key</code></pre>
</details>
</dd>
<dt id="cvpysdk.identity_management.IdentityManagementApp.app_name"><code class="name">var <span class="ident">app_name</span></code></dt>
<dd>
<div class="desc"><p>Treats the app name as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L837-L840" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def app_name(self):
    &#34;&#34;&#34;Treats the app name as a read-only attribute.&#34;&#34;&#34;
    return self._app_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.identity_management.IdentityManagementApp.app_type"><code class="name">var <span class="ident">app_type</span></code></dt>
<dd>
<div class="desc"><p>Treats the app type as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L852-L855" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def app_type(self):
    &#34;&#34;&#34;Treats the app type as a read-only attribute.&#34;&#34;&#34;
    return self._app_type</code></pre>
</details>
</dd>
<dt id="cvpysdk.identity_management.IdentityManagementApp.flags"><code class="name">var <span class="ident">flags</span></code></dt>
<dd>
<div class="desc"><p>Treats the app flags as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L862-L865" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def flags(self):
    &#34;&#34;&#34;Treats the app flags as a read-only attribute.&#34;&#34;&#34;
    return self._flags</code></pre>
</details>
</dd>
<dt id="cvpysdk.identity_management.IdentityManagementApp.is_enabled"><code class="name">var <span class="ident">is_enabled</span></code></dt>
<dd>
<div class="desc"><p>Treats the enabled peroperty as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L857-L860" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_enabled(self):
    &#34;&#34;&#34;Treats the enabled peroperty as a read-only attribute.&#34;&#34;&#34;
    return self._is_enabled</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.identity_management.IdentityManagementApp.get_app_props"><code class="name flex">
<span>def <span class="ident">get_app_props</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a dict containing the properties of a third party app.</p>
<h2 id="returns">Returns</h2>
<p>dict
-
properties of the identity app</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if response is not success</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L814-L831" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_app_props(self):
    &#34;&#34;&#34;Returns a dict containing the properties of a third party app.

        Returns:
            dict    -   properties of the identity app

        Raises:
            SDKException:
                if response is not success
    &#34;&#34;&#34;
    req_xml = &#34;&#34;&#34;&lt;App_GetClientThirdPartyAppPropReq propLevel=&#39;30&#39;&gt;
                    &lt;appKeys val=&#39;{0}&#39;/&gt;
                &lt;/App_GetClientThirdPartyAppPropReq&gt;&#34;&#34;&#34;.format(self.app_key)
    response = self._commcell_object._qoperation_execute(req_xml)
    if &#39;clientThirdPartyApps&#39; in response:
        return response[&#39;clientThirdPartyApps&#39;][0][&#39;props&#39;][&#39;nameValues&#39;]
    else:
        raise SDKException(&#39;IdentityManagement&#39;, &#39;102&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.identity_management.IdentityManagementApp.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the properties of the app.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L833-L835" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the properties of the app.&#34;&#34;&#34;
    self._properties = self._get_app_details()</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.identity_management.IdentityManagementApps"><code class="flex name class">
<span>class <span class="ident">IdentityManagementApps</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing third party apps in the commcell</p>
<p>Initialize object of third party apps class.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the Commcell class</p>
<h2 id="returns">Returns</h2>
<p>object - instance of ThirdPartyApps class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L124-L716" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class IdentityManagementApps(object):
    &#34;&#34;&#34;Class for representing third party apps in the commcell&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of third party apps class.

            Args:
                commcell_object    (object)    --  instance of the Commcell class

            Returns:
                object - instance of ThirdPartyApps class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._update_response_ = commcell_object._update_response_
        self._APPS = commcell_object._services[&#39;IDENTITY_APPS&#39;]
        self._ADD_SAML = commcell_object._services[&#39;ADD_OR_GET_SAML&#39;]
        self._SAML = commcell_object._services[&#39;EDIT_SAML&#39;]
        self._apps = None
        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all identity management apps of the Commcell.

            Returns:
                str -   string of all the identity management apps in a commcell
        &#34;&#34;&#34;
        representation_string = &#34;{:^5}\t{:^50}\n\n&#34;.format(&#39;S. No.&#39;, &#39;App&#39;)

        for index, app in enumerate(self._apps):
            sub_str = &#39;{:^5}\t{:30}\n&#39;.format(index + 1, app)
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the IdentityManagementApps class.&#34;&#34;&#34;
        return &#34;IdentityManagementApps class instance for Commcell&#34;

    def __len__(self):
        &#34;&#34;&#34;Returns the number of the app added to the Commcell.&#34;&#34;&#34;
        return len(self.all_apps)

    def _get_apps(self, hard=False):
        &#34;&#34;&#34;Gets list of all third party apps.

            Returns:
                dict - consisits of all thrid party apps in the commcell
                        {
                            &#39;app1_name&#39;: {
                                &#39;appKey&#39;: app1_key,
                                &#39;appType&#39;: app1_type,
                                &#39;appDescription&#39;: &#39;app1_description&#39;,
                                &#39;flags&#39;: &#39;app1_flags&#39;,
                                &#39;isEnabled&#39;: &#39;app1_isEnabled&#39;
                            },
                            &#39;app2_name&#39;: {
                                &#39;appKey&#39;: app2_key,
                                &#39;appType&#39;: app2_type,
                                &#39;appDescription&#39;: &#39;app1_description&#39;,
                                &#39;flags&#39;: &#39;app1_flags&#39;,
                                &#39;isEnabled&#39;: &#39;app1_isEnabled&#39;
                            }
                        }

            Raises:
                SDKException:
                        if response is not success
        &#34;&#34;&#34;
        if hard:
            self._cvpysdk_object.make_request(
                &#39;GET&#39;, self._APPS + &#39;?hardRefresh=true&#39;
            )
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._APPS
        )

        if flag:
            apps = {}

            if response.json() and &#39;clientThirdPartyApps&#39; in response.json():
                response_value = response.json()[&#39;clientThirdPartyApps&#39;]

                for app in response_value:
                    apps[app[&#39;appName&#39;].lower()] = {
                        &#39;appKey&#39;: app[&#39;appKey&#39;],
                        &#39;appType&#39;: app[&#39;appType&#39;],
                        &#39;appDescription&#39;: app[&#39;appDescription&#39;],
                        &#39;flags&#39;: app[&#39;flags&#39;],
                        &#39;isEnabled&#39;: app[&#39;isEnabled&#39;]
                    }
                return apps
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def get(self, app_name):
        &#34;&#34;&#34;Returns a identitymanagementapp object of the specified app name

            Args:
                app_name    (str)   --  name of the app

            Returns:
                object  -   instance of IdentityManagementApp class for the given app name

            Raises:
                SDKException:
                    if type of the app name argument is not string
        &#34;&#34;&#34;
        if not isinstance(app_name, str):
            raise SDKException(&#39;IdentityManagement&#39;, &#39;101&#39;)
        else:
            app_name = app_name.lower()
            if self.has_identity_app(app_name):
                return IdentityManagementApp(
                    self._commcell_object,
                    app_name,
                    self._apps[app_name]
                )

            raise SDKException(&#39;IdentityManagement&#39;, &#39;102&#39;)

    def get_saml(self, app_name):
        &#34;&#34;&#34;Returns a SamlApp object of the specified app name

            Args:
                app_name    (str)   --  name of the saml app

            Returns:
                object  -   instance of SamlApp class for the given app name

            Raises:
                SDKException:
                    if type of the app name argument is not string
        &#34;&#34;&#34;
        if not isinstance(app_name, str):
            raise SDKException(&#39;IdentityManagement&#39;, &#39;101&#39;)
        else:
            app_name = app_name.lower()
            if self.has_identity_app(app_name):
                return SamlApp(
                    self._commcell_object,
                    app_name
                )

            raise SDKException(&#39;IdentityManagement&#39;, &#39;102&#39;)

    @property
    def get_local_identity_app(self):
        &#34;&#34;&#34;Returns the local identity app details for IDP commcell

            Returns:
                object    -   object of IdentityManangementApp class
        &#34;&#34;&#34;
        if self._apps:
            for app in self._apps:
                if self._apps[app][&#39;appType&#39;] == 4:
                    return self.get(app)

    @property
    def get_commcell_identity_apps(self):
        &#34;&#34;&#34;Returns a list of commcell apps for the local commcell

            Returns:
                list    -   List containing commcell apps in the SP commcell

                    [
                        app1_obj,
                        app2_obj
                    ]
        &#34;&#34;&#34;
        commcell_apps = []
        if self._apps:
            for app in self._apps:
                if self._apps[app][&#39;appType&#39;] == 3:
                    commcell_apps.append(self.get(app))
            return commcell_apps

    @property
    def all_apps(self):
        &#34;&#34;&#34;Returns the dictionary consisting of all the ID apps added to the Commcell.

            dict - consists of all the apps configured on the commcell

                {
                    &#34;app1_name&#34;: app1_id,

                    &#34;app2_name&#34;: app2_id
                }

        &#34;&#34;&#34;
        return self._apps

    def delete_identity_app(self, app_name):
        &#34;&#34;&#34;Deletes the specified local identity app

            Args:
                app_name     (str)      -- name of the app to be deleted

            Returns:
                bool    -   True if operation succeeds

            Raises:
                SDKException:
                    if passed app not found

                    if failure in response
        &#34;&#34;&#34;
        draft_json = self._apps.get(app_name)

        if draft_json:
            req_json = {
                &#39;opType&#39;: 2,
                &#39;clientThirdPartyApps&#39;: [
                    draft_json
                ]
            }
        else:
            raise SDKException(&#39;IdentityManagement&#39;, &#39;102&#39;)

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._APPS, req_json
        )
        if flag:
            if response.json() and &#39;error&#39; in response.json():
                if response.json()[&#39;error&#39;][&#39;errorCode&#39;] == 0:
                    self.refresh()
                else:
                    raise SDKException(
                        &#39;Response&#39;,
                        &#39;101&#39;,
                        response.json()[&#39;error&#39;][&#39;warningMessage&#39;]
                    )
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def delete_saml_app(self, app_name):
        &#34;&#34;&#34;Deletes the specified saml app
            Args:
                app_name       (string) name of the saml app

            Raises :
                SDK Exception :
                    if failure in response
                    if invalid response
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            &#39;DELETE&#39;, self._SAML % app_name
        )
        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                if response.json()[&#39;errorCode&#39;] == 0:
                    self.refresh()
                else:
                    raise SDKException(
                        &#39;IdentityManagement&#39;,
                        &#39;104&#39;,
                        &#39; - error {0}&#39;.format(response.json()[&#39;errorMessage&#39;])
                    )
            else:
                raise SDKException(&#39;Response&#39;, &#39;500&#39; + &#39;Invalid Response Returned&#39;)

        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def configure_saml_app(self, app_name, desc, idp_metadata, sp_metadata, associations):
        &#34;&#34;&#34;Creates a saml app

            Args:
                app_name        (string)    saml app name

                desc            (string)    saml app description

                idp_metadata   (dict)  idp_metadata = {
                                            &#39;entityId&#39; : &#39;&#39;,
                                            &#39;redirectUrl&#39; : &#39;&#39;,
                                            &#39;logoutUrl&#39; : &#39;&#39;,
                                            &#39;certificateData&#39;: &#39;&#39;,
                                            &#39;SAMLProtocolVersion&#39; : &#34;urn:oasis:names:tc:SAML:2.0:metadata&#34;
                                        }
                sp_metadata      (dict)  dict of serviceProviderEndpoint, autoGenerateSPMetaData, jksFileContents
                                        sp_metadata = {
                                            &#34;serviceProviderEndpoint&#34;: &#34;https://test.mydomain:443/webconsole&#34;,
                                            &#34;autoGenerateSPMetaData&#34;: true,
                                            &#34;jksFileContents&#34;:[]
                                        }
                associations    (dict)  dict of email suffixes, companies, domains and usergroups
                                        associations = {
                                            &#39;emails&#39; = [&#39;a.com&#39;, b.com&#39;],
                                            &#39;companies&#39; = [],
                                            &#39;domains&#39; = [],
                                            &#39;usergroups&#39;= []
                                        }

            Returns:
                object - returns object of SamlApp class

            Raises:
                SDKException:   if failure in response
                                if invalid response
        &#34;&#34;&#34;
        req_body = {
            &#34;name&#34;: app_name,
            &#34;description&#34;: desc,
            &#34;identityProviderMetaData&#34;: idp_metadata,
            &#34;serviceProviderMetaData&#34;: sp_metadata,
            &#34;associations&#34;: associations
        }
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._ADD_SAML, req_body
        )

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                if response.json()[&#39;errorCode&#39;] == 0:
                    self.refresh()
                    return SamlApp(
                        self._commcell_object,
                        app_name
                    )
                else:
                    raise SDKException(
                        &#39;IdentityManagement&#39;,
                        &#39;103&#39;,
                        &#39; - error {0}&#39;.format(response.json()[&#39;errorMessage&#39;])
                    )
            else:
                raise SDKException(&#39;Response&#39;, &#39;500&#39; + &#39;Invalid Response Returned&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def configure_local_identity_app(self, user_list=None, optype=1):
        &#34;&#34;&#34;Creates a local identity app by associating speccified users

            Args:
                user_list      (list)     --  list of names of users to be associated
                                              with identity server
                optype         (int)      --  operation type, 1 is &#39;create&#39;, 3 is &#39;overwrite&#39;

            Returns:
                object  -   returns object of IdentityManagementApp class

            Raises:
                SDKException:
                    if failed to configure identity app
        &#34;&#34;&#34;
        users_list = []
        if user_list.lower() == &#39;all&#39;:
            users_list += [{&#39;_type_&#39;: 12}]
        else:
            users_list += [
                {&#39;userId&#39;: self._commcell_object.users.all_users[user_name], &#39;_type_&#39;: 13}
                for user_name in user_list
            ]
        third_party_json = {
            &#39;opType&#39;: optype,
            &#39;clientThirdPartyApps&#39;: [
                {
                    &#39;appType&#39;: 4,
                    &#39;isEnabled&#39;: True,
                    &#39;assocTree&#39;: users_list
                }
            ]
        }

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._APPS, third_party_json
        )

        if flag:
            if response.json() and &#39;error&#39; in response.json():
                if response.json()[&#39;error&#39;][&#39;errorCode&#39;] == 0:
                    self.refresh()
                    return self.get_local_identity_app
                else:
                    raise SDKException(
                        &#39;IdentityManagement&#39;,
                        &#39;103&#39;,
                        &#39; - error {0}&#39;.format(response.json()[&#39;error&#39;][&#39;errorString&#39;])
                    )
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;&#39;, &#39;101&#39;, response_string)

    def configure_commcell_app(self,
                               idp_props,
                               app_name,
                               app_display_name,
                               app_description=&#39;&#39;,
                               user_assoc_list=None,
                               user_mappings=None):
        &#34;&#34;&#34;Creates a commcell app by associating speccified users

            Args:
                idp_props      (list)     --  dict containing properties of the IDP&#39;s identity app

                    [
                        {
                            &#34;name&#34;: &#34;SP Certificate Data&#34;,
                            &#34;value: &#34;certificate1_str&#34;
                        },
                        {
                            &#34;name&#34;: &#34;JKS Private Key&#34;,
                            &#34;value: &#34;key1_str&#34;
                        },
                        {
                            &#34;name&#34;: &#34;CommcellId&#34;,
                            &#34;value&#34;: &#34;id1&#34;
                        },
                        {
                            &#34;name&#34;: &#34;RedirectUrl&#34;,
                            &#34;value&#34;: &#34;url1&#34;
                        }
                    ]

                app_name       (str)      --  GUID for the app

                app_display_name (str)    --  display name for the app

                app_description  (str)    --  description for the app

                user_assoc_list (list)    --  list of users for association

                user_mappings  (dict)     --  dict containing mapping of IDP user to local user

                    {
                        &#34;idp1_user&#34;:  &#34;sp1_user&#34;,

                        &#34;idp2_user&#34;:  &#34;sp2_user&#34;
                    }

            Returns:
                object  -   returns object of IdentityManagementApp class

            Raises:
                SDKException:
                    if failed to configure identity app
        &#34;&#34;&#34;
        third_party_json = {
            &#39;opType&#39;: 1,
            &#39;clientThirdPartyApps&#39;: [
                {
                    &#39;appName&#39;: app_name,
                    &#39;appDisplayName&#39;: app_display_name,
                    &#39;appDescription&#39;: app_description,
                    &#39;flags&#39;: 0,
                    &#39;appType&#39;: 3,
                    &#39;isEnabled&#39;: True,
                    &#39;UserMappings&#39;: {
                        &#39;opType&#39;: 2,
                        &#39;userslist&#39;: [
                            {
                                &#39;userfromToken&#39;: spuser,
                                &#34;localuser&#34;: {
                                    &#34;userId&#34;: self._commcell_object.users.all_users[
                                        user_mappings[spuser]
                                    ]
                                }
                            } for spuser in user_mappings
                        ]
                    },
                    &#39;props&#39;: {
                        &#39;nameValues&#39;: idp_props
                    },
                    &#39;assocTree&#39;: [
                        {
                            &#39;userId&#39;: self._commcell_object.users.all_users[user_name],
                            &#39;_type_&#39;: 13
                        } for user_name in user_assoc_list
                    ]
                }
            ]
        }

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._APPS, third_party_json
        )

        if flag:
            if response.json() and &#39;error&#39; in response.json():
                if response.json()[&#39;error&#39;][&#39;errorCode&#39;] == 0:
                    self.refresh()
                    return self.get_commcell_identity_apps
                else:
                    raise SDKException(
                        &#39;IdentityManagement&#39;,
                        &#39;103&#39;,
                        &#39; - error {0}&#39;.format(response.json()[&#39;error&#39;][&#39;errorString&#39;])
                    )
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;&#39;, &#39;101&#39;, response_string)

    def configure_openid_app(self, appname, props, user_to_be_added):
        &#34;&#34;&#34;
        Adding OpenID app

        Args:
            appname (str)           :       Name of the app to be created

            props      (list)       :  dict containing properties of the IDP&#39;s identity app

                    [
                        {
                                &#34;name&#34;: &#34;clientId&#34;,
                                &#34;value&#34;: &#34;13445&#34;
                            },
                            {
                                &#34;name&#34;: &#34;clientSecret&#34;,
                                &#34;value&#34;: &#34;ABC13567&#34;
                            },
                            {
                                &#34;name&#34;: &#34;endPointUrl&#34;,
                                &#34;value&#34;: &#34;https://test.okta.com/.well-known/openid-configuration&#34;
                            },
                            {
                                &#34;name&#34;: &#34;webConsoleUrls&#34;,
                                &#34;values&#34;: [
                                    https://mydomain:443/webconsole
                                ]
                            }
                    ]

            user_to_be_added   (list) :   list of users for association

        Raises:
            SDKException:
                if failed to configure identity app

        &#34;&#34;&#34;
        third_party_json = {
            &#34;App_SetClientThirdPartyAppPropReq&#34;: {
                &#34;opType&#34;: 1,
                &#34;clientThirdPartyApps&#34;: [
                    {
                        &#34;appName&#34;: appname,
                        &#34;flags&#34;: 0,
                        &#34;appType&#34;: 5,
                        &#34;isEnabled&#34;: 1,
                        &#34;props&#34;: {
                            &#34;nameValues&#34;: props
                        },
                        &#34;assocTree&#34;: [
                            {
                                &#34;_type_&#34;: 13,
                                &#34;userName&#34;: user_name
                            } for user_name in user_to_be_added
                        ]
                    }
                ]
            }
        }

        response_json = self._commcell_object.qoperation_execute(third_party_json)

        if response_json.get(&#39;errorCode&#39;, 0) != 0:
            raise SDKException(
                &#39;IdentityManagement&#39;,
                &#39;103&#39;,
                &#39;Error: &#34;{}&#34;&#39;.format(response_json[&#39;errorMessage&#39;])
            )
        else:
            self.refresh()

    def has_identity_app(self, app_name):
        &#34;&#34;&#34;Checks if an identity app exits in the commcell

            Args:
                app_name    (str)   --  name of the identity app

            Returns:
                bool    -   boolean output whether the app exists in the commcell or not

            Raises:
                SDKException:
                    if type of the app name argument is not string
        &#34;&#34;&#34;
        if not isinstance(app_name, str):
            raise SDKException(&#39;IdentityManagement&#39;, &#39;102&#39;)

        return self._apps and app_name.lower() in self._apps

    def refresh(self, hard=False):
        &#34;&#34;&#34;Refresh the apps associated with the Commcell.
        
            Args:
                hard    (bool)  --  perform a hard refresh of the cache
                
        &#34;&#34;&#34;
        self._apps = self._get_apps(hard)</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.identity_management.IdentityManagementApps.all_apps"><code class="name">var <span class="ident">all_apps</span></code></dt>
<dd>
<div class="desc"><p>Returns the dictionary consisting of all the ID apps added to the Commcell.</p>
<p>dict - consists of all the apps configured on the commcell</p>
<pre><code>{
    "app1_name": app1_id,

    "app2_name": app2_id
}
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L302-L315" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def all_apps(self):
    &#34;&#34;&#34;Returns the dictionary consisting of all the ID apps added to the Commcell.

        dict - consists of all the apps configured on the commcell

            {
                &#34;app1_name&#34;: app1_id,

                &#34;app2_name&#34;: app2_id
            }

    &#34;&#34;&#34;
    return self._apps</code></pre>
</details>
</dd>
<dt id="cvpysdk.identity_management.IdentityManagementApps.get_commcell_identity_apps"><code class="name">var <span class="ident">get_commcell_identity_apps</span></code></dt>
<dd>
<div class="desc"><p>Returns a list of commcell apps for the local commcell</p>
<h2 id="returns">Returns</h2>
<p>list
-
List containing commcell apps in the SP commcell</p>
<pre><code>[
    app1_obj,
    app2_obj
]
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L283-L300" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def get_commcell_identity_apps(self):
    &#34;&#34;&#34;Returns a list of commcell apps for the local commcell

        Returns:
            list    -   List containing commcell apps in the SP commcell

                [
                    app1_obj,
                    app2_obj
                ]
    &#34;&#34;&#34;
    commcell_apps = []
    if self._apps:
        for app in self._apps:
            if self._apps[app][&#39;appType&#39;] == 3:
                commcell_apps.append(self.get(app))
        return commcell_apps</code></pre>
</details>
</dd>
<dt id="cvpysdk.identity_management.IdentityManagementApps.get_local_identity_app"><code class="name">var <span class="ident">get_local_identity_app</span></code></dt>
<dd>
<div class="desc"><p>Returns the local identity app details for IDP commcell</p>
<h2 id="returns">Returns</h2>
<p>object
-
object of IdentityManangementApp class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L271-L281" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def get_local_identity_app(self):
    &#34;&#34;&#34;Returns the local identity app details for IDP commcell

        Returns:
            object    -   object of IdentityManangementApp class
    &#34;&#34;&#34;
    if self._apps:
        for app in self._apps:
            if self._apps[app][&#39;appType&#39;] == 4:
                return self.get(app)</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.identity_management.IdentityManagementApps.configure_commcell_app"><code class="name flex">
<span>def <span class="ident">configure_commcell_app</span></span>(<span>self, idp_props, app_name, app_display_name, app_description='', user_assoc_list=None, user_mappings=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Creates a commcell app by associating speccified users</p>
<h2 id="args">Args</h2>
<p>idp_props
(list)
&ndash;
dict containing properties of the IDP's identity app</p>
<pre><code>[
    {
        "name": "SP Certificate Data",
        "value: "certificate1_str"
    },
    {
        "name": "JKS Private Key",
        "value: "key1_str"
    },
    {
        "name": "CommcellId",
        "value": "id1"
    },
    {
        "name": "RedirectUrl",
        "value": "url1"
    }
]
</code></pre>
<p>app_name
(str)
&ndash;
GUID for the app</p>
<p>app_display_name (str)
&ndash;
display name for the app</p>
<p>app_description
(str)
&ndash;
description for the app</p>
<p>user_assoc_list (list)
&ndash;
list of users for association</p>
<p>user_mappings
(dict)
&ndash;
dict containing mapping of IDP user to local user</p>
<pre><code>{
    "idp1_user":  "sp1_user",

    "idp2_user":  "sp2_user"
}
</code></pre>
<h2 id="returns">Returns</h2>
<p>object
-
returns object of IdentityManagementApp class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to configure identity app</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L511-L618" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def configure_commcell_app(self,
                           idp_props,
                           app_name,
                           app_display_name,
                           app_description=&#39;&#39;,
                           user_assoc_list=None,
                           user_mappings=None):
    &#34;&#34;&#34;Creates a commcell app by associating speccified users

        Args:
            idp_props      (list)     --  dict containing properties of the IDP&#39;s identity app

                [
                    {
                        &#34;name&#34;: &#34;SP Certificate Data&#34;,
                        &#34;value: &#34;certificate1_str&#34;
                    },
                    {
                        &#34;name&#34;: &#34;JKS Private Key&#34;,
                        &#34;value: &#34;key1_str&#34;
                    },
                    {
                        &#34;name&#34;: &#34;CommcellId&#34;,
                        &#34;value&#34;: &#34;id1&#34;
                    },
                    {
                        &#34;name&#34;: &#34;RedirectUrl&#34;,
                        &#34;value&#34;: &#34;url1&#34;
                    }
                ]

            app_name       (str)      --  GUID for the app

            app_display_name (str)    --  display name for the app

            app_description  (str)    --  description for the app

            user_assoc_list (list)    --  list of users for association

            user_mappings  (dict)     --  dict containing mapping of IDP user to local user

                {
                    &#34;idp1_user&#34;:  &#34;sp1_user&#34;,

                    &#34;idp2_user&#34;:  &#34;sp2_user&#34;
                }

        Returns:
            object  -   returns object of IdentityManagementApp class

        Raises:
            SDKException:
                if failed to configure identity app
    &#34;&#34;&#34;
    third_party_json = {
        &#39;opType&#39;: 1,
        &#39;clientThirdPartyApps&#39;: [
            {
                &#39;appName&#39;: app_name,
                &#39;appDisplayName&#39;: app_display_name,
                &#39;appDescription&#39;: app_description,
                &#39;flags&#39;: 0,
                &#39;appType&#39;: 3,
                &#39;isEnabled&#39;: True,
                &#39;UserMappings&#39;: {
                    &#39;opType&#39;: 2,
                    &#39;userslist&#39;: [
                        {
                            &#39;userfromToken&#39;: spuser,
                            &#34;localuser&#34;: {
                                &#34;userId&#34;: self._commcell_object.users.all_users[
                                    user_mappings[spuser]
                                ]
                            }
                        } for spuser in user_mappings
                    ]
                },
                &#39;props&#39;: {
                    &#39;nameValues&#39;: idp_props
                },
                &#39;assocTree&#39;: [
                    {
                        &#39;userId&#39;: self._commcell_object.users.all_users[user_name],
                        &#39;_type_&#39;: 13
                    } for user_name in user_assoc_list
                ]
            }
        ]
    }

    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._APPS, third_party_json
    )

    if flag:
        if response.json() and &#39;error&#39; in response.json():
            if response.json()[&#39;error&#39;][&#39;errorCode&#39;] == 0:
                self.refresh()
                return self.get_commcell_identity_apps
            else:
                raise SDKException(
                    &#39;IdentityManagement&#39;,
                    &#39;103&#39;,
                    &#39; - error {0}&#39;.format(response.json()[&#39;error&#39;][&#39;errorString&#39;])
                )
    else:
        response_string = self._update_response_(response.text)
        raise SDKException(&#39;&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.identity_management.IdentityManagementApps.configure_local_identity_app"><code class="name flex">
<span>def <span class="ident">configure_local_identity_app</span></span>(<span>self, user_list=None, optype=1)</span>
</code></dt>
<dd>
<div class="desc"><p>Creates a local identity app by associating speccified users</p>
<h2 id="args">Args</h2>
<p>user_list
(list)
&ndash;
list of names of users to be associated
with identity server
optype
(int)
&ndash;
operation type, 1 is 'create', 3 is 'overwrite'</p>
<h2 id="returns">Returns</h2>
<p>object
-
returns object of IdentityManagementApp class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to configure identity app</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L458-L509" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def configure_local_identity_app(self, user_list=None, optype=1):
    &#34;&#34;&#34;Creates a local identity app by associating speccified users

        Args:
            user_list      (list)     --  list of names of users to be associated
                                          with identity server
            optype         (int)      --  operation type, 1 is &#39;create&#39;, 3 is &#39;overwrite&#39;

        Returns:
            object  -   returns object of IdentityManagementApp class

        Raises:
            SDKException:
                if failed to configure identity app
    &#34;&#34;&#34;
    users_list = []
    if user_list.lower() == &#39;all&#39;:
        users_list += [{&#39;_type_&#39;: 12}]
    else:
        users_list += [
            {&#39;userId&#39;: self._commcell_object.users.all_users[user_name], &#39;_type_&#39;: 13}
            for user_name in user_list
        ]
    third_party_json = {
        &#39;opType&#39;: optype,
        &#39;clientThirdPartyApps&#39;: [
            {
                &#39;appType&#39;: 4,
                &#39;isEnabled&#39;: True,
                &#39;assocTree&#39;: users_list
            }
        ]
    }

    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._APPS, third_party_json
    )

    if flag:
        if response.json() and &#39;error&#39; in response.json():
            if response.json()[&#39;error&#39;][&#39;errorCode&#39;] == 0:
                self.refresh()
                return self.get_local_identity_app
            else:
                raise SDKException(
                    &#39;IdentityManagement&#39;,
                    &#39;103&#39;,
                    &#39; - error {0}&#39;.format(response.json()[&#39;error&#39;][&#39;errorString&#39;])
                )
    else:
        response_string = self._update_response_(response.text)
        raise SDKException(&#39;&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.identity_management.IdentityManagementApps.configure_openid_app"><code class="name flex">
<span>def <span class="ident">configure_openid_app</span></span>(<span>self, appname, props, user_to_be_added)</span>
</code></dt>
<dd>
<div class="desc"><p>Adding OpenID app</p>
<h2 id="args">Args</h2>
<p>appname (str)
:
Name of the app to be created</p>
<p>props
(list)
:
dict containing properties of the IDP's identity app</p>
<pre><code>    [
        {
                "name": "clientId",
                "value": "13445"
            },
            {
                "name": "clientSecret",
                "value": "ABC13567"
            },
            {
                "name": "endPointUrl",
                "value": "https://test.okta.com/.well-known/openid-configuration"
            },
            {
                "name": "webConsoleUrls",
                "values": [
                    &lt;https://mydomain:443/webconsole&gt;
                ]
            }
    ]
</code></pre>
<p>user_to_be_added
(list) :
list of users for association</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to configure identity app</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L620-L689" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def configure_openid_app(self, appname, props, user_to_be_added):
    &#34;&#34;&#34;
    Adding OpenID app

    Args:
        appname (str)           :       Name of the app to be created

        props      (list)       :  dict containing properties of the IDP&#39;s identity app

                [
                    {
                            &#34;name&#34;: &#34;clientId&#34;,
                            &#34;value&#34;: &#34;13445&#34;
                        },
                        {
                            &#34;name&#34;: &#34;clientSecret&#34;,
                            &#34;value&#34;: &#34;ABC13567&#34;
                        },
                        {
                            &#34;name&#34;: &#34;endPointUrl&#34;,
                            &#34;value&#34;: &#34;https://test.okta.com/.well-known/openid-configuration&#34;
                        },
                        {
                            &#34;name&#34;: &#34;webConsoleUrls&#34;,
                            &#34;values&#34;: [
                                https://mydomain:443/webconsole
                            ]
                        }
                ]

        user_to_be_added   (list) :   list of users for association

    Raises:
        SDKException:
            if failed to configure identity app

    &#34;&#34;&#34;
    third_party_json = {
        &#34;App_SetClientThirdPartyAppPropReq&#34;: {
            &#34;opType&#34;: 1,
            &#34;clientThirdPartyApps&#34;: [
                {
                    &#34;appName&#34;: appname,
                    &#34;flags&#34;: 0,
                    &#34;appType&#34;: 5,
                    &#34;isEnabled&#34;: 1,
                    &#34;props&#34;: {
                        &#34;nameValues&#34;: props
                    },
                    &#34;assocTree&#34;: [
                        {
                            &#34;_type_&#34;: 13,
                            &#34;userName&#34;: user_name
                        } for user_name in user_to_be_added
                    ]
                }
            ]
        }
    }

    response_json = self._commcell_object.qoperation_execute(third_party_json)

    if response_json.get(&#39;errorCode&#39;, 0) != 0:
        raise SDKException(
            &#39;IdentityManagement&#39;,
            &#39;103&#39;,
            &#39;Error: &#34;{}&#34;&#39;.format(response_json[&#39;errorMessage&#39;])
        )
    else:
        self.refresh()</code></pre>
</details>
</dd>
<dt id="cvpysdk.identity_management.IdentityManagementApps.configure_saml_app"><code class="name flex">
<span>def <span class="ident">configure_saml_app</span></span>(<span>self, app_name, desc, idp_metadata, sp_metadata, associations)</span>
</code></dt>
<dd>
<div class="desc"><p>Creates a saml app</p>
<h2 id="args">Args</h2>
<p>app_name
(string)
saml app name</p>
<p>desc
(string)
saml app description</p>
<p>idp_metadata
(dict)
idp_metadata = {
'entityId' : '',
'redirectUrl' : '',
'logoutUrl' : '',
'certificateData': '',
'SAMLProtocolVersion' : "urn:oasis:names:tc:SAML:2.0:metadata"
}
sp_metadata
(dict)
dict of serviceProviderEndpoint, autoGenerateSPMetaData, jksFileContents
sp_metadata = {
"serviceProviderEndpoint": "https://test.mydomain:443/webconsole",
"autoGenerateSPMetaData": true,
"jksFileContents":[]
}
associations
(dict)
dict of email suffixes, companies, domains and usergroups
associations = {
'emails' = ['a.com', b.com'],
'companies' = [],
'domains' = [],
'usergroups'= []
}</p>
<h2 id="returns">Returns</h2>
<p>object - returns object of SamlApp class</p>
<h2 id="raises">Raises</h2>
<dl>
<dt><code>SDKException</code></dt>
<dd>if failure in response
if invalid response</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L391-L456" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def configure_saml_app(self, app_name, desc, idp_metadata, sp_metadata, associations):
    &#34;&#34;&#34;Creates a saml app

        Args:
            app_name        (string)    saml app name

            desc            (string)    saml app description

            idp_metadata   (dict)  idp_metadata = {
                                        &#39;entityId&#39; : &#39;&#39;,
                                        &#39;redirectUrl&#39; : &#39;&#39;,
                                        &#39;logoutUrl&#39; : &#39;&#39;,
                                        &#39;certificateData&#39;: &#39;&#39;,
                                        &#39;SAMLProtocolVersion&#39; : &#34;urn:oasis:names:tc:SAML:2.0:metadata&#34;
                                    }
            sp_metadata      (dict)  dict of serviceProviderEndpoint, autoGenerateSPMetaData, jksFileContents
                                    sp_metadata = {
                                        &#34;serviceProviderEndpoint&#34;: &#34;https://test.mydomain:443/webconsole&#34;,
                                        &#34;autoGenerateSPMetaData&#34;: true,
                                        &#34;jksFileContents&#34;:[]
                                    }
            associations    (dict)  dict of email suffixes, companies, domains and usergroups
                                    associations = {
                                        &#39;emails&#39; = [&#39;a.com&#39;, b.com&#39;],
                                        &#39;companies&#39; = [],
                                        &#39;domains&#39; = [],
                                        &#39;usergroups&#39;= []
                                    }

        Returns:
            object - returns object of SamlApp class

        Raises:
            SDKException:   if failure in response
                            if invalid response
    &#34;&#34;&#34;
    req_body = {
        &#34;name&#34;: app_name,
        &#34;description&#34;: desc,
        &#34;identityProviderMetaData&#34;: idp_metadata,
        &#34;serviceProviderMetaData&#34;: sp_metadata,
        &#34;associations&#34;: associations
    }
    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._ADD_SAML, req_body
    )

    if flag:
        if response.json() and &#39;errorCode&#39; in response.json():
            if response.json()[&#39;errorCode&#39;] == 0:
                self.refresh()
                return SamlApp(
                    self._commcell_object,
                    app_name
                )
            else:
                raise SDKException(
                    &#39;IdentityManagement&#39;,
                    &#39;103&#39;,
                    &#39; - error {0}&#39;.format(response.json()[&#39;errorMessage&#39;])
                )
        else:
            raise SDKException(&#39;Response&#39;, &#39;500&#39; + &#39;Invalid Response Returned&#39;)
    else:
        response_string = self._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.identity_management.IdentityManagementApps.delete_identity_app"><code class="name flex">
<span>def <span class="ident">delete_identity_app</span></span>(<span>self, app_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes the specified local identity app</p>
<h2 id="args">Args</h2>
<p>app_name
(str)
&ndash; name of the app to be deleted</p>
<h2 id="returns">Returns</h2>
<p>bool
-
True if operation succeeds</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if passed app not found</p>
<pre><code>if failure in response
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L317-L359" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete_identity_app(self, app_name):
    &#34;&#34;&#34;Deletes the specified local identity app

        Args:
            app_name     (str)      -- name of the app to be deleted

        Returns:
            bool    -   True if operation succeeds

        Raises:
            SDKException:
                if passed app not found

                if failure in response
    &#34;&#34;&#34;
    draft_json = self._apps.get(app_name)

    if draft_json:
        req_json = {
            &#39;opType&#39;: 2,
            &#39;clientThirdPartyApps&#39;: [
                draft_json
            ]
        }
    else:
        raise SDKException(&#39;IdentityManagement&#39;, &#39;102&#39;)

    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._APPS, req_json
    )
    if flag:
        if response.json() and &#39;error&#39; in response.json():
            if response.json()[&#39;error&#39;][&#39;errorCode&#39;] == 0:
                self.refresh()
            else:
                raise SDKException(
                    &#39;Response&#39;,
                    &#39;101&#39;,
                    response.json()[&#39;error&#39;][&#39;warningMessage&#39;]
                )
    else:
        response_string = self._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.identity_management.IdentityManagementApps.delete_saml_app"><code class="name flex">
<span>def <span class="ident">delete_saml_app</span></span>(<span>self, app_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes the specified saml app</p>
<h2 id="args">Args</h2>
<p>app_name
(string) name of the saml app
Raises :
SDK Exception :
if failure in response
if invalid response</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L361-L389" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete_saml_app(self, app_name):
    &#34;&#34;&#34;Deletes the specified saml app
        Args:
            app_name       (string) name of the saml app

        Raises :
            SDK Exception :
                if failure in response
                if invalid response
    &#34;&#34;&#34;
    flag, response = self._cvpysdk_object.make_request(
        &#39;DELETE&#39;, self._SAML % app_name
    )
    if flag:
        if response.json() and &#39;errorCode&#39; in response.json():
            if response.json()[&#39;errorCode&#39;] == 0:
                self.refresh()
            else:
                raise SDKException(
                    &#39;IdentityManagement&#39;,
                    &#39;104&#39;,
                    &#39; - error {0}&#39;.format(response.json()[&#39;errorMessage&#39;])
                )
        else:
            raise SDKException(&#39;Response&#39;, &#39;500&#39; + &#39;Invalid Response Returned&#39;)

    else:
        response_string = self._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.identity_management.IdentityManagementApps.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, app_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a identitymanagementapp object of the specified app name</p>
<h2 id="args">Args</h2>
<p>app_name
(str)
&ndash;
name of the app</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of IdentityManagementApp class for the given app name</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the app name argument is not string</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L220-L244" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, app_name):
    &#34;&#34;&#34;Returns a identitymanagementapp object of the specified app name

        Args:
            app_name    (str)   --  name of the app

        Returns:
            object  -   instance of IdentityManagementApp class for the given app name

        Raises:
            SDKException:
                if type of the app name argument is not string
    &#34;&#34;&#34;
    if not isinstance(app_name, str):
        raise SDKException(&#39;IdentityManagement&#39;, &#39;101&#39;)
    else:
        app_name = app_name.lower()
        if self.has_identity_app(app_name):
            return IdentityManagementApp(
                self._commcell_object,
                app_name,
                self._apps[app_name]
            )

        raise SDKException(&#39;IdentityManagement&#39;, &#39;102&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.identity_management.IdentityManagementApps.get_saml"><code class="name flex">
<span>def <span class="ident">get_saml</span></span>(<span>self, app_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a SamlApp object of the specified app name</p>
<h2 id="args">Args</h2>
<p>app_name
(str)
&ndash;
name of the saml app</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of SamlApp class for the given app name</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the app name argument is not string</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L246-L269" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_saml(self, app_name):
    &#34;&#34;&#34;Returns a SamlApp object of the specified app name

        Args:
            app_name    (str)   --  name of the saml app

        Returns:
            object  -   instance of SamlApp class for the given app name

        Raises:
            SDKException:
                if type of the app name argument is not string
    &#34;&#34;&#34;
    if not isinstance(app_name, str):
        raise SDKException(&#39;IdentityManagement&#39;, &#39;101&#39;)
    else:
        app_name = app_name.lower()
        if self.has_identity_app(app_name):
            return SamlApp(
                self._commcell_object,
                app_name
            )

        raise SDKException(&#39;IdentityManagement&#39;, &#39;102&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.identity_management.IdentityManagementApps.has_identity_app"><code class="name flex">
<span>def <span class="ident">has_identity_app</span></span>(<span>self, app_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks if an identity app exits in the commcell</p>
<h2 id="args">Args</h2>
<p>app_name
(str)
&ndash;
name of the identity app</p>
<h2 id="returns">Returns</h2>
<p>bool
-
boolean output whether the app exists in the commcell or not</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the app name argument is not string</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L691-L707" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_identity_app(self, app_name):
    &#34;&#34;&#34;Checks if an identity app exits in the commcell

        Args:
            app_name    (str)   --  name of the identity app

        Returns:
            bool    -   boolean output whether the app exists in the commcell or not

        Raises:
            SDKException:
                if type of the app name argument is not string
    &#34;&#34;&#34;
    if not isinstance(app_name, str):
        raise SDKException(&#39;IdentityManagement&#39;, &#39;102&#39;)

    return self._apps and app_name.lower() in self._apps</code></pre>
</details>
</dd>
<dt id="cvpysdk.identity_management.IdentityManagementApps.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self, hard=False)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the apps associated with the Commcell.</p>
<h2 id="args">Args</h2>
<p>hard
(bool)
&ndash;
perform a hard refresh of the cache</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L709-L716" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self, hard=False):
    &#34;&#34;&#34;Refresh the apps associated with the Commcell.
    
        Args:
            hard    (bool)  --  perform a hard refresh of the cache
            
    &#34;&#34;&#34;
    self._apps = self._get_apps(hard)</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.identity_management.SamlApp"><code class="flex name class">
<span>class <span class="ident">SamlApp</span></span>
<span>(</span><span>commcell, appname, properties=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for performing operations on a specific saml app</p>
<p>Initialise SamlApp class</p>
<h2 id="args">Args</h2>
<p>commcell
(object)
instance of commcell class</p>
<p>appname
(string)
saml app name</p>
<p>properties
(dict)
dict containing properties of saml app. Default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the SamlApp class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L868-L1129" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class SamlApp(object):
    &#34;&#34;&#34;Class for performing operations on a specific saml app&#34;&#34;&#34;

    def __init__(self, commcell, appname, properties=None):
        &#34;&#34;&#34;Initialise SamlApp class
            Args:
                commcell            (object)        instance of commcell class

                appname             (string)        saml app name

                properties          (dict)          dict containing properties of saml app. Default: None

            Returns:
                object - instance of the SamlApp class
        &#34;&#34;&#34;

        self._commcell = commcell
        self._cvpysdk_object = commcell._cvpysdk_object
        self._update_response_ = commcell._update_response_
        self._appname = appname
        self._properties = None
        self._SAML = commcell._services[&#39;EDIT_SAML&#39;]
        self._redirecturl = commcell._services[&#39;POLL_REQUEST_ROUTER&#39;]
        self._APPS = commcell._services[&#39;IDENTITY_APPS&#39;]
        self._SAML_PROP = commcell._services[&#39;GET_SAML_PROP&#39;]

        if properties:
            self._properties = properties
        else:
            self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        representation_string = &#39;SamlApp class instance for app: \
                                &#34;{0}&#34;, of Commcell: &#34;{1}&#34;&#39;

        return representation_string.format(
            self._appname, self._commcell.commserv_name
        )

    def refresh(self):
        &#34;&#34;&#34;Refresh the saml app properties&#34;&#34;&#34;
        self._properties = self._get_saml_app_details()

    def _get_saml_app_details(self):
        &#34;&#34;&#34;gets the properties of a saml app
        Returns:
                        prop        (dict)      properties of a saml app

        Raises:
                SDK Exception:
                    if saml app is not found
                    if request is not successful
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._SAML % self._appname
        )
        if flag:
            if response.json() and &#39;name&#39; in response.json():
                if response.json()[&#39;name&#39;] == self._appname:
                    return response.json()
                else:
                    raise SDKException(
                        &#39;IdentityManagement&#39;,
                        &#39;102&#39;,
                        &#39; - error {0}&#39;.format(response.json()[&#39;errorMessage&#39;])
                    )
            else:
                raise SDKException(&#39;Response&#39;, &#39;500&#39; + &#39;Invalid Response Returned&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def get_all_saml_app_prop(self) -&gt; dict:
        &#34;&#34;&#34;Returns a dict containing the properties of SAML app.

            Returns:
                dict    -   properties of the saml app

            Raises:
                SDKException:
                    if response is not success
        &#34;&#34;&#34;
        req_json = {
            &#34;appKeys&#34;: [self._properties[&#39;appKey&#39;]],
            &#34;propLevel&#34;: 30
        }
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._SAML_PROP, req_json)
        if flag:
            if response.json() and &#39;clientThirdPartyApps&#39; in response.json():
                return response.json()[&#39;clientThirdPartyApps&#39;][0]
            else:
                raise SDKException(
                    &#39;IdentityManagement&#39;,
                    &#39;102&#39;,
                    &#39; - error {0}&#39;.format(response.text)
                )
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)


    def modify_saml_app(self, req_body):
        &#34;&#34;&#34;Modifies a saml app
            Args:
                req_body  (json)       saml app properties in json format

            Raises:
                SDKException:
                    if failed to modify saml app
                    if request is not successful

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            &#39;PUT&#39;, self._SAML % self._appname, req_body
        )
        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                if response.json()[&#39;errorCode&#39;] == 0:
                    self.refresh()
                else:
                    raise SDKException(
                        &#39;IdentityManagement&#39;,
                        &#39;105&#39;,
                        &#39; - error {0}&#39;.format(response.json()[&#39;errorMessage&#39;])
                    )
            else:
                raise SDKException(&#39;Response&#39;, &#39;500&#39; + &#39;Invalid Response Returned&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def set_auto_redirect_to_idp(self, enable:bool=True) -&gt; None:
        &#34;&#34;&#34;Enable/Disable auto redirection to IDP

        Args:
            enable (bool)   :   True to enable, False to disable

        Raises:
            SDKException:
                if request is not successful
        &#34;&#34;&#34;
        # preserve the state of other flags
        flags = self.get_all_saml_app_prop()[&#39;flags&#39;]
        if enable:
            flags |= 1
        else:
            flags &amp;= ~1

        req_json = {
                        &#34;opType&#34;: 3,
                        &#34;clientThirdPartyApps&#34;: [
                            {
                                &#34;flags&#34;: flags,
                                &#34;appType&#34;: 2,
                                &#34;appKey&#34;: self._properties[&#39;appKey&#39;]
                            }
                        ]
                    }
        flag, response = self._cvpysdk_object.make_request(&#39;PUT&#39;, self._APPS, req_json)
        if flag:
            if response.json():
                error_code = response.json().get(&#39;errorCode&#39;, 0)
                if error_code != 0:
                    raise SDKException(
                        &#39;Response&#39;, &#39;101&#39;,
                        self._update_response_(response.text)
                    )
                return
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        raise SDKException(
            &#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def get_saml_user_redirect_url(self, user_email):
        &#34;&#34;&#34;Get Redirect Url of SAML User
        Args:
            user_email         (str)        user email

        Returns :
                redirect url of user, None if redirect url is not found for the user
        Raises:
                SDKException:
                    if failed to get redirect url
                    if request is not successful
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._redirecturl % user_email
        )
        if flag:
            if response.json():
                if &#39;AvailableRedirects&#39; in response.json():
                    if len(response.json()[&#39;AvailableRedirects&#39;]) &gt; 0:
                        return response.json()[&#39;AvailableRedirects&#39;][0].get(&#39;redirectUrl&#39;)
                    else:
                        return None
                else:
                    raise SDKException(
                        &#39;IdentityManagement&#39;,
                        &#39;106&#39;,
                        &#39; - error {0}&#39;.format(response.json()[&#39;error&#39;])
                    )
            else:
                raise SDKException(&#39;Response&#39;, &#39;500&#39; + &#39;Invalid Response Returned&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def saml_app_description(self):
        &#34;&#34;&#34;Treats the saml_app_description as a read-only attribute.&#34;&#34;&#34;
        return self._properties.get(&#39;description&#39;)

    @property
    def is_saml_app_enabled(self):
        &#34;&#34;&#34;Treats the is_saml_app_enabled as a read-only attribute.&#34;&#34;&#34;
        return self._properties.get(&#39;enabled&#39;)

    @property
    def is_auto_create_user(self):
        &#34;&#34;&#34;Treats the is_auto_create_user as a read-only attribute.&#34;&#34;&#34;
        return self._properties.get(&#39;autoCreateUser&#39;)

    @property
    def saml_app_default_user_groups(self):
        &#34;&#34;&#34;Treats the saml_app_default_user_groups as a read-only attribute.&#34;&#34;&#34;
        return self._properties.get(&#39;userGroups&#39;)

    @property
    def saml_app_nameid_attribute(self):
        &#34;&#34;&#34;Treats the saml_app_nameid_attribute as a read-only attribute.&#34;&#34;&#34;
        return self._properties.get(&#39;nameIDAttribute&#39;)

    @property
    def saml_app_attribute_mappings(self):
        &#34;&#34;&#34;Treats the saml_app_attribute_mappings as a read-only attribute.&#34;&#34;&#34;
        return self._properties.get(&#39;attributeMappings&#39;)

    @property
    def saml_app_identity_provider_metadata(self):
        &#34;&#34;&#34;Treats the saml_app_identity_provider_metadata as a read-only attribute.&#34;&#34;&#34;
        return self._properties.get(&#39;identityProviderMetaData&#39;)

    @property
    def saml_app_service_provider_metadata(self):
        &#34;&#34;&#34;Treats the saml_app_service_provider_metadata as a read-only attribute.&#34;&#34;&#34;
        return self._properties.get(&#39;serviceProviderMetaData&#39;)

    @property
    def saml_app_associations(self):
        &#34;&#34;&#34;Treats the saml_app_associations as a read-only attribute.&#34;&#34;&#34;
        return self._properties.get(&#39;associations&#39;)

    @property
    def is_company_saml_app(self):
        &#34;&#34;&#34;Treats the is_company_saml_app as a read-only attribute.
            Returns
                    True if saml app is created for a company, False otherwise
        &#34;&#34;&#34;
        if self._properties.get(&#39;createdForCompany&#39;):
            return True
        else:
            return False</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.identity_management.SamlApp.is_auto_create_user"><code class="name">var <span class="ident">is_auto_create_user</span></code></dt>
<dd>
<div class="desc"><p>Treats the is_auto_create_user as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L1085-L1088" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_auto_create_user(self):
    &#34;&#34;&#34;Treats the is_auto_create_user as a read-only attribute.&#34;&#34;&#34;
    return self._properties.get(&#39;autoCreateUser&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.identity_management.SamlApp.is_company_saml_app"><code class="name">var <span class="ident">is_company_saml_app</span></code></dt>
<dd>
<div class="desc"><p>Treats the is_company_saml_app as a read-only attribute.
Returns
True if saml app is created for a company, False otherwise</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L1120-L1129" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_company_saml_app(self):
    &#34;&#34;&#34;Treats the is_company_saml_app as a read-only attribute.
        Returns
                True if saml app is created for a company, False otherwise
    &#34;&#34;&#34;
    if self._properties.get(&#39;createdForCompany&#39;):
        return True
    else:
        return False</code></pre>
</details>
</dd>
<dt id="cvpysdk.identity_management.SamlApp.is_saml_app_enabled"><code class="name">var <span class="ident">is_saml_app_enabled</span></code></dt>
<dd>
<div class="desc"><p>Treats the is_saml_app_enabled as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L1080-L1083" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_saml_app_enabled(self):
    &#34;&#34;&#34;Treats the is_saml_app_enabled as a read-only attribute.&#34;&#34;&#34;
    return self._properties.get(&#39;enabled&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.identity_management.SamlApp.saml_app_associations"><code class="name">var <span class="ident">saml_app_associations</span></code></dt>
<dd>
<div class="desc"><p>Treats the saml_app_associations as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L1115-L1118" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def saml_app_associations(self):
    &#34;&#34;&#34;Treats the saml_app_associations as a read-only attribute.&#34;&#34;&#34;
    return self._properties.get(&#39;associations&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.identity_management.SamlApp.saml_app_attribute_mappings"><code class="name">var <span class="ident">saml_app_attribute_mappings</span></code></dt>
<dd>
<div class="desc"><p>Treats the saml_app_attribute_mappings as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L1100-L1103" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def saml_app_attribute_mappings(self):
    &#34;&#34;&#34;Treats the saml_app_attribute_mappings as a read-only attribute.&#34;&#34;&#34;
    return self._properties.get(&#39;attributeMappings&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.identity_management.SamlApp.saml_app_default_user_groups"><code class="name">var <span class="ident">saml_app_default_user_groups</span></code></dt>
<dd>
<div class="desc"><p>Treats the saml_app_default_user_groups as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L1090-L1093" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def saml_app_default_user_groups(self):
    &#34;&#34;&#34;Treats the saml_app_default_user_groups as a read-only attribute.&#34;&#34;&#34;
    return self._properties.get(&#39;userGroups&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.identity_management.SamlApp.saml_app_description"><code class="name">var <span class="ident">saml_app_description</span></code></dt>
<dd>
<div class="desc"><p>Treats the saml_app_description as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L1075-L1078" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def saml_app_description(self):
    &#34;&#34;&#34;Treats the saml_app_description as a read-only attribute.&#34;&#34;&#34;
    return self._properties.get(&#39;description&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.identity_management.SamlApp.saml_app_identity_provider_metadata"><code class="name">var <span class="ident">saml_app_identity_provider_metadata</span></code></dt>
<dd>
<div class="desc"><p>Treats the saml_app_identity_provider_metadata as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L1105-L1108" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def saml_app_identity_provider_metadata(self):
    &#34;&#34;&#34;Treats the saml_app_identity_provider_metadata as a read-only attribute.&#34;&#34;&#34;
    return self._properties.get(&#39;identityProviderMetaData&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.identity_management.SamlApp.saml_app_nameid_attribute"><code class="name">var <span class="ident">saml_app_nameid_attribute</span></code></dt>
<dd>
<div class="desc"><p>Treats the saml_app_nameid_attribute as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L1095-L1098" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def saml_app_nameid_attribute(self):
    &#34;&#34;&#34;Treats the saml_app_nameid_attribute as a read-only attribute.&#34;&#34;&#34;
    return self._properties.get(&#39;nameIDAttribute&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.identity_management.SamlApp.saml_app_service_provider_metadata"><code class="name">var <span class="ident">saml_app_service_provider_metadata</span></code></dt>
<dd>
<div class="desc"><p>Treats the saml_app_service_provider_metadata as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L1110-L1113" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def saml_app_service_provider_metadata(self):
    &#34;&#34;&#34;Treats the saml_app_service_provider_metadata as a read-only attribute.&#34;&#34;&#34;
    return self._properties.get(&#39;serviceProviderMetaData&#39;)</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.identity_management.SamlApp.get_all_saml_app_prop"><code class="name flex">
<span>def <span class="ident">get_all_saml_app_prop</span></span>(<span>self) ‑> dict</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a dict containing the properties of SAML app.</p>
<h2 id="returns">Returns</h2>
<p>dict
-
properties of the saml app</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if response is not success</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L941-L967" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_all_saml_app_prop(self) -&gt; dict:
    &#34;&#34;&#34;Returns a dict containing the properties of SAML app.

        Returns:
            dict    -   properties of the saml app

        Raises:
            SDKException:
                if response is not success
    &#34;&#34;&#34;
    req_json = {
        &#34;appKeys&#34;: [self._properties[&#39;appKey&#39;]],
        &#34;propLevel&#34;: 30
    }
    flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._SAML_PROP, req_json)
    if flag:
        if response.json() and &#39;clientThirdPartyApps&#39; in response.json():
            return response.json()[&#39;clientThirdPartyApps&#39;][0]
        else:
            raise SDKException(
                &#39;IdentityManagement&#39;,
                &#39;102&#39;,
                &#39; - error {0}&#39;.format(response.text)
            )
    else:
        response_string = self._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.identity_management.SamlApp.get_saml_user_redirect_url"><code class="name flex">
<span>def <span class="ident">get_saml_user_redirect_url</span></span>(<span>self, user_email)</span>
</code></dt>
<dd>
<div class="desc"><p>Get Redirect Url of SAML User</p>
<h2 id="args">Args</h2>
<p>user_email
(str)
user email
Returns :
redirect url of user, None if redirect url is not found for the user</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to get redirect url
if request is not successful</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L1041-L1073" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_saml_user_redirect_url(self, user_email):
    &#34;&#34;&#34;Get Redirect Url of SAML User
    Args:
        user_email         (str)        user email

    Returns :
            redirect url of user, None if redirect url is not found for the user
    Raises:
            SDKException:
                if failed to get redirect url
                if request is not successful
    &#34;&#34;&#34;
    flag, response = self._cvpysdk_object.make_request(
        &#39;GET&#39;, self._redirecturl % user_email
    )
    if flag:
        if response.json():
            if &#39;AvailableRedirects&#39; in response.json():
                if len(response.json()[&#39;AvailableRedirects&#39;]) &gt; 0:
                    return response.json()[&#39;AvailableRedirects&#39;][0].get(&#39;redirectUrl&#39;)
                else:
                    return None
            else:
                raise SDKException(
                    &#39;IdentityManagement&#39;,
                    &#39;106&#39;,
                    &#39; - error {0}&#39;.format(response.json()[&#39;error&#39;])
                )
        else:
            raise SDKException(&#39;Response&#39;, &#39;500&#39; + &#39;Invalid Response Returned&#39;)
    else:
        response_string = self._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.identity_management.SamlApp.modify_saml_app"><code class="name flex">
<span>def <span class="ident">modify_saml_app</span></span>(<span>self, req_body)</span>
</code></dt>
<dd>
<div class="desc"><p>Modifies a saml app</p>
<h2 id="args">Args</h2>
<p>req_body
(json)
saml app properties in json format</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to modify saml app
if request is not successful</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L970-L998" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def modify_saml_app(self, req_body):
    &#34;&#34;&#34;Modifies a saml app
        Args:
            req_body  (json)       saml app properties in json format

        Raises:
            SDKException:
                if failed to modify saml app
                if request is not successful

    &#34;&#34;&#34;
    flag, response = self._cvpysdk_object.make_request(
        &#39;PUT&#39;, self._SAML % self._appname, req_body
    )
    if flag:
        if response.json() and &#39;errorCode&#39; in response.json():
            if response.json()[&#39;errorCode&#39;] == 0:
                self.refresh()
            else:
                raise SDKException(
                    &#39;IdentityManagement&#39;,
                    &#39;105&#39;,
                    &#39; - error {0}&#39;.format(response.json()[&#39;errorMessage&#39;])
                )
        else:
            raise SDKException(&#39;Response&#39;, &#39;500&#39; + &#39;Invalid Response Returned&#39;)
    else:
        response_string = self._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.identity_management.SamlApp.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the saml app properties</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L908-L910" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the saml app properties&#34;&#34;&#34;
    self._properties = self._get_saml_app_details()</code></pre>
</details>
</dd>
<dt id="cvpysdk.identity_management.SamlApp.set_auto_redirect_to_idp"><code class="name flex">
<span>def <span class="ident">set_auto_redirect_to_idp</span></span>(<span>self, enable: bool = True) ‑> None</span>
</code></dt>
<dd>
<div class="desc"><p>Enable/Disable auto redirection to IDP</p>
<h2 id="args">Args</h2>
<p>enable (bool)
:
True to enable, False to disable</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if request is not successful</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/identity_management.py#L1000-L1039" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_auto_redirect_to_idp(self, enable:bool=True) -&gt; None:
    &#34;&#34;&#34;Enable/Disable auto redirection to IDP

    Args:
        enable (bool)   :   True to enable, False to disable

    Raises:
        SDKException:
            if request is not successful
    &#34;&#34;&#34;
    # preserve the state of other flags
    flags = self.get_all_saml_app_prop()[&#39;flags&#39;]
    if enable:
        flags |= 1
    else:
        flags &amp;= ~1

    req_json = {
                    &#34;opType&#34;: 3,
                    &#34;clientThirdPartyApps&#34;: [
                        {
                            &#34;flags&#34;: flags,
                            &#34;appType&#34;: 2,
                            &#34;appKey&#34;: self._properties[&#39;appKey&#39;]
                        }
                    ]
                }
    flag, response = self._cvpysdk_object.make_request(&#39;PUT&#39;, self._APPS, req_json)
    if flag:
        if response.json():
            error_code = response.json().get(&#39;errorCode&#39;, 0)
            if error_code != 0:
                raise SDKException(
                    &#39;Response&#39;, &#39;101&#39;,
                    self._update_response_(response.text)
                )
            return
        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    raise SDKException(
        &#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#identitymanagementapps">IdentityManagementApps</a></li>
<li><a href="#identitymanagementapp">IdentityManagementApp</a></li>
<li><a href="#samlapp">SamlApp</a></li>
<li><a href="#samlapp-instance-attributes">SamlApp instance Attributes</a></li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.identity_management.IdentityManagementApp" href="#cvpysdk.identity_management.IdentityManagementApp">IdentityManagementApp</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.identity_management.IdentityManagementApp.app_description" href="#cvpysdk.identity_management.IdentityManagementApp.app_description">app_description</a></code></li>
<li><code><a title="cvpysdk.identity_management.IdentityManagementApp.app_key" href="#cvpysdk.identity_management.IdentityManagementApp.app_key">app_key</a></code></li>
<li><code><a title="cvpysdk.identity_management.IdentityManagementApp.app_name" href="#cvpysdk.identity_management.IdentityManagementApp.app_name">app_name</a></code></li>
<li><code><a title="cvpysdk.identity_management.IdentityManagementApp.app_type" href="#cvpysdk.identity_management.IdentityManagementApp.app_type">app_type</a></code></li>
<li><code><a title="cvpysdk.identity_management.IdentityManagementApp.flags" href="#cvpysdk.identity_management.IdentityManagementApp.flags">flags</a></code></li>
<li><code><a title="cvpysdk.identity_management.IdentityManagementApp.get_app_props" href="#cvpysdk.identity_management.IdentityManagementApp.get_app_props">get_app_props</a></code></li>
<li><code><a title="cvpysdk.identity_management.IdentityManagementApp.is_enabled" href="#cvpysdk.identity_management.IdentityManagementApp.is_enabled">is_enabled</a></code></li>
<li><code><a title="cvpysdk.identity_management.IdentityManagementApp.refresh" href="#cvpysdk.identity_management.IdentityManagementApp.refresh">refresh</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.identity_management.IdentityManagementApps" href="#cvpysdk.identity_management.IdentityManagementApps">IdentityManagementApps</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.identity_management.IdentityManagementApps.all_apps" href="#cvpysdk.identity_management.IdentityManagementApps.all_apps">all_apps</a></code></li>
<li><code><a title="cvpysdk.identity_management.IdentityManagementApps.configure_commcell_app" href="#cvpysdk.identity_management.IdentityManagementApps.configure_commcell_app">configure_commcell_app</a></code></li>
<li><code><a title="cvpysdk.identity_management.IdentityManagementApps.configure_local_identity_app" href="#cvpysdk.identity_management.IdentityManagementApps.configure_local_identity_app">configure_local_identity_app</a></code></li>
<li><code><a title="cvpysdk.identity_management.IdentityManagementApps.configure_openid_app" href="#cvpysdk.identity_management.IdentityManagementApps.configure_openid_app">configure_openid_app</a></code></li>
<li><code><a title="cvpysdk.identity_management.IdentityManagementApps.configure_saml_app" href="#cvpysdk.identity_management.IdentityManagementApps.configure_saml_app">configure_saml_app</a></code></li>
<li><code><a title="cvpysdk.identity_management.IdentityManagementApps.delete_identity_app" href="#cvpysdk.identity_management.IdentityManagementApps.delete_identity_app">delete_identity_app</a></code></li>
<li><code><a title="cvpysdk.identity_management.IdentityManagementApps.delete_saml_app" href="#cvpysdk.identity_management.IdentityManagementApps.delete_saml_app">delete_saml_app</a></code></li>
<li><code><a title="cvpysdk.identity_management.IdentityManagementApps.get" href="#cvpysdk.identity_management.IdentityManagementApps.get">get</a></code></li>
<li><code><a title="cvpysdk.identity_management.IdentityManagementApps.get_commcell_identity_apps" href="#cvpysdk.identity_management.IdentityManagementApps.get_commcell_identity_apps">get_commcell_identity_apps</a></code></li>
<li><code><a title="cvpysdk.identity_management.IdentityManagementApps.get_local_identity_app" href="#cvpysdk.identity_management.IdentityManagementApps.get_local_identity_app">get_local_identity_app</a></code></li>
<li><code><a title="cvpysdk.identity_management.IdentityManagementApps.get_saml" href="#cvpysdk.identity_management.IdentityManagementApps.get_saml">get_saml</a></code></li>
<li><code><a title="cvpysdk.identity_management.IdentityManagementApps.has_identity_app" href="#cvpysdk.identity_management.IdentityManagementApps.has_identity_app">has_identity_app</a></code></li>
<li><code><a title="cvpysdk.identity_management.IdentityManagementApps.refresh" href="#cvpysdk.identity_management.IdentityManagementApps.refresh">refresh</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.identity_management.SamlApp" href="#cvpysdk.identity_management.SamlApp">SamlApp</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.identity_management.SamlApp.get_all_saml_app_prop" href="#cvpysdk.identity_management.SamlApp.get_all_saml_app_prop">get_all_saml_app_prop</a></code></li>
<li><code><a title="cvpysdk.identity_management.SamlApp.get_saml_user_redirect_url" href="#cvpysdk.identity_management.SamlApp.get_saml_user_redirect_url">get_saml_user_redirect_url</a></code></li>
<li><code><a title="cvpysdk.identity_management.SamlApp.is_auto_create_user" href="#cvpysdk.identity_management.SamlApp.is_auto_create_user">is_auto_create_user</a></code></li>
<li><code><a title="cvpysdk.identity_management.SamlApp.is_company_saml_app" href="#cvpysdk.identity_management.SamlApp.is_company_saml_app">is_company_saml_app</a></code></li>
<li><code><a title="cvpysdk.identity_management.SamlApp.is_saml_app_enabled" href="#cvpysdk.identity_management.SamlApp.is_saml_app_enabled">is_saml_app_enabled</a></code></li>
<li><code><a title="cvpysdk.identity_management.SamlApp.modify_saml_app" href="#cvpysdk.identity_management.SamlApp.modify_saml_app">modify_saml_app</a></code></li>
<li><code><a title="cvpysdk.identity_management.SamlApp.refresh" href="#cvpysdk.identity_management.SamlApp.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.identity_management.SamlApp.saml_app_associations" href="#cvpysdk.identity_management.SamlApp.saml_app_associations">saml_app_associations</a></code></li>
<li><code><a title="cvpysdk.identity_management.SamlApp.saml_app_attribute_mappings" href="#cvpysdk.identity_management.SamlApp.saml_app_attribute_mappings">saml_app_attribute_mappings</a></code></li>
<li><code><a title="cvpysdk.identity_management.SamlApp.saml_app_default_user_groups" href="#cvpysdk.identity_management.SamlApp.saml_app_default_user_groups">saml_app_default_user_groups</a></code></li>
<li><code><a title="cvpysdk.identity_management.SamlApp.saml_app_description" href="#cvpysdk.identity_management.SamlApp.saml_app_description">saml_app_description</a></code></li>
<li><code><a title="cvpysdk.identity_management.SamlApp.saml_app_identity_provider_metadata" href="#cvpysdk.identity_management.SamlApp.saml_app_identity_provider_metadata">saml_app_identity_provider_metadata</a></code></li>
<li><code><a title="cvpysdk.identity_management.SamlApp.saml_app_nameid_attribute" href="#cvpysdk.identity_management.SamlApp.saml_app_nameid_attribute">saml_app_nameid_attribute</a></code></li>
<li><code><a title="cvpysdk.identity_management.SamlApp.saml_app_service_provider_metadata" href="#cvpysdk.identity_management.SamlApp.saml_app_service_provider_metadata">saml_app_service_provider_metadata</a></code></li>
<li><code><a title="cvpysdk.identity_management.SamlApp.set_auto_redirect_to_idp" href="#cvpysdk.identity_management.SamlApp.set_auto_redirect_to_idp">set_auto_redirect_to_idp</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>