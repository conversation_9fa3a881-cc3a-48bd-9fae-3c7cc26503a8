<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.download_center API documentation</title>
<meta name="description" content="File for doing operations on Download Center …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.download_center</code></h1>
</header>
<section id="section-intro">
<p>File for doing operations on Download Center.</p>
<p>DownloadCenter is the class defined in this module for doing operations on the Download Center.</p>
<p>Following Operations can be performed on the Download Center:</p>
<pre><code>1.  Add a new category to Download Center

2.  Add a new sub category to the specified category to Download Center

3.  Upload a package to Download Center

4.  Download a package from Download Center

5.  Delete a sub category from the specified category on Download Center

6.  Delete a category from Download Center

7.  Delete a package from Download Center

8.  Update the category information

9.  Update the sub category information for the specified category
</code></pre>
<h2 id="downloadcenter">Downloadcenter</h2>
<p><strong>init</strong>(commcell_object)
&ndash;
initializes a connection to the download center</p>
<p><strong>repr</strong>()
&ndash;
returns the string representation of an instance of this class</p>
<p>_get_properties()
&ndash;
get the properties of the download center</p>
<p>_get_packages()
&ndash;
get the packages available at download center</p>
<p>_process_category_request() &ndash;
executes the request on the server, and parses the response</p>
<p>_process_sub_category_request() &ndash;
executes the request on the server, and parses the response</p>
<p>sub_categories()
&ndash;
returns the sub categories available for the given category</p>
<p>get_package_details()
&ndash;
returns the details of the package specified</p>
<p>add_category()
&ndash;
adds a new category to the download center</p>
<p>update_category()
&ndash;
updates the category details at the download center</p>
<p>delete_category()
&ndash;
deletes the given category from the download center</p>
<p>add_sub_category()
&ndash;
adds a new sub category to the specified category</p>
<p>update_sub_category()
&ndash;
updates the sub category details for the given catetory
at the download center</p>
<p>delete_sub_category()
&ndash;
deletes the specified sub category for the given category
from the download center</p>
<p>upload_package()
&ndash;
uploads the given package to download center</p>
<p>download_package()
&ndash;
downloads the given package from download center</p>
<p>delete_package()
&ndash;
deletes the given package from download center</p>
<p>refresh()
&ndash;
refresh the properties of the download center class instance</p>
<h2 id="attributes">Attributes</h2>
<p>Following attributes are available for an instance of the Download Center class:</p>
<pre><code>**product_versions**    --  returns list of product versions supported on Download Center

**servers_for_browse**  --  returns the list of servers available for browse on DC

**error_detail**        --  errors returned while getting the Download Center attributes

**users_and_groups**    --  returns the list of users and user groups available at DC

**categories**          --  returns the list of categories available at Download Center

**download_types**      --  returns the list of supported download types for packages

**vendors**             --  returns the list of vendors available at Download Center

**platforms**           --  returns the list of supported platforms for DC packages

**packages**            --  returns the list of packages available at Download Center
</code></pre>
<h1 id="todo-implement-update-method-for-updating-details-of-a-package">TODO: implement update method for updating details of a package</h1>
<h1 id="todo-add-a-ps-script-to-be-called-via-commcell-client-to-check-if-the-location-is-valid">TODO: add a PS script to be called via commcell client, to check if the location is valid,</h1>
<p>and get the size of the file</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/download_center.py#L1-L1388" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;File for doing operations on Download Center.

DownloadCenter is the class defined in this module for doing operations on the Download Center.

Following Operations can be performed on the Download Center:

    1.  Add a new category to Download Center

    2.  Add a new sub category to the specified category to Download Center

    3.  Upload a package to Download Center

    4.  Download a package from Download Center

    5.  Delete a sub category from the specified category on Download Center

    6.  Delete a category from Download Center

    7.  Delete a package from Download Center

    8.  Update the category information

    9.  Update the sub category information for the specified category


DownloadCenter:

    __init__(commcell_object)   --  initializes a connection to the download center

    __repr__()                  --  returns the string representation of an instance of this class

    _get_properties()           --  get the properties of the download center

    _get_packages()             --  get the packages available at download center

    _process_category_request() --  executes the request on the server, and parses the response

    _process_sub_category_request() --  executes the request on the server, and parses the response

    sub_categories()            --  returns the sub categories available for the given category

    get_package_details()       --  returns the details of the package specified

    add_category()              --  adds a new category to the download center

    update_category()           --  updates the category details at the download center

    delete_category()           --  deletes the given category from the download center

    add_sub_category()          --  adds a new sub category to the specified category

    update_sub_category()       --  updates the sub category details for the given catetory
    at the download center

    delete_sub_category()       --  deletes the specified sub category for the given category
    from the download center

    upload_package()            --  uploads the given package to download center

    download_package()          --  downloads the given package from download center

    delete_package()            --  deletes the given package from download center

    refresh()                   --  refresh the properties of the download center class instance


Attributes:

    Following attributes are available for an instance of the Download Center class:

        **product_versions**    --  returns list of product versions supported on Download Center

        **servers_for_browse**  --  returns the list of servers available for browse on DC

        **error_detail**        --  errors returned while getting the Download Center attributes

        **users_and_groups**    --  returns the list of users and user groups available at DC

        **categories**          --  returns the list of categories available at Download Center

        **download_types**      --  returns the list of supported download types for packages

        **vendors**             --  returns the list of vendors available at Download Center

        **platforms**           --  returns the list of supported platforms for DC packages

        **packages**            --  returns the list of packages available at Download Center


# TODO: implement update method for updating details of a package

# TODO: add a PS script to be called via commcell client, to check if the location is valid,
and get the size of the file


&#34;&#34;&#34;

from xml.parsers.expat import ExpatError

import os
import time
import xmltodict

from .exception import SDKException


class DownloadCenter(object):
    &#34;&#34;&#34;Class for doing operations on Download Center like upload or download product.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initializes an instance of the DownloadCenter class.

            Args:
                commcell_object     (object)    --  instance of the Commcell class

            Returns:
                object  -   instance of the DownloadCenter class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_

        self._response = None
        self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;Returns the string representation of an instance of this class.&#34;&#34;&#34;
        return &#34;DownloadCenter class instance for Commcell&#34;

    def _get_properties(self):
        &#34;&#34;&#34;Get the properties of the download center.&#34;&#34;&#34;

        request_xml = &#34;&#34;&#34;
        &lt;App_DCGetDataToCreatePackageReq getListOfUsers=&#34;1&#34;
            getListOfGroups=&#34;1&#34; getCategories=&#34;1&#34; getSubCategories=&#34;1&#34;
            getPlatforms=&#34;1&#34; getDownloadTypes=&#34;1&#34; getProductVersions=&#34;1&#34;
            getRecutNumbers=&#34;1&#34; getVendors=&#34;1&#34; getDownloadedPackageUsers=&#34;1&#34;
            packageId=&#34;1&#34; getServerTypes=&#34;1&#34;/&gt;
        &#34;&#34;&#34;

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;GET_DC_DATA&#39;], request_xml
        )

        if flag:
            try:
                self._response = response.json()
            except ExpatError:
                raise SDKException(&#39;DownloadCenter&#39;, &#39;101&#39;, response.text)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _get_packages(self):
        &#34;&#34;&#34;Gets the list of all the Active packages available at the download center.&#34;&#34;&#34;

        request_xml = &#34;&#34;&#34;
        &lt;DM2ContentIndexing_CVSearchReq mode=&#34;2&#34;&gt;
            &lt;searchProcessingInfo pageSize=&#34;1000000&#34;&gt;
                &lt;queryParams param=&#34;ENABLE_DOWNLOADCENTER&#34; value=&#34;true&#34;/&gt;
                &lt;queryParams param=&#34;GROUP_RESULTS_BY&#34; value=&#34;PKG_ID&#34;/&gt;
                &lt;queryParams param=&#34;GROUP_LIMIT&#34; value=&#34;50&#34;/&gt;
                &lt;queryParams param=&#34;GROUP_FACETS&#34; value=&#34;true&#34;/&gt;
                &lt;queryParams param=&#34;GROUP_FLAT_RESULTS&#34; value=&#34;false&#34;/&gt;
                &lt;queryParams param=&#34;SORTFIELD&#34; value=&#34;VALID_FROM&#34;/&gt;
            &lt;/searchProcessingInfo&gt;
            &lt;advSearchGrp /&gt;
            &lt;facetRequests&gt;
                &lt;facetRequest count=&#34;1&#34; name=&#34;PKG_STATUS&#34;&gt;
                    &lt;stringParameter selected=&#34;1&#34; name=&#34;0&#34;/&gt;
                &lt;/facetRequest&gt;
            &lt;/facetRequests&gt;
        &lt;/DM2ContentIndexing_CVSearchReq&gt;
        &#34;&#34;&#34;

        root = &#39;DM2ContentIndexing_CVDownloadCenterResp&#39;

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;SEARCH_PACKAGES&#39;], request_xml
        )

        if flag:
            try:
                packages = response.json()[&#39;searchResult&#39;][&#39;packages&#39;]

                if isinstance(packages, dict):
                    packages = [packages]

                for package in packages:
                    name = package[&#39;name&#39;].lower()

                    self._packages[name] = {
                        &#39;id&#39;: package[&#39;packageId&#39;],
                        &#39;description&#39;: package[&#39;description&#39;],
                        &#39;platforms&#39;: {}
                    }

                    platforms = package[&#39;platforms&#39;]

                    if isinstance(platforms, dict):
                        platforms = [platforms]

                    for platform in platforms:
                        platform_name = platform[&#39;name&#39;]
                        platform_id = platform[&#39;id&#39;]
                        download_type = platform[&#39;downloadType&#39;][&#39;name&#39;]

                        if platform_name not in self._packages[name][&#39;platforms&#39;]:
                            self._packages[name][&#39;platforms&#39;][platform_name] = {
                                &#39;id&#39;: platform_id,
                                &#39;download_type&#39;: [download_type]
                            }
                        else:
                            self._packages[name][&#39;platforms&#39;][platform_name][
                                &#39;download_type&#39;].append(download_type)
            except ExpatError:
                raise SDKException(&#39;DownloadCenter&#39;, &#39;101&#39;, response.text)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _process_category_request(self, operation, name, description=None, new_name=None):
        &#34;&#34;&#34;Executes the request on the server, and process the response received from the server.

            Args:
                operation       (str)   --  type of operation to be performed on the server

                    e.g:
                        add     -   to add a new category

                        delete  -   to delete an existing category

                        update  -   to update the details of an existing category


                name            (str)   --  name of the category to perform the operation on

                description     (str)   --  description for the category

                    default: None

                new_name        (str)   --  new name to be set for the category

                    in case of ``update`` operation

                    default: None


            Returns:
                None    -   if the operation was performed successfully

            Raises:
                SDKException:
                    if failed to process the request

                    if failed to parse the response

        &#34;&#34;&#34;
        operations = {
            &#39;service&#39;: &#39;DC_ENTITY&#39;,
            &#39;root&#39;: &#39;App_DCSaveLookupEntityResp&#39;,
            &#39;error&#39;: &#39;Failed to %s the category.\nError: &#34;{0}&#34;&#39; % (operation)
        }

        if operation == &#39;add&#39;:
            operation_type = &#39;1&#39;
            category_id = &#39;&#39;
        elif operation == &#39;delete&#39;:
            operation_type = &#39;2&#39;
            category_id = self._categories[name][&#39;id&#39;]
        elif operation == &#39;update&#39;:
            operation_type = &#39;3&#39;
            category_id = self._categories[name][&#39;id&#39;]
            name = new_name
        else:
            raise SDKException(&#39;DownloadCenter&#39;, &#39;102&#39;, &#39;Invalid Operation&#39;)

        if description is None:
            description = &#39;&#39;

        request_xml = &#34;&#34;&#34;
        &lt;App_DCSaveLookupEntityReq operation=&#34;{0}&#34;&gt;
            &lt;entitiesToSave entityType=&#34;0&#34; id=&#34;{1}&#34; name=&#34;{2}&#34; description=&#34;{3}&#34;/&gt;
        &lt;/App_DCSaveLookupEntityReq&gt;
        &#34;&#34;&#34;.format(operation_type, category_id, name, description)

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[operations[&#39;service&#39;]], request_xml
        )

        if flag:
            try:
                response = xmltodict.parse(response.text)[operations[&#39;root&#39;]]
                result = response[&#39;@result&#39;]

                try:
                    category_id = response[&#39;entitiesToSave&#39;][&#39;@id&#39;]
                    error_code = response[&#39;entitiesToSave&#39;][&#39;errorDetail&#39;][&#39;@errorCode&#39;]
                    error_message = response[&#39;entitiesToSave&#39;][&#39;errorDetail&#39;][&#39;@errorMessage&#39;]
                except KeyError:
                    # for delete category request,
                    # entitiesToSave key is not returned in the response
                    # so initialize these values to None
                    category_id = error_code = error_message = None

                if result == &#39;3&#39; and error_code != &#39;0&#39;:
                    error_message = operations[&#39;error&#39;].format(error_message)
                    raise SDKException(&#39;DownloadCenter&#39;, &#39;102&#39;, error_message)

                self.refresh()
            except ExpatError:
                raise SDKException(&#39;DownloadCenter&#39;, &#39;101&#39;, response.text)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _process_sub_category_request(
            self,
            operation,
            name,
            category,
            description=None,
            new_name=None):
        &#34;&#34;&#34;Executes the request on the server, and process the response received from the server.

            Args:
                operation       (str)   --  type of operation to be performed on the server

                    e.g:
                        add     -   to add a new sub category

                        delete  -   to delete an existing sub category from the specified category

                        update  -   to update the details of an existing sub category


                name            (str)   --  name of the sub category to perform the operation on

                category        (str)   --  category for the sub category

                description     (str)   --  description for the sub category

                    default: None

                new_name        (str)   --  new name to be set for the sub category

                    in case of ``update`` operation

                    default: None


            Returns:
                None    -   if the operation was performed successfully

            Raises:
                SDKException:
                    if failed to process the request

                    if failed to parse the response

        &#34;&#34;&#34;
        operations = {
            &#39;service&#39;: &#39;DC_SUB_CATEGORY&#39;,
            &#39;root&#39;: &#39;App_DCSaveSubCategoriesMsg&#39;,
            &#39;error&#39;: &#39;Failed to %s the sub category.\nError: &#34;{0}&#34;&#39; % (operation)
        }

        if operation == &#39;add&#39;:
            operation_type = &#39;1&#39;
            sub_category_id = &#39;&#39;
        elif operation == &#39;delete&#39;:
            operation_type = &#39;2&#39;
            sub_category_id = self._categories[category][&#39;sub_categories&#39;][name][&#39;id&#39;]
        elif operation == &#39;update&#39;:
            operation_type = &#39;3&#39;
            sub_category_id = self._categories[category][&#39;sub_categories&#39;][name][&#39;id&#39;]
            name = new_name
        else:
            raise SDKException(&#39;DownloadCenter&#39;, &#39;102&#39;, &#39;Invalid Operation&#39;)

        if description is None:
            description = &#39;&#39;

        request_xml = &#34;&#34;&#34;
        &lt;App_DCSaveSubCategoriesMsg operation=&#34;{0}&#34;&gt;
            &lt;subCategories id=&#34;{1}&#34; name=&#34;{2}&#34; description=&#34;{3}&#34; categoryId=&#34;{4}&#34;/&gt;
        &lt;/App_DCSaveSubCategoriesMsg&gt;
        &#34;&#34;&#34;.format(
            operation_type, sub_category_id, name, description, self._categories[category][&#39;id&#39;]
        )

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[operations[&#39;service&#39;]], request_xml
        )

        if flag:
            try:
                response = xmltodict.parse(response.text)[operations[&#39;root&#39;]]

                result = response[&#39;@result&#39;]
                sub_category_id = response[&#39;subCategories&#39;][&#39;@id&#39;]
                error_code = response[&#39;subCategories&#39;][&#39;errorDetail&#39;][&#39;@errorCode&#39;]
                error_message = response[&#39;subCategories&#39;][&#39;errorDetail&#39;][&#39;@errorMessage&#39;]

                if result == &#39;3&#39; and error_code != &#39;0&#39;:
                    error_message = operations[&#39;error&#39;].format(error_message)
                    raise SDKException(&#39;DownloadCenter&#39;, &#39;102&#39;, error_message)

                self.refresh()
            except ExpatError:
                raise SDKException(&#39;DownloadCenter&#39;, &#39;101&#39;, response.text)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def product_versions(self):
        &#34;&#34;&#34;Return the versions of product available at Download Center.&#34;&#34;&#34;
        if not self._product_versions:
            product_versions = self._response[&#39;productVersions&#39;]

            if isinstance(product_versions, dict):
                product_versions = [product_versions]

            for product_version in product_versions:
                self._product_versions[product_version[&#39;@name&#39;]] = {
                    &#39;id&#39;: product_version[&#39;@id&#39;],
                    &#39;entity_type&#39;: product_version[&#39;@entityType&#39;]
                }

        return list(self._product_versions.keys())

    @property
    def servers_for_browse(self):
        &#34;&#34;&#34;Returns the servers available for browse at Download Center.&#34;&#34;&#34;
        if not self._servers_for_browse:
            servers_for_browse = self._response[&#39;serversForBrowse&#39;]

            if isinstance(servers_for_browse, dict):
                servers_for_browse = [servers_for_browse]

            for server_for_browse in servers_for_browse:
                self._servers_for_browse[server_for_browse[&#39;@name&#39;]] = {
                    &#39;id&#39;: server_for_browse[&#39;@id&#39;],
                    &#39;internal_name&#39;: server_for_browse[&#39;@internalName&#39;],
                    &#39;entity_type&#39;: server_for_browse[&#39;@entityType&#39;],
                    &#39;attribute&#39;: server_for_browse[&#39;@attribute&#39;]
                }

        return list(self._servers_for_browse.keys())

    @property
    def error_detail(self):
        &#34;&#34;&#34;Returns the error details.&#34;&#34;&#34;
        return self._response[&#39;errorDetail&#39;]

    @property
    def users_and_groups(self):
        &#34;&#34;&#34;Returns the Users and User Groups available at Download Center.&#34;&#34;&#34;
        if not self._users_and_groups:
            users_and_groups = self._response[&#39;usersAndGroups&#39;]

            if isinstance(users_and_groups, dict):
                users_and_groups = [users_and_groups]

            for user_and_group in self._response[&#39;usersAndGroups&#39;]:
                self._users_and_groups[user_and_group[&#39;@name&#39;]] = {
                    &#39;id&#39;: user_and_group[&#39;@id&#39;],
                    &#39;provider_id&#39;: user_and_group[&#39;@providerId&#39;],
                    &#39;guid&#39;: user_and_group[&#39;@guid&#39;],
                    &#39;type&#39;: user_and_group[&#39;@type&#39;],
                    &#39;service_type&#39;: user_and_group[&#39;@serviceType&#39;]
                }

        return list(self._users_and_groups.keys())

    @property
    def categories(self):
        &#34;&#34;&#34;Returns the categories of products available at Download Center.&#34;&#34;&#34;
        if not self._categories:
            categories = self._response[&#39;categories&#39;]

            if isinstance(categories, dict):
                categories = [categories]

            for category in categories:
                category_id = category[&#39;@id&#39;]
                temp = {}

                sub_categories = self._response[&#39;subCategories&#39;]

                if isinstance(sub_categories, dict):
                    sub_categories = [sub_categories]

                for sub_category in sub_categories:
                    cat_id = sub_category[&#39;@categoryId&#39;]

                    if cat_id == category_id:
                        temp[sub_category[&#39;@name&#39;]] = {
                            &#39;id&#39;: sub_category[&#39;@id&#39;],
                            &#39;description&#39;: sub_category[&#39;@description&#39;],
                            &#39;entity_type&#39;: sub_category[&#39;@entityType&#39;],
                            &#39;attribute&#39;: sub_category[&#39;@attribute&#39;]
                        }

                self._categories[category[&#39;@name&#39;]] = {
                    &#39;id&#39;: category_id,
                    &#39;description&#39;: category[&#39;@description&#39;],
                    &#39;entity_type&#39;: category[&#39;@entityType&#39;],
                    &#39;attribute&#39;: category[&#39;@attribute&#39;],
                    &#39;sub_categories&#39;: temp
                }

        return list(self._categories)

    @property
    def download_types(self):
        &#34;&#34;&#34;Returns the types of packages available for download at Download Center.&#34;&#34;&#34;
        if not self._download_types:
            download_types = self._response[&#39;downloadTypes&#39;]

            if isinstance(download_types, dict):
                download_types = [download_types]

            for download_type in download_types:
                self._download_types[download_type[&#39;@name&#39;]] = {
                    &#39;id&#39;: download_type[&#39;@id&#39;],
                    &#39;entity_type&#39;: download_type[&#39;@entityType&#39;]
                }

        return list(self._download_types.keys())

    @property
    def vendors(self):
        &#34;&#34;&#34;Returns the vendors available at Download Center.&#34;&#34;&#34;
        if not self._vendors:
            vendors = self._response[&#39;vendors&#39;]

            if isinstance(vendors, dict):
                vendors = [vendors]

            for vendor in vendors:
                self._vendors[vendor[&#39;@name&#39;]] = {
                    &#39;id&#39;: vendor[&#39;@id&#39;],
                    &#39;entity_type&#39;: vendor[&#39;@entityType&#39;]
                }

        return list(self._vendors.keys())

    @property
    def platforms(self):
        &#34;&#34;&#34;Returns the platforms supported for packages at Download Center.&#34;&#34;&#34;
        if not self._platforms:
            platforms = self._response[&#39;platforms&#39;]

            if isinstance(platforms, dict):
                platforms = [platforms]

            for platform in platforms:
                self._platforms[platform[&#39;@name&#39;]] = {
                    &#39;id&#39;: platform[&#39;@id&#39;],
                    &#39;entity_type&#39;: platform[&#39;@entityType&#39;],
                    &#39;architecture&#39;: platform[&#39;@architectureName&#39;]
                }

        return list(self._platforms.keys())

    @property
    def packages(self):
        &#34;&#34;&#34;Returns the packages available for download at Download Center.&#34;&#34;&#34;
        if not self._packages:
            self._get_packages()

        return list(self._packages.keys())

    def has_package(self, package):
        &#34;&#34;&#34;Checks if a package with the given name already exists at Download Center or not.

            Args:
                package     (str)   --  name of the package to check

            Returns:
                bool    -   boolean specifying whether the package exists or not

        &#34;&#34;&#34;
        return self.packages and package.lower() in self.packages

    def sub_categories(self, category):
        &#34;&#34;&#34;Returns the sub categories available for the specified category.

            Args:
                category    (str)   --  name of the category to get the sub categories of

            Returns:
                list    -   list of sub categories available for the given category

            Raises:
                SDKException:
                    if category does not exist

        &#34;&#34;&#34;
        if category not in self.categories:
            raise SDKException(&#39;DownloadCenter&#39;, &#39;103&#39;)

        return list(self._categories[category][&#39;sub_categories&#39;].keys())

    def get_package_details(self, package):
        &#34;&#34;&#34;Returns the details of the package, like the package description, platforms, etc.

            Args:
                package     (str)   --  name of the package to get the details of

            Returns:
                dict    -   dictionary consisting of the details of the package

            Raises:
                SDKException:
                    if package does not exist

        &#34;&#34;&#34;
        if not self.has_package(package):
            raise SDKException(&#39;DownloadCenter&#39;, &#39;106&#39;)

        package = package.lower()
        package_detail = self._packages[package]

        output = {
            &#39;name&#39;: package,
            &#39;description&#39;: package_detail[&#39;description&#39;],
            &#39;platforms&#39;: {}
        }

        platforms = package_detail[&#39;platforms&#39;]

        for platform in platforms:
            output[&#39;platforms&#39;][platform] = platforms[platform][&#39;download_type&#39;]

        return output

    def add_category(self, name, description=None):
        &#34;&#34;&#34;Adds a new category with the given name, and description.

            Args:
                name            (str)   --  name of the category to add

                description     (str)   --  description for the category (optional)
                    default: None

            Returns:
                None    -   if the category was added successfully

            Raises:
                SDKException:
                    if category already exists

                    if failed to add the category

        &#34;&#34;&#34;
        if name in self.categories:
            raise SDKException(&#39;DownloadCenter&#39;, &#39;104&#39;)

        self._process_category_request(&#39;add&#39;, name, description)

    def update_category(self, name, new_name, description=None):
        &#34;&#34;&#34;Updates the name and description of the category with the given name.

            Args:
                name            (str)   --  name of the existing category to update

                new_name        (str)   --  new name for the category

                description     (str)   --  description for the category (optional)

                    default: None


            Returns:
                None    -   if the category information was updated successfully

            Raises:
                SDKException:
                    if no category exists with the given name

                    if category already exists with the new name specified

                    if failed to update the category

        &#34;&#34;&#34;
        if name not in self.categories:
            raise SDKException(&#39;DownloadCenter&#39;, &#39;108&#39;)

        if new_name in self.categories:
            raise SDKException(&#39;DownloadCenter&#39;, &#39;104&#39;)

        self._process_category_request(&#39;update&#39;, name, description, new_name)

    def delete_category(self, name):
        &#34;&#34;&#34;Deletes the category with the given name.

            Args:
                name            (str)   --  name of the category to delete

            Returns:
                None    -   if the category was deleted successfully

            Raises:
                SDKException:
                    if category does not exists

                    if failed to delete the category

        &#34;&#34;&#34;
        if name not in self.categories:
            raise SDKException(&#39;DownloadCenter&#39;, &#39;108&#39;)

        self._process_category_request(&#39;delete&#39;, name)

    def add_sub_category(self, name, category, description=None):
        &#34;&#34;&#34;Adds a new sub category with the given name, and description to the specified category.

            Args:
                name            (str)   --  name of the sub category to add

                category        (str)   --  name of the category to add the sub category to

                description     (str)   --  description for the sub category (optional)
                    default: None

            Returns:
                None    -   if the sub category was added successfully

            Raises:
                SDKException:
                    if category does not exist

                    if sub category already exists

                    if failed to add the sub category

        &#34;&#34;&#34;
        if name in self.sub_categories(category):
            raise SDKException(&#39;DownloadCenter&#39;, &#39;105&#39;)

        self._process_sub_category_request(&#39;add&#39;, name, category, description)

    def update_sub_category(self, name, category, new_name, description=None):
        &#34;&#34;&#34;Updates the name and description of the sub category with the given name and category.

            Args:
                name            (str)   --  name of the sub category to update the details of

                category        (str)   --  name of the category to update the sub category of

                new_name        (str)   --  new name for the sub category

                description     (str)   --  description for the sub category (optional)

                    default: None


            Returns:
                None    -   if the sub category information was updated successfully

            Raises:
                SDKException:
                    if no sub category exists with the given name

                    if sub category already exists with the new name specified

                    if failed to update the sub category

        &#34;&#34;&#34;
        if name not in self.sub_categories(category):
            raise SDKException(&#39;DownloadCenter&#39;, &#39;109&#39;)

        if new_name in self.sub_categories(category):
            raise SDKException(&#39;DownloadCenter&#39;, &#39;105&#39;)

        self._process_sub_category_request(&#39;update&#39;, name, category, description, new_name)

    def delete_sub_category(self, name, category):
        &#34;&#34;&#34;Deletes the sub category from the category with the given name.

            Args:
                name            (str)   --  name of the sub category to delete

                category        (str)   --  name of the category to delete the sub category from

            Returns:
                None    -   if the sub category was deleted successfully

            Raises:
                SDKException:
                    if sub category does not exist

                    if failed to delete the sub category

        &#34;&#34;&#34;
        if name not in self.sub_categories(category):
            raise SDKException(&#39;DownloadCenter&#39;, &#39;109&#39;)

        self._process_sub_category_request(&#39;delete&#39;, name, category)

    def upload_package(self, package, category, version, platform_download_locations, **kwargs):
        &#34;&#34;&#34;Uploads the given package to Download Center.

            Args:
                package                         (str)   --  name of the package to upload

                category                        (str)   --  category to upload the package for

                version                         (str)   --  product version for package to upload

                platform_download_locations     (list)  --  list consisting of dictionaries

                    where each dictionary contains the values for the platform, download type, and
                    location of the file

                    e.g.:
                        [
                            {
                                &#39;platform&#39;: &#39;Windows-x64&#39;,

                                &#39;download_type&#39;: &#39;Exe&#39;,

                                &#39;location&#39;: &#39;C:\\location1&#39;
                            }, {
                                &#39;platform&#39;: &#39;Windows-x64&#39;,

                                &#39;download_type&#39;: &#39;Script&#39;,

                                &#39;location&#39;: &#39;C:\\location2&#39;
                            }, {
                                &#39;platform&#39;: &#39;Windows-x86&#39;,

                                &#39;download_type&#39;: &#39;Exe&#39;,

                                &#39;location&#39;: &#39;C:\\location3&#39;
                            }, {
                                &#39;platform&#39;: &#39;Windows-x86&#39;,

                                &#39;download_type&#39;: &#39;Script&#39;,

                                &#39;location&#39;: &#39;C:\\location4&#39;
                            }
                        ]


                **kwargs:

                    valid_from          (str)   --  date from which the package should be valid

                        if the value is not specified, then current date is taken as it&#39;s value

                        format:     DD/MM/YYYY


                    description         (str)   --  description of the package

                    readme_location     (str)   --  location of the readme file

                        readme file should have one of the following extensions

                            [**.txt**, **.pdf**, **.doc**, **.docx**]


                    sub_category        (str)   --  sub category to associate the package with

                    vendor              (str)   --  vendor / distributor of the package

                    valid_to            (str)   --  date till which the package should be valid

                        format:     DD/MM/YYYY


                    repository          (str)   --  name of the repository to add the package to

                        if this value is not defined, the first repository will be taken by default


                    visible_to          (list)  --  list of users, the package should be visible to

                    not_visible_to      (list)  --  users, the package should not be visible to

                    early_preview_users (list)  --  list of users, the package should be
                    visible to before release


            Returns:
                None    -   if the package was uploaded successfully to Download Center


            Raises:
                SDKException:
                    if package with given name already exists

                    if category does not exists at Download Center

                    if version is not supported at Download Center

                    if platform is not supported at Download Center

                    if download type is not supported at Download Center

                    if sub category not present for the given category

                    if failed to upload the package

                    if error returned by the server

                    if response was not success

        &#34;&#34;&#34;
        if self.has_package(package):
            raise SDKException(&#39;DownloadCenter&#39;, &#39;114&#39;)

        if category not in self.categories:
            raise SDKException(
                &#39;DownloadCenter&#39;, &#39;103&#39;, &#39;Available categories: {0}&#39;.format(self.categories)
            )

        if version not in self.product_versions:
            raise SDKException(
                &#39;DownloadCenter&#39;, &#39;115&#39;, &#39;Available versions: {0}&#39;.format(self.product_versions)
            )

        platforms = []

        readme_location = kwargs.get(&#39;readme_location&#39;, &#39;&#39;)
        readme_file_extensions = [&#39;.txt&#39;, &#39;.pdf&#39;, &#39;.doc&#39;, &#39;.docx&#39;]

        if readme_location:
            if os.path.splitext(readme_location)[1] not in readme_file_extensions:
                raise SDKException(&#39;DownloadCenter&#39;, &#39;118&#39;)

        del readme_file_extensions

        for platform_dl_loc in platform_download_locations:
            platform = platform_dl_loc[&#39;platform&#39;]
            download_type = platform_dl_loc[&#39;download_type&#39;]
            location = platform_dl_loc[&#39;location&#39;]

            if platform not in self.platforms:
                raise SDKException(
                    &#39;DownloadCenter&#39;, &#39;116&#39;, &#39;Available platforms: {0}&#39;.format(self.platforms)
                )

            if download_type not in self.download_types:
                raise SDKException(
                    &#39;DownloadCenter&#39;,
                    &#39;117&#39;,
                    &#39;Available download types: {0}&#39;.format(self.download_types)
                )

            package_repository = kwargs.get(&#39;repository&#39;, self.servers_for_browse[0])
            package_repository_id = self._servers_for_browse[package_repository][&#39;id&#39;]
            package_repository_name = self._servers_for_browse[package_repository][&#39;internal_name&#39;]

            temp = {
                &#34;@name&#34;: platform,
                &#34;@readMeLocation&#34;: readme_location,
                &#34;@location&#34;: location,
                &#34;@id&#34;: self._platforms[platform][&#39;id&#39;],
                &#34;downloadType&#34;: {
                    &#34;@name&#34;: download_type,
                    &#34;@id&#34;: self._download_types[download_type][&#39;id&#39;]
                },
                &#39;pkgRepository&#39;: {
                    &#39;@repositoryId&#39;: package_repository_id,
                    &#39;@respositoryName&#39;: package_repository_name
                },
                &#39;@size&#39;: 186646528
            }

            platforms.append(temp)

        del platform
        del download_type
        del location
        del temp
        del platform_download_locations

        valid_from = kwargs.get(&#39;valid_from&#39;, time.strftime(&#39;%d/%m/%Y&#39;, time.localtime()))
        valid_from = int(time.mktime(time.strptime(valid_from, &#39;%d/%m/%Y&#39;)))

        valid_to = kwargs.get(&#39;valid_to&#39;, &#39;&#39;)
        if valid_to:
            valid_to = int(time.mktime(time.strptime(valid_from, &#39;%d/%m/%Y&#39;)))

        sub_category = {
            &#34;@entityType&#34;: 1,
            &#34;@categoryId&#34;: self._categories[category][&#39;id&#39;]
        }

        if &#39;sub_category&#39; in kwargs:
            sub_category_name = kwargs[&#39;sub_category&#39;]
            sub_categories = self.sub_categories(category)

            if sub_category_name in sub_categories:
                sub_category[&#34;@id&#34;] = self._categories[category][
                    &#39;sub_categories&#39;][sub_category_name][&#39;id&#39;]
            else:
                raise SDKException(
                    &#39;DownloadCenter&#39;, &#39;109&#39;, &#39;Available Sub Categories: {0}&#39;.format(sub_categories)
                )

            del sub_categories

        vendor = kwargs.get(&#39;vendor&#39;, &#39;&#39;)

        if vendor and vendor in self.vendors:
            vendor = self._vendors[vendor][&#39;id&#39;]

        visible_to = []
        not_visible_to = []
        early_preview_users = []

        for user in kwargs.get(&#39;visible_to&#39;, []):
            if user in self.users_and_groups:
                temp = {
                    &#39;@name&#39;: user,
                    &#39;@guid&#39;: self._users_and_groups[user][&#39;guid&#39;],
                    &#39;@type&#39;: self._users_and_groups[user][&#39;type&#39;]
                }
                visible_to.append(temp)

        for user in kwargs.get(&#39;not_visible_to&#39;, []):
            if user in self.users_and_groups:
                temp = {
                    &#39;@name&#39;: user,
                    &#39;@guid&#39;: self._users_and_groups[user][&#39;guid&#39;],
                    &#39;@type&#39;: self._users_and_groups[user][&#39;type&#39;]
                }
                not_visible_to.append(temp)

        for user in kwargs.get(&#39;early_preview_users&#39;, []):
            if user in self.users_and_groups:
                temp = {
                    &#39;@name&#39;: user,
                    &#39;@guid&#39;: self._users_and_groups[user][&#39;guid&#39;],
                    &#39;@type&#39;: self._users_and_groups[user][&#39;type&#39;]
                }
                early_preview_users.append(temp)

        request_json = {
            &#34;App_DCPackage&#34;: {
                &#34;@name&#34;: package,
                &#34;@description&#34;: kwargs.get(&#39;description&#39;, &#39;&#39;),
                &#34;@validFrom&#34;: valid_from,
                &#34;@validTo&#34;: valid_to,
                &#34;@rank&#34;: kwargs.get(&#39;rank&#39;, 0),
                &#34;category&#34;: {
                    &#34;@entityType&#34;: 0,
                    &#34;@id&#34;: self._categories[category][&#39;id&#39;]
                },
                &#34;subCategory&#34;: sub_category,
                &#34;platforms&#34;: platforms,
                &#34;productVersion&#34;: {
                    &#34;entityType&#34;: 3,
                    &#34;id&#34;: self._product_versions[version][&#39;id&#39;]
                },
                &#34;vendor&#34;: {
                    &#34;entityType&#34;: 6,
                    &#34;id&#34;: vendor
                },
                &#34;recutNumber&#34;: {
                    &#34;entityType&#34;: 4
                },
                &#34;visibleTo&#34;: visible_to,
                &#34;notVisibleTo&#34;: not_visible_to,
                &#34;earlyPreviewUsers&#34;: early_preview_users,
            }
        }

        xml = xmltodict.unparse(request_json)
        xml = xml[xml.find(&#39;&lt;App_&#39;):]

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;UPLOAD_PACKAGE&#39;], xml
        )

        self.refresh()

        if flag:
            response = xmltodict.parse(response.text)[&#39;App_DCPackage&#39;]
            error_code = response[&#39;errorDetail&#39;][&#39;@errorCode&#39;]

            if error_code != &#39;0&#39;:
                error_message = response[&#39;errorDetail&#39;][&#39;@errorMessage&#39;]

                raise SDKException(&#39;DownloadCenter&#39;, &#39;119&#39;, &#39;Error: {0}&#39;.format(error_message))
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def download_package(self, package, download_location, platform=None, download_type=None):
        &#34;&#34;&#34;Downloads the given package from Download Center to the path specified.

            Args:
                package             (str)   --  name of the pacakge to be downloaded

                download_location   (str)   --  path on local machine to download the package at

                platform            (str)   --  platform to download the package for

                    to be provided only if the package is added for multiple platforms

                    default: None

                download_type       (str)   --  type of package to be downloaded

                    to be provided only if multiple download types are present for single platform

                    default: None

            Returns:
                str     -   path on local machine where the file has been downloaded

            Raises:
                SDKException:
                    if package does not exist

                    if platform not given:
                        in case of multiple platforms

                    if platform given does not exists in the list of supported platforms

                    if download type is not specified:
                        if case of multiple download types for the selected platform

                    if download type given does not exists in the list of download types available

                    if error returned by the server

                    if response was not success

        &#34;&#34;&#34;

        # get the id of the package, if it is a valid package
        if not self.has_package(package):
            raise SDKException(&#39;DownloadCenter&#39;, &#39;106&#39;)
        else:
            package = package.lower()
            package_id = self._packages[package][&#39;id&#39;]

        def get_platform_id(package, platform):
            &#34;&#34;&#34;Checks if the platform is valid or not, and returns the platform id.

                If platform is set to None, gets the platform from the list of platforms,
                and the platform id.

                Args:
                    package     (str)   --  name of the package to check the platform for

                    platform    (str)   --  name of the platform to get the id of

                Returns:
                    (str, str)  -   tuple consisting of the platform name as the first,
                                        and platform id as the second value

                Raises:
                    SDKException:
                        if platform is not given, and multiple platforms exists for the package

                        if platform specified is not supported for the package

            &#34;&#34;&#34;
            platforms = self._packages[package][&#39;platforms&#39;]
            # check if the package has a single platform only, in case platform is not given
            if platform is None:

                # raise exception if multiple platforms exist for the package
                if len(platforms.keys()) &gt; 1:
                    raise SDKException(&#39;DownloadCenter&#39;, &#39;110&#39;)

                # get the platform name and id, if it&#39;s a single platform
                else:
                    platform = list(platforms.keys())[0]
                    platform_id = platforms[platform][&#39;id&#39;]

            # raise exception if the platform does not exists in the list of supported platforms,
            # when it is given
            elif platform not in platforms:
                raise SDKException(&#39;DownloadCenter&#39;, &#39;112&#39;)

            # get the id of the platform,
            # when it&#39;s given and exists in the list of supported platforms
            else:
                platform_id = platforms[platform][&#39;id&#39;]

            return platform, platform_id

        def get_download_type(package, platform, download_type):
            &#34;&#34;&#34;Checks if the download type for the given package and platform is valid or not.

                If download type is set to None, gets the download type from the list of download
                types availalble for the given package and platform.

                Args:
                    package         (str)   --  name of the package to get the download type for

                    platform        (str)   --  name of the platform to get the download type of

                    download_type   (str)   --  download type to be validated

                Returns:
                    str     -   name of the download type

                Raises:
                    SDKException:
                        if download type is not given, and multiple download types exists

                        if download type specified is not available for the package

            &#34;&#34;&#34;
            download_types = self._packages[package][&#39;platforms&#39;][platform][&#39;download_type&#39;]
            # check if the package has a single download type only,
            # in case download type is not given
            if download_type is None:

                # raise exception if multiple download types exist for the package and the platform
                if len(download_types) &gt; 1:
                    raise SDKException(&#39;DownloadCenter&#39;, &#39;111&#39;)

                # get the download type name, if it&#39;s a single download type for the given platform
                else:
                    download_type = download_types[0]

            # raise exception if the download type does not exists in the list, when it is given
            elif download_type not in download_types:
                raise SDKException(&#39;DownloadCenter&#39;, &#39;113&#39;)

            # use the download type given by the user
            else:
                pass

            return download_type

        platform, platform_id = get_platform_id(package, platform)
        download_type = get_download_type(package, platform, download_type)

        if not os.path.exists(download_location):
            try:
                os.makedirs(download_location)
            except FileExistsError:
                pass

        request_id = &#39;&#39;
        file_name = None

        # ID: 3 is static for Download Center, and has the value &#34;Package&#34;
        # ID: 2, needs to provide the package id as the value for &#34;name&#34;
        # ID: 9, needs to provide the platform id as the value for &#34;name&#34;
        # ID: 11, needs to provide the download tyoe as the value for &#34;name&#34;
        # ID: 10 is static for Streamed downloads
        request_xml = &#39;&#39;&#39;
        &lt;DM2ContentIndexing_OpenFileReq requestId=&#34;{3}&#34;&gt;
            &lt;fileParams id=&#34;3&#34; name=&#34;Package&#34;/&gt;
            &lt;fileParams id=&#34;2&#34; name=&#34;{0}&#34;/&gt;
            &lt;fileParams id=&#34;9&#34; name=&#34;{1}&#34;/&gt;
            &lt;fileParams id=&#34;11&#34; name=&#34;{2}&#34;/&gt;
            &lt;fileParams id=&#34;10&#34; name=&#34;Streamed&#34;/&gt;
        &lt;/DM2ContentIndexing_OpenFileReq&gt;
        &#39;&#39;&#39;

        # execute the request to get the details like file name, and request id
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;DOWNLOAD_PACKAGE&#39;], request_xml.format(
                package_id, platform_id, download_type, request_id
            )
        )

        if flag:
            error_list = response.json()[&#39;errList&#39;]
            file_content = response.json()[&#39;fileContent&#39;]

            if error_list:
                raise SDKException(&#39;DownloadCenter&#39;, &#39;107&#39;, &#39;Error: {0}&#39;.format(error_list))

            file_name = file_content.get(&#39;fileName&#39;, file_name)
            request_id = file_content[&#39;requestId&#39;]

            # full path of the file on local machine to be downloaded
            download_path = os.path.join(download_location, file_name)

            # execute request to get the stream of content
            # using request id returned in the previous response
            flag1, response1 = self._cvpysdk_object.make_request(
                &#39;POST&#39;,
                self._services[&#39;DOWNLOAD_VIA_STREAM&#39;],
                request_xml.format(package_id, platform_id, download_type, request_id),
                stream=True
            )

            # download chunks of 1MB each
            chunk_size = 1024 ** 2

            if flag1:
                with open(download_path, &#34;wb&#34;) as file_pointer:
                    for content in response1.iter_content(chunk_size=chunk_size):
                        file_pointer.write(content)
            else:
                response_string = self._update_response_(response1.text)
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        return download_path

    def delete_package(self, package):
        &#34;&#34;&#34;Deletes the package from Download Center.

            Args:
                package     (str)   --  name of the package to be deleted

            Returns:
                None    -   if the package was deleted successfully

            Raises:
                SDKException:
                    if no package exists with the given name

                    if failed to delete the package

                    if response is not success

        &#34;&#34;&#34;
        if not self.has_package(package):
            raise SDKException(&#39;DownloadCenter&#39;, &#39;106&#39;)
        else:
            package = package.lower()
            package_id = self._packages[package][&#39;id&#39;]

        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._services[&#39;DELETE_PACKAGE&#39;] % package_id
        )

        self.refresh()

        if flag:
            response = xmltodict.parse(response.text)[&#39;DM2ContentIndexing_CVDownloadCenterResp&#39;]

            if &#39;errList&#39; in response:
                error_code = response[&#39;errList&#39;][&#39;@errorCode&#39;]

                if error_code != &#39;0&#39;:
                    error_message = response[&#39;errList&#39;][&#39;@errLogMessage&#39;]

                    raise SDKException(&#39;DownloadCenter&#39;, &#39;119&#39;, &#39;Error: {0}&#39;.format(error_message))
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the DownloadCenter.&#34;&#34;&#34;
        self._get_properties()

        self._product_versions = {}
        self._servers_for_browse = {}
        self._users_and_groups = {}
        self._categories = {}
        self._download_types = {}
        self._vendors = {}
        self._platforms = {}
        self._packages = {}

        self._get_packages()</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.download_center.DownloadCenter"><code class="flex name class">
<span>class <span class="ident">DownloadCenter</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for doing operations on Download Center like upload or download product.</p>
<p>Initializes an instance of the DownloadCenter class.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the Commcell class</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the DownloadCenter class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/download_center.py#L125-L1388" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class DownloadCenter(object):
    &#34;&#34;&#34;Class for doing operations on Download Center like upload or download product.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initializes an instance of the DownloadCenter class.

            Args:
                commcell_object     (object)    --  instance of the Commcell class

            Returns:
                object  -   instance of the DownloadCenter class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_

        self._response = None
        self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;Returns the string representation of an instance of this class.&#34;&#34;&#34;
        return &#34;DownloadCenter class instance for Commcell&#34;

    def _get_properties(self):
        &#34;&#34;&#34;Get the properties of the download center.&#34;&#34;&#34;

        request_xml = &#34;&#34;&#34;
        &lt;App_DCGetDataToCreatePackageReq getListOfUsers=&#34;1&#34;
            getListOfGroups=&#34;1&#34; getCategories=&#34;1&#34; getSubCategories=&#34;1&#34;
            getPlatforms=&#34;1&#34; getDownloadTypes=&#34;1&#34; getProductVersions=&#34;1&#34;
            getRecutNumbers=&#34;1&#34; getVendors=&#34;1&#34; getDownloadedPackageUsers=&#34;1&#34;
            packageId=&#34;1&#34; getServerTypes=&#34;1&#34;/&gt;
        &#34;&#34;&#34;

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;GET_DC_DATA&#39;], request_xml
        )

        if flag:
            try:
                self._response = response.json()
            except ExpatError:
                raise SDKException(&#39;DownloadCenter&#39;, &#39;101&#39;, response.text)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _get_packages(self):
        &#34;&#34;&#34;Gets the list of all the Active packages available at the download center.&#34;&#34;&#34;

        request_xml = &#34;&#34;&#34;
        &lt;DM2ContentIndexing_CVSearchReq mode=&#34;2&#34;&gt;
            &lt;searchProcessingInfo pageSize=&#34;1000000&#34;&gt;
                &lt;queryParams param=&#34;ENABLE_DOWNLOADCENTER&#34; value=&#34;true&#34;/&gt;
                &lt;queryParams param=&#34;GROUP_RESULTS_BY&#34; value=&#34;PKG_ID&#34;/&gt;
                &lt;queryParams param=&#34;GROUP_LIMIT&#34; value=&#34;50&#34;/&gt;
                &lt;queryParams param=&#34;GROUP_FACETS&#34; value=&#34;true&#34;/&gt;
                &lt;queryParams param=&#34;GROUP_FLAT_RESULTS&#34; value=&#34;false&#34;/&gt;
                &lt;queryParams param=&#34;SORTFIELD&#34; value=&#34;VALID_FROM&#34;/&gt;
            &lt;/searchProcessingInfo&gt;
            &lt;advSearchGrp /&gt;
            &lt;facetRequests&gt;
                &lt;facetRequest count=&#34;1&#34; name=&#34;PKG_STATUS&#34;&gt;
                    &lt;stringParameter selected=&#34;1&#34; name=&#34;0&#34;/&gt;
                &lt;/facetRequest&gt;
            &lt;/facetRequests&gt;
        &lt;/DM2ContentIndexing_CVSearchReq&gt;
        &#34;&#34;&#34;

        root = &#39;DM2ContentIndexing_CVDownloadCenterResp&#39;

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;SEARCH_PACKAGES&#39;], request_xml
        )

        if flag:
            try:
                packages = response.json()[&#39;searchResult&#39;][&#39;packages&#39;]

                if isinstance(packages, dict):
                    packages = [packages]

                for package in packages:
                    name = package[&#39;name&#39;].lower()

                    self._packages[name] = {
                        &#39;id&#39;: package[&#39;packageId&#39;],
                        &#39;description&#39;: package[&#39;description&#39;],
                        &#39;platforms&#39;: {}
                    }

                    platforms = package[&#39;platforms&#39;]

                    if isinstance(platforms, dict):
                        platforms = [platforms]

                    for platform in platforms:
                        platform_name = platform[&#39;name&#39;]
                        platform_id = platform[&#39;id&#39;]
                        download_type = platform[&#39;downloadType&#39;][&#39;name&#39;]

                        if platform_name not in self._packages[name][&#39;platforms&#39;]:
                            self._packages[name][&#39;platforms&#39;][platform_name] = {
                                &#39;id&#39;: platform_id,
                                &#39;download_type&#39;: [download_type]
                            }
                        else:
                            self._packages[name][&#39;platforms&#39;][platform_name][
                                &#39;download_type&#39;].append(download_type)
            except ExpatError:
                raise SDKException(&#39;DownloadCenter&#39;, &#39;101&#39;, response.text)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _process_category_request(self, operation, name, description=None, new_name=None):
        &#34;&#34;&#34;Executes the request on the server, and process the response received from the server.

            Args:
                operation       (str)   --  type of operation to be performed on the server

                    e.g:
                        add     -   to add a new category

                        delete  -   to delete an existing category

                        update  -   to update the details of an existing category


                name            (str)   --  name of the category to perform the operation on

                description     (str)   --  description for the category

                    default: None

                new_name        (str)   --  new name to be set for the category

                    in case of ``update`` operation

                    default: None


            Returns:
                None    -   if the operation was performed successfully

            Raises:
                SDKException:
                    if failed to process the request

                    if failed to parse the response

        &#34;&#34;&#34;
        operations = {
            &#39;service&#39;: &#39;DC_ENTITY&#39;,
            &#39;root&#39;: &#39;App_DCSaveLookupEntityResp&#39;,
            &#39;error&#39;: &#39;Failed to %s the category.\nError: &#34;{0}&#34;&#39; % (operation)
        }

        if operation == &#39;add&#39;:
            operation_type = &#39;1&#39;
            category_id = &#39;&#39;
        elif operation == &#39;delete&#39;:
            operation_type = &#39;2&#39;
            category_id = self._categories[name][&#39;id&#39;]
        elif operation == &#39;update&#39;:
            operation_type = &#39;3&#39;
            category_id = self._categories[name][&#39;id&#39;]
            name = new_name
        else:
            raise SDKException(&#39;DownloadCenter&#39;, &#39;102&#39;, &#39;Invalid Operation&#39;)

        if description is None:
            description = &#39;&#39;

        request_xml = &#34;&#34;&#34;
        &lt;App_DCSaveLookupEntityReq operation=&#34;{0}&#34;&gt;
            &lt;entitiesToSave entityType=&#34;0&#34; id=&#34;{1}&#34; name=&#34;{2}&#34; description=&#34;{3}&#34;/&gt;
        &lt;/App_DCSaveLookupEntityReq&gt;
        &#34;&#34;&#34;.format(operation_type, category_id, name, description)

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[operations[&#39;service&#39;]], request_xml
        )

        if flag:
            try:
                response = xmltodict.parse(response.text)[operations[&#39;root&#39;]]
                result = response[&#39;@result&#39;]

                try:
                    category_id = response[&#39;entitiesToSave&#39;][&#39;@id&#39;]
                    error_code = response[&#39;entitiesToSave&#39;][&#39;errorDetail&#39;][&#39;@errorCode&#39;]
                    error_message = response[&#39;entitiesToSave&#39;][&#39;errorDetail&#39;][&#39;@errorMessage&#39;]
                except KeyError:
                    # for delete category request,
                    # entitiesToSave key is not returned in the response
                    # so initialize these values to None
                    category_id = error_code = error_message = None

                if result == &#39;3&#39; and error_code != &#39;0&#39;:
                    error_message = operations[&#39;error&#39;].format(error_message)
                    raise SDKException(&#39;DownloadCenter&#39;, &#39;102&#39;, error_message)

                self.refresh()
            except ExpatError:
                raise SDKException(&#39;DownloadCenter&#39;, &#39;101&#39;, response.text)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _process_sub_category_request(
            self,
            operation,
            name,
            category,
            description=None,
            new_name=None):
        &#34;&#34;&#34;Executes the request on the server, and process the response received from the server.

            Args:
                operation       (str)   --  type of operation to be performed on the server

                    e.g:
                        add     -   to add a new sub category

                        delete  -   to delete an existing sub category from the specified category

                        update  -   to update the details of an existing sub category


                name            (str)   --  name of the sub category to perform the operation on

                category        (str)   --  category for the sub category

                description     (str)   --  description for the sub category

                    default: None

                new_name        (str)   --  new name to be set for the sub category

                    in case of ``update`` operation

                    default: None


            Returns:
                None    -   if the operation was performed successfully

            Raises:
                SDKException:
                    if failed to process the request

                    if failed to parse the response

        &#34;&#34;&#34;
        operations = {
            &#39;service&#39;: &#39;DC_SUB_CATEGORY&#39;,
            &#39;root&#39;: &#39;App_DCSaveSubCategoriesMsg&#39;,
            &#39;error&#39;: &#39;Failed to %s the sub category.\nError: &#34;{0}&#34;&#39; % (operation)
        }

        if operation == &#39;add&#39;:
            operation_type = &#39;1&#39;
            sub_category_id = &#39;&#39;
        elif operation == &#39;delete&#39;:
            operation_type = &#39;2&#39;
            sub_category_id = self._categories[category][&#39;sub_categories&#39;][name][&#39;id&#39;]
        elif operation == &#39;update&#39;:
            operation_type = &#39;3&#39;
            sub_category_id = self._categories[category][&#39;sub_categories&#39;][name][&#39;id&#39;]
            name = new_name
        else:
            raise SDKException(&#39;DownloadCenter&#39;, &#39;102&#39;, &#39;Invalid Operation&#39;)

        if description is None:
            description = &#39;&#39;

        request_xml = &#34;&#34;&#34;
        &lt;App_DCSaveSubCategoriesMsg operation=&#34;{0}&#34;&gt;
            &lt;subCategories id=&#34;{1}&#34; name=&#34;{2}&#34; description=&#34;{3}&#34; categoryId=&#34;{4}&#34;/&gt;
        &lt;/App_DCSaveSubCategoriesMsg&gt;
        &#34;&#34;&#34;.format(
            operation_type, sub_category_id, name, description, self._categories[category][&#39;id&#39;]
        )

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[operations[&#39;service&#39;]], request_xml
        )

        if flag:
            try:
                response = xmltodict.parse(response.text)[operations[&#39;root&#39;]]

                result = response[&#39;@result&#39;]
                sub_category_id = response[&#39;subCategories&#39;][&#39;@id&#39;]
                error_code = response[&#39;subCategories&#39;][&#39;errorDetail&#39;][&#39;@errorCode&#39;]
                error_message = response[&#39;subCategories&#39;][&#39;errorDetail&#39;][&#39;@errorMessage&#39;]

                if result == &#39;3&#39; and error_code != &#39;0&#39;:
                    error_message = operations[&#39;error&#39;].format(error_message)
                    raise SDKException(&#39;DownloadCenter&#39;, &#39;102&#39;, error_message)

                self.refresh()
            except ExpatError:
                raise SDKException(&#39;DownloadCenter&#39;, &#39;101&#39;, response.text)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def product_versions(self):
        &#34;&#34;&#34;Return the versions of product available at Download Center.&#34;&#34;&#34;
        if not self._product_versions:
            product_versions = self._response[&#39;productVersions&#39;]

            if isinstance(product_versions, dict):
                product_versions = [product_versions]

            for product_version in product_versions:
                self._product_versions[product_version[&#39;@name&#39;]] = {
                    &#39;id&#39;: product_version[&#39;@id&#39;],
                    &#39;entity_type&#39;: product_version[&#39;@entityType&#39;]
                }

        return list(self._product_versions.keys())

    @property
    def servers_for_browse(self):
        &#34;&#34;&#34;Returns the servers available for browse at Download Center.&#34;&#34;&#34;
        if not self._servers_for_browse:
            servers_for_browse = self._response[&#39;serversForBrowse&#39;]

            if isinstance(servers_for_browse, dict):
                servers_for_browse = [servers_for_browse]

            for server_for_browse in servers_for_browse:
                self._servers_for_browse[server_for_browse[&#39;@name&#39;]] = {
                    &#39;id&#39;: server_for_browse[&#39;@id&#39;],
                    &#39;internal_name&#39;: server_for_browse[&#39;@internalName&#39;],
                    &#39;entity_type&#39;: server_for_browse[&#39;@entityType&#39;],
                    &#39;attribute&#39;: server_for_browse[&#39;@attribute&#39;]
                }

        return list(self._servers_for_browse.keys())

    @property
    def error_detail(self):
        &#34;&#34;&#34;Returns the error details.&#34;&#34;&#34;
        return self._response[&#39;errorDetail&#39;]

    @property
    def users_and_groups(self):
        &#34;&#34;&#34;Returns the Users and User Groups available at Download Center.&#34;&#34;&#34;
        if not self._users_and_groups:
            users_and_groups = self._response[&#39;usersAndGroups&#39;]

            if isinstance(users_and_groups, dict):
                users_and_groups = [users_and_groups]

            for user_and_group in self._response[&#39;usersAndGroups&#39;]:
                self._users_and_groups[user_and_group[&#39;@name&#39;]] = {
                    &#39;id&#39;: user_and_group[&#39;@id&#39;],
                    &#39;provider_id&#39;: user_and_group[&#39;@providerId&#39;],
                    &#39;guid&#39;: user_and_group[&#39;@guid&#39;],
                    &#39;type&#39;: user_and_group[&#39;@type&#39;],
                    &#39;service_type&#39;: user_and_group[&#39;@serviceType&#39;]
                }

        return list(self._users_and_groups.keys())

    @property
    def categories(self):
        &#34;&#34;&#34;Returns the categories of products available at Download Center.&#34;&#34;&#34;
        if not self._categories:
            categories = self._response[&#39;categories&#39;]

            if isinstance(categories, dict):
                categories = [categories]

            for category in categories:
                category_id = category[&#39;@id&#39;]
                temp = {}

                sub_categories = self._response[&#39;subCategories&#39;]

                if isinstance(sub_categories, dict):
                    sub_categories = [sub_categories]

                for sub_category in sub_categories:
                    cat_id = sub_category[&#39;@categoryId&#39;]

                    if cat_id == category_id:
                        temp[sub_category[&#39;@name&#39;]] = {
                            &#39;id&#39;: sub_category[&#39;@id&#39;],
                            &#39;description&#39;: sub_category[&#39;@description&#39;],
                            &#39;entity_type&#39;: sub_category[&#39;@entityType&#39;],
                            &#39;attribute&#39;: sub_category[&#39;@attribute&#39;]
                        }

                self._categories[category[&#39;@name&#39;]] = {
                    &#39;id&#39;: category_id,
                    &#39;description&#39;: category[&#39;@description&#39;],
                    &#39;entity_type&#39;: category[&#39;@entityType&#39;],
                    &#39;attribute&#39;: category[&#39;@attribute&#39;],
                    &#39;sub_categories&#39;: temp
                }

        return list(self._categories)

    @property
    def download_types(self):
        &#34;&#34;&#34;Returns the types of packages available for download at Download Center.&#34;&#34;&#34;
        if not self._download_types:
            download_types = self._response[&#39;downloadTypes&#39;]

            if isinstance(download_types, dict):
                download_types = [download_types]

            for download_type in download_types:
                self._download_types[download_type[&#39;@name&#39;]] = {
                    &#39;id&#39;: download_type[&#39;@id&#39;],
                    &#39;entity_type&#39;: download_type[&#39;@entityType&#39;]
                }

        return list(self._download_types.keys())

    @property
    def vendors(self):
        &#34;&#34;&#34;Returns the vendors available at Download Center.&#34;&#34;&#34;
        if not self._vendors:
            vendors = self._response[&#39;vendors&#39;]

            if isinstance(vendors, dict):
                vendors = [vendors]

            for vendor in vendors:
                self._vendors[vendor[&#39;@name&#39;]] = {
                    &#39;id&#39;: vendor[&#39;@id&#39;],
                    &#39;entity_type&#39;: vendor[&#39;@entityType&#39;]
                }

        return list(self._vendors.keys())

    @property
    def platforms(self):
        &#34;&#34;&#34;Returns the platforms supported for packages at Download Center.&#34;&#34;&#34;
        if not self._platforms:
            platforms = self._response[&#39;platforms&#39;]

            if isinstance(platforms, dict):
                platforms = [platforms]

            for platform in platforms:
                self._platforms[platform[&#39;@name&#39;]] = {
                    &#39;id&#39;: platform[&#39;@id&#39;],
                    &#39;entity_type&#39;: platform[&#39;@entityType&#39;],
                    &#39;architecture&#39;: platform[&#39;@architectureName&#39;]
                }

        return list(self._platforms.keys())

    @property
    def packages(self):
        &#34;&#34;&#34;Returns the packages available for download at Download Center.&#34;&#34;&#34;
        if not self._packages:
            self._get_packages()

        return list(self._packages.keys())

    def has_package(self, package):
        &#34;&#34;&#34;Checks if a package with the given name already exists at Download Center or not.

            Args:
                package     (str)   --  name of the package to check

            Returns:
                bool    -   boolean specifying whether the package exists or not

        &#34;&#34;&#34;
        return self.packages and package.lower() in self.packages

    def sub_categories(self, category):
        &#34;&#34;&#34;Returns the sub categories available for the specified category.

            Args:
                category    (str)   --  name of the category to get the sub categories of

            Returns:
                list    -   list of sub categories available for the given category

            Raises:
                SDKException:
                    if category does not exist

        &#34;&#34;&#34;
        if category not in self.categories:
            raise SDKException(&#39;DownloadCenter&#39;, &#39;103&#39;)

        return list(self._categories[category][&#39;sub_categories&#39;].keys())

    def get_package_details(self, package):
        &#34;&#34;&#34;Returns the details of the package, like the package description, platforms, etc.

            Args:
                package     (str)   --  name of the package to get the details of

            Returns:
                dict    -   dictionary consisting of the details of the package

            Raises:
                SDKException:
                    if package does not exist

        &#34;&#34;&#34;
        if not self.has_package(package):
            raise SDKException(&#39;DownloadCenter&#39;, &#39;106&#39;)

        package = package.lower()
        package_detail = self._packages[package]

        output = {
            &#39;name&#39;: package,
            &#39;description&#39;: package_detail[&#39;description&#39;],
            &#39;platforms&#39;: {}
        }

        platforms = package_detail[&#39;platforms&#39;]

        for platform in platforms:
            output[&#39;platforms&#39;][platform] = platforms[platform][&#39;download_type&#39;]

        return output

    def add_category(self, name, description=None):
        &#34;&#34;&#34;Adds a new category with the given name, and description.

            Args:
                name            (str)   --  name of the category to add

                description     (str)   --  description for the category (optional)
                    default: None

            Returns:
                None    -   if the category was added successfully

            Raises:
                SDKException:
                    if category already exists

                    if failed to add the category

        &#34;&#34;&#34;
        if name in self.categories:
            raise SDKException(&#39;DownloadCenter&#39;, &#39;104&#39;)

        self._process_category_request(&#39;add&#39;, name, description)

    def update_category(self, name, new_name, description=None):
        &#34;&#34;&#34;Updates the name and description of the category with the given name.

            Args:
                name            (str)   --  name of the existing category to update

                new_name        (str)   --  new name for the category

                description     (str)   --  description for the category (optional)

                    default: None


            Returns:
                None    -   if the category information was updated successfully

            Raises:
                SDKException:
                    if no category exists with the given name

                    if category already exists with the new name specified

                    if failed to update the category

        &#34;&#34;&#34;
        if name not in self.categories:
            raise SDKException(&#39;DownloadCenter&#39;, &#39;108&#39;)

        if new_name in self.categories:
            raise SDKException(&#39;DownloadCenter&#39;, &#39;104&#39;)

        self._process_category_request(&#39;update&#39;, name, description, new_name)

    def delete_category(self, name):
        &#34;&#34;&#34;Deletes the category with the given name.

            Args:
                name            (str)   --  name of the category to delete

            Returns:
                None    -   if the category was deleted successfully

            Raises:
                SDKException:
                    if category does not exists

                    if failed to delete the category

        &#34;&#34;&#34;
        if name not in self.categories:
            raise SDKException(&#39;DownloadCenter&#39;, &#39;108&#39;)

        self._process_category_request(&#39;delete&#39;, name)

    def add_sub_category(self, name, category, description=None):
        &#34;&#34;&#34;Adds a new sub category with the given name, and description to the specified category.

            Args:
                name            (str)   --  name of the sub category to add

                category        (str)   --  name of the category to add the sub category to

                description     (str)   --  description for the sub category (optional)
                    default: None

            Returns:
                None    -   if the sub category was added successfully

            Raises:
                SDKException:
                    if category does not exist

                    if sub category already exists

                    if failed to add the sub category

        &#34;&#34;&#34;
        if name in self.sub_categories(category):
            raise SDKException(&#39;DownloadCenter&#39;, &#39;105&#39;)

        self._process_sub_category_request(&#39;add&#39;, name, category, description)

    def update_sub_category(self, name, category, new_name, description=None):
        &#34;&#34;&#34;Updates the name and description of the sub category with the given name and category.

            Args:
                name            (str)   --  name of the sub category to update the details of

                category        (str)   --  name of the category to update the sub category of

                new_name        (str)   --  new name for the sub category

                description     (str)   --  description for the sub category (optional)

                    default: None


            Returns:
                None    -   if the sub category information was updated successfully

            Raises:
                SDKException:
                    if no sub category exists with the given name

                    if sub category already exists with the new name specified

                    if failed to update the sub category

        &#34;&#34;&#34;
        if name not in self.sub_categories(category):
            raise SDKException(&#39;DownloadCenter&#39;, &#39;109&#39;)

        if new_name in self.sub_categories(category):
            raise SDKException(&#39;DownloadCenter&#39;, &#39;105&#39;)

        self._process_sub_category_request(&#39;update&#39;, name, category, description, new_name)

    def delete_sub_category(self, name, category):
        &#34;&#34;&#34;Deletes the sub category from the category with the given name.

            Args:
                name            (str)   --  name of the sub category to delete

                category        (str)   --  name of the category to delete the sub category from

            Returns:
                None    -   if the sub category was deleted successfully

            Raises:
                SDKException:
                    if sub category does not exist

                    if failed to delete the sub category

        &#34;&#34;&#34;
        if name not in self.sub_categories(category):
            raise SDKException(&#39;DownloadCenter&#39;, &#39;109&#39;)

        self._process_sub_category_request(&#39;delete&#39;, name, category)

    def upload_package(self, package, category, version, platform_download_locations, **kwargs):
        &#34;&#34;&#34;Uploads the given package to Download Center.

            Args:
                package                         (str)   --  name of the package to upload

                category                        (str)   --  category to upload the package for

                version                         (str)   --  product version for package to upload

                platform_download_locations     (list)  --  list consisting of dictionaries

                    where each dictionary contains the values for the platform, download type, and
                    location of the file

                    e.g.:
                        [
                            {
                                &#39;platform&#39;: &#39;Windows-x64&#39;,

                                &#39;download_type&#39;: &#39;Exe&#39;,

                                &#39;location&#39;: &#39;C:\\location1&#39;
                            }, {
                                &#39;platform&#39;: &#39;Windows-x64&#39;,

                                &#39;download_type&#39;: &#39;Script&#39;,

                                &#39;location&#39;: &#39;C:\\location2&#39;
                            }, {
                                &#39;platform&#39;: &#39;Windows-x86&#39;,

                                &#39;download_type&#39;: &#39;Exe&#39;,

                                &#39;location&#39;: &#39;C:\\location3&#39;
                            }, {
                                &#39;platform&#39;: &#39;Windows-x86&#39;,

                                &#39;download_type&#39;: &#39;Script&#39;,

                                &#39;location&#39;: &#39;C:\\location4&#39;
                            }
                        ]


                **kwargs:

                    valid_from          (str)   --  date from which the package should be valid

                        if the value is not specified, then current date is taken as it&#39;s value

                        format:     DD/MM/YYYY


                    description         (str)   --  description of the package

                    readme_location     (str)   --  location of the readme file

                        readme file should have one of the following extensions

                            [**.txt**, **.pdf**, **.doc**, **.docx**]


                    sub_category        (str)   --  sub category to associate the package with

                    vendor              (str)   --  vendor / distributor of the package

                    valid_to            (str)   --  date till which the package should be valid

                        format:     DD/MM/YYYY


                    repository          (str)   --  name of the repository to add the package to

                        if this value is not defined, the first repository will be taken by default


                    visible_to          (list)  --  list of users, the package should be visible to

                    not_visible_to      (list)  --  users, the package should not be visible to

                    early_preview_users (list)  --  list of users, the package should be
                    visible to before release


            Returns:
                None    -   if the package was uploaded successfully to Download Center


            Raises:
                SDKException:
                    if package with given name already exists

                    if category does not exists at Download Center

                    if version is not supported at Download Center

                    if platform is not supported at Download Center

                    if download type is not supported at Download Center

                    if sub category not present for the given category

                    if failed to upload the package

                    if error returned by the server

                    if response was not success

        &#34;&#34;&#34;
        if self.has_package(package):
            raise SDKException(&#39;DownloadCenter&#39;, &#39;114&#39;)

        if category not in self.categories:
            raise SDKException(
                &#39;DownloadCenter&#39;, &#39;103&#39;, &#39;Available categories: {0}&#39;.format(self.categories)
            )

        if version not in self.product_versions:
            raise SDKException(
                &#39;DownloadCenter&#39;, &#39;115&#39;, &#39;Available versions: {0}&#39;.format(self.product_versions)
            )

        platforms = []

        readme_location = kwargs.get(&#39;readme_location&#39;, &#39;&#39;)
        readme_file_extensions = [&#39;.txt&#39;, &#39;.pdf&#39;, &#39;.doc&#39;, &#39;.docx&#39;]

        if readme_location:
            if os.path.splitext(readme_location)[1] not in readme_file_extensions:
                raise SDKException(&#39;DownloadCenter&#39;, &#39;118&#39;)

        del readme_file_extensions

        for platform_dl_loc in platform_download_locations:
            platform = platform_dl_loc[&#39;platform&#39;]
            download_type = platform_dl_loc[&#39;download_type&#39;]
            location = platform_dl_loc[&#39;location&#39;]

            if platform not in self.platforms:
                raise SDKException(
                    &#39;DownloadCenter&#39;, &#39;116&#39;, &#39;Available platforms: {0}&#39;.format(self.platforms)
                )

            if download_type not in self.download_types:
                raise SDKException(
                    &#39;DownloadCenter&#39;,
                    &#39;117&#39;,
                    &#39;Available download types: {0}&#39;.format(self.download_types)
                )

            package_repository = kwargs.get(&#39;repository&#39;, self.servers_for_browse[0])
            package_repository_id = self._servers_for_browse[package_repository][&#39;id&#39;]
            package_repository_name = self._servers_for_browse[package_repository][&#39;internal_name&#39;]

            temp = {
                &#34;@name&#34;: platform,
                &#34;@readMeLocation&#34;: readme_location,
                &#34;@location&#34;: location,
                &#34;@id&#34;: self._platforms[platform][&#39;id&#39;],
                &#34;downloadType&#34;: {
                    &#34;@name&#34;: download_type,
                    &#34;@id&#34;: self._download_types[download_type][&#39;id&#39;]
                },
                &#39;pkgRepository&#39;: {
                    &#39;@repositoryId&#39;: package_repository_id,
                    &#39;@respositoryName&#39;: package_repository_name
                },
                &#39;@size&#39;: 186646528
            }

            platforms.append(temp)

        del platform
        del download_type
        del location
        del temp
        del platform_download_locations

        valid_from = kwargs.get(&#39;valid_from&#39;, time.strftime(&#39;%d/%m/%Y&#39;, time.localtime()))
        valid_from = int(time.mktime(time.strptime(valid_from, &#39;%d/%m/%Y&#39;)))

        valid_to = kwargs.get(&#39;valid_to&#39;, &#39;&#39;)
        if valid_to:
            valid_to = int(time.mktime(time.strptime(valid_from, &#39;%d/%m/%Y&#39;)))

        sub_category = {
            &#34;@entityType&#34;: 1,
            &#34;@categoryId&#34;: self._categories[category][&#39;id&#39;]
        }

        if &#39;sub_category&#39; in kwargs:
            sub_category_name = kwargs[&#39;sub_category&#39;]
            sub_categories = self.sub_categories(category)

            if sub_category_name in sub_categories:
                sub_category[&#34;@id&#34;] = self._categories[category][
                    &#39;sub_categories&#39;][sub_category_name][&#39;id&#39;]
            else:
                raise SDKException(
                    &#39;DownloadCenter&#39;, &#39;109&#39;, &#39;Available Sub Categories: {0}&#39;.format(sub_categories)
                )

            del sub_categories

        vendor = kwargs.get(&#39;vendor&#39;, &#39;&#39;)

        if vendor and vendor in self.vendors:
            vendor = self._vendors[vendor][&#39;id&#39;]

        visible_to = []
        not_visible_to = []
        early_preview_users = []

        for user in kwargs.get(&#39;visible_to&#39;, []):
            if user in self.users_and_groups:
                temp = {
                    &#39;@name&#39;: user,
                    &#39;@guid&#39;: self._users_and_groups[user][&#39;guid&#39;],
                    &#39;@type&#39;: self._users_and_groups[user][&#39;type&#39;]
                }
                visible_to.append(temp)

        for user in kwargs.get(&#39;not_visible_to&#39;, []):
            if user in self.users_and_groups:
                temp = {
                    &#39;@name&#39;: user,
                    &#39;@guid&#39;: self._users_and_groups[user][&#39;guid&#39;],
                    &#39;@type&#39;: self._users_and_groups[user][&#39;type&#39;]
                }
                not_visible_to.append(temp)

        for user in kwargs.get(&#39;early_preview_users&#39;, []):
            if user in self.users_and_groups:
                temp = {
                    &#39;@name&#39;: user,
                    &#39;@guid&#39;: self._users_and_groups[user][&#39;guid&#39;],
                    &#39;@type&#39;: self._users_and_groups[user][&#39;type&#39;]
                }
                early_preview_users.append(temp)

        request_json = {
            &#34;App_DCPackage&#34;: {
                &#34;@name&#34;: package,
                &#34;@description&#34;: kwargs.get(&#39;description&#39;, &#39;&#39;),
                &#34;@validFrom&#34;: valid_from,
                &#34;@validTo&#34;: valid_to,
                &#34;@rank&#34;: kwargs.get(&#39;rank&#39;, 0),
                &#34;category&#34;: {
                    &#34;@entityType&#34;: 0,
                    &#34;@id&#34;: self._categories[category][&#39;id&#39;]
                },
                &#34;subCategory&#34;: sub_category,
                &#34;platforms&#34;: platforms,
                &#34;productVersion&#34;: {
                    &#34;entityType&#34;: 3,
                    &#34;id&#34;: self._product_versions[version][&#39;id&#39;]
                },
                &#34;vendor&#34;: {
                    &#34;entityType&#34;: 6,
                    &#34;id&#34;: vendor
                },
                &#34;recutNumber&#34;: {
                    &#34;entityType&#34;: 4
                },
                &#34;visibleTo&#34;: visible_to,
                &#34;notVisibleTo&#34;: not_visible_to,
                &#34;earlyPreviewUsers&#34;: early_preview_users,
            }
        }

        xml = xmltodict.unparse(request_json)
        xml = xml[xml.find(&#39;&lt;App_&#39;):]

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;UPLOAD_PACKAGE&#39;], xml
        )

        self.refresh()

        if flag:
            response = xmltodict.parse(response.text)[&#39;App_DCPackage&#39;]
            error_code = response[&#39;errorDetail&#39;][&#39;@errorCode&#39;]

            if error_code != &#39;0&#39;:
                error_message = response[&#39;errorDetail&#39;][&#39;@errorMessage&#39;]

                raise SDKException(&#39;DownloadCenter&#39;, &#39;119&#39;, &#39;Error: {0}&#39;.format(error_message))
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def download_package(self, package, download_location, platform=None, download_type=None):
        &#34;&#34;&#34;Downloads the given package from Download Center to the path specified.

            Args:
                package             (str)   --  name of the pacakge to be downloaded

                download_location   (str)   --  path on local machine to download the package at

                platform            (str)   --  platform to download the package for

                    to be provided only if the package is added for multiple platforms

                    default: None

                download_type       (str)   --  type of package to be downloaded

                    to be provided only if multiple download types are present for single platform

                    default: None

            Returns:
                str     -   path on local machine where the file has been downloaded

            Raises:
                SDKException:
                    if package does not exist

                    if platform not given:
                        in case of multiple platforms

                    if platform given does not exists in the list of supported platforms

                    if download type is not specified:
                        if case of multiple download types for the selected platform

                    if download type given does not exists in the list of download types available

                    if error returned by the server

                    if response was not success

        &#34;&#34;&#34;

        # get the id of the package, if it is a valid package
        if not self.has_package(package):
            raise SDKException(&#39;DownloadCenter&#39;, &#39;106&#39;)
        else:
            package = package.lower()
            package_id = self._packages[package][&#39;id&#39;]

        def get_platform_id(package, platform):
            &#34;&#34;&#34;Checks if the platform is valid or not, and returns the platform id.

                If platform is set to None, gets the platform from the list of platforms,
                and the platform id.

                Args:
                    package     (str)   --  name of the package to check the platform for

                    platform    (str)   --  name of the platform to get the id of

                Returns:
                    (str, str)  -   tuple consisting of the platform name as the first,
                                        and platform id as the second value

                Raises:
                    SDKException:
                        if platform is not given, and multiple platforms exists for the package

                        if platform specified is not supported for the package

            &#34;&#34;&#34;
            platforms = self._packages[package][&#39;platforms&#39;]
            # check if the package has a single platform only, in case platform is not given
            if platform is None:

                # raise exception if multiple platforms exist for the package
                if len(platforms.keys()) &gt; 1:
                    raise SDKException(&#39;DownloadCenter&#39;, &#39;110&#39;)

                # get the platform name and id, if it&#39;s a single platform
                else:
                    platform = list(platforms.keys())[0]
                    platform_id = platforms[platform][&#39;id&#39;]

            # raise exception if the platform does not exists in the list of supported platforms,
            # when it is given
            elif platform not in platforms:
                raise SDKException(&#39;DownloadCenter&#39;, &#39;112&#39;)

            # get the id of the platform,
            # when it&#39;s given and exists in the list of supported platforms
            else:
                platform_id = platforms[platform][&#39;id&#39;]

            return platform, platform_id

        def get_download_type(package, platform, download_type):
            &#34;&#34;&#34;Checks if the download type for the given package and platform is valid or not.

                If download type is set to None, gets the download type from the list of download
                types availalble for the given package and platform.

                Args:
                    package         (str)   --  name of the package to get the download type for

                    platform        (str)   --  name of the platform to get the download type of

                    download_type   (str)   --  download type to be validated

                Returns:
                    str     -   name of the download type

                Raises:
                    SDKException:
                        if download type is not given, and multiple download types exists

                        if download type specified is not available for the package

            &#34;&#34;&#34;
            download_types = self._packages[package][&#39;platforms&#39;][platform][&#39;download_type&#39;]
            # check if the package has a single download type only,
            # in case download type is not given
            if download_type is None:

                # raise exception if multiple download types exist for the package and the platform
                if len(download_types) &gt; 1:
                    raise SDKException(&#39;DownloadCenter&#39;, &#39;111&#39;)

                # get the download type name, if it&#39;s a single download type for the given platform
                else:
                    download_type = download_types[0]

            # raise exception if the download type does not exists in the list, when it is given
            elif download_type not in download_types:
                raise SDKException(&#39;DownloadCenter&#39;, &#39;113&#39;)

            # use the download type given by the user
            else:
                pass

            return download_type

        platform, platform_id = get_platform_id(package, platform)
        download_type = get_download_type(package, platform, download_type)

        if not os.path.exists(download_location):
            try:
                os.makedirs(download_location)
            except FileExistsError:
                pass

        request_id = &#39;&#39;
        file_name = None

        # ID: 3 is static for Download Center, and has the value &#34;Package&#34;
        # ID: 2, needs to provide the package id as the value for &#34;name&#34;
        # ID: 9, needs to provide the platform id as the value for &#34;name&#34;
        # ID: 11, needs to provide the download tyoe as the value for &#34;name&#34;
        # ID: 10 is static for Streamed downloads
        request_xml = &#39;&#39;&#39;
        &lt;DM2ContentIndexing_OpenFileReq requestId=&#34;{3}&#34;&gt;
            &lt;fileParams id=&#34;3&#34; name=&#34;Package&#34;/&gt;
            &lt;fileParams id=&#34;2&#34; name=&#34;{0}&#34;/&gt;
            &lt;fileParams id=&#34;9&#34; name=&#34;{1}&#34;/&gt;
            &lt;fileParams id=&#34;11&#34; name=&#34;{2}&#34;/&gt;
            &lt;fileParams id=&#34;10&#34; name=&#34;Streamed&#34;/&gt;
        &lt;/DM2ContentIndexing_OpenFileReq&gt;
        &#39;&#39;&#39;

        # execute the request to get the details like file name, and request id
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;DOWNLOAD_PACKAGE&#39;], request_xml.format(
                package_id, platform_id, download_type, request_id
            )
        )

        if flag:
            error_list = response.json()[&#39;errList&#39;]
            file_content = response.json()[&#39;fileContent&#39;]

            if error_list:
                raise SDKException(&#39;DownloadCenter&#39;, &#39;107&#39;, &#39;Error: {0}&#39;.format(error_list))

            file_name = file_content.get(&#39;fileName&#39;, file_name)
            request_id = file_content[&#39;requestId&#39;]

            # full path of the file on local machine to be downloaded
            download_path = os.path.join(download_location, file_name)

            # execute request to get the stream of content
            # using request id returned in the previous response
            flag1, response1 = self._cvpysdk_object.make_request(
                &#39;POST&#39;,
                self._services[&#39;DOWNLOAD_VIA_STREAM&#39;],
                request_xml.format(package_id, platform_id, download_type, request_id),
                stream=True
            )

            # download chunks of 1MB each
            chunk_size = 1024 ** 2

            if flag1:
                with open(download_path, &#34;wb&#34;) as file_pointer:
                    for content in response1.iter_content(chunk_size=chunk_size):
                        file_pointer.write(content)
            else:
                response_string = self._update_response_(response1.text)
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        return download_path

    def delete_package(self, package):
        &#34;&#34;&#34;Deletes the package from Download Center.

            Args:
                package     (str)   --  name of the package to be deleted

            Returns:
                None    -   if the package was deleted successfully

            Raises:
                SDKException:
                    if no package exists with the given name

                    if failed to delete the package

                    if response is not success

        &#34;&#34;&#34;
        if not self.has_package(package):
            raise SDKException(&#39;DownloadCenter&#39;, &#39;106&#39;)
        else:
            package = package.lower()
            package_id = self._packages[package][&#39;id&#39;]

        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._services[&#39;DELETE_PACKAGE&#39;] % package_id
        )

        self.refresh()

        if flag:
            response = xmltodict.parse(response.text)[&#39;DM2ContentIndexing_CVDownloadCenterResp&#39;]

            if &#39;errList&#39; in response:
                error_code = response[&#39;errList&#39;][&#39;@errorCode&#39;]

                if error_code != &#39;0&#39;:
                    error_message = response[&#39;errList&#39;][&#39;@errLogMessage&#39;]

                    raise SDKException(&#39;DownloadCenter&#39;, &#39;119&#39;, &#39;Error: {0}&#39;.format(error_message))
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the DownloadCenter.&#34;&#34;&#34;
        self._get_properties()

        self._product_versions = {}
        self._servers_for_browse = {}
        self._users_and_groups = {}
        self._categories = {}
        self._download_types = {}
        self._vendors = {}
        self._platforms = {}
        self._packages = {}

        self._get_packages()</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.download_center.DownloadCenter.categories"><code class="name">var <span class="ident">categories</span></code></dt>
<dd>
<div class="desc"><p>Returns the categories of products available at Download Center.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/download_center.py#L497-L534" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def categories(self):
    &#34;&#34;&#34;Returns the categories of products available at Download Center.&#34;&#34;&#34;
    if not self._categories:
        categories = self._response[&#39;categories&#39;]

        if isinstance(categories, dict):
            categories = [categories]

        for category in categories:
            category_id = category[&#39;@id&#39;]
            temp = {}

            sub_categories = self._response[&#39;subCategories&#39;]

            if isinstance(sub_categories, dict):
                sub_categories = [sub_categories]

            for sub_category in sub_categories:
                cat_id = sub_category[&#39;@categoryId&#39;]

                if cat_id == category_id:
                    temp[sub_category[&#39;@name&#39;]] = {
                        &#39;id&#39;: sub_category[&#39;@id&#39;],
                        &#39;description&#39;: sub_category[&#39;@description&#39;],
                        &#39;entity_type&#39;: sub_category[&#39;@entityType&#39;],
                        &#39;attribute&#39;: sub_category[&#39;@attribute&#39;]
                    }

            self._categories[category[&#39;@name&#39;]] = {
                &#39;id&#39;: category_id,
                &#39;description&#39;: category[&#39;@description&#39;],
                &#39;entity_type&#39;: category[&#39;@entityType&#39;],
                &#39;attribute&#39;: category[&#39;@attribute&#39;],
                &#39;sub_categories&#39;: temp
            }

    return list(self._categories)</code></pre>
</details>
</dd>
<dt id="cvpysdk.download_center.DownloadCenter.download_types"><code class="name">var <span class="ident">download_types</span></code></dt>
<dd>
<div class="desc"><p>Returns the types of packages available for download at Download Center.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/download_center.py#L536-L551" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def download_types(self):
    &#34;&#34;&#34;Returns the types of packages available for download at Download Center.&#34;&#34;&#34;
    if not self._download_types:
        download_types = self._response[&#39;downloadTypes&#39;]

        if isinstance(download_types, dict):
            download_types = [download_types]

        for download_type in download_types:
            self._download_types[download_type[&#39;@name&#39;]] = {
                &#39;id&#39;: download_type[&#39;@id&#39;],
                &#39;entity_type&#39;: download_type[&#39;@entityType&#39;]
            }

    return list(self._download_types.keys())</code></pre>
</details>
</dd>
<dt id="cvpysdk.download_center.DownloadCenter.error_detail"><code class="name">var <span class="ident">error_detail</span></code></dt>
<dd>
<div class="desc"><p>Returns the error details.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/download_center.py#L472-L475" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def error_detail(self):
    &#34;&#34;&#34;Returns the error details.&#34;&#34;&#34;
    return self._response[&#39;errorDetail&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.download_center.DownloadCenter.packages"><code class="name">var <span class="ident">packages</span></code></dt>
<dd>
<div class="desc"><p>Returns the packages available for download at Download Center.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/download_center.py#L588-L594" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def packages(self):
    &#34;&#34;&#34;Returns the packages available for download at Download Center.&#34;&#34;&#34;
    if not self._packages:
        self._get_packages()

    return list(self._packages.keys())</code></pre>
</details>
</dd>
<dt id="cvpysdk.download_center.DownloadCenter.platforms"><code class="name">var <span class="ident">platforms</span></code></dt>
<dd>
<div class="desc"><p>Returns the platforms supported for packages at Download Center.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/download_center.py#L570-L586" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def platforms(self):
    &#34;&#34;&#34;Returns the platforms supported for packages at Download Center.&#34;&#34;&#34;
    if not self._platforms:
        platforms = self._response[&#39;platforms&#39;]

        if isinstance(platforms, dict):
            platforms = [platforms]

        for platform in platforms:
            self._platforms[platform[&#39;@name&#39;]] = {
                &#39;id&#39;: platform[&#39;@id&#39;],
                &#39;entity_type&#39;: platform[&#39;@entityType&#39;],
                &#39;architecture&#39;: platform[&#39;@architectureName&#39;]
            }

    return list(self._platforms.keys())</code></pre>
</details>
</dd>
<dt id="cvpysdk.download_center.DownloadCenter.product_versions"><code class="name">var <span class="ident">product_versions</span></code></dt>
<dd>
<div class="desc"><p>Return the versions of product available at Download Center.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/download_center.py#L436-L451" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def product_versions(self):
    &#34;&#34;&#34;Return the versions of product available at Download Center.&#34;&#34;&#34;
    if not self._product_versions:
        product_versions = self._response[&#39;productVersions&#39;]

        if isinstance(product_versions, dict):
            product_versions = [product_versions]

        for product_version in product_versions:
            self._product_versions[product_version[&#39;@name&#39;]] = {
                &#39;id&#39;: product_version[&#39;@id&#39;],
                &#39;entity_type&#39;: product_version[&#39;@entityType&#39;]
            }

    return list(self._product_versions.keys())</code></pre>
</details>
</dd>
<dt id="cvpysdk.download_center.DownloadCenter.servers_for_browse"><code class="name">var <span class="ident">servers_for_browse</span></code></dt>
<dd>
<div class="desc"><p>Returns the servers available for browse at Download Center.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/download_center.py#L453-L470" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def servers_for_browse(self):
    &#34;&#34;&#34;Returns the servers available for browse at Download Center.&#34;&#34;&#34;
    if not self._servers_for_browse:
        servers_for_browse = self._response[&#39;serversForBrowse&#39;]

        if isinstance(servers_for_browse, dict):
            servers_for_browse = [servers_for_browse]

        for server_for_browse in servers_for_browse:
            self._servers_for_browse[server_for_browse[&#39;@name&#39;]] = {
                &#39;id&#39;: server_for_browse[&#39;@id&#39;],
                &#39;internal_name&#39;: server_for_browse[&#39;@internalName&#39;],
                &#39;entity_type&#39;: server_for_browse[&#39;@entityType&#39;],
                &#39;attribute&#39;: server_for_browse[&#39;@attribute&#39;]
            }

    return list(self._servers_for_browse.keys())</code></pre>
</details>
</dd>
<dt id="cvpysdk.download_center.DownloadCenter.users_and_groups"><code class="name">var <span class="ident">users_and_groups</span></code></dt>
<dd>
<div class="desc"><p>Returns the Users and User Groups available at Download Center.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/download_center.py#L477-L495" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def users_and_groups(self):
    &#34;&#34;&#34;Returns the Users and User Groups available at Download Center.&#34;&#34;&#34;
    if not self._users_and_groups:
        users_and_groups = self._response[&#39;usersAndGroups&#39;]

        if isinstance(users_and_groups, dict):
            users_and_groups = [users_and_groups]

        for user_and_group in self._response[&#39;usersAndGroups&#39;]:
            self._users_and_groups[user_and_group[&#39;@name&#39;]] = {
                &#39;id&#39;: user_and_group[&#39;@id&#39;],
                &#39;provider_id&#39;: user_and_group[&#39;@providerId&#39;],
                &#39;guid&#39;: user_and_group[&#39;@guid&#39;],
                &#39;type&#39;: user_and_group[&#39;@type&#39;],
                &#39;service_type&#39;: user_and_group[&#39;@serviceType&#39;]
            }

    return list(self._users_and_groups.keys())</code></pre>
</details>
</dd>
<dt id="cvpysdk.download_center.DownloadCenter.vendors"><code class="name">var <span class="ident">vendors</span></code></dt>
<dd>
<div class="desc"><p>Returns the vendors available at Download Center.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/download_center.py#L553-L568" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def vendors(self):
    &#34;&#34;&#34;Returns the vendors available at Download Center.&#34;&#34;&#34;
    if not self._vendors:
        vendors = self._response[&#39;vendors&#39;]

        if isinstance(vendors, dict):
            vendors = [vendors]

        for vendor in vendors:
            self._vendors[vendor[&#39;@name&#39;]] = {
                &#39;id&#39;: vendor[&#39;@id&#39;],
                &#39;entity_type&#39;: vendor[&#39;@entityType&#39;]
            }

    return list(self._vendors.keys())</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.download_center.DownloadCenter.add_category"><code class="name flex">
<span>def <span class="ident">add_category</span></span>(<span>self, name, description=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds a new category with the given name, and description.</p>
<h2 id="args">Args</h2>
<p>name
(str)
&ndash;
name of the category to add</p>
<p>description
(str)
&ndash;
description for the category (optional)
default: None</p>
<h2 id="returns">Returns</h2>
<p>None
-
if the category was added successfully</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if category already exists</p>
<pre><code>if failed to add the category
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/download_center.py#L660-L682" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_category(self, name, description=None):
    &#34;&#34;&#34;Adds a new category with the given name, and description.

        Args:
            name            (str)   --  name of the category to add

            description     (str)   --  description for the category (optional)
                default: None

        Returns:
            None    -   if the category was added successfully

        Raises:
            SDKException:
                if category already exists

                if failed to add the category

    &#34;&#34;&#34;
    if name in self.categories:
        raise SDKException(&#39;DownloadCenter&#39;, &#39;104&#39;)

    self._process_category_request(&#39;add&#39;, name, description)</code></pre>
</details>
</dd>
<dt id="cvpysdk.download_center.DownloadCenter.add_sub_category"><code class="name flex">
<span>def <span class="ident">add_sub_category</span></span>(<span>self, name, category, description=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds a new sub category with the given name, and description to the specified category.</p>
<h2 id="args">Args</h2>
<p>name
(str)
&ndash;
name of the sub category to add</p>
<p>category
(str)
&ndash;
name of the category to add the sub category to</p>
<p>description
(str)
&ndash;
description for the sub category (optional)
default: None</p>
<h2 id="returns">Returns</h2>
<p>None
-
if the sub category was added successfully</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if category does not exist</p>
<pre><code>if sub category already exists

if failed to add the sub category
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/download_center.py#L738-L764" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_sub_category(self, name, category, description=None):
    &#34;&#34;&#34;Adds a new sub category with the given name, and description to the specified category.

        Args:
            name            (str)   --  name of the sub category to add

            category        (str)   --  name of the category to add the sub category to

            description     (str)   --  description for the sub category (optional)
                default: None

        Returns:
            None    -   if the sub category was added successfully

        Raises:
            SDKException:
                if category does not exist

                if sub category already exists

                if failed to add the sub category

    &#34;&#34;&#34;
    if name in self.sub_categories(category):
        raise SDKException(&#39;DownloadCenter&#39;, &#39;105&#39;)

    self._process_sub_category_request(&#39;add&#39;, name, category, description)</code></pre>
</details>
</dd>
<dt id="cvpysdk.download_center.DownloadCenter.delete_category"><code class="name flex">
<span>def <span class="ident">delete_category</span></span>(<span>self, name)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes the category with the given name.</p>
<h2 id="args">Args</h2>
<p>name
(str)
&ndash;
name of the category to delete</p>
<h2 id="returns">Returns</h2>
<p>None
-
if the category was deleted successfully</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if category does not exists</p>
<pre><code>if failed to delete the category
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/download_center.py#L717-L736" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete_category(self, name):
    &#34;&#34;&#34;Deletes the category with the given name.

        Args:
            name            (str)   --  name of the category to delete

        Returns:
            None    -   if the category was deleted successfully

        Raises:
            SDKException:
                if category does not exists

                if failed to delete the category

    &#34;&#34;&#34;
    if name not in self.categories:
        raise SDKException(&#39;DownloadCenter&#39;, &#39;108&#39;)

    self._process_category_request(&#39;delete&#39;, name)</code></pre>
</details>
</dd>
<dt id="cvpysdk.download_center.DownloadCenter.delete_package"><code class="name flex">
<span>def <span class="ident">delete_package</span></span>(<span>self, package)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes the package from Download Center.</p>
<h2 id="args">Args</h2>
<p>package
(str)
&ndash;
name of the package to be deleted</p>
<h2 id="returns">Returns</h2>
<p>None
-
if the package was deleted successfully</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if no package exists with the given name</p>
<pre><code>if failed to delete the package

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/download_center.py#L1331-L1373" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete_package(self, package):
    &#34;&#34;&#34;Deletes the package from Download Center.

        Args:
            package     (str)   --  name of the package to be deleted

        Returns:
            None    -   if the package was deleted successfully

        Raises:
            SDKException:
                if no package exists with the given name

                if failed to delete the package

                if response is not success

    &#34;&#34;&#34;
    if not self.has_package(package):
        raise SDKException(&#39;DownloadCenter&#39;, &#39;106&#39;)
    else:
        package = package.lower()
        package_id = self._packages[package][&#39;id&#39;]

    flag, response = self._cvpysdk_object.make_request(
        &#39;GET&#39;, self._services[&#39;DELETE_PACKAGE&#39;] % package_id
    )

    self.refresh()

    if flag:
        response = xmltodict.parse(response.text)[&#39;DM2ContentIndexing_CVDownloadCenterResp&#39;]

        if &#39;errList&#39; in response:
            error_code = response[&#39;errList&#39;][&#39;@errorCode&#39;]

            if error_code != &#39;0&#39;:
                error_message = response[&#39;errList&#39;][&#39;@errLogMessage&#39;]

                raise SDKException(&#39;DownloadCenter&#39;, &#39;119&#39;, &#39;Error: {0}&#39;.format(error_message))
    else:
        response_string = self._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.download_center.DownloadCenter.delete_sub_category"><code class="name flex">
<span>def <span class="ident">delete_sub_category</span></span>(<span>self, name, category)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes the sub category from the category with the given name.</p>
<h2 id="args">Args</h2>
<p>name
(str)
&ndash;
name of the sub category to delete</p>
<p>category
(str)
&ndash;
name of the category to delete the sub category from</p>
<h2 id="returns">Returns</h2>
<p>None
-
if the sub category was deleted successfully</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if sub category does not exist</p>
<pre><code>if failed to delete the sub category
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/download_center.py#L801-L822" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete_sub_category(self, name, category):
    &#34;&#34;&#34;Deletes the sub category from the category with the given name.

        Args:
            name            (str)   --  name of the sub category to delete

            category        (str)   --  name of the category to delete the sub category from

        Returns:
            None    -   if the sub category was deleted successfully

        Raises:
            SDKException:
                if sub category does not exist

                if failed to delete the sub category

    &#34;&#34;&#34;
    if name not in self.sub_categories(category):
        raise SDKException(&#39;DownloadCenter&#39;, &#39;109&#39;)

    self._process_sub_category_request(&#39;delete&#39;, name, category)</code></pre>
</details>
</dd>
<dt id="cvpysdk.download_center.DownloadCenter.download_package"><code class="name flex">
<span>def <span class="ident">download_package</span></span>(<span>self, package, download_location, platform=None, download_type=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Downloads the given package from Download Center to the path specified.</p>
<h2 id="args">Args</h2>
<p>package
(str)
&ndash;
name of the pacakge to be downloaded</p>
<p>download_location
(str)
&ndash;
path on local machine to download the package at</p>
<p>platform
(str)
&ndash;
platform to download the package for</p>
<pre><code>to be provided only if the package is added for multiple platforms

default: None
</code></pre>
<p>download_type
(str)
&ndash;
type of package to be downloaded</p>
<pre><code>to be provided only if multiple download types are present for single platform

default: None
</code></pre>
<h2 id="returns">Returns</h2>
<p>str
-
path on local machine where the file has been downloaded</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if package does not exist</p>
<pre><code>if platform not given:
    in case of multiple platforms

if platform given does not exists in the list of supported platforms

if download type is not specified:
    if case of multiple download types for the selected platform

if download type given does not exists in the list of download types available

if error returned by the server

if response was not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/download_center.py#L1116-L1329" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def download_package(self, package, download_location, platform=None, download_type=None):
    &#34;&#34;&#34;Downloads the given package from Download Center to the path specified.

        Args:
            package             (str)   --  name of the pacakge to be downloaded

            download_location   (str)   --  path on local machine to download the package at

            platform            (str)   --  platform to download the package for

                to be provided only if the package is added for multiple platforms

                default: None

            download_type       (str)   --  type of package to be downloaded

                to be provided only if multiple download types are present for single platform

                default: None

        Returns:
            str     -   path on local machine where the file has been downloaded

        Raises:
            SDKException:
                if package does not exist

                if platform not given:
                    in case of multiple platforms

                if platform given does not exists in the list of supported platforms

                if download type is not specified:
                    if case of multiple download types for the selected platform

                if download type given does not exists in the list of download types available

                if error returned by the server

                if response was not success

    &#34;&#34;&#34;

    # get the id of the package, if it is a valid package
    if not self.has_package(package):
        raise SDKException(&#39;DownloadCenter&#39;, &#39;106&#39;)
    else:
        package = package.lower()
        package_id = self._packages[package][&#39;id&#39;]

    def get_platform_id(package, platform):
        &#34;&#34;&#34;Checks if the platform is valid or not, and returns the platform id.

            If platform is set to None, gets the platform from the list of platforms,
            and the platform id.

            Args:
                package     (str)   --  name of the package to check the platform for

                platform    (str)   --  name of the platform to get the id of

            Returns:
                (str, str)  -   tuple consisting of the platform name as the first,
                                    and platform id as the second value

            Raises:
                SDKException:
                    if platform is not given, and multiple platforms exists for the package

                    if platform specified is not supported for the package

        &#34;&#34;&#34;
        platforms = self._packages[package][&#39;platforms&#39;]
        # check if the package has a single platform only, in case platform is not given
        if platform is None:

            # raise exception if multiple platforms exist for the package
            if len(platforms.keys()) &gt; 1:
                raise SDKException(&#39;DownloadCenter&#39;, &#39;110&#39;)

            # get the platform name and id, if it&#39;s a single platform
            else:
                platform = list(platforms.keys())[0]
                platform_id = platforms[platform][&#39;id&#39;]

        # raise exception if the platform does not exists in the list of supported platforms,
        # when it is given
        elif platform not in platforms:
            raise SDKException(&#39;DownloadCenter&#39;, &#39;112&#39;)

        # get the id of the platform,
        # when it&#39;s given and exists in the list of supported platforms
        else:
            platform_id = platforms[platform][&#39;id&#39;]

        return platform, platform_id

    def get_download_type(package, platform, download_type):
        &#34;&#34;&#34;Checks if the download type for the given package and platform is valid or not.

            If download type is set to None, gets the download type from the list of download
            types availalble for the given package and platform.

            Args:
                package         (str)   --  name of the package to get the download type for

                platform        (str)   --  name of the platform to get the download type of

                download_type   (str)   --  download type to be validated

            Returns:
                str     -   name of the download type

            Raises:
                SDKException:
                    if download type is not given, and multiple download types exists

                    if download type specified is not available for the package

        &#34;&#34;&#34;
        download_types = self._packages[package][&#39;platforms&#39;][platform][&#39;download_type&#39;]
        # check if the package has a single download type only,
        # in case download type is not given
        if download_type is None:

            # raise exception if multiple download types exist for the package and the platform
            if len(download_types) &gt; 1:
                raise SDKException(&#39;DownloadCenter&#39;, &#39;111&#39;)

            # get the download type name, if it&#39;s a single download type for the given platform
            else:
                download_type = download_types[0]

        # raise exception if the download type does not exists in the list, when it is given
        elif download_type not in download_types:
            raise SDKException(&#39;DownloadCenter&#39;, &#39;113&#39;)

        # use the download type given by the user
        else:
            pass

        return download_type

    platform, platform_id = get_platform_id(package, platform)
    download_type = get_download_type(package, platform, download_type)

    if not os.path.exists(download_location):
        try:
            os.makedirs(download_location)
        except FileExistsError:
            pass

    request_id = &#39;&#39;
    file_name = None

    # ID: 3 is static for Download Center, and has the value &#34;Package&#34;
    # ID: 2, needs to provide the package id as the value for &#34;name&#34;
    # ID: 9, needs to provide the platform id as the value for &#34;name&#34;
    # ID: 11, needs to provide the download tyoe as the value for &#34;name&#34;
    # ID: 10 is static for Streamed downloads
    request_xml = &#39;&#39;&#39;
    &lt;DM2ContentIndexing_OpenFileReq requestId=&#34;{3}&#34;&gt;
        &lt;fileParams id=&#34;3&#34; name=&#34;Package&#34;/&gt;
        &lt;fileParams id=&#34;2&#34; name=&#34;{0}&#34;/&gt;
        &lt;fileParams id=&#34;9&#34; name=&#34;{1}&#34;/&gt;
        &lt;fileParams id=&#34;11&#34; name=&#34;{2}&#34;/&gt;
        &lt;fileParams id=&#34;10&#34; name=&#34;Streamed&#34;/&gt;
    &lt;/DM2ContentIndexing_OpenFileReq&gt;
    &#39;&#39;&#39;

    # execute the request to get the details like file name, and request id
    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._services[&#39;DOWNLOAD_PACKAGE&#39;], request_xml.format(
            package_id, platform_id, download_type, request_id
        )
    )

    if flag:
        error_list = response.json()[&#39;errList&#39;]
        file_content = response.json()[&#39;fileContent&#39;]

        if error_list:
            raise SDKException(&#39;DownloadCenter&#39;, &#39;107&#39;, &#39;Error: {0}&#39;.format(error_list))

        file_name = file_content.get(&#39;fileName&#39;, file_name)
        request_id = file_content[&#39;requestId&#39;]

        # full path of the file on local machine to be downloaded
        download_path = os.path.join(download_location, file_name)

        # execute request to get the stream of content
        # using request id returned in the previous response
        flag1, response1 = self._cvpysdk_object.make_request(
            &#39;POST&#39;,
            self._services[&#39;DOWNLOAD_VIA_STREAM&#39;],
            request_xml.format(package_id, platform_id, download_type, request_id),
            stream=True
        )

        # download chunks of 1MB each
        chunk_size = 1024 ** 2

        if flag1:
            with open(download_path, &#34;wb&#34;) as file_pointer:
                for content in response1.iter_content(chunk_size=chunk_size):
                    file_pointer.write(content)
        else:
            response_string = self._update_response_(response1.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
    else:
        response_string = self._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    return download_path</code></pre>
</details>
</dd>
<dt id="cvpysdk.download_center.DownloadCenter.get_package_details"><code class="name flex">
<span>def <span class="ident">get_package_details</span></span>(<span>self, package)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the details of the package, like the package description, platforms, etc.</p>
<h2 id="args">Args</h2>
<p>package
(str)
&ndash;
name of the package to get the details of</p>
<h2 id="returns">Returns</h2>
<p>dict
-
dictionary consisting of the details of the package</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if package does not exist</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/download_center.py#L627-L658" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_package_details(self, package):
    &#34;&#34;&#34;Returns the details of the package, like the package description, platforms, etc.

        Args:
            package     (str)   --  name of the package to get the details of

        Returns:
            dict    -   dictionary consisting of the details of the package

        Raises:
            SDKException:
                if package does not exist

    &#34;&#34;&#34;
    if not self.has_package(package):
        raise SDKException(&#39;DownloadCenter&#39;, &#39;106&#39;)

    package = package.lower()
    package_detail = self._packages[package]

    output = {
        &#39;name&#39;: package,
        &#39;description&#39;: package_detail[&#39;description&#39;],
        &#39;platforms&#39;: {}
    }

    platforms = package_detail[&#39;platforms&#39;]

    for platform in platforms:
        output[&#39;platforms&#39;][platform] = platforms[platform][&#39;download_type&#39;]

    return output</code></pre>
</details>
</dd>
<dt id="cvpysdk.download_center.DownloadCenter.has_package"><code class="name flex">
<span>def <span class="ident">has_package</span></span>(<span>self, package)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks if a package with the given name already exists at Download Center or not.</p>
<h2 id="args">Args</h2>
<p>package
(str)
&ndash;
name of the package to check</p>
<h2 id="returns">Returns</h2>
<p>bool
-
boolean specifying whether the package exists or not</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/download_center.py#L596-L606" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_package(self, package):
    &#34;&#34;&#34;Checks if a package with the given name already exists at Download Center or not.

        Args:
            package     (str)   --  name of the package to check

        Returns:
            bool    -   boolean specifying whether the package exists or not

    &#34;&#34;&#34;
    return self.packages and package.lower() in self.packages</code></pre>
</details>
</dd>
<dt id="cvpysdk.download_center.DownloadCenter.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the properties of the DownloadCenter.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/download_center.py#L1375-L1388" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the properties of the DownloadCenter.&#34;&#34;&#34;
    self._get_properties()

    self._product_versions = {}
    self._servers_for_browse = {}
    self._users_and_groups = {}
    self._categories = {}
    self._download_types = {}
    self._vendors = {}
    self._platforms = {}
    self._packages = {}

    self._get_packages()</code></pre>
</details>
</dd>
<dt id="cvpysdk.download_center.DownloadCenter.sub_categories"><code class="name flex">
<span>def <span class="ident">sub_categories</span></span>(<span>self, category)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the sub categories available for the specified category.</p>
<h2 id="args">Args</h2>
<p>category
(str)
&ndash;
name of the category to get the sub categories of</p>
<h2 id="returns">Returns</h2>
<p>list
-
list of sub categories available for the given category</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if category does not exist</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/download_center.py#L608-L625" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def sub_categories(self, category):
    &#34;&#34;&#34;Returns the sub categories available for the specified category.

        Args:
            category    (str)   --  name of the category to get the sub categories of

        Returns:
            list    -   list of sub categories available for the given category

        Raises:
            SDKException:
                if category does not exist

    &#34;&#34;&#34;
    if category not in self.categories:
        raise SDKException(&#39;DownloadCenter&#39;, &#39;103&#39;)

    return list(self._categories[category][&#39;sub_categories&#39;].keys())</code></pre>
</details>
</dd>
<dt id="cvpysdk.download_center.DownloadCenter.update_category"><code class="name flex">
<span>def <span class="ident">update_category</span></span>(<span>self, name, new_name, description=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Updates the name and description of the category with the given name.</p>
<h2 id="args">Args</h2>
<p>name
(str)
&ndash;
name of the existing category to update</p>
<p>new_name
(str)
&ndash;
new name for the category</p>
<p>description
(str)
&ndash;
description for the category (optional)</p>
<pre><code>default: None
</code></pre>
<h2 id="returns">Returns</h2>
<p>None
-
if the category information was updated successfully</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if no category exists with the given name</p>
<pre><code>if category already exists with the new name specified

if failed to update the category
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/download_center.py#L684-L715" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def update_category(self, name, new_name, description=None):
    &#34;&#34;&#34;Updates the name and description of the category with the given name.

        Args:
            name            (str)   --  name of the existing category to update

            new_name        (str)   --  new name for the category

            description     (str)   --  description for the category (optional)

                default: None


        Returns:
            None    -   if the category information was updated successfully

        Raises:
            SDKException:
                if no category exists with the given name

                if category already exists with the new name specified

                if failed to update the category

    &#34;&#34;&#34;
    if name not in self.categories:
        raise SDKException(&#39;DownloadCenter&#39;, &#39;108&#39;)

    if new_name in self.categories:
        raise SDKException(&#39;DownloadCenter&#39;, &#39;104&#39;)

    self._process_category_request(&#39;update&#39;, name, description, new_name)</code></pre>
</details>
</dd>
<dt id="cvpysdk.download_center.DownloadCenter.update_sub_category"><code class="name flex">
<span>def <span class="ident">update_sub_category</span></span>(<span>self, name, category, new_name, description=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Updates the name and description of the sub category with the given name and category.</p>
<h2 id="args">Args</h2>
<p>name
(str)
&ndash;
name of the sub category to update the details of</p>
<p>category
(str)
&ndash;
name of the category to update the sub category of</p>
<p>new_name
(str)
&ndash;
new name for the sub category</p>
<p>description
(str)
&ndash;
description for the sub category (optional)</p>
<pre><code>default: None
</code></pre>
<h2 id="returns">Returns</h2>
<p>None
-
if the sub category information was updated successfully</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if no sub category exists with the given name</p>
<pre><code>if sub category already exists with the new name specified

if failed to update the sub category
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/download_center.py#L766-L799" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def update_sub_category(self, name, category, new_name, description=None):
    &#34;&#34;&#34;Updates the name and description of the sub category with the given name and category.

        Args:
            name            (str)   --  name of the sub category to update the details of

            category        (str)   --  name of the category to update the sub category of

            new_name        (str)   --  new name for the sub category

            description     (str)   --  description for the sub category (optional)

                default: None


        Returns:
            None    -   if the sub category information was updated successfully

        Raises:
            SDKException:
                if no sub category exists with the given name

                if sub category already exists with the new name specified

                if failed to update the sub category

    &#34;&#34;&#34;
    if name not in self.sub_categories(category):
        raise SDKException(&#39;DownloadCenter&#39;, &#39;109&#39;)

    if new_name in self.sub_categories(category):
        raise SDKException(&#39;DownloadCenter&#39;, &#39;105&#39;)

    self._process_sub_category_request(&#39;update&#39;, name, category, description, new_name)</code></pre>
</details>
</dd>
<dt id="cvpysdk.download_center.DownloadCenter.upload_package"><code class="name flex">
<span>def <span class="ident">upload_package</span></span>(<span>self, package, category, version, platform_download_locations, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Uploads the given package to Download Center.</p>
<h2 id="args">Args</h2>
<p>package
(str)
&ndash;
name of the package to upload</p>
<p>category
(str)
&ndash;
category to upload the package for</p>
<p>version
(str)
&ndash;
product version for package to upload</p>
<p>platform_download_locations
(list)
&ndash;
list consisting of dictionaries</p>
<pre><code>where each dictionary contains the values for the platform, download type, and
location of the file

e.g.:
    [
        {
            'platform': 'Windows-x64',

            'download_type': 'Exe',

            'location': 'C:\location1'
        }, {
            'platform': 'Windows-x64',

            'download_type': 'Script',

            'location': 'C:\location2'
        }, {
            'platform': 'Windows-x86',

            'download_type': 'Exe',

            'location': 'C:\location3'
        }, {
            'platform': 'Windows-x86',

            'download_type': 'Script',

            'location': 'C:\location4'
        }
    ]
</code></pre>
<p>**kwargs:</p>
<pre><code>valid_from          (str)   --  date from which the package should be valid

    if the value is not specified, then current date is taken as it's value

    format:     DD/MM/YYYY


description         (str)   --  description of the package

readme_location     (str)   --  location of the readme file

    readme file should have one of the following extensions

        [**.txt**, **.pdf**, **.doc**, **.docx**]


sub_category        (str)   --  sub category to associate the package with

vendor              (str)   --  vendor / distributor of the package

valid_to            (str)   --  date till which the package should be valid

    format:     DD/MM/YYYY


repository          (str)   --  name of the repository to add the package to

    if this value is not defined, the first repository will be taken by default


visible_to          (list)  --  list of users, the package should be visible to

not_visible_to      (list)  --  users, the package should not be visible to

early_preview_users (list)  --  list of users, the package should be
visible to before release
</code></pre>
<h2 id="returns">Returns</h2>
<p>None
-
if the package was uploaded successfully to Download Center</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if package with given name already exists</p>
<pre><code>if category does not exists at Download Center

if version is not supported at Download Center

if platform is not supported at Download Center

if download type is not supported at Download Center

if sub category not present for the given category

if failed to upload the package

if error returned by the server

if response was not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/download_center.py#L824-L1114" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def upload_package(self, package, category, version, platform_download_locations, **kwargs):
    &#34;&#34;&#34;Uploads the given package to Download Center.

        Args:
            package                         (str)   --  name of the package to upload

            category                        (str)   --  category to upload the package for

            version                         (str)   --  product version for package to upload

            platform_download_locations     (list)  --  list consisting of dictionaries

                where each dictionary contains the values for the platform, download type, and
                location of the file

                e.g.:
                    [
                        {
                            &#39;platform&#39;: &#39;Windows-x64&#39;,

                            &#39;download_type&#39;: &#39;Exe&#39;,

                            &#39;location&#39;: &#39;C:\\location1&#39;
                        }, {
                            &#39;platform&#39;: &#39;Windows-x64&#39;,

                            &#39;download_type&#39;: &#39;Script&#39;,

                            &#39;location&#39;: &#39;C:\\location2&#39;
                        }, {
                            &#39;platform&#39;: &#39;Windows-x86&#39;,

                            &#39;download_type&#39;: &#39;Exe&#39;,

                            &#39;location&#39;: &#39;C:\\location3&#39;
                        }, {
                            &#39;platform&#39;: &#39;Windows-x86&#39;,

                            &#39;download_type&#39;: &#39;Script&#39;,

                            &#39;location&#39;: &#39;C:\\location4&#39;
                        }
                    ]


            **kwargs:

                valid_from          (str)   --  date from which the package should be valid

                    if the value is not specified, then current date is taken as it&#39;s value

                    format:     DD/MM/YYYY


                description         (str)   --  description of the package

                readme_location     (str)   --  location of the readme file

                    readme file should have one of the following extensions

                        [**.txt**, **.pdf**, **.doc**, **.docx**]


                sub_category        (str)   --  sub category to associate the package with

                vendor              (str)   --  vendor / distributor of the package

                valid_to            (str)   --  date till which the package should be valid

                    format:     DD/MM/YYYY


                repository          (str)   --  name of the repository to add the package to

                    if this value is not defined, the first repository will be taken by default


                visible_to          (list)  --  list of users, the package should be visible to

                not_visible_to      (list)  --  users, the package should not be visible to

                early_preview_users (list)  --  list of users, the package should be
                visible to before release


        Returns:
            None    -   if the package was uploaded successfully to Download Center


        Raises:
            SDKException:
                if package with given name already exists

                if category does not exists at Download Center

                if version is not supported at Download Center

                if platform is not supported at Download Center

                if download type is not supported at Download Center

                if sub category not present for the given category

                if failed to upload the package

                if error returned by the server

                if response was not success

    &#34;&#34;&#34;
    if self.has_package(package):
        raise SDKException(&#39;DownloadCenter&#39;, &#39;114&#39;)

    if category not in self.categories:
        raise SDKException(
            &#39;DownloadCenter&#39;, &#39;103&#39;, &#39;Available categories: {0}&#39;.format(self.categories)
        )

    if version not in self.product_versions:
        raise SDKException(
            &#39;DownloadCenter&#39;, &#39;115&#39;, &#39;Available versions: {0}&#39;.format(self.product_versions)
        )

    platforms = []

    readme_location = kwargs.get(&#39;readme_location&#39;, &#39;&#39;)
    readme_file_extensions = [&#39;.txt&#39;, &#39;.pdf&#39;, &#39;.doc&#39;, &#39;.docx&#39;]

    if readme_location:
        if os.path.splitext(readme_location)[1] not in readme_file_extensions:
            raise SDKException(&#39;DownloadCenter&#39;, &#39;118&#39;)

    del readme_file_extensions

    for platform_dl_loc in platform_download_locations:
        platform = platform_dl_loc[&#39;platform&#39;]
        download_type = platform_dl_loc[&#39;download_type&#39;]
        location = platform_dl_loc[&#39;location&#39;]

        if platform not in self.platforms:
            raise SDKException(
                &#39;DownloadCenter&#39;, &#39;116&#39;, &#39;Available platforms: {0}&#39;.format(self.platforms)
            )

        if download_type not in self.download_types:
            raise SDKException(
                &#39;DownloadCenter&#39;,
                &#39;117&#39;,
                &#39;Available download types: {0}&#39;.format(self.download_types)
            )

        package_repository = kwargs.get(&#39;repository&#39;, self.servers_for_browse[0])
        package_repository_id = self._servers_for_browse[package_repository][&#39;id&#39;]
        package_repository_name = self._servers_for_browse[package_repository][&#39;internal_name&#39;]

        temp = {
            &#34;@name&#34;: platform,
            &#34;@readMeLocation&#34;: readme_location,
            &#34;@location&#34;: location,
            &#34;@id&#34;: self._platforms[platform][&#39;id&#39;],
            &#34;downloadType&#34;: {
                &#34;@name&#34;: download_type,
                &#34;@id&#34;: self._download_types[download_type][&#39;id&#39;]
            },
            &#39;pkgRepository&#39;: {
                &#39;@repositoryId&#39;: package_repository_id,
                &#39;@respositoryName&#39;: package_repository_name
            },
            &#39;@size&#39;: 186646528
        }

        platforms.append(temp)

    del platform
    del download_type
    del location
    del temp
    del platform_download_locations

    valid_from = kwargs.get(&#39;valid_from&#39;, time.strftime(&#39;%d/%m/%Y&#39;, time.localtime()))
    valid_from = int(time.mktime(time.strptime(valid_from, &#39;%d/%m/%Y&#39;)))

    valid_to = kwargs.get(&#39;valid_to&#39;, &#39;&#39;)
    if valid_to:
        valid_to = int(time.mktime(time.strptime(valid_from, &#39;%d/%m/%Y&#39;)))

    sub_category = {
        &#34;@entityType&#34;: 1,
        &#34;@categoryId&#34;: self._categories[category][&#39;id&#39;]
    }

    if &#39;sub_category&#39; in kwargs:
        sub_category_name = kwargs[&#39;sub_category&#39;]
        sub_categories = self.sub_categories(category)

        if sub_category_name in sub_categories:
            sub_category[&#34;@id&#34;] = self._categories[category][
                &#39;sub_categories&#39;][sub_category_name][&#39;id&#39;]
        else:
            raise SDKException(
                &#39;DownloadCenter&#39;, &#39;109&#39;, &#39;Available Sub Categories: {0}&#39;.format(sub_categories)
            )

        del sub_categories

    vendor = kwargs.get(&#39;vendor&#39;, &#39;&#39;)

    if vendor and vendor in self.vendors:
        vendor = self._vendors[vendor][&#39;id&#39;]

    visible_to = []
    not_visible_to = []
    early_preview_users = []

    for user in kwargs.get(&#39;visible_to&#39;, []):
        if user in self.users_and_groups:
            temp = {
                &#39;@name&#39;: user,
                &#39;@guid&#39;: self._users_and_groups[user][&#39;guid&#39;],
                &#39;@type&#39;: self._users_and_groups[user][&#39;type&#39;]
            }
            visible_to.append(temp)

    for user in kwargs.get(&#39;not_visible_to&#39;, []):
        if user in self.users_and_groups:
            temp = {
                &#39;@name&#39;: user,
                &#39;@guid&#39;: self._users_and_groups[user][&#39;guid&#39;],
                &#39;@type&#39;: self._users_and_groups[user][&#39;type&#39;]
            }
            not_visible_to.append(temp)

    for user in kwargs.get(&#39;early_preview_users&#39;, []):
        if user in self.users_and_groups:
            temp = {
                &#39;@name&#39;: user,
                &#39;@guid&#39;: self._users_and_groups[user][&#39;guid&#39;],
                &#39;@type&#39;: self._users_and_groups[user][&#39;type&#39;]
            }
            early_preview_users.append(temp)

    request_json = {
        &#34;App_DCPackage&#34;: {
            &#34;@name&#34;: package,
            &#34;@description&#34;: kwargs.get(&#39;description&#39;, &#39;&#39;),
            &#34;@validFrom&#34;: valid_from,
            &#34;@validTo&#34;: valid_to,
            &#34;@rank&#34;: kwargs.get(&#39;rank&#39;, 0),
            &#34;category&#34;: {
                &#34;@entityType&#34;: 0,
                &#34;@id&#34;: self._categories[category][&#39;id&#39;]
            },
            &#34;subCategory&#34;: sub_category,
            &#34;platforms&#34;: platforms,
            &#34;productVersion&#34;: {
                &#34;entityType&#34;: 3,
                &#34;id&#34;: self._product_versions[version][&#39;id&#39;]
            },
            &#34;vendor&#34;: {
                &#34;entityType&#34;: 6,
                &#34;id&#34;: vendor
            },
            &#34;recutNumber&#34;: {
                &#34;entityType&#34;: 4
            },
            &#34;visibleTo&#34;: visible_to,
            &#34;notVisibleTo&#34;: not_visible_to,
            &#34;earlyPreviewUsers&#34;: early_preview_users,
        }
    }

    xml = xmltodict.unparse(request_json)
    xml = xml[xml.find(&#39;&lt;App_&#39;):]

    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._services[&#39;UPLOAD_PACKAGE&#39;], xml
    )

    self.refresh()

    if flag:
        response = xmltodict.parse(response.text)[&#39;App_DCPackage&#39;]
        error_code = response[&#39;errorDetail&#39;][&#39;@errorCode&#39;]

        if error_code != &#39;0&#39;:
            error_message = response[&#39;errorDetail&#39;][&#39;@errorMessage&#39;]

            raise SDKException(&#39;DownloadCenter&#39;, &#39;119&#39;, &#39;Error: {0}&#39;.format(error_message))
    else:
        response_string = self._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#todo-implement-update-method-for-updating-details-of-a-package">TODO: implement update method for updating details of a package</a></li>
<li><a href="#todo-add-a-ps-script-to-be-called-via-commcell-client-to-check-if-the-location-is-valid">TODO: add a PS script to be called via commcell client, to check if the location is valid,</a></li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.download_center.DownloadCenter" href="#cvpysdk.download_center.DownloadCenter">DownloadCenter</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.download_center.DownloadCenter.add_category" href="#cvpysdk.download_center.DownloadCenter.add_category">add_category</a></code></li>
<li><code><a title="cvpysdk.download_center.DownloadCenter.add_sub_category" href="#cvpysdk.download_center.DownloadCenter.add_sub_category">add_sub_category</a></code></li>
<li><code><a title="cvpysdk.download_center.DownloadCenter.categories" href="#cvpysdk.download_center.DownloadCenter.categories">categories</a></code></li>
<li><code><a title="cvpysdk.download_center.DownloadCenter.delete_category" href="#cvpysdk.download_center.DownloadCenter.delete_category">delete_category</a></code></li>
<li><code><a title="cvpysdk.download_center.DownloadCenter.delete_package" href="#cvpysdk.download_center.DownloadCenter.delete_package">delete_package</a></code></li>
<li><code><a title="cvpysdk.download_center.DownloadCenter.delete_sub_category" href="#cvpysdk.download_center.DownloadCenter.delete_sub_category">delete_sub_category</a></code></li>
<li><code><a title="cvpysdk.download_center.DownloadCenter.download_package" href="#cvpysdk.download_center.DownloadCenter.download_package">download_package</a></code></li>
<li><code><a title="cvpysdk.download_center.DownloadCenter.download_types" href="#cvpysdk.download_center.DownloadCenter.download_types">download_types</a></code></li>
<li><code><a title="cvpysdk.download_center.DownloadCenter.error_detail" href="#cvpysdk.download_center.DownloadCenter.error_detail">error_detail</a></code></li>
<li><code><a title="cvpysdk.download_center.DownloadCenter.get_package_details" href="#cvpysdk.download_center.DownloadCenter.get_package_details">get_package_details</a></code></li>
<li><code><a title="cvpysdk.download_center.DownloadCenter.has_package" href="#cvpysdk.download_center.DownloadCenter.has_package">has_package</a></code></li>
<li><code><a title="cvpysdk.download_center.DownloadCenter.packages" href="#cvpysdk.download_center.DownloadCenter.packages">packages</a></code></li>
<li><code><a title="cvpysdk.download_center.DownloadCenter.platforms" href="#cvpysdk.download_center.DownloadCenter.platforms">platforms</a></code></li>
<li><code><a title="cvpysdk.download_center.DownloadCenter.product_versions" href="#cvpysdk.download_center.DownloadCenter.product_versions">product_versions</a></code></li>
<li><code><a title="cvpysdk.download_center.DownloadCenter.refresh" href="#cvpysdk.download_center.DownloadCenter.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.download_center.DownloadCenter.servers_for_browse" href="#cvpysdk.download_center.DownloadCenter.servers_for_browse">servers_for_browse</a></code></li>
<li><code><a title="cvpysdk.download_center.DownloadCenter.sub_categories" href="#cvpysdk.download_center.DownloadCenter.sub_categories">sub_categories</a></code></li>
<li><code><a title="cvpysdk.download_center.DownloadCenter.update_category" href="#cvpysdk.download_center.DownloadCenter.update_category">update_category</a></code></li>
<li><code><a title="cvpysdk.download_center.DownloadCenter.update_sub_category" href="#cvpysdk.download_center.DownloadCenter.update_sub_category">update_sub_category</a></code></li>
<li><code><a title="cvpysdk.download_center.DownloadCenter.upload_package" href="#cvpysdk.download_center.DownloadCenter.upload_package">upload_package</a></code></li>
<li><code><a title="cvpysdk.download_center.DownloadCenter.users_and_groups" href="#cvpysdk.download_center.DownloadCenter.users_and_groups">users_and_groups</a></code></li>
<li><code><a title="cvpysdk.download_center.DownloadCenter.vendors" href="#cvpysdk.download_center.DownloadCenter.vendors">vendors</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>