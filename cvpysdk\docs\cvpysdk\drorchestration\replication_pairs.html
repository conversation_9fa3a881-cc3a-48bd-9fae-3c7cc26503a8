<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.drorchestration.replication_pairs API documentation</title>
<meta name="description" content="ReplicationPairs: Class for a group of replication pairs from the replication monitor …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.drorchestration.replication_pairs</code></h1>
</header>
<section id="section-intro">
<p>ReplicationPairs: Class for a group of replication pairs from the replication monitor</p>
<h2 id="replicationpairs-methods">ReplicationPairs Methods:</h2>
<p><strong>init</strong>(commcell_object, <strong>kwargs)
&ndash; Initializes the Replication pairs class with Commcell object and filters
<strong>str</strong>()
&ndash; Returns the list of all replication pairs as a string
<strong>repr</strong>()
&ndash; Return the string representation for Replication pairs class
refresh()
&ndash; Refreshes the list of Replication pairs
_get_replication_pairs()
&ndash; Internal method to fetch replication pairs information from CommServ
</strong>replication_pairs**
&ndash; Returns the dictionary of replication pair IDs with their information
get(replication_id)
&ndash; Returns the ReplicationPair class associated to replication ID</p>
<p>ReplicationPair: Class for monitoring a replication pair which exists on periodic monitor</p>
<h2 id="replicationpair-attributes">ReplicationPair Attributes:</h2>
<p><strong>vm_pair_id</strong>
&ndash; Returns the live sync VM pair ID</p>
<p><strong>vm_pair_name</strong>
&ndash; Returns the live sync VM pair name</p>
<p><strong>replication_guid</strong>
&ndash; Returns the replication guid of the live sync pair</p>
<p><strong>source_vm</strong>
&ndash; Returns the name of the source virtual machine</p>
<p><strong>destination_vm</strong>
&ndash; Returns the name of the destination virtual machine</p>
<p><strong>destination_client</strong>
&ndash; Returns the destination client of the Live sync VM pair</p>
<p><strong>destination_proxy</strong>
&ndash; Returns the destination proxy of the Live sync VM pair</p>
<p><strong>destination_instance</strong>&ndash; Returns the destination instance of the Live sync VM pair</p>
<p><strong>status</strong>
&ndash; Returns the status of the live sync pair</p>
<p><strong>last_synced_backup_job</strong> &ndash; Returns the last synced backup job ID</p>
<p><strong>latest_replication_job</strong> &ndash; Returns the latest replication job ID</p>
<p><strong>last_replication_job</strong>
&ndash; Returns the last replication job ID</p>
<p>**reverse_replication_schedule_id &ndash; Returns the ID of the reverse replication schedule</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replication_pairs.py#L1-L278" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------


&#34;&#34;&#34;
ReplicationPairs: Class for a group of replication pairs from the replication monitor

ReplicationPairs Methods:
--------------------------
__init__(commcell_object, **kwargs)     -- Initializes the Replication pairs class with Commcell object and filters
__str__()                               -- Returns the list of all replication pairs as a string
__repr__()                              -- Return the string representation for Replication pairs class
refresh()                               -- Refreshes the list of Replication pairs
_get_replication_pairs()                -- Internal method to fetch replication pairs information from CommServ
**replication_pairs**                   -- Returns the dictionary of replication pair IDs with their information
get(replication_id)                     -- Returns the ReplicationPair class associated to replication ID


ReplicationPair: Class for monitoring a replication pair which exists on periodic monitor

ReplicationPair Attributes:
--------------------------

**vm_pair_id**          -- Returns the live sync VM pair ID

**vm_pair_name**        -- Returns the live sync VM pair name

**replication_guid**    -- Returns the replication guid of the live sync pair

**source_vm**           -- Returns the name of the source virtual machine

**destination_vm**      -- Returns the name of the destination virtual machine

**destination_client**  -- Returns the destination client of the Live sync VM pair

**destination_proxy**   -- Returns the destination proxy of the Live sync VM pair

**destination_instance**-- Returns the destination instance of the Live sync VM pair

**status**              -- Returns the status of the live sync pair

**last_synced_backup_job** -- Returns the last synced backup job ID

**latest_replication_job** -- Returns the latest replication job ID

**last_replication_job**   -- Returns the last replication job ID

**reverse_replication_schedule_id -- Returns the ID of the reverse replication schedule
&#34;&#34;&#34;
from ..exception import SDKException
from ..subclients.virtualserver.livesync.vsa_live_sync import LiveSyncVMPair


class ReplicationPairs:
    &#34;&#34;&#34;Class for a group of replication pairs from the replication monitor&#34;&#34;&#34;

    def __init__(self, commcell_object, **kwargs):
        &#34;&#34;&#34;
        Constructor method for replication monitor with filtering on the basis of kwargs
        Args:
            commcell_object (Commcell): CVPySDK commcell object
            kwargs (dict): The arguments passed for filtering replication pairs
                application_id: The application ID for the replication pairs. eg: 106 for VSA
                instance_id: The instance ID for the source instance associated
                subclient_id: The ID of VM group to find associated pairs for
                schedule_id: The ID of the replication schedule that replication pairs are part of
                failover_group_id: The ID of the failover group that is used for DROrchestration
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._cypysdk_object = self._commcell_object._cvpysdk_object
        self._services = self._commcell_object._services

        self._filter_args = kwargs

        self._REPLICATION_PAIRS = self._services[&#39;GET_REPLICATION_PAIRS&#39;]
        if self._filter_args.get(&#39;application_id&#39;):
            self._REPLICATION_PAIRS += f&#39;applicationId={self._filter_args.get(&#34;application_id&#34;)}&amp;&#39;
        if self._filter_args.get(&#39;instance_id&#39;):
            self._REPLICATION_PAIRS += f&#39;instanceId={self._filter_args.get(&#34;instance_id&#34;)}&amp;&#39;
        if self._filter_args.get(&#39;subclient_id&#39;):
            self._REPLICATION_PAIRS += f&#39;subclientId={self._filter_args.get(&#34;subclient_id&#34;)}&amp;&#39;
        if self._filter_args.get(&#39;schedule_id&#39;):
            self._REPLICATION_PAIRS += f&#39;taskId={self._filter_args.get(&#34;schedule_id&#34;)}&amp;&#39;
        if self._filter_args.get(&#39;failover_group_id&#39;):
            self._REPLICATION_PAIRS += f&#39;vAppId={self._filter_args.get(&#34;failover_group_id&#34;)}&amp;&#39;

        self._replication_pairs = {}

        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;
        Representation string consisting of all Replication VM pairs.
        Returns: (str) table of all replication pairs
        &#34;&#34;&#34;
        representation_string = (&#39;{:^5}\t{:^20}\t{:^25}\t{:^25}\n\n&#39;
                                 .format(&#39;S. No.&#39;, &#39;Replication ID&#39;, &#39;Source Name&#39;, &#39;Destination Name&#39;))

        for index, replication_pair in enumerate(self.replication_pairs):
            replication_pair_dict = self.replication_pairs[replication_pair]
            sub_str = (&#39;{:^5}\t{:^20}\t{:^25}\t{:^25}\n\n&#39;
                       .format(index + 1,
                               replication_pair,
                               replication_pair_dict.get(&#39;source_vm&#39;),
                               replication_pair_dict.get(&#39;destination_vm&#39;)
                               ))
            representation_string += sub_str
        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        representation_string = f&#39;ReplicationPairs class instance for Filters: &#34;{self._filter_args}&#34;&#39;
        return representation_string

    def refresh(self):
        &#34;&#34;&#34;Re-populate the replication monitor information&#34;&#34;&#34;
        self._get_replication_pairs()

    def _get_replication_pairs(self):
        &#34;&#34;&#34;
        Gets the list of replication pairs associated to the filters
        Returns: (dict) Dictionary of all replication pairs for the filters
                {
                    &#34;id1&#34;: {
                        &#34;source_vm&#34;: &#34;sourceVM1&#34;,
                        &#34;destination_vm&#34;: &#34;sourceVM1DRVM&#34;
                    },
                    &#34;id2&#34;: {
                        &#34;source_vm&#34;: &#34;sourceVM2&#34;,
                        &#34;destination_vm&#34;: &#34;sourceVM2DR&#34;
                    }
                }
        Raises:
            SDKException:
                if response is empty
                if response is not success
        &#34;&#34;&#34;
        flag, response = self._cypysdk_object.make_request(&#39;GET&#39;, self._REPLICATION_PAIRS)

        if flag:
            if response.json() and &#39;siteInfo&#39; in response.json():
                replication_pairs = {}
                for site_info in response.json().get(&#39;siteInfo&#39;, []):
                    replication_pairs[str(site_info.get(&#39;replicationId&#39;, 0))] = {
                        &#39;source_vm&#39;: site_info.get(&#39;sourceName&#39;, &#39;&#39;).lower(),
                        &#39;destination_vm&#39;: site_info.get(&#39;destinationName&#39;, &#39;&#39;).lower()
                    }
                return replication_pairs

            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def replication_pairs(self):
        &#34;&#34;&#34;
        Returns: (dict) Returns the dictionary of all the replication pairs and their info
                {
                    &#34;id1&#34;: {
                        &#34;source_vm&#34;: &#34;sourceVM1&#34;,
                        &#34;destination_vm&#34;: &#34;sourceVM1DRVM&#34;
                    },
                    &#34;id2&#34;: {
                        &#34;source_vm&#34;: &#34;sourceVM2&#34;,
                        &#34;destination_vm&#34;: &#34;sourceVM2DR&#34;
                    }
                }
        &#34;&#34;&#34;
        return self._replication_pairs

    def get(self, replication_id: str or int):
        &#34;&#34;&#34;
        Returns the ReplicationPair object associated to the pair ID
        Args:
            replication_id (str or int)    : The ID of the replication pair
        &#34;&#34;&#34;
        if replication_id in self.replication_pairs:
            return ReplicationPair(self._commcell_object, str(replication_id))
        raise Exception(&#39;ReplicationPairs&#39;, &#39;103&#39;)


class ReplicationPair(LiveSyncVMPair):
    &#34;&#34;&#34;Class for replication pair in the replication monitor&#34;&#34;&#34;

    def __init__(self, commcell_object, replication_pair_id: int or str):
        &#34;&#34;&#34;New constructor method which uses pair ID instead of Live sync objects
        Args:
            commcell_object (Commcell): CVPySDK commcell object
            replication_pair_id (int or str): The pair id of the live sync pair
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._cvpysdk_object = self._commcell_object._cvpysdk_object
        self._services = self._commcell_object._services
        self._update_response_ = self._commcell_object._update_response_

        self._vm_pair_id = str(replication_pair_id)

        self._VM_PAIR = self._services[&#39;GET_REPLICATION_PAIR&#39;] % (
            self._vm_pair_id
        )

        self._properties = {}
        self._replication_guid = None
        self._status = None
        self._failover_status = None
        self._source_vm = None
        self._destination_vm = None
        self._destination_client = None
        self._destination_proxy = None
        self._destination_instance = None
        self._last_backup_job = None
        self._latest_replication_job = None

        self._subclient_object = None
        self._subclient_name = None
        self._agent_object = None
        self.live_sync_pair = None

        self.refresh()

        self._populate_live_sync()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        return f&#39;ReplicationPair class instance for Replication ID: &#34;{self._vm_pair_id}&#34;&#39;

    def __str__(self):
        &#34;&#34;&#34;String representation of the instance of replication pair&#34;&#34;&#34;
        if self._source_vm and self._destination_vm:
            return f&#39;Replication pair: {self._source_vm} -&gt; {self._destination_vm}&#39;
        return f&#39;Replication pair ID: {self._vm_pair_id}&#39;

    def _populate_live_sync(self):
        &#34;&#34;&#34; Method used to populate live sync classes&#34;&#34;&#34;
        subclient_dict = self._properties.get(&#39;parentSubclient&#39;, {})
        subtask_dict = self._properties.get(&#39;subTask&#39;, {})

        client_name = subclient_dict.get(&#39;clientName&#39;)
        agent_name = self._properties.get(&#39;entity&#39;, {}).get(&#39;appName&#39;)
        self._subclient_id = str(subclient_dict.get(&#39;subclientId&#39;, &#39;&#39;))

        client_object = self._commcell_object.clients.get(client_name)
        self._agent_object = client_object.agents.get(agent_name)

        instance_name = [name for name, instance_id in
                         self._agent_object.instances.all_instances.items()
                         if instance_id == str(subclient_dict.get(&#39;instanceId&#39;, &#39;&#39;))][0]
        instance_object = self._agent_object.instances.get(instance_name)
        subclient = [(name, subclient_info.get(&#39;backupset&#39;)) for name, subclient_info in
                     instance_object.subclients.all_subclients.items()
                     if subclient_info.get(&#39;id&#39;) == self._subclient_id][0]

        backupset_object = instance_object.backupsets.get(subclient[-1])
        self._subclient_object = backupset_object.subclients.get(subclient[0].split(&#39;\\&#39;)[-1])
        self._subclient_name = self._subclient_object.name

        live_sync = [live_sync_name for live_sync_name, live_sync_dict in
                     self._subclient_object.live_sync.live_sync_pairs.items()
                     if live_sync_dict.get(&#39;id&#39;) == str(subtask_dict.get(&#39;taskId&#39;, &#39;&#39;))][0]

        self.live_sync_pair = self._subclient_object.live_sync.get(live_sync)
        self._vm_pair_name = self._source_vm</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.drorchestration.replication_pairs.ReplicationPair"><code class="flex name class">
<span>class <span class="ident">ReplicationPair</span></span>
<span>(</span><span>commcell_object, replication_pair_id: int)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for replication pair in the replication monitor</p>
<p>New constructor method which uses pair ID instead of Live sync objects</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>commcell_object</code></strong> :&ensp;<code>Commcell</code></dt>
<dd>CVPySDK commcell object</dd>
<dt><strong><code>replication_pair_id</code></strong> :&ensp;<code>int</code> or <code>str</code></dt>
<dd>The pair id of the live sync pair</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replication_pairs.py#L198-L278" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class ReplicationPair(LiveSyncVMPair):
    &#34;&#34;&#34;Class for replication pair in the replication monitor&#34;&#34;&#34;

    def __init__(self, commcell_object, replication_pair_id: int or str):
        &#34;&#34;&#34;New constructor method which uses pair ID instead of Live sync objects
        Args:
            commcell_object (Commcell): CVPySDK commcell object
            replication_pair_id (int or str): The pair id of the live sync pair
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._cvpysdk_object = self._commcell_object._cvpysdk_object
        self._services = self._commcell_object._services
        self._update_response_ = self._commcell_object._update_response_

        self._vm_pair_id = str(replication_pair_id)

        self._VM_PAIR = self._services[&#39;GET_REPLICATION_PAIR&#39;] % (
            self._vm_pair_id
        )

        self._properties = {}
        self._replication_guid = None
        self._status = None
        self._failover_status = None
        self._source_vm = None
        self._destination_vm = None
        self._destination_client = None
        self._destination_proxy = None
        self._destination_instance = None
        self._last_backup_job = None
        self._latest_replication_job = None

        self._subclient_object = None
        self._subclient_name = None
        self._agent_object = None
        self.live_sync_pair = None

        self.refresh()

        self._populate_live_sync()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        return f&#39;ReplicationPair class instance for Replication ID: &#34;{self._vm_pair_id}&#34;&#39;

    def __str__(self):
        &#34;&#34;&#34;String representation of the instance of replication pair&#34;&#34;&#34;
        if self._source_vm and self._destination_vm:
            return f&#39;Replication pair: {self._source_vm} -&gt; {self._destination_vm}&#39;
        return f&#39;Replication pair ID: {self._vm_pair_id}&#39;

    def _populate_live_sync(self):
        &#34;&#34;&#34; Method used to populate live sync classes&#34;&#34;&#34;
        subclient_dict = self._properties.get(&#39;parentSubclient&#39;, {})
        subtask_dict = self._properties.get(&#39;subTask&#39;, {})

        client_name = subclient_dict.get(&#39;clientName&#39;)
        agent_name = self._properties.get(&#39;entity&#39;, {}).get(&#39;appName&#39;)
        self._subclient_id = str(subclient_dict.get(&#39;subclientId&#39;, &#39;&#39;))

        client_object = self._commcell_object.clients.get(client_name)
        self._agent_object = client_object.agents.get(agent_name)

        instance_name = [name for name, instance_id in
                         self._agent_object.instances.all_instances.items()
                         if instance_id == str(subclient_dict.get(&#39;instanceId&#39;, &#39;&#39;))][0]
        instance_object = self._agent_object.instances.get(instance_name)
        subclient = [(name, subclient_info.get(&#39;backupset&#39;)) for name, subclient_info in
                     instance_object.subclients.all_subclients.items()
                     if subclient_info.get(&#39;id&#39;) == self._subclient_id][0]

        backupset_object = instance_object.backupsets.get(subclient[-1])
        self._subclient_object = backupset_object.subclients.get(subclient[0].split(&#39;\\&#39;)[-1])
        self._subclient_name = self._subclient_object.name

        live_sync = [live_sync_name for live_sync_name, live_sync_dict in
                     self._subclient_object.live_sync.live_sync_pairs.items()
                     if live_sync_dict.get(&#39;id&#39;) == str(subtask_dict.get(&#39;taskId&#39;, &#39;&#39;))][0]

        self.live_sync_pair = self._subclient_object.live_sync.get(live_sync)
        self._vm_pair_name = self._source_vm</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair" href="../subclients/virtualserver/livesync/vsa_live_sync.html#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair">LiveSyncVMPair</a></li>
</ul>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair" href="../subclients/virtualserver/livesync/vsa_live_sync.html#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair">LiveSyncVMPair</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.destination_client" href="../subclients/virtualserver/livesync/vsa_live_sync.html#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.destination_client">destination_client</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.destination_instance" href="../subclients/virtualserver/livesync/vsa_live_sync.html#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.destination_instance">destination_instance</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.destination_proxy" href="../subclients/virtualserver/livesync/vsa_live_sync.html#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.destination_proxy">destination_proxy</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.destination_vm" href="../subclients/virtualserver/livesync/vsa_live_sync.html#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.destination_vm">destination_vm</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.destination_vm_guid" href="../subclients/virtualserver/livesync/vsa_live_sync.html#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.destination_vm_guid">destination_vm_guid</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.failover_job_id" href="../subclients/virtualserver/livesync/vsa_live_sync.html#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.failover_job_id">failover_job_id</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.failover_status" href="../subclients/virtualserver/livesync/vsa_live_sync.html#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.failover_status">failover_status</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.is_warm_sync_pair" href="../subclients/virtualserver/livesync/vsa_live_sync.html#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.is_warm_sync_pair">is_warm_sync_pair</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.last_replication_job" href="../subclients/virtualserver/livesync/vsa_live_sync.html#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.last_replication_job">last_replication_job</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.last_synced_backup_job" href="../subclients/virtualserver/livesync/vsa_live_sync.html#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.last_synced_backup_job">last_synced_backup_job</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.latest_replication_job" href="../subclients/virtualserver/livesync/vsa_live_sync.html#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.latest_replication_job">latest_replication_job</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.refresh" href="../subclients/virtualserver/livesync/vsa_live_sync.html#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.replication_group_name" href="../subclients/virtualserver/livesync/vsa_live_sync.html#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.replication_group_name">replication_group_name</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.replication_guid" href="../subclients/virtualserver/livesync/vsa_live_sync.html#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.replication_guid">replication_guid</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.reverse_replication_schedule_id" href="../subclients/virtualserver/livesync/vsa_live_sync.html#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.reverse_replication_schedule_id">reverse_replication_schedule_id</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.source_vm" href="../subclients/virtualserver/livesync/vsa_live_sync.html#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.source_vm">source_vm</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.source_vm_guid" href="../subclients/virtualserver/livesync/vsa_live_sync.html#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.source_vm_guid">source_vm_guid</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.status" href="../subclients/virtualserver/livesync/vsa_live_sync.html#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.status">status</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.task_id" href="../subclients/virtualserver/livesync/vsa_live_sync.html#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.task_id">task_id</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.vm_pair_id" href="../subclients/virtualserver/livesync/vsa_live_sync.html#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.vm_pair_id">vm_pair_id</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.vm_pair_name" href="../subclients/virtualserver/livesync/vsa_live_sync.html#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.LiveSyncVMPair.vm_pair_name">vm_pair_name</a></code></li>
</ul>
</li>
</ul>
</dd>
<dt id="cvpysdk.drorchestration.replication_pairs.ReplicationPairs"><code class="flex name class">
<span>class <span class="ident">ReplicationPairs</span></span>
<span>(</span><span>commcell_object, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for a group of replication pairs from the replication monitor</p>
<p>Constructor method for replication monitor with filtering on the basis of kwargs</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>commcell_object</code></strong> :&ensp;<code>Commcell</code></dt>
<dd>CVPySDK commcell object</dd>
<dt><strong><code>kwargs</code></strong> :&ensp;<code>dict</code></dt>
<dd>The arguments passed for filtering replication pairs
application_id: The application ID for the replication pairs. eg: 106 for VSA
instance_id: The instance ID for the source instance associated
subclient_id: The ID of VM group to find associated pairs for
schedule_id: The ID of the replication schedule that replication pairs are part of
failover_group_id: The ID of the failover group that is used for DROrchestration</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replication_pairs.py#L69-L195" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class ReplicationPairs:
    &#34;&#34;&#34;Class for a group of replication pairs from the replication monitor&#34;&#34;&#34;

    def __init__(self, commcell_object, **kwargs):
        &#34;&#34;&#34;
        Constructor method for replication monitor with filtering on the basis of kwargs
        Args:
            commcell_object (Commcell): CVPySDK commcell object
            kwargs (dict): The arguments passed for filtering replication pairs
                application_id: The application ID for the replication pairs. eg: 106 for VSA
                instance_id: The instance ID for the source instance associated
                subclient_id: The ID of VM group to find associated pairs for
                schedule_id: The ID of the replication schedule that replication pairs are part of
                failover_group_id: The ID of the failover group that is used for DROrchestration
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._cypysdk_object = self._commcell_object._cvpysdk_object
        self._services = self._commcell_object._services

        self._filter_args = kwargs

        self._REPLICATION_PAIRS = self._services[&#39;GET_REPLICATION_PAIRS&#39;]
        if self._filter_args.get(&#39;application_id&#39;):
            self._REPLICATION_PAIRS += f&#39;applicationId={self._filter_args.get(&#34;application_id&#34;)}&amp;&#39;
        if self._filter_args.get(&#39;instance_id&#39;):
            self._REPLICATION_PAIRS += f&#39;instanceId={self._filter_args.get(&#34;instance_id&#34;)}&amp;&#39;
        if self._filter_args.get(&#39;subclient_id&#39;):
            self._REPLICATION_PAIRS += f&#39;subclientId={self._filter_args.get(&#34;subclient_id&#34;)}&amp;&#39;
        if self._filter_args.get(&#39;schedule_id&#39;):
            self._REPLICATION_PAIRS += f&#39;taskId={self._filter_args.get(&#34;schedule_id&#34;)}&amp;&#39;
        if self._filter_args.get(&#39;failover_group_id&#39;):
            self._REPLICATION_PAIRS += f&#39;vAppId={self._filter_args.get(&#34;failover_group_id&#34;)}&amp;&#39;

        self._replication_pairs = {}

        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;
        Representation string consisting of all Replication VM pairs.
        Returns: (str) table of all replication pairs
        &#34;&#34;&#34;
        representation_string = (&#39;{:^5}\t{:^20}\t{:^25}\t{:^25}\n\n&#39;
                                 .format(&#39;S. No.&#39;, &#39;Replication ID&#39;, &#39;Source Name&#39;, &#39;Destination Name&#39;))

        for index, replication_pair in enumerate(self.replication_pairs):
            replication_pair_dict = self.replication_pairs[replication_pair]
            sub_str = (&#39;{:^5}\t{:^20}\t{:^25}\t{:^25}\n\n&#39;
                       .format(index + 1,
                               replication_pair,
                               replication_pair_dict.get(&#39;source_vm&#39;),
                               replication_pair_dict.get(&#39;destination_vm&#39;)
                               ))
            representation_string += sub_str
        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        representation_string = f&#39;ReplicationPairs class instance for Filters: &#34;{self._filter_args}&#34;&#39;
        return representation_string

    def refresh(self):
        &#34;&#34;&#34;Re-populate the replication monitor information&#34;&#34;&#34;
        self._get_replication_pairs()

    def _get_replication_pairs(self):
        &#34;&#34;&#34;
        Gets the list of replication pairs associated to the filters
        Returns: (dict) Dictionary of all replication pairs for the filters
                {
                    &#34;id1&#34;: {
                        &#34;source_vm&#34;: &#34;sourceVM1&#34;,
                        &#34;destination_vm&#34;: &#34;sourceVM1DRVM&#34;
                    },
                    &#34;id2&#34;: {
                        &#34;source_vm&#34;: &#34;sourceVM2&#34;,
                        &#34;destination_vm&#34;: &#34;sourceVM2DR&#34;
                    }
                }
        Raises:
            SDKException:
                if response is empty
                if response is not success
        &#34;&#34;&#34;
        flag, response = self._cypysdk_object.make_request(&#39;GET&#39;, self._REPLICATION_PAIRS)

        if flag:
            if response.json() and &#39;siteInfo&#39; in response.json():
                replication_pairs = {}
                for site_info in response.json().get(&#39;siteInfo&#39;, []):
                    replication_pairs[str(site_info.get(&#39;replicationId&#39;, 0))] = {
                        &#39;source_vm&#39;: site_info.get(&#39;sourceName&#39;, &#39;&#39;).lower(),
                        &#39;destination_vm&#39;: site_info.get(&#39;destinationName&#39;, &#39;&#39;).lower()
                    }
                return replication_pairs

            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def replication_pairs(self):
        &#34;&#34;&#34;
        Returns: (dict) Returns the dictionary of all the replication pairs and their info
                {
                    &#34;id1&#34;: {
                        &#34;source_vm&#34;: &#34;sourceVM1&#34;,
                        &#34;destination_vm&#34;: &#34;sourceVM1DRVM&#34;
                    },
                    &#34;id2&#34;: {
                        &#34;source_vm&#34;: &#34;sourceVM2&#34;,
                        &#34;destination_vm&#34;: &#34;sourceVM2DR&#34;
                    }
                }
        &#34;&#34;&#34;
        return self._replication_pairs

    def get(self, replication_id: str or int):
        &#34;&#34;&#34;
        Returns the ReplicationPair object associated to the pair ID
        Args:
            replication_id (str or int)    : The ID of the replication pair
        &#34;&#34;&#34;
        if replication_id in self.replication_pairs:
            return ReplicationPair(self._commcell_object, str(replication_id))
        raise Exception(&#39;ReplicationPairs&#39;, &#39;103&#39;)</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.drorchestration.replication_pairs.ReplicationPairs.replication_pairs"><code class="name">var <span class="ident">replication_pairs</span></code></dt>
<dd>
<div class="desc"><p>Returns: (dict) Returns the dictionary of all the replication pairs and their info
{
"id1": {
"source_vm": "sourceVM1",
"destination_vm": "sourceVM1DRVM"
},
"id2": {
"source_vm": "sourceVM2",
"destination_vm": "sourceVM2DR"
}
}</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replication_pairs.py#L170-L185" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def replication_pairs(self):
    &#34;&#34;&#34;
    Returns: (dict) Returns the dictionary of all the replication pairs and their info
            {
                &#34;id1&#34;: {
                    &#34;source_vm&#34;: &#34;sourceVM1&#34;,
                    &#34;destination_vm&#34;: &#34;sourceVM1DRVM&#34;
                },
                &#34;id2&#34;: {
                    &#34;source_vm&#34;: &#34;sourceVM2&#34;,
                    &#34;destination_vm&#34;: &#34;sourceVM2DR&#34;
                }
            }
    &#34;&#34;&#34;
    return self._replication_pairs</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.drorchestration.replication_pairs.ReplicationPairs.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, replication_id: str)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the ReplicationPair object associated to the pair ID</p>
<h2 id="args">Args</h2>
<p>replication_id (str or int)
: The ID of the replication pair</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replication_pairs.py#L187-L195" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, replication_id: str or int):
    &#34;&#34;&#34;
    Returns the ReplicationPair object associated to the pair ID
    Args:
        replication_id (str or int)    : The ID of the replication pair
    &#34;&#34;&#34;
    if replication_id in self.replication_pairs:
        return ReplicationPair(self._commcell_object, str(replication_id))
    raise Exception(&#39;ReplicationPairs&#39;, &#39;103&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.replication_pairs.ReplicationPairs.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Re-populate the replication monitor information</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/replication_pairs.py#L130-L132" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Re-populate the replication monitor information&#34;&#34;&#34;
    self._get_replication_pairs()</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#replicationpairs-methods">ReplicationPairs Methods:</a></li>
<li><a href="#replicationpair-attributes">ReplicationPair Attributes:</a></li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.drorchestration" href="index.html">cvpysdk.drorchestration</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.drorchestration.replication_pairs.ReplicationPair" href="#cvpysdk.drorchestration.replication_pairs.ReplicationPair">ReplicationPair</a></code></h4>
</li>
<li>
<h4><code><a title="cvpysdk.drorchestration.replication_pairs.ReplicationPairs" href="#cvpysdk.drorchestration.replication_pairs.ReplicationPairs">ReplicationPairs</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.drorchestration.replication_pairs.ReplicationPairs.get" href="#cvpysdk.drorchestration.replication_pairs.ReplicationPairs.get">get</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replication_pairs.ReplicationPairs.refresh" href="#cvpysdk.drorchestration.replication_pairs.ReplicationPairs.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.drorchestration.replication_pairs.ReplicationPairs.replication_pairs" href="#cvpysdk.drorchestration.replication_pairs.ReplicationPairs.replication_pairs">replication_pairs</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>