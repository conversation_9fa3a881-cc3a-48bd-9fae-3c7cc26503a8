<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.backupsets.fsbackupset API documentation</title>
<meta name="description" content="Module for performing operations on a Backupset for the **File System** Agent …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.backupsets.fsbackupset</code></h1>
</header>
<section id="section-intro">
<p>Module for performing operations on a Backupset for the <strong>File System</strong> Agent.</p>
<p>FSBackupset is the only class defined in this file.</p>
<h2 id="fsbackupset">Fsbackupset</h2>
<p>restore_in_place()
&ndash;
Restores the files/folders specified in the
input paths list to the same location</p>
<p>restore_out_of_place()
&ndash;
Restores the files/folders specified in the input paths list
to the input client, at the specified destionation location</p>
<p>find_all_versions()
&ndash;
Returns the dict containing list of all the backuped up
versions of specified file</p>
<p>restore_bmr_admin_json()
&ndash;
Returns the restore JSON required for BMR operations.</p>
<p>restore_bmr_virtualserveropts_json()
&ndash;
Returns the Virtual Server JSON options needed
for BMR</p>
<p>_get_responsefile()
&ndash;
Returns the 1-touch response file for that backupset</p>
<p>run_bmr_restore()
&ndash;
Triggers the VIrtualize Me to VMWare job</p>
<p>_get_cs_login_details()
&ndash;
Get the cs login information.</p>
<p>_restore_aix_1touch_admin_json()
&ndash;Returns the restore JSON required for BMR operations.</p>
<p>run_bmr_aix_restore()
&ndash;Triggers the Aix 1-touch restore Job</p>
<p>index_pruning_type()
&ndash;
Sets the index pruning type</p>
<p>index_pruning_days_retention()
&ndash;
Sets the number of days to be maintained in
the index database</p>
<p>index_pruning_cycles_retention()
&ndash;
Sets the number of cycles to be maintained in
the index database</p>
<p>create_replica_copy()
&ndash;
Triggers Replica Copy for live Replication.</p>
<p>delete_replication_pair()
&ndash;
Delete Replication Pair</p>
<p>get_mount_path_guid()
&ndash;
Get the mount path volume's GUID</p>
<p>get_recovery_points()
&ndash;
Gets all the valid recovery points from the RPStore for the BLR pair</p>
<p>create_fsblr_replication_pair()
&ndash;
Create Live/Granular Replication Pair</p>
<p>create_granular_replica_copy()
&ndash;
Triggers
Granular replication permanent mount</p>
<p>get_browse_volume_guid()
&ndash;
It returns browse volume guid</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/fsbackupset.py#L1-L1690" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Module for performing operations on a Backupset for the **File System** Agent.

FSBackupset is the only class defined in this file.

FSBackupset:

    restore_in_place()          --  Restores the files/folders specified in the
    input paths list to the same location

    restore_out_of_place()      --  Restores the files/folders specified in the input paths list
    to the input client, at the specified destionation location

    find_all_versions()         --  Returns the dict containing list of all the backuped up
    versions of specified file

    restore_bmr_admin_json()    --  Returns the restore JSON required for BMR operations.

    restore_bmr_virtualserveropts_json()    --  Returns the Virtual Server JSON options needed
                                                for BMR

    _get_responsefile()          --  Returns the 1-touch response file for that backupset

    run_bmr_restore()           --  Triggers the VIrtualize Me to VMWare job

    _get_cs_login_details()     --  Get the cs login information.

    _restore_aix_1touch_admin_json()    --Returns the restore JSON required for BMR operations.

    run_bmr_aix_restore()               --Triggers the Aix 1-touch restore Job

    index_pruning_type()                --  Sets the index pruning type

    index_pruning_days_retention()      --  Sets the number of days to be maintained in
                                            the index database

    index_pruning_cycles_retention()    --  Sets the number of cycles to be maintained in
                                            the index database

    create_replica_copy()               --  Triggers Replica Copy for live Replication.

    delete_replication_pair()           --  Delete Replication Pair

    get_mount_path_guid()               --  Get the mount path volume&#39;s GUID

    get_recovery_points()               --  Gets all the valid recovery points from the RPStore for the BLR pair

    create_fsblr_replication_pair()     --  Create Live/Granular Replication Pair

    create_granular_replica_copy()      --  Triggers  Granular replication permanent mount

    get_browse_volume_guid()            --  It returns browse volume guid

&#34;&#34;&#34;

from __future__ import unicode_literals

from ..backupset import Backupset
from ..client import Client
from ..exception import SDKException
from ..job import Job
from ..schedules import Schedules
import socket

class FSBackupset(Backupset):
    &#34;&#34;&#34;Derived class from Backupset Base class, representing a fs backupset,
        and to perform operations on that backupset.&#34;&#34;&#34;

    def restore_in_place(
            self,
            paths,
            overwrite=True,
            restore_data_and_acl=True,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            fs_options=None,
            restore_jobs=None,
            advanced_options=None
    ):
        &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the same location.

            Args:
                paths                   (list)  --  list of full paths of files/folders to restore

                overwrite               (bool)  --  unconditional overwrite files during restore
                    default: True

                restore_data_and_acl    (bool)  --  restore data and ACL files
                    default: True

                copy_precedence         (int)   --  copy precedence value of storage policy copy
                    default: None

                from_time           (str)       --  time to retore the contents after
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time           (str)         --  time to retore the contents before
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                fs_options      (dict)          -- dictionary that includes all advanced options

                    options:

                        all_versions        : if set to True restores all the versions of the
                                                specified file

                        versions            : list of version numbers to be backed up

                        validate_only       : To validate data backed up for restore

                        no_of_streams   (int)       -- Number of streams to be used for restore

                restore_jobs    (list)          --  list of jobs to be restored if the job is index free restore

                advanced_options    (dict)  -- Advanced restore options

                    Options:

                        job_description (str)   --  Restore job description

                        timezone        (str)   --  Timezone to be used for restore

                            **Note** make use of TIMEZONES dict in constants.py to pass timezone

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        self._instance_object._restore_association = self._backupset_association

        if fs_options is not None and fs_options.get(&#39;no_of_streams&#39;, 1) &gt; 1 and not fs_options.get(&#39;destination_appTypeId&#39;, False):
            fs_options[&#39;destination_appTypeId&#39;] = int(self._client_object.agents.all_agents.get(&#39;file system&#39;, self._client_object.agents.all_agents.get(&#39;windows file system&#39;, self._client_object.agents.all_agents.get(&#39;linux file system&#39;, self._client_object.agents.all_agents.get(&#39;big data apps&#39;, self._client_object.agents.all_agents.get(&#39;cloud apps&#39;, 0))))))
            if not fs_options[&#39;destination_appTypeId&#39;]:
                del fs_options[&#39;destination_appTypeId&#39;]

        return self._instance_object._restore_in_place(
            paths=paths,
            overwrite=overwrite,
            restore_data_and_acl=restore_data_and_acl,
            copy_precedence=copy_precedence,
            from_time=from_time,
            to_time=to_time,
            fs_options=fs_options,
            restore_jobs=restore_jobs,
            advanced_options=advanced_options
        )

    def restore_out_of_place(
            self,
            client,
            destination_path,
            paths,
            overwrite=True,
            restore_data_and_acl=True,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            fs_options=None,
            restore_jobs=None,
            advanced_options=None
    ):
        &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the input client,
            at the specified destionation location.

            Args:
                client                (str/object) --  either the name of the client or
                                                           the instance of the Client

                destination_path      (str)        --  full path of the restore location on client

                paths                 (list)       --  list of full paths of
                                                           files/folders to restore

                overwrite             (bool)       --  unconditional overwrite files during restore
                    default: True

                restore_data_and_acl  (bool)       --  restore data and ACL files
                    default: True

                copy_precedence         (int)      --  copy precedence value of storage policy copy
                    default: None

                from_time           (str)          --  time to retore the contents after
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time           (str)            --  time to retore the contents before
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                fs_options      (dict)             -- dictionary that includes all advanced options

                    options:

                        preserve_level      : preserve level option to set in restore

                        proxy_client        : proxy that needed to be used for restore

                        impersonate_user    : Impersonate user options for restore

                        impersonate_password: Impersonate password option for restore
                                                in base64 encoded form

                        all_versions        : if set to True restores all the versions of the
                                                specified file

                        versions            : list of version numbers to be backed up

                        validate_only       : To validate data backed up for restore

                        no_of_streams   (int)       -- Number of streams to be used for restore

                restore_jobs    (list)          --  list of jobs to be restored if the job is index free restore

                advanced_options    (dict)  -- Advanced restore options

                    Options:

                        job_description (str)   --  Restore job description

                        timezone        (str)   --  Timezone to be used for restore

                            **Note** make use of TIMEZONES dict in constants.py to pass timezone

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if client is not a string or Client instance

                    if destination_path is not a string

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        self._instance_object._restore_association = self._backupset_association

        if not isinstance(client, (str, Client)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if isinstance(client, str):
            client = Client(self._commcell_object, client)

        if fs_options is not None and fs_options.get(&#39;no_of_streams&#39;, 1) &gt; 1 and not fs_options.get(&#39;destination_appTypeId&#39;, False):
            fs_options[&#39;destination_appTypeId&#39;] = int(client.agents.all_agents.get(&#39;file system&#39;, client.agents.all_agents.get(&#39;windows file system&#39;, client.agents.all_agents.get(&#39;linux file system&#39;, client.agents.all_agents.get(&#39;big data apps&#39;, client.agents.all_agents.get(&#39;cloud apps&#39;, 0))))))
            if not fs_options[&#39;destination_appTypeId&#39;]:
                del fs_options[&#39;destination_appTypeId&#39;]

        return self._instance_object._restore_out_of_place(
            client=client,
            destination_path=destination_path,
            paths=paths,
            overwrite=overwrite,
            restore_data_and_acl=restore_data_and_acl,
            copy_precedence=copy_precedence,
            from_time=from_time,
            to_time=to_time,
            fs_options=fs_options,
            restore_jobs=restore_jobs,
            advanced_options=advanced_options
        )

    def find_all_versions(self, *args, **kwargs):
        &#34;&#34;&#34;Searches the content of a Subclient, and returns all versions available for the content.

            Args:
                Dictionary of browse options:
                    Example:
                        find_all_versions({
                            &#39;path&#39;: &#39;c:\\hello&#39;,
                            &#39;show_deleted&#39;: True,
                            &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,
                            &#39;to_time&#39;: &#39;2016-04-31 12:00:00&#39;
                        })

                    (OR)

                Keyword argument of browse options:
                    Example:
                        find_all_versions(
                            path=&#39;c:\\hello.txt&#39;,
                            show_deleted=True,
                            to_time=&#39;2016-04-31 12:00:00&#39;
                        )

                Refer self._default_browse_options for all the supported options

        Returns:
            dict    -   dictionary of the specified file with list of all the file versions and
                            additional metadata retrieved from browse

        &#34;&#34;&#34;
        if args and isinstance(args[0], dict):
            options = args[0]
        else:
            options = kwargs

        options[&#39;operation&#39;] = &#39;all_versions&#39;

        return self._do_browse(options)

    def _restore_bmr_admin_json(self, ipconfig, hwconfig):
        &#34;&#34;&#34;&#34;setter for the BMR options required  for 1-touch restore

        Args:
            ipconfig    (dict)  -- The IP Configuration details obtained form response file.

            hwconfig    (dict)  -- The hardware configuration details obtained form response file.

        Returns :
                    returns the JSON required for BMR
        &#34;&#34;&#34;
        bmr_restore_vmprov_json = {
            &#34;vmProvisioningOption&#34;: {
                &#34;operationType&#34;: 14, &#34;virtualMachineOption&#34;: [
                    {
                        &#34;powerOnVM&#34;: True, &#34;isoPath&#34;: &#34; &#34;, &#34;flags&#34;: 0,
                        &#34;useLinkedClone&#34;: False, &#34;vendor&#34;: 1,
                        &#34;doLinkedCloneFromLocalTemplateCopy&#34;: False,
                        &#34;oneTouchResponse&#34;: {
                            &#34;copyPrecedence&#34;: 0, &#34;version&#34;: &#34;10.0&#34;, &#34;platform&#34;: 1,
                            &#34;dateCreated&#34;: &#34;&#34;,
                            &#34;automationTest&#34;: False, &#34;autoReboot&#34;: True, &#34;clients&#34;: [
                                {
                                    &#34;platform&#34;: 1, &#34;isBlockLevelBackup&#34;: False,
                                    &#34;indexCachePath&#34;: &#34;&#34;,
                                    &#34;isClientMA&#34;: False, &#34;clone&#34;: False,
                                    &#34;isIndexCacheInUSB&#34;: True, &#34;firewallCS&#34;: &#34;&#34;,
                                    &#34;backupSet&#34;: {
                                        &#34;backupsetName&#34;: self.backupset_name,
                                        &#34;backupsetId&#34;: int(self.backupset_id)
                                    }, &#34;netconfig&#34;:{
                                        &#34;wins&#34;:{
                                            &#34;useDhcp&#34;: False
                                        },
                                        &#34;firewall&#34;: {
                                            &#34;certificatePath&#34;: &#34;&#34;, &#34;certificateBlob&#34;: &#34;&#34;,
                                            &#34;configBlob&#34;: &#34;&#34;
                                        }, &#34;hosts&#34;: [
                                            {
                                                &#34;fqdn&#34;: &#34;&#34;, &#34;alias&#34;: &#34;&#34;, &#34;ip&#34;: {}
                                            }
                                        ],
                                        &#34;dns&#34;:
                                        {
                                            &#34;suffix&#34;: &#34;&#34;, &#34;useDhcp&#34;: False
                                        }, &#34;ipinfo&#34;: ipconfig
                                    },
                                    &#34;platformConfig&#34;:
                                        {
                                            &#34;platformCfgBlob&#34;: &#34;&#34;, &#34;win_passPhrase&#34;: &#34;&#34;,
                                            &#34;win_licenceKey&#34;: &#34;&#34;, &#34;type&#34;: 1,
                                            &#34;goToMiniSetUp&#34;: 0, &#34;Win_DomainCreds&#34;:
                                            {
                                                &#34;isClientInDomain&#34;: True, &#34;DomainCreds&#34;:
                                                {
                                                    &#34;password&#34;: &#34;&#34;, &#34;domainName&#34;: &#34;#####&#34;,
                                                    &#34;confirmPassword&#34;: &#34;&#34;,
                                                    &#34;userName&#34;: &#34;&#34;
                                                }
                                            }
                                        },
                                    &#34;firewallLocal&#34;: {
                                        &#34;certificatePath&#34;: &#34;&#34;, &#34;certificateBlob&#34;: &#34;&#34;, &#34;configBlob&#34;: &#34;&#34;
                                    },
                                    &#34;client&#34;: {
                                        &#34;hostName&#34;: &#34; &#34;, &#34;clientName&#34;: &#34; &#34;,
                                        &#34;type&#34;: 0
                                    },
                                    &#34;indexPathCreds&#34;: {
                                        &#34;password&#34;: &#34;&#34;, &#34;domainName&#34;: &#34;&#34;, &#34;confirmPassword&#34;: &#34;&#34;,
                                        &#34;userName&#34;: &#34;&#34;
                                    }, &#34;newclient&#34;: {
                                        &#34;hostName&#34;: &#34;&#34;, &#34;clientName&#34;: &#34;&#34;
                                    }
                                }
                            ], &#34;csinfo&#34;: {
                                &#34;firewallPort&#34;: 0, &#34;cvdPort&#34;: 0, &#34;evmgrPort&#34;: 0,
                                &#34;fwClientGroupName&#34;: &#34;&#34;,
                                &#34;mediaAgentInfo&#34;: {

                                }, &#34;mediaAgentIP&#34;: {

                                }, &#34;ip&#34;: {
                                    &#34;address&#34;: &#34; &#34;
                                }, &#34;commservInfo&#34;: {
                                    &#34;hostName&#34;: &#34; &#34;, &#34;clientName&#34;: &#34;&#34;
                                }, &#34;creds&#34;: {
                                    &#34;password&#34;: &#34; &#34;,
                                    &#34;domainName&#34;: &#34;&#34;, &#34;confirmPassword&#34;: &#34;&#34;, &#34;userName&#34;: &#34;admin&#34;
                                }
                            }, &#34;hwconfig&#34;: hwconfig,
                            &#34;netconfig&#34;: {
                                &#34;wins&#34;: {
                                    &#34;useDhcp&#34;: False
                                }, &#34;firewall&#34;: {
                                    &#34;certificatePath&#34;: &#34;&#34;, &#34;certificateBlob&#34;: &#34;&#34;, &#34;configBlob&#34;: &#34;&#34;
                                }, &#34;dns&#34;: {
                                    &#34;suffix&#34;: &#34;&#34;, &#34;useDhcp&#34;: False
                                }, &#34;ipinfo&#34;: {
                                    &#34;defaultgw&#34;: &#34;&#34;
                                }
                            }, &#34;dataBrowseTime&#34;: {

                            }, &#34;maInfo&#34;:
                                {
                                    &#34;clientName&#34;: &#34;&#34;
                                }, &#34;datastoreList&#34;: {}
                        }, &#34;vmInfo&#34;: {
                            &#34;registerWithFailoverCluster&#34;: False, &#34;proxyClient&#34;: {
                                &#34;clientName&#34;: &#34; &#34;
                            }, &#34;vmLocation&#34;: {
                                &#34;pathName&#34;: &#34; &#34;, &#34;inventoryPath&#34;: &#34;&#34;,
                                &#34;hostName&#34;: &#34; &#34;, &#34;resourcePoolPath&#34;: &#34;&#34;, &#34;dataCenterName&#34;: &#34;&#34;,
                                &#34;vCenter&#34;: &#34; &#34;, &#34;datastore&#34;: {
                                    &#34;name&#34;: &#34; &#34;
                                    },
                                &#34;instanceEntity&#34;: {
                                    &#34;clientName&#34;: &#34;&#34;,
                                    &#34;instanceName&#34;: &#34;&#34;,
                                    &#34;instanceId&#34;: 0
                                }
                                }, &#34;expirationTime&#34;: {}
                        }
                    }
                ]
            }
        }
        return bmr_restore_vmprov_json

    def _restore_bmr_virtualserveropts_json(self):
        &#34;&#34;&#34;Get the JSON for virtual server options


        Returns :
                    The virtualserver options JSON required for Virtualize Me restores

        &#34;&#34;&#34;
        bmr_restore_json = {
            &#34;diskLevelVMRestoreOption&#34;: {
                &#34;esxServerName&#34;: &#34; &#34;, &#34;userPassword&#34;: {

                }
            }
        }
        return bmr_restore_json

    def _restore_bmr_firewallopts_json(self, hostname, direction, port):
        &#34;&#34;&#34;Get the JSON for firewall configuration options

        Args:
            hostname    (String)   -- The hostname of the machine.

            direction   (Integer)  -- The direction of the connection.

            port        (Integer)  -- The port at which the machine will communicate.

        Returns :
                    The firewall configuration options JSON required for Virtualize Me restores

        &#34;&#34;&#34;
        bmr_firewall_restore_json = {
            &#34;direction&#34;: direction,
            &#34;connectionInfoList&#34;: [
                {
                    &#34;hostname&#34;: hostname,
                    &#34;port&#34;: port
                }
            ]
        }
        return bmr_firewall_restore_json

    def _azure_advancedrestoreopts_json(self):
        &#34;&#34;&#34;Get the JSON for Advanced restore options for azure


        Returns :
                    The Advanced restore options JSON required for Virtualize Me to Azure restores

        &#34;&#34;&#34;
        azure_adv_rest_opts_json = [
            {
                &#34;vmSize&#34;: &#34;&#34;,
                &#34;securityGroups&#34;: [
                    {
                        &#34;groupName&#34;: &#34;--Auto Select--&#34;,
                        &#34;groupId&#34;: &#34;&#34;
                    }
                ]
            }
        ]
        return azure_adv_rest_opts_json

    def _azure_advancedopts_json(self):
        &#34;&#34;&#34;Get the JSON for Advanced restore options for azure


        Returns :
                    The Advanced restore options JSON required for Virtualize Me to Azure restores

        &#34;&#34;&#34;
        azure_adv_opts_json = {
            &#34;networkCards&#34;: [
                {
                    &#34;privateIPAddress&#34;: &#34;&#34;,
                    &#34;networkName&#34;: &#34;&#34;,
                    &#34;label&#34;: &#34;--Auto Select--&#34;,
                    &#34;subnetNames&#34;: [
                        {
                            &#34;subnetId&#34;: &#34;&#34;
                        }
                    ]
                }
            ]
        }
        return azure_adv_opts_json


    def _get_responsefile(self):
        &#34;&#34;&#34;Get the response file for the backupset

        Returns :
            (dict, dict) - The hardware and IP configuration details from the response file obtained

        &#34;&#34;&#34;
        request = {
            &#34;CVGui_GetResponseFilesReq&#34;: {
                &#34;entity&#34;: {
                    &#34;_type_&#34;: &#34;6&#34;,
                    &#34;appName&#34;: &#34;File System&#34;,
                    &#34;applicationId&#34;: self._agent_object.agent_id,
                    &#34;backupsetId&#34;: self.backupset_id,
                    &#34;backupsetName&#34;: self.backupset_name,
                    &#34;clientId&#34;: self._agent_object._client_object.client_id,
                    &#34;clientName&#34;: self._agent_object._client_object.client_name,
                    &#34;commCellId&#34;: &#34;0&#34;,
                    &#34;commCellName&#34;: &#34;&#34;,
                    &#34;instanceId&#34;: &#34;1&#34;,
                    &#34;instanceName&#34;: &#34;&#34;
                },
                &#34;RecoveryTime&#34;: &#34;&#34;,
                &#34;platform&#34;: &#34;1&#34;,
                &#34;virtualizeME&#34;: &#34;1&#34;
            }
        }

        response = self._commcell_object._qoperation_execute(request)

        hwconfig = response[&#39;responseFile&#39;][&#39;hwconfig&#39;]
        ipconfig = response[&#39;responseFile&#39;][&#39;clients&#39;][0][&#39;netconfig&#39;][&#39;ipinfo&#39;]
        cs_user = response[&#39;responseFile&#39;][&#39;csinfo&#39;][&#39;creds&#39;][&#39;userName&#39;]
        cs_pwd = response[&#39;responseFile&#39;][&#39;csinfo&#39;][&#39;creds&#39;][&#39;password&#39;]
        cs_token = response[&#39;responseFile&#39;][&#39;csinfo&#39;][&#39;creds&#39;][&#39;password&#39;][5:]
        return hwconfig, ipconfig,cs_user,cs_pwd,cs_token

    def run_bmr_restore(self, **restore_options):
        &#34;&#34;&#34;
        Calling the create task API with the final restore JSON

        Args :
                IsoPath                 (String)    : The location of ISO in the datastore

                CommServIP              (String)    : The IP of the CS

                CommServHostname        (String)    : The hostname of he CS

                CommServUsername        (String)    : The username for the Comcell

                CommServPassword        (String)    : The password for the comcell

                Datastore               (String)    : The ESX store in which the VM is provisioned

                VcenterServerName       (String)    : The Vcenter to be used

                ClientHostName          (String)    : The hostname of the client being virtualized.

                VmName                  (String)    : The name with which the VM is provisioned.

                VirtualizationClient    (String)    : The vmware virualization client

                EsxServer               (String)    : The ESX server name

               NetworkLabel             (String)    : The network label to be assigned to the VM.

               HyperVHost               (String)    : The Hyper-V host

               GuestUser                (String)    : The Username of the guest OS

               GuestPassword            (String)    : The Password of the guest OS

               CloneClientName          (String)    : The clone client name

        Returns :
                    returns the task object

        &#34;&#34;&#34;
        client_name = self._agent_object._client_object.client_name

        self._instance_object._restore_association = self._backupset_association

        hwconfig, ipconfig, cs_username, cs_password = self._get_responsefile()
        response_json = self._restore_json(paths=[&#39;&#39;])

        restore_json_system_state = self._restore_bmr_admin_json(ipconfig, hwconfig)
        restore_json_virtualserver = self._restore_bmr_virtualserveropts_json()

        #Checking for Firewall rules
        if restore_options.get(&#34;FirewallClientGroup&#34;, &#34;&#34;).strip():
            fwconfigtocs = self._restore_bmr_firewallopts_json(restore_options.get(&#34;FirewallHostname&#34;),
                                                               restore_options.get(&#34;FirewallDirection&#34;),
                                                               restore_options.get(&#34;FirewallPort&#34;))
            restore_json_system_state[&#39;vmProvisioningOption&#39;][&#39;virtualMachineOption&#39;][0][
                &#39;oneTouchResponse&#39;][&#39;clients&#39;][0][&#39;fwconfigtocs&#39;] = fwconfigtocs
            restore_json_system_state[&#39;vmProvisioningOption&#39;][&#39;virtualMachineOption&#39;][0][&#39;oneTouchResponse&#39;][&#39;csinfo&#39;][
                &#39;fwClientGroupName&#39;] = restore_options.get(&#34;FirewallClientGroup&#34;)

        #Get instance Id of the virtual client
        virtual_client_object = self._commcell_object.clients.get(restore_options.get(&#39;VirtualizationClient&#39;))
        virtual_agent_object = virtual_client_object.agents.get(&#39;Virtual Server&#39;)
        instances_list = virtual_agent_object.instances._instances
        instance_id = int(list(instances_list.values())[0])
        instance_name = list(instances_list.keys())[0]

        response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][
            &#39;adminOpts&#39;] = restore_json_system_state

        response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][
            &#39;virtualServerRstOption&#39;] = restore_json_virtualserver

        response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;destination&#39;][
            &#39;inPlace&#39;] = False
        response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;subTask&#39;][&#39;subTaskType&#39;] = 1
        response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;subTask&#39;][&#39;operationType&#39;] = 4041

        vm_option = response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][
            &#39;vmProvisioningOption&#39;][&#39;virtualMachineOption&#39;][0]

        vm_option[&#39;vmInfo&#39;][&#39;vmLocation&#39;][&#39;instanceEntity&#39;][&#39;clientName&#39;] = restore_options.get(&#39;VirtualizationClient&#39;)
        vm_option[&#39;vmInfo&#39;][&#39;vmLocation&#39;][&#39;instanceEntity&#39;][&#39;instanceId&#39;] = instance_id

        if(response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][
            &#39;vmProvisioningOption&#39;][&#39;virtualMachineOption&#39;][0][&#39;oneTouchResponse&#39;][
            &#39;hwconfig&#39;][&#39;mem_size&#39;]) &lt; 4096:
            vm_option[&#39;oneTouchResponse&#39;][&#39;hwconfig&#39;][&#39;mem_size&#39;] = 4096

        if restore_options.get(&#39;CommServIP&#39;):
            cs_ip = restore_options.get(&#39;CommServIP&#39;)
        else:
            try:
                cs_ip = socket.gethostbyname(self._commcell_object.commserv_hostname)

            except Exception as e:
                raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, &#39;Error while reading CommServer IP : {}\n. Please set the CommServIP argument.&#39;.format(e))

        vm_option[&#39;oneTouchResponse&#39;][&#39;csinfo&#39;][&#39;ip&#39;][
            &#39;address&#39;] = cs_ip

        vm_option[&#39;oneTouchResponse&#39;][&#39;csinfo&#39;][&#39;commservInfo&#39;][
            &#39;clientName&#39;] = self._commcell_object.commserv_name
        vm_option[&#39;oneTouchResponse&#39;][&#39;csinfo&#39;][&#39;commservInfo&#39;][
            &#39;hostName&#39;] = self._commcell_object.commserv_hostname

        vm_option[&#39;oneTouchResponse&#39;][&#39;csinfo&#39;][&#39;creds&#39;][
            &#39;password&#39;] = cs_password

        vm_option[&#39;oneTouchResponse&#39;][&#39;csinfo&#39;][&#39;creds&#39;][
            &#39;userName&#39;] = cs_username

        vm_option[&#39;oneTouchResponse&#39;][&#39;hwconfig&#39;][&#39;vmName&#39;] = restore_options.get(&#39;VmName&#39;, None)

        vm_option[&#39;oneTouchResponse&#39;][&#39;hwconfig&#39;][&#39;overwriteVm&#39;] = True

        vm_option[&#39;oneTouchResponse&#39;][&#39;clients&#39;][0][&#39;client&#39;][
            &#39;hostName&#39;] = restore_options.get(&#39;ClientHostname&#39;, None)

        vm_option[&#39;oneTouchResponse&#39;][&#39;clients&#39;][0][&#39;client&#39;][
            &#39;clientName&#39;] = restore_options.get(&#39;ClientName&#39;, None)

        if instance_name == &#39;vmware&#39; or instance_name == &#39;hyper-v&#39;:

            vm_option[&#39;isoPath&#39;] = restore_options.get(&#39;IsoPath&#39;)

            vm_option[&#39;vmInfo&#39;][&#39;vmLocation&#39;][&#39;pathName&#39;] = restore_options.get(&#39;IsoPath&#39;, None)

            vm_option[&#39;oneTouchResponse&#39;][&#39;clients&#39;][0][&#39;netconfig&#39;][&#39;ipinfo&#39;][&#39;interfaces&#39;][0][
                &#39;protocols&#39;][0][&#39;useDhcp&#39;] = True

            vm_option[&#39;oneTouchResponse&#39;][&#39;clients&#39;][0][&#39;netconfig&#39;][&#39;ipinfo&#39;][&#39;interfaces&#39;][0][
                &#39;networkLabel&#39;] = restore_options.get(&#39;NetworkLabel&#39;, None)

            if &#39;scsi_disks&#39; in vm_option[&#39;oneTouchResponse&#39;][&#39;hwconfig&#39;]:
                vm_option[&#39;oneTouchResponse&#39;][&#39;hwconfig&#39;][&#39;scsi_disks&#39;][0][
                    &#39;dataStoreName&#39;] = restore_options.get(&#39;Datastore&#39;, None)

            if &#39;ide_disks&#39; in vm_option[&#39;oneTouchResponse&#39;][&#39;hwconfig&#39;]:
                vm_option[&#39;oneTouchResponse&#39;][&#39;hwconfig&#39;][&#39;ide_disks&#39;][0][
                    &#39;dataStoreName&#39;] = restore_options.get(&#39;Datastore&#39;, None)

            vm_option[&#39;vmInfo&#39;][&#39;vmLocation&#39;][&#39;datastore&#39;][
                &#39;name&#39;] = restore_options.get(&#39;Datastore&#39;, None)

            if instance_name == &#39;vmware&#39;:

                vm_option[&#39;vmInfo&#39;][&#39;proxyClient&#39;][
                    &#39;clientName&#39;] = restore_options.get(&#39;VirtualizationClient&#39;, None)

                response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][
                    &#39;restoreOptions&#39;][&#39;virtualServerRstOption&#39;][&#39;diskLevelVMRestoreOption&#39;][
                        &#39;esxServerName&#39;] = restore_options.get(&#39;VcenterServerName&#39;, None)

                response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;destination&#39;][
                    &#39;destClient&#39;][&#39;clientName&#39;] = restore_options.get(&#39;VirtualizationClient&#39;, None)

                vm_option[&#39;vmInfo&#39;][&#39;vmLocation&#39;][&#39;hostName&#39;] = restore_options.get(
                    &#39;EsxServer&#39;)

                vm_option[&#39;vmInfo&#39;][&#39;vmLocation&#39;][&#39;vCenter&#39;] = restore_options.get(&#39;VcenterServerName&#39;)

            if instance_name == &#39;hyper-v&#39;:

                if restore_options.get(&#39;OsType&#39;) == &#39;UNIX&#39;:

                    vm_option[&#39;vendor&#39;] = &#39;MICROSOFT&#39;

                response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][
                    &#39;restoreOptions&#39;][&#39;virtualServerRstOption&#39;][&#39;diskLevelVMRestoreOption&#39;][
                        &#39;esxServerName&#39;] = restore_options.get(&#39;VirtualizationClient&#39;, None)

                response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;destination&#39;][
                    &#39;destClient&#39;][&#39;clientName&#39;] = restore_options.get(&#39;VirtualizationClient&#39;, None)

                vm_option[&#39;vmInfo&#39;][&#39;vmLocation&#39;][&#39;hostName&#39;] = restore_options.get(
                    &#39;HyperVHost&#39;)

                vm_option[&#39;vmInfo&#39;][&#39;vmLocation&#39;][&#39;vCenter&#39;] = restore_options.get(&#39;VirtualizationClient&#39;)

        if &#39;azure&#39; in instance_name:

            az_advanced_ops_json = self._azure_advancedopts_json()

            az_adv_restore_opts_json = self._azure_advancedrestoreopts_json()

            vm_option[&#39;vendor&#39;] = 7

            vm_option[&#39;createPublicIp&#39;] = restore_options.get(&#39;CreatePublicIP&#39;)

            vm_option[&#39;oneTouchResponse&#39;][&#39;clients&#39;][0][&#39;isBlockLevelBackup&#39;] = True

            vm_option[&#39;vmInfo&#39;][&#39;vmLocation&#39;][&#39;advancedProperties&#39;] = az_advanced_ops_json

            vm_option[&#39;vmInfo&#39;][&#39;vmLocation&#39;][&#39;advancedProperties&#39;][&#39;networkCards&#39;][0][&#39;label&#39;] = &#34;--Auto Select--&#34;
            vm_option[&#39;vmInfo&#39;][&#39;vmLocation&#39;][&#39;hostName&#39;] = restore_options.get(&#39;ResourceGroup&#39;)

            response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][
                &#39;restoreOptions&#39;][&#39;virtualServerRstOption&#39;][&#39;diskLevelVMRestoreOption&#39;][
                &#39;advancedRestoreOptions&#39;] = az_adv_restore_opts_json

            response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][
                &#39;restoreOptions&#39;][&#39;virtualServerRstOption&#39;][&#39;diskLevelVMRestoreOption&#39;][
                &#39;advancedRestoreOptions&#39;][0][&#39;securityGroups&#39;][0][&#39;groupName&#39;] = &#34;--Auto Select--&#34;

            response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;destination&#39;][
                &#39;destClient&#39;][&#39;clientName&#39;] = restore_options.get(&#39;VirtualizationClient&#39;, None)

            if &#39;scsi_disks&#39; in vm_option[&#39;oneTouchResponse&#39;][&#39;hwconfig&#39;]:
                vm_option[&#39;oneTouchResponse&#39;][&#39;hwconfig&#39;][&#39;scsi_disks&#39;][0][
                    &#39;dataStoreName&#39;] = restore_options.get(&#39;StorageAccount&#39;, None)

            if &#39;ide_disks&#39; in vm_option[&#39;oneTouchResponse&#39;][&#39;hwconfig&#39;]:
                vm_option[&#39;oneTouchResponse&#39;][&#39;hwconfig&#39;][&#39;ide_disks&#39;][0][
                    &#39;dataStoreName&#39;] = restore_options.get(&#39;StorageAccount&#39;, None)

            vm_option[&#39;vmInfo&#39;][&#39;vmLocation&#39;][&#39;datastore&#39;][
                &#39;name&#39;] = restore_options.get(&#39;StorageAccount&#39;, None)

        if instance_name == &#39;azure stack&#39;:

            vm_option[&#39;vmInfo&#39;][&#39;vmLocation&#39;][&#39;vCenter&#39;] = restore_options.get(&#39;ManagementURL&#39;, None)

            vm_option[&#39;vendor&#39;] = 17

    # Additional options

        if restore_options.get(&#39;CloneClientName&#39;):
            vm_option[&#39;oneTouchResponse&#39;][&#39;clients&#39;][0][&#39;clone&#39;] = True
            vm_option[&#39;oneTouchResponse&#39;][&#39;clients&#39;][0][&#39;newclient&#39;][
                &#39;clientName&#39;] = restore_options.get(&#39;CloneClientName&#39;, None)
            vm_option[&#39;oneTouchResponse&#39;][&#39;clients&#39;][0][&#39;newclient&#39;][
                &#39;hostName&#39;] = restore_options.get(&#39;CloneClientName&#39;, None)

        if restore_options.get(&#39;OsType&#39;) == &#39;UNIX&#39;:
            vm_option[&#39;oneTouchResponse&#39;][&#39;clients&#39;][0][&#39;newclient&#39;][
                &#39;hostName&#39;] = &#39;&#39;

        if restore_options.get(&#39;UseDhcp&#39;):
            vm_option[&#39;oneTouchResponse&#39;][&#39;clients&#39;][0][&#39;netconfig&#39;][&#39;ipinfo&#39;][&#39;interfaces&#39;][0][
                &#39;protocols&#39;][0][&#39;useDhcp&#39;] = True


        return self._process_restore_response(response_json)

    def _get_cs_login_details(self):
        &#34;&#34;&#34;Get the cs login information.

        Returns :
            (dict, dict) - CS login details

        &#34;&#34;&#34;
        request = {
            &#34;CVGui_GetResponseFilesReq&#34;: {
                &#34;entity&#34;: {
                    &#34;_type_&#34;: &#34;6&#34;,
                    &#34;appName&#34;: &#34;File System&#34;,
                    &#34;applicationId&#34;: self._agent_object.agent_id,
                    &#34;backupsetId&#34;: self.backupset_id,
                    &#34;backupsetName&#34;: self.backupset_name,
                    &#34;clientId&#34;: self._agent_object._client_object.client_id,
                    &#34;clientName&#34;: self._agent_object._client_object.client_name,
                    &#34;commCellId&#34;: &#34;0&#34;,
                    &#34;commCellName&#34;: &#34;&#34;,
                    &#34;instanceId&#34;: &#34;1&#34;,
                    &#34;instanceName&#34;: &#34;&#34;
                },
                &#34;RecoveryTime&#34;: &#34;&#34;,
                &#34;platform&#34;: &#34;1&#34;,
                &#34;virtualizeME&#34;: &#34;1&#34;
            }
        }

        response = self._commcell_object._qoperation_execute(request)
        cs_user = self._commcell_object.commcell_username
        cs_pwd = self._commcell_object.auth_token
        cs_token = self._commcell_object.auth_token[5:]
        cs_name = self._commcell_object.commserv_name
        cs_hostname = self._commcell_object.commserv_hostname
        cs_ip_address = socket.gethostbyname(self._commcell_object.commserv_hostname)

        return cs_name, cs_hostname, cs_ip_address, cs_user, cs_pwd, cs_token

    def _restore_aix_1touch_admin_json(self):
        &#34;&#34;&#34;&#34;setter for the BMR options required  for 1-touch restore

                Returns :
                            returns the JSON required for BMR
                &#34;&#34;&#34;

        bmr_restore_aix1touch_json = {

            &#34;restoreFromBackupBeforeDate&#34;: False, &#34;recoverAllVolumeGroups&#34;: True,
            &#34;automaticClientReboot&#34;: True, &#34;preserveExistingVolumeGroups&#34;: False, &#34;responseData&#34;:
                [
                    {
                        &#34;copyPrecedence&#34;: 0, &#34;version&#34;: &#34;&#34;, &#34;platform&#34;: 0, &#34;dateCreated&#34;: &#34;&#34;,
                        &#34;automationTest&#34;: False, &#34;autoReboot&#34;: True, &#34;clients&#34;: [
                            {
                                &#34;platform&#34;: 0, &#34;isBlockLevelBackup&#34;: False, &#34;indexCachePath&#34;: &#34;&#34;, &#34;isClientMA&#34;: False,
                                &#34;clone&#34;: True, &#34;isIndexCacheInUSB&#34;: True, &#34;firewallCS&#34;: &#34;&#34;, &#34;backupSet&#34;:
                                {
                                    &#34;_type_&#34;: 6
                                }, &#34;netconfig&#34;:
                                {
                                    &#34;wins&#34;:
                                    {
                                        &#34;useDhcp&#34;: False
                                    },
                                    &#34;firewall&#34;:
                                        {
                                            &#34;certificatePath&#34;: &#34;&#34;, &#34;certificateBlob&#34;: &#34;&#34;, &#34;configBlob&#34;: &#34;&#34;
                                        },
                                    &#34;hosts&#34;: [
                                        {
                                            &#34;fqdn&#34;: &#34;&#34;, &#34;alias&#34;: &#34;&#34;,
                                            &#34;ip&#34;: {}
                                        }
                                    ], &#34;dns&#34;: {
                                        &#34;suffix&#34;: &#34;&#34;, &#34;useDhcp&#34;: False, &#34;nameservers&#34;:
                                        [
                                            {
                                                &#34;address&#34;: &#34;&#34;, &#34;family&#34;: 32
                                            }
                                        ]
                                    }, &#34;ipinfo&#34;: {
                                        &#34;defaultgw&#34;: &#34;&#34;, &#34;interfaces&#34;:
                                            [
                                                {
                                                    &#34;adapter&#34;: 0, &#34;networkLabel&#34;: &#34;&#34;, &#34;vlan&#34;: 0,
                                                    &#34;macAddressType&#34;: 0,
                                                    &#34;isEnabled&#34;: True, &#34;name&#34;: &#34;&#34;, &#34;mac&#34;: &#34;&#34;,
                                                    &#34;classicname&#34;: &#34;&#34;, &#34;wins&#34;:
                                                    {
                                                        &#34;useDhcp&#34;: False
                                                    }, &#34;dns&#34;:{
                                                        &#34;suffix&#34;: &#34;&#34;, &#34;useDhcp&#34;: False
                                                    }, &#34;protocols&#34;:[
                                                        {
                                                            &#34;gw&#34;: &#34;&#34;, &#34;subnetId&#34;: &#34;&#34;, &#34;netmask&#34;: &#34;&#34;,
                                                            &#34;networkAddress&#34;: &#34;&#34;, &#34;useDhcp&#34;: False,
                                                            &#34;ip&#34;:
                                                                {
                                                                    &#34;address&#34;: &#34;&#34;
                                                                }
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    }, &#34;platformConfig&#34;: {
                                        &#34;platformCfgBlob&#34;: &#34;&#34;, &#34;win_passPhrase&#34;: &#34;&#34;, &#34;win_licenceKey&#34;: &#34;&#34;, &#34;type&#34;: 0,
                                        &#34;goToMiniSetUp&#34;: 0, &#34;Win_DomainCreds&#34;: {
                                            &#34;isClientInDomain&#34;: False, &#34;DomainCreds&#34;:{
                                                &#34;password&#34;: &#34;&#34;, &#34;domainName&#34;: &#34;&#34;, &#34;confirmPassword&#34;: &#34;&#34;, &#34;userName&#34;: &#34;&#34;
                                            }
                                        }
                                    }, &#34;firewallLocal&#34;: {
                                        &#34;certificatePath&#34;: &#34;&#34;, &#34;certificateBlob&#34;: &#34;&#34;, &#34;configBlob&#34;: &#34;&#34;
                                    }, &#34;client&#34;: {},
                                &#34;indexPathCreds&#34;: {
                                    &#34;password&#34;: &#34;&#34;, &#34;domainName&#34;: &#34;&#34;, &#34;confirmPassword&#34;: &#34;&#34;, &#34;userName&#34;: &#34;&#34;
                                }, &#34;newclient&#34;: {
                                    &#34;hostName&#34;: &#34;&#34;, &#34;clientName&#34;: &#34;&#34;
                                }
                            }
                        ], &#34;csinfo&#34;: {
                            &#34;firewallPort&#34;: 0, &#34;cvdPort&#34;: 0, &#34;evmgrPort&#34;: 0, &#34;fwClientGroupName&#34;: &#34;&#34;,
                            &#34;mediaAgentInfo&#34;: {
                                &#34;_type_&#34;: 3
                            }, &#34;mediaAgentIP&#34;: {
                            }, &#34;ip&#34;: {
                                &#34;address&#34;: &#34;&#34;
                            }, &#34;commservInfo&#34;: {
                                &#34;hostName&#34;: &#34;&#34;, &#34;clientName&#34;: &#34;&#34;
                            }, &#34;creds&#34;: {
                                &#34;password&#34;: &#34;&#34;, &#34;domainName&#34;: &#34;&#34;,
                                &#34;confirmPassword&#34;: &#34;&#34;, &#34;userName&#34;: &#34;&#34;, &#34;token&#34;:&#34;&#34;
                            }
                        }, &#34;hwconfig&#34;: {
                            &#34;minMemoryMB&#34;: 0, &#34;vmName&#34;: &#34;&#34;, &#34;magicno&#34;: &#34;&#34;, &#34;enableDynamicMemory&#34;: False,
                            &#34;bootFirmware&#34;: 0, &#34;version&#34;: &#34;&#34;, &#34;mem_size&#34;: 0, &#34;cpu_count&#34;: 1, &#34;maxMemoryMB&#34;: 0,
                            &#34;nic_count&#34;: 1, &#34;overwriteVm&#34;: False, &#34;useMtptSelection&#34;: False, &#34;ide_count&#34;: 0,
                            &#34;mtpt_count&#34;: 0, &#34;scsi_count&#34;: 0, &#34;diskType&#34;: 1, &#34;optimizeStorage&#34;: False, &#34;systemDisk&#34;: {
                                &#34;forceProvision&#34;: False, &#34;bus&#34;: 0, &#34;refcnt&#34;: 0, &#34;size&#34;: 0, &#34;scsiControllerType&#34;: 0,
                                &#34;name&#34;: &#34;&#34;, &#34;dataStoreName&#34;: &#34;&#34;, &#34;vm_disk_type&#34;: 0, &#34;slot&#34;: 0, &#34;diskType&#34;: 1,
                                &#34;tx_type&#34;: 0
                            }
                        }, &#34;netconfig&#34;: {
                            &#34;wins&#34;: {
                                &#34;useDhcp&#34;: False
                            }, &#34;firewall&#34;: {
                                &#34;certificatePath&#34;: &#34;&#34;, &#34;certificateBlob&#34;: &#34;&#34;, &#34;configBlob&#34;: &#34;&#34;
                            }, &#34;dns&#34;: {
                                &#34;suffix&#34;: &#34;&#34;, &#34;useDhcp&#34;: False
                            }, &#34;ipinfo&#34;: {
                                &#34;defaultgw&#34;: &#34;&#34;
                            }
                        }, &#34;dataBrowseTime&#34;: {
                            &#34;_type_&#34;: 55
                        }, &#34;maInfo&#34;: {
                            &#34;clientName&#34;: &#34;&#34;
                        }, &#34;datastoreList&#34;: {
                        }
                    }
                ]
        }
        return bmr_restore_aix1touch_json

    def run_bmr_aix_restore(self, **restore_options):
        &#34;&#34;&#34;
                Calling the create task API with the final restore JSON

                Args :


                        Clone Clinet Name  (String)     : Clone machine name

                        Clone Hostname  (String)        :Clone machine host name

                        DNS Suffix      (String)        :Dns suffix name

                        DNS IP  (Integer)                :Ip of Dns Address

                        Clone IP    (Integer)            :Clone Machine IP

                        Clone Netmask (Integer)          :Clone Machine NetMask

                        Clone Gateway (Integer)          :Clone Machine Gateway

                        Auto Reboot   (Boolean)          :Client machine Auto reboot(True or False)

                        Clone          (Boolean)         :Is Clone enabled(True or False)

                        CS_Username   (String)    : The username for the Comcell

                        CS_Password   (String)     : The password for the comcell

                Returns :
                            returns the task object

                &#34;&#34;&#34;
        self._instance_object._restore_association = self._backupset_association
        request_json = self._restore_json(paths=[&#39;&#39;])
        restore_json_aix_system_state = self._restore_aix_1touch_admin_json()
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][
            &#39;oneTouchRestoreOption&#39;] = restore_json_aix_system_state
        hwconfig_aix = restore_json_aix_system_state[&#39;responseData&#39;][0][&#39;hwconfig&#39;]
        ipconfig_aix = restore_json_aix_system_state[&#39;responseData&#39;][0][&#39;clients&#39;][0][&#39;netconfig&#39;][&#39;ipinfo&#39;]
        cs_name, cs_hostname, cs_ip_address, cs_user, cs_pwd, cs_token = self._get_cs_login_details()
        vmjson = self._restore_bmr_admin_json(ipconfig_aix, hwconfig_aix)
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;] = vmjson
        is_clone = restore_options.get(&#39;clone&#39;, None)
        subtask_json = {
            &#39;subTaskType&#39; : 3,
            &#39;operationType&#39;: 1006
        }
        common_options = {
            &#39;systemStateBackup&#39; : True,
            &#39;copyToObjectStore&#39; :False,
            &#39;restoreToDisk&#39; : False,
            &#39;skipIfExists&#39; : restore_options.get(&#39;run_FS_restore&#39;, False),
            &#39;SyncRestore&#39; : False
        }
        response_data = {
                &#34;clients&#34;: [{
                    &#34;clone&#34;: is_clone,
                    &#34;netconfig&#34;: {
                        &#34;dns&#34;: {
                            &#34;suffix&#34;: restore_options.get(&#39;dns_suffix&#39;, None),
                            &#34;nameservers&#34;: [{
                                &#34;address&#34;: restore_options.get(&#39;dns_ip&#39;, None),
                            }]
                        },
                        &#34;ipinfo&#34;: {
                            &#34;interfaces&#34;: [{
                                &#34;protocols&#34;: [{
                                    &#34;gw&#34;: restore_options.get(&#39;clone_machine_gateway&#39;, None),
                                    &#34;netmask&#34;: restore_options.get(&#39;clone_machine_netmask&#39;, None),
                                    &#34;ip&#34;: {
                                        &#34;address&#34;: restore_options.get(&#39;clone_ip_address&#39;, None)
                                    }
                                }]
                            }]
                        }
                    },
                    &#34;newclient&#34;: {
                        &#34;hostName&#34;: restore_options.get(&#39;clone_client_hostname&#39;, None),
                        &#34;clientName&#34;: restore_options.get(&#39;clone_client_name&#39;, None)
                    }
                }],
                &#34;csinfo&#34;: {
                    &#34;ip&#34;: {
                        &#34;address&#34;: cs_ip_address
                    },
                    &#34;commservInfo&#34;: {
                        &#34;hostName&#34;: cs_hostname,
                        &#34;clientName&#34;: cs_name
                    },
                    &#34;creds&#34;: {
                        &#34;password&#34;: cs_pwd,
                        &#34;confirmPassword&#34;: cs_pwd,
                        &#34;userName&#34;: cs_user,
                        &#34;token&#34;:  cs_token
                    }
                },
            }
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;subTask&#39;] = subtask_json
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;commonOptions&#39;] = common_options
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;destination&#39;][&#39;destPath&#39;] = (
            [restore_options.get(&#39;onetouch_server_directory&#39;, &#39;&#39;)])
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;destination&#39;][
            &#39;destClient&#39;][&#39;clientName&#39;] = restore_options.get(&#39;onetouch_server&#39;, None)
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][
            &#39;oneTouchRestoreOption&#39;][&#39;automaticClientReboot&#39;] = restore_options.get(&#39;automaticClientReboot&#39;, None)

        if is_clone:
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;oneTouchRestoreOption&#39;][
                &#39;responseData&#39;][0][&#39;clients&#39;][0][&#39;netconfig&#39;][
                    &#39;dns&#39;][&#39;nameservers&#39;][0][&#39;address&#39;] = restore_options.get(&#39;dns_ip&#39;, None)
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;oneTouchRestoreOption&#39;][&#39;responseData&#39;][
            0] = response_data
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;oneTouchRestoreOption&#39;][
            &#39;restoreFromBackupBeforeDate&#39;] = True if restore_options.get(&#39;restoreFromBackupBeforeDate&#39;) else False
        if restore_options.get(&#39;onetouch_backup_jobid&#39;) is not None:
            _job = self._commcell_object.job_controller.get(restore_options.get(&#39;onetouch_backup_jobid&#39;))
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;browseOption&#34;][
                &#34;timeRange&#34;][&#34;toTime&#34;] = _job.end_timestamp
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;browseOption&#34;][
                &#34;timeRange&#34;][&#34;fromTime&#34;] = _job.start_timestamp

            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;vmProvisioningOption&#39;][
                &#39;virtualMachineOption&#39;][0][&#39;oneTouchResponse&#39;][&#39;dataBrowseTime&#39;][
                &#39;TimeZoneName&#39;] = self._commcell_object.commserv_timezone
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;vmProvisioningOption&#39;][
                &#39;virtualMachineOption&#39;][0][&#39;oneTouchResponse&#39;][&#39;dataBrowseTime&#39;][&#39;time&#39;] = _job.end_timestamp

            one_touch_option = {&#39;fromTime&#39;: 0, &#39;toTime&#39;: _job.end_timestamp}
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;oneTouchOption&#39;] = one_touch_option
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;fileOption&#39;][
                &#39;sourceItem&#39;] = ([f&#39;2:{_job.job_id}&#39;])
        return self._process_restore_response(request_json)

    @property
    def index_server(self):
        &#34;&#34;&#34;Returns the index server client set for the backupset&#34;&#34;&#34;

        client_name = None

        if &#39;indexSettings&#39; in self._properties:
            if &#39;currentIndexServer&#39; in self._properties[&#39;indexSettings&#39;]:
                client_name = self._properties[&#39;indexSettings&#39;][&#39;currentIndexServer&#39;][&#39;clientName&#39;]

        if client_name is not None:
            return Client(self._commcell_object, client_name=client_name)

        return None

    @index_server.setter
    def index_server(self, value):
        &#34;&#34;&#34;Sets index server client for the backupset. Property value should be a client object

            Args:
                value   (object)    --  The cvpysdk client object of the index server client

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;

        if not isinstance(value, Client):
            raise SDKException(&#39;Backupset&#39;, &#39;106&#39;)

        properties = self._properties
        index_server_id = int(value.client_id)
        index_server_name = value.client_name

        if &#39;indexSettings&#39; in properties:
            qualified_index_servers = []
            if &#39;qualifyingIndexServers&#39; in properties[&#39;indexSettings&#39;]:
                for index_server in properties[&#39;indexSettings&#39;][&#39;qualifyingIndexServers&#39;]:
                    qualified_index_servers.append(index_server[&#39;clientId&#39;])

            if index_server_id in qualified_index_servers:
                properties[&#39;indexSettings&#39;][&#39;currentIndexServer&#39;] = {
                    &#39;clientId&#39;: index_server_id,
                    &#39;clientName&#39;: index_server_name
                }
            else:
                raise SDKException(
                    &#39;Backupset&#39;, &#39;102&#39;, &#39;{0} is not a qualified IndexServer client&#39;.format(
                        index_server_name))
        else:
            properties[&#39;indexSettings&#39;] = {
                &#39;currentIndexServer&#39;: {
                    &#39;clientId&#39;: index_server_id,
                    &#39;clientName&#39;: index_server_name
                }
            }

        request_json = {
            &#39;backupsetProperties&#39;: properties
        }

        self._process_update_reponse(request_json)

    @property
    def index_pruning_type(self):
        &#34;&#34;&#34;Returns index pruning type for the backupset&#34;&#34;&#34;
        return self._properties[&#34;indexSettings&#34;][&#34;indexPruningType&#34;]

    @property
    def index_pruning_days_retention(self):
        &#34;&#34;&#34;Returns number of days to be maintained in index by index pruning for the backupset&#34;&#34;&#34;

        return self._properties[&#34;indexSettings&#34;][&#34;indexRetDays&#34;]

    @property
    def index_pruning_cycles_retention(self):
        &#34;&#34;&#34;Returns number of cycles to be maintained in index by index pruning for the backupset&#34;&#34;&#34;

        return self._properties[&#34;indexSettings&#34;][&#34;indexRetCycles&#34;]

    @index_pruning_type.setter
    def index_pruning_type(self, value):
        &#34;&#34;&#34;Updates the pruning type for the backupset when backupset level indexing is enabled.
        Can be days based pruning or cycles based pruning.
        Days based pruning will set index retention on the basis of days,
        cycles based pruning will set index retention on basis of cycles.

        Args:
            value    (str)  --  &#34;days_based&#34; or &#34;cycles_based&#34;

        &#34;&#34;&#34;

        if value.lower() == &#34;cycles_based&#34;:
            final_value = 1

        elif value.lower() == &#34;days_based&#34;:
            final_value = 2

        elif value.lower() == &#34;infinite&#34;:
            final_value = 0

        else:
            raise SDKException(&#39;Backupset&#39;, &#39;104&#39;)

        request_json = {
            &#34;backupsetProperties&#34;: {
                &#34;indexSettings&#34;: {
                    &#34;indexRetCycle&#34;: 0,
                    &#34;overrideIndexPruning&#34;: 1,
                    &#34;indexRetDays&#34;: 0,
                    &#34;isPruningEnabled&#34;: 1,
                    &#34;indexPruningType&#34;: final_value

                }
            }
        }

        self._process_update_reponse(request_json)

    @index_pruning_days_retention.setter
    def index_pruning_days_retention(self, value):
        &#34;&#34;&#34;Sets index pruning days value at backupset level for days-based index pruning&#34;&#34;&#34;

        if isinstance(value, int) and value &gt;= 2:
            request_json = {
                &#34;backupsetProperties&#34;: {
                    &#34;indexSettings&#34;: {
                        &#34;indexRetCycle&#34;: 0,
                        &#34;overrideIndexPruning&#34;: 1,
                        &#34;indexRetDays&#34;: value,
                        &#34;isPruningEnabled&#34;: 1,
                        &#34;indexPruningType&#34;: 2
                    }
                }
            }

            self._process_update_reponse(request_json)
        else:
            raise SDKException(&#39;Backupset&#39;, &#39;105&#39;)

    @index_pruning_cycles_retention.setter
    def index_pruning_cycles_retention(self, value):
        &#34;&#34;&#34;Sets index pruning cycles value at backupset level for cycles-based index pruning&#34;&#34;&#34;

        if isinstance(value, int) and value &gt;= 2:
            request_json = {
                &#34;backupsetProperties&#34;: {
                    &#34;indexSettings&#34;: {
                        &#34;indexRetCycle&#34;: value,
                        &#34;overrideIndexPruning&#34;: 1,
                        &#34;indexRetDays&#34;: 0,
                        &#34;isPruningEnabled&#34;: 1,
                        &#34;indexPruningType&#34;: 1
                    }
                }
            }

            self._process_update_reponse(request_json)
        else:
            raise SDKException(&#39;Backupset&#39;, &#39;105&#39;)


    def create_replica_copy(self, srcclientid, destclientid, scid, blrid,
                            srcguid, dstguid, **replication_options):

        &#34;&#34;&#34;&#34;setter for live  blklvl Replication replica copy...

        Args:
            srcclientid   (int)  --  Source client id.

            destclientid    (dict)  -- Destintion client id .

            scid           (int) --  Replication Subclient id

            blrid           (int) -- Blr pair id

            srcguid         (str) -- Browse guid of source

            dstguid          (str) -- Browse guid of destination volume

            **replication_options (dict) -- object instance


        &#34;&#34;&#34;
        srcvol = replication_options.get(&#39;srcvol&#39;)
        restorepath = replication_options.get(&#39;RestorePath&#39;)
        replicacopyjson = {
            &#34;taskInfo&#34;: {
                &#34;task&#34;: {
                    &#34;ownerId&#34;: 1,
                    &#34;taskType&#34;: 1,
                    &#34;ownerName&#34;: &#34;&#34;,
                    &#34;initiatedFrom&#34;: 1,
                    &#34;taskFlags&#34;: {
                        &#34;disabled&#34;: False
                    }
                },
                &#34;subTasks&#34;: [
                    {
                        &#34;subTaskOperation&#34;: 1,
                        &#34;subTask&#34;: {
                            &#34;subTaskType&#34;: 1,
                            &#34;operationType&#34;: 4047
                        },
                        &#34;options&#34;: {
                            &#34;backupOpts&#34;: {
                                &#34;mediaOpt&#34;: {
                                    &#34;auxcopyJobOption&#34;: {
                                        &#34;maxNumberOfStreams&#34;: 0,
                                        &#34;allCopies&#34;: True,
                                        &#34;useMaximumStreams&#34;: True,
                                        &#34;useScallableResourceManagement&#34;: False
                                    }
                                }
                            },
                            &#34;adminOpts&#34;: {
                                &#34;blockOperation&#34;: {
                                    &#34;operations&#34;: [
                                        {
                                            &#34;appId&#34;: int(scid),
                                            &#34;opType&#34;: 8,
                                            &#34;dstProxyClientId&#34;: int(destclientid),
                                            &#34;fsMountInfo&#34;: {
                                                &#34;doLiveMount&#34;: True,
                                                &#34;lifeTimeInSec&#34;: 7200,
                                                &#34;blrPairId&#34;: int(blrid),
                                                &#34;mountPathPairs&#34;: [
                                                    {
                                                        &#34;mountPath&#34;: restorepath,
                                                        &#34;srcPath&#34;: srcvol,
                                                        &#34;srcGuid&#34;: srcguid,
                                                        &#34;dstGuid&#34;: dstguid
                                                    }
                                                ]
                                            }
                                        }
                                    ]
                                }
                            },
                            &#34;commonOpts&#34;: {
                                &#34;subscriptionInfo&#34;: &#34;&lt;Api_Subscription subscriptionId =\&#34;116\&#34;/&gt;&#34;
                            }
                        }
                    }
                ]
            }
        }

        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._services[&#39;RESTORE&#39;],
                                                           replicacopyjson)
        if flag:
            if response.json():
                if &#34;jobIds&#34; in response.json():
                    return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])

                elif &#34;taskId&#34; in response.json():
                    return Schedules(self._commcell_object).get(task_id=response.json()[&#39;taskId&#39;])

                elif &#34;errorCode&#34; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]

                    o_str = &#39;Restore job failed\nError: &#34;{0}&#34;&#39;.format(error_message)
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
                else:
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Failed to run the restore job&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))


    def delete_replication_pair(self, blrid):
        &#34;&#34;&#34;&#34;Delete replication pair
        Args:
            blrid   (int)  --  blocklevel replication id.
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;DELETE&#39;, self._services[&#39;DELETE_BLR_PAIR&#39;]%blrid)

        if response.status_code != 200 and flag == False:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def get_mount_path_guid(self, volume):
        &#34;&#34;&#34;
        Gets the mount points for the BLR pairs
        Args:
            volume (str): volume name eg: &#34;E:&#34;
        &#34;&#34;&#34;
        volume_list = self.get_browse_volume_guid()
        for mount_path in volume_list[&#39;mountPathInfo&#39;]:
            if mount_path[&#39;accessPathList&#39;][0] == volume:
                return mount_path[&#39;guid&#39;]
        return &#39;&#39;

    def get_recovery_points(self, client_id, subclient_id):
        &#34;&#34;&#34; Get all recovery points for the BLR pair from the associated RPStore.
        These recovery points are those to which BLR pairs can failover/permanent mount to
        Args:
            client_id       (int): The ID of the source client machine
            subclient_id    (int): The ID of the subclient associated with the BLR pair

        Returns:
            List of dictionary of recovery points in the format: {&#39;timestamp&#39;: 12323, &#39;dataChangedSize&#39;: 1200,
            &#39;sequenceNumber&#39;: 898}
        &#34;&#34;&#34;
        client_name = [key for key, value in self._commcell_object.clients.all_clients.items()
                       if value[&#39;id&#39;] == client_id]
        if not client_name:
            raise SDKException(f&#39;Client not found with client id [{client_id}]&#39;)
        client = self._commcell_object.clients.get(client_name[0])
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._services[&#39;GRANULAR_BLR_POINTS&#39;]
                                                           %(client_id, subclient_id, client.client_guid))
        if not flag or response.status_code != 200:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
        if &#39;vmScale&#39; not in response.json():
            return []
        return response.json()[&#39;vmScale&#39;][&#39;restorePoints&#39;]


    def create_fsblr_replication_pair(self, srcclientid, destclientid, srcguid, destguid,
                                      rpstoreid=None, replicationtype=None, **replication_options):
        &#34;&#34;&#34;&#34;
        Create FSBLR continuous replication pair
        Args:
            srcclientid   (int)  --  Source client id

            destclientid   (dict)  -- Destintion client id

            srcguid        (str) -- Browse guid of source volume

            dstguid        (str) -- Browse guid of destination volume

            rpstoreid      (str) -- Rp store id for replication

            replicationtype (int) -- Replication pair  type to create (1 for live, 4 for granular pairs)

            **replication_options (dict) --
            {
                srcvol          (str): Source volume name
                destvol         (str): Destination volume name
                srcclient       (str): Source volume name
                srcclient       (str): Destination volume name
                rpstore         (int): RPStore ID,
                ccrp            (str): Time in minutes for crash consistent recovery point
                arcp            (str): Time in minutes for app consistent recovery point
            }


        &#34;&#34;&#34;
        srcvol = replication_options.get(&#39;srcvol&#39;)
        destvol = replication_options.get(&#39;destvol&#39;)
        destclient = replication_options.get(&#39;destclient&#39;)
        srcclient = replication_options.get(&#39;srcclient&#39;)
        rpstore = replication_options.get(&#39;rpstore&#39;)
        ccrp = replication_options.get(&#39;ccrp&#39;, &#34;120&#34;)
        acrp = replication_options.get(&#39;acrp&#39;, &#34;180&#34;)

        if replicationtype == 4:
            blr_options = f&#34;&lt;?xml version=&#39;1.0&#39; encoding=&#39;UTF-8&#39;?&gt;&lt;BlockReplication_BLRRecoveryOptions recoveryType=\&#34;4\&#34;&gt;&lt;granularV2 ccrpInterval=\&#34;{ccrp}\&#34; acrpInterval=\&#34;{acrp}\&#34; maxRpInterval=\&#34;21600\&#34; rpMergeDelay=\&#34;172800\&#34; rpRetention=\&#34;604800\&#34; maxRpStoreOfflineTime=\&#34;0\&#34; useOffPeakSchedule=\&#34;0\&#34; rpStoreId=\&#34;{rpstoreid}\&#34; rpStoreName=\&#34;{rpstore}\&#34;/&gt;&lt;/BlockReplication_BLRRecoveryOptions&gt;&#34;

        else:
            blr_options = &#34;&lt;?xml version=&#39;1.0&#39; encoding=&#39;UTF-8&#39;?&gt;&lt;BlockReplication_BLRRecoveryOptions recoveryType=\&#34;1\&#34;&gt;&lt;granularV2 ccrpInterval=\&#34;300\&#34; acrpInterval=\&#34;0\&#34; maxRpInterval=\&#34;21600\&#34; rpMergeDelay=\&#34;172800\&#34; rpRetention=\&#34;604800\&#34; maxRpStoreOfflineTime=\&#34;0\&#34; useOffPeakSchedule=\&#34;0\&#34;/&gt;&lt;/BlockReplication_BLRRecoveryOptions&gt;&#34;

        granularjson = {
            &#34;destEndPointType&#34;: 2,
            &#34;blrRecoveryOpts&#34;: blr_options,
            &#34;srcEndPointType&#34;: 2,
            &#34;srcDestVolumeMap&#34;: [
                {
                    &#34;sourceVolumeGUID&#34;: srcguid,
                    &#34;destVolume&#34;: destvol,
                    &#34;destVolumeGUID&#34;: destguid,
                    &#34;sourceVolume&#34;: srcvol
                }
            ],
            &#34;destEntity&#34;: {
                &#34;client&#34;: {
                    &#34;clientId&#34;: int(destclientid),
                    &#34;clientName&#34;: destclient
                }
            },
            &#34;sourceEntity&#34;: {
                &#34;client&#34;: {
                    &#34;clientId&#34;: int(srcclientid),
                    &#34;clientName&#34;: srcclient
                }
            }
        }
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._services[&#39;CREATE_BLR_PAIR&#39;], granularjson)

        if flag:
            if response and response.json():
                if response.json().get(&#39;errorCode&#39;, 0) != 0:
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)



    def create_granular_replica_copy(self, srcclientid, destclientid, scid, blrid, srcguid, dstguid, restoreguid,
                                     **replication_options):
        &#34;&#34;&#34;&#34;setter for granular blklvl Replication replica copy...

        Args:
            srcclientid   (int)  --  Source client id.

            destclientid    (dict)  -- Destintion client id .

            scid           (int) --  Replication Subclient id

            blrid           (int) -- Blr pair id

            srcguid         (str) -- source volume guid

            dstguid         (str) -- Destination relication guid

            restoreguid     (str) -- RP store guid

            timestamp        (int) -- Replication point timestamp

            **replication_options (dict) -- object instance


        &#34;&#34;&#34;

        replicapoints = self.get_recovery_points(destclientid, scid)
        timestamp = replication_options.get(&#39;timestamp&#39;)
        if timestamp:
            restore_point = [replica_point for replica_point in replicapoints
                             if int(replica_point[&#39;timeStamp&#39;]) == timestamp]
        else:
            restore_point = replicapoints[-1]

        srcvol = replication_options.get(&#39;srcvol&#39;)
        restorepath = replication_options.get(&#39;RestorePath&#39;)
        replicacopyjson = {
            &#34;taskInfo&#34;: {
                &#34;task&#34;: {
                    &#34;ownerId&#34;: 1,
                    &#34;taskType&#34;: 1,
                    &#34;ownerName&#34;: &#34;&#34;,
                    &#34;initiatedFrom&#34;: 1,
                    &#34;taskFlags&#34;: {
                        &#34;disabled&#34;: False
                    }
                },
                &#34;subTasks&#34;: [
                    {
                        &#34;subTaskOperation&#34;: 1,
                        &#34;subTask&#34;: {
                            &#34;subTaskType&#34;: 1,
                            &#34;operationType&#34;: 4047
                        },
                        &#34;options&#34;: {
                            &#34;backupOpts&#34;: {
                                &#34;mediaOpt&#34;: {
                                    &#34;auxcopyJobOption&#34;: {
                                        &#34;maxNumberOfStreams&#34;: 0,
                                        &#34;allCopies&#34;: True,
                                        &#34;useMaximumStreams&#34;: True,
                                        &#34;useScallableResourceManagement&#34;: False
                                    }
                                }
                            },
                            &#34;adminOpts&#34;: {
                                &#34;blockOperation&#34;: {
                                    &#34;operations&#34;: [
                                        {
                                            &#34;appId&#34;: int(scid),
                                            &#34;opType&#34;: 8,
                                            &#34;dstProxyClientId&#34;: int(destclientid),
                                            &#34;fsMountInfo&#34;: {
                                                &#34;doLiveMount&#34;: False,
                                                &#34;lifeTimeInSec&#34;: 7200,
                                                &#34;blrPairId&#34;: int(blrid),
                                                &#34;mountPathPairs&#34;: [
                                                    {
                                                        &#34;mountPath&#34;: restorepath,
                                                        &#34;srcPath&#34;: srcvol,
                                                        &#34;srcGuid&#34;: dstguid,
                                                        &#34;dstGuid&#34;: restoreguid
                                                    }
                                                ],
                                                &#34;rp&#34;: {
                                                    &#34;timeStamp&#34;: int(restore_point[&#39;timeStamp&#39;]),
                                                    &#34;sequenceNumber&#34;: int(restore_point[&#39;sequenceNumber&#39;]),
                                                    &#34;rpType&#34;: 1,
                                                    &#34;appConsistent&#34;: False,
                                                    &#34;dataChangedSize&#34;: int(restore_point[&#39;dataChangedSize&#39;])
                                                }
                                            }
                                        }
                                    ]
                                }
                            },
                            &#34;commonOpts&#34;: {
                                &#34;subscriptionInfo&#34;: &#34;&lt;Api_Subscription subscriptionId =\&#34;1451\&#34;/&gt;&#34;
                            }
                        }
                    }
                ]
            }
        }

        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._services[&#39;RESTORE&#39;], replicacopyjson)

        if flag:
            if response.json():
                if &#34;jobIds&#34; in response.json():
                    return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])

                elif &#34;taskId&#34; in response.json():
                    return Schedules(self._commcell_object).get(task_id=response.json()[&#39;taskId&#39;])

                elif &#34;errorCode&#34; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]

                    o_str = &#39;Restore job failed\nError: &#34;{0}&#34;&#39;.format(error_message)
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
                else:
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Failed to run the restore job&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))


    def get_browse_volume_guid(self):

        &#34;&#34;&#34;&#34;to get browse volume guids for client
            Returns:
                vguids (json) : Returns volume guids and properties

        &#34;&#34;&#34;
        client_id= self._client_object.client_id
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._services[&#39;BROWSE_MOUNT_POINTS&#39;]
                                                           %(client_id))
        if flag:
            if response and response.json():
                vguids = response.json()
                if response.json().get(&#39;errorCode&#39;, 0) != 0:
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)

        return vguids</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.backupsets.fsbackupset.FSBackupset"><code class="flex name class">
<span>class <span class="ident">FSBackupset</span></span>
<span>(</span><span>instance_object, backupset_name, backupset_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Derived class from Backupset Base class, representing a fs backupset,
and to perform operations on that backupset.</p>
<p>Initialise the backupset object.</p>
<h2 id="args">Args</h2>
<p>instance_object
(object)
&ndash;
instance of the Instance class</p>
<p>backupset_name
(str)
&ndash;
name of the backupset</p>
<p>backupset_id
(str)
&ndash;
id of the backupset
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Backupset class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/fsbackupset.py#L82-L1690" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class FSBackupset(Backupset):
    &#34;&#34;&#34;Derived class from Backupset Base class, representing a fs backupset,
        and to perform operations on that backupset.&#34;&#34;&#34;

    def restore_in_place(
            self,
            paths,
            overwrite=True,
            restore_data_and_acl=True,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            fs_options=None,
            restore_jobs=None,
            advanced_options=None
    ):
        &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the same location.

            Args:
                paths                   (list)  --  list of full paths of files/folders to restore

                overwrite               (bool)  --  unconditional overwrite files during restore
                    default: True

                restore_data_and_acl    (bool)  --  restore data and ACL files
                    default: True

                copy_precedence         (int)   --  copy precedence value of storage policy copy
                    default: None

                from_time           (str)       --  time to retore the contents after
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time           (str)         --  time to retore the contents before
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                fs_options      (dict)          -- dictionary that includes all advanced options

                    options:

                        all_versions        : if set to True restores all the versions of the
                                                specified file

                        versions            : list of version numbers to be backed up

                        validate_only       : To validate data backed up for restore

                        no_of_streams   (int)       -- Number of streams to be used for restore

                restore_jobs    (list)          --  list of jobs to be restored if the job is index free restore

                advanced_options    (dict)  -- Advanced restore options

                    Options:

                        job_description (str)   --  Restore job description

                        timezone        (str)   --  Timezone to be used for restore

                            **Note** make use of TIMEZONES dict in constants.py to pass timezone

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        self._instance_object._restore_association = self._backupset_association

        if fs_options is not None and fs_options.get(&#39;no_of_streams&#39;, 1) &gt; 1 and not fs_options.get(&#39;destination_appTypeId&#39;, False):
            fs_options[&#39;destination_appTypeId&#39;] = int(self._client_object.agents.all_agents.get(&#39;file system&#39;, self._client_object.agents.all_agents.get(&#39;windows file system&#39;, self._client_object.agents.all_agents.get(&#39;linux file system&#39;, self._client_object.agents.all_agents.get(&#39;big data apps&#39;, self._client_object.agents.all_agents.get(&#39;cloud apps&#39;, 0))))))
            if not fs_options[&#39;destination_appTypeId&#39;]:
                del fs_options[&#39;destination_appTypeId&#39;]

        return self._instance_object._restore_in_place(
            paths=paths,
            overwrite=overwrite,
            restore_data_and_acl=restore_data_and_acl,
            copy_precedence=copy_precedence,
            from_time=from_time,
            to_time=to_time,
            fs_options=fs_options,
            restore_jobs=restore_jobs,
            advanced_options=advanced_options
        )

    def restore_out_of_place(
            self,
            client,
            destination_path,
            paths,
            overwrite=True,
            restore_data_and_acl=True,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            fs_options=None,
            restore_jobs=None,
            advanced_options=None
    ):
        &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the input client,
            at the specified destionation location.

            Args:
                client                (str/object) --  either the name of the client or
                                                           the instance of the Client

                destination_path      (str)        --  full path of the restore location on client

                paths                 (list)       --  list of full paths of
                                                           files/folders to restore

                overwrite             (bool)       --  unconditional overwrite files during restore
                    default: True

                restore_data_and_acl  (bool)       --  restore data and ACL files
                    default: True

                copy_precedence         (int)      --  copy precedence value of storage policy copy
                    default: None

                from_time           (str)          --  time to retore the contents after
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time           (str)            --  time to retore the contents before
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                fs_options      (dict)             -- dictionary that includes all advanced options

                    options:

                        preserve_level      : preserve level option to set in restore

                        proxy_client        : proxy that needed to be used for restore

                        impersonate_user    : Impersonate user options for restore

                        impersonate_password: Impersonate password option for restore
                                                in base64 encoded form

                        all_versions        : if set to True restores all the versions of the
                                                specified file

                        versions            : list of version numbers to be backed up

                        validate_only       : To validate data backed up for restore

                        no_of_streams   (int)       -- Number of streams to be used for restore

                restore_jobs    (list)          --  list of jobs to be restored if the job is index free restore

                advanced_options    (dict)  -- Advanced restore options

                    Options:

                        job_description (str)   --  Restore job description

                        timezone        (str)   --  Timezone to be used for restore

                            **Note** make use of TIMEZONES dict in constants.py to pass timezone

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if client is not a string or Client instance

                    if destination_path is not a string

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        self._instance_object._restore_association = self._backupset_association

        if not isinstance(client, (str, Client)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if isinstance(client, str):
            client = Client(self._commcell_object, client)

        if fs_options is not None and fs_options.get(&#39;no_of_streams&#39;, 1) &gt; 1 and not fs_options.get(&#39;destination_appTypeId&#39;, False):
            fs_options[&#39;destination_appTypeId&#39;] = int(client.agents.all_agents.get(&#39;file system&#39;, client.agents.all_agents.get(&#39;windows file system&#39;, client.agents.all_agents.get(&#39;linux file system&#39;, client.agents.all_agents.get(&#39;big data apps&#39;, client.agents.all_agents.get(&#39;cloud apps&#39;, 0))))))
            if not fs_options[&#39;destination_appTypeId&#39;]:
                del fs_options[&#39;destination_appTypeId&#39;]

        return self._instance_object._restore_out_of_place(
            client=client,
            destination_path=destination_path,
            paths=paths,
            overwrite=overwrite,
            restore_data_and_acl=restore_data_and_acl,
            copy_precedence=copy_precedence,
            from_time=from_time,
            to_time=to_time,
            fs_options=fs_options,
            restore_jobs=restore_jobs,
            advanced_options=advanced_options
        )

    def find_all_versions(self, *args, **kwargs):
        &#34;&#34;&#34;Searches the content of a Subclient, and returns all versions available for the content.

            Args:
                Dictionary of browse options:
                    Example:
                        find_all_versions({
                            &#39;path&#39;: &#39;c:\\hello&#39;,
                            &#39;show_deleted&#39;: True,
                            &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,
                            &#39;to_time&#39;: &#39;2016-04-31 12:00:00&#39;
                        })

                    (OR)

                Keyword argument of browse options:
                    Example:
                        find_all_versions(
                            path=&#39;c:\\hello.txt&#39;,
                            show_deleted=True,
                            to_time=&#39;2016-04-31 12:00:00&#39;
                        )

                Refer self._default_browse_options for all the supported options

        Returns:
            dict    -   dictionary of the specified file with list of all the file versions and
                            additional metadata retrieved from browse

        &#34;&#34;&#34;
        if args and isinstance(args[0], dict):
            options = args[0]
        else:
            options = kwargs

        options[&#39;operation&#39;] = &#39;all_versions&#39;

        return self._do_browse(options)

    def _restore_bmr_admin_json(self, ipconfig, hwconfig):
        &#34;&#34;&#34;&#34;setter for the BMR options required  for 1-touch restore

        Args:
            ipconfig    (dict)  -- The IP Configuration details obtained form response file.

            hwconfig    (dict)  -- The hardware configuration details obtained form response file.

        Returns :
                    returns the JSON required for BMR
        &#34;&#34;&#34;
        bmr_restore_vmprov_json = {
            &#34;vmProvisioningOption&#34;: {
                &#34;operationType&#34;: 14, &#34;virtualMachineOption&#34;: [
                    {
                        &#34;powerOnVM&#34;: True, &#34;isoPath&#34;: &#34; &#34;, &#34;flags&#34;: 0,
                        &#34;useLinkedClone&#34;: False, &#34;vendor&#34;: 1,
                        &#34;doLinkedCloneFromLocalTemplateCopy&#34;: False,
                        &#34;oneTouchResponse&#34;: {
                            &#34;copyPrecedence&#34;: 0, &#34;version&#34;: &#34;10.0&#34;, &#34;platform&#34;: 1,
                            &#34;dateCreated&#34;: &#34;&#34;,
                            &#34;automationTest&#34;: False, &#34;autoReboot&#34;: True, &#34;clients&#34;: [
                                {
                                    &#34;platform&#34;: 1, &#34;isBlockLevelBackup&#34;: False,
                                    &#34;indexCachePath&#34;: &#34;&#34;,
                                    &#34;isClientMA&#34;: False, &#34;clone&#34;: False,
                                    &#34;isIndexCacheInUSB&#34;: True, &#34;firewallCS&#34;: &#34;&#34;,
                                    &#34;backupSet&#34;: {
                                        &#34;backupsetName&#34;: self.backupset_name,
                                        &#34;backupsetId&#34;: int(self.backupset_id)
                                    }, &#34;netconfig&#34;:{
                                        &#34;wins&#34;:{
                                            &#34;useDhcp&#34;: False
                                        },
                                        &#34;firewall&#34;: {
                                            &#34;certificatePath&#34;: &#34;&#34;, &#34;certificateBlob&#34;: &#34;&#34;,
                                            &#34;configBlob&#34;: &#34;&#34;
                                        }, &#34;hosts&#34;: [
                                            {
                                                &#34;fqdn&#34;: &#34;&#34;, &#34;alias&#34;: &#34;&#34;, &#34;ip&#34;: {}
                                            }
                                        ],
                                        &#34;dns&#34;:
                                        {
                                            &#34;suffix&#34;: &#34;&#34;, &#34;useDhcp&#34;: False
                                        }, &#34;ipinfo&#34;: ipconfig
                                    },
                                    &#34;platformConfig&#34;:
                                        {
                                            &#34;platformCfgBlob&#34;: &#34;&#34;, &#34;win_passPhrase&#34;: &#34;&#34;,
                                            &#34;win_licenceKey&#34;: &#34;&#34;, &#34;type&#34;: 1,
                                            &#34;goToMiniSetUp&#34;: 0, &#34;Win_DomainCreds&#34;:
                                            {
                                                &#34;isClientInDomain&#34;: True, &#34;DomainCreds&#34;:
                                                {
                                                    &#34;password&#34;: &#34;&#34;, &#34;domainName&#34;: &#34;#####&#34;,
                                                    &#34;confirmPassword&#34;: &#34;&#34;,
                                                    &#34;userName&#34;: &#34;&#34;
                                                }
                                            }
                                        },
                                    &#34;firewallLocal&#34;: {
                                        &#34;certificatePath&#34;: &#34;&#34;, &#34;certificateBlob&#34;: &#34;&#34;, &#34;configBlob&#34;: &#34;&#34;
                                    },
                                    &#34;client&#34;: {
                                        &#34;hostName&#34;: &#34; &#34;, &#34;clientName&#34;: &#34; &#34;,
                                        &#34;type&#34;: 0
                                    },
                                    &#34;indexPathCreds&#34;: {
                                        &#34;password&#34;: &#34;&#34;, &#34;domainName&#34;: &#34;&#34;, &#34;confirmPassword&#34;: &#34;&#34;,
                                        &#34;userName&#34;: &#34;&#34;
                                    }, &#34;newclient&#34;: {
                                        &#34;hostName&#34;: &#34;&#34;, &#34;clientName&#34;: &#34;&#34;
                                    }
                                }
                            ], &#34;csinfo&#34;: {
                                &#34;firewallPort&#34;: 0, &#34;cvdPort&#34;: 0, &#34;evmgrPort&#34;: 0,
                                &#34;fwClientGroupName&#34;: &#34;&#34;,
                                &#34;mediaAgentInfo&#34;: {

                                }, &#34;mediaAgentIP&#34;: {

                                }, &#34;ip&#34;: {
                                    &#34;address&#34;: &#34; &#34;
                                }, &#34;commservInfo&#34;: {
                                    &#34;hostName&#34;: &#34; &#34;, &#34;clientName&#34;: &#34;&#34;
                                }, &#34;creds&#34;: {
                                    &#34;password&#34;: &#34; &#34;,
                                    &#34;domainName&#34;: &#34;&#34;, &#34;confirmPassword&#34;: &#34;&#34;, &#34;userName&#34;: &#34;admin&#34;
                                }
                            }, &#34;hwconfig&#34;: hwconfig,
                            &#34;netconfig&#34;: {
                                &#34;wins&#34;: {
                                    &#34;useDhcp&#34;: False
                                }, &#34;firewall&#34;: {
                                    &#34;certificatePath&#34;: &#34;&#34;, &#34;certificateBlob&#34;: &#34;&#34;, &#34;configBlob&#34;: &#34;&#34;
                                }, &#34;dns&#34;: {
                                    &#34;suffix&#34;: &#34;&#34;, &#34;useDhcp&#34;: False
                                }, &#34;ipinfo&#34;: {
                                    &#34;defaultgw&#34;: &#34;&#34;
                                }
                            }, &#34;dataBrowseTime&#34;: {

                            }, &#34;maInfo&#34;:
                                {
                                    &#34;clientName&#34;: &#34;&#34;
                                }, &#34;datastoreList&#34;: {}
                        }, &#34;vmInfo&#34;: {
                            &#34;registerWithFailoverCluster&#34;: False, &#34;proxyClient&#34;: {
                                &#34;clientName&#34;: &#34; &#34;
                            }, &#34;vmLocation&#34;: {
                                &#34;pathName&#34;: &#34; &#34;, &#34;inventoryPath&#34;: &#34;&#34;,
                                &#34;hostName&#34;: &#34; &#34;, &#34;resourcePoolPath&#34;: &#34;&#34;, &#34;dataCenterName&#34;: &#34;&#34;,
                                &#34;vCenter&#34;: &#34; &#34;, &#34;datastore&#34;: {
                                    &#34;name&#34;: &#34; &#34;
                                    },
                                &#34;instanceEntity&#34;: {
                                    &#34;clientName&#34;: &#34;&#34;,
                                    &#34;instanceName&#34;: &#34;&#34;,
                                    &#34;instanceId&#34;: 0
                                }
                                }, &#34;expirationTime&#34;: {}
                        }
                    }
                ]
            }
        }
        return bmr_restore_vmprov_json

    def _restore_bmr_virtualserveropts_json(self):
        &#34;&#34;&#34;Get the JSON for virtual server options


        Returns :
                    The virtualserver options JSON required for Virtualize Me restores

        &#34;&#34;&#34;
        bmr_restore_json = {
            &#34;diskLevelVMRestoreOption&#34;: {
                &#34;esxServerName&#34;: &#34; &#34;, &#34;userPassword&#34;: {

                }
            }
        }
        return bmr_restore_json

    def _restore_bmr_firewallopts_json(self, hostname, direction, port):
        &#34;&#34;&#34;Get the JSON for firewall configuration options

        Args:
            hostname    (String)   -- The hostname of the machine.

            direction   (Integer)  -- The direction of the connection.

            port        (Integer)  -- The port at which the machine will communicate.

        Returns :
                    The firewall configuration options JSON required for Virtualize Me restores

        &#34;&#34;&#34;
        bmr_firewall_restore_json = {
            &#34;direction&#34;: direction,
            &#34;connectionInfoList&#34;: [
                {
                    &#34;hostname&#34;: hostname,
                    &#34;port&#34;: port
                }
            ]
        }
        return bmr_firewall_restore_json

    def _azure_advancedrestoreopts_json(self):
        &#34;&#34;&#34;Get the JSON for Advanced restore options for azure


        Returns :
                    The Advanced restore options JSON required for Virtualize Me to Azure restores

        &#34;&#34;&#34;
        azure_adv_rest_opts_json = [
            {
                &#34;vmSize&#34;: &#34;&#34;,
                &#34;securityGroups&#34;: [
                    {
                        &#34;groupName&#34;: &#34;--Auto Select--&#34;,
                        &#34;groupId&#34;: &#34;&#34;
                    }
                ]
            }
        ]
        return azure_adv_rest_opts_json

    def _azure_advancedopts_json(self):
        &#34;&#34;&#34;Get the JSON for Advanced restore options for azure


        Returns :
                    The Advanced restore options JSON required for Virtualize Me to Azure restores

        &#34;&#34;&#34;
        azure_adv_opts_json = {
            &#34;networkCards&#34;: [
                {
                    &#34;privateIPAddress&#34;: &#34;&#34;,
                    &#34;networkName&#34;: &#34;&#34;,
                    &#34;label&#34;: &#34;--Auto Select--&#34;,
                    &#34;subnetNames&#34;: [
                        {
                            &#34;subnetId&#34;: &#34;&#34;
                        }
                    ]
                }
            ]
        }
        return azure_adv_opts_json


    def _get_responsefile(self):
        &#34;&#34;&#34;Get the response file for the backupset

        Returns :
            (dict, dict) - The hardware and IP configuration details from the response file obtained

        &#34;&#34;&#34;
        request = {
            &#34;CVGui_GetResponseFilesReq&#34;: {
                &#34;entity&#34;: {
                    &#34;_type_&#34;: &#34;6&#34;,
                    &#34;appName&#34;: &#34;File System&#34;,
                    &#34;applicationId&#34;: self._agent_object.agent_id,
                    &#34;backupsetId&#34;: self.backupset_id,
                    &#34;backupsetName&#34;: self.backupset_name,
                    &#34;clientId&#34;: self._agent_object._client_object.client_id,
                    &#34;clientName&#34;: self._agent_object._client_object.client_name,
                    &#34;commCellId&#34;: &#34;0&#34;,
                    &#34;commCellName&#34;: &#34;&#34;,
                    &#34;instanceId&#34;: &#34;1&#34;,
                    &#34;instanceName&#34;: &#34;&#34;
                },
                &#34;RecoveryTime&#34;: &#34;&#34;,
                &#34;platform&#34;: &#34;1&#34;,
                &#34;virtualizeME&#34;: &#34;1&#34;
            }
        }

        response = self._commcell_object._qoperation_execute(request)

        hwconfig = response[&#39;responseFile&#39;][&#39;hwconfig&#39;]
        ipconfig = response[&#39;responseFile&#39;][&#39;clients&#39;][0][&#39;netconfig&#39;][&#39;ipinfo&#39;]
        cs_user = response[&#39;responseFile&#39;][&#39;csinfo&#39;][&#39;creds&#39;][&#39;userName&#39;]
        cs_pwd = response[&#39;responseFile&#39;][&#39;csinfo&#39;][&#39;creds&#39;][&#39;password&#39;]
        cs_token = response[&#39;responseFile&#39;][&#39;csinfo&#39;][&#39;creds&#39;][&#39;password&#39;][5:]
        return hwconfig, ipconfig,cs_user,cs_pwd,cs_token

    def run_bmr_restore(self, **restore_options):
        &#34;&#34;&#34;
        Calling the create task API with the final restore JSON

        Args :
                IsoPath                 (String)    : The location of ISO in the datastore

                CommServIP              (String)    : The IP of the CS

                CommServHostname        (String)    : The hostname of he CS

                CommServUsername        (String)    : The username for the Comcell

                CommServPassword        (String)    : The password for the comcell

                Datastore               (String)    : The ESX store in which the VM is provisioned

                VcenterServerName       (String)    : The Vcenter to be used

                ClientHostName          (String)    : The hostname of the client being virtualized.

                VmName                  (String)    : The name with which the VM is provisioned.

                VirtualizationClient    (String)    : The vmware virualization client

                EsxServer               (String)    : The ESX server name

               NetworkLabel             (String)    : The network label to be assigned to the VM.

               HyperVHost               (String)    : The Hyper-V host

               GuestUser                (String)    : The Username of the guest OS

               GuestPassword            (String)    : The Password of the guest OS

               CloneClientName          (String)    : The clone client name

        Returns :
                    returns the task object

        &#34;&#34;&#34;
        client_name = self._agent_object._client_object.client_name

        self._instance_object._restore_association = self._backupset_association

        hwconfig, ipconfig, cs_username, cs_password = self._get_responsefile()
        response_json = self._restore_json(paths=[&#39;&#39;])

        restore_json_system_state = self._restore_bmr_admin_json(ipconfig, hwconfig)
        restore_json_virtualserver = self._restore_bmr_virtualserveropts_json()

        #Checking for Firewall rules
        if restore_options.get(&#34;FirewallClientGroup&#34;, &#34;&#34;).strip():
            fwconfigtocs = self._restore_bmr_firewallopts_json(restore_options.get(&#34;FirewallHostname&#34;),
                                                               restore_options.get(&#34;FirewallDirection&#34;),
                                                               restore_options.get(&#34;FirewallPort&#34;))
            restore_json_system_state[&#39;vmProvisioningOption&#39;][&#39;virtualMachineOption&#39;][0][
                &#39;oneTouchResponse&#39;][&#39;clients&#39;][0][&#39;fwconfigtocs&#39;] = fwconfigtocs
            restore_json_system_state[&#39;vmProvisioningOption&#39;][&#39;virtualMachineOption&#39;][0][&#39;oneTouchResponse&#39;][&#39;csinfo&#39;][
                &#39;fwClientGroupName&#39;] = restore_options.get(&#34;FirewallClientGroup&#34;)

        #Get instance Id of the virtual client
        virtual_client_object = self._commcell_object.clients.get(restore_options.get(&#39;VirtualizationClient&#39;))
        virtual_agent_object = virtual_client_object.agents.get(&#39;Virtual Server&#39;)
        instances_list = virtual_agent_object.instances._instances
        instance_id = int(list(instances_list.values())[0])
        instance_name = list(instances_list.keys())[0]

        response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][
            &#39;adminOpts&#39;] = restore_json_system_state

        response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][
            &#39;virtualServerRstOption&#39;] = restore_json_virtualserver

        response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;destination&#39;][
            &#39;inPlace&#39;] = False
        response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;subTask&#39;][&#39;subTaskType&#39;] = 1
        response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;subTask&#39;][&#39;operationType&#39;] = 4041

        vm_option = response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][
            &#39;vmProvisioningOption&#39;][&#39;virtualMachineOption&#39;][0]

        vm_option[&#39;vmInfo&#39;][&#39;vmLocation&#39;][&#39;instanceEntity&#39;][&#39;clientName&#39;] = restore_options.get(&#39;VirtualizationClient&#39;)
        vm_option[&#39;vmInfo&#39;][&#39;vmLocation&#39;][&#39;instanceEntity&#39;][&#39;instanceId&#39;] = instance_id

        if(response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][
            &#39;vmProvisioningOption&#39;][&#39;virtualMachineOption&#39;][0][&#39;oneTouchResponse&#39;][
            &#39;hwconfig&#39;][&#39;mem_size&#39;]) &lt; 4096:
            vm_option[&#39;oneTouchResponse&#39;][&#39;hwconfig&#39;][&#39;mem_size&#39;] = 4096

        if restore_options.get(&#39;CommServIP&#39;):
            cs_ip = restore_options.get(&#39;CommServIP&#39;)
        else:
            try:
                cs_ip = socket.gethostbyname(self._commcell_object.commserv_hostname)

            except Exception as e:
                raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, &#39;Error while reading CommServer IP : {}\n. Please set the CommServIP argument.&#39;.format(e))

        vm_option[&#39;oneTouchResponse&#39;][&#39;csinfo&#39;][&#39;ip&#39;][
            &#39;address&#39;] = cs_ip

        vm_option[&#39;oneTouchResponse&#39;][&#39;csinfo&#39;][&#39;commservInfo&#39;][
            &#39;clientName&#39;] = self._commcell_object.commserv_name
        vm_option[&#39;oneTouchResponse&#39;][&#39;csinfo&#39;][&#39;commservInfo&#39;][
            &#39;hostName&#39;] = self._commcell_object.commserv_hostname

        vm_option[&#39;oneTouchResponse&#39;][&#39;csinfo&#39;][&#39;creds&#39;][
            &#39;password&#39;] = cs_password

        vm_option[&#39;oneTouchResponse&#39;][&#39;csinfo&#39;][&#39;creds&#39;][
            &#39;userName&#39;] = cs_username

        vm_option[&#39;oneTouchResponse&#39;][&#39;hwconfig&#39;][&#39;vmName&#39;] = restore_options.get(&#39;VmName&#39;, None)

        vm_option[&#39;oneTouchResponse&#39;][&#39;hwconfig&#39;][&#39;overwriteVm&#39;] = True

        vm_option[&#39;oneTouchResponse&#39;][&#39;clients&#39;][0][&#39;client&#39;][
            &#39;hostName&#39;] = restore_options.get(&#39;ClientHostname&#39;, None)

        vm_option[&#39;oneTouchResponse&#39;][&#39;clients&#39;][0][&#39;client&#39;][
            &#39;clientName&#39;] = restore_options.get(&#39;ClientName&#39;, None)

        if instance_name == &#39;vmware&#39; or instance_name == &#39;hyper-v&#39;:

            vm_option[&#39;isoPath&#39;] = restore_options.get(&#39;IsoPath&#39;)

            vm_option[&#39;vmInfo&#39;][&#39;vmLocation&#39;][&#39;pathName&#39;] = restore_options.get(&#39;IsoPath&#39;, None)

            vm_option[&#39;oneTouchResponse&#39;][&#39;clients&#39;][0][&#39;netconfig&#39;][&#39;ipinfo&#39;][&#39;interfaces&#39;][0][
                &#39;protocols&#39;][0][&#39;useDhcp&#39;] = True

            vm_option[&#39;oneTouchResponse&#39;][&#39;clients&#39;][0][&#39;netconfig&#39;][&#39;ipinfo&#39;][&#39;interfaces&#39;][0][
                &#39;networkLabel&#39;] = restore_options.get(&#39;NetworkLabel&#39;, None)

            if &#39;scsi_disks&#39; in vm_option[&#39;oneTouchResponse&#39;][&#39;hwconfig&#39;]:
                vm_option[&#39;oneTouchResponse&#39;][&#39;hwconfig&#39;][&#39;scsi_disks&#39;][0][
                    &#39;dataStoreName&#39;] = restore_options.get(&#39;Datastore&#39;, None)

            if &#39;ide_disks&#39; in vm_option[&#39;oneTouchResponse&#39;][&#39;hwconfig&#39;]:
                vm_option[&#39;oneTouchResponse&#39;][&#39;hwconfig&#39;][&#39;ide_disks&#39;][0][
                    &#39;dataStoreName&#39;] = restore_options.get(&#39;Datastore&#39;, None)

            vm_option[&#39;vmInfo&#39;][&#39;vmLocation&#39;][&#39;datastore&#39;][
                &#39;name&#39;] = restore_options.get(&#39;Datastore&#39;, None)

            if instance_name == &#39;vmware&#39;:

                vm_option[&#39;vmInfo&#39;][&#39;proxyClient&#39;][
                    &#39;clientName&#39;] = restore_options.get(&#39;VirtualizationClient&#39;, None)

                response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][
                    &#39;restoreOptions&#39;][&#39;virtualServerRstOption&#39;][&#39;diskLevelVMRestoreOption&#39;][
                        &#39;esxServerName&#39;] = restore_options.get(&#39;VcenterServerName&#39;, None)

                response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;destination&#39;][
                    &#39;destClient&#39;][&#39;clientName&#39;] = restore_options.get(&#39;VirtualizationClient&#39;, None)

                vm_option[&#39;vmInfo&#39;][&#39;vmLocation&#39;][&#39;hostName&#39;] = restore_options.get(
                    &#39;EsxServer&#39;)

                vm_option[&#39;vmInfo&#39;][&#39;vmLocation&#39;][&#39;vCenter&#39;] = restore_options.get(&#39;VcenterServerName&#39;)

            if instance_name == &#39;hyper-v&#39;:

                if restore_options.get(&#39;OsType&#39;) == &#39;UNIX&#39;:

                    vm_option[&#39;vendor&#39;] = &#39;MICROSOFT&#39;

                response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][
                    &#39;restoreOptions&#39;][&#39;virtualServerRstOption&#39;][&#39;diskLevelVMRestoreOption&#39;][
                        &#39;esxServerName&#39;] = restore_options.get(&#39;VirtualizationClient&#39;, None)

                response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;destination&#39;][
                    &#39;destClient&#39;][&#39;clientName&#39;] = restore_options.get(&#39;VirtualizationClient&#39;, None)

                vm_option[&#39;vmInfo&#39;][&#39;vmLocation&#39;][&#39;hostName&#39;] = restore_options.get(
                    &#39;HyperVHost&#39;)

                vm_option[&#39;vmInfo&#39;][&#39;vmLocation&#39;][&#39;vCenter&#39;] = restore_options.get(&#39;VirtualizationClient&#39;)

        if &#39;azure&#39; in instance_name:

            az_advanced_ops_json = self._azure_advancedopts_json()

            az_adv_restore_opts_json = self._azure_advancedrestoreopts_json()

            vm_option[&#39;vendor&#39;] = 7

            vm_option[&#39;createPublicIp&#39;] = restore_options.get(&#39;CreatePublicIP&#39;)

            vm_option[&#39;oneTouchResponse&#39;][&#39;clients&#39;][0][&#39;isBlockLevelBackup&#39;] = True

            vm_option[&#39;vmInfo&#39;][&#39;vmLocation&#39;][&#39;advancedProperties&#39;] = az_advanced_ops_json

            vm_option[&#39;vmInfo&#39;][&#39;vmLocation&#39;][&#39;advancedProperties&#39;][&#39;networkCards&#39;][0][&#39;label&#39;] = &#34;--Auto Select--&#34;
            vm_option[&#39;vmInfo&#39;][&#39;vmLocation&#39;][&#39;hostName&#39;] = restore_options.get(&#39;ResourceGroup&#39;)

            response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][
                &#39;restoreOptions&#39;][&#39;virtualServerRstOption&#39;][&#39;diskLevelVMRestoreOption&#39;][
                &#39;advancedRestoreOptions&#39;] = az_adv_restore_opts_json

            response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][
                &#39;restoreOptions&#39;][&#39;virtualServerRstOption&#39;][&#39;diskLevelVMRestoreOption&#39;][
                &#39;advancedRestoreOptions&#39;][0][&#39;securityGroups&#39;][0][&#39;groupName&#39;] = &#34;--Auto Select--&#34;

            response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;destination&#39;][
                &#39;destClient&#39;][&#39;clientName&#39;] = restore_options.get(&#39;VirtualizationClient&#39;, None)

            if &#39;scsi_disks&#39; in vm_option[&#39;oneTouchResponse&#39;][&#39;hwconfig&#39;]:
                vm_option[&#39;oneTouchResponse&#39;][&#39;hwconfig&#39;][&#39;scsi_disks&#39;][0][
                    &#39;dataStoreName&#39;] = restore_options.get(&#39;StorageAccount&#39;, None)

            if &#39;ide_disks&#39; in vm_option[&#39;oneTouchResponse&#39;][&#39;hwconfig&#39;]:
                vm_option[&#39;oneTouchResponse&#39;][&#39;hwconfig&#39;][&#39;ide_disks&#39;][0][
                    &#39;dataStoreName&#39;] = restore_options.get(&#39;StorageAccount&#39;, None)

            vm_option[&#39;vmInfo&#39;][&#39;vmLocation&#39;][&#39;datastore&#39;][
                &#39;name&#39;] = restore_options.get(&#39;StorageAccount&#39;, None)

        if instance_name == &#39;azure stack&#39;:

            vm_option[&#39;vmInfo&#39;][&#39;vmLocation&#39;][&#39;vCenter&#39;] = restore_options.get(&#39;ManagementURL&#39;, None)

            vm_option[&#39;vendor&#39;] = 17

    # Additional options

        if restore_options.get(&#39;CloneClientName&#39;):
            vm_option[&#39;oneTouchResponse&#39;][&#39;clients&#39;][0][&#39;clone&#39;] = True
            vm_option[&#39;oneTouchResponse&#39;][&#39;clients&#39;][0][&#39;newclient&#39;][
                &#39;clientName&#39;] = restore_options.get(&#39;CloneClientName&#39;, None)
            vm_option[&#39;oneTouchResponse&#39;][&#39;clients&#39;][0][&#39;newclient&#39;][
                &#39;hostName&#39;] = restore_options.get(&#39;CloneClientName&#39;, None)

        if restore_options.get(&#39;OsType&#39;) == &#39;UNIX&#39;:
            vm_option[&#39;oneTouchResponse&#39;][&#39;clients&#39;][0][&#39;newclient&#39;][
                &#39;hostName&#39;] = &#39;&#39;

        if restore_options.get(&#39;UseDhcp&#39;):
            vm_option[&#39;oneTouchResponse&#39;][&#39;clients&#39;][0][&#39;netconfig&#39;][&#39;ipinfo&#39;][&#39;interfaces&#39;][0][
                &#39;protocols&#39;][0][&#39;useDhcp&#39;] = True


        return self._process_restore_response(response_json)

    def _get_cs_login_details(self):
        &#34;&#34;&#34;Get the cs login information.

        Returns :
            (dict, dict) - CS login details

        &#34;&#34;&#34;
        request = {
            &#34;CVGui_GetResponseFilesReq&#34;: {
                &#34;entity&#34;: {
                    &#34;_type_&#34;: &#34;6&#34;,
                    &#34;appName&#34;: &#34;File System&#34;,
                    &#34;applicationId&#34;: self._agent_object.agent_id,
                    &#34;backupsetId&#34;: self.backupset_id,
                    &#34;backupsetName&#34;: self.backupset_name,
                    &#34;clientId&#34;: self._agent_object._client_object.client_id,
                    &#34;clientName&#34;: self._agent_object._client_object.client_name,
                    &#34;commCellId&#34;: &#34;0&#34;,
                    &#34;commCellName&#34;: &#34;&#34;,
                    &#34;instanceId&#34;: &#34;1&#34;,
                    &#34;instanceName&#34;: &#34;&#34;
                },
                &#34;RecoveryTime&#34;: &#34;&#34;,
                &#34;platform&#34;: &#34;1&#34;,
                &#34;virtualizeME&#34;: &#34;1&#34;
            }
        }

        response = self._commcell_object._qoperation_execute(request)
        cs_user = self._commcell_object.commcell_username
        cs_pwd = self._commcell_object.auth_token
        cs_token = self._commcell_object.auth_token[5:]
        cs_name = self._commcell_object.commserv_name
        cs_hostname = self._commcell_object.commserv_hostname
        cs_ip_address = socket.gethostbyname(self._commcell_object.commserv_hostname)

        return cs_name, cs_hostname, cs_ip_address, cs_user, cs_pwd, cs_token

    def _restore_aix_1touch_admin_json(self):
        &#34;&#34;&#34;&#34;setter for the BMR options required  for 1-touch restore

                Returns :
                            returns the JSON required for BMR
                &#34;&#34;&#34;

        bmr_restore_aix1touch_json = {

            &#34;restoreFromBackupBeforeDate&#34;: False, &#34;recoverAllVolumeGroups&#34;: True,
            &#34;automaticClientReboot&#34;: True, &#34;preserveExistingVolumeGroups&#34;: False, &#34;responseData&#34;:
                [
                    {
                        &#34;copyPrecedence&#34;: 0, &#34;version&#34;: &#34;&#34;, &#34;platform&#34;: 0, &#34;dateCreated&#34;: &#34;&#34;,
                        &#34;automationTest&#34;: False, &#34;autoReboot&#34;: True, &#34;clients&#34;: [
                            {
                                &#34;platform&#34;: 0, &#34;isBlockLevelBackup&#34;: False, &#34;indexCachePath&#34;: &#34;&#34;, &#34;isClientMA&#34;: False,
                                &#34;clone&#34;: True, &#34;isIndexCacheInUSB&#34;: True, &#34;firewallCS&#34;: &#34;&#34;, &#34;backupSet&#34;:
                                {
                                    &#34;_type_&#34;: 6
                                }, &#34;netconfig&#34;:
                                {
                                    &#34;wins&#34;:
                                    {
                                        &#34;useDhcp&#34;: False
                                    },
                                    &#34;firewall&#34;:
                                        {
                                            &#34;certificatePath&#34;: &#34;&#34;, &#34;certificateBlob&#34;: &#34;&#34;, &#34;configBlob&#34;: &#34;&#34;
                                        },
                                    &#34;hosts&#34;: [
                                        {
                                            &#34;fqdn&#34;: &#34;&#34;, &#34;alias&#34;: &#34;&#34;,
                                            &#34;ip&#34;: {}
                                        }
                                    ], &#34;dns&#34;: {
                                        &#34;suffix&#34;: &#34;&#34;, &#34;useDhcp&#34;: False, &#34;nameservers&#34;:
                                        [
                                            {
                                                &#34;address&#34;: &#34;&#34;, &#34;family&#34;: 32
                                            }
                                        ]
                                    }, &#34;ipinfo&#34;: {
                                        &#34;defaultgw&#34;: &#34;&#34;, &#34;interfaces&#34;:
                                            [
                                                {
                                                    &#34;adapter&#34;: 0, &#34;networkLabel&#34;: &#34;&#34;, &#34;vlan&#34;: 0,
                                                    &#34;macAddressType&#34;: 0,
                                                    &#34;isEnabled&#34;: True, &#34;name&#34;: &#34;&#34;, &#34;mac&#34;: &#34;&#34;,
                                                    &#34;classicname&#34;: &#34;&#34;, &#34;wins&#34;:
                                                    {
                                                        &#34;useDhcp&#34;: False
                                                    }, &#34;dns&#34;:{
                                                        &#34;suffix&#34;: &#34;&#34;, &#34;useDhcp&#34;: False
                                                    }, &#34;protocols&#34;:[
                                                        {
                                                            &#34;gw&#34;: &#34;&#34;, &#34;subnetId&#34;: &#34;&#34;, &#34;netmask&#34;: &#34;&#34;,
                                                            &#34;networkAddress&#34;: &#34;&#34;, &#34;useDhcp&#34;: False,
                                                            &#34;ip&#34;:
                                                                {
                                                                    &#34;address&#34;: &#34;&#34;
                                                                }
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    }, &#34;platformConfig&#34;: {
                                        &#34;platformCfgBlob&#34;: &#34;&#34;, &#34;win_passPhrase&#34;: &#34;&#34;, &#34;win_licenceKey&#34;: &#34;&#34;, &#34;type&#34;: 0,
                                        &#34;goToMiniSetUp&#34;: 0, &#34;Win_DomainCreds&#34;: {
                                            &#34;isClientInDomain&#34;: False, &#34;DomainCreds&#34;:{
                                                &#34;password&#34;: &#34;&#34;, &#34;domainName&#34;: &#34;&#34;, &#34;confirmPassword&#34;: &#34;&#34;, &#34;userName&#34;: &#34;&#34;
                                            }
                                        }
                                    }, &#34;firewallLocal&#34;: {
                                        &#34;certificatePath&#34;: &#34;&#34;, &#34;certificateBlob&#34;: &#34;&#34;, &#34;configBlob&#34;: &#34;&#34;
                                    }, &#34;client&#34;: {},
                                &#34;indexPathCreds&#34;: {
                                    &#34;password&#34;: &#34;&#34;, &#34;domainName&#34;: &#34;&#34;, &#34;confirmPassword&#34;: &#34;&#34;, &#34;userName&#34;: &#34;&#34;
                                }, &#34;newclient&#34;: {
                                    &#34;hostName&#34;: &#34;&#34;, &#34;clientName&#34;: &#34;&#34;
                                }
                            }
                        ], &#34;csinfo&#34;: {
                            &#34;firewallPort&#34;: 0, &#34;cvdPort&#34;: 0, &#34;evmgrPort&#34;: 0, &#34;fwClientGroupName&#34;: &#34;&#34;,
                            &#34;mediaAgentInfo&#34;: {
                                &#34;_type_&#34;: 3
                            }, &#34;mediaAgentIP&#34;: {
                            }, &#34;ip&#34;: {
                                &#34;address&#34;: &#34;&#34;
                            }, &#34;commservInfo&#34;: {
                                &#34;hostName&#34;: &#34;&#34;, &#34;clientName&#34;: &#34;&#34;
                            }, &#34;creds&#34;: {
                                &#34;password&#34;: &#34;&#34;, &#34;domainName&#34;: &#34;&#34;,
                                &#34;confirmPassword&#34;: &#34;&#34;, &#34;userName&#34;: &#34;&#34;, &#34;token&#34;:&#34;&#34;
                            }
                        }, &#34;hwconfig&#34;: {
                            &#34;minMemoryMB&#34;: 0, &#34;vmName&#34;: &#34;&#34;, &#34;magicno&#34;: &#34;&#34;, &#34;enableDynamicMemory&#34;: False,
                            &#34;bootFirmware&#34;: 0, &#34;version&#34;: &#34;&#34;, &#34;mem_size&#34;: 0, &#34;cpu_count&#34;: 1, &#34;maxMemoryMB&#34;: 0,
                            &#34;nic_count&#34;: 1, &#34;overwriteVm&#34;: False, &#34;useMtptSelection&#34;: False, &#34;ide_count&#34;: 0,
                            &#34;mtpt_count&#34;: 0, &#34;scsi_count&#34;: 0, &#34;diskType&#34;: 1, &#34;optimizeStorage&#34;: False, &#34;systemDisk&#34;: {
                                &#34;forceProvision&#34;: False, &#34;bus&#34;: 0, &#34;refcnt&#34;: 0, &#34;size&#34;: 0, &#34;scsiControllerType&#34;: 0,
                                &#34;name&#34;: &#34;&#34;, &#34;dataStoreName&#34;: &#34;&#34;, &#34;vm_disk_type&#34;: 0, &#34;slot&#34;: 0, &#34;diskType&#34;: 1,
                                &#34;tx_type&#34;: 0
                            }
                        }, &#34;netconfig&#34;: {
                            &#34;wins&#34;: {
                                &#34;useDhcp&#34;: False
                            }, &#34;firewall&#34;: {
                                &#34;certificatePath&#34;: &#34;&#34;, &#34;certificateBlob&#34;: &#34;&#34;, &#34;configBlob&#34;: &#34;&#34;
                            }, &#34;dns&#34;: {
                                &#34;suffix&#34;: &#34;&#34;, &#34;useDhcp&#34;: False
                            }, &#34;ipinfo&#34;: {
                                &#34;defaultgw&#34;: &#34;&#34;
                            }
                        }, &#34;dataBrowseTime&#34;: {
                            &#34;_type_&#34;: 55
                        }, &#34;maInfo&#34;: {
                            &#34;clientName&#34;: &#34;&#34;
                        }, &#34;datastoreList&#34;: {
                        }
                    }
                ]
        }
        return bmr_restore_aix1touch_json

    def run_bmr_aix_restore(self, **restore_options):
        &#34;&#34;&#34;
                Calling the create task API with the final restore JSON

                Args :


                        Clone Clinet Name  (String)     : Clone machine name

                        Clone Hostname  (String)        :Clone machine host name

                        DNS Suffix      (String)        :Dns suffix name

                        DNS IP  (Integer)                :Ip of Dns Address

                        Clone IP    (Integer)            :Clone Machine IP

                        Clone Netmask (Integer)          :Clone Machine NetMask

                        Clone Gateway (Integer)          :Clone Machine Gateway

                        Auto Reboot   (Boolean)          :Client machine Auto reboot(True or False)

                        Clone          (Boolean)         :Is Clone enabled(True or False)

                        CS_Username   (String)    : The username for the Comcell

                        CS_Password   (String)     : The password for the comcell

                Returns :
                            returns the task object

                &#34;&#34;&#34;
        self._instance_object._restore_association = self._backupset_association
        request_json = self._restore_json(paths=[&#39;&#39;])
        restore_json_aix_system_state = self._restore_aix_1touch_admin_json()
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][
            &#39;oneTouchRestoreOption&#39;] = restore_json_aix_system_state
        hwconfig_aix = restore_json_aix_system_state[&#39;responseData&#39;][0][&#39;hwconfig&#39;]
        ipconfig_aix = restore_json_aix_system_state[&#39;responseData&#39;][0][&#39;clients&#39;][0][&#39;netconfig&#39;][&#39;ipinfo&#39;]
        cs_name, cs_hostname, cs_ip_address, cs_user, cs_pwd, cs_token = self._get_cs_login_details()
        vmjson = self._restore_bmr_admin_json(ipconfig_aix, hwconfig_aix)
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;] = vmjson
        is_clone = restore_options.get(&#39;clone&#39;, None)
        subtask_json = {
            &#39;subTaskType&#39; : 3,
            &#39;operationType&#39;: 1006
        }
        common_options = {
            &#39;systemStateBackup&#39; : True,
            &#39;copyToObjectStore&#39; :False,
            &#39;restoreToDisk&#39; : False,
            &#39;skipIfExists&#39; : restore_options.get(&#39;run_FS_restore&#39;, False),
            &#39;SyncRestore&#39; : False
        }
        response_data = {
                &#34;clients&#34;: [{
                    &#34;clone&#34;: is_clone,
                    &#34;netconfig&#34;: {
                        &#34;dns&#34;: {
                            &#34;suffix&#34;: restore_options.get(&#39;dns_suffix&#39;, None),
                            &#34;nameservers&#34;: [{
                                &#34;address&#34;: restore_options.get(&#39;dns_ip&#39;, None),
                            }]
                        },
                        &#34;ipinfo&#34;: {
                            &#34;interfaces&#34;: [{
                                &#34;protocols&#34;: [{
                                    &#34;gw&#34;: restore_options.get(&#39;clone_machine_gateway&#39;, None),
                                    &#34;netmask&#34;: restore_options.get(&#39;clone_machine_netmask&#39;, None),
                                    &#34;ip&#34;: {
                                        &#34;address&#34;: restore_options.get(&#39;clone_ip_address&#39;, None)
                                    }
                                }]
                            }]
                        }
                    },
                    &#34;newclient&#34;: {
                        &#34;hostName&#34;: restore_options.get(&#39;clone_client_hostname&#39;, None),
                        &#34;clientName&#34;: restore_options.get(&#39;clone_client_name&#39;, None)
                    }
                }],
                &#34;csinfo&#34;: {
                    &#34;ip&#34;: {
                        &#34;address&#34;: cs_ip_address
                    },
                    &#34;commservInfo&#34;: {
                        &#34;hostName&#34;: cs_hostname,
                        &#34;clientName&#34;: cs_name
                    },
                    &#34;creds&#34;: {
                        &#34;password&#34;: cs_pwd,
                        &#34;confirmPassword&#34;: cs_pwd,
                        &#34;userName&#34;: cs_user,
                        &#34;token&#34;:  cs_token
                    }
                },
            }
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;subTask&#39;] = subtask_json
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;commonOptions&#39;] = common_options
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;destination&#39;][&#39;destPath&#39;] = (
            [restore_options.get(&#39;onetouch_server_directory&#39;, &#39;&#39;)])
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;destination&#39;][
            &#39;destClient&#39;][&#39;clientName&#39;] = restore_options.get(&#39;onetouch_server&#39;, None)
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][
            &#39;oneTouchRestoreOption&#39;][&#39;automaticClientReboot&#39;] = restore_options.get(&#39;automaticClientReboot&#39;, None)

        if is_clone:
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;oneTouchRestoreOption&#39;][
                &#39;responseData&#39;][0][&#39;clients&#39;][0][&#39;netconfig&#39;][
                    &#39;dns&#39;][&#39;nameservers&#39;][0][&#39;address&#39;] = restore_options.get(&#39;dns_ip&#39;, None)
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;oneTouchRestoreOption&#39;][&#39;responseData&#39;][
            0] = response_data
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;oneTouchRestoreOption&#39;][
            &#39;restoreFromBackupBeforeDate&#39;] = True if restore_options.get(&#39;restoreFromBackupBeforeDate&#39;) else False
        if restore_options.get(&#39;onetouch_backup_jobid&#39;) is not None:
            _job = self._commcell_object.job_controller.get(restore_options.get(&#39;onetouch_backup_jobid&#39;))
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;browseOption&#34;][
                &#34;timeRange&#34;][&#34;toTime&#34;] = _job.end_timestamp
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;browseOption&#34;][
                &#34;timeRange&#34;][&#34;fromTime&#34;] = _job.start_timestamp

            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;vmProvisioningOption&#39;][
                &#39;virtualMachineOption&#39;][0][&#39;oneTouchResponse&#39;][&#39;dataBrowseTime&#39;][
                &#39;TimeZoneName&#39;] = self._commcell_object.commserv_timezone
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;vmProvisioningOption&#39;][
                &#39;virtualMachineOption&#39;][0][&#39;oneTouchResponse&#39;][&#39;dataBrowseTime&#39;][&#39;time&#39;] = _job.end_timestamp

            one_touch_option = {&#39;fromTime&#39;: 0, &#39;toTime&#39;: _job.end_timestamp}
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;oneTouchOption&#39;] = one_touch_option
            request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;fileOption&#39;][
                &#39;sourceItem&#39;] = ([f&#39;2:{_job.job_id}&#39;])
        return self._process_restore_response(request_json)

    @property
    def index_server(self):
        &#34;&#34;&#34;Returns the index server client set for the backupset&#34;&#34;&#34;

        client_name = None

        if &#39;indexSettings&#39; in self._properties:
            if &#39;currentIndexServer&#39; in self._properties[&#39;indexSettings&#39;]:
                client_name = self._properties[&#39;indexSettings&#39;][&#39;currentIndexServer&#39;][&#39;clientName&#39;]

        if client_name is not None:
            return Client(self._commcell_object, client_name=client_name)

        return None

    @index_server.setter
    def index_server(self, value):
        &#34;&#34;&#34;Sets index server client for the backupset. Property value should be a client object

            Args:
                value   (object)    --  The cvpysdk client object of the index server client

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;

        if not isinstance(value, Client):
            raise SDKException(&#39;Backupset&#39;, &#39;106&#39;)

        properties = self._properties
        index_server_id = int(value.client_id)
        index_server_name = value.client_name

        if &#39;indexSettings&#39; in properties:
            qualified_index_servers = []
            if &#39;qualifyingIndexServers&#39; in properties[&#39;indexSettings&#39;]:
                for index_server in properties[&#39;indexSettings&#39;][&#39;qualifyingIndexServers&#39;]:
                    qualified_index_servers.append(index_server[&#39;clientId&#39;])

            if index_server_id in qualified_index_servers:
                properties[&#39;indexSettings&#39;][&#39;currentIndexServer&#39;] = {
                    &#39;clientId&#39;: index_server_id,
                    &#39;clientName&#39;: index_server_name
                }
            else:
                raise SDKException(
                    &#39;Backupset&#39;, &#39;102&#39;, &#39;{0} is not a qualified IndexServer client&#39;.format(
                        index_server_name))
        else:
            properties[&#39;indexSettings&#39;] = {
                &#39;currentIndexServer&#39;: {
                    &#39;clientId&#39;: index_server_id,
                    &#39;clientName&#39;: index_server_name
                }
            }

        request_json = {
            &#39;backupsetProperties&#39;: properties
        }

        self._process_update_reponse(request_json)

    @property
    def index_pruning_type(self):
        &#34;&#34;&#34;Returns index pruning type for the backupset&#34;&#34;&#34;
        return self._properties[&#34;indexSettings&#34;][&#34;indexPruningType&#34;]

    @property
    def index_pruning_days_retention(self):
        &#34;&#34;&#34;Returns number of days to be maintained in index by index pruning for the backupset&#34;&#34;&#34;

        return self._properties[&#34;indexSettings&#34;][&#34;indexRetDays&#34;]

    @property
    def index_pruning_cycles_retention(self):
        &#34;&#34;&#34;Returns number of cycles to be maintained in index by index pruning for the backupset&#34;&#34;&#34;

        return self._properties[&#34;indexSettings&#34;][&#34;indexRetCycles&#34;]

    @index_pruning_type.setter
    def index_pruning_type(self, value):
        &#34;&#34;&#34;Updates the pruning type for the backupset when backupset level indexing is enabled.
        Can be days based pruning or cycles based pruning.
        Days based pruning will set index retention on the basis of days,
        cycles based pruning will set index retention on basis of cycles.

        Args:
            value    (str)  --  &#34;days_based&#34; or &#34;cycles_based&#34;

        &#34;&#34;&#34;

        if value.lower() == &#34;cycles_based&#34;:
            final_value = 1

        elif value.lower() == &#34;days_based&#34;:
            final_value = 2

        elif value.lower() == &#34;infinite&#34;:
            final_value = 0

        else:
            raise SDKException(&#39;Backupset&#39;, &#39;104&#39;)

        request_json = {
            &#34;backupsetProperties&#34;: {
                &#34;indexSettings&#34;: {
                    &#34;indexRetCycle&#34;: 0,
                    &#34;overrideIndexPruning&#34;: 1,
                    &#34;indexRetDays&#34;: 0,
                    &#34;isPruningEnabled&#34;: 1,
                    &#34;indexPruningType&#34;: final_value

                }
            }
        }

        self._process_update_reponse(request_json)

    @index_pruning_days_retention.setter
    def index_pruning_days_retention(self, value):
        &#34;&#34;&#34;Sets index pruning days value at backupset level for days-based index pruning&#34;&#34;&#34;

        if isinstance(value, int) and value &gt;= 2:
            request_json = {
                &#34;backupsetProperties&#34;: {
                    &#34;indexSettings&#34;: {
                        &#34;indexRetCycle&#34;: 0,
                        &#34;overrideIndexPruning&#34;: 1,
                        &#34;indexRetDays&#34;: value,
                        &#34;isPruningEnabled&#34;: 1,
                        &#34;indexPruningType&#34;: 2
                    }
                }
            }

            self._process_update_reponse(request_json)
        else:
            raise SDKException(&#39;Backupset&#39;, &#39;105&#39;)

    @index_pruning_cycles_retention.setter
    def index_pruning_cycles_retention(self, value):
        &#34;&#34;&#34;Sets index pruning cycles value at backupset level for cycles-based index pruning&#34;&#34;&#34;

        if isinstance(value, int) and value &gt;= 2:
            request_json = {
                &#34;backupsetProperties&#34;: {
                    &#34;indexSettings&#34;: {
                        &#34;indexRetCycle&#34;: value,
                        &#34;overrideIndexPruning&#34;: 1,
                        &#34;indexRetDays&#34;: 0,
                        &#34;isPruningEnabled&#34;: 1,
                        &#34;indexPruningType&#34;: 1
                    }
                }
            }

            self._process_update_reponse(request_json)
        else:
            raise SDKException(&#39;Backupset&#39;, &#39;105&#39;)


    def create_replica_copy(self, srcclientid, destclientid, scid, blrid,
                            srcguid, dstguid, **replication_options):

        &#34;&#34;&#34;&#34;setter for live  blklvl Replication replica copy...

        Args:
            srcclientid   (int)  --  Source client id.

            destclientid    (dict)  -- Destintion client id .

            scid           (int) --  Replication Subclient id

            blrid           (int) -- Blr pair id

            srcguid         (str) -- Browse guid of source

            dstguid          (str) -- Browse guid of destination volume

            **replication_options (dict) -- object instance


        &#34;&#34;&#34;
        srcvol = replication_options.get(&#39;srcvol&#39;)
        restorepath = replication_options.get(&#39;RestorePath&#39;)
        replicacopyjson = {
            &#34;taskInfo&#34;: {
                &#34;task&#34;: {
                    &#34;ownerId&#34;: 1,
                    &#34;taskType&#34;: 1,
                    &#34;ownerName&#34;: &#34;&#34;,
                    &#34;initiatedFrom&#34;: 1,
                    &#34;taskFlags&#34;: {
                        &#34;disabled&#34;: False
                    }
                },
                &#34;subTasks&#34;: [
                    {
                        &#34;subTaskOperation&#34;: 1,
                        &#34;subTask&#34;: {
                            &#34;subTaskType&#34;: 1,
                            &#34;operationType&#34;: 4047
                        },
                        &#34;options&#34;: {
                            &#34;backupOpts&#34;: {
                                &#34;mediaOpt&#34;: {
                                    &#34;auxcopyJobOption&#34;: {
                                        &#34;maxNumberOfStreams&#34;: 0,
                                        &#34;allCopies&#34;: True,
                                        &#34;useMaximumStreams&#34;: True,
                                        &#34;useScallableResourceManagement&#34;: False
                                    }
                                }
                            },
                            &#34;adminOpts&#34;: {
                                &#34;blockOperation&#34;: {
                                    &#34;operations&#34;: [
                                        {
                                            &#34;appId&#34;: int(scid),
                                            &#34;opType&#34;: 8,
                                            &#34;dstProxyClientId&#34;: int(destclientid),
                                            &#34;fsMountInfo&#34;: {
                                                &#34;doLiveMount&#34;: True,
                                                &#34;lifeTimeInSec&#34;: 7200,
                                                &#34;blrPairId&#34;: int(blrid),
                                                &#34;mountPathPairs&#34;: [
                                                    {
                                                        &#34;mountPath&#34;: restorepath,
                                                        &#34;srcPath&#34;: srcvol,
                                                        &#34;srcGuid&#34;: srcguid,
                                                        &#34;dstGuid&#34;: dstguid
                                                    }
                                                ]
                                            }
                                        }
                                    ]
                                }
                            },
                            &#34;commonOpts&#34;: {
                                &#34;subscriptionInfo&#34;: &#34;&lt;Api_Subscription subscriptionId =\&#34;116\&#34;/&gt;&#34;
                            }
                        }
                    }
                ]
            }
        }

        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._services[&#39;RESTORE&#39;],
                                                           replicacopyjson)
        if flag:
            if response.json():
                if &#34;jobIds&#34; in response.json():
                    return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])

                elif &#34;taskId&#34; in response.json():
                    return Schedules(self._commcell_object).get(task_id=response.json()[&#39;taskId&#39;])

                elif &#34;errorCode&#34; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]

                    o_str = &#39;Restore job failed\nError: &#34;{0}&#34;&#39;.format(error_message)
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
                else:
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Failed to run the restore job&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))


    def delete_replication_pair(self, blrid):
        &#34;&#34;&#34;&#34;Delete replication pair
        Args:
            blrid   (int)  --  blocklevel replication id.
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;DELETE&#39;, self._services[&#39;DELETE_BLR_PAIR&#39;]%blrid)

        if response.status_code != 200 and flag == False:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def get_mount_path_guid(self, volume):
        &#34;&#34;&#34;
        Gets the mount points for the BLR pairs
        Args:
            volume (str): volume name eg: &#34;E:&#34;
        &#34;&#34;&#34;
        volume_list = self.get_browse_volume_guid()
        for mount_path in volume_list[&#39;mountPathInfo&#39;]:
            if mount_path[&#39;accessPathList&#39;][0] == volume:
                return mount_path[&#39;guid&#39;]
        return &#39;&#39;

    def get_recovery_points(self, client_id, subclient_id):
        &#34;&#34;&#34; Get all recovery points for the BLR pair from the associated RPStore.
        These recovery points are those to which BLR pairs can failover/permanent mount to
        Args:
            client_id       (int): The ID of the source client machine
            subclient_id    (int): The ID of the subclient associated with the BLR pair

        Returns:
            List of dictionary of recovery points in the format: {&#39;timestamp&#39;: 12323, &#39;dataChangedSize&#39;: 1200,
            &#39;sequenceNumber&#39;: 898}
        &#34;&#34;&#34;
        client_name = [key for key, value in self._commcell_object.clients.all_clients.items()
                       if value[&#39;id&#39;] == client_id]
        if not client_name:
            raise SDKException(f&#39;Client not found with client id [{client_id}]&#39;)
        client = self._commcell_object.clients.get(client_name[0])
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._services[&#39;GRANULAR_BLR_POINTS&#39;]
                                                           %(client_id, subclient_id, client.client_guid))
        if not flag or response.status_code != 200:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
        if &#39;vmScale&#39; not in response.json():
            return []
        return response.json()[&#39;vmScale&#39;][&#39;restorePoints&#39;]


    def create_fsblr_replication_pair(self, srcclientid, destclientid, srcguid, destguid,
                                      rpstoreid=None, replicationtype=None, **replication_options):
        &#34;&#34;&#34;&#34;
        Create FSBLR continuous replication pair
        Args:
            srcclientid   (int)  --  Source client id

            destclientid   (dict)  -- Destintion client id

            srcguid        (str) -- Browse guid of source volume

            dstguid        (str) -- Browse guid of destination volume

            rpstoreid      (str) -- Rp store id for replication

            replicationtype (int) -- Replication pair  type to create (1 for live, 4 for granular pairs)

            **replication_options (dict) --
            {
                srcvol          (str): Source volume name
                destvol         (str): Destination volume name
                srcclient       (str): Source volume name
                srcclient       (str): Destination volume name
                rpstore         (int): RPStore ID,
                ccrp            (str): Time in minutes for crash consistent recovery point
                arcp            (str): Time in minutes for app consistent recovery point
            }


        &#34;&#34;&#34;
        srcvol = replication_options.get(&#39;srcvol&#39;)
        destvol = replication_options.get(&#39;destvol&#39;)
        destclient = replication_options.get(&#39;destclient&#39;)
        srcclient = replication_options.get(&#39;srcclient&#39;)
        rpstore = replication_options.get(&#39;rpstore&#39;)
        ccrp = replication_options.get(&#39;ccrp&#39;, &#34;120&#34;)
        acrp = replication_options.get(&#39;acrp&#39;, &#34;180&#34;)

        if replicationtype == 4:
            blr_options = f&#34;&lt;?xml version=&#39;1.0&#39; encoding=&#39;UTF-8&#39;?&gt;&lt;BlockReplication_BLRRecoveryOptions recoveryType=\&#34;4\&#34;&gt;&lt;granularV2 ccrpInterval=\&#34;{ccrp}\&#34; acrpInterval=\&#34;{acrp}\&#34; maxRpInterval=\&#34;21600\&#34; rpMergeDelay=\&#34;172800\&#34; rpRetention=\&#34;604800\&#34; maxRpStoreOfflineTime=\&#34;0\&#34; useOffPeakSchedule=\&#34;0\&#34; rpStoreId=\&#34;{rpstoreid}\&#34; rpStoreName=\&#34;{rpstore}\&#34;/&gt;&lt;/BlockReplication_BLRRecoveryOptions&gt;&#34;

        else:
            blr_options = &#34;&lt;?xml version=&#39;1.0&#39; encoding=&#39;UTF-8&#39;?&gt;&lt;BlockReplication_BLRRecoveryOptions recoveryType=\&#34;1\&#34;&gt;&lt;granularV2 ccrpInterval=\&#34;300\&#34; acrpInterval=\&#34;0\&#34; maxRpInterval=\&#34;21600\&#34; rpMergeDelay=\&#34;172800\&#34; rpRetention=\&#34;604800\&#34; maxRpStoreOfflineTime=\&#34;0\&#34; useOffPeakSchedule=\&#34;0\&#34;/&gt;&lt;/BlockReplication_BLRRecoveryOptions&gt;&#34;

        granularjson = {
            &#34;destEndPointType&#34;: 2,
            &#34;blrRecoveryOpts&#34;: blr_options,
            &#34;srcEndPointType&#34;: 2,
            &#34;srcDestVolumeMap&#34;: [
                {
                    &#34;sourceVolumeGUID&#34;: srcguid,
                    &#34;destVolume&#34;: destvol,
                    &#34;destVolumeGUID&#34;: destguid,
                    &#34;sourceVolume&#34;: srcvol
                }
            ],
            &#34;destEntity&#34;: {
                &#34;client&#34;: {
                    &#34;clientId&#34;: int(destclientid),
                    &#34;clientName&#34;: destclient
                }
            },
            &#34;sourceEntity&#34;: {
                &#34;client&#34;: {
                    &#34;clientId&#34;: int(srcclientid),
                    &#34;clientName&#34;: srcclient
                }
            }
        }
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._services[&#39;CREATE_BLR_PAIR&#39;], granularjson)

        if flag:
            if response and response.json():
                if response.json().get(&#39;errorCode&#39;, 0) != 0:
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)



    def create_granular_replica_copy(self, srcclientid, destclientid, scid, blrid, srcguid, dstguid, restoreguid,
                                     **replication_options):
        &#34;&#34;&#34;&#34;setter for granular blklvl Replication replica copy...

        Args:
            srcclientid   (int)  --  Source client id.

            destclientid    (dict)  -- Destintion client id .

            scid           (int) --  Replication Subclient id

            blrid           (int) -- Blr pair id

            srcguid         (str) -- source volume guid

            dstguid         (str) -- Destination relication guid

            restoreguid     (str) -- RP store guid

            timestamp        (int) -- Replication point timestamp

            **replication_options (dict) -- object instance


        &#34;&#34;&#34;

        replicapoints = self.get_recovery_points(destclientid, scid)
        timestamp = replication_options.get(&#39;timestamp&#39;)
        if timestamp:
            restore_point = [replica_point for replica_point in replicapoints
                             if int(replica_point[&#39;timeStamp&#39;]) == timestamp]
        else:
            restore_point = replicapoints[-1]

        srcvol = replication_options.get(&#39;srcvol&#39;)
        restorepath = replication_options.get(&#39;RestorePath&#39;)
        replicacopyjson = {
            &#34;taskInfo&#34;: {
                &#34;task&#34;: {
                    &#34;ownerId&#34;: 1,
                    &#34;taskType&#34;: 1,
                    &#34;ownerName&#34;: &#34;&#34;,
                    &#34;initiatedFrom&#34;: 1,
                    &#34;taskFlags&#34;: {
                        &#34;disabled&#34;: False
                    }
                },
                &#34;subTasks&#34;: [
                    {
                        &#34;subTaskOperation&#34;: 1,
                        &#34;subTask&#34;: {
                            &#34;subTaskType&#34;: 1,
                            &#34;operationType&#34;: 4047
                        },
                        &#34;options&#34;: {
                            &#34;backupOpts&#34;: {
                                &#34;mediaOpt&#34;: {
                                    &#34;auxcopyJobOption&#34;: {
                                        &#34;maxNumberOfStreams&#34;: 0,
                                        &#34;allCopies&#34;: True,
                                        &#34;useMaximumStreams&#34;: True,
                                        &#34;useScallableResourceManagement&#34;: False
                                    }
                                }
                            },
                            &#34;adminOpts&#34;: {
                                &#34;blockOperation&#34;: {
                                    &#34;operations&#34;: [
                                        {
                                            &#34;appId&#34;: int(scid),
                                            &#34;opType&#34;: 8,
                                            &#34;dstProxyClientId&#34;: int(destclientid),
                                            &#34;fsMountInfo&#34;: {
                                                &#34;doLiveMount&#34;: False,
                                                &#34;lifeTimeInSec&#34;: 7200,
                                                &#34;blrPairId&#34;: int(blrid),
                                                &#34;mountPathPairs&#34;: [
                                                    {
                                                        &#34;mountPath&#34;: restorepath,
                                                        &#34;srcPath&#34;: srcvol,
                                                        &#34;srcGuid&#34;: dstguid,
                                                        &#34;dstGuid&#34;: restoreguid
                                                    }
                                                ],
                                                &#34;rp&#34;: {
                                                    &#34;timeStamp&#34;: int(restore_point[&#39;timeStamp&#39;]),
                                                    &#34;sequenceNumber&#34;: int(restore_point[&#39;sequenceNumber&#39;]),
                                                    &#34;rpType&#34;: 1,
                                                    &#34;appConsistent&#34;: False,
                                                    &#34;dataChangedSize&#34;: int(restore_point[&#39;dataChangedSize&#39;])
                                                }
                                            }
                                        }
                                    ]
                                }
                            },
                            &#34;commonOpts&#34;: {
                                &#34;subscriptionInfo&#34;: &#34;&lt;Api_Subscription subscriptionId =\&#34;1451\&#34;/&gt;&#34;
                            }
                        }
                    }
                ]
            }
        }

        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._services[&#39;RESTORE&#39;], replicacopyjson)

        if flag:
            if response.json():
                if &#34;jobIds&#34; in response.json():
                    return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])

                elif &#34;taskId&#34; in response.json():
                    return Schedules(self._commcell_object).get(task_id=response.json()[&#39;taskId&#39;])

                elif &#34;errorCode&#34; in response.json():
                    error_message = response.json()[&#39;errorMessage&#39;]

                    o_str = &#39;Restore job failed\nError: &#34;{0}&#34;&#39;.format(error_message)
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
                else:
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Failed to run the restore job&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))


    def get_browse_volume_guid(self):

        &#34;&#34;&#34;&#34;to get browse volume guids for client
            Returns:
                vguids (json) : Returns volume guids and properties

        &#34;&#34;&#34;
        client_id= self._client_object.client_id
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._services[&#39;BROWSE_MOUNT_POINTS&#39;]
                                                           %(client_id))
        if flag:
            if response and response.json():
                vguids = response.json()
                if response.json().get(&#39;errorCode&#39;, 0) != 0:
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;)

        return vguids</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.backupset.Backupset" href="../backupset.html#cvpysdk.backupset.Backupset">Backupset</a></li>
</ul>
<h3>Subclasses</h3>
<ul class="hlist">
<li><a title="cvpysdk.backupsets.nasbackupset.NASBackupset" href="nasbackupset.html#cvpysdk.backupsets.nasbackupset.NASBackupset">NASBackupset</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.backupsets.fsbackupset.FSBackupset.index_pruning_cycles_retention"><code class="name">var <span class="ident">index_pruning_cycles_retention</span></code></dt>
<dd>
<div class="desc"><p>Returns number of cycles to be maintained in index by index pruning for the backupset</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/fsbackupset.py#L1216-L1220" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def index_pruning_cycles_retention(self):
    &#34;&#34;&#34;Returns number of cycles to be maintained in index by index pruning for the backupset&#34;&#34;&#34;

    return self._properties[&#34;indexSettings&#34;][&#34;indexRetCycles&#34;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupsets.fsbackupset.FSBackupset.index_pruning_days_retention"><code class="name">var <span class="ident">index_pruning_days_retention</span></code></dt>
<dd>
<div class="desc"><p>Returns number of days to be maintained in index by index pruning for the backupset</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/fsbackupset.py#L1210-L1214" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def index_pruning_days_retention(self):
    &#34;&#34;&#34;Returns number of days to be maintained in index by index pruning for the backupset&#34;&#34;&#34;

    return self._properties[&#34;indexSettings&#34;][&#34;indexRetDays&#34;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupsets.fsbackupset.FSBackupset.index_pruning_type"><code class="name">var <span class="ident">index_pruning_type</span></code></dt>
<dd>
<div class="desc"><p>Returns index pruning type for the backupset</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/fsbackupset.py#L1205-L1208" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def index_pruning_type(self):
    &#34;&#34;&#34;Returns index pruning type for the backupset&#34;&#34;&#34;
    return self._properties[&#34;indexSettings&#34;][&#34;indexPruningType&#34;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupsets.fsbackupset.FSBackupset.index_server"><code class="name">var <span class="ident">index_server</span></code></dt>
<dd>
<div class="desc"><p>Returns the index server client set for the backupset</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/fsbackupset.py#L1139-L1152" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def index_server(self):
    &#34;&#34;&#34;Returns the index server client set for the backupset&#34;&#34;&#34;

    client_name = None

    if &#39;indexSettings&#39; in self._properties:
        if &#39;currentIndexServer&#39; in self._properties[&#39;indexSettings&#39;]:
            client_name = self._properties[&#39;indexSettings&#39;][&#39;currentIndexServer&#39;][&#39;clientName&#39;]

    if client_name is not None:
        return Client(self._commcell_object, client_name=client_name)

    return None</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.backupsets.fsbackupset.FSBackupset.create_fsblr_replication_pair"><code class="name flex">
<span>def <span class="ident">create_fsblr_replication_pair</span></span>(<span>self, srcclientid, destclientid, srcguid, destguid, rpstoreid=None, replicationtype=None, **replication_options)</span>
</code></dt>
<dd>
<div class="desc"><p>"
Create FSBLR continuous replication pair</p>
<h2 id="args">Args</h2>
<p>srcclientid
(int)
&ndash;
Source client id</p>
<p>destclientid
(dict)
&ndash; Destintion client id</p>
<p>srcguid
(str) &ndash; Browse guid of source volume</p>
<p>dstguid
(str) &ndash; Browse guid of destination volume</p>
<p>rpstoreid
(str) &ndash; Rp store id for replication</p>
<p>replicationtype (int) &ndash; Replication pair
type to create (1 for live, 4 for granular pairs)</p>
<p>**replication_options (dict) &ndash;
{
srcvol
(str): Source volume name
destvol
(str): Destination volume name
srcclient
(str): Source volume name
srcclient
(str): Destination volume name
rpstore
(int): RPStore ID,
ccrp
(str): Time in minutes for crash consistent recovery point
arcp
(str): Time in minutes for app consistent recovery point
}</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/fsbackupset.py#L1460-L1538" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def create_fsblr_replication_pair(self, srcclientid, destclientid, srcguid, destguid,
                                  rpstoreid=None, replicationtype=None, **replication_options):
    &#34;&#34;&#34;&#34;
    Create FSBLR continuous replication pair
    Args:
        srcclientid   (int)  --  Source client id

        destclientid   (dict)  -- Destintion client id

        srcguid        (str) -- Browse guid of source volume

        dstguid        (str) -- Browse guid of destination volume

        rpstoreid      (str) -- Rp store id for replication

        replicationtype (int) -- Replication pair  type to create (1 for live, 4 for granular pairs)

        **replication_options (dict) --
        {
            srcvol          (str): Source volume name
            destvol         (str): Destination volume name
            srcclient       (str): Source volume name
            srcclient       (str): Destination volume name
            rpstore         (int): RPStore ID,
            ccrp            (str): Time in minutes for crash consistent recovery point
            arcp            (str): Time in minutes for app consistent recovery point
        }


    &#34;&#34;&#34;
    srcvol = replication_options.get(&#39;srcvol&#39;)
    destvol = replication_options.get(&#39;destvol&#39;)
    destclient = replication_options.get(&#39;destclient&#39;)
    srcclient = replication_options.get(&#39;srcclient&#39;)
    rpstore = replication_options.get(&#39;rpstore&#39;)
    ccrp = replication_options.get(&#39;ccrp&#39;, &#34;120&#34;)
    acrp = replication_options.get(&#39;acrp&#39;, &#34;180&#34;)

    if replicationtype == 4:
        blr_options = f&#34;&lt;?xml version=&#39;1.0&#39; encoding=&#39;UTF-8&#39;?&gt;&lt;BlockReplication_BLRRecoveryOptions recoveryType=\&#34;4\&#34;&gt;&lt;granularV2 ccrpInterval=\&#34;{ccrp}\&#34; acrpInterval=\&#34;{acrp}\&#34; maxRpInterval=\&#34;21600\&#34; rpMergeDelay=\&#34;172800\&#34; rpRetention=\&#34;604800\&#34; maxRpStoreOfflineTime=\&#34;0\&#34; useOffPeakSchedule=\&#34;0\&#34; rpStoreId=\&#34;{rpstoreid}\&#34; rpStoreName=\&#34;{rpstore}\&#34;/&gt;&lt;/BlockReplication_BLRRecoveryOptions&gt;&#34;

    else:
        blr_options = &#34;&lt;?xml version=&#39;1.0&#39; encoding=&#39;UTF-8&#39;?&gt;&lt;BlockReplication_BLRRecoveryOptions recoveryType=\&#34;1\&#34;&gt;&lt;granularV2 ccrpInterval=\&#34;300\&#34; acrpInterval=\&#34;0\&#34; maxRpInterval=\&#34;21600\&#34; rpMergeDelay=\&#34;172800\&#34; rpRetention=\&#34;604800\&#34; maxRpStoreOfflineTime=\&#34;0\&#34; useOffPeakSchedule=\&#34;0\&#34;/&gt;&lt;/BlockReplication_BLRRecoveryOptions&gt;&#34;

    granularjson = {
        &#34;destEndPointType&#34;: 2,
        &#34;blrRecoveryOpts&#34;: blr_options,
        &#34;srcEndPointType&#34;: 2,
        &#34;srcDestVolumeMap&#34;: [
            {
                &#34;sourceVolumeGUID&#34;: srcguid,
                &#34;destVolume&#34;: destvol,
                &#34;destVolumeGUID&#34;: destguid,
                &#34;sourceVolume&#34;: srcvol
            }
        ],
        &#34;destEntity&#34;: {
            &#34;client&#34;: {
                &#34;clientId&#34;: int(destclientid),
                &#34;clientName&#34;: destclient
            }
        },
        &#34;sourceEntity&#34;: {
            &#34;client&#34;: {
                &#34;clientId&#34;: int(srcclientid),
                &#34;clientName&#34;: srcclient
            }
        }
    }
    flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._services[&#39;CREATE_BLR_PAIR&#39;], granularjson)

    if flag:
        if response and response.json():
            if response.json().get(&#39;errorCode&#39;, 0) != 0:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupsets.fsbackupset.FSBackupset.create_granular_replica_copy"><code class="name flex">
<span>def <span class="ident">create_granular_replica_copy</span></span>(<span>self, srcclientid, destclientid, scid, blrid, srcguid, dstguid, restoreguid, **replication_options)</span>
</code></dt>
<dd>
<div class="desc"><p>"setter for granular blklvl Replication replica copy&hellip;</p>
<h2 id="args">Args</h2>
<p>srcclientid
(int)
&ndash;
Source client id.</p>
<p>destclientid
(dict)
&ndash; Destintion client id .</p>
<p>scid
(int) &ndash;
Replication Subclient id</p>
<p>blrid
(int) &ndash; Blr pair id</p>
<p>srcguid
(str) &ndash; source volume guid</p>
<p>dstguid
(str) &ndash; Destination relication guid</p>
<p>restoreguid
(str) &ndash; RP store guid</p>
<p>timestamp
(int) &ndash; Replication point timestamp</p>
<p>**replication_options (dict) &ndash; object instance</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/fsbackupset.py#L1542-L1667" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def create_granular_replica_copy(self, srcclientid, destclientid, scid, blrid, srcguid, dstguid, restoreguid,
                                 **replication_options):
    &#34;&#34;&#34;&#34;setter for granular blklvl Replication replica copy...

    Args:
        srcclientid   (int)  --  Source client id.

        destclientid    (dict)  -- Destintion client id .

        scid           (int) --  Replication Subclient id

        blrid           (int) -- Blr pair id

        srcguid         (str) -- source volume guid

        dstguid         (str) -- Destination relication guid

        restoreguid     (str) -- RP store guid

        timestamp        (int) -- Replication point timestamp

        **replication_options (dict) -- object instance


    &#34;&#34;&#34;

    replicapoints = self.get_recovery_points(destclientid, scid)
    timestamp = replication_options.get(&#39;timestamp&#39;)
    if timestamp:
        restore_point = [replica_point for replica_point in replicapoints
                         if int(replica_point[&#39;timeStamp&#39;]) == timestamp]
    else:
        restore_point = replicapoints[-1]

    srcvol = replication_options.get(&#39;srcvol&#39;)
    restorepath = replication_options.get(&#39;RestorePath&#39;)
    replicacopyjson = {
        &#34;taskInfo&#34;: {
            &#34;task&#34;: {
                &#34;ownerId&#34;: 1,
                &#34;taskType&#34;: 1,
                &#34;ownerName&#34;: &#34;&#34;,
                &#34;initiatedFrom&#34;: 1,
                &#34;taskFlags&#34;: {
                    &#34;disabled&#34;: False
                }
            },
            &#34;subTasks&#34;: [
                {
                    &#34;subTaskOperation&#34;: 1,
                    &#34;subTask&#34;: {
                        &#34;subTaskType&#34;: 1,
                        &#34;operationType&#34;: 4047
                    },
                    &#34;options&#34;: {
                        &#34;backupOpts&#34;: {
                            &#34;mediaOpt&#34;: {
                                &#34;auxcopyJobOption&#34;: {
                                    &#34;maxNumberOfStreams&#34;: 0,
                                    &#34;allCopies&#34;: True,
                                    &#34;useMaximumStreams&#34;: True,
                                    &#34;useScallableResourceManagement&#34;: False
                                }
                            }
                        },
                        &#34;adminOpts&#34;: {
                            &#34;blockOperation&#34;: {
                                &#34;operations&#34;: [
                                    {
                                        &#34;appId&#34;: int(scid),
                                        &#34;opType&#34;: 8,
                                        &#34;dstProxyClientId&#34;: int(destclientid),
                                        &#34;fsMountInfo&#34;: {
                                            &#34;doLiveMount&#34;: False,
                                            &#34;lifeTimeInSec&#34;: 7200,
                                            &#34;blrPairId&#34;: int(blrid),
                                            &#34;mountPathPairs&#34;: [
                                                {
                                                    &#34;mountPath&#34;: restorepath,
                                                    &#34;srcPath&#34;: srcvol,
                                                    &#34;srcGuid&#34;: dstguid,
                                                    &#34;dstGuid&#34;: restoreguid
                                                }
                                            ],
                                            &#34;rp&#34;: {
                                                &#34;timeStamp&#34;: int(restore_point[&#39;timeStamp&#39;]),
                                                &#34;sequenceNumber&#34;: int(restore_point[&#39;sequenceNumber&#39;]),
                                                &#34;rpType&#34;: 1,
                                                &#34;appConsistent&#34;: False,
                                                &#34;dataChangedSize&#34;: int(restore_point[&#39;dataChangedSize&#39;])
                                            }
                                        }
                                    }
                                ]
                            }
                        },
                        &#34;commonOpts&#34;: {
                            &#34;subscriptionInfo&#34;: &#34;&lt;Api_Subscription subscriptionId =\&#34;1451\&#34;/&gt;&#34;
                        }
                    }
                }
            ]
        }
    }

    flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._services[&#39;RESTORE&#39;], replicacopyjson)

    if flag:
        if response.json():
            if &#34;jobIds&#34; in response.json():
                return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])

            elif &#34;taskId&#34; in response.json():
                return Schedules(self._commcell_object).get(task_id=response.json()[&#39;taskId&#39;])

            elif &#34;errorCode&#34; in response.json():
                error_message = response.json()[&#39;errorMessage&#39;]

                o_str = &#39;Restore job failed\nError: &#34;{0}&#34;&#39;.format(error_message)
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Failed to run the restore job&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupsets.fsbackupset.FSBackupset.create_replica_copy"><code class="name flex">
<span>def <span class="ident">create_replica_copy</span></span>(<span>self, srcclientid, destclientid, scid, blrid, srcguid, dstguid, **replication_options)</span>
</code></dt>
<dd>
<div class="desc"><p>"setter for live
blklvl Replication replica copy&hellip;</p>
<h2 id="args">Args</h2>
<p>srcclientid
(int)
&ndash;
Source client id.</p>
<p>destclientid
(dict)
&ndash; Destintion client id .</p>
<p>scid
(int) &ndash;
Replication Subclient id</p>
<p>blrid
(int) &ndash; Blr pair id</p>
<p>srcguid
(str) &ndash; Browse guid of source</p>
<p>dstguid
(str) &ndash; Browse guid of destination volume</p>
<p>**replication_options (dict) &ndash; object instance</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/fsbackupset.py#L1304-L1410" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def create_replica_copy(self, srcclientid, destclientid, scid, blrid,
                        srcguid, dstguid, **replication_options):

    &#34;&#34;&#34;&#34;setter for live  blklvl Replication replica copy...

    Args:
        srcclientid   (int)  --  Source client id.

        destclientid    (dict)  -- Destintion client id .

        scid           (int) --  Replication Subclient id

        blrid           (int) -- Blr pair id

        srcguid         (str) -- Browse guid of source

        dstguid          (str) -- Browse guid of destination volume

        **replication_options (dict) -- object instance


    &#34;&#34;&#34;
    srcvol = replication_options.get(&#39;srcvol&#39;)
    restorepath = replication_options.get(&#39;RestorePath&#39;)
    replicacopyjson = {
        &#34;taskInfo&#34;: {
            &#34;task&#34;: {
                &#34;ownerId&#34;: 1,
                &#34;taskType&#34;: 1,
                &#34;ownerName&#34;: &#34;&#34;,
                &#34;initiatedFrom&#34;: 1,
                &#34;taskFlags&#34;: {
                    &#34;disabled&#34;: False
                }
            },
            &#34;subTasks&#34;: [
                {
                    &#34;subTaskOperation&#34;: 1,
                    &#34;subTask&#34;: {
                        &#34;subTaskType&#34;: 1,
                        &#34;operationType&#34;: 4047
                    },
                    &#34;options&#34;: {
                        &#34;backupOpts&#34;: {
                            &#34;mediaOpt&#34;: {
                                &#34;auxcopyJobOption&#34;: {
                                    &#34;maxNumberOfStreams&#34;: 0,
                                    &#34;allCopies&#34;: True,
                                    &#34;useMaximumStreams&#34;: True,
                                    &#34;useScallableResourceManagement&#34;: False
                                }
                            }
                        },
                        &#34;adminOpts&#34;: {
                            &#34;blockOperation&#34;: {
                                &#34;operations&#34;: [
                                    {
                                        &#34;appId&#34;: int(scid),
                                        &#34;opType&#34;: 8,
                                        &#34;dstProxyClientId&#34;: int(destclientid),
                                        &#34;fsMountInfo&#34;: {
                                            &#34;doLiveMount&#34;: True,
                                            &#34;lifeTimeInSec&#34;: 7200,
                                            &#34;blrPairId&#34;: int(blrid),
                                            &#34;mountPathPairs&#34;: [
                                                {
                                                    &#34;mountPath&#34;: restorepath,
                                                    &#34;srcPath&#34;: srcvol,
                                                    &#34;srcGuid&#34;: srcguid,
                                                    &#34;dstGuid&#34;: dstguid
                                                }
                                            ]
                                        }
                                    }
                                ]
                            }
                        },
                        &#34;commonOpts&#34;: {
                            &#34;subscriptionInfo&#34;: &#34;&lt;Api_Subscription subscriptionId =\&#34;116\&#34;/&gt;&#34;
                        }
                    }
                }
            ]
        }
    }

    flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._services[&#39;RESTORE&#39;],
                                                       replicacopyjson)
    if flag:
        if response.json():
            if &#34;jobIds&#34; in response.json():
                return Job(self._commcell_object, response.json()[&#39;jobIds&#39;][0])

            elif &#34;taskId&#34; in response.json():
                return Schedules(self._commcell_object).get(task_id=response.json()[&#39;taskId&#39;])

            elif &#34;errorCode&#34; in response.json():
                error_message = response.json()[&#39;errorMessage&#39;]

                o_str = &#39;Restore job failed\nError: &#34;{0}&#34;&#39;.format(error_message)
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Failed to run the restore job&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupsets.fsbackupset.FSBackupset.delete_replication_pair"><code class="name flex">
<span>def <span class="ident">delete_replication_pair</span></span>(<span>self, blrid)</span>
</code></dt>
<dd>
<div class="desc"><p>"Delete replication pair</p>
<h2 id="args">Args</h2>
<p>blrid
(int)
&ndash;
blocklevel replication id.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/fsbackupset.py#L1413-L1421" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete_replication_pair(self, blrid):
    &#34;&#34;&#34;&#34;Delete replication pair
    Args:
        blrid   (int)  --  blocklevel replication id.
    &#34;&#34;&#34;
    flag, response = self._cvpysdk_object.make_request(&#39;DELETE&#39;, self._services[&#39;DELETE_BLR_PAIR&#39;]%blrid)

    if response.status_code != 200 and flag == False:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupsets.fsbackupset.FSBackupset.find_all_versions"><code class="name flex">
<span>def <span class="ident">find_all_versions</span></span>(<span>self, *args, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Searches the content of a Subclient, and returns all versions available for the content.</p>
<pre><code>Args:
    Dictionary of browse options:
        Example:
            find_all_versions({
                'path': 'c:\hello',
                'show_deleted': True,
                'from_time': '2014-04-20 12:00:00',
                'to_time': '2016-04-31 12:00:00'
            })

        (OR)

    Keyword argument of browse options:
        Example:
            find_all_versions(
                path='c:\hello.txt',
                show_deleted=True,
                to_time='2016-04-31 12:00:00'
            )

    Refer self._default_browse_options for all the supported options
</code></pre>
<h2 id="returns">Returns</h2>
<p>dict
-
dictionary of the specified file with list of all the file versions and
additional metadata retrieved from browse</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/fsbackupset.py#L302-L339" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def find_all_versions(self, *args, **kwargs):
    &#34;&#34;&#34;Searches the content of a Subclient, and returns all versions available for the content.

        Args:
            Dictionary of browse options:
                Example:
                    find_all_versions({
                        &#39;path&#39;: &#39;c:\\hello&#39;,
                        &#39;show_deleted&#39;: True,
                        &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,
                        &#39;to_time&#39;: &#39;2016-04-31 12:00:00&#39;
                    })

                (OR)

            Keyword argument of browse options:
                Example:
                    find_all_versions(
                        path=&#39;c:\\hello.txt&#39;,
                        show_deleted=True,
                        to_time=&#39;2016-04-31 12:00:00&#39;
                    )

            Refer self._default_browse_options for all the supported options

    Returns:
        dict    -   dictionary of the specified file with list of all the file versions and
                        additional metadata retrieved from browse

    &#34;&#34;&#34;
    if args and isinstance(args[0], dict):
        options = args[0]
    else:
        options = kwargs

    options[&#39;operation&#39;] = &#39;all_versions&#39;

    return self._do_browse(options)</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupsets.fsbackupset.FSBackupset.get_browse_volume_guid"><code class="name flex">
<span>def <span class="ident">get_browse_volume_guid</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>"to get browse volume guids for client</p>
<h2 id="returns">Returns</h2>
<p>vguids (json) : Returns volume guids and properties</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/fsbackupset.py#L1670-L1690" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_browse_volume_guid(self):

    &#34;&#34;&#34;&#34;to get browse volume guids for client
        Returns:
            vguids (json) : Returns volume guids and properties

    &#34;&#34;&#34;
    client_id= self._client_object.client_id
    flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._services[&#39;BROWSE_MOUNT_POINTS&#39;]
                                                       %(client_id))
    if flag:
        if response and response.json():
            vguids = response.json()
            if response.json().get(&#39;errorCode&#39;, 0) != 0:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;)

    return vguids</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupsets.fsbackupset.FSBackupset.get_mount_path_guid"><code class="name flex">
<span>def <span class="ident">get_mount_path_guid</span></span>(<span>self, volume)</span>
</code></dt>
<dd>
<div class="desc"><p>Gets the mount points for the BLR pairs</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>volume</code></strong> :&ensp;<code>str</code></dt>
<dd>volume name eg: "E:"</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/fsbackupset.py#L1423-L1433" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_mount_path_guid(self, volume):
    &#34;&#34;&#34;
    Gets the mount points for the BLR pairs
    Args:
        volume (str): volume name eg: &#34;E:&#34;
    &#34;&#34;&#34;
    volume_list = self.get_browse_volume_guid()
    for mount_path in volume_list[&#39;mountPathInfo&#39;]:
        if mount_path[&#39;accessPathList&#39;][0] == volume:
            return mount_path[&#39;guid&#39;]
    return &#39;&#39;</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupsets.fsbackupset.FSBackupset.get_recovery_points"><code class="name flex">
<span>def <span class="ident">get_recovery_points</span></span>(<span>self, client_id, subclient_id)</span>
</code></dt>
<dd>
<div class="desc"><p>Get all recovery points for the BLR pair from the associated RPStore.
These recovery points are those to which BLR pairs can failover/permanent mount to</p>
<h2 id="args">Args</h2>
<p>client_id
(int): The ID of the source client machine
subclient_id
(int): The ID of the subclient associated with the BLR pair</p>
<h2 id="returns">Returns</h2>
<dl>
<dt><code>List</code> of <code>dictionary</code> of <code>recovery points in the format</code></dt>
<dd>{'timestamp': 12323, 'dataChangedSize': 1200,</dd>
</dl>
<p>'sequenceNumber': 898}</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/fsbackupset.py#L1435-L1457" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_recovery_points(self, client_id, subclient_id):
    &#34;&#34;&#34; Get all recovery points for the BLR pair from the associated RPStore.
    These recovery points are those to which BLR pairs can failover/permanent mount to
    Args:
        client_id       (int): The ID of the source client machine
        subclient_id    (int): The ID of the subclient associated with the BLR pair

    Returns:
        List of dictionary of recovery points in the format: {&#39;timestamp&#39;: 12323, &#39;dataChangedSize&#39;: 1200,
        &#39;sequenceNumber&#39;: 898}
    &#34;&#34;&#34;
    client_name = [key for key, value in self._commcell_object.clients.all_clients.items()
                   if value[&#39;id&#39;] == client_id]
    if not client_name:
        raise SDKException(f&#39;Client not found with client id [{client_id}]&#39;)
    client = self._commcell_object.clients.get(client_name[0])
    flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._services[&#39;GRANULAR_BLR_POINTS&#39;]
                                                       %(client_id, subclient_id, client.client_guid))
    if not flag or response.status_code != 200:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
    if &#39;vmScale&#39; not in response.json():
        return []
    return response.json()[&#39;vmScale&#39;][&#39;restorePoints&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupsets.fsbackupset.FSBackupset.restore_in_place"><code class="name flex">
<span>def <span class="ident">restore_in_place</span></span>(<span>self, paths, overwrite=True, restore_data_and_acl=True, copy_precedence=None, from_time=None, to_time=None, fs_options=None, restore_jobs=None, advanced_options=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the files/folders specified in the input paths list to the same location.</p>
<h2 id="args">Args</h2>
<p>paths
(list)
&ndash;
list of full paths of files/folders to restore</p>
<p>overwrite
(bool)
&ndash;
unconditional overwrite files during restore
default: True</p>
<p>restore_data_and_acl
(bool)
&ndash;
restore data and ACL files
default: True</p>
<p>copy_precedence
(int)
&ndash;
copy precedence value of storage policy copy
default: None</p>
<p>from_time
(str)
&ndash;
time to retore the contents after
format: YYYY-MM-DD HH:MM:SS</p>
<pre><code>default: None
</code></pre>
<p>to_time
(str)
&ndash;
time to retore the contents before
format: YYYY-MM-DD HH:MM:SS</p>
<pre><code>default: None
</code></pre>
<p>fs_options
(dict)
&ndash; dictionary that includes all advanced options</p>
<pre><code>options:

    all_versions        : if set to True restores all the versions of the
                            specified file

    versions            : list of version numbers to be backed up

    validate_only       : To validate data backed up for restore

    no_of_streams   (int)       -- Number of streams to be used for restore
</code></pre>
<p>restore_jobs
(list)
&ndash;
list of jobs to be restored if the job is index free restore</p>
<p>advanced_options
(dict)
&ndash; Advanced restore options</p>
<pre><code>Options:

    job_description (str)   --  Restore job description

    timezone        (str)   --  Timezone to be used for restore

        **Note** make use of TIMEZONES dict in constants.py to pass timezone
</code></pre>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if paths is not a list</p>
<pre><code>if failed to initialize job

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/fsbackupset.py#L86-L177" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_in_place(
        self,
        paths,
        overwrite=True,
        restore_data_and_acl=True,
        copy_precedence=None,
        from_time=None,
        to_time=None,
        fs_options=None,
        restore_jobs=None,
        advanced_options=None
):
    &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the same location.

        Args:
            paths                   (list)  --  list of full paths of files/folders to restore

            overwrite               (bool)  --  unconditional overwrite files during restore
                default: True

            restore_data_and_acl    (bool)  --  restore data and ACL files
                default: True

            copy_precedence         (int)   --  copy precedence value of storage policy copy
                default: None

            from_time           (str)       --  time to retore the contents after
                    format: YYYY-MM-DD HH:MM:SS

                default: None

            to_time           (str)         --  time to retore the contents before
                    format: YYYY-MM-DD HH:MM:SS

                default: None

            fs_options      (dict)          -- dictionary that includes all advanced options

                options:

                    all_versions        : if set to True restores all the versions of the
                                            specified file

                    versions            : list of version numbers to be backed up

                    validate_only       : To validate data backed up for restore

                    no_of_streams   (int)       -- Number of streams to be used for restore

            restore_jobs    (list)          --  list of jobs to be restored if the job is index free restore

            advanced_options    (dict)  -- Advanced restore options

                Options:

                    job_description (str)   --  Restore job description

                    timezone        (str)   --  Timezone to be used for restore

                        **Note** make use of TIMEZONES dict in constants.py to pass timezone

        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:
                if paths is not a list

                if failed to initialize job

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    self._instance_object._restore_association = self._backupset_association

    if fs_options is not None and fs_options.get(&#39;no_of_streams&#39;, 1) &gt; 1 and not fs_options.get(&#39;destination_appTypeId&#39;, False):
        fs_options[&#39;destination_appTypeId&#39;] = int(self._client_object.agents.all_agents.get(&#39;file system&#39;, self._client_object.agents.all_agents.get(&#39;windows file system&#39;, self._client_object.agents.all_agents.get(&#39;linux file system&#39;, self._client_object.agents.all_agents.get(&#39;big data apps&#39;, self._client_object.agents.all_agents.get(&#39;cloud apps&#39;, 0))))))
        if not fs_options[&#39;destination_appTypeId&#39;]:
            del fs_options[&#39;destination_appTypeId&#39;]

    return self._instance_object._restore_in_place(
        paths=paths,
        overwrite=overwrite,
        restore_data_and_acl=restore_data_and_acl,
        copy_precedence=copy_precedence,
        from_time=from_time,
        to_time=to_time,
        fs_options=fs_options,
        restore_jobs=restore_jobs,
        advanced_options=advanced_options
    )</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupsets.fsbackupset.FSBackupset.restore_out_of_place"><code class="name flex">
<span>def <span class="ident">restore_out_of_place</span></span>(<span>self, client, destination_path, paths, overwrite=True, restore_data_and_acl=True, copy_precedence=None, from_time=None, to_time=None, fs_options=None, restore_jobs=None, advanced_options=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the files/folders specified in the input paths list to the input client,
at the specified destionation location.</p>
<h2 id="args">Args</h2>
<p>client
(str/object) &ndash;
either the name of the client or
the instance of the Client</p>
<p>destination_path
(str)
&ndash;
full path of the restore location on client</p>
<p>paths
(list)
&ndash;
list of full paths of
files/folders to restore</p>
<p>overwrite
(bool)
&ndash;
unconditional overwrite files during restore
default: True</p>
<p>restore_data_and_acl
(bool)
&ndash;
restore data and ACL files
default: True</p>
<p>copy_precedence
(int)
&ndash;
copy precedence value of storage policy copy
default: None</p>
<p>from_time
(str)
&ndash;
time to retore the contents after
format: YYYY-MM-DD HH:MM:SS</p>
<pre><code>default: None
</code></pre>
<p>to_time
(str)
&ndash;
time to retore the contents before
format: YYYY-MM-DD HH:MM:SS</p>
<pre><code>default: None
</code></pre>
<p>fs_options
(dict)
&ndash; dictionary that includes all advanced options</p>
<pre><code>options:

    preserve_level      : preserve level option to set in restore

    proxy_client        : proxy that needed to be used for restore

    impersonate_user    : Impersonate user options for restore

    impersonate_password: Impersonate password option for restore
                            in base64 encoded form

    all_versions        : if set to True restores all the versions of the
                            specified file

    versions            : list of version numbers to be backed up

    validate_only       : To validate data backed up for restore

    no_of_streams   (int)       -- Number of streams to be used for restore
</code></pre>
<p>restore_jobs
(list)
&ndash;
list of jobs to be restored if the job is index free restore</p>
<p>advanced_options
(dict)
&ndash; Advanced restore options</p>
<pre><code>Options:

    job_description (str)   --  Restore job description

    timezone        (str)   --  Timezone to be used for restore

        **Note** make use of TIMEZONES dict in constants.py to pass timezone
</code></pre>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if client is not a string or Client instance</p>
<pre><code>if destination_path is not a string

if paths is not a list

if failed to initialize job

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/fsbackupset.py#L179-L300" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_out_of_place(
        self,
        client,
        destination_path,
        paths,
        overwrite=True,
        restore_data_and_acl=True,
        copy_precedence=None,
        from_time=None,
        to_time=None,
        fs_options=None,
        restore_jobs=None,
        advanced_options=None
):
    &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the input client,
        at the specified destionation location.

        Args:
            client                (str/object) --  either the name of the client or
                                                       the instance of the Client

            destination_path      (str)        --  full path of the restore location on client

            paths                 (list)       --  list of full paths of
                                                       files/folders to restore

            overwrite             (bool)       --  unconditional overwrite files during restore
                default: True

            restore_data_and_acl  (bool)       --  restore data and ACL files
                default: True

            copy_precedence         (int)      --  copy precedence value of storage policy copy
                default: None

            from_time           (str)          --  time to retore the contents after
                    format: YYYY-MM-DD HH:MM:SS

                default: None

            to_time           (str)            --  time to retore the contents before
                    format: YYYY-MM-DD HH:MM:SS

                default: None

            fs_options      (dict)             -- dictionary that includes all advanced options

                options:

                    preserve_level      : preserve level option to set in restore

                    proxy_client        : proxy that needed to be used for restore

                    impersonate_user    : Impersonate user options for restore

                    impersonate_password: Impersonate password option for restore
                                            in base64 encoded form

                    all_versions        : if set to True restores all the versions of the
                                            specified file

                    versions            : list of version numbers to be backed up

                    validate_only       : To validate data backed up for restore

                    no_of_streams   (int)       -- Number of streams to be used for restore

            restore_jobs    (list)          --  list of jobs to be restored if the job is index free restore

            advanced_options    (dict)  -- Advanced restore options

                Options:

                    job_description (str)   --  Restore job description

                    timezone        (str)   --  Timezone to be used for restore

                        **Note** make use of TIMEZONES dict in constants.py to pass timezone

        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:
                if client is not a string or Client instance

                if destination_path is not a string

                if paths is not a list

                if failed to initialize job

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    self._instance_object._restore_association = self._backupset_association

    if not isinstance(client, (str, Client)):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    if isinstance(client, str):
        client = Client(self._commcell_object, client)

    if fs_options is not None and fs_options.get(&#39;no_of_streams&#39;, 1) &gt; 1 and not fs_options.get(&#39;destination_appTypeId&#39;, False):
        fs_options[&#39;destination_appTypeId&#39;] = int(client.agents.all_agents.get(&#39;file system&#39;, client.agents.all_agents.get(&#39;windows file system&#39;, client.agents.all_agents.get(&#39;linux file system&#39;, client.agents.all_agents.get(&#39;big data apps&#39;, client.agents.all_agents.get(&#39;cloud apps&#39;, 0))))))
        if not fs_options[&#39;destination_appTypeId&#39;]:
            del fs_options[&#39;destination_appTypeId&#39;]

    return self._instance_object._restore_out_of_place(
        client=client,
        destination_path=destination_path,
        paths=paths,
        overwrite=overwrite,
        restore_data_and_acl=restore_data_and_acl,
        copy_precedence=copy_precedence,
        from_time=from_time,
        to_time=to_time,
        fs_options=fs_options,
        restore_jobs=restore_jobs,
        advanced_options=advanced_options
    )</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupsets.fsbackupset.FSBackupset.run_bmr_aix_restore"><code class="name flex">
<span>def <span class="ident">run_bmr_aix_restore</span></span>(<span>self, **restore_options)</span>
</code></dt>
<dd>
<div class="desc"><p>Calling the create task API with the final restore JSON</p>
<p>Args :</p>
<pre><code>    Clone Clinet Name  (String)     : Clone machine name

    Clone Hostname  (String)        :Clone machine host name

    DNS Suffix      (String)        :Dns suffix name

    DNS IP  (Integer)                :Ip of Dns Address

    Clone IP    (Integer)            :Clone Machine IP

    Clone Netmask (Integer)          :Clone Machine NetMask

    Clone Gateway (Integer)          :Clone Machine Gateway

    Auto Reboot   (Boolean)          :Client machine Auto reboot(True or False)

    Clone          (Boolean)         :Is Clone enabled(True or False)

    CS_Username   (String)    : The username for the Comcell

    CS_Password   (String)     : The password for the comcell
</code></pre>
<p>Returns :
returns the task object</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/fsbackupset.py#L1005-L1137" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def run_bmr_aix_restore(self, **restore_options):
    &#34;&#34;&#34;
            Calling the create task API with the final restore JSON

            Args :


                    Clone Clinet Name  (String)     : Clone machine name

                    Clone Hostname  (String)        :Clone machine host name

                    DNS Suffix      (String)        :Dns suffix name

                    DNS IP  (Integer)                :Ip of Dns Address

                    Clone IP    (Integer)            :Clone Machine IP

                    Clone Netmask (Integer)          :Clone Machine NetMask

                    Clone Gateway (Integer)          :Clone Machine Gateway

                    Auto Reboot   (Boolean)          :Client machine Auto reboot(True or False)

                    Clone          (Boolean)         :Is Clone enabled(True or False)

                    CS_Username   (String)    : The username for the Comcell

                    CS_Password   (String)     : The password for the comcell

            Returns :
                        returns the task object

            &#34;&#34;&#34;
    self._instance_object._restore_association = self._backupset_association
    request_json = self._restore_json(paths=[&#39;&#39;])
    restore_json_aix_system_state = self._restore_aix_1touch_admin_json()
    request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][
        &#39;oneTouchRestoreOption&#39;] = restore_json_aix_system_state
    hwconfig_aix = restore_json_aix_system_state[&#39;responseData&#39;][0][&#39;hwconfig&#39;]
    ipconfig_aix = restore_json_aix_system_state[&#39;responseData&#39;][0][&#39;clients&#39;][0][&#39;netconfig&#39;][&#39;ipinfo&#39;]
    cs_name, cs_hostname, cs_ip_address, cs_user, cs_pwd, cs_token = self._get_cs_login_details()
    vmjson = self._restore_bmr_admin_json(ipconfig_aix, hwconfig_aix)
    request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;] = vmjson
    is_clone = restore_options.get(&#39;clone&#39;, None)
    subtask_json = {
        &#39;subTaskType&#39; : 3,
        &#39;operationType&#39;: 1006
    }
    common_options = {
        &#39;systemStateBackup&#39; : True,
        &#39;copyToObjectStore&#39; :False,
        &#39;restoreToDisk&#39; : False,
        &#39;skipIfExists&#39; : restore_options.get(&#39;run_FS_restore&#39;, False),
        &#39;SyncRestore&#39; : False
    }
    response_data = {
            &#34;clients&#34;: [{
                &#34;clone&#34;: is_clone,
                &#34;netconfig&#34;: {
                    &#34;dns&#34;: {
                        &#34;suffix&#34;: restore_options.get(&#39;dns_suffix&#39;, None),
                        &#34;nameservers&#34;: [{
                            &#34;address&#34;: restore_options.get(&#39;dns_ip&#39;, None),
                        }]
                    },
                    &#34;ipinfo&#34;: {
                        &#34;interfaces&#34;: [{
                            &#34;protocols&#34;: [{
                                &#34;gw&#34;: restore_options.get(&#39;clone_machine_gateway&#39;, None),
                                &#34;netmask&#34;: restore_options.get(&#39;clone_machine_netmask&#39;, None),
                                &#34;ip&#34;: {
                                    &#34;address&#34;: restore_options.get(&#39;clone_ip_address&#39;, None)
                                }
                            }]
                        }]
                    }
                },
                &#34;newclient&#34;: {
                    &#34;hostName&#34;: restore_options.get(&#39;clone_client_hostname&#39;, None),
                    &#34;clientName&#34;: restore_options.get(&#39;clone_client_name&#39;, None)
                }
            }],
            &#34;csinfo&#34;: {
                &#34;ip&#34;: {
                    &#34;address&#34;: cs_ip_address
                },
                &#34;commservInfo&#34;: {
                    &#34;hostName&#34;: cs_hostname,
                    &#34;clientName&#34;: cs_name
                },
                &#34;creds&#34;: {
                    &#34;password&#34;: cs_pwd,
                    &#34;confirmPassword&#34;: cs_pwd,
                    &#34;userName&#34;: cs_user,
                    &#34;token&#34;:  cs_token
                }
            },
        }
    request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;subTask&#39;] = subtask_json
    request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;commonOptions&#39;] = common_options
    request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;destination&#39;][&#39;destPath&#39;] = (
        [restore_options.get(&#39;onetouch_server_directory&#39;, &#39;&#39;)])
    request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;destination&#39;][
        &#39;destClient&#39;][&#39;clientName&#39;] = restore_options.get(&#39;onetouch_server&#39;, None)
    request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][
        &#39;oneTouchRestoreOption&#39;][&#39;automaticClientReboot&#39;] = restore_options.get(&#39;automaticClientReboot&#39;, None)

    if is_clone:
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;oneTouchRestoreOption&#39;][
            &#39;responseData&#39;][0][&#39;clients&#39;][0][&#39;netconfig&#39;][
                &#39;dns&#39;][&#39;nameservers&#39;][0][&#39;address&#39;] = restore_options.get(&#39;dns_ip&#39;, None)
    request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;oneTouchRestoreOption&#39;][&#39;responseData&#39;][
        0] = response_data
    request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;oneTouchRestoreOption&#39;][
        &#39;restoreFromBackupBeforeDate&#39;] = True if restore_options.get(&#39;restoreFromBackupBeforeDate&#39;) else False
    if restore_options.get(&#39;onetouch_backup_jobid&#39;) is not None:
        _job = self._commcell_object.job_controller.get(restore_options.get(&#39;onetouch_backup_jobid&#39;))
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;browseOption&#34;][
            &#34;timeRange&#34;][&#34;toTime&#34;] = _job.end_timestamp
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;browseOption&#34;][
            &#34;timeRange&#34;][&#34;fromTime&#34;] = _job.start_timestamp

        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;vmProvisioningOption&#39;][
            &#39;virtualMachineOption&#39;][0][&#39;oneTouchResponse&#39;][&#39;dataBrowseTime&#39;][
            &#39;TimeZoneName&#39;] = self._commcell_object.commserv_timezone
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][&#39;vmProvisioningOption&#39;][
            &#39;virtualMachineOption&#39;][0][&#39;oneTouchResponse&#39;][&#39;dataBrowseTime&#39;][&#39;time&#39;] = _job.end_timestamp

        one_touch_option = {&#39;fromTime&#39;: 0, &#39;toTime&#39;: _job.end_timestamp}
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;oneTouchOption&#39;] = one_touch_option
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;fileOption&#39;][
            &#39;sourceItem&#39;] = ([f&#39;2:{_job.job_id}&#39;])
    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupsets.fsbackupset.FSBackupset.run_bmr_restore"><code class="name flex">
<span>def <span class="ident">run_bmr_restore</span></span>(<span>self, **restore_options)</span>
</code></dt>
<dd>
<div class="desc"><p>Calling the create task API with the final restore JSON</p>
<p>Args :
IsoPath
(String)
: The location of ISO in the datastore</p>
<pre><code>    CommServIP              (String)    : The IP of the CS

    CommServHostname        (String)    : The hostname of he CS

    CommServUsername        (String)    : The username for the Comcell

    CommServPassword        (String)    : The password for the comcell

    Datastore               (String)    : The ESX store in which the VM is provisioned

    VcenterServerName       (String)    : The Vcenter to be used

    ClientHostName          (String)    : The hostname of the client being virtualized.

    VmName                  (String)    : The name with which the VM is provisioned.

    VirtualizationClient    (String)    : The vmware virualization client

    EsxServer               (String)    : The ESX server name

   NetworkLabel             (String)    : The network label to be assigned to the VM.

   HyperVHost               (String)    : The Hyper-V host

   GuestUser                (String)    : The Username of the guest OS

   GuestPassword            (String)    : The Password of the guest OS

   CloneClientName          (String)    : The clone client name
</code></pre>
<p>Returns :
returns the task object</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/fsbackupset.py#L595-L839" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def run_bmr_restore(self, **restore_options):
    &#34;&#34;&#34;
    Calling the create task API with the final restore JSON

    Args :
            IsoPath                 (String)    : The location of ISO in the datastore

            CommServIP              (String)    : The IP of the CS

            CommServHostname        (String)    : The hostname of he CS

            CommServUsername        (String)    : The username for the Comcell

            CommServPassword        (String)    : The password for the comcell

            Datastore               (String)    : The ESX store in which the VM is provisioned

            VcenterServerName       (String)    : The Vcenter to be used

            ClientHostName          (String)    : The hostname of the client being virtualized.

            VmName                  (String)    : The name with which the VM is provisioned.

            VirtualizationClient    (String)    : The vmware virualization client

            EsxServer               (String)    : The ESX server name

           NetworkLabel             (String)    : The network label to be assigned to the VM.

           HyperVHost               (String)    : The Hyper-V host

           GuestUser                (String)    : The Username of the guest OS

           GuestPassword            (String)    : The Password of the guest OS

           CloneClientName          (String)    : The clone client name

    Returns :
                returns the task object

    &#34;&#34;&#34;
    client_name = self._agent_object._client_object.client_name

    self._instance_object._restore_association = self._backupset_association

    hwconfig, ipconfig, cs_username, cs_password = self._get_responsefile()
    response_json = self._restore_json(paths=[&#39;&#39;])

    restore_json_system_state = self._restore_bmr_admin_json(ipconfig, hwconfig)
    restore_json_virtualserver = self._restore_bmr_virtualserveropts_json()

    #Checking for Firewall rules
    if restore_options.get(&#34;FirewallClientGroup&#34;, &#34;&#34;).strip():
        fwconfigtocs = self._restore_bmr_firewallopts_json(restore_options.get(&#34;FirewallHostname&#34;),
                                                           restore_options.get(&#34;FirewallDirection&#34;),
                                                           restore_options.get(&#34;FirewallPort&#34;))
        restore_json_system_state[&#39;vmProvisioningOption&#39;][&#39;virtualMachineOption&#39;][0][
            &#39;oneTouchResponse&#39;][&#39;clients&#39;][0][&#39;fwconfigtocs&#39;] = fwconfigtocs
        restore_json_system_state[&#39;vmProvisioningOption&#39;][&#39;virtualMachineOption&#39;][0][&#39;oneTouchResponse&#39;][&#39;csinfo&#39;][
            &#39;fwClientGroupName&#39;] = restore_options.get(&#34;FirewallClientGroup&#34;)

    #Get instance Id of the virtual client
    virtual_client_object = self._commcell_object.clients.get(restore_options.get(&#39;VirtualizationClient&#39;))
    virtual_agent_object = virtual_client_object.agents.get(&#39;Virtual Server&#39;)
    instances_list = virtual_agent_object.instances._instances
    instance_id = int(list(instances_list.values())[0])
    instance_name = list(instances_list.keys())[0]

    response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][
        &#39;adminOpts&#39;] = restore_json_system_state

    response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][
        &#39;virtualServerRstOption&#39;] = restore_json_virtualserver

    response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;destination&#39;][
        &#39;inPlace&#39;] = False
    response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;subTask&#39;][&#39;subTaskType&#39;] = 1
    response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;subTask&#39;][&#39;operationType&#39;] = 4041

    vm_option = response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][
        &#39;vmProvisioningOption&#39;][&#39;virtualMachineOption&#39;][0]

    vm_option[&#39;vmInfo&#39;][&#39;vmLocation&#39;][&#39;instanceEntity&#39;][&#39;clientName&#39;] = restore_options.get(&#39;VirtualizationClient&#39;)
    vm_option[&#39;vmInfo&#39;][&#39;vmLocation&#39;][&#39;instanceEntity&#39;][&#39;instanceId&#39;] = instance_id

    if(response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;adminOpts&#39;][
        &#39;vmProvisioningOption&#39;][&#39;virtualMachineOption&#39;][0][&#39;oneTouchResponse&#39;][
        &#39;hwconfig&#39;][&#39;mem_size&#39;]) &lt; 4096:
        vm_option[&#39;oneTouchResponse&#39;][&#39;hwconfig&#39;][&#39;mem_size&#39;] = 4096

    if restore_options.get(&#39;CommServIP&#39;):
        cs_ip = restore_options.get(&#39;CommServIP&#39;)
    else:
        try:
            cs_ip = socket.gethostbyname(self._commcell_object.commserv_hostname)

        except Exception as e:
            raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, &#39;Error while reading CommServer IP : {}\n. Please set the CommServIP argument.&#39;.format(e))

    vm_option[&#39;oneTouchResponse&#39;][&#39;csinfo&#39;][&#39;ip&#39;][
        &#39;address&#39;] = cs_ip

    vm_option[&#39;oneTouchResponse&#39;][&#39;csinfo&#39;][&#39;commservInfo&#39;][
        &#39;clientName&#39;] = self._commcell_object.commserv_name
    vm_option[&#39;oneTouchResponse&#39;][&#39;csinfo&#39;][&#39;commservInfo&#39;][
        &#39;hostName&#39;] = self._commcell_object.commserv_hostname

    vm_option[&#39;oneTouchResponse&#39;][&#39;csinfo&#39;][&#39;creds&#39;][
        &#39;password&#39;] = cs_password

    vm_option[&#39;oneTouchResponse&#39;][&#39;csinfo&#39;][&#39;creds&#39;][
        &#39;userName&#39;] = cs_username

    vm_option[&#39;oneTouchResponse&#39;][&#39;hwconfig&#39;][&#39;vmName&#39;] = restore_options.get(&#39;VmName&#39;, None)

    vm_option[&#39;oneTouchResponse&#39;][&#39;hwconfig&#39;][&#39;overwriteVm&#39;] = True

    vm_option[&#39;oneTouchResponse&#39;][&#39;clients&#39;][0][&#39;client&#39;][
        &#39;hostName&#39;] = restore_options.get(&#39;ClientHostname&#39;, None)

    vm_option[&#39;oneTouchResponse&#39;][&#39;clients&#39;][0][&#39;client&#39;][
        &#39;clientName&#39;] = restore_options.get(&#39;ClientName&#39;, None)

    if instance_name == &#39;vmware&#39; or instance_name == &#39;hyper-v&#39;:

        vm_option[&#39;isoPath&#39;] = restore_options.get(&#39;IsoPath&#39;)

        vm_option[&#39;vmInfo&#39;][&#39;vmLocation&#39;][&#39;pathName&#39;] = restore_options.get(&#39;IsoPath&#39;, None)

        vm_option[&#39;oneTouchResponse&#39;][&#39;clients&#39;][0][&#39;netconfig&#39;][&#39;ipinfo&#39;][&#39;interfaces&#39;][0][
            &#39;protocols&#39;][0][&#39;useDhcp&#39;] = True

        vm_option[&#39;oneTouchResponse&#39;][&#39;clients&#39;][0][&#39;netconfig&#39;][&#39;ipinfo&#39;][&#39;interfaces&#39;][0][
            &#39;networkLabel&#39;] = restore_options.get(&#39;NetworkLabel&#39;, None)

        if &#39;scsi_disks&#39; in vm_option[&#39;oneTouchResponse&#39;][&#39;hwconfig&#39;]:
            vm_option[&#39;oneTouchResponse&#39;][&#39;hwconfig&#39;][&#39;scsi_disks&#39;][0][
                &#39;dataStoreName&#39;] = restore_options.get(&#39;Datastore&#39;, None)

        if &#39;ide_disks&#39; in vm_option[&#39;oneTouchResponse&#39;][&#39;hwconfig&#39;]:
            vm_option[&#39;oneTouchResponse&#39;][&#39;hwconfig&#39;][&#39;ide_disks&#39;][0][
                &#39;dataStoreName&#39;] = restore_options.get(&#39;Datastore&#39;, None)

        vm_option[&#39;vmInfo&#39;][&#39;vmLocation&#39;][&#39;datastore&#39;][
            &#39;name&#39;] = restore_options.get(&#39;Datastore&#39;, None)

        if instance_name == &#39;vmware&#39;:

            vm_option[&#39;vmInfo&#39;][&#39;proxyClient&#39;][
                &#39;clientName&#39;] = restore_options.get(&#39;VirtualizationClient&#39;, None)

            response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][
                &#39;restoreOptions&#39;][&#39;virtualServerRstOption&#39;][&#39;diskLevelVMRestoreOption&#39;][
                    &#39;esxServerName&#39;] = restore_options.get(&#39;VcenterServerName&#39;, None)

            response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;destination&#39;][
                &#39;destClient&#39;][&#39;clientName&#39;] = restore_options.get(&#39;VirtualizationClient&#39;, None)

            vm_option[&#39;vmInfo&#39;][&#39;vmLocation&#39;][&#39;hostName&#39;] = restore_options.get(
                &#39;EsxServer&#39;)

            vm_option[&#39;vmInfo&#39;][&#39;vmLocation&#39;][&#39;vCenter&#39;] = restore_options.get(&#39;VcenterServerName&#39;)

        if instance_name == &#39;hyper-v&#39;:

            if restore_options.get(&#39;OsType&#39;) == &#39;UNIX&#39;:

                vm_option[&#39;vendor&#39;] = &#39;MICROSOFT&#39;

            response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][
                &#39;restoreOptions&#39;][&#39;virtualServerRstOption&#39;][&#39;diskLevelVMRestoreOption&#39;][
                    &#39;esxServerName&#39;] = restore_options.get(&#39;VirtualizationClient&#39;, None)

            response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;destination&#39;][
                &#39;destClient&#39;][&#39;clientName&#39;] = restore_options.get(&#39;VirtualizationClient&#39;, None)

            vm_option[&#39;vmInfo&#39;][&#39;vmLocation&#39;][&#39;hostName&#39;] = restore_options.get(
                &#39;HyperVHost&#39;)

            vm_option[&#39;vmInfo&#39;][&#39;vmLocation&#39;][&#39;vCenter&#39;] = restore_options.get(&#39;VirtualizationClient&#39;)

    if &#39;azure&#39; in instance_name:

        az_advanced_ops_json = self._azure_advancedopts_json()

        az_adv_restore_opts_json = self._azure_advancedrestoreopts_json()

        vm_option[&#39;vendor&#39;] = 7

        vm_option[&#39;createPublicIp&#39;] = restore_options.get(&#39;CreatePublicIP&#39;)

        vm_option[&#39;oneTouchResponse&#39;][&#39;clients&#39;][0][&#39;isBlockLevelBackup&#39;] = True

        vm_option[&#39;vmInfo&#39;][&#39;vmLocation&#39;][&#39;advancedProperties&#39;] = az_advanced_ops_json

        vm_option[&#39;vmInfo&#39;][&#39;vmLocation&#39;][&#39;advancedProperties&#39;][&#39;networkCards&#39;][0][&#39;label&#39;] = &#34;--Auto Select--&#34;
        vm_option[&#39;vmInfo&#39;][&#39;vmLocation&#39;][&#39;hostName&#39;] = restore_options.get(&#39;ResourceGroup&#39;)

        response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][
            &#39;restoreOptions&#39;][&#39;virtualServerRstOption&#39;][&#39;diskLevelVMRestoreOption&#39;][
            &#39;advancedRestoreOptions&#39;] = az_adv_restore_opts_json

        response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][
            &#39;restoreOptions&#39;][&#39;virtualServerRstOption&#39;][&#39;diskLevelVMRestoreOption&#39;][
            &#39;advancedRestoreOptions&#39;][0][&#39;securityGroups&#39;][0][&#39;groupName&#39;] = &#34;--Auto Select--&#34;

        response_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;destination&#39;][
            &#39;destClient&#39;][&#39;clientName&#39;] = restore_options.get(&#39;VirtualizationClient&#39;, None)

        if &#39;scsi_disks&#39; in vm_option[&#39;oneTouchResponse&#39;][&#39;hwconfig&#39;]:
            vm_option[&#39;oneTouchResponse&#39;][&#39;hwconfig&#39;][&#39;scsi_disks&#39;][0][
                &#39;dataStoreName&#39;] = restore_options.get(&#39;StorageAccount&#39;, None)

        if &#39;ide_disks&#39; in vm_option[&#39;oneTouchResponse&#39;][&#39;hwconfig&#39;]:
            vm_option[&#39;oneTouchResponse&#39;][&#39;hwconfig&#39;][&#39;ide_disks&#39;][0][
                &#39;dataStoreName&#39;] = restore_options.get(&#39;StorageAccount&#39;, None)

        vm_option[&#39;vmInfo&#39;][&#39;vmLocation&#39;][&#39;datastore&#39;][
            &#39;name&#39;] = restore_options.get(&#39;StorageAccount&#39;, None)

    if instance_name == &#39;azure stack&#39;:

        vm_option[&#39;vmInfo&#39;][&#39;vmLocation&#39;][&#39;vCenter&#39;] = restore_options.get(&#39;ManagementURL&#39;, None)

        vm_option[&#39;vendor&#39;] = 17

# Additional options

    if restore_options.get(&#39;CloneClientName&#39;):
        vm_option[&#39;oneTouchResponse&#39;][&#39;clients&#39;][0][&#39;clone&#39;] = True
        vm_option[&#39;oneTouchResponse&#39;][&#39;clients&#39;][0][&#39;newclient&#39;][
            &#39;clientName&#39;] = restore_options.get(&#39;CloneClientName&#39;, None)
        vm_option[&#39;oneTouchResponse&#39;][&#39;clients&#39;][0][&#39;newclient&#39;][
            &#39;hostName&#39;] = restore_options.get(&#39;CloneClientName&#39;, None)

    if restore_options.get(&#39;OsType&#39;) == &#39;UNIX&#39;:
        vm_option[&#39;oneTouchResponse&#39;][&#39;clients&#39;][0][&#39;newclient&#39;][
            &#39;hostName&#39;] = &#39;&#39;

    if restore_options.get(&#39;UseDhcp&#39;):
        vm_option[&#39;oneTouchResponse&#39;][&#39;clients&#39;][0][&#39;netconfig&#39;][&#39;ipinfo&#39;][&#39;interfaces&#39;][0][
            &#39;protocols&#39;][0][&#39;useDhcp&#39;] = True


    return self._process_restore_response(response_json)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.backupset.Backupset" href="../backupset.html#cvpysdk.backupset.Backupset">Backupset</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.backupset.Backupset.backed_up_files_count" href="../backupset.html#cvpysdk.backupset.Backupset.backed_up_files_count">backed_up_files_count</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.backup" href="../backupset.html#cvpysdk.backupset.Backupset.backup">backup</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.backupset_id" href="../backupset.html#cvpysdk.backupset.Backupset.backupset_id">backupset_id</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.backupset_name" href="../backupset.html#cvpysdk.backupset.Backupset.backupset_name">backupset_name</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.browse" href="../backupset.html#cvpysdk.backupset.Backupset.browse">browse</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.delete_data" href="../backupset.html#cvpysdk.backupset.Backupset.delete_data">delete_data</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.description" href="../backupset.html#cvpysdk.backupset.Backupset.description">description</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.find" href="../backupset.html#cvpysdk.backupset.Backupset.find">find</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.guid" href="../backupset.html#cvpysdk.backupset.Backupset.guid">guid</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.is_default_backupset" href="../backupset.html#cvpysdk.backupset.Backupset.is_default_backupset">is_default_backupset</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.is_on_demand_backupset" href="../backupset.html#cvpysdk.backupset.Backupset.is_on_demand_backupset">is_on_demand_backupset</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.list_media" href="../backupset.html#cvpysdk.backupset.Backupset.list_media">list_media</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.name" href="../backupset.html#cvpysdk.backupset.Backupset.name">name</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.plan" href="../backupset.html#cvpysdk.backupset.Backupset.plan">plan</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.properties" href="../backupset.html#cvpysdk.backupset.Backupset.properties">properties</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.refresh" href="../backupset.html#cvpysdk.backupset.Backupset.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.set_default_backupset" href="../backupset.html#cvpysdk.backupset.Backupset.set_default_backupset">set_default_backupset</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.update_properties" href="../backupset.html#cvpysdk.backupset.Backupset.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.backupsets" href="index.html">cvpysdk.backupsets</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.backupsets.fsbackupset.FSBackupset" href="#cvpysdk.backupsets.fsbackupset.FSBackupset">FSBackupset</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.backupsets.fsbackupset.FSBackupset.create_fsblr_replication_pair" href="#cvpysdk.backupsets.fsbackupset.FSBackupset.create_fsblr_replication_pair">create_fsblr_replication_pair</a></code></li>
<li><code><a title="cvpysdk.backupsets.fsbackupset.FSBackupset.create_granular_replica_copy" href="#cvpysdk.backupsets.fsbackupset.FSBackupset.create_granular_replica_copy">create_granular_replica_copy</a></code></li>
<li><code><a title="cvpysdk.backupsets.fsbackupset.FSBackupset.create_replica_copy" href="#cvpysdk.backupsets.fsbackupset.FSBackupset.create_replica_copy">create_replica_copy</a></code></li>
<li><code><a title="cvpysdk.backupsets.fsbackupset.FSBackupset.delete_replication_pair" href="#cvpysdk.backupsets.fsbackupset.FSBackupset.delete_replication_pair">delete_replication_pair</a></code></li>
<li><code><a title="cvpysdk.backupsets.fsbackupset.FSBackupset.find_all_versions" href="#cvpysdk.backupsets.fsbackupset.FSBackupset.find_all_versions">find_all_versions</a></code></li>
<li><code><a title="cvpysdk.backupsets.fsbackupset.FSBackupset.get_browse_volume_guid" href="#cvpysdk.backupsets.fsbackupset.FSBackupset.get_browse_volume_guid">get_browse_volume_guid</a></code></li>
<li><code><a title="cvpysdk.backupsets.fsbackupset.FSBackupset.get_mount_path_guid" href="#cvpysdk.backupsets.fsbackupset.FSBackupset.get_mount_path_guid">get_mount_path_guid</a></code></li>
<li><code><a title="cvpysdk.backupsets.fsbackupset.FSBackupset.get_recovery_points" href="#cvpysdk.backupsets.fsbackupset.FSBackupset.get_recovery_points">get_recovery_points</a></code></li>
<li><code><a title="cvpysdk.backupsets.fsbackupset.FSBackupset.index_pruning_cycles_retention" href="#cvpysdk.backupsets.fsbackupset.FSBackupset.index_pruning_cycles_retention">index_pruning_cycles_retention</a></code></li>
<li><code><a title="cvpysdk.backupsets.fsbackupset.FSBackupset.index_pruning_days_retention" href="#cvpysdk.backupsets.fsbackupset.FSBackupset.index_pruning_days_retention">index_pruning_days_retention</a></code></li>
<li><code><a title="cvpysdk.backupsets.fsbackupset.FSBackupset.index_pruning_type" href="#cvpysdk.backupsets.fsbackupset.FSBackupset.index_pruning_type">index_pruning_type</a></code></li>
<li><code><a title="cvpysdk.backupsets.fsbackupset.FSBackupset.index_server" href="#cvpysdk.backupsets.fsbackupset.FSBackupset.index_server">index_server</a></code></li>
<li><code><a title="cvpysdk.backupsets.fsbackupset.FSBackupset.restore_in_place" href="#cvpysdk.backupsets.fsbackupset.FSBackupset.restore_in_place">restore_in_place</a></code></li>
<li><code><a title="cvpysdk.backupsets.fsbackupset.FSBackupset.restore_out_of_place" href="#cvpysdk.backupsets.fsbackupset.FSBackupset.restore_out_of_place">restore_out_of_place</a></code></li>
<li><code><a title="cvpysdk.backupsets.fsbackupset.FSBackupset.run_bmr_aix_restore" href="#cvpysdk.backupsets.fsbackupset.FSBackupset.run_bmr_aix_restore">run_bmr_aix_restore</a></code></li>
<li><code><a title="cvpysdk.backupsets.fsbackupset.FSBackupset.run_bmr_restore" href="#cvpysdk.backupsets.fsbackupset.FSBackupset.run_bmr_restore">run_bmr_restore</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>