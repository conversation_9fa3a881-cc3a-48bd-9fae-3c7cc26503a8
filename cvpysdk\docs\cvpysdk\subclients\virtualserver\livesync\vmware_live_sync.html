<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.subclients.virtualserver.livesync.vmware_live_sync API documentation</title>
<meta name="description" content="File for configuring and monitoring live sync on the VMW subclient …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.subclients.virtualserver.livesync.vmware_live_sync</code></h1>
</header>
<section id="section-intro">
<p>File for configuring and monitoring live sync on the VMW subclient.</p>
<p>VMWareLiveSync is the only class defined in this file.</p>
<p>VMWareLiveSync: Class for configuring and monitoring VMWare subclient live sync</p>
<h2 id="vmwarelivesync">Vmwarelivesync</h2>
<p>configure_live_sync()
&ndash; To configure live sync from supplied parameters</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/livesync/vmware_live_sync.py#L1-L165" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;File for configuring and monitoring live sync on the VMW subclient.

VMWareLiveSync is the only class defined in this file.

VMWareLiveSync: Class for configuring and monitoring VMWare subclient live sync

VMWareLiveSync:

    configure_live_sync()     -- To configure live sync from supplied parameters

&#34;&#34;&#34;

from .vsa_live_sync import VsaLiveSync
from ....exception import SDKException


class VMWareLiveSync(VsaLiveSync):
    &#34;&#34;&#34;Class for configuring and monitoring VMWare live sync operations&#34;&#34;&#34;
    def configure_live_sync(self,
                            schedule_name=None,
                            destination_client=None,
                            proxy_client=None,
                            copy_precedence=0,
                            vm_to_restore=None,
                            destination_network=None,
                            power_on=True,
                            overwrite=False,
                            distribute_vm_workload=None,
                            datastore=None,
                            restored_vm_name=None,
                            restore_option=None,
                            pattern_dict=None,
                            ):
        &#34;&#34;&#34;To configure live

        Args:

            schedule_name               (str)   -- Name of the Live sync schedule to be created

            destination_client          (str)   -- VMWare Host Client Name where VM needs to be restored

            proxy_client                (str)   -- Name of the proxy client to be used

            copy_precedence             (int)   -- Copy id from which restore needs to be performed
                                                    default: 0

            vm_to_restore               (list)  -- VM&#39;s to be restored

            destination_network         (str)   -- Network card name on the destination machine

            power_on                    (bool)  -- To validate destination VM power on and off
                                                    default: True

            overwrite                   (bool)  -- To overwrite VM and VHDs in destination path
                                                    default: False

            distribute_vm_workload      (int)   -- Virtual machines to be used per job

            datastore                   (str)   -- Datastore to restore the VM

            restored_vm_name            (str)   -- Name used for the VM when restored

            restore_option              (dict)  -- Restore options dictionary with advanced options

            pattern_dict                (dict)  -- Dictionary to generate the live sync schedule

                Sample:

                    for after_job_completes :
                    {
                        &#34;freq_type&#34;: &#39;after_job_completes&#39;,
                        &#34;active_start_date&#34;: date_in_%m/%d/%y (str),
                        &#34;active_start_time&#34;: time_in_%H/%S (str),
                        &#34;repeat_days&#34;: days_to_repeat (int)
                    }

                    for daily:
                    {
                         &#34;freq_type&#34;: &#39;daily&#39;,
                         &#34;active_start_time&#34;: time_in_%H/%S (str),
                         &#34;repeat_days&#34;: days_to_repeat (int)
                    }

                    for weekly:
                    {
                         &#34;freq_type&#34;: &#39;weekly&#39;,
                         &#34;active_start_time&#34;: time_in_%H/%S (str),
                         &#34;repeat_weeks&#34;: weeks_to_repeat (int)
                         &#34;weekdays&#34;: list of weekdays [&#39;Monday&#39;,&#39;Tuesday&#39;]
                    }

                    for monthly:
                    {
                         &#34;freq_type&#34;: &#39;monthly&#39;,
                         &#34;active_start_time&#34;: time_in_%H/%S (str),
                         &#34;repeat_months&#34;: weeks_to_repeat (int)
                         &#34;on_day&#34;: Day to run schedule (int)
                    }

                    for yearly:
                    {
                         &#34;active_start_time&#34;: time_in_%H/%S (str),
                         &#34;on_month&#34;: month to run schedule (str) January, Febuary...
                         &#34;on_day&#34;: Day to run schedule (int)
                    }

        Returns:
            object - instance of the Schedule class for this Live sync

        &#34;&#34;&#34;
        # restore options
        if restore_option is None:
            restore_option = {}

        if not restored_vm_name:
            restored_vm_name = &#34;LiveSync_&#34;
        restore_option[&#39;restore_new_name&#39;] = restored_vm_name

        if copy_precedence:
            restore_option[&#34;copy_precedence_applicable&#34;] = True

        if vm_to_restore:
            vm_to_restore = [vm_to_restore]

        # check mandatory input parameters are correct
        if bool(restore_option):
            if not (isinstance(overwrite, bool) and
                    isinstance(power_on, bool)):
                raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        # set attr for all the option in restore xml from user inputs
        self._subclient_object._set_restore_inputs(
            restore_option,
            vm_to_restore=self._subclient_object._set_vm_to_restore(vm_to_restore),
            unconditional_overwrite=overwrite,
            power_on=power_on,
            distribute_vm_workload=distribute_vm_workload,
            copy_precedence=copy_precedence,
            volume_level_restore=1,
            vcenter_client=destination_client,
            client_name=proxy_client,
            datastore=datastore,
            destination_network=destination_network,
            in_place=False
        )

        return self._configure_live_sync(schedule_name, restore_option, pattern_dict)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.subclients.virtualserver.livesync.vmware_live_sync.VMWareLiveSync"><code class="flex name class">
<span>class <span class="ident">VMWareLiveSync</span></span>
<span>(</span><span>subclient_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for configuring and monitoring VMWare live sync operations</p>
<p>Initializing instance of the VsaLiveSync class</p>
<h2 id="args">Args</h2>
<p>subclient_object
(obj)
&ndash; Instance of Subclient class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/livesync/vmware_live_sync.py#L35-L165" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class VMWareLiveSync(VsaLiveSync):
    &#34;&#34;&#34;Class for configuring and monitoring VMWare live sync operations&#34;&#34;&#34;
    def configure_live_sync(self,
                            schedule_name=None,
                            destination_client=None,
                            proxy_client=None,
                            copy_precedence=0,
                            vm_to_restore=None,
                            destination_network=None,
                            power_on=True,
                            overwrite=False,
                            distribute_vm_workload=None,
                            datastore=None,
                            restored_vm_name=None,
                            restore_option=None,
                            pattern_dict=None,
                            ):
        &#34;&#34;&#34;To configure live

        Args:

            schedule_name               (str)   -- Name of the Live sync schedule to be created

            destination_client          (str)   -- VMWare Host Client Name where VM needs to be restored

            proxy_client                (str)   -- Name of the proxy client to be used

            copy_precedence             (int)   -- Copy id from which restore needs to be performed
                                                    default: 0

            vm_to_restore               (list)  -- VM&#39;s to be restored

            destination_network         (str)   -- Network card name on the destination machine

            power_on                    (bool)  -- To validate destination VM power on and off
                                                    default: True

            overwrite                   (bool)  -- To overwrite VM and VHDs in destination path
                                                    default: False

            distribute_vm_workload      (int)   -- Virtual machines to be used per job

            datastore                   (str)   -- Datastore to restore the VM

            restored_vm_name            (str)   -- Name used for the VM when restored

            restore_option              (dict)  -- Restore options dictionary with advanced options

            pattern_dict                (dict)  -- Dictionary to generate the live sync schedule

                Sample:

                    for after_job_completes :
                    {
                        &#34;freq_type&#34;: &#39;after_job_completes&#39;,
                        &#34;active_start_date&#34;: date_in_%m/%d/%y (str),
                        &#34;active_start_time&#34;: time_in_%H/%S (str),
                        &#34;repeat_days&#34;: days_to_repeat (int)
                    }

                    for daily:
                    {
                         &#34;freq_type&#34;: &#39;daily&#39;,
                         &#34;active_start_time&#34;: time_in_%H/%S (str),
                         &#34;repeat_days&#34;: days_to_repeat (int)
                    }

                    for weekly:
                    {
                         &#34;freq_type&#34;: &#39;weekly&#39;,
                         &#34;active_start_time&#34;: time_in_%H/%S (str),
                         &#34;repeat_weeks&#34;: weeks_to_repeat (int)
                         &#34;weekdays&#34;: list of weekdays [&#39;Monday&#39;,&#39;Tuesday&#39;]
                    }

                    for monthly:
                    {
                         &#34;freq_type&#34;: &#39;monthly&#39;,
                         &#34;active_start_time&#34;: time_in_%H/%S (str),
                         &#34;repeat_months&#34;: weeks_to_repeat (int)
                         &#34;on_day&#34;: Day to run schedule (int)
                    }

                    for yearly:
                    {
                         &#34;active_start_time&#34;: time_in_%H/%S (str),
                         &#34;on_month&#34;: month to run schedule (str) January, Febuary...
                         &#34;on_day&#34;: Day to run schedule (int)
                    }

        Returns:
            object - instance of the Schedule class for this Live sync

        &#34;&#34;&#34;
        # restore options
        if restore_option is None:
            restore_option = {}

        if not restored_vm_name:
            restored_vm_name = &#34;LiveSync_&#34;
        restore_option[&#39;restore_new_name&#39;] = restored_vm_name

        if copy_precedence:
            restore_option[&#34;copy_precedence_applicable&#34;] = True

        if vm_to_restore:
            vm_to_restore = [vm_to_restore]

        # check mandatory input parameters are correct
        if bool(restore_option):
            if not (isinstance(overwrite, bool) and
                    isinstance(power_on, bool)):
                raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        # set attr for all the option in restore xml from user inputs
        self._subclient_object._set_restore_inputs(
            restore_option,
            vm_to_restore=self._subclient_object._set_vm_to_restore(vm_to_restore),
            unconditional_overwrite=overwrite,
            power_on=power_on,
            distribute_vm_workload=distribute_vm_workload,
            copy_precedence=copy_precedence,
            volume_level_restore=1,
            vcenter_client=destination_client,
            client_name=proxy_client,
            datastore=datastore,
            destination_network=destination_network,
            in_place=False
        )

        return self._configure_live_sync(schedule_name, restore_option, pattern_dict)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.VsaLiveSync" href="vsa_live_sync.html#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.VsaLiveSync">VsaLiveSync</a></li>
</ul>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.subclients.virtualserver.livesync.vmware_live_sync.VMWareLiveSync.configure_live_sync"><code class="name flex">
<span>def <span class="ident">configure_live_sync</span></span>(<span>self, schedule_name=None, destination_client=None, proxy_client=None, copy_precedence=0, vm_to_restore=None, destination_network=None, power_on=True, overwrite=False, distribute_vm_workload=None, datastore=None, restored_vm_name=None, restore_option=None, pattern_dict=None)</span>
</code></dt>
<dd>
<div class="desc"><p>To configure live</p>
<h2 id="args">Args</h2>
<p>schedule_name
(str)
&ndash; Name of the Live sync schedule to be created</p>
<p>destination_client
(str)
&ndash; VMWare Host Client Name where VM needs to be restored</p>
<p>proxy_client
(str)
&ndash; Name of the proxy client to be used</p>
<p>copy_precedence
(int)
&ndash; Copy id from which restore needs to be performed
default: 0</p>
<p>vm_to_restore
(list)
&ndash; VM's to be restored</p>
<p>destination_network
(str)
&ndash; Network card name on the destination machine</p>
<p>power_on
(bool)
&ndash; To validate destination VM power on and off
default: True</p>
<p>overwrite
(bool)
&ndash; To overwrite VM and VHDs in destination path
default: False</p>
<p>distribute_vm_workload
(int)
&ndash; Virtual machines to be used per job</p>
<p>datastore
(str)
&ndash; Datastore to restore the VM</p>
<p>restored_vm_name
(str)
&ndash; Name used for the VM when restored</p>
<p>restore_option
(dict)
&ndash; Restore options dictionary with advanced options</p>
<p>pattern_dict
(dict)
&ndash; Dictionary to generate the live sync schedule</p>
<pre><code>Sample:

    for after_job_completes :
    {
        "freq_type": 'after_job_completes',
        "active_start_date": date_in_%m/%d/%y (str),
        "active_start_time": time_in_%H/%S (str),
        "repeat_days": days_to_repeat (int)
    }

    for daily:
    {
         "freq_type": 'daily',
         "active_start_time": time_in_%H/%S (str),
         "repeat_days": days_to_repeat (int)
    }

    for weekly:
    {
         "freq_type": 'weekly',
         "active_start_time": time_in_%H/%S (str),
         "repeat_weeks": weeks_to_repeat (int)
         "weekdays": list of weekdays ['Monday','Tuesday']
    }

    for monthly:
    {
         "freq_type": 'monthly',
         "active_start_time": time_in_%H/%S (str),
         "repeat_months": weeks_to_repeat (int)
         "on_day": Day to run schedule (int)
    }

    for yearly:
    {
         "active_start_time": time_in_%H/%S (str),
         "on_month": month to run schedule (str) January, Febuary...
         "on_day": Day to run schedule (int)
    }
</code></pre>
<h2 id="returns">Returns</h2>
<p>object - instance of the Schedule class for this Live sync</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/livesync/vmware_live_sync.py#L37-L165" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def configure_live_sync(self,
                        schedule_name=None,
                        destination_client=None,
                        proxy_client=None,
                        copy_precedence=0,
                        vm_to_restore=None,
                        destination_network=None,
                        power_on=True,
                        overwrite=False,
                        distribute_vm_workload=None,
                        datastore=None,
                        restored_vm_name=None,
                        restore_option=None,
                        pattern_dict=None,
                        ):
    &#34;&#34;&#34;To configure live

    Args:

        schedule_name               (str)   -- Name of the Live sync schedule to be created

        destination_client          (str)   -- VMWare Host Client Name where VM needs to be restored

        proxy_client                (str)   -- Name of the proxy client to be used

        copy_precedence             (int)   -- Copy id from which restore needs to be performed
                                                default: 0

        vm_to_restore               (list)  -- VM&#39;s to be restored

        destination_network         (str)   -- Network card name on the destination machine

        power_on                    (bool)  -- To validate destination VM power on and off
                                                default: True

        overwrite                   (bool)  -- To overwrite VM and VHDs in destination path
                                                default: False

        distribute_vm_workload      (int)   -- Virtual machines to be used per job

        datastore                   (str)   -- Datastore to restore the VM

        restored_vm_name            (str)   -- Name used for the VM when restored

        restore_option              (dict)  -- Restore options dictionary with advanced options

        pattern_dict                (dict)  -- Dictionary to generate the live sync schedule

            Sample:

                for after_job_completes :
                {
                    &#34;freq_type&#34;: &#39;after_job_completes&#39;,
                    &#34;active_start_date&#34;: date_in_%m/%d/%y (str),
                    &#34;active_start_time&#34;: time_in_%H/%S (str),
                    &#34;repeat_days&#34;: days_to_repeat (int)
                }

                for daily:
                {
                     &#34;freq_type&#34;: &#39;daily&#39;,
                     &#34;active_start_time&#34;: time_in_%H/%S (str),
                     &#34;repeat_days&#34;: days_to_repeat (int)
                }

                for weekly:
                {
                     &#34;freq_type&#34;: &#39;weekly&#39;,
                     &#34;active_start_time&#34;: time_in_%H/%S (str),
                     &#34;repeat_weeks&#34;: weeks_to_repeat (int)
                     &#34;weekdays&#34;: list of weekdays [&#39;Monday&#39;,&#39;Tuesday&#39;]
                }

                for monthly:
                {
                     &#34;freq_type&#34;: &#39;monthly&#39;,
                     &#34;active_start_time&#34;: time_in_%H/%S (str),
                     &#34;repeat_months&#34;: weeks_to_repeat (int)
                     &#34;on_day&#34;: Day to run schedule (int)
                }

                for yearly:
                {
                     &#34;active_start_time&#34;: time_in_%H/%S (str),
                     &#34;on_month&#34;: month to run schedule (str) January, Febuary...
                     &#34;on_day&#34;: Day to run schedule (int)
                }

    Returns:
        object - instance of the Schedule class for this Live sync

    &#34;&#34;&#34;
    # restore options
    if restore_option is None:
        restore_option = {}

    if not restored_vm_name:
        restored_vm_name = &#34;LiveSync_&#34;
    restore_option[&#39;restore_new_name&#39;] = restored_vm_name

    if copy_precedence:
        restore_option[&#34;copy_precedence_applicable&#34;] = True

    if vm_to_restore:
        vm_to_restore = [vm_to_restore]

    # check mandatory input parameters are correct
    if bool(restore_option):
        if not (isinstance(overwrite, bool) and
                isinstance(power_on, bool)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    # set attr for all the option in restore xml from user inputs
    self._subclient_object._set_restore_inputs(
        restore_option,
        vm_to_restore=self._subclient_object._set_vm_to_restore(vm_to_restore),
        unconditional_overwrite=overwrite,
        power_on=power_on,
        distribute_vm_workload=distribute_vm_workload,
        copy_precedence=copy_precedence,
        volume_level_restore=1,
        vcenter_client=destination_client,
        client_name=proxy_client,
        datastore=datastore,
        destination_network=destination_network,
        in_place=False
    )

    return self._configure_live_sync(schedule_name, restore_option, pattern_dict)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.VsaLiveSync" href="vsa_live_sync.html#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.VsaLiveSync">VsaLiveSync</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.VsaLiveSync.get" href="vsa_live_sync.html#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.VsaLiveSync.get">get</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.VsaLiveSync.has_live_sync_pair" href="vsa_live_sync.html#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.VsaLiveSync.has_live_sync_pair">has_live_sync_pair</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.VsaLiveSync.live_sync_pairs" href="vsa_live_sync.html#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.VsaLiveSync.live_sync_pairs">live_sync_pairs</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.VsaLiveSync.refresh" href="vsa_live_sync.html#cvpysdk.subclients.virtualserver.livesync.vsa_live_sync.VsaLiveSync.refresh">refresh</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.subclients.virtualserver.livesync" href="index.html">cvpysdk.subclients.virtualserver.livesync</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.subclients.virtualserver.livesync.vmware_live_sync.VMWareLiveSync" href="#cvpysdk.subclients.virtualserver.livesync.vmware_live_sync.VMWareLiveSync">VMWareLiveSync</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.subclients.virtualserver.livesync.vmware_live_sync.VMWareLiveSync.configure_live_sync" href="#cvpysdk.subclients.virtualserver.livesync.vmware_live_sync.VMWareLiveSync.configure_live_sync">configure_live_sync</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>