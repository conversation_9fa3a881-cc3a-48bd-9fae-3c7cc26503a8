import requests
import urllib3
from pprint import pprint

# Suppress warnings for insecure requests in a lab environment
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# The full URL for the client API endpoint
#url = "http://192.168.100.55/commandcenter/api/Client" # this is the working endpoint
url = "http://192.168.100.55/commandcenter/api/Session"
# url = "http://192.168.100.55/commandcenter/api/User"

'''
https://documentation.commvault.com/v11/essential/available_web_services_for_rest_api.html
'''

# The authentication token obtained from a previous login request
auth_token = "QSDK 3194920e5bfa16266e2d3bc5083219d30e79f67bc2a1cb7c9c8086227eb222142862198966f949c75757702a130654044dafb5b42ffdcee7f2071dfb13fecb36371c80984394acbcba73e874012c0ea860f4422e05c41b516de667071d1b8182d353cb3577764066658fc4a01656aae8b535558a0a4acbe7ae33d87eab0bf44d3a3368285cd5c7dcff8bf84392662e784ae2f760e4fd4f384a32d4a825d5de1f53ee720a9a975c517c00b6d9ef26789c84d48661ae599111940bb5c7af3a178635e68dec4754f559e2db6f059c247bc36c6421d85de81c4f4"

# Headers for the request, including the token in the Cookie2 header
headers = {
    "Accept": "application/json",
    "Cookie2": auth_token
}

# Make the GET request

response = requests.get(url, headers=headers, verify=False)
response.raise_for_status()  # Raise an exception for HTTP errors (4xx or 5xx)

# Print the response from the server, which is expected to be JSON
print("Status Code:", response.status_code)
# pprint(response.json())
pprint(response.json())