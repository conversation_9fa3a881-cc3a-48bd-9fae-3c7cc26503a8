<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.backupsets.cloudapps.salesforce_backupset API documentation</title>
<meta name="description" content="File for operating on a Salesforce Backupset …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.backupsets.cloudapps.salesforce_backupset</code></h1>
</header>
<section id="section-intro">
<p>File for operating on a Salesforce Backupset.</p>
<p>SalesforceBackupset is the only class defined in this file.</p>
<p>SalesforceBackuset:
Derived class from CloudAppsBackupset Base class, representing a
salesforce backupset, and to perform operations on that backupset</p>
<h2 id="salesforcebackupset">Salesforcebackupset</h2>
<p><strong>init</strong>()
&ndash;
Backupset class method overwritten to add salesforce
browse options in default browse options</p>
<p>_get_backupset_properties()
&ndash;
Backupset class method overwritten to add salesforce
backupset properties as well</p>
<p>_prepare_browse_json()
&ndash;
Backupset class method overwritten to add salesforce
browse option</p>
<p>download_cache_path()
&ndash;
Fetches download cache path from backupset</p>
<p>mutual_auth_path()
&ndash;
Fetches mutual auth path from backupset</p>
<p>salesforce_user_name()
&ndash;
Fetches salesforce user name from backupset</p>
<p>is_sync_db_enabled()
&ndash;
Determines sync database enabled or not on backupset</p>
<p>sync_db_type()
&ndash;
Fetches sync database type from backupset</p>
<p>sync_db_host()
&ndash;
Fetches sync database host name from backupset</p>
<p>sync_db_instance()
&ndash;
Fetches ssync database instance name from backupset</p>
<p>sync_db_name()
&ndash;
Fetches sync database name from backupset</p>
<p>sync_db_port()
&ndash;
Fetches sync database port number from backupset</p>
<p>sync_db_user_name()
&ndash;
Fetches sync database user name from backupset</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/cloudapps/salesforce_backupset.py#L1-L222" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;File for operating on a Salesforce Backupset.

SalesforceBackupset is the only class defined in this file.

SalesforceBackuset:     Derived class from CloudAppsBackupset Base class, representing a
                            salesforce backupset, and to perform operations on that backupset

SalesforceBackupset:
     __init__()                      --    Backupset class method overwritten to add salesforce
                                               browse options in default browse options

    _get_backupset_properties()      --    Backupset class method overwritten to add salesforce
                                               backupset properties as well

    _prepare_browse_json()           --    Backupset class method overwritten to add salesforce
                                               browse option

    download_cache_path()            --    Fetches download cache path from backupset

    mutual_auth_path()               --    Fetches mutual auth path from backupset

    salesforce_user_name()           --    Fetches salesforce user name from backupset

    is_sync_db_enabled()             --    Determines sync database enabled or not on backupset

    sync_db_type()                   --    Fetches sync database type from backupset

    sync_db_host()                   --    Fetches sync database host name from backupset

    sync_db_instance()               --    Fetches ssync database instance name from backupset

    sync_db_name()                   --    Fetches sync database name from backupset

    sync_db_port()                   --    Fetches sync database port number from backupset

    sync_db_user_name()              --    Fetches sync database user name from backupset

&#34;&#34;&#34;

from __future__ import unicode_literals

from ..cabackupset import CloudAppsBackupset


class SalesforceBackupset(CloudAppsBackupset):
    &#34;&#34;&#34;Derived class from CloudAppsBackupset Base class, representing a
        salesforce backupset, and to perform operations on that backupset.
    &#34;&#34;&#34;

    def __init__(self, instance_object, backupset_name, backupset_id=None):
        &#34;&#34;&#34;Initlializes instance of the Backupset class for the Salesforce instance.

            Args:
                instance_object     (object)    --  instance of the Instance class

                backupset_name      (str)       --  name of backupset

                backupset_id        (int)       --  id of backupset

            Returns:
                object - instance of the SalesforceBackupset class

        &#34;&#34;&#34;
        self._download_cache_path = None
        self._mutual_auth_path = None
        self._user_name = None
        self._api_token = None
        self._sync_db_enabled = None
        self._sync_db_type = None
        self._sync_db_host = None
        self._sync_db_instance = None
        self._sync_db_name = None
        self._sync_db_port = None
        self._sync_db_user_name = None
        self._sync_db_user_password = None

        super(SalesforceBackupset, self).__init__(instance_object, backupset_name, backupset_id)

        salesforce_browse_options = {
            &#39;_browse_view_name_list&#39;: [&#39;TBLVIEW&#39;, &#39;FILEVIEW&#39;]
        }

        self._default_browse_options.update(salesforce_browse_options)

    def _get_backupset_properties(self):
        &#34;&#34;&#34;Gets the properties of this backupset.

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        super(SalesforceBackupset, self)._get_backupset_properties()

        if &#39;cloudAppsBackupset&#39; in self._properties:
            cloud_apps_backupset = self._properties[&#39;cloudAppsBackupset&#39;]
            if &#39;salesforceBackupSet&#39; in cloud_apps_backupset:
                sfbackupset = cloud_apps_backupset[&#39;salesforceBackupSet&#39;]
                if &#39;downloadCachePath&#39; in sfbackupset:
                    self._download_cache_path = sfbackupset[&#39;downloadCachePath&#39;]
                self._mutual_auth_path = sfbackupset.get(&#39;mutualAuthPath&#39;, &#39;&#39;)
                if &#39;userName&#39; in sfbackupset[&#39;userPassword&#39;]:
                    self._user_name = sfbackupset[&#39;userPassword&#39;][&#39;userName&#39;]
                if &#39;syncDatabase&#39; in sfbackupset:
                    self._sync_db_enabled = sfbackupset[&#39;syncDatabase&#39;].get(&#39;dbEnabled&#39;, False)
                if self._sync_db_enabled:
                    if &#39;dbType&#39; in sfbackupset[&#39;syncDatabase&#39;]:
                        self._sync_db_type = sfbackupset[&#39;syncDatabase&#39;][&#39;dbType&#39;]
                    if &#39;dbHost&#39; in sfbackupset[&#39;syncDatabase&#39;]:
                        self._sync_db_host = sfbackupset[&#39;syncDatabase&#39;][&#39;dbHost&#39;]
                    if &#39;dbInstance&#39; in sfbackupset[&#39;syncDatabase&#39;]:
                        self._sync_db_instance = sfbackupset[&#39;syncDatabase&#39;][&#39;dbInstance&#39;]
                    if &#39;dbName&#39; in sfbackupset[&#39;syncDatabase&#39;]:
                        self._sync_db_name = sfbackupset[&#39;syncDatabase&#39;][&#39;dbName&#39;]
                    if &#39;dbPort&#39; in sfbackupset[&#39;syncDatabase&#39;]:
                        self._sync_db_port = sfbackupset[&#39;syncDatabase&#39;][&#39;dbPort&#39;]
                    if &#39;userName&#39; in sfbackupset[&#39;syncDatabase&#39;][&#39;dbUserPassword&#39;]:
                        self._sync_db_user_name = sfbackupset[
                            &#39;syncDatabase&#39;][&#39;dbUserPassword&#39;][&#39;userName&#39;]
                    if &#39;password&#39; in sfbackupset[&#39;syncDatabase&#39;][&#39;dbUserPassword&#39;]:
                        self._sync_db_user_password = sfbackupset[
                            &#39;syncDatabase&#39;][&#39;dbUserPassword&#39;][&#39;password&#39;]

    def _prepare_browse_json(self, options):
        &#34;&#34;&#34;Prepares the JSON object for the browse request.

             Args:
                options     (dict)  --  the browse options dictionary

            Returns:
                dict - A JSON object for the browse response

        &#34;&#34;&#34;
        request_json = super(SalesforceBackupset, self)._prepare_browse_json(options)
        salesforce_browse_view = {
            &#39;browseViewNameList&#39;: options[&#39;_browse_view_name_list&#39;]
        }
        request_json[&#39;advOptions&#39;].update(salesforce_browse_view)
        return request_json

    @property
    def download_cache_path(self):
        &#34;&#34;&#34;getter for download cache path&#34;&#34;&#34;
        return self._download_cache_path

    @property
    def mutual_auth_path(self):
        &#34;&#34;&#34;getter for download cache path&#34;&#34;&#34;
        return self._mutual_auth_path

    @property
    def salesforce_user_name(self):
        &#34;&#34;&#34;getter for salesforce user name&#34;&#34;&#34;
        return self._user_name

    @property
    def is_sync_db_enabled(self):
        &#34;&#34;&#34;lets the user know whether sync db enabled or not&#34;&#34;&#34;
        return self._sync_db_enabled

    @property
    def sync_db_type(self):
        &#34;&#34;&#34;getter for the sync database type&#34;&#34;&#34;
        return self._sync_db_type

    @property
    def sync_db_host(self):
        &#34;&#34;&#34;getter for the sync database hostname&#34;&#34;&#34;
        return self._sync_db_host

    @property
    def sync_db_instance(self):
        &#34;&#34;&#34;getter for the sync database instance name&#34;&#34;&#34;
        return self._sync_db_instance

    @property
    def sync_db_name(self):
        &#34;&#34;&#34;getter for the sync database name&#34;&#34;&#34;
        return self._sync_db_name

    @property
    def sync_db_port(self):
        &#34;&#34;&#34;getter for the sync database port number&#34;&#34;&#34;
        return self._sync_db_port

    @property
    def sync_db_user_name(self):
        &#34;&#34;&#34;getter for the sync database user name&#34;&#34;&#34;
        return self._sync_db_user_name

    @mutual_auth_path.setter
    def mutual_auth_path(self, value):
        &#34;&#34;&#34;Sets mutual auth path for the backupset.
        Args:
            value       (str)      --   mutual auth certificate path on access node
        &#34;&#34;&#34;
        if self.mutual_auth_path != value:
            if self.is_sync_db_enabled:
                del self._properties[&#39;cloudAppsBackupset&#39;][&#39;salesforceBackupSet&#39;][&#39;syncDatabase&#39;][&#39;dbUserPassword&#39;][
                    &#39;password&#39;]
            self._properties[&#39;cloudAppsBackupset&#39;][&#39;salesforceBackupSet&#39;][&#39;mutualAuthPath&#39;] = value
            self.update_properties(self._properties)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.backupsets.cloudapps.salesforce_backupset.SalesforceBackupset"><code class="flex name class">
<span>class <span class="ident">SalesforceBackupset</span></span>
<span>(</span><span>instance_object, backupset_name, backupset_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Derived class from CloudAppsBackupset Base class, representing a
salesforce backupset, and to perform operations on that backupset.</p>
<p>Initlializes instance of the Backupset class for the Salesforce instance.</p>
<h2 id="args">Args</h2>
<p>instance_object
(object)
&ndash;
instance of the Instance class</p>
<p>backupset_name
(str)
&ndash;
name of backupset</p>
<p>backupset_id
(int)
&ndash;
id of backupset</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the SalesforceBackupset class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/cloudapps/salesforce_backupset.py#L63-L222" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class SalesforceBackupset(CloudAppsBackupset):
    &#34;&#34;&#34;Derived class from CloudAppsBackupset Base class, representing a
        salesforce backupset, and to perform operations on that backupset.
    &#34;&#34;&#34;

    def __init__(self, instance_object, backupset_name, backupset_id=None):
        &#34;&#34;&#34;Initlializes instance of the Backupset class for the Salesforce instance.

            Args:
                instance_object     (object)    --  instance of the Instance class

                backupset_name      (str)       --  name of backupset

                backupset_id        (int)       --  id of backupset

            Returns:
                object - instance of the SalesforceBackupset class

        &#34;&#34;&#34;
        self._download_cache_path = None
        self._mutual_auth_path = None
        self._user_name = None
        self._api_token = None
        self._sync_db_enabled = None
        self._sync_db_type = None
        self._sync_db_host = None
        self._sync_db_instance = None
        self._sync_db_name = None
        self._sync_db_port = None
        self._sync_db_user_name = None
        self._sync_db_user_password = None

        super(SalesforceBackupset, self).__init__(instance_object, backupset_name, backupset_id)

        salesforce_browse_options = {
            &#39;_browse_view_name_list&#39;: [&#39;TBLVIEW&#39;, &#39;FILEVIEW&#39;]
        }

        self._default_browse_options.update(salesforce_browse_options)

    def _get_backupset_properties(self):
        &#34;&#34;&#34;Gets the properties of this backupset.

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        super(SalesforceBackupset, self)._get_backupset_properties()

        if &#39;cloudAppsBackupset&#39; in self._properties:
            cloud_apps_backupset = self._properties[&#39;cloudAppsBackupset&#39;]
            if &#39;salesforceBackupSet&#39; in cloud_apps_backupset:
                sfbackupset = cloud_apps_backupset[&#39;salesforceBackupSet&#39;]
                if &#39;downloadCachePath&#39; in sfbackupset:
                    self._download_cache_path = sfbackupset[&#39;downloadCachePath&#39;]
                self._mutual_auth_path = sfbackupset.get(&#39;mutualAuthPath&#39;, &#39;&#39;)
                if &#39;userName&#39; in sfbackupset[&#39;userPassword&#39;]:
                    self._user_name = sfbackupset[&#39;userPassword&#39;][&#39;userName&#39;]
                if &#39;syncDatabase&#39; in sfbackupset:
                    self._sync_db_enabled = sfbackupset[&#39;syncDatabase&#39;].get(&#39;dbEnabled&#39;, False)
                if self._sync_db_enabled:
                    if &#39;dbType&#39; in sfbackupset[&#39;syncDatabase&#39;]:
                        self._sync_db_type = sfbackupset[&#39;syncDatabase&#39;][&#39;dbType&#39;]
                    if &#39;dbHost&#39; in sfbackupset[&#39;syncDatabase&#39;]:
                        self._sync_db_host = sfbackupset[&#39;syncDatabase&#39;][&#39;dbHost&#39;]
                    if &#39;dbInstance&#39; in sfbackupset[&#39;syncDatabase&#39;]:
                        self._sync_db_instance = sfbackupset[&#39;syncDatabase&#39;][&#39;dbInstance&#39;]
                    if &#39;dbName&#39; in sfbackupset[&#39;syncDatabase&#39;]:
                        self._sync_db_name = sfbackupset[&#39;syncDatabase&#39;][&#39;dbName&#39;]
                    if &#39;dbPort&#39; in sfbackupset[&#39;syncDatabase&#39;]:
                        self._sync_db_port = sfbackupset[&#39;syncDatabase&#39;][&#39;dbPort&#39;]
                    if &#39;userName&#39; in sfbackupset[&#39;syncDatabase&#39;][&#39;dbUserPassword&#39;]:
                        self._sync_db_user_name = sfbackupset[
                            &#39;syncDatabase&#39;][&#39;dbUserPassword&#39;][&#39;userName&#39;]
                    if &#39;password&#39; in sfbackupset[&#39;syncDatabase&#39;][&#39;dbUserPassword&#39;]:
                        self._sync_db_user_password = sfbackupset[
                            &#39;syncDatabase&#39;][&#39;dbUserPassword&#39;][&#39;password&#39;]

    def _prepare_browse_json(self, options):
        &#34;&#34;&#34;Prepares the JSON object for the browse request.

             Args:
                options     (dict)  --  the browse options dictionary

            Returns:
                dict - A JSON object for the browse response

        &#34;&#34;&#34;
        request_json = super(SalesforceBackupset, self)._prepare_browse_json(options)
        salesforce_browse_view = {
            &#39;browseViewNameList&#39;: options[&#39;_browse_view_name_list&#39;]
        }
        request_json[&#39;advOptions&#39;].update(salesforce_browse_view)
        return request_json

    @property
    def download_cache_path(self):
        &#34;&#34;&#34;getter for download cache path&#34;&#34;&#34;
        return self._download_cache_path

    @property
    def mutual_auth_path(self):
        &#34;&#34;&#34;getter for download cache path&#34;&#34;&#34;
        return self._mutual_auth_path

    @property
    def salesforce_user_name(self):
        &#34;&#34;&#34;getter for salesforce user name&#34;&#34;&#34;
        return self._user_name

    @property
    def is_sync_db_enabled(self):
        &#34;&#34;&#34;lets the user know whether sync db enabled or not&#34;&#34;&#34;
        return self._sync_db_enabled

    @property
    def sync_db_type(self):
        &#34;&#34;&#34;getter for the sync database type&#34;&#34;&#34;
        return self._sync_db_type

    @property
    def sync_db_host(self):
        &#34;&#34;&#34;getter for the sync database hostname&#34;&#34;&#34;
        return self._sync_db_host

    @property
    def sync_db_instance(self):
        &#34;&#34;&#34;getter for the sync database instance name&#34;&#34;&#34;
        return self._sync_db_instance

    @property
    def sync_db_name(self):
        &#34;&#34;&#34;getter for the sync database name&#34;&#34;&#34;
        return self._sync_db_name

    @property
    def sync_db_port(self):
        &#34;&#34;&#34;getter for the sync database port number&#34;&#34;&#34;
        return self._sync_db_port

    @property
    def sync_db_user_name(self):
        &#34;&#34;&#34;getter for the sync database user name&#34;&#34;&#34;
        return self._sync_db_user_name

    @mutual_auth_path.setter
    def mutual_auth_path(self, value):
        &#34;&#34;&#34;Sets mutual auth path for the backupset.
        Args:
            value       (str)      --   mutual auth certificate path on access node
        &#34;&#34;&#34;
        if self.mutual_auth_path != value:
            if self.is_sync_db_enabled:
                del self._properties[&#39;cloudAppsBackupset&#39;][&#39;salesforceBackupSet&#39;][&#39;syncDatabase&#39;][&#39;dbUserPassword&#39;][
                    &#39;password&#39;]
            self._properties[&#39;cloudAppsBackupset&#39;][&#39;salesforceBackupSet&#39;][&#39;mutualAuthPath&#39;] = value
            self.update_properties(self._properties)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.backupsets.cabackupset.CloudAppsBackupset" href="../cabackupset.html#cvpysdk.backupsets.cabackupset.CloudAppsBackupset">CloudAppsBackupset</a></li>
<li><a title="cvpysdk.backupset.Backupset" href="../../backupset.html#cvpysdk.backupset.Backupset">Backupset</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.backupsets.cloudapps.salesforce_backupset.SalesforceBackupset.download_cache_path"><code class="name">var <span class="ident">download_cache_path</span></code></dt>
<dd>
<div class="desc"><p>getter for download cache path</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/cloudapps/salesforce_backupset.py#L161-L164" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def download_cache_path(self):
    &#34;&#34;&#34;getter for download cache path&#34;&#34;&#34;
    return self._download_cache_path</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupsets.cloudapps.salesforce_backupset.SalesforceBackupset.is_sync_db_enabled"><code class="name">var <span class="ident">is_sync_db_enabled</span></code></dt>
<dd>
<div class="desc"><p>lets the user know whether sync db enabled or not</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/cloudapps/salesforce_backupset.py#L176-L179" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_sync_db_enabled(self):
    &#34;&#34;&#34;lets the user know whether sync db enabled or not&#34;&#34;&#34;
    return self._sync_db_enabled</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupsets.cloudapps.salesforce_backupset.SalesforceBackupset.mutual_auth_path"><code class="name">var <span class="ident">mutual_auth_path</span></code></dt>
<dd>
<div class="desc"><p>getter for download cache path</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/cloudapps/salesforce_backupset.py#L166-L169" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def mutual_auth_path(self):
    &#34;&#34;&#34;getter for download cache path&#34;&#34;&#34;
    return self._mutual_auth_path</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupsets.cloudapps.salesforce_backupset.SalesforceBackupset.salesforce_user_name"><code class="name">var <span class="ident">salesforce_user_name</span></code></dt>
<dd>
<div class="desc"><p>getter for salesforce user name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/cloudapps/salesforce_backupset.py#L171-L174" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def salesforce_user_name(self):
    &#34;&#34;&#34;getter for salesforce user name&#34;&#34;&#34;
    return self._user_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupsets.cloudapps.salesforce_backupset.SalesforceBackupset.sync_db_host"><code class="name">var <span class="ident">sync_db_host</span></code></dt>
<dd>
<div class="desc"><p>getter for the sync database hostname</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/cloudapps/salesforce_backupset.py#L186-L189" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def sync_db_host(self):
    &#34;&#34;&#34;getter for the sync database hostname&#34;&#34;&#34;
    return self._sync_db_host</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupsets.cloudapps.salesforce_backupset.SalesforceBackupset.sync_db_instance"><code class="name">var <span class="ident">sync_db_instance</span></code></dt>
<dd>
<div class="desc"><p>getter for the sync database instance name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/cloudapps/salesforce_backupset.py#L191-L194" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def sync_db_instance(self):
    &#34;&#34;&#34;getter for the sync database instance name&#34;&#34;&#34;
    return self._sync_db_instance</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupsets.cloudapps.salesforce_backupset.SalesforceBackupset.sync_db_name"><code class="name">var <span class="ident">sync_db_name</span></code></dt>
<dd>
<div class="desc"><p>getter for the sync database name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/cloudapps/salesforce_backupset.py#L196-L199" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def sync_db_name(self):
    &#34;&#34;&#34;getter for the sync database name&#34;&#34;&#34;
    return self._sync_db_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupsets.cloudapps.salesforce_backupset.SalesforceBackupset.sync_db_port"><code class="name">var <span class="ident">sync_db_port</span></code></dt>
<dd>
<div class="desc"><p>getter for the sync database port number</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/cloudapps/salesforce_backupset.py#L201-L204" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def sync_db_port(self):
    &#34;&#34;&#34;getter for the sync database port number&#34;&#34;&#34;
    return self._sync_db_port</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupsets.cloudapps.salesforce_backupset.SalesforceBackupset.sync_db_type"><code class="name">var <span class="ident">sync_db_type</span></code></dt>
<dd>
<div class="desc"><p>getter for the sync database type</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/cloudapps/salesforce_backupset.py#L181-L184" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def sync_db_type(self):
    &#34;&#34;&#34;getter for the sync database type&#34;&#34;&#34;
    return self._sync_db_type</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupsets.cloudapps.salesforce_backupset.SalesforceBackupset.sync_db_user_name"><code class="name">var <span class="ident">sync_db_user_name</span></code></dt>
<dd>
<div class="desc"><p>getter for the sync database user name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupsets/cloudapps/salesforce_backupset.py#L206-L209" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def sync_db_user_name(self):
    &#34;&#34;&#34;getter for the sync database user name&#34;&#34;&#34;
    return self._sync_db_user_name</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.backupsets.cabackupset.CloudAppsBackupset" href="../cabackupset.html#cvpysdk.backupsets.cabackupset.CloudAppsBackupset">CloudAppsBackupset</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.backupsets.cabackupset.CloudAppsBackupset.backed_up_files_count" href="../../backupset.html#cvpysdk.backupset.Backupset.backed_up_files_count">backed_up_files_count</a></code></li>
<li><code><a title="cvpysdk.backupsets.cabackupset.CloudAppsBackupset.backup" href="../../backupset.html#cvpysdk.backupset.Backupset.backup">backup</a></code></li>
<li><code><a title="cvpysdk.backupsets.cabackupset.CloudAppsBackupset.backupset_id" href="../../backupset.html#cvpysdk.backupset.Backupset.backupset_id">backupset_id</a></code></li>
<li><code><a title="cvpysdk.backupsets.cabackupset.CloudAppsBackupset.backupset_name" href="../../backupset.html#cvpysdk.backupset.Backupset.backupset_name">backupset_name</a></code></li>
<li><code><a title="cvpysdk.backupsets.cabackupset.CloudAppsBackupset.browse" href="../../backupset.html#cvpysdk.backupset.Backupset.browse">browse</a></code></li>
<li><code><a title="cvpysdk.backupsets.cabackupset.CloudAppsBackupset.delete_data" href="../../backupset.html#cvpysdk.backupset.Backupset.delete_data">delete_data</a></code></li>
<li><code><a title="cvpysdk.backupsets.cabackupset.CloudAppsBackupset.description" href="../../backupset.html#cvpysdk.backupset.Backupset.description">description</a></code></li>
<li><code><a title="cvpysdk.backupsets.cabackupset.CloudAppsBackupset.find" href="../../backupset.html#cvpysdk.backupset.Backupset.find">find</a></code></li>
<li><code><a title="cvpysdk.backupsets.cabackupset.CloudAppsBackupset.guid" href="../../backupset.html#cvpysdk.backupset.Backupset.guid">guid</a></code></li>
<li><code><a title="cvpysdk.backupsets.cabackupset.CloudAppsBackupset.is_default_backupset" href="../../backupset.html#cvpysdk.backupset.Backupset.is_default_backupset">is_default_backupset</a></code></li>
<li><code><a title="cvpysdk.backupsets.cabackupset.CloudAppsBackupset.is_on_demand_backupset" href="../../backupset.html#cvpysdk.backupset.Backupset.is_on_demand_backupset">is_on_demand_backupset</a></code></li>
<li><code><a title="cvpysdk.backupsets.cabackupset.CloudAppsBackupset.list_media" href="../../backupset.html#cvpysdk.backupset.Backupset.list_media">list_media</a></code></li>
<li><code><a title="cvpysdk.backupsets.cabackupset.CloudAppsBackupset.name" href="../../backupset.html#cvpysdk.backupset.Backupset.name">name</a></code></li>
<li><code><a title="cvpysdk.backupsets.cabackupset.CloudAppsBackupset.plan" href="../../backupset.html#cvpysdk.backupset.Backupset.plan">plan</a></code></li>
<li><code><a title="cvpysdk.backupsets.cabackupset.CloudAppsBackupset.properties" href="../../backupset.html#cvpysdk.backupset.Backupset.properties">properties</a></code></li>
<li><code><a title="cvpysdk.backupsets.cabackupset.CloudAppsBackupset.refresh" href="../../backupset.html#cvpysdk.backupset.Backupset.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.backupsets.cabackupset.CloudAppsBackupset.set_default_backupset" href="../../backupset.html#cvpysdk.backupset.Backupset.set_default_backupset">set_default_backupset</a></code></li>
<li><code><a title="cvpysdk.backupsets.cabackupset.CloudAppsBackupset.update_properties" href="../../backupset.html#cvpysdk.backupset.Backupset.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.backupsets.cloudapps" href="index.html">cvpysdk.backupsets.cloudapps</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.backupsets.cloudapps.salesforce_backupset.SalesforceBackupset" href="#cvpysdk.backupsets.cloudapps.salesforce_backupset.SalesforceBackupset">SalesforceBackupset</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.backupsets.cloudapps.salesforce_backupset.SalesforceBackupset.download_cache_path" href="#cvpysdk.backupsets.cloudapps.salesforce_backupset.SalesforceBackupset.download_cache_path">download_cache_path</a></code></li>
<li><code><a title="cvpysdk.backupsets.cloudapps.salesforce_backupset.SalesforceBackupset.is_sync_db_enabled" href="#cvpysdk.backupsets.cloudapps.salesforce_backupset.SalesforceBackupset.is_sync_db_enabled">is_sync_db_enabled</a></code></li>
<li><code><a title="cvpysdk.backupsets.cloudapps.salesforce_backupset.SalesforceBackupset.mutual_auth_path" href="#cvpysdk.backupsets.cloudapps.salesforce_backupset.SalesforceBackupset.mutual_auth_path">mutual_auth_path</a></code></li>
<li><code><a title="cvpysdk.backupsets.cloudapps.salesforce_backupset.SalesforceBackupset.salesforce_user_name" href="#cvpysdk.backupsets.cloudapps.salesforce_backupset.SalesforceBackupset.salesforce_user_name">salesforce_user_name</a></code></li>
<li><code><a title="cvpysdk.backupsets.cloudapps.salesforce_backupset.SalesforceBackupset.sync_db_host" href="#cvpysdk.backupsets.cloudapps.salesforce_backupset.SalesforceBackupset.sync_db_host">sync_db_host</a></code></li>
<li><code><a title="cvpysdk.backupsets.cloudapps.salesforce_backupset.SalesforceBackupset.sync_db_instance" href="#cvpysdk.backupsets.cloudapps.salesforce_backupset.SalesforceBackupset.sync_db_instance">sync_db_instance</a></code></li>
<li><code><a title="cvpysdk.backupsets.cloudapps.salesforce_backupset.SalesforceBackupset.sync_db_name" href="#cvpysdk.backupsets.cloudapps.salesforce_backupset.SalesforceBackupset.sync_db_name">sync_db_name</a></code></li>
<li><code><a title="cvpysdk.backupsets.cloudapps.salesforce_backupset.SalesforceBackupset.sync_db_port" href="#cvpysdk.backupsets.cloudapps.salesforce_backupset.SalesforceBackupset.sync_db_port">sync_db_port</a></code></li>
<li><code><a title="cvpysdk.backupsets.cloudapps.salesforce_backupset.SalesforceBackupset.sync_db_type" href="#cvpysdk.backupsets.cloudapps.salesforce_backupset.SalesforceBackupset.sync_db_type">sync_db_type</a></code></li>
<li><code><a title="cvpysdk.backupsets.cloudapps.salesforce_backupset.SalesforceBackupset.sync_db_user_name" href="#cvpysdk.backupsets.cloudapps.salesforce_backupset.SalesforceBackupset.sync_db_user_name">sync_db_user_name</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>