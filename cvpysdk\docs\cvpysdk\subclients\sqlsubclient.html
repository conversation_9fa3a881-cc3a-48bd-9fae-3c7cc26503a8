<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.subclients.sqlsubclient API documentation</title>
<meta name="description" content="File for operating on a SQL Server Subclient …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.subclients.sqlsubclient</code></h1>
</header>
<section id="section-intro">
<p>File for operating on a SQL Server Subclient</p>
<p>SQLServerSubclient is the only class defined in this file.</p>
<p>SQLServerSubclient: Derived class from Subclient Base class, representing a sql server subclient,
and to perform operations on that subclient</p>
<h2 id="sqlserversubclient">Sqlserversubclient</h2>
<p>_get_subclient_properties()
&ndash;
gets the subclient related properties of SQL subclient.</p>
<p>_get_subclient_properties_json()
&ndash;
gets all the subclient related properties of SQL subclient.</p>
<p>content()
&ndash;
sets the content of the subclient.</p>
<p>log_backup_storage_policy()
&ndash;
updates the log backup storage policy for this subclient.</p>
<p>backup()
&ndash;
run a backup job for the subclient.</p>
<p>update_content()
&ndash;
add, delete, overwrite the sql server subclient contents.</p>
<p>blocklevel_backup_option
&ndash;
setter for block level backup option on SQL subclient</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sqlsubclient.py#L1-L394" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-
# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;File for operating on a SQL Server Subclient

SQLServerSubclient is the only class defined in this file.

SQLServerSubclient: Derived class from Subclient Base class, representing a sql server subclient,
and to perform operations on that subclient

SQLServerSubclient:

    _get_subclient_properties()         --  gets the subclient related properties of SQL subclient.

    _get_subclient_properties_json()    --  gets all the subclient related properties of SQL subclient.

    content()                           --  sets the content of the subclient.

    log_backup_storage_policy()         --  updates the log backup storage policy for this subclient.

    backup()                            --  run a backup job for the subclient.

    update_content()                    --  add, delete, overwrite the sql server subclient contents.

    blocklevel_backup_option            --  setter for block level backup option on SQL subclient

&#34;&#34;&#34;

from __future__ import unicode_literals

from .dbsubclient import DatabaseSubclient
from ..exception import SDKException


class SQLServerSubclient(DatabaseSubclient):
    &#34;&#34;&#34;Derived class from Subclient Base class, representing a sql server subclient,
        and to perform operations on that subclient.&#34;&#34;&#34;

    def _get_subclient_properties(self):
        &#34;&#34;&#34;Gets the subclient  related properties of SQL Server subclient.

        &#34;&#34;&#34;
        super(DatabaseSubclient, self)._get_subclient_properties()

        self._mssql_subclient_prop = self._subclient_properties.get(&#39;mssqlSubClientProp&#39;, {})
        self._content = self._subclient_properties.get(&#39;content&#39;, {})
        self._is_file_group_subclient = self._mssql_subclient_prop.get(&#39;sqlSubclientType&#39;, False) == 2

    def _get_subclient_properties_json(self):
        &#34;&#34;&#34;Get the all subclient related properties of this subclient.

           Returns:
                dict - all subclient properties put inside a dict

        &#34;&#34;&#34;
        subclient_json = {
            &#34;subClientProperties&#34;:
                {
                    &#34;proxyClient&#34;: self._proxyClient,
                    &#34;subClientEntity&#34;: self._subClientEntity,
                    &#34;mssqlSubClientProp&#34;: self._mssql_subclient_prop,
                    &#34;content&#34;: self._content,
                    &#34;commonProperties&#34;: self._commonProperties,
                    &#34;contentOperationType&#34;: 1
                }
        }
        return subclient_json

    @property
    def content(self):
        &#34;&#34;&#34;Gets the appropriate content from the Subclient relevant to the user.

            Args:
                subclient_properties (dict)  --  dictionary contatining the properties of
                                                     subclient

            Returns:
                list - list of content associated with the subclient
        &#34;&#34;&#34;
        contents = []

        if &#39;content&#39; in self._subclient_properties:
            subclient_content = self._subclient_properties[&#39;content&#39;]
        else:
            return []

        database_name = None
        content_list = []

        if &#39;mssqlFFGDBName&#39; in self._subclient_properties[&#39;mssqlSubClientProp&#39;]:
            database_name = self._subclient_properties[&#39;mssqlSubClientProp&#39;][&#39;mssqlFFGDBName&#39;]

        for content in subclient_content:
            if &#39;mssqlDbContent&#39; in content:
                content_list.append(content[&#34;mssqlDbContent&#34;][&#34;databaseName&#34;])
            elif &#39;mssqlFGContent&#39; in content:
                content_list.append(content[&#39;mssqlFGContent&#39;][&#39;databaseName&#39;])

        if self._is_file_group_subclient:
            contents.append(database_name)
            contents.append(content_list)
        else:
            contents = content_list

        return contents

    @content.setter
    def content(self, subclient_content):
        &#34;&#34;&#34;Creates the list of content JSON to pass to the API to add a new sql server Subclient
            with the content passed in subclient content.

            Args:
                subclient_content (list)  --  list of the content to add to the subclient

            Returns:
                list - list of the appropriate JSON for an agent to send to the POST Subclient API
        &#34;&#34;&#34;
        content = []

        if self._is_file_group_subclient:
            err_message = &#39;Content addition is not supported for FILE/ FILE GROUP subclient.&#39;
            &#39;Please use Commcell Console to update the content.&#39;
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, err_message)
        else:
            for database_name in subclient_content:
                sql_server_dict = {
                    &#34;mssqlDbContent&#34;: {
                        &#34;databaseName&#34;: database_name
                    }
                }
                content.append(sql_server_dict)

        self._set_subclient_properties(&#34;_content&#34;, content)

    @property
    def browse(self):
        raise AttributeError(&#34;&#39;{0}&#39; object has no attribute &#39;{1}&#39;&#34;.format(
            self.__class__.__name__,
            &#39;browse&#39;
        ))

    @property
    def browse_in_time(self):
        raise AttributeError(&#34;&#39;{0}&#39; object has no attribute &#39;{1}&#39;&#34;.format(
            self.__class__.__name__,
            &#39;browse_in_time&#39;
        ))

    @property
    def find(self):
        raise AttributeError(&#34;&#39;{0}&#39; object has no attribute &#39;{1}&#39;&#34;.format(
            self.__class__.__name__,
            &#39;find&#39;
        ))

    @property
    def restore_in_place(self):
        raise AttributeError(&#34;&#39;{0}&#39; object has no attribute &#39;{1}&#39;&#34;.format(
            self.__class__.__name__,
            &#39;restore_in_place&#39;
        ))

    @property
    def restore_out_of_place(self):
        raise AttributeError(&#34;&#39;{0}&#39; object has no attribute &#39;{1}&#39;&#34;.format(
            self.__class__.__name__,
            &#39;restore_out_of_place&#39;
        ))

    def backup(
            self,
            backup_level=&#34;Differential&#34;,
            data_options=[],
            schedule_pattern=None
    ):
        &#34;&#34;&#34;Runs a backup job for the subclient of the level specified.

            Args:
                backup_level    (str)   --  level of backup the user wish to run
                        Full / Transaction_Log / Differential
                    default: Differential

                data_options    (list)  --  List of options to be enabled on backup

                The accepted string values are:
                    * start_log_backup_after_successfull_backup
                    * copy_only
                    * allow_diff_backup_on_read_only
                    * partial_sql_backup
                    * tail_log_backup
                    * use_sql_compression
                    * checksum
                    * continue_after_error

                    default: []

                schedule_pattern (dict) -- scheduling options to be included for the task

                        Please refer schedules.schedulePattern.createSchedule()
                                                                    doc for the types of Jsons

            Returns:
                object - instance of the Job class for this backup job if its an immediate Job

                         instance of the Schedule class for the backup job if its a scheduled Job

            Raises:
                SDKException:
                    if backup level specified is not correct

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        advanced_options = {}
        backup_level = backup_level.lower()

        if backup_level not in [&#39;full&#39;, &#39;transaction_log&#39;, &#39;differential&#39;]:
            raise SDKException(&#39;Subclient&#39;, &#39;103&#39;)

        if data_options or schedule_pattern:
            if data_options:
                invalid_full_data_opts = [&#39;tail_log_backup&#39;, &#39;allow_diff_backup_on_read_only&#39;]
                invalid_transaction_log_data_opts = [
                    &#39;start_log_backup_after_successfull_backup&#39;,
                    &#39;allow_diff_backup_on_read_only&#39;,
                    &#39;copy_only&#39;]
                invalid_differential_data_opts = [&#39;tail_log_backup&#39;, &#39;copy_only&#39;]

                if &#39;checksum&#39; in data_options and &#39;use_sql_compression&#39; in data_options:
                    raise ValueError(&#34;checksum or use_sql_compression can be enabled , but not both&#34;)
                if backup_level == &#39;full&#39; and any(option in data_options for option in invalid_full_data_opts):
                    raise ValueError(&#34;{0} are not applicable for full backup&#34;.format(invalid_full_data_opts))
                elif backup_level == &#39;transaction_log&#39; and any(option in data_options
                                                               for option in invalid_transaction_log_data_opts):
                    raise ValueError(&#34;{0} are not applicable for Transaction log backup&#34;.format(
                        invalid_transaction_log_data_opts))
                elif backup_level == &#39;differential&#39; and any(option in data_options
                                                            for option in invalid_differential_data_opts):
                    raise ValueError(&#34;{0} are not applicable for full backup&#34;.format(invalid_differential_data_opts))

                advanced_options[&#34;dataOpt&#34;] = {
                    &#34;enableIndexCheckPointing&#34;: False,
                    &#34;verifySynthFull&#34;: True,
                    &#34;startLogBackupAfterSuccessfullBackup&#34;:
                        &#34;start_log_backup_after_successfull_backup&#34; in data_options,
                    &#34;tailLogBackup&#34;: &#34;tail_log_backup&#34; in data_options,
                    &#34;partailSqlBkp&#34;: &#34;partial_sql_backup&#34; in data_options,
                    &#34;useSqlCompression&#34;: &#34;use_sql_compression&#34; in data_options,
                    &#34;useCatalogServer&#34;: False,
                    &#34;enforceTransactionLogUsage&#34;: False,
                    &#34;copyOnly&#34;: &#34;copy_only&#34; in data_options,
                    &#34;skipConsistencyCheck&#34;: False,
                    &#34;skipCatalogPhaseForSnapBackup&#34;: True,
                    &#34;runIntegrityCheck&#34;: False,
                    &#34;checksum&#34;: &#34;checksum&#34; in data_options,
                    &#34;continueaftererror&#34;: &#34;continue_after_error&#34; in data_options,
                    &#34;allowDiffBackupOnReadOnly&#34;: &#34;allow_diff_backup_on_read_only&#34; in data_options
                }

            request_json = self._backup_json(
                backup_level,
                False,
                &#34;BEFORE_SYNTH&#34;,
                advanced_options,
                schedule_pattern
            )

            backup_service = self._services[&#39;CREATE_TASK&#39;]

            flag, response = self._cvpysdk_object.make_request(
                &#39;POST&#39;, backup_service, request_json
            )

            return self._process_backup_response(flag, response)

        return super(SQLServerSubclient, self).backup(
            backup_level=backup_level,
        )

    @property
    def mssql_subclient_prop(self):
        &#34;&#34;&#34; getter for sql server subclient properties &#34;&#34;&#34;
        return self._mssql_subclient_prop

    @mssql_subclient_prop.setter
    def mssql_subclient_prop(self, value):
        &#34;&#34;&#34;

            Args:
                value (list)  --  list of the category and properties to update on the subclient

            Returns:
                list - list of the appropriate JSON for an agent to send to the POST Subclient API
        &#34;&#34;&#34;
        category, prop = value

        if self._is_file_group_subclient:
            err_message = &#39;Updating properties is not supported for FILE/ FILE GROUP subclient.&#39;
            &#39;Please use Commcell Console to update the subclient.&#39;
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, err_message)

        self._set_subclient_properties(category, prop)

    def update_content(self, subclient_content, action):
        &#34;&#34;&#34;Updates the sql server subclient contents with supplied content list.

            Args:
                subclient_content (list)  --  list of the content to add to the subclient

                action (int)  --   action to perform on subclient
                1: OVERWRITE, 2: ADD, 3: DELETE

            Returns:
                list - list of the appropriate JSON to send to the POST Subclient API
        &#34;&#34;&#34;
        request_json = self._get_subclient_properties_json()
        content_list = []

        if self._is_file_group_subclient:
            err_message = &#39;Content modification is not supported for FILE/ FILE GROUP subclient.&#39;
            &#39;Please use Commcell Console to update the content.&#39;
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, err_message)
        else:
            for database_name in subclient_content:
                sql_server_dict = {
                    &#34;mssqlDbContent&#34;: {
                        &#34;databaseName&#34;: database_name
                    }
                }
                content_list.append(sql_server_dict)
        request_json[&#39;subClientProperties&#39;][&#39;content&#39;] = content_list

        content_op_dict = {
            &#34;contentOperationType&#34;: action
        }
        request_json[&#39;subClientProperties&#39;].update(content_op_dict)

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._SUBCLIENT, request_json
        )

        output = self._process_update_response(flag, response)

        if output[0]:
            return
        else:
            o_str = &#39;Failed to update content of subclient\nError: &#34;{0}&#34;&#39;
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str.format(output[2]))

    @property
    def blocklevel_backup_option(self):
        &#34;&#34;&#34;returns True if block level backup is enabled else returns false

            Returns:
                bool - boolean value based on blocklevel enable status

                    True if block level is enabled
                    False if block level is not enabled

        &#34;&#34;&#34;
        return bool(
            self._subclient_properties.get(
                &#39;mssqlSubClientProp&#39;, {}).get(&#39;useBlockLevelBackupWithOptimizedRecovery&#39;, False))

    @blocklevel_backup_option.setter
    def blocklevel_backup_option(self, value):
        &#34;&#34;&#34;Enables or disables block level option on SQL subclient

            Args:
                value (bool)  --  Boolean value whether to set block level option on or off

        &#34;&#34;&#34;

        if self._is_file_group_subclient:
            err_message = &#39;Updating properties is not supported for FILE/ FILE GROUP subclient.&#39;
            &#39;Please use Commcell Console to update the subclient.&#39;
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, err_message)

        self._set_subclient_properties(&#34;_mssql_subclient_prop[&#39;useBlockLevelBackupWithOptimizedRecovery&#39;]&#34;, value)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.subclients.sqlsubclient.SQLServerSubclient"><code class="flex name class">
<span>class <span class="ident">SQLServerSubclient</span></span>
<span>(</span><span>backupset_object, subclient_name, subclient_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Derived class from Subclient Base class, representing a sql server subclient,
and to perform operations on that subclient.</p>
<p>Initialise the Subclient object.</p>
<h2 id="args">Args</h2>
<p>backupset_object (object)
&ndash;
instance of the Backupset class</p>
<p>subclient_name
(str)
&ndash;
name of the subclient</p>
<p>subclient_id
(str)
&ndash;
id of the subclient
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Subclient class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sqlsubclient.py#L49-L394" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class SQLServerSubclient(DatabaseSubclient):
    &#34;&#34;&#34;Derived class from Subclient Base class, representing a sql server subclient,
        and to perform operations on that subclient.&#34;&#34;&#34;

    def _get_subclient_properties(self):
        &#34;&#34;&#34;Gets the subclient  related properties of SQL Server subclient.

        &#34;&#34;&#34;
        super(DatabaseSubclient, self)._get_subclient_properties()

        self._mssql_subclient_prop = self._subclient_properties.get(&#39;mssqlSubClientProp&#39;, {})
        self._content = self._subclient_properties.get(&#39;content&#39;, {})
        self._is_file_group_subclient = self._mssql_subclient_prop.get(&#39;sqlSubclientType&#39;, False) == 2

    def _get_subclient_properties_json(self):
        &#34;&#34;&#34;Get the all subclient related properties of this subclient.

           Returns:
                dict - all subclient properties put inside a dict

        &#34;&#34;&#34;
        subclient_json = {
            &#34;subClientProperties&#34;:
                {
                    &#34;proxyClient&#34;: self._proxyClient,
                    &#34;subClientEntity&#34;: self._subClientEntity,
                    &#34;mssqlSubClientProp&#34;: self._mssql_subclient_prop,
                    &#34;content&#34;: self._content,
                    &#34;commonProperties&#34;: self._commonProperties,
                    &#34;contentOperationType&#34;: 1
                }
        }
        return subclient_json

    @property
    def content(self):
        &#34;&#34;&#34;Gets the appropriate content from the Subclient relevant to the user.

            Args:
                subclient_properties (dict)  --  dictionary contatining the properties of
                                                     subclient

            Returns:
                list - list of content associated with the subclient
        &#34;&#34;&#34;
        contents = []

        if &#39;content&#39; in self._subclient_properties:
            subclient_content = self._subclient_properties[&#39;content&#39;]
        else:
            return []

        database_name = None
        content_list = []

        if &#39;mssqlFFGDBName&#39; in self._subclient_properties[&#39;mssqlSubClientProp&#39;]:
            database_name = self._subclient_properties[&#39;mssqlSubClientProp&#39;][&#39;mssqlFFGDBName&#39;]

        for content in subclient_content:
            if &#39;mssqlDbContent&#39; in content:
                content_list.append(content[&#34;mssqlDbContent&#34;][&#34;databaseName&#34;])
            elif &#39;mssqlFGContent&#39; in content:
                content_list.append(content[&#39;mssqlFGContent&#39;][&#39;databaseName&#39;])

        if self._is_file_group_subclient:
            contents.append(database_name)
            contents.append(content_list)
        else:
            contents = content_list

        return contents

    @content.setter
    def content(self, subclient_content):
        &#34;&#34;&#34;Creates the list of content JSON to pass to the API to add a new sql server Subclient
            with the content passed in subclient content.

            Args:
                subclient_content (list)  --  list of the content to add to the subclient

            Returns:
                list - list of the appropriate JSON for an agent to send to the POST Subclient API
        &#34;&#34;&#34;
        content = []

        if self._is_file_group_subclient:
            err_message = &#39;Content addition is not supported for FILE/ FILE GROUP subclient.&#39;
            &#39;Please use Commcell Console to update the content.&#39;
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, err_message)
        else:
            for database_name in subclient_content:
                sql_server_dict = {
                    &#34;mssqlDbContent&#34;: {
                        &#34;databaseName&#34;: database_name
                    }
                }
                content.append(sql_server_dict)

        self._set_subclient_properties(&#34;_content&#34;, content)

    @property
    def browse(self):
        raise AttributeError(&#34;&#39;{0}&#39; object has no attribute &#39;{1}&#39;&#34;.format(
            self.__class__.__name__,
            &#39;browse&#39;
        ))

    @property
    def browse_in_time(self):
        raise AttributeError(&#34;&#39;{0}&#39; object has no attribute &#39;{1}&#39;&#34;.format(
            self.__class__.__name__,
            &#39;browse_in_time&#39;
        ))

    @property
    def find(self):
        raise AttributeError(&#34;&#39;{0}&#39; object has no attribute &#39;{1}&#39;&#34;.format(
            self.__class__.__name__,
            &#39;find&#39;
        ))

    @property
    def restore_in_place(self):
        raise AttributeError(&#34;&#39;{0}&#39; object has no attribute &#39;{1}&#39;&#34;.format(
            self.__class__.__name__,
            &#39;restore_in_place&#39;
        ))

    @property
    def restore_out_of_place(self):
        raise AttributeError(&#34;&#39;{0}&#39; object has no attribute &#39;{1}&#39;&#34;.format(
            self.__class__.__name__,
            &#39;restore_out_of_place&#39;
        ))

    def backup(
            self,
            backup_level=&#34;Differential&#34;,
            data_options=[],
            schedule_pattern=None
    ):
        &#34;&#34;&#34;Runs a backup job for the subclient of the level specified.

            Args:
                backup_level    (str)   --  level of backup the user wish to run
                        Full / Transaction_Log / Differential
                    default: Differential

                data_options    (list)  --  List of options to be enabled on backup

                The accepted string values are:
                    * start_log_backup_after_successfull_backup
                    * copy_only
                    * allow_diff_backup_on_read_only
                    * partial_sql_backup
                    * tail_log_backup
                    * use_sql_compression
                    * checksum
                    * continue_after_error

                    default: []

                schedule_pattern (dict) -- scheduling options to be included for the task

                        Please refer schedules.schedulePattern.createSchedule()
                                                                    doc for the types of Jsons

            Returns:
                object - instance of the Job class for this backup job if its an immediate Job

                         instance of the Schedule class for the backup job if its a scheduled Job

            Raises:
                SDKException:
                    if backup level specified is not correct

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        advanced_options = {}
        backup_level = backup_level.lower()

        if backup_level not in [&#39;full&#39;, &#39;transaction_log&#39;, &#39;differential&#39;]:
            raise SDKException(&#39;Subclient&#39;, &#39;103&#39;)

        if data_options or schedule_pattern:
            if data_options:
                invalid_full_data_opts = [&#39;tail_log_backup&#39;, &#39;allow_diff_backup_on_read_only&#39;]
                invalid_transaction_log_data_opts = [
                    &#39;start_log_backup_after_successfull_backup&#39;,
                    &#39;allow_diff_backup_on_read_only&#39;,
                    &#39;copy_only&#39;]
                invalid_differential_data_opts = [&#39;tail_log_backup&#39;, &#39;copy_only&#39;]

                if &#39;checksum&#39; in data_options and &#39;use_sql_compression&#39; in data_options:
                    raise ValueError(&#34;checksum or use_sql_compression can be enabled , but not both&#34;)
                if backup_level == &#39;full&#39; and any(option in data_options for option in invalid_full_data_opts):
                    raise ValueError(&#34;{0} are not applicable for full backup&#34;.format(invalid_full_data_opts))
                elif backup_level == &#39;transaction_log&#39; and any(option in data_options
                                                               for option in invalid_transaction_log_data_opts):
                    raise ValueError(&#34;{0} are not applicable for Transaction log backup&#34;.format(
                        invalid_transaction_log_data_opts))
                elif backup_level == &#39;differential&#39; and any(option in data_options
                                                            for option in invalid_differential_data_opts):
                    raise ValueError(&#34;{0} are not applicable for full backup&#34;.format(invalid_differential_data_opts))

                advanced_options[&#34;dataOpt&#34;] = {
                    &#34;enableIndexCheckPointing&#34;: False,
                    &#34;verifySynthFull&#34;: True,
                    &#34;startLogBackupAfterSuccessfullBackup&#34;:
                        &#34;start_log_backup_after_successfull_backup&#34; in data_options,
                    &#34;tailLogBackup&#34;: &#34;tail_log_backup&#34; in data_options,
                    &#34;partailSqlBkp&#34;: &#34;partial_sql_backup&#34; in data_options,
                    &#34;useSqlCompression&#34;: &#34;use_sql_compression&#34; in data_options,
                    &#34;useCatalogServer&#34;: False,
                    &#34;enforceTransactionLogUsage&#34;: False,
                    &#34;copyOnly&#34;: &#34;copy_only&#34; in data_options,
                    &#34;skipConsistencyCheck&#34;: False,
                    &#34;skipCatalogPhaseForSnapBackup&#34;: True,
                    &#34;runIntegrityCheck&#34;: False,
                    &#34;checksum&#34;: &#34;checksum&#34; in data_options,
                    &#34;continueaftererror&#34;: &#34;continue_after_error&#34; in data_options,
                    &#34;allowDiffBackupOnReadOnly&#34;: &#34;allow_diff_backup_on_read_only&#34; in data_options
                }

            request_json = self._backup_json(
                backup_level,
                False,
                &#34;BEFORE_SYNTH&#34;,
                advanced_options,
                schedule_pattern
            )

            backup_service = self._services[&#39;CREATE_TASK&#39;]

            flag, response = self._cvpysdk_object.make_request(
                &#39;POST&#39;, backup_service, request_json
            )

            return self._process_backup_response(flag, response)

        return super(SQLServerSubclient, self).backup(
            backup_level=backup_level,
        )

    @property
    def mssql_subclient_prop(self):
        &#34;&#34;&#34; getter for sql server subclient properties &#34;&#34;&#34;
        return self._mssql_subclient_prop

    @mssql_subclient_prop.setter
    def mssql_subclient_prop(self, value):
        &#34;&#34;&#34;

            Args:
                value (list)  --  list of the category and properties to update on the subclient

            Returns:
                list - list of the appropriate JSON for an agent to send to the POST Subclient API
        &#34;&#34;&#34;
        category, prop = value

        if self._is_file_group_subclient:
            err_message = &#39;Updating properties is not supported for FILE/ FILE GROUP subclient.&#39;
            &#39;Please use Commcell Console to update the subclient.&#39;
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, err_message)

        self._set_subclient_properties(category, prop)

    def update_content(self, subclient_content, action):
        &#34;&#34;&#34;Updates the sql server subclient contents with supplied content list.

            Args:
                subclient_content (list)  --  list of the content to add to the subclient

                action (int)  --   action to perform on subclient
                1: OVERWRITE, 2: ADD, 3: DELETE

            Returns:
                list - list of the appropriate JSON to send to the POST Subclient API
        &#34;&#34;&#34;
        request_json = self._get_subclient_properties_json()
        content_list = []

        if self._is_file_group_subclient:
            err_message = &#39;Content modification is not supported for FILE/ FILE GROUP subclient.&#39;
            &#39;Please use Commcell Console to update the content.&#39;
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, err_message)
        else:
            for database_name in subclient_content:
                sql_server_dict = {
                    &#34;mssqlDbContent&#34;: {
                        &#34;databaseName&#34;: database_name
                    }
                }
                content_list.append(sql_server_dict)
        request_json[&#39;subClientProperties&#39;][&#39;content&#39;] = content_list

        content_op_dict = {
            &#34;contentOperationType&#34;: action
        }
        request_json[&#39;subClientProperties&#39;].update(content_op_dict)

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._SUBCLIENT, request_json
        )

        output = self._process_update_response(flag, response)

        if output[0]:
            return
        else:
            o_str = &#39;Failed to update content of subclient\nError: &#34;{0}&#34;&#39;
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str.format(output[2]))

    @property
    def blocklevel_backup_option(self):
        &#34;&#34;&#34;returns True if block level backup is enabled else returns false

            Returns:
                bool - boolean value based on blocklevel enable status

                    True if block level is enabled
                    False if block level is not enabled

        &#34;&#34;&#34;
        return bool(
            self._subclient_properties.get(
                &#39;mssqlSubClientProp&#39;, {}).get(&#39;useBlockLevelBackupWithOptimizedRecovery&#39;, False))

    @blocklevel_backup_option.setter
    def blocklevel_backup_option(self, value):
        &#34;&#34;&#34;Enables or disables block level option on SQL subclient

            Args:
                value (bool)  --  Boolean value whether to set block level option on or off

        &#34;&#34;&#34;

        if self._is_file_group_subclient:
            err_message = &#39;Updating properties is not supported for FILE/ FILE GROUP subclient.&#39;
            &#39;Please use Commcell Console to update the subclient.&#39;
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, err_message)

        self._set_subclient_properties(&#34;_mssql_subclient_prop[&#39;useBlockLevelBackupWithOptimizedRecovery&#39;]&#34;, value)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient" href="dbsubclient.html#cvpysdk.subclients.dbsubclient.DatabaseSubclient">DatabaseSubclient</a></li>
<li><a title="cvpysdk.subclient.Subclient" href="../subclient.html#cvpysdk.subclient.Subclient">Subclient</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.subclients.sqlsubclient.SQLServerSubclient.blocklevel_backup_option"><code class="name">var <span class="ident">blocklevel_backup_option</span></code></dt>
<dd>
<div class="desc"><p>returns True if block level backup is enabled else returns false</p>
<h2 id="returns">Returns</h2>
<p>bool - boolean value based on blocklevel enable status</p>
<pre><code>True if block level is enabled
False if block level is not enabled
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sqlsubclient.py#L365-L378" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def blocklevel_backup_option(self):
    &#34;&#34;&#34;returns True if block level backup is enabled else returns false

        Returns:
            bool - boolean value based on blocklevel enable status

                True if block level is enabled
                False if block level is not enabled

    &#34;&#34;&#34;
    return bool(
        self._subclient_properties.get(
            &#39;mssqlSubClientProp&#39;, {}).get(&#39;useBlockLevelBackupWithOptimizedRecovery&#39;, False))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.sqlsubclient.SQLServerSubclient.browse_in_time"><code class="name">var <span class="ident">browse_in_time</span></code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sqlsubclient.py#L156-L161" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def browse_in_time(self):
    raise AttributeError(&#34;&#39;{0}&#39; object has no attribute &#39;{1}&#39;&#34;.format(
        self.__class__.__name__,
        &#39;browse_in_time&#39;
    ))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.sqlsubclient.SQLServerSubclient.content"><code class="name">var <span class="ident">content</span></code></dt>
<dd>
<div class="desc"><p>Gets the appropriate content from the Subclient relevant to the user.</p>
<h2 id="args">Args</h2>
<p>subclient_properties (dict)
&ndash;
dictionary contatining the properties of
subclient</p>
<h2 id="returns">Returns</h2>
<p>list - list of content associated with the subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sqlsubclient.py#L83-L119" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def content(self):
    &#34;&#34;&#34;Gets the appropriate content from the Subclient relevant to the user.

        Args:
            subclient_properties (dict)  --  dictionary contatining the properties of
                                                 subclient

        Returns:
            list - list of content associated with the subclient
    &#34;&#34;&#34;
    contents = []

    if &#39;content&#39; in self._subclient_properties:
        subclient_content = self._subclient_properties[&#39;content&#39;]
    else:
        return []

    database_name = None
    content_list = []

    if &#39;mssqlFFGDBName&#39; in self._subclient_properties[&#39;mssqlSubClientProp&#39;]:
        database_name = self._subclient_properties[&#39;mssqlSubClientProp&#39;][&#39;mssqlFFGDBName&#39;]

    for content in subclient_content:
        if &#39;mssqlDbContent&#39; in content:
            content_list.append(content[&#34;mssqlDbContent&#34;][&#34;databaseName&#34;])
        elif &#39;mssqlFGContent&#39; in content:
            content_list.append(content[&#39;mssqlFGContent&#39;][&#39;databaseName&#39;])

    if self._is_file_group_subclient:
        contents.append(database_name)
        contents.append(content_list)
    else:
        contents = content_list

    return contents</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.sqlsubclient.SQLServerSubclient.mssql_subclient_prop"><code class="name">var <span class="ident">mssql_subclient_prop</span></code></dt>
<dd>
<div class="desc"><p>getter for sql server subclient properties</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sqlsubclient.py#L295-L298" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def mssql_subclient_prop(self):
    &#34;&#34;&#34; getter for sql server subclient properties &#34;&#34;&#34;
    return self._mssql_subclient_prop</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.subclients.sqlsubclient.SQLServerSubclient.backup"><code class="name flex">
<span>def <span class="ident">backup</span></span>(<span>self, backup_level='Differential', data_options=[], schedule_pattern=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Runs a backup job for the subclient of the level specified.</p>
<h2 id="args">Args</h2>
<p>backup_level
(str)
&ndash;
level of backup the user wish to run
Full / Transaction_Log / Differential
default: Differential</p>
<p>data_options
(list)
&ndash;
List of options to be enabled on backup</p>
<p>The accepted string values are:
* start_log_backup_after_successfull_backup
* copy_only
* allow_diff_backup_on_read_only
* partial_sql_backup
* tail_log_backup
* use_sql_compression
* checksum
* continue_after_error</p>
<pre><code>default: []
</code></pre>
<p>schedule_pattern (dict) &ndash; scheduling options to be included for the task</p>
<pre><code>    Please refer schedules.schedulePattern.createSchedule()
                                                doc for the types of Jsons
</code></pre>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this backup job if its an immediate Job</p>
<pre><code>     instance of the Schedule class for the backup job if its a scheduled Job
</code></pre>
<h2 id="raises">Raises</h2>
<p>SDKException:
if backup level specified is not correct</p>
<pre><code>if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sqlsubclient.py#L184-L293" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def backup(
        self,
        backup_level=&#34;Differential&#34;,
        data_options=[],
        schedule_pattern=None
):
    &#34;&#34;&#34;Runs a backup job for the subclient of the level specified.

        Args:
            backup_level    (str)   --  level of backup the user wish to run
                    Full / Transaction_Log / Differential
                default: Differential

            data_options    (list)  --  List of options to be enabled on backup

            The accepted string values are:
                * start_log_backup_after_successfull_backup
                * copy_only
                * allow_diff_backup_on_read_only
                * partial_sql_backup
                * tail_log_backup
                * use_sql_compression
                * checksum
                * continue_after_error

                default: []

            schedule_pattern (dict) -- scheduling options to be included for the task

                    Please refer schedules.schedulePattern.createSchedule()
                                                                doc for the types of Jsons

        Returns:
            object - instance of the Job class for this backup job if its an immediate Job

                     instance of the Schedule class for the backup job if its a scheduled Job

        Raises:
            SDKException:
                if backup level specified is not correct

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    advanced_options = {}
    backup_level = backup_level.lower()

    if backup_level not in [&#39;full&#39;, &#39;transaction_log&#39;, &#39;differential&#39;]:
        raise SDKException(&#39;Subclient&#39;, &#39;103&#39;)

    if data_options or schedule_pattern:
        if data_options:
            invalid_full_data_opts = [&#39;tail_log_backup&#39;, &#39;allow_diff_backup_on_read_only&#39;]
            invalid_transaction_log_data_opts = [
                &#39;start_log_backup_after_successfull_backup&#39;,
                &#39;allow_diff_backup_on_read_only&#39;,
                &#39;copy_only&#39;]
            invalid_differential_data_opts = [&#39;tail_log_backup&#39;, &#39;copy_only&#39;]

            if &#39;checksum&#39; in data_options and &#39;use_sql_compression&#39; in data_options:
                raise ValueError(&#34;checksum or use_sql_compression can be enabled , but not both&#34;)
            if backup_level == &#39;full&#39; and any(option in data_options for option in invalid_full_data_opts):
                raise ValueError(&#34;{0} are not applicable for full backup&#34;.format(invalid_full_data_opts))
            elif backup_level == &#39;transaction_log&#39; and any(option in data_options
                                                           for option in invalid_transaction_log_data_opts):
                raise ValueError(&#34;{0} are not applicable for Transaction log backup&#34;.format(
                    invalid_transaction_log_data_opts))
            elif backup_level == &#39;differential&#39; and any(option in data_options
                                                        for option in invalid_differential_data_opts):
                raise ValueError(&#34;{0} are not applicable for full backup&#34;.format(invalid_differential_data_opts))

            advanced_options[&#34;dataOpt&#34;] = {
                &#34;enableIndexCheckPointing&#34;: False,
                &#34;verifySynthFull&#34;: True,
                &#34;startLogBackupAfterSuccessfullBackup&#34;:
                    &#34;start_log_backup_after_successfull_backup&#34; in data_options,
                &#34;tailLogBackup&#34;: &#34;tail_log_backup&#34; in data_options,
                &#34;partailSqlBkp&#34;: &#34;partial_sql_backup&#34; in data_options,
                &#34;useSqlCompression&#34;: &#34;use_sql_compression&#34; in data_options,
                &#34;useCatalogServer&#34;: False,
                &#34;enforceTransactionLogUsage&#34;: False,
                &#34;copyOnly&#34;: &#34;copy_only&#34; in data_options,
                &#34;skipConsistencyCheck&#34;: False,
                &#34;skipCatalogPhaseForSnapBackup&#34;: True,
                &#34;runIntegrityCheck&#34;: False,
                &#34;checksum&#34;: &#34;checksum&#34; in data_options,
                &#34;continueaftererror&#34;: &#34;continue_after_error&#34; in data_options,
                &#34;allowDiffBackupOnReadOnly&#34;: &#34;allow_diff_backup_on_read_only&#34; in data_options
            }

        request_json = self._backup_json(
            backup_level,
            False,
            &#34;BEFORE_SYNTH&#34;,
            advanced_options,
            schedule_pattern
        )

        backup_service = self._services[&#39;CREATE_TASK&#39;]

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, backup_service, request_json
        )

        return self._process_backup_response(flag, response)

    return super(SQLServerSubclient, self).backup(
        backup_level=backup_level,
    )</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.sqlsubclient.SQLServerSubclient.update_content"><code class="name flex">
<span>def <span class="ident">update_content</span></span>(<span>self, subclient_content, action)</span>
</code></dt>
<dd>
<div class="desc"><p>Updates the sql server subclient contents with supplied content list.</p>
<h2 id="args">Args</h2>
<p>subclient_content (list)
&ndash;
list of the content to add to the subclient</p>
<dl>
<dt>action (int)
&ndash;
action to perform on subclient</dt>
<dt><strong><code>1</code></strong></dt>
<dd>OVERWRITE, 2: ADD, 3: DELETE</dd>
</dl>
<h2 id="returns">Returns</h2>
<p>list - list of the appropriate JSON to send to the POST Subclient API</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/sqlsubclient.py#L319-L363" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def update_content(self, subclient_content, action):
    &#34;&#34;&#34;Updates the sql server subclient contents with supplied content list.

        Args:
            subclient_content (list)  --  list of the content to add to the subclient

            action (int)  --   action to perform on subclient
            1: OVERWRITE, 2: ADD, 3: DELETE

        Returns:
            list - list of the appropriate JSON to send to the POST Subclient API
    &#34;&#34;&#34;
    request_json = self._get_subclient_properties_json()
    content_list = []

    if self._is_file_group_subclient:
        err_message = &#39;Content modification is not supported for FILE/ FILE GROUP subclient.&#39;
        &#39;Please use Commcell Console to update the content.&#39;
        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, err_message)
    else:
        for database_name in subclient_content:
            sql_server_dict = {
                &#34;mssqlDbContent&#34;: {
                    &#34;databaseName&#34;: database_name
                }
            }
            content_list.append(sql_server_dict)
    request_json[&#39;subClientProperties&#39;][&#39;content&#39;] = content_list

    content_op_dict = {
        &#34;contentOperationType&#34;: action
    }
    request_json[&#39;subClientProperties&#39;].update(content_op_dict)

    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, self._SUBCLIENT, request_json
    )

    output = self._process_update_response(flag, response)

    if output[0]:
        return
    else:
        o_str = &#39;Failed to update content of subclient\nError: &#34;{0}&#34;&#39;
        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str.format(output[2]))</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient" href="dbsubclient.html#cvpysdk.subclients.dbsubclient.DatabaseSubclient">DatabaseSubclient</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.allow_multiple_readers" href="../subclient.html#cvpysdk.subclient.Subclient.allow_multiple_readers">allow_multiple_readers</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.browse" href="../subclient.html#cvpysdk.subclient.Subclient.browse">browse</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.data_readers" href="../subclient.html#cvpysdk.subclient.Subclient.data_readers">data_readers</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.deduplication_options" href="../subclient.html#cvpysdk.subclient.Subclient.deduplication_options">deduplication_options</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.description" href="../subclient.html#cvpysdk.subclient.Subclient.description">description</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.disable_backup" href="../subclient.html#cvpysdk.subclient.Subclient.disable_backup">disable_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.disable_intelli_snap" href="../subclient.html#cvpysdk.subclient.Subclient.disable_intelli_snap">disable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.display_name" href="../subclient.html#cvpysdk.subclient.Subclient.display_name">display_name</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.enable_backup" href="../subclient.html#cvpysdk.subclient.Subclient.enable_backup">enable_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.enable_backup_at_time" href="../subclient.html#cvpysdk.subclient.Subclient.enable_backup_at_time">enable_backup_at_time</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.enable_intelli_snap" href="../subclient.html#cvpysdk.subclient.Subclient.enable_intelli_snap">enable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.enable_trueup" href="../subclient.html#cvpysdk.subclient.Subclient.enable_trueup">enable_trueup</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.enable_trueup_days" href="../subclient.html#cvpysdk.subclient.Subclient.enable_trueup_days">enable_trueup_days</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.encryption_flag" href="../subclient.html#cvpysdk.subclient.Subclient.encryption_flag">encryption_flag</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.exclude_from_sla" href="../subclient.html#cvpysdk.subclient.Subclient.exclude_from_sla">exclude_from_sla</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.find" href="../subclient.html#cvpysdk.subclient.Subclient.find">find</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.find_latest_job" href="../subclient.html#cvpysdk.subclient.Subclient.find_latest_job">find_latest_job</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.get_ma_associated_storagepolicy" href="../subclient.html#cvpysdk.subclient.Subclient.get_ma_associated_storagepolicy">get_ma_associated_storagepolicy</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.is_backup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_backup_enabled">is_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.is_blocklevel_backup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_blocklevel_backup_enabled">is_blocklevel_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.is_default_subclient" href="../subclient.html#cvpysdk.subclient.Subclient.is_default_subclient">is_default_subclient</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.is_intelli_snap_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_intelli_snap_enabled">is_intelli_snap_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.is_on_demand_subclient" href="../subclient.html#cvpysdk.subclient.Subclient.is_on_demand_subclient">is_on_demand_subclient</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.is_trueup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_trueup_enabled">is_trueup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.last_backup_time" href="../subclient.html#cvpysdk.subclient.Subclient.last_backup_time">last_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.list_media" href="../subclient.html#cvpysdk.subclient.Subclient.list_media">list_media</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.log_backup_storage_policy" href="dbsubclient.html#cvpysdk.subclients.dbsubclient.DatabaseSubclient.log_backup_storage_policy">log_backup_storage_policy</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.name" href="../subclient.html#cvpysdk.subclient.Subclient.name">name</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.network_agent" href="../subclient.html#cvpysdk.subclient.Subclient.network_agent">network_agent</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.next_backup_time" href="../subclient.html#cvpysdk.subclient.Subclient.next_backup_time">next_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.plan" href="../subclient.html#cvpysdk.subclient.Subclient.plan">plan</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.properties" href="../subclient.html#cvpysdk.subclient.Subclient.properties">properties</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.read_buffer_size" href="../subclient.html#cvpysdk.subclient.Subclient.read_buffer_size">read_buffer_size</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.refresh" href="../subclient.html#cvpysdk.subclient.Subclient.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.restore_in_place" href="../subclient.html#cvpysdk.subclient.Subclient.restore_in_place">restore_in_place</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.restore_out_of_place" href="../subclient.html#cvpysdk.subclient.Subclient.restore_out_of_place">restore_out_of_place</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.run_content_indexing" href="../subclient.html#cvpysdk.subclient.Subclient.run_content_indexing">run_content_indexing</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.set_backup_nodes" href="../subclient.html#cvpysdk.subclient.Subclient.set_backup_nodes">set_backup_nodes</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.set_proxy_for_snap" href="../subclient.html#cvpysdk.subclient.Subclient.set_proxy_for_snap">set_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.snapshot_engine_name" href="../subclient.html#cvpysdk.subclient.Subclient.snapshot_engine_name">snapshot_engine_name</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.software_compression" href="../subclient.html#cvpysdk.subclient.Subclient.software_compression">software_compression</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.storage_ma" href="../subclient.html#cvpysdk.subclient.Subclient.storage_ma">storage_ma</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.storage_ma_id" href="../subclient.html#cvpysdk.subclient.Subclient.storage_ma_id">storage_ma_id</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.storage_policy" href="../subclient.html#cvpysdk.subclient.Subclient.storage_policy">storage_policy</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.subclient_guid" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_guid">subclient_guid</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.subclient_id" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_id">subclient_id</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.subclient_name" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_name">subclient_name</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.unset_proxy_for_snap" href="../subclient.html#cvpysdk.subclient.Subclient.unset_proxy_for_snap">unset_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.update_properties" href="../subclient.html#cvpysdk.subclient.Subclient.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.subclients" href="index.html">cvpysdk.subclients</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.subclients.sqlsubclient.SQLServerSubclient" href="#cvpysdk.subclients.sqlsubclient.SQLServerSubclient">SQLServerSubclient</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.subclients.sqlsubclient.SQLServerSubclient.backup" href="#cvpysdk.subclients.sqlsubclient.SQLServerSubclient.backup">backup</a></code></li>
<li><code><a title="cvpysdk.subclients.sqlsubclient.SQLServerSubclient.blocklevel_backup_option" href="#cvpysdk.subclients.sqlsubclient.SQLServerSubclient.blocklevel_backup_option">blocklevel_backup_option</a></code></li>
<li><code><a title="cvpysdk.subclients.sqlsubclient.SQLServerSubclient.browse_in_time" href="#cvpysdk.subclients.sqlsubclient.SQLServerSubclient.browse_in_time">browse_in_time</a></code></li>
<li><code><a title="cvpysdk.subclients.sqlsubclient.SQLServerSubclient.content" href="#cvpysdk.subclients.sqlsubclient.SQLServerSubclient.content">content</a></code></li>
<li><code><a title="cvpysdk.subclients.sqlsubclient.SQLServerSubclient.mssql_subclient_prop" href="#cvpysdk.subclients.sqlsubclient.SQLServerSubclient.mssql_subclient_prop">mssql_subclient_prop</a></code></li>
<li><code><a title="cvpysdk.subclients.sqlsubclient.SQLServerSubclient.update_content" href="#cvpysdk.subclients.sqlsubclient.SQLServerSubclient.update_content">update_content</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>