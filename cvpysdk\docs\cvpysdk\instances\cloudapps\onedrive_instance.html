<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.instances.cloudapps.onedrive_instance API documentation</title>
<meta name="description" content="File for operating on a OneDrive Instance …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.instances.cloudapps.onedrive_instance</code></h1>
</header>
<section id="section-intro">
<p>File for operating on a OneDrive Instance.</p>
<p>OneDriveInstance is the only class defined in this file.</p>
<p>OneDriveInstance: Derived class from CloudAppsInstance Base class, representing a
OneDrive instance,and to perform operations on that instance</p>
<h2 id="onedriveinstance">Onedriveinstance</h2>
<p>_prepare_advsearchgrp_onedrive_for_business_client() &ndash;
Utility function to prepare advsearchgrp json for restore
job for OneDrive for business clients</p>
<p>_prepare_findquery_onedrive_for_business_client()
&ndash;
Utility function to prepare findquery json for restore job
for OneDrive for business clients</p>
<p>_prepare_restore_json_onedrive_for_business_client() &ndash;
Utility function to prepare user level restore json for
OneDrive for business clients</p>
<p>_prepare_delete_json_onedrive_v2()
&ndash;
Utility function to prepare delete documents json for
OneDrive for business clients</p>
<p>_get_instance_properties()
&ndash;
Instance class method overwritten to add cloud apps
instance properties as well</p>
<p>restore_out_of_place()
&ndash;
runs out-of-place restore for the instance</p>
<p>modify_connection_settings()
&ndash;
Modifies the azure app connection settings</p>
<p>delete_data_from_browse()
&ndash;
Deletes items for the backupset in the Index and makes them unavailable for
browsing and recovery</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/onedrive_instance.py#L1-L907" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;File for operating on a OneDrive Instance.

OneDriveInstance is the only class defined in this file.

OneDriveInstance: Derived class from CloudAppsInstance Base class, representing a
OneDrive instance,and to perform operations on that instance

OneDriveInstance:

    _prepare_advsearchgrp_onedrive_for_business_client() --  Utility function to prepare advsearchgrp json for restore
                                                            job for OneDrive for business clients

    _prepare_findquery_onedrive_for_business_client()    --  Utility function to prepare findquery json for restore job
                                                            for OneDrive for business clients

    _prepare_restore_json_onedrive_for_business_client() --  Utility function to prepare user level restore json for
                                                            OneDrive for business clients

    _prepare_delete_json_onedrive_v2()      --  Utility function to prepare delete documents json for
                                                OneDrive for business clients

    _get_instance_properties()       --  Instance class method overwritten to add cloud apps
    instance properties as well

    restore_out_of_place()                               --  runs out-of-place restore for the instance

    modify_connection_settings()                         --  Modifies the azure app connection settings

    delete_data_from_browse()       --  Deletes items for the backupset in the Index and makes them unavailable for
                                        browsing and recovery

&#34;&#34;&#34;

from __future__ import unicode_literals
from ...exception import SDKException
from ..cainstance import CloudAppsInstance
from ...constants import AppIDAType
from base64 import b64encode


class OneDriveInstance(CloudAppsInstance):
    &#34;&#34;&#34;Class for representing an Instance of the OneDrive instance type.&#34;&#34;&#34;

    def _get_instance_properties(self):
        &#34;&#34;&#34;Gets the properties of this instance.

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        super(OneDriveInstance, self)._get_instance_properties()
        # Common properties for Google and OneDrive
        self._ca_instance_type = None
        self._manage_content_automatically = None
        self._auto_discovery_enabled = None
        self._auto_discovery_mode = None
        self._proxy_client = None

        self._client_id = None
        self._tenant = None

        if &#39;cloudAppsInstance&#39; in self._properties:
            cloud_apps_instance = self._properties[&#39;cloudAppsInstance&#39;]
            self._ca_instance_type = cloud_apps_instance[&#39;instanceType&#39;]

            if &#39;oneDriveInstance&#39; in cloud_apps_instance:
                onedrive_instance = cloud_apps_instance[&#39;oneDriveInstance&#39;]

                self._manage_content_automatically = onedrive_instance[&#39;manageContentAutomatically&#39;]
                self._auto_discovery_enabled = onedrive_instance[&#39;isAutoDiscoveryEnabled&#39;]
                self._auto_discovery_mode = onedrive_instance[&#39;autoDiscoveryMode&#39;]
                if &#39;clientId&#39; in onedrive_instance:
                    self._client_id = onedrive_instance.get(&#39;clientId&#39;)
                    self._tenant = onedrive_instance.get(&#39;tenant&#39;)
                else:
                    self._client_id = onedrive_instance.get(
                        &#39;azureAppList&#39;, {}).get(&#39;azureApps&#39;, [{}])[0].get(&#39;azureAppId&#39;)
                    self._tenant = onedrive_instance.get(
                        &#39;azureAppList&#39;, {}).get(&#39;azureApps&#39;, [{}])[0].get(&#39;azureDirectoryId&#39;)

                if self._client_id is None:
                    raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;Azure App has not been configured&#39;)

            if &#39;generalCloudProperties&#39; in cloud_apps_instance:
                if &#39;proxyServers&#39; in cloud_apps_instance[&#39;generalCloudProperties&#39;]:
                    self._proxy_client = cloud_apps_instance.get(
                        &#39;generalCloudProperties&#39;, {}).get(&#39;proxyServers&#39;, [{}])[0].get(&#39;clientName&#39;)
                else:
                    if &#39;clientName&#39; in cloud_apps_instance.get(
                            &#39;generalCloudProperties&#39;, {}).get(&#39;memberServers&#39;, [{}])[0].get(&#39;client&#39;):
                        self._proxy_client = cloud_apps_instance.get(&#39;generalCloudProperties&#39;, {}).get(
                            &#39;memberServers&#39;, [{}])[0].get(&#39;client&#39;, {}).get(&#39;clientName&#39;)
                    else:
                        self._proxy_client = cloud_apps_instance.get(&#39;generalCloudProperties&#39;, {}).get(
                            &#39;memberServers&#39;, [{}])[0].get(&#39;client&#39;, {}).get(&#39;clientGroupName&#39;)

                if self._proxy_client is None:
                    raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;Access Node has not been configured&#39;)

    @property
    def ca_instance_type(self):
        &#34;&#34;&#34;Returns the CloudApps instance type&#34;&#34;&#34;
        if self._ca_instance_type == 7:
            return &#39;ONEDRIVE&#39;
        return self._ca_instance_type

    @property
    def manage_content_automatically(self):
        &#34;&#34;&#34;Returns the CloudApps Manage Content Automatically property&#34;&#34;&#34;
        return self._manage_content_automatically

    @property
    def auto_discovery_status(self):
        &#34;&#34;&#34;Treats the Auto discovery property as a read-only attribute.&#34;&#34;&#34;
        return self._auto_discovery_enabled

    @property
    def auto_discovery_mode(self):
        &#34;&#34;&#34;Returns the Auto discovery mode property&#34;&#34;&#34;
        return self._auto_discovery_mode

    @property
    def onedrive_client_id(self):
        &#34;&#34;&#34;Returns the OneDrive app client id&#34;&#34;&#34;
        return self._client_id

    @property
    def onedrive_tenant(self):
        &#34;&#34;&#34;Returns the OneDrive tenant id&#34;&#34;&#34;
        return self._tenant

    @property
    def proxy_client(self):
        &#34;&#34;&#34;Returns the proxy client name to this instance&#34;&#34;&#34;
        return self._proxy_client


    def _prepare_advsearchgrp_onedrive_for_business_client(self, source_item_list, subclient_id):
        &#34;&#34;&#34;
                    Utility function to prepare advsearchgrp json for restore job for OneDrive for business clients

                    Args:
                        source_item_list (list)         --  list of user GUID to process in restore

                        subclient_id                    --  subclient id of the client

                    Returns:
                        advsearchgrp (dict) - advsearchgrp json for restore job
        &#34;&#34;&#34;


        user_guid = source_item_list[0]
        advsearchgrp = {
            &#34;fileFilter&#34;: [
                {
                    &#34;interGroupOP&#34;: &#34;FTAnd&#34;,
                    &#34;filter&#34;: {
                        &#34;filters&#34;: [
                            {
                                &#34;field&#34;: &#34;HIDDEN&#34;,
                                &#34;fieldValues&#34;: {
                                    &#34;values&#34;: [
                                        &#34;true&#34;
                                    ]
                                },
                                &#34;intraFieldOp&#34;: &#34;FTNot&#34;
                            },
                            {
                                &#34;field&#34;: &#34;CV_OBJECT_GUID&#34;,
                                &#34;fieldValues&#34;: {
                                    &#34;values&#34;: [
                                        user_guid
                                    ]
                                },
                                &#34;intraFieldOp&#34;: &#34;FTOr&#34;
                            }
                        ],
                        &#34;interFilterOP&#34;: &#34;FTAnd&#34;
                    }
                }
            ],
            &#34;commonFilter&#34;: [
                {
                    &#34;filter&#34;: {
                        &#34;filters&#34;: [
                            {
                                &#34;field&#34;: &#34;CISTATE&#34;,
                                &#34;fieldValues&#34;: {
                                    &#34;values&#34;: [
                                        &#34;1&#34;
                                    ]
                                },
                                &#34;intraFieldOp&#34;: &#34;FTOr&#34;,
                                &#34;groupType&#34;: 0
                            },
                            {
                                &#34;field&#34;: &#34;IS_VISIBLE&#34;,
                                &#34;fieldValues&#34;: {
                                    &#34;values&#34;: [
                                        &#34;true&#34;
                                    ],
                                    &#34;isRange&#34;: False,
                                    &#34;isMoniker&#34;: False
                                },
                                &#34;intraFieldOp&#34;: &#34;FTOr&#34;,
                                &#34;intraFieldOpStr&#34;: &#34;None&#34;
                            }
                        ],
                        &#34;interFilterOP&#34;: &#34;FTAnd&#34;
                    }
                }
            ],
            &#34;galaxyFilter&#34;: [
                {
                    &#34;appIdList&#34;: [
                        subclient_id
                    ]
                }
            ],
            &#34;graphFilter&#34;: [
                {
                    &#34;fromField&#34;: &#34;PARENT_GUID&#34;,
                    &#34;toField&#34;: &#34;CV_OBJECT_GUID&#34;,
                    &#34;returnRoot&#34;: True,
                    &#34;traversalFilter&#34;: [
                        {
                            &#34;filters&#34;: [
                                {
                                    &#34;field&#34;: &#34;IS_VISIBLE&#34;,
                                    &#34;fieldValues&#34;: {
                                        &#34;values&#34;: [
                                            &#34;true&#34;
                                        ]
                                    },
                                    &#34;intraFieldOp&#34;: &#34;FTAnd&#34;,
                                    &#34;groupType&#34;: 0
                                },
                                {
                                    &#34;field&#34;: &#34;HIDDEN&#34;,
                                    &#34;fieldValues&#34;: {
                                        &#34;values&#34;: [
                                            &#34;true&#34;
                                        ]
                                    },
                                    &#34;intraFieldOp&#34;: &#34;FTNot&#34;
                                }
                            ]
                        }
                    ]
                }
            ]
        }

        return advsearchgrp

    def _prepare_findquery_onedrive_for_business_client(self, source_item_list, subclient_id):
        &#34;&#34;&#34;
            Utility function to prepare findquery json for restore job for OneDrive for bussiness clients

            Args:
                source_item_list (list)         --  list of user GUID to process in restore

                subclient_id                    --  subclient id of the client

            Returns:
                findquery (dict) - findquery json for restore job
        &#34;&#34;&#34;

        findquery = {
                  &#34;searchProcessingInfo&#34;: {
                    &#34;pageSize&#34;: 20,
                    &#34;resultOffset&#34;: 0,
                    &#34;sortParams&#34;: [
                      {
                        &#34;sortField&#34;: &#34;DATA_TYPE&#34;,
                        &#34;sortDirection&#34;: &#34;DESCENDING&#34;
                      },
                      {
                        &#34;sortField&#34;: &#34;FileName&#34;,
                        &#34;sortDirection&#34;: &#34;ASCENDING&#34;
                      }
                    ],
                    &#34;queryParams&#34;: [
                      {
                        &#34;param&#34;: &#34;ENABLE_MIXEDVIEW&#34;,
                        &#34;value&#34;: &#34;true&#34;
                      },
                      {
                        &#34;param&#34;: &#34;RESPONSE_FIELD_LIST&#34;,
                        &#34;value&#34;: &#34;FAST_URL,BACKUPTIME,SIZEINKB,MODIFIEDTIME,CONTENTID,CV_TURBO_GUID,AFILEID,AFILEOFFSET,COMMCELLNO,FILE_NAME,FILE_FOLDER,CVSTUB,DATA_TYPE,APPID,JOBID,CISTATE,DATE_DELETED,IdxFlags,CV_OBJECT_GUID,PARENT_GUID,CUSTODIAN,OWNER,ObjectType&#34;
                      },
                      {
                        &#34;param&#34;: &#34;DO_NOT_AUDIT&#34;,
                        &#34;value&#34;: &#34;false&#34;
                      },
                      {
                        &#34;param&#34;: &#34;COLLAPSE_FIELD&#34;,
                        &#34;value&#34;: &#34;CV_OBJECT_GUID&#34;
                      },
                      {
                        &#34;param&#34;: &#34;COLLAPSE_SORT&#34;,
                        &#34;value&#34;: &#34;BACKUPTIME DESC&#34;
                      },
                      {
                        &#34;param&#34;: &#34;ENABLE_NAVIGATION&#34;,
                        &#34;value&#34;: &#34;on&#34;
                      },
                      {
                        &#34;param&#34;: &#34;ENABLE_DEFAULTFACETS&#34;,
                        &#34;value&#34;: &#34;false&#34;
                      }
                    ]
                  },
                  &#34;advSearchGrp&#34;: self._prepare_advsearchgrp_onedrive_for_business_client(source_item_list,subclient_id),
                  &#34;mode&#34;: &#34;WebConsole&#34;
                }

        return findquery




    def _prepare_restore_json_onedrive_for_business_client(self, source_item_list, **kwargs):

        &#34;&#34;&#34; Utility function to prepare user level restore json for OneDrive for bussiness clients

            Args:
                source_item_list (list)         --  list of user GUID to process in restore

            Kwargs:

                out_of_place (bool)             --  If True, out of place restore will be performed

                disk_restore (bool)             --  If True, restore to disk will be performed

                destination_path (str)          --  destination path for oop and disk restores

                destination_client              -- destination client for disk restore

                overwrite (bool)                --  If True, files will be overwritten in destination if already exists

                restore_as_copy (bool)          --  If True, files will be restored as copy if already exists

                skip_file_permissions (bool)    --  If True, file permissions will be restored

                include_deleted_items  (bool)   --  If True, deleted items will be included

            Returns:
                request_json (dict) - request json for restore job

            Raises:
                SDKException:
                    if destination client with given name does not exist

                    if type of parameter is invalid

        &#34;&#34;&#34;

        out_of_place = kwargs.get(&#39;out_of_place&#39;, False)
        disk_restore = kwargs.get(&#39;disk_restore&#39;, False)
        destination_path = kwargs.get(&#39;destination_path&#39;, False)
        destination_client = kwargs.get(&#39;destination_client&#39;)
        overwrite = kwargs.get(&#39;overwrite&#39;, False)
        restore_as_copy = kwargs.get(&#39;restore_as_copy&#39;, False)
        skip_file_permissions = kwargs.get(&#39;skip_file_permissions&#39;, True)
        include_deleted_items = kwargs.get(&#39;include_deleted_items&#39;, False)


        if destination_client:
            if self._commcell_object.clients.all_clients.get(destination_client):
                destination_client_object = self._commcell_object.clients.all_clients.get(destination_client)
                destination_client_id = int(destination_client_object.get(&#39;id&#39;))
            else:
                raise SDKException(&#39;Client&#39;, &#39;102&#39;, &#39;Client &#34;{0}&#34; does not exist.&#39;.format(destination_client))

        if ((destination_client and not isinstance(destination_client, str) or
             destination_path and not isinstance(destination_path, str)) or not
            (isinstance(source_item_list, list) and
             isinstance(skip_file_permissions, bool) and
             isinstance(disk_restore, bool) and
             isinstance(out_of_place, bool) and
             isinstance(overwrite, bool) and
             isinstance(restore_as_copy, bool))):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        request_json = self._restore_json(client=self._agent_object._client_object)

        subtasks = request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0]
        options = subtasks[&#39;options&#39;]
        restore_options = options[&#39;restoreOptions&#39;]

        options[&#34;restoreOptions&#34;][&#34;browseOption&#34;] = {
            &#34;commCellId&#34;: self._commcell_object.commcell_id,
            &#34;showDeletedItems&#34;: False
        }

        restore_options[&#39;commonOptions&#39;] = {
            &#34;overwriteFiles&#34;: overwrite,
            &#34;skip&#34;: True if not restore_as_copy and not overwrite else False,
            &#34;unconditionalOverwrite&#34;: overwrite
        }

        destination = restore_options[&#39;destination&#39;]
        destination[&#39;destAppId&#39;] = AppIDAType.WINDOWS_FILE_SYSTEM.value if disk_restore else AppIDAType.CLOUD_APP.value
        destination[&#39;inPlace&#39;] = False if out_of_place or disk_restore else True

        destination[&#39;destClient&#39;] = {
            &#34;clientId&#34;: destination_client_id,
            &#34;clientName&#34;: destination_client
        } if disk_restore else {
            &#34;clientId&#34;: int(self._agent_object._client_object.client_id),
            &#34;clientName&#34;: self._agent_object._client_object.client_name
        }

        if destination_path:
            destination[&#39;destPath&#39;] = [destination_path]

        restore_options[&#39;fileOption&#39;][&#39;sourceItem&#39;] = source_item_list

        restore_options[&#39;cloudAppsRestoreOptions&#39;] = {
            &#34;instanceType&#34;: self._ca_instance_type,
            &#34;googleRestoreOptions&#34;: {
                &#34;skipPermissionsRestore&#34;: False if disk_restore else skip_file_permissions,
                &#34;restoreToDifferentAccount&#34;: True if out_of_place else False,
                &#34;restoreAsCopy&#34;: False if disk_restore else restore_as_copy,
                &#34;filelevelRestore&#34;: False,
                &#34;strDestUserAccount&#34;: destination_path if out_of_place else &#39;&#39;,
                &#34;overWriteItems&#34;: False if disk_restore else overwrite,
                &#34;restoreToGoogle&#34;: False if disk_restore else True
            }
        }

        del subtasks[&#39;subTaskOperation&#39;]
        del restore_options[&#39;fileOption&#39;]
        del restore_options[&#39;impersonation&#39;]
        del restore_options[&#39;volumeRstOption&#39;]
        del restore_options[&#39;sharePointRstOption&#39;]
        del restore_options[&#39;virtualServerRstOption&#39;]

        associations = request_json[&#39;taskInfo&#39;][&#39;associations&#39;][0]
        subclient_id = associations[&#39;subclientId&#39;]

        cloudAppsRestoreOptions = restore_options[&#39;cloudAppsRestoreOptions&#39;]
        cloudAppsRestoreOptions[&#39;googleRestoreOptions&#39;][&#39;findQuery&#39;] = self._prepare_findquery_onedrive_for_business_client(source_item_list, subclient_id)
        if include_deleted_items:
            cloudAppsRestoreOptions[&#39;googleRestoreOptions&#39;][&#39;findQuery&#39;][&#39;advSearchGrp&#39;][&#39;commonFilter&#39;][0][&#39;filter&#39;][&#39;filters&#39;][0][&#39;fieldValues&#39;][&#39;values&#39;].extend([&#34;3333&#34;,&#34;3334&#34;,&#34;3335&#34;])

        destination_option = &#34;Destination&#34;
        destination_value = &#34;Original location&#34;
        if out_of_place:
            destination_option = &#34;Destination user&#34;
            destination_value = source_item_list[0]
        if disk_restore:
            destination_option = &#34;Destination server&#34;
            destination_value = destination_client


        options[&#34;commonOpts&#34;] = {
            &#34;notifyUserOnJobCompletion&#34;: False,
            &#34;jobMetadata&#34;: [
              {
                &#34;selectedItems&#34;: [
                  {
                    &#34;itemName&#34;: source_item_list[0],
                    &#34;itemType&#34;: &#34;User&#34;
                  }
                ],
                &#34;jobOptionItems&#34;: [
                  {
                    &#34;option&#34;: &#34;Restore destination&#34;,
                    &#34;value&#34;: &#34;OneDrive for Business&#34;
                  },
                  {
                    &#34;option&#34;: &#34;Source&#34;,
                    &#34;value&#34;: source_item_list[0]
                  },
                  {
                    &#34;option&#34;: destination_option,
                    &#34;value&#34;: destination_value
                  },
                  {
                    &#34;option&#34;: &#34;If the file exists&#34;,
                    &#34;value&#34;: &#34;Restore as a copy&#34; if restore_as_copy and not overwrite else &#34;Unconditionally overwrite&#34; if overwrite else &#34;Skip&#34;

                  },
                  {
                    &#34;option&#34;: &#34;Skip file permissions&#34;,
                    &#34;value&#34;: &#34;Enabled&#34; if skip_file_permissions else &#34;Disabled&#34;
                  },
                  {
                    &#34;option&#34;: &#34;Include deleted items&#34;,
                    &#34;value&#34;: &#34;Enabled&#34; if include_deleted_items else &#34;Disabled&#34;
                  }
                ]
              }
            ]
          }

        joboptionitems = options[&#39;commonOpts&#39;][&#39;jobMetadata&#39;][0][&#39;jobOptionItems&#39;]

        if out_of_place:
            joboptionitems.append({&#34;option&#34;: &#34;Destination client&#34;,&#34;value&#34;: destination_client })
        if disk_restore:
            joboptionitems.append({&#34;option&#34;: &#34;Destination path&#34;, &#34;value&#34;: destination_path})

        return request_json

    def _prepare_delete_json_onedrive_v2(self, item_guids, **kwargs):
        &#34;&#34;&#34; Utility function to prepare delete documents json for OneDrive for bussiness clients

            Args:
                item_guid (str)         --  item GUID to delete in browse

                Kwargs:

                    include_deleted_items (bool)             --  If True, deleted items will be included in search

            Returns:
                request_json (dict) - request json for delete document

            Raises:
                SDKException:
                    if destination client with given item does not exist

                    if type of parameter is invalid
        &#34;&#34;&#34;
        folder = kwargs.get(&#39;folder&#39;, False)
        include_deleted_items = kwargs.get(&#39;include_deleted_items&#39;, False)
        subclient_id = self.subclients[&#39;default&#39;][&#39;id&#39;]

        if isinstance(item_guids, str):
            source_item_list = [item_guids]
        else:
            source_item_list = [].extend(item_guids)
        req_json = {
            &#39;opType&#39;: 1,
            &#39;bulkMode&#39;: folder,
            &#39;deleteOption&#39;: {
                &#39;folderDelete&#39;: folder
            },
            &#39;searchReq&#39;: {
                &#39;mode&#39;: 4,
                &#39;facetRequests&#39;: {
                    &#39;facetRequest&#39;: []
                },
                &#39;advSearchGrp&#39;: self._prepare_advsearchgrp_onedrive_for_business_client(source_item_list, subclient_id),
                &#39;searchProcessingInfo&#39;: {
                    &#39;resultOffset&#39;: 0,
                    &#39;pageSize&#39;: 50,
                    &#39;queryParams&#39;: [
                        {
                            &#39;param&#39;: &#39;ENABLE_MIXEDVIEW&#39;,
                            &#39;value&#39;: &#39;true&#39;
                        },
                        {
                            &#39;param&#39;: &#39;ENABLE_NAVIGATION&#39;,
                            &#39;value&#39;: &#39;on&#39;
                        },
                        {
                            &#39;param&#39;: &#39;ENABLE_DEFAULTFACETS&#39;,
                            &#39;value&#39;: &#39;false&#39;
                        }
                    ],
                    &#39;sortParams&#39;: []
                }
            }
        }

        if isinstance(item_guids, list):
            req_json[&#39;searchReq&#39;][&#39;advSearchGrp&#39;][&#39;fileFilter&#39;][0][&#39;filter&#39;][&#39;filters&#39;][1][&#39;fieldValues&#39;][
                &#39;values&#39;].extend(item_guids[1:])

        if include_deleted_items:
            req_json[&#39;searchReq&#39;][&#39;advSearchGrp&#39;][&#39;commonFilter&#39;][0][&#39;filter&#39;][&#39;filters&#39;][0][&#39;fieldValues&#39;][&#39;values&#39;].extend(
                [&#34;3333&#34;, &#34;3334&#34;, &#34;3335&#34;])

        return req_json

    def restore_out_of_place(
            self,
            client,
            destination_path,
            paths,
            overwrite=True,
            restore_data_and_acl=True,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            to_disk=False):
        &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the input client,
            at the specified destionation location.

            Args:
                client                (str/object) --  either the name of the client or
                                                           the instance of the Client

                destination_path      (str)        --  full path of the restore location on client

                paths                 (list)       --  list of full paths of
                                                           files/folders to restore

                overwrite             (bool)       --  unconditional overwrite files during restore
                    default: True

                restore_data_and_acl  (bool)       --  restore data and ACL files
                    default: True

                copy_precedence         (int)   --  copy precedence value of storage policy copy
                    default: None

                from_time           (str)       --  time to retore the contents after
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time           (str)         --  time to retore the contents before
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_disk             (bool)       --  If True, restore to disk will be performed

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if client is not a string or Client instance

                    if destination_path is not a string

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        from cvpysdk.client import Client

        if not ((isinstance(client, str) or isinstance(client, Client)) and
                isinstance(destination_path, str) and
                isinstance(paths, list) and
                isinstance(overwrite, bool) and
                isinstance(restore_data_and_acl, bool)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if isinstance(client, Client):
            client = client
        elif isinstance(client, str):
            client = Client(self._commcell_object, client)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;105&#39;)

        paths = self._filter_paths(paths)

        destination_path = self._filter_paths([destination_path], True)

        if paths == []:
            raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)

        request_json = self._restore_json(
            paths=paths,
            in_place=False,
            client=client,
            destination_path=destination_path,
            overwrite=overwrite,
            restore_data_and_acl=restore_data_and_acl,
            copy_precedence=copy_precedence,
            from_time=from_time,
            to_time=to_time,
        )
        dest_user_account = destination_path
        rest_different_account = True
        restore_to_google = True

        if to_disk:
            dest_user_account = &#39;&#39;
            rest_different_account = False
            restore_to_google = False
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#39;cloudAppsRestoreOptions&#39;] = {
            &#34;instanceType&#34;: self._ca_instance_type,
            &#34;googleRestoreOptions&#34;: {
                &#34;strDestUserAccount&#34;: dest_user_account,
                &#34;folderGuid&#34;: &#34;&#34;,
                &#34;restoreToDifferentAccount&#34;: rest_different_account,
                &#34;restoreToGoogle&#34;: restore_to_google
            }
        }
        return self._process_restore_response(request_json)

    def enable_auto_discovery(self, mode=&#39;REGEX&#39;):
        &#34;&#34;&#34;Enables auto discovery on instance.

           Args:

                mode    (str)   -- Auto Discovery mode

                Valid Values:

                    REGEX
                    GROUP

        &#34;&#34;&#34;
        auto_discovery_dict = {
            &#39;REGEX&#39;: 0,
            &#39;GROUP&#39;: 1
        }
        instance_dict = {
            1: &#39;gInstance&#39;,
            2: &#39;gInstance&#39;,
            7: &#39;oneDriveInstance&#39;
        }
        auto_discovery_mode = auto_discovery_dict.get(mode.upper(), None)

        if auto_discovery_mode is None:
            raise SDKException(&#39;Instance&#39;, &#39;107&#39;)
        instance_prop = self._properties[&#39;cloudAppsInstance&#39;].copy()

        instance_prop[instance_dict[instance_prop[&#39;instanceType&#39;]]][&#39;isAutoDiscoveryEnabled&#39;] = True
        instance_prop[instance_dict[instance_prop[&#39;instanceType&#39;]]][&#39;autoDiscoveryMode&#39;] = auto_discovery_mode

        self._set_instance_properties(&#34;_properties[&#39;cloudAppsInstance&#39;]&#34;, instance_prop)
        self.refresh()

    def _get_instance_properties_json(self):
        &#34;&#34;&#34;Returns the instance properties json.&#34;&#34;&#34;

        return {&#39;instanceProperties&#39;: self._properties}

    def modify_index_server(self, modified_index_server):
        &#34;&#34;&#34;
            Method to modify the index server

            Arguments:
                modified_index_server        (str)--     new index server name
        &#34;&#34;&#34;
        update_dict = {
            &#34;instance&#34;: {
                &#34;instanceId&#34;: int(self.instance_id),
                &#34;clientId&#34;: int(self._agent_object._client_object.client_id),
                &#34;applicationId&#34;: int(self._agent_object.agent_id)
            },
                &#34;cloudAppsInstance&#34;: {
                    &#34;instanceType&#34;: self.ca_instance_type,
                    &#34;oneDriveInstance&#34;: {
                    },
                    &#34;generalCloudProperties&#34;: {
                        &#34;indexServer&#34;: {
                            &#34;clientName&#34;: modified_index_server
                        }
                    }
                }
            }

        self.update_properties(properties_dict=update_dict)

    def modify_accessnodes(self,modified_accessnodes_list,modified_user_name,modified_user_password):
        &#34;&#34;&#34;
                   Method to modify accessnodes

                   Arguments:
                       modified_accessnodes_list     (list)  --     list of new accessnodes
                       modified_user_name            (str)   --     new user account name
                       modified_user_password        (str)   --     new user account password
        &#34;&#34;&#34;
        member_servers=[]
        for client in modified_accessnodes_list:
            client_dict = {
                &#34;client&#34;: {
                    &#34;clientName&#34;: client
                }
            }
            member_servers.append(client_dict)

        update_dict = {
            &#34;instance&#34;: {
                &#34;instanceId&#34;: int(self.instance_id),
                &#34;clientId&#34;: int(self._agent_object._client_object.client_id),
                &#34;applicationId&#34;: int(self._agent_object.agent_id)
            },
            &#34;cloudAppsInstance&#34;: {
                &#34;instanceType&#34;: self.ca_instance_type,
                &#34;oneDriveInstance&#34;: {
                    &#34;serviceAccounts&#34;: {
                        &#34;accounts&#34;: [
                            {
                                &#34;serviceType&#34;: &#34;SYSTEM_ACCOUNT&#34;,
                                &#34;userAccount&#34;: {
                                    &#34;userName&#34;: modified_user_name,
                                    &#34;password&#34;: b64encode(modified_user_password.encode()).decode(),
                                }
                            }
                        ]
                    }
                },
                &#34;generalCloudProperties&#34;: {
                    &#34;memberServers&#34;: member_servers
                }
            }
        }

        self.update_properties(properties_dict=update_dict)

    def modify_connection_settings(self, azure_app_id, azure_dir_id, azure_app_secret):
        &#34;&#34;&#34;
                   Method to modify OneDrive connection settings

                   Arguments:
                       azure_app_id         (str)   --      new azure application id
                       azure_dir_id         (str)   --      new azure directory id
                       azure_app_secret        (str)   --     new azure app password

                  Returns:
                       None

                  Raises:
                      SDKException:
                            if failed to add

                            if response is empty

                            if response code is not as expected
        &#34;&#34;&#34;

        if not isinstance(azure_app_id, str) or not azure_app_id:
            raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;Invalid argument: azure_app_id must be a non-empty string&#39;)
        if not isinstance(azure_dir_id, str) or not azure_dir_id:
            raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;Invalid argument: azure_dir_id must be a non-empty string&#39;)
        if not isinstance(azure_app_secret, str) or not azure_app_secret:
            raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;Invalid argument: azure_app_secret must be a non-empty string&#39;)

        update_dict = {
            &#34;instance&#34;: self._instance,
            &#34;cloudAppsInstance&#34;: {
                &#34;instanceType&#34;: self.ca_instance_type,
                &#34;oneDriveInstance&#34;: {
                    &#34;azureAppList&#34;: {
                        &#34;azureApps&#34;: [
                            {
                                &#34;azureDirectoryId&#34;: azure_dir_id,
                                &#34;azureAppId&#34;: azure_app_id,
                                &#34;azureAppKeyValue&#34;: b64encode(azure_app_secret.encode()).decode()
                            }
                        ]
                    }
                }
            }
        }

        self.update_properties(properties_dict=update_dict)

    def delete_data_from_browse(self, item_guids, include_deleted_items=False, folder=False):
        &#34;&#34;&#34;
        Deletes items for the backupset in the Index and makes them unavailable for browsing and recovery

            Args:
                item_guids       (str/list)      --      The guids of items to be deleted from browse
                include_deleted_items (bool)     --      If True, deleted items will be included in browse
                folder           (bool)          --      If True, item to be deleted is Folder

            Returns:
                None        --      If delete request is sent successfully for file
                jobIds      --      If delete request is sent successfully for folder

            Raises:
                SDKException:
                    if failed to delete

                    if response is empty

                    if response code is not as expected

        &#34;&#34;&#34;
        if not isinstance(item_guids, str) or not item_guids:
            if not isinstance(item_guids, list):
                raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#34;Invalid argument: item_guid must be a non-empty string or list of strings.&#34;)
        if isinstance(item_guids, list):
            for item in item_guids:
                if not isinstance(item, str) or not item:
                    raise SDKException(&#39;Instance&#39;, &#39;102&#39;,&#34;Invalid argument: item_guid must be a non-empty string.&#34;)

        request_json = self._prepare_delete_json_onedrive_v2(item_guids, include_deleted_items=include_deleted_items, folder=folder)
        return self._process_delete_response(request_json)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance"><code class="flex name class">
<span>class <span class="ident">OneDriveInstance</span></span>
<span>(</span><span>agent_object, instance_name, instance_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing an Instance of the OneDrive instance type.</p>
<p>Initialise the instance object.</p>
<h2 id="args">Args</h2>
<p>agent_object
(object)
&ndash;
instance of the Agent class</p>
<p>instance_name
(str)
&ndash;
name of the instance</p>
<p>instance_id
(str)
&ndash;
id of the instance
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Instance class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/onedrive_instance.py#L59-L907" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class OneDriveInstance(CloudAppsInstance):
    &#34;&#34;&#34;Class for representing an Instance of the OneDrive instance type.&#34;&#34;&#34;

    def _get_instance_properties(self):
        &#34;&#34;&#34;Gets the properties of this instance.

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        super(OneDriveInstance, self)._get_instance_properties()
        # Common properties for Google and OneDrive
        self._ca_instance_type = None
        self._manage_content_automatically = None
        self._auto_discovery_enabled = None
        self._auto_discovery_mode = None
        self._proxy_client = None

        self._client_id = None
        self._tenant = None

        if &#39;cloudAppsInstance&#39; in self._properties:
            cloud_apps_instance = self._properties[&#39;cloudAppsInstance&#39;]
            self._ca_instance_type = cloud_apps_instance[&#39;instanceType&#39;]

            if &#39;oneDriveInstance&#39; in cloud_apps_instance:
                onedrive_instance = cloud_apps_instance[&#39;oneDriveInstance&#39;]

                self._manage_content_automatically = onedrive_instance[&#39;manageContentAutomatically&#39;]
                self._auto_discovery_enabled = onedrive_instance[&#39;isAutoDiscoveryEnabled&#39;]
                self._auto_discovery_mode = onedrive_instance[&#39;autoDiscoveryMode&#39;]
                if &#39;clientId&#39; in onedrive_instance:
                    self._client_id = onedrive_instance.get(&#39;clientId&#39;)
                    self._tenant = onedrive_instance.get(&#39;tenant&#39;)
                else:
                    self._client_id = onedrive_instance.get(
                        &#39;azureAppList&#39;, {}).get(&#39;azureApps&#39;, [{}])[0].get(&#39;azureAppId&#39;)
                    self._tenant = onedrive_instance.get(
                        &#39;azureAppList&#39;, {}).get(&#39;azureApps&#39;, [{}])[0].get(&#39;azureDirectoryId&#39;)

                if self._client_id is None:
                    raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;Azure App has not been configured&#39;)

            if &#39;generalCloudProperties&#39; in cloud_apps_instance:
                if &#39;proxyServers&#39; in cloud_apps_instance[&#39;generalCloudProperties&#39;]:
                    self._proxy_client = cloud_apps_instance.get(
                        &#39;generalCloudProperties&#39;, {}).get(&#39;proxyServers&#39;, [{}])[0].get(&#39;clientName&#39;)
                else:
                    if &#39;clientName&#39; in cloud_apps_instance.get(
                            &#39;generalCloudProperties&#39;, {}).get(&#39;memberServers&#39;, [{}])[0].get(&#39;client&#39;):
                        self._proxy_client = cloud_apps_instance.get(&#39;generalCloudProperties&#39;, {}).get(
                            &#39;memberServers&#39;, [{}])[0].get(&#39;client&#39;, {}).get(&#39;clientName&#39;)
                    else:
                        self._proxy_client = cloud_apps_instance.get(&#39;generalCloudProperties&#39;, {}).get(
                            &#39;memberServers&#39;, [{}])[0].get(&#39;client&#39;, {}).get(&#39;clientGroupName&#39;)

                if self._proxy_client is None:
                    raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;Access Node has not been configured&#39;)

    @property
    def ca_instance_type(self):
        &#34;&#34;&#34;Returns the CloudApps instance type&#34;&#34;&#34;
        if self._ca_instance_type == 7:
            return &#39;ONEDRIVE&#39;
        return self._ca_instance_type

    @property
    def manage_content_automatically(self):
        &#34;&#34;&#34;Returns the CloudApps Manage Content Automatically property&#34;&#34;&#34;
        return self._manage_content_automatically

    @property
    def auto_discovery_status(self):
        &#34;&#34;&#34;Treats the Auto discovery property as a read-only attribute.&#34;&#34;&#34;
        return self._auto_discovery_enabled

    @property
    def auto_discovery_mode(self):
        &#34;&#34;&#34;Returns the Auto discovery mode property&#34;&#34;&#34;
        return self._auto_discovery_mode

    @property
    def onedrive_client_id(self):
        &#34;&#34;&#34;Returns the OneDrive app client id&#34;&#34;&#34;
        return self._client_id

    @property
    def onedrive_tenant(self):
        &#34;&#34;&#34;Returns the OneDrive tenant id&#34;&#34;&#34;
        return self._tenant

    @property
    def proxy_client(self):
        &#34;&#34;&#34;Returns the proxy client name to this instance&#34;&#34;&#34;
        return self._proxy_client


    def _prepare_advsearchgrp_onedrive_for_business_client(self, source_item_list, subclient_id):
        &#34;&#34;&#34;
                    Utility function to prepare advsearchgrp json for restore job for OneDrive for business clients

                    Args:
                        source_item_list (list)         --  list of user GUID to process in restore

                        subclient_id                    --  subclient id of the client

                    Returns:
                        advsearchgrp (dict) - advsearchgrp json for restore job
        &#34;&#34;&#34;


        user_guid = source_item_list[0]
        advsearchgrp = {
            &#34;fileFilter&#34;: [
                {
                    &#34;interGroupOP&#34;: &#34;FTAnd&#34;,
                    &#34;filter&#34;: {
                        &#34;filters&#34;: [
                            {
                                &#34;field&#34;: &#34;HIDDEN&#34;,
                                &#34;fieldValues&#34;: {
                                    &#34;values&#34;: [
                                        &#34;true&#34;
                                    ]
                                },
                                &#34;intraFieldOp&#34;: &#34;FTNot&#34;
                            },
                            {
                                &#34;field&#34;: &#34;CV_OBJECT_GUID&#34;,
                                &#34;fieldValues&#34;: {
                                    &#34;values&#34;: [
                                        user_guid
                                    ]
                                },
                                &#34;intraFieldOp&#34;: &#34;FTOr&#34;
                            }
                        ],
                        &#34;interFilterOP&#34;: &#34;FTAnd&#34;
                    }
                }
            ],
            &#34;commonFilter&#34;: [
                {
                    &#34;filter&#34;: {
                        &#34;filters&#34;: [
                            {
                                &#34;field&#34;: &#34;CISTATE&#34;,
                                &#34;fieldValues&#34;: {
                                    &#34;values&#34;: [
                                        &#34;1&#34;
                                    ]
                                },
                                &#34;intraFieldOp&#34;: &#34;FTOr&#34;,
                                &#34;groupType&#34;: 0
                            },
                            {
                                &#34;field&#34;: &#34;IS_VISIBLE&#34;,
                                &#34;fieldValues&#34;: {
                                    &#34;values&#34;: [
                                        &#34;true&#34;
                                    ],
                                    &#34;isRange&#34;: False,
                                    &#34;isMoniker&#34;: False
                                },
                                &#34;intraFieldOp&#34;: &#34;FTOr&#34;,
                                &#34;intraFieldOpStr&#34;: &#34;None&#34;
                            }
                        ],
                        &#34;interFilterOP&#34;: &#34;FTAnd&#34;
                    }
                }
            ],
            &#34;galaxyFilter&#34;: [
                {
                    &#34;appIdList&#34;: [
                        subclient_id
                    ]
                }
            ],
            &#34;graphFilter&#34;: [
                {
                    &#34;fromField&#34;: &#34;PARENT_GUID&#34;,
                    &#34;toField&#34;: &#34;CV_OBJECT_GUID&#34;,
                    &#34;returnRoot&#34;: True,
                    &#34;traversalFilter&#34;: [
                        {
                            &#34;filters&#34;: [
                                {
                                    &#34;field&#34;: &#34;IS_VISIBLE&#34;,
                                    &#34;fieldValues&#34;: {
                                        &#34;values&#34;: [
                                            &#34;true&#34;
                                        ]
                                    },
                                    &#34;intraFieldOp&#34;: &#34;FTAnd&#34;,
                                    &#34;groupType&#34;: 0
                                },
                                {
                                    &#34;field&#34;: &#34;HIDDEN&#34;,
                                    &#34;fieldValues&#34;: {
                                        &#34;values&#34;: [
                                            &#34;true&#34;
                                        ]
                                    },
                                    &#34;intraFieldOp&#34;: &#34;FTNot&#34;
                                }
                            ]
                        }
                    ]
                }
            ]
        }

        return advsearchgrp

    def _prepare_findquery_onedrive_for_business_client(self, source_item_list, subclient_id):
        &#34;&#34;&#34;
            Utility function to prepare findquery json for restore job for OneDrive for bussiness clients

            Args:
                source_item_list (list)         --  list of user GUID to process in restore

                subclient_id                    --  subclient id of the client

            Returns:
                findquery (dict) - findquery json for restore job
        &#34;&#34;&#34;

        findquery = {
                  &#34;searchProcessingInfo&#34;: {
                    &#34;pageSize&#34;: 20,
                    &#34;resultOffset&#34;: 0,
                    &#34;sortParams&#34;: [
                      {
                        &#34;sortField&#34;: &#34;DATA_TYPE&#34;,
                        &#34;sortDirection&#34;: &#34;DESCENDING&#34;
                      },
                      {
                        &#34;sortField&#34;: &#34;FileName&#34;,
                        &#34;sortDirection&#34;: &#34;ASCENDING&#34;
                      }
                    ],
                    &#34;queryParams&#34;: [
                      {
                        &#34;param&#34;: &#34;ENABLE_MIXEDVIEW&#34;,
                        &#34;value&#34;: &#34;true&#34;
                      },
                      {
                        &#34;param&#34;: &#34;RESPONSE_FIELD_LIST&#34;,
                        &#34;value&#34;: &#34;FAST_URL,BACKUPTIME,SIZEINKB,MODIFIEDTIME,CONTENTID,CV_TURBO_GUID,AFILEID,AFILEOFFSET,COMMCELLNO,FILE_NAME,FILE_FOLDER,CVSTUB,DATA_TYPE,APPID,JOBID,CISTATE,DATE_DELETED,IdxFlags,CV_OBJECT_GUID,PARENT_GUID,CUSTODIAN,OWNER,ObjectType&#34;
                      },
                      {
                        &#34;param&#34;: &#34;DO_NOT_AUDIT&#34;,
                        &#34;value&#34;: &#34;false&#34;
                      },
                      {
                        &#34;param&#34;: &#34;COLLAPSE_FIELD&#34;,
                        &#34;value&#34;: &#34;CV_OBJECT_GUID&#34;
                      },
                      {
                        &#34;param&#34;: &#34;COLLAPSE_SORT&#34;,
                        &#34;value&#34;: &#34;BACKUPTIME DESC&#34;
                      },
                      {
                        &#34;param&#34;: &#34;ENABLE_NAVIGATION&#34;,
                        &#34;value&#34;: &#34;on&#34;
                      },
                      {
                        &#34;param&#34;: &#34;ENABLE_DEFAULTFACETS&#34;,
                        &#34;value&#34;: &#34;false&#34;
                      }
                    ]
                  },
                  &#34;advSearchGrp&#34;: self._prepare_advsearchgrp_onedrive_for_business_client(source_item_list,subclient_id),
                  &#34;mode&#34;: &#34;WebConsole&#34;
                }

        return findquery




    def _prepare_restore_json_onedrive_for_business_client(self, source_item_list, **kwargs):

        &#34;&#34;&#34; Utility function to prepare user level restore json for OneDrive for bussiness clients

            Args:
                source_item_list (list)         --  list of user GUID to process in restore

            Kwargs:

                out_of_place (bool)             --  If True, out of place restore will be performed

                disk_restore (bool)             --  If True, restore to disk will be performed

                destination_path (str)          --  destination path for oop and disk restores

                destination_client              -- destination client for disk restore

                overwrite (bool)                --  If True, files will be overwritten in destination if already exists

                restore_as_copy (bool)          --  If True, files will be restored as copy if already exists

                skip_file_permissions (bool)    --  If True, file permissions will be restored

                include_deleted_items  (bool)   --  If True, deleted items will be included

            Returns:
                request_json (dict) - request json for restore job

            Raises:
                SDKException:
                    if destination client with given name does not exist

                    if type of parameter is invalid

        &#34;&#34;&#34;

        out_of_place = kwargs.get(&#39;out_of_place&#39;, False)
        disk_restore = kwargs.get(&#39;disk_restore&#39;, False)
        destination_path = kwargs.get(&#39;destination_path&#39;, False)
        destination_client = kwargs.get(&#39;destination_client&#39;)
        overwrite = kwargs.get(&#39;overwrite&#39;, False)
        restore_as_copy = kwargs.get(&#39;restore_as_copy&#39;, False)
        skip_file_permissions = kwargs.get(&#39;skip_file_permissions&#39;, True)
        include_deleted_items = kwargs.get(&#39;include_deleted_items&#39;, False)


        if destination_client:
            if self._commcell_object.clients.all_clients.get(destination_client):
                destination_client_object = self._commcell_object.clients.all_clients.get(destination_client)
                destination_client_id = int(destination_client_object.get(&#39;id&#39;))
            else:
                raise SDKException(&#39;Client&#39;, &#39;102&#39;, &#39;Client &#34;{0}&#34; does not exist.&#39;.format(destination_client))

        if ((destination_client and not isinstance(destination_client, str) or
             destination_path and not isinstance(destination_path, str)) or not
            (isinstance(source_item_list, list) and
             isinstance(skip_file_permissions, bool) and
             isinstance(disk_restore, bool) and
             isinstance(out_of_place, bool) and
             isinstance(overwrite, bool) and
             isinstance(restore_as_copy, bool))):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        request_json = self._restore_json(client=self._agent_object._client_object)

        subtasks = request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0]
        options = subtasks[&#39;options&#39;]
        restore_options = options[&#39;restoreOptions&#39;]

        options[&#34;restoreOptions&#34;][&#34;browseOption&#34;] = {
            &#34;commCellId&#34;: self._commcell_object.commcell_id,
            &#34;showDeletedItems&#34;: False
        }

        restore_options[&#39;commonOptions&#39;] = {
            &#34;overwriteFiles&#34;: overwrite,
            &#34;skip&#34;: True if not restore_as_copy and not overwrite else False,
            &#34;unconditionalOverwrite&#34;: overwrite
        }

        destination = restore_options[&#39;destination&#39;]
        destination[&#39;destAppId&#39;] = AppIDAType.WINDOWS_FILE_SYSTEM.value if disk_restore else AppIDAType.CLOUD_APP.value
        destination[&#39;inPlace&#39;] = False if out_of_place or disk_restore else True

        destination[&#39;destClient&#39;] = {
            &#34;clientId&#34;: destination_client_id,
            &#34;clientName&#34;: destination_client
        } if disk_restore else {
            &#34;clientId&#34;: int(self._agent_object._client_object.client_id),
            &#34;clientName&#34;: self._agent_object._client_object.client_name
        }

        if destination_path:
            destination[&#39;destPath&#39;] = [destination_path]

        restore_options[&#39;fileOption&#39;][&#39;sourceItem&#39;] = source_item_list

        restore_options[&#39;cloudAppsRestoreOptions&#39;] = {
            &#34;instanceType&#34;: self._ca_instance_type,
            &#34;googleRestoreOptions&#34;: {
                &#34;skipPermissionsRestore&#34;: False if disk_restore else skip_file_permissions,
                &#34;restoreToDifferentAccount&#34;: True if out_of_place else False,
                &#34;restoreAsCopy&#34;: False if disk_restore else restore_as_copy,
                &#34;filelevelRestore&#34;: False,
                &#34;strDestUserAccount&#34;: destination_path if out_of_place else &#39;&#39;,
                &#34;overWriteItems&#34;: False if disk_restore else overwrite,
                &#34;restoreToGoogle&#34;: False if disk_restore else True
            }
        }

        del subtasks[&#39;subTaskOperation&#39;]
        del restore_options[&#39;fileOption&#39;]
        del restore_options[&#39;impersonation&#39;]
        del restore_options[&#39;volumeRstOption&#39;]
        del restore_options[&#39;sharePointRstOption&#39;]
        del restore_options[&#39;virtualServerRstOption&#39;]

        associations = request_json[&#39;taskInfo&#39;][&#39;associations&#39;][0]
        subclient_id = associations[&#39;subclientId&#39;]

        cloudAppsRestoreOptions = restore_options[&#39;cloudAppsRestoreOptions&#39;]
        cloudAppsRestoreOptions[&#39;googleRestoreOptions&#39;][&#39;findQuery&#39;] = self._prepare_findquery_onedrive_for_business_client(source_item_list, subclient_id)
        if include_deleted_items:
            cloudAppsRestoreOptions[&#39;googleRestoreOptions&#39;][&#39;findQuery&#39;][&#39;advSearchGrp&#39;][&#39;commonFilter&#39;][0][&#39;filter&#39;][&#39;filters&#39;][0][&#39;fieldValues&#39;][&#39;values&#39;].extend([&#34;3333&#34;,&#34;3334&#34;,&#34;3335&#34;])

        destination_option = &#34;Destination&#34;
        destination_value = &#34;Original location&#34;
        if out_of_place:
            destination_option = &#34;Destination user&#34;
            destination_value = source_item_list[0]
        if disk_restore:
            destination_option = &#34;Destination server&#34;
            destination_value = destination_client


        options[&#34;commonOpts&#34;] = {
            &#34;notifyUserOnJobCompletion&#34;: False,
            &#34;jobMetadata&#34;: [
              {
                &#34;selectedItems&#34;: [
                  {
                    &#34;itemName&#34;: source_item_list[0],
                    &#34;itemType&#34;: &#34;User&#34;
                  }
                ],
                &#34;jobOptionItems&#34;: [
                  {
                    &#34;option&#34;: &#34;Restore destination&#34;,
                    &#34;value&#34;: &#34;OneDrive for Business&#34;
                  },
                  {
                    &#34;option&#34;: &#34;Source&#34;,
                    &#34;value&#34;: source_item_list[0]
                  },
                  {
                    &#34;option&#34;: destination_option,
                    &#34;value&#34;: destination_value
                  },
                  {
                    &#34;option&#34;: &#34;If the file exists&#34;,
                    &#34;value&#34;: &#34;Restore as a copy&#34; if restore_as_copy and not overwrite else &#34;Unconditionally overwrite&#34; if overwrite else &#34;Skip&#34;

                  },
                  {
                    &#34;option&#34;: &#34;Skip file permissions&#34;,
                    &#34;value&#34;: &#34;Enabled&#34; if skip_file_permissions else &#34;Disabled&#34;
                  },
                  {
                    &#34;option&#34;: &#34;Include deleted items&#34;,
                    &#34;value&#34;: &#34;Enabled&#34; if include_deleted_items else &#34;Disabled&#34;
                  }
                ]
              }
            ]
          }

        joboptionitems = options[&#39;commonOpts&#39;][&#39;jobMetadata&#39;][0][&#39;jobOptionItems&#39;]

        if out_of_place:
            joboptionitems.append({&#34;option&#34;: &#34;Destination client&#34;,&#34;value&#34;: destination_client })
        if disk_restore:
            joboptionitems.append({&#34;option&#34;: &#34;Destination path&#34;, &#34;value&#34;: destination_path})

        return request_json

    def _prepare_delete_json_onedrive_v2(self, item_guids, **kwargs):
        &#34;&#34;&#34; Utility function to prepare delete documents json for OneDrive for bussiness clients

            Args:
                item_guid (str)         --  item GUID to delete in browse

                Kwargs:

                    include_deleted_items (bool)             --  If True, deleted items will be included in search

            Returns:
                request_json (dict) - request json for delete document

            Raises:
                SDKException:
                    if destination client with given item does not exist

                    if type of parameter is invalid
        &#34;&#34;&#34;
        folder = kwargs.get(&#39;folder&#39;, False)
        include_deleted_items = kwargs.get(&#39;include_deleted_items&#39;, False)
        subclient_id = self.subclients[&#39;default&#39;][&#39;id&#39;]

        if isinstance(item_guids, str):
            source_item_list = [item_guids]
        else:
            source_item_list = [].extend(item_guids)
        req_json = {
            &#39;opType&#39;: 1,
            &#39;bulkMode&#39;: folder,
            &#39;deleteOption&#39;: {
                &#39;folderDelete&#39;: folder
            },
            &#39;searchReq&#39;: {
                &#39;mode&#39;: 4,
                &#39;facetRequests&#39;: {
                    &#39;facetRequest&#39;: []
                },
                &#39;advSearchGrp&#39;: self._prepare_advsearchgrp_onedrive_for_business_client(source_item_list, subclient_id),
                &#39;searchProcessingInfo&#39;: {
                    &#39;resultOffset&#39;: 0,
                    &#39;pageSize&#39;: 50,
                    &#39;queryParams&#39;: [
                        {
                            &#39;param&#39;: &#39;ENABLE_MIXEDVIEW&#39;,
                            &#39;value&#39;: &#39;true&#39;
                        },
                        {
                            &#39;param&#39;: &#39;ENABLE_NAVIGATION&#39;,
                            &#39;value&#39;: &#39;on&#39;
                        },
                        {
                            &#39;param&#39;: &#39;ENABLE_DEFAULTFACETS&#39;,
                            &#39;value&#39;: &#39;false&#39;
                        }
                    ],
                    &#39;sortParams&#39;: []
                }
            }
        }

        if isinstance(item_guids, list):
            req_json[&#39;searchReq&#39;][&#39;advSearchGrp&#39;][&#39;fileFilter&#39;][0][&#39;filter&#39;][&#39;filters&#39;][1][&#39;fieldValues&#39;][
                &#39;values&#39;].extend(item_guids[1:])

        if include_deleted_items:
            req_json[&#39;searchReq&#39;][&#39;advSearchGrp&#39;][&#39;commonFilter&#39;][0][&#39;filter&#39;][&#39;filters&#39;][0][&#39;fieldValues&#39;][&#39;values&#39;].extend(
                [&#34;3333&#34;, &#34;3334&#34;, &#34;3335&#34;])

        return req_json

    def restore_out_of_place(
            self,
            client,
            destination_path,
            paths,
            overwrite=True,
            restore_data_and_acl=True,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            to_disk=False):
        &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the input client,
            at the specified destionation location.

            Args:
                client                (str/object) --  either the name of the client or
                                                           the instance of the Client

                destination_path      (str)        --  full path of the restore location on client

                paths                 (list)       --  list of full paths of
                                                           files/folders to restore

                overwrite             (bool)       --  unconditional overwrite files during restore
                    default: True

                restore_data_and_acl  (bool)       --  restore data and ACL files
                    default: True

                copy_precedence         (int)   --  copy precedence value of storage policy copy
                    default: None

                from_time           (str)       --  time to retore the contents after
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time           (str)         --  time to retore the contents before
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_disk             (bool)       --  If True, restore to disk will be performed

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if client is not a string or Client instance

                    if destination_path is not a string

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        from cvpysdk.client import Client

        if not ((isinstance(client, str) or isinstance(client, Client)) and
                isinstance(destination_path, str) and
                isinstance(paths, list) and
                isinstance(overwrite, bool) and
                isinstance(restore_data_and_acl, bool)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if isinstance(client, Client):
            client = client
        elif isinstance(client, str):
            client = Client(self._commcell_object, client)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;105&#39;)

        paths = self._filter_paths(paths)

        destination_path = self._filter_paths([destination_path], True)

        if paths == []:
            raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)

        request_json = self._restore_json(
            paths=paths,
            in_place=False,
            client=client,
            destination_path=destination_path,
            overwrite=overwrite,
            restore_data_and_acl=restore_data_and_acl,
            copy_precedence=copy_precedence,
            from_time=from_time,
            to_time=to_time,
        )
        dest_user_account = destination_path
        rest_different_account = True
        restore_to_google = True

        if to_disk:
            dest_user_account = &#39;&#39;
            rest_different_account = False
            restore_to_google = False
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#39;cloudAppsRestoreOptions&#39;] = {
            &#34;instanceType&#34;: self._ca_instance_type,
            &#34;googleRestoreOptions&#34;: {
                &#34;strDestUserAccount&#34;: dest_user_account,
                &#34;folderGuid&#34;: &#34;&#34;,
                &#34;restoreToDifferentAccount&#34;: rest_different_account,
                &#34;restoreToGoogle&#34;: restore_to_google
            }
        }
        return self._process_restore_response(request_json)

    def enable_auto_discovery(self, mode=&#39;REGEX&#39;):
        &#34;&#34;&#34;Enables auto discovery on instance.

           Args:

                mode    (str)   -- Auto Discovery mode

                Valid Values:

                    REGEX
                    GROUP

        &#34;&#34;&#34;
        auto_discovery_dict = {
            &#39;REGEX&#39;: 0,
            &#39;GROUP&#39;: 1
        }
        instance_dict = {
            1: &#39;gInstance&#39;,
            2: &#39;gInstance&#39;,
            7: &#39;oneDriveInstance&#39;
        }
        auto_discovery_mode = auto_discovery_dict.get(mode.upper(), None)

        if auto_discovery_mode is None:
            raise SDKException(&#39;Instance&#39;, &#39;107&#39;)
        instance_prop = self._properties[&#39;cloudAppsInstance&#39;].copy()

        instance_prop[instance_dict[instance_prop[&#39;instanceType&#39;]]][&#39;isAutoDiscoveryEnabled&#39;] = True
        instance_prop[instance_dict[instance_prop[&#39;instanceType&#39;]]][&#39;autoDiscoveryMode&#39;] = auto_discovery_mode

        self._set_instance_properties(&#34;_properties[&#39;cloudAppsInstance&#39;]&#34;, instance_prop)
        self.refresh()

    def _get_instance_properties_json(self):
        &#34;&#34;&#34;Returns the instance properties json.&#34;&#34;&#34;

        return {&#39;instanceProperties&#39;: self._properties}

    def modify_index_server(self, modified_index_server):
        &#34;&#34;&#34;
            Method to modify the index server

            Arguments:
                modified_index_server        (str)--     new index server name
        &#34;&#34;&#34;
        update_dict = {
            &#34;instance&#34;: {
                &#34;instanceId&#34;: int(self.instance_id),
                &#34;clientId&#34;: int(self._agent_object._client_object.client_id),
                &#34;applicationId&#34;: int(self._agent_object.agent_id)
            },
                &#34;cloudAppsInstance&#34;: {
                    &#34;instanceType&#34;: self.ca_instance_type,
                    &#34;oneDriveInstance&#34;: {
                    },
                    &#34;generalCloudProperties&#34;: {
                        &#34;indexServer&#34;: {
                            &#34;clientName&#34;: modified_index_server
                        }
                    }
                }
            }

        self.update_properties(properties_dict=update_dict)

    def modify_accessnodes(self,modified_accessnodes_list,modified_user_name,modified_user_password):
        &#34;&#34;&#34;
                   Method to modify accessnodes

                   Arguments:
                       modified_accessnodes_list     (list)  --     list of new accessnodes
                       modified_user_name            (str)   --     new user account name
                       modified_user_password        (str)   --     new user account password
        &#34;&#34;&#34;
        member_servers=[]
        for client in modified_accessnodes_list:
            client_dict = {
                &#34;client&#34;: {
                    &#34;clientName&#34;: client
                }
            }
            member_servers.append(client_dict)

        update_dict = {
            &#34;instance&#34;: {
                &#34;instanceId&#34;: int(self.instance_id),
                &#34;clientId&#34;: int(self._agent_object._client_object.client_id),
                &#34;applicationId&#34;: int(self._agent_object.agent_id)
            },
            &#34;cloudAppsInstance&#34;: {
                &#34;instanceType&#34;: self.ca_instance_type,
                &#34;oneDriveInstance&#34;: {
                    &#34;serviceAccounts&#34;: {
                        &#34;accounts&#34;: [
                            {
                                &#34;serviceType&#34;: &#34;SYSTEM_ACCOUNT&#34;,
                                &#34;userAccount&#34;: {
                                    &#34;userName&#34;: modified_user_name,
                                    &#34;password&#34;: b64encode(modified_user_password.encode()).decode(),
                                }
                            }
                        ]
                    }
                },
                &#34;generalCloudProperties&#34;: {
                    &#34;memberServers&#34;: member_servers
                }
            }
        }

        self.update_properties(properties_dict=update_dict)

    def modify_connection_settings(self, azure_app_id, azure_dir_id, azure_app_secret):
        &#34;&#34;&#34;
                   Method to modify OneDrive connection settings

                   Arguments:
                       azure_app_id         (str)   --      new azure application id
                       azure_dir_id         (str)   --      new azure directory id
                       azure_app_secret        (str)   --     new azure app password

                  Returns:
                       None

                  Raises:
                      SDKException:
                            if failed to add

                            if response is empty

                            if response code is not as expected
        &#34;&#34;&#34;

        if not isinstance(azure_app_id, str) or not azure_app_id:
            raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;Invalid argument: azure_app_id must be a non-empty string&#39;)
        if not isinstance(azure_dir_id, str) or not azure_dir_id:
            raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;Invalid argument: azure_dir_id must be a non-empty string&#39;)
        if not isinstance(azure_app_secret, str) or not azure_app_secret:
            raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;Invalid argument: azure_app_secret must be a non-empty string&#39;)

        update_dict = {
            &#34;instance&#34;: self._instance,
            &#34;cloudAppsInstance&#34;: {
                &#34;instanceType&#34;: self.ca_instance_type,
                &#34;oneDriveInstance&#34;: {
                    &#34;azureAppList&#34;: {
                        &#34;azureApps&#34;: [
                            {
                                &#34;azureDirectoryId&#34;: azure_dir_id,
                                &#34;azureAppId&#34;: azure_app_id,
                                &#34;azureAppKeyValue&#34;: b64encode(azure_app_secret.encode()).decode()
                            }
                        ]
                    }
                }
            }
        }

        self.update_properties(properties_dict=update_dict)

    def delete_data_from_browse(self, item_guids, include_deleted_items=False, folder=False):
        &#34;&#34;&#34;
        Deletes items for the backupset in the Index and makes them unavailable for browsing and recovery

            Args:
                item_guids       (str/list)      --      The guids of items to be deleted from browse
                include_deleted_items (bool)     --      If True, deleted items will be included in browse
                folder           (bool)          --      If True, item to be deleted is Folder

            Returns:
                None        --      If delete request is sent successfully for file
                jobIds      --      If delete request is sent successfully for folder

            Raises:
                SDKException:
                    if failed to delete

                    if response is empty

                    if response code is not as expected

        &#34;&#34;&#34;
        if not isinstance(item_guids, str) or not item_guids:
            if not isinstance(item_guids, list):
                raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#34;Invalid argument: item_guid must be a non-empty string or list of strings.&#34;)
        if isinstance(item_guids, list):
            for item in item_guids:
                if not isinstance(item, str) or not item:
                    raise SDKException(&#39;Instance&#39;, &#39;102&#39;,&#34;Invalid argument: item_guid must be a non-empty string.&#34;)

        request_json = self._prepare_delete_json_onedrive_v2(item_guids, include_deleted_items=include_deleted_items, folder=folder)
        return self._process_delete_response(request_json)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.instances.cainstance.CloudAppsInstance" href="../cainstance.html#cvpysdk.instances.cainstance.CloudAppsInstance">CloudAppsInstance</a></li>
<li><a title="cvpysdk.instance.Instance" href="../../instance.html#cvpysdk.instance.Instance">Instance</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance.auto_discovery_mode"><code class="name">var <span class="ident">auto_discovery_mode</span></code></dt>
<dd>
<div class="desc"><p>Returns the Auto discovery mode property</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/onedrive_instance.py#L138-L141" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def auto_discovery_mode(self):
    &#34;&#34;&#34;Returns the Auto discovery mode property&#34;&#34;&#34;
    return self._auto_discovery_mode</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance.auto_discovery_status"><code class="name">var <span class="ident">auto_discovery_status</span></code></dt>
<dd>
<div class="desc"><p>Treats the Auto discovery property as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/onedrive_instance.py#L133-L136" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def auto_discovery_status(self):
    &#34;&#34;&#34;Treats the Auto discovery property as a read-only attribute.&#34;&#34;&#34;
    return self._auto_discovery_enabled</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance.ca_instance_type"><code class="name">var <span class="ident">ca_instance_type</span></code></dt>
<dd>
<div class="desc"><p>Returns the CloudApps instance type</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/onedrive_instance.py#L121-L126" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def ca_instance_type(self):
    &#34;&#34;&#34;Returns the CloudApps instance type&#34;&#34;&#34;
    if self._ca_instance_type == 7:
        return &#39;ONEDRIVE&#39;
    return self._ca_instance_type</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance.manage_content_automatically"><code class="name">var <span class="ident">manage_content_automatically</span></code></dt>
<dd>
<div class="desc"><p>Returns the CloudApps Manage Content Automatically property</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/onedrive_instance.py#L128-L131" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def manage_content_automatically(self):
    &#34;&#34;&#34;Returns the CloudApps Manage Content Automatically property&#34;&#34;&#34;
    return self._manage_content_automatically</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance.onedrive_client_id"><code class="name">var <span class="ident">onedrive_client_id</span></code></dt>
<dd>
<div class="desc"><p>Returns the OneDrive app client id</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/onedrive_instance.py#L143-L146" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def onedrive_client_id(self):
    &#34;&#34;&#34;Returns the OneDrive app client id&#34;&#34;&#34;
    return self._client_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance.onedrive_tenant"><code class="name">var <span class="ident">onedrive_tenant</span></code></dt>
<dd>
<div class="desc"><p>Returns the OneDrive tenant id</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/onedrive_instance.py#L148-L151" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def onedrive_tenant(self):
    &#34;&#34;&#34;Returns the OneDrive tenant id&#34;&#34;&#34;
    return self._tenant</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance.proxy_client"><code class="name">var <span class="ident">proxy_client</span></code></dt>
<dd>
<div class="desc"><p>Returns the proxy client name to this instance</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/onedrive_instance.py#L153-L156" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def proxy_client(self):
    &#34;&#34;&#34;Returns the proxy client name to this instance&#34;&#34;&#34;
    return self._proxy_client</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance.delete_data_from_browse"><code class="name flex">
<span>def <span class="ident">delete_data_from_browse</span></span>(<span>self, item_guids, include_deleted_items=False, folder=False)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes items for the backupset in the Index and makes them unavailable for browsing and recovery</p>
<pre><code>Args:
    item_guids       (str/list)      --      The guids of items to be deleted from browse
    include_deleted_items (bool)     --      If True, deleted items will be included in browse
    folder           (bool)          --      If True, item to be deleted is Folder

Returns:
    None        --      If delete request is sent successfully for file
    jobIds      --      If delete request is sent successfully for folder

Raises:
    SDKException:
        if failed to delete

        if response is empty

        if response code is not as expected
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/onedrive_instance.py#L876-L907" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete_data_from_browse(self, item_guids, include_deleted_items=False, folder=False):
    &#34;&#34;&#34;
    Deletes items for the backupset in the Index and makes them unavailable for browsing and recovery

        Args:
            item_guids       (str/list)      --      The guids of items to be deleted from browse
            include_deleted_items (bool)     --      If True, deleted items will be included in browse
            folder           (bool)          --      If True, item to be deleted is Folder

        Returns:
            None        --      If delete request is sent successfully for file
            jobIds      --      If delete request is sent successfully for folder

        Raises:
            SDKException:
                if failed to delete

                if response is empty

                if response code is not as expected

    &#34;&#34;&#34;
    if not isinstance(item_guids, str) or not item_guids:
        if not isinstance(item_guids, list):
            raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#34;Invalid argument: item_guid must be a non-empty string or list of strings.&#34;)
    if isinstance(item_guids, list):
        for item in item_guids:
            if not isinstance(item, str) or not item:
                raise SDKException(&#39;Instance&#39;, &#39;102&#39;,&#34;Invalid argument: item_guid must be a non-empty string.&#34;)

    request_json = self._prepare_delete_json_onedrive_v2(item_guids, include_deleted_items=include_deleted_items, folder=folder)
    return self._process_delete_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance.enable_auto_discovery"><code class="name flex">
<span>def <span class="ident">enable_auto_discovery</span></span>(<span>self, mode='REGEX')</span>
</code></dt>
<dd>
<div class="desc"><p>Enables auto discovery on instance.</p>
<h2 id="args">Args</h2>
<p>mode
(str)
&ndash; Auto Discovery mode</p>
<p>Valid Values:</p>
<pre><code>REGEX
GROUP
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/onedrive_instance.py#L715-L747" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable_auto_discovery(self, mode=&#39;REGEX&#39;):
    &#34;&#34;&#34;Enables auto discovery on instance.

       Args:

            mode    (str)   -- Auto Discovery mode

            Valid Values:

                REGEX
                GROUP

    &#34;&#34;&#34;
    auto_discovery_dict = {
        &#39;REGEX&#39;: 0,
        &#39;GROUP&#39;: 1
    }
    instance_dict = {
        1: &#39;gInstance&#39;,
        2: &#39;gInstance&#39;,
        7: &#39;oneDriveInstance&#39;
    }
    auto_discovery_mode = auto_discovery_dict.get(mode.upper(), None)

    if auto_discovery_mode is None:
        raise SDKException(&#39;Instance&#39;, &#39;107&#39;)
    instance_prop = self._properties[&#39;cloudAppsInstance&#39;].copy()

    instance_prop[instance_dict[instance_prop[&#39;instanceType&#39;]]][&#39;isAutoDiscoveryEnabled&#39;] = True
    instance_prop[instance_dict[instance_prop[&#39;instanceType&#39;]]][&#39;autoDiscoveryMode&#39;] = auto_discovery_mode

    self._set_instance_properties(&#34;_properties[&#39;cloudAppsInstance&#39;]&#34;, instance_prop)
    self.refresh()</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance.modify_accessnodes"><code class="name flex">
<span>def <span class="ident">modify_accessnodes</span></span>(<span>self, modified_accessnodes_list, modified_user_name, modified_user_password)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to modify accessnodes</p>
<h2 id="arguments">Arguments</h2>
<p>modified_accessnodes_list
(list)
&ndash;
list of new accessnodes
modified_user_name
(str)
&ndash;
new user account name
modified_user_password
(str)
&ndash;
new user account password</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/onedrive_instance.py#L781-L826" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def modify_accessnodes(self,modified_accessnodes_list,modified_user_name,modified_user_password):
    &#34;&#34;&#34;
               Method to modify accessnodes

               Arguments:
                   modified_accessnodes_list     (list)  --     list of new accessnodes
                   modified_user_name            (str)   --     new user account name
                   modified_user_password        (str)   --     new user account password
    &#34;&#34;&#34;
    member_servers=[]
    for client in modified_accessnodes_list:
        client_dict = {
            &#34;client&#34;: {
                &#34;clientName&#34;: client
            }
        }
        member_servers.append(client_dict)

    update_dict = {
        &#34;instance&#34;: {
            &#34;instanceId&#34;: int(self.instance_id),
            &#34;clientId&#34;: int(self._agent_object._client_object.client_id),
            &#34;applicationId&#34;: int(self._agent_object.agent_id)
        },
        &#34;cloudAppsInstance&#34;: {
            &#34;instanceType&#34;: self.ca_instance_type,
            &#34;oneDriveInstance&#34;: {
                &#34;serviceAccounts&#34;: {
                    &#34;accounts&#34;: [
                        {
                            &#34;serviceType&#34;: &#34;SYSTEM_ACCOUNT&#34;,
                            &#34;userAccount&#34;: {
                                &#34;userName&#34;: modified_user_name,
                                &#34;password&#34;: b64encode(modified_user_password.encode()).decode(),
                            }
                        }
                    ]
                }
            },
            &#34;generalCloudProperties&#34;: {
                &#34;memberServers&#34;: member_servers
            }
        }
    }

    self.update_properties(properties_dict=update_dict)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance.modify_connection_settings"><code class="name flex">
<span>def <span class="ident">modify_connection_settings</span></span>(<span>self, azure_app_id, azure_dir_id, azure_app_secret)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to modify OneDrive connection settings</p>
<p>Arguments:
azure_app_id
(str)
&ndash;
new azure application id
azure_dir_id
(str)
&ndash;
new azure directory id
azure_app_secret
(str)
&ndash;
new azure app password</p>
<h2 id="returns">Returns</h2>
<p>None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to add</p>
<pre><code>  if response is empty

  if response code is not as expected
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/onedrive_instance.py#L828-L874" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def modify_connection_settings(self, azure_app_id, azure_dir_id, azure_app_secret):
    &#34;&#34;&#34;
               Method to modify OneDrive connection settings

               Arguments:
                   azure_app_id         (str)   --      new azure application id
                   azure_dir_id         (str)   --      new azure directory id
                   azure_app_secret        (str)   --     new azure app password

              Returns:
                   None

              Raises:
                  SDKException:
                        if failed to add

                        if response is empty

                        if response code is not as expected
    &#34;&#34;&#34;

    if not isinstance(azure_app_id, str) or not azure_app_id:
        raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;Invalid argument: azure_app_id must be a non-empty string&#39;)
    if not isinstance(azure_dir_id, str) or not azure_dir_id:
        raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;Invalid argument: azure_dir_id must be a non-empty string&#39;)
    if not isinstance(azure_app_secret, str) or not azure_app_secret:
        raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;Invalid argument: azure_app_secret must be a non-empty string&#39;)

    update_dict = {
        &#34;instance&#34;: self._instance,
        &#34;cloudAppsInstance&#34;: {
            &#34;instanceType&#34;: self.ca_instance_type,
            &#34;oneDriveInstance&#34;: {
                &#34;azureAppList&#34;: {
                    &#34;azureApps&#34;: [
                        {
                            &#34;azureDirectoryId&#34;: azure_dir_id,
                            &#34;azureAppId&#34;: azure_app_id,
                            &#34;azureAppKeyValue&#34;: b64encode(azure_app_secret.encode()).decode()
                        }
                    ]
                }
            }
        }
    }

    self.update_properties(properties_dict=update_dict)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance.modify_index_server"><code class="name flex">
<span>def <span class="ident">modify_index_server</span></span>(<span>self, modified_index_server)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to modify the index server</p>
<h2 id="arguments">Arguments</h2>
<p>modified_index_server
(str)&ndash;
new index server name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/onedrive_instance.py#L754-L779" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def modify_index_server(self, modified_index_server):
    &#34;&#34;&#34;
        Method to modify the index server

        Arguments:
            modified_index_server        (str)--     new index server name
    &#34;&#34;&#34;
    update_dict = {
        &#34;instance&#34;: {
            &#34;instanceId&#34;: int(self.instance_id),
            &#34;clientId&#34;: int(self._agent_object._client_object.client_id),
            &#34;applicationId&#34;: int(self._agent_object.agent_id)
        },
            &#34;cloudAppsInstance&#34;: {
                &#34;instanceType&#34;: self.ca_instance_type,
                &#34;oneDriveInstance&#34;: {
                },
                &#34;generalCloudProperties&#34;: {
                    &#34;indexServer&#34;: {
                        &#34;clientName&#34;: modified_index_server
                    }
                }
            }
        }

    self.update_properties(properties_dict=update_dict)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance.restore_out_of_place"><code class="name flex">
<span>def <span class="ident">restore_out_of_place</span></span>(<span>self, client, destination_path, paths, overwrite=True, restore_data_and_acl=True, copy_precedence=None, from_time=None, to_time=None, to_disk=False)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the files/folders specified in the input paths list to the input client,
at the specified destionation location.</p>
<h2 id="args">Args</h2>
<p>client
(str/object) &ndash;
either the name of the client or
the instance of the Client</p>
<p>destination_path
(str)
&ndash;
full path of the restore location on client</p>
<p>paths
(list)
&ndash;
list of full paths of
files/folders to restore</p>
<p>overwrite
(bool)
&ndash;
unconditional overwrite files during restore
default: True</p>
<p>restore_data_and_acl
(bool)
&ndash;
restore data and ACL files
default: True</p>
<p>copy_precedence
(int)
&ndash;
copy precedence value of storage policy copy
default: None</p>
<p>from_time
(str)
&ndash;
time to retore the contents after
format: YYYY-MM-DD HH:MM:SS</p>
<pre><code>default: None
</code></pre>
<p>to_time
(str)
&ndash;
time to retore the contents before
format: YYYY-MM-DD HH:MM:SS</p>
<pre><code>default: None
</code></pre>
<p>to_disk
(bool)
&ndash;
If True, restore to disk will be performed</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if client is not a string or Client instance</p>
<pre><code>if destination_path is not a string

if paths is not a list

if failed to initialize job

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/onedrive_instance.py#L600-L713" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_out_of_place(
        self,
        client,
        destination_path,
        paths,
        overwrite=True,
        restore_data_and_acl=True,
        copy_precedence=None,
        from_time=None,
        to_time=None,
        to_disk=False):
    &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the input client,
        at the specified destionation location.

        Args:
            client                (str/object) --  either the name of the client or
                                                       the instance of the Client

            destination_path      (str)        --  full path of the restore location on client

            paths                 (list)       --  list of full paths of
                                                       files/folders to restore

            overwrite             (bool)       --  unconditional overwrite files during restore
                default: True

            restore_data_and_acl  (bool)       --  restore data and ACL files
                default: True

            copy_precedence         (int)   --  copy precedence value of storage policy copy
                default: None

            from_time           (str)       --  time to retore the contents after
                    format: YYYY-MM-DD HH:MM:SS

                default: None

            to_time           (str)         --  time to retore the contents before
                    format: YYYY-MM-DD HH:MM:SS

                default: None

            to_disk             (bool)       --  If True, restore to disk will be performed

        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:
                if client is not a string or Client instance

                if destination_path is not a string

                if paths is not a list

                if failed to initialize job

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    from cvpysdk.client import Client

    if not ((isinstance(client, str) or isinstance(client, Client)) and
            isinstance(destination_path, str) and
            isinstance(paths, list) and
            isinstance(overwrite, bool) and
            isinstance(restore_data_and_acl, bool)):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    if isinstance(client, Client):
        client = client
    elif isinstance(client, str):
        client = Client(self._commcell_object, client)
    else:
        raise SDKException(&#39;Subclient&#39;, &#39;105&#39;)

    paths = self._filter_paths(paths)

    destination_path = self._filter_paths([destination_path], True)

    if paths == []:
        raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)

    request_json = self._restore_json(
        paths=paths,
        in_place=False,
        client=client,
        destination_path=destination_path,
        overwrite=overwrite,
        restore_data_and_acl=restore_data_and_acl,
        copy_precedence=copy_precedence,
        from_time=from_time,
        to_time=to_time,
    )
    dest_user_account = destination_path
    rest_different_account = True
    restore_to_google = True

    if to_disk:
        dest_user_account = &#39;&#39;
        rest_different_account = False
        restore_to_google = False
    request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
        &#34;restoreOptions&#34;][&#39;cloudAppsRestoreOptions&#39;] = {
        &#34;instanceType&#34;: self._ca_instance_type,
        &#34;googleRestoreOptions&#34;: {
            &#34;strDestUserAccount&#34;: dest_user_account,
            &#34;folderGuid&#34;: &#34;&#34;,
            &#34;restoreToDifferentAccount&#34;: rest_different_account,
            &#34;restoreToGoogle&#34;: restore_to_google
        }
    }
    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.instances.cainstance.CloudAppsInstance" href="../cainstance.html#cvpysdk.instances.cainstance.CloudAppsInstance">CloudAppsInstance</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.backupsets" href="../../instance.html#cvpysdk.instance.Instance.backupsets">backupsets</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.browse" href="../../instance.html#cvpysdk.instance.Instance.browse">browse</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.credentials" href="../../instance.html#cvpysdk.instance.Instance.credentials">credentials</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.find" href="../../instance.html#cvpysdk.instance.Instance.find">find</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.instance_id" href="../../instance.html#cvpysdk.instance.Instance.instance_id">instance_id</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.instance_name" href="../../instance.html#cvpysdk.instance.Instance.instance_name">instance_name</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.name" href="../../instance.html#cvpysdk.instance.Instance.name">name</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.properties" href="../../instance.html#cvpysdk.instance.Instance.properties">properties</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.refresh" href="../../instance.html#cvpysdk.instance.Instance.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.subclients" href="../../instance.html#cvpysdk.instance.Instance.subclients">subclients</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.update_properties" href="../../instance.html#cvpysdk.instance.Instance.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.instances.cloudapps" href="index.html">cvpysdk.instances.cloudapps</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance" href="#cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance">OneDriveInstance</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance.auto_discovery_mode" href="#cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance.auto_discovery_mode">auto_discovery_mode</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance.auto_discovery_status" href="#cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance.auto_discovery_status">auto_discovery_status</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance.ca_instance_type" href="#cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance.ca_instance_type">ca_instance_type</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance.delete_data_from_browse" href="#cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance.delete_data_from_browse">delete_data_from_browse</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance.enable_auto_discovery" href="#cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance.enable_auto_discovery">enable_auto_discovery</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance.manage_content_automatically" href="#cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance.manage_content_automatically">manage_content_automatically</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance.modify_accessnodes" href="#cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance.modify_accessnodes">modify_accessnodes</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance.modify_connection_settings" href="#cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance.modify_connection_settings">modify_connection_settings</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance.modify_index_server" href="#cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance.modify_index_server">modify_index_server</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance.onedrive_client_id" href="#cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance.onedrive_client_id">onedrive_client_id</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance.onedrive_tenant" href="#cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance.onedrive_tenant">onedrive_tenant</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance.proxy_client" href="#cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance.proxy_client">proxy_client</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance.restore_out_of_place" href="#cvpysdk.instances.cloudapps.onedrive_instance.OneDriveInstance.restore_out_of_place">restore_out_of_place</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>