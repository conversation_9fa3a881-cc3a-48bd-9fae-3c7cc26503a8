<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.instances.db2instance API documentation</title>
<meta name="description" content="File for operating on a DB2 Instance …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.instances.db2instance</code></h1>
</header>
<section id="section-intro">
<p>File for operating on a DB2 Instance.</p>
<p>DB2Instance is the only class defined in this file.</p>
<p>DB2Instance:
Derived class from Instance Base class, representing a
DB2 instance, and to perform operations on that instance</p>
<h1 id="db2instance">DB2Instance:</h1>
<pre><code>_restore_destination_json()     --      setter for the Db2 Destination options in restore JSON

_db2_restore_options_json()     --      setter for  the db2 options of in restore JSON

_restore_json()                 --      returns the JSON request to pass to the API as per
the options selected by the user

restore_entire_database()       --      Restores the db2 database

restore_out_of_place()          --      runs the out of place restore for given backupset

restore_table_level()           --      Table level restore function
</code></pre>
<h1 id="db2instance-instance-attributes">DB2Instance instance Attributes:</h1>
<pre><code>**version**                         -- returns db2 version

**home_directory**                  -- returns db2 home directory

**user_name**                       -- returns db2 user name

**data_backup_storage_policy**      -- returns data backup storage policy

**command_line_storage_policy**     -- returns commandline storage policy

**log_backup_storage_policy**       -- returns log backup storage policy
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/db2instance.py#L1-L630" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;File for operating on a DB2 Instance.

DB2Instance is the only class defined in this file.

DB2Instance:    Derived class from Instance Base class, representing a
DB2 instance, and to perform operations on that instance

DB2Instance:
============

    _restore_destination_json()     --      setter for the Db2 Destination options in restore JSON

    _db2_restore_options_json()     --      setter for  the db2 options of in restore JSON

    _restore_json()                 --      returns the JSON request to pass to the API as per
    the options selected by the user

    restore_entire_database()       --      Restores the db2 database

    restore_out_of_place()          --      runs the out of place restore for given backupset

    restore_table_level()           --      Table level restore function


DB2Instance instance Attributes:
================================
    **version**                         -- returns db2 version

    **home_directory**                  -- returns db2 home directory

    **user_name**                       -- returns db2 user name

    **data_backup_storage_policy**      -- returns data backup storage policy

    **command_line_storage_policy**     -- returns commandline storage policy

    **log_backup_storage_policy**       -- returns log backup storage policy

&#34;&#34;&#34;
from __future__ import unicode_literals
from ..instance import Instance
from ..exception import SDKException
from base64 import b64encode


class DB2Instance(Instance):
    &#34;&#34;&#34; Derived class from Instance Base class, representing a DB2 instance,
        and to perform operations on that Instance.&#34;&#34;&#34;

    @property
    def version(self):
        &#34;&#34;&#34;returns db2 version

        Returns:
            (str) -- db2 version value in string

        &#34;&#34;&#34;
        return self._properties.get(&#39;version&#39;, &#34;&#34;)

    @property
    def home_directory(self):
        &#34;&#34;&#34;
        returns db2 home directory

        Returns:
            (str) - string of db2_home

        &#34;&#34;&#34;
        return self._properties.get(&#39;db2Instance&#39;, {}).get(&#39;homeDirectory&#39;, &#34;&#34;)

    @property
    def user_name(self):
        &#34;&#34;&#34;
                returns db2 user name

                Returns:
                    (str)  - String containing db2 user

        &#34;&#34;&#34;
        return self._properties.get(
            &#39;db2Instance&#39;, {}).get(&#39;userAccount&#39;, {}).get(&#39;userName&#39;, &#34;&#34;)

    @property
    def data_backup_storage_policy(self):
        &#34;&#34;&#34; returns data backup storage policy

            Returns:
                (str) -- Storage policy name from db2 instance level

        &#34;&#34;&#34;
        return self._properties.get(&#39;db2Instance&#39;, {}).get(
            &#39;DB2StorageDevice&#39;, {}).get(&#39;dataBackupStoragePolicy&#39;, {}).get(&#39;storagePolicyName&#39;, &#34;&#34;)

    @property
    def command_line_storage_policy(self):
        &#34;&#34;&#34;returns commandline storage policy

            Returns:
                (str)  --  Command line sp name from db2 instance level

        &#34;&#34;&#34;
        return self._properties.get(&#39;db2Instance&#39;, {}).get(
            &#39;DB2StorageDevice&#39;, {}).get(&#39;commandLineStoragePolicy&#39;, {}).get(&#39;storagePolicyName&#39;, &#34;&#34;)

    @property
    def log_backup_storage_policy(self):
        &#34;&#34;&#34;
        returns log backup storage policy

            Returns:
                (str)  -- Log backup SP name from instance level

        &#34;&#34;&#34;
        return self._properties.get(&#39;db2Instance&#39;, {}).get(
            &#39;DB2StorageDevice&#39;, {}).get(&#39;logBackupStoragePolicy&#39;, {}).get(&#39;storagePolicyName&#39;, &#34;&#34;)

    def _restore_destination_json(self, value):
        &#34;&#34;&#34;setter for the Db2 Destination options in restore JSON&#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        self._destination_restore_json = {
            &#34;destinationInstance&#34;: {
                &#34;clientName&#34;: value.get(&#34;dest_client_name&#34;, &#34;&#34;),
                &#34;instanceName&#34;: value.get(&#34;dest_instance_name&#34;, &#34;&#34;),
                &#34;backupsetName&#34;: value.get(&#34;dest_backupset_name&#34;, &#34;&#34;),
                &#34;appName&#34;: &#34;DB2&#34;
            },
            &#34;destClient&#34;: {
                &#34;clientName&#34;: value.get(&#34;dest_client_name&#34;, &#34;&#34;)
            }
        }

    def _db2_restore_options_json(self, value):
        &#34;&#34;&#34;setter for  the db2 options of in restore JSON
            Args:
                value (dict) -- Dictionary of options need to be set for restore

        &#34;&#34;&#34;
        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        self.db2_options_restore_json = {
            &#34;restoreType&#34;: value.get(&#34;restore_type&#34;, 0),
            &#34;restoreLevel&#34;: value.get(&#34;restore_level&#34;, 0),
            &#34;redirect&#34;: value.get(&#34;redirect&#34;, False),
            &#34;rollForwardPending&#34;: value.get(&#34;rollforward_pending&#34;, False),
            &#34;restoreArchiveLogs&#34;: value.get(&#34;restore_archive_logs&#34;, True),
            &#34;rollForward&#34;: value.get(&#34;roll_forward&#34;, True),
            &#34;restoreIncremental&#34;: value.get(&#34;restore_incremental&#34;, False),
            &#34;archiveLogLSN&#34;: value.get(&#34;archivelog_lsn&#34;, False),
            &#34;archiveLogTime&#34;: value.get(&#34;archive_log_time&#34;, False),
            &#34;startLSN&#34;: value.get(&#34;start_lsn&#34;, False),
            &#34;endLSN&#34;: value.get(&#34;end_lsn&#34;, False),
            &#34;logTimeStart&#34;: value.get(&#34;logtime_start&#34;, False),
            &#34;logTimeEnd&#34;: value.get(&#34;logtime_end&#34;, False),
            &#34;rollForwardToEnd&#34;: value.get(&#34;roll_forward_to_end&#34;, 1),
            &#34;useAlternateLogFile&#34;: value.get(&#34;use_alternate_logfile&#34;, False),
            &#34;restoreData&#34;: value.get(&#34;restore_data&#34;, True),
            &#34;restoreOnline&#34;: value.get(&#34;restore_online&#34;, False),
            &#34;targetDb&#34;: value.get(&#34;target_db&#34;, &#34; &#34;),
            &#34;targetPath&#34;: value.get(&#34;target_path&#34;, &#34; &#34;),
            &#34;reportFile&#34;: value.get(&#34;report_file&#34;, &#34; &#34;),
            &#34;buffers&#34;: value.get(&#34;buffers&#34;, 2),
            &#34;bufferSize&#34;: value.get(&#34;buffer_size&#34;, 1024),
            &#34;rollForwardDir&#34;: value.get(&#34;roll_forward_dir&#34;, &#34; &#34;),
            &#34;recoverDb&#34;: value.get(&#34;recover_db&#34;, False),
            &#34;dbHistoryFilepath&#34;: value.get(&#34;db_history_filepath&#34;, False),
            &#34;storagePath&#34;: value.get(&#34;storage_path&#34;, False),
            &#34;parallelism&#34;: value.get(&#34;parallelism&#34;, 0),
            &#34;useSnapRestore&#34;: value.get(&#34;use_snap_restore&#34;, False),
            &#34;useLatestImage&#34;: value.get(&#34;use_latest_image&#34;, True),
            &#34;tableViewRestore&#34;: value.get(&#34;table_view_restore&#34;, False),
            &#34;useLogTarget&#34;: value.get(&#34;use_log_target&#34;, False),
            &#34;cloneRecovery&#34;: value.get(&#34;clone_recovery&#34;, False)
        }

        if value.get(&#34;archive_log_time&#34;, False):
            self.db2_options_restore_json[&#34;logTimeRange&#34;] = dict()
            self.db2_options_restore_json[&#34;logTimeRange&#34;][&#34;fromTimeValue&#34;] = value.get(&#34;from_time_value&#34;, 0)
            self.db2_options_restore_json[&#34;logTimeRange&#34;][&#34;toTimeValue&#34;] = value.get(&#34;to_time_value&#34;, 0)

        if value.get(&#34;archivelog_lsn&#34;, False):
            self.db2_options_restore_json[&#34;startLSNNum&#34;] = value.get(&#34;start_lsn_num&#34;, 1)
            self.db2_options_restore_json[&#34;endLSNNum&#34;] = value.get(&#34;end_lsn_num&#34;, 1)

        return self.db2_options_restore_json

    def _restore_json(self, **kwargs):
        &#34;&#34;&#34;Returns the JSON request to pass to the API as per the options selected by the user.

            Args:
                kwargs   (dict)  --  list of options need to be set for restore

            Returns:
                dict - JSON request to pass to the API

        &#34;&#34;&#34;
        rest_json = super(DB2Instance, self)._restore_json(**kwargs)
        restore_option = {}
        if kwargs.get(&#34;restore_option&#34;):
            restore_option = kwargs[&#34;restore_option&#34;]
            for key in kwargs:
                if not key == &#34;restore_option&#34;:
                    restore_option[key] = kwargs[key]
        else:
            restore_option = kwargs

        json = self._db2_restore_options_json(restore_option)
        rest_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;db2Option&#34;] = json

        return rest_json

    def restore_entire_database(
            self,
            dest_client_name,
            dest_instance_name,
            dest_database_name,
            **kwargs
    ):
        &#34;&#34;&#34;Restores the db2 database

            Args:

                dest_client_name        (str)   --  destination client name

                dest_instance_name      (str)   --  destination db2 instance name of
                destination on destination client

                dest_database_name      (str)   -- destination database name

                restore_type            (str)   -- db2 restore type

                    default: &#34;ENTIREDB&#34;

                recover_db              (bool)  -- recover database flag

                    default: True

                restore_incremental     (bool)  -- Restore incremental flag

                    default: True

                restore_data            (bool)  -- Restore data or not
                    default: True

                copy_precedence         (int)   -- Copy precedence to perform restore from
                    default : None

                roll_forward            (bool)  -- Rollforward database or not
                    default: True

                restore_logs (bool)  -   Restore the logs or not
                default: True

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;

        recover_db = kwargs.get(&#34;recover_db&#34;, True)
        restore_incremental = kwargs.get(&#34;restore_incremental&#34;, True)
        restore_data = kwargs.get(&#34;restore_data&#34;, True)
        copy_precedence = kwargs.get(&#34;copy_precedence&#34;, None)
        roll_forward = kwargs.get(&#34;roll_forward&#34;, True)
        restore_logs = kwargs.get(&#34;restore_logs&#34;, True)
        restore_type = kwargs.get(&#34;restore_type&#34;, &#39;ENTIREDB&#39;)
        start_lsn_num = kwargs.get(&#34;startLSNNum&#34;, 1)
        end_lsn_num = kwargs.get(&#34;endLSNNum&#34;, 1)
        end_lsn = kwargs.get(&#34;endLSN&#34;, False)
        start_lsn = kwargs.get(&#34;startLSN&#34;, False)
        archivelog_lsn = kwargs.get(&#34;archiveLogLSN&#34;, False)
        archive_log_time = kwargs.get(&#34;archiveLogTime&#34;, False)
        logtime_start = kwargs.get(&#34;logTimeStart&#34;, False)
        logtime_end = kwargs.get(&#34;logTimeEnd&#34;, False)
        from_time_value = kwargs.get(&#34;fromTimeValue&#34;, 0)
        to_time_value = kwargs.get(&#34;toTimeValue&#34;, 0)


        if &#34;entiredb&#34; in restore_type.lower():
            restore_type = 0

        request_json = self._restore_json(
            dest_client_name=dest_client_name,
            dest_instance_name=dest_instance_name,
            dest_backupset_name=dest_database_name,
            target_db=dest_database_name,
            restore_type=restore_type,
            recover_db=recover_db,
            restore_incremental=restore_incremental,
            restore_data=restore_data,
            copy_precedence=copy_precedence,
            roll_forward=roll_forward,
            rollforward_pending=not roll_forward,
            restore_archive_logs=restore_logs,
            start_lsn_num=start_lsn_num,
            end_lsn_num=end_lsn_num,
            archivelog_lsn=archivelog_lsn,
            start_lsn=start_lsn,
            end_lsn=end_lsn,
            archive_log_time=archive_log_time,
            logtime_start=logtime_start,
            logtime_end=logtime_end,
            from_time_value=from_time_value,
            to_time_value=to_time_value
        )

        return self._process_restore_response(request_json)

    def restore_out_of_place(
            self,
            dest_client_name,
            dest_instance_name,
            dest_backupset_name,
            target_path,
            **kwargs):
        &#34;&#34;&#34;Restores the DB2 data/log files specified in the input paths
        list to the same location.

            Args:
                dest_client_name        (str)   --  destination client name where files are to be
                restored

                dest_instance_name      (str)   --  destination db2 instance name of
                destination client

                dest_backupset_name     (str)   --  destination db2 backupset name of
                destination client

                target_path             (str)   --  Destination DB restore path

                copy_precedence         (int)   --  copy precedence value of storage policy copy
                    default: None

                from_time               (str)   --  time to retore the contents after
                    format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time                 (str)   --  time to retore the contents before
                    format: YYYY-MM-DD HH:MM:SS

                    default: None

                redirect_enabled         (bool)  --  boolean to specify if redirect restore is
                enabled

                    default: False

                redirect_storage_group_path           (dict)   --  Path specified for each storage group
                in advanced restore options in order to perform redirect restore
                    format: {&#39;Storage Group Name&#39;: &#39;Redirect Path&#39;}

                    default: None

                 redirect_tablespace_path           (dict)   --  Path specified for each tablespace in advanced
                 restore options in order to perform redirect restore
                    format: {&#39;Tablespace name&#39;: &#39;Redirect Path&#39;}

                    default: None

                destination_path        (str)   --  destinath path for restore
                    default: None

                restore_data            (bool)  -- Restore data or not

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;

        copy_precedence = kwargs.get(&#39;copy_precedence&#39;, None)
        from_time = kwargs.get(&#39;from_time&#39;, None)
        to_time = kwargs.get(&#39;to_time&#39;, None)
        redirect_enabled = kwargs.get(&#39;redirect_enabled&#39;, False)
        redirect_tablespace_path = kwargs.get(&#39;redirect_tablespace_path&#39;, None)
        redirect_storage_group_path = kwargs.get(&#39;redirect_storage_group_path&#39;, None)
        rollforward = kwargs.get(&#39;rollforward&#39;, True)
        restoreArchiveLogs = kwargs.get(&#39;restoreArchiveLogs&#39;, False)
        restore_incremental = kwargs.get(&#39;restore_incremental&#39;, True)
        restore_data = kwargs.get(&#39;restore_data&#39;, True)

        if redirect_enabled:
            if not (isinstance(redirect_tablespace_path, dict) or isinstance(redirect_tablespace_path, str)) and \
                    not isinstance(redirect_storage_group_path, dict):
                raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        request_json = self._restore_json(
            dest_client_name=dest_client_name,
            dest_instance_name=dest_instance_name,
            dest_backupset_name=dest_backupset_name,
            target_db=dest_backupset_name,
            target_path=target_path,
            copy_precedence=copy_precedence,
            from_time=from_time,
            to_time=to_time,
            redirect=redirect_enabled,
            redirect_storage_group_path=redirect_storage_group_path,
            redirect_tablespace_path=redirect_tablespace_path,
            rollforward_pending=not rollforward,
            restore_archive_logs=restoreArchiveLogs,
            roll_forward=rollforward,
            restore_incremental=restore_incremental,
            storage_path=True,
            restore_data=restore_data)

        if redirect_storage_group_path:
            storagePaths = []
            storageGroup = {&#34;storageGroup&#34;: []}

            for name, path in redirect_storage_group_path.items():
                if isinstance(path, str):
                    storageGroup[&#34;storageGroup&#34;].append({&#34;groupName&#34;: name, &#34;stoPaths&#34;: [path]})
                    storagePaths = [path]
                else:
                    storageGroup[&#34;storageGroup&#34;].append({&#34;groupName&#34;: name, &#34;stoPaths&#34;: path})
                    storagePaths = [path[0]]

            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;db2Option&#34;][
                &#34;redirectStorageGroups&#34;] = True
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;db2Option&#34;][
                &#34;storagePaths&#34;] = storagePaths
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;db2Option&#34;][
                &#34;storageGroupInfo&#34;] = storageGroup

        if redirect_tablespace_path:
            if isinstance(redirect_tablespace_path, str):
                request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;db2Option&#34;][
                    &#34;redirectAllPaths&#34;] = redirect_tablespace_path
                request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;db2Option&#34;][
                    &#34;redirectAllTableSpacesSelected&#34;] = True
                request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;db2Option&#34;][
                    &#34;redirectAllTableSpacesValue&#34;] = redirect_tablespace_path
            else:
                redirect_info = []
                for tablespace, path in redirect_tablespace_path.items():
                    table_string = &#34;%s\t1\t%s\t6\t25600\t1\t1&#34; % (tablespace, path)
                    redirect_info.append(table_string)
                request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;db2Option&#34;][
                    &#34;redirectInfo&#34;] = redirect_info

        return self._process_restore_response(request_json)

    def restore_table_level(
            self,
            aux_client_name,
            aux_instance_name,
            aux_backupset_name,
            dest_client_name,
            dest_instance_name,
            dest_backupset_name,
            target_path,
            staging_path,
            tables_path,
            user_name,
            password,
            **kwargs
        ):
        &#34;&#34;&#34;
        Performs DB2 table level restore
            Args:
                aux_client_name         (str)   --  auxiliary client name where files are to be restored
                aux_instance_name       (str)   --  auxiliary instance name where files are to be restored
                aux_backupset_name      (str)   --  auxiliary backupset name where files are to be restored
                dest_client_name        (str)   --  destination client name where files are to be restored
                dest_instance_name      (str)   --  destination db2 instance name of destination client
                dest_backupset_name     (str)   --  destination db2 backupset name of destination client

                target_path             (str)   --  Destination DB restore path

                src_backupset_name       (str)   --  Source Backupset Name

                staging_path             (str)   -- Staging Path

                user_name                (str)   -- Destination User name

                password                 (str)  --  Destination User Password

                tables_path             (list)   -- List of tables path
                    Example:
                        Unix:  [&#39;/+tblview+/instance_name/database_name/schema_name/table_name/**&#39;]
                        Windows: [&#34;\\+tblview+\\instance_name\\database_name\\schema_name\\table_name\\**&#34;]

                copy_precedence         (int)   --  copy precedence value of storage policy copy
                    default: None

                from_time               (str)   --  time to retore the contents after
                    format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time                 (str)   --  time to retore the contents before
                    format: YYYY-MM-DD HH:MM:SS

                    default: None

                rollForward             (bool)   --   Rollforward or not
                    default: True

                destination_path        (str)   --  destinath path for restore
                    default: None

                server_port              (int)   -- Server Port Destination instance
                    default: 50000

                generateAuthorizationDDL    (bool)  -- Generate Authorization DDL
                    default: False

                extractDDLStatements        (bool)  --  Extracts DDL statement or not
                    default: True

                clearAuxiliary              (bool)  -- Cleanup auxilliary or not
                    default: True

                dropTable                   (bool)  -- Drop table for import
                    default: False


            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        copy_precedence = kwargs.get(&#39;copy_precedence&#39;, None)
        from_time = kwargs.get(&#39;from_time&#39;, None)
        to_time = kwargs.get(&#39;to_time&#39;, None)
        rollforward = kwargs.get(&#39;rollforward&#39;, True)
        restoreArchiveLogs = kwargs.get(&#39;restoreArchiveLogs&#39;, False)

        request_json = self._restore_json(
            dest_client_name=aux_client_name,
            dest_instance_name=aux_instance_name,
            dest_backupset_name=aux_backupset_name,
            target_db=aux_backupset_name,
            target_path=target_path,
            copy_precedence=copy_precedence,
            from_time=from_time,
            to_time=to_time,
            rollforward_pending=not rollforward,
            restoreArchiveLogs=restoreArchiveLogs,
            roll_forward=rollforward,
            storage_path=True,
            table_view_restore=True)

        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;db2Option&#34;][
            &#34;storagePaths&#34;] = [target_path]

        password = b64encode(password.encode()).decode()

        table_json = {
            &#34;additionalExportParameter&#34;: kwargs.get(&#34;additionalExportParameter&#34;, &#34;&#34;),
            &#34;serverPort&#34;: kwargs.get(&#34;server_port&#34;, 50000),
            &#34;generateAuthorizationDDL&#34;: kwargs.get(&#34;generateAuthorizationDDL&#34;, False),
            &#34;importInstance&#34;: dest_instance_name,
            &#34;extractDDLStatements&#34;: kwargs.get(&#34;extractDDLStatements&#34;, True),
            &#34;useAdditionalExportParameters&#34;: kwargs.get(&#34;useAdditionalExportParameters&#34;, False),
            &#34;auxiliaryInstance&#34;: False,
            &#34;clearAuxiliary&#34;: kwargs.get(&#34;clearAuxiliary&#34;, True),
            &#34;importDatabase&#34;: dest_backupset_name,
            &#34;importToWhere&#34;: 2,
            &#34;dropTable&#34;: kwargs.get(&#34;dropTable&#34;, False),
            &#34;stagingPath&#34;: staging_path,
            &#34;importDbClient&#34;: {&#34;clientName&#34;: dest_client_name},
            &#34;importUserInfo&#34;: {&#34;userName&#34;: user_name, &#34;password&#34;: password}
        }

        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;db2Option&#34;][
            &#34;storagePaths&#34;] = [target_path]

        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;db2Option&#34;][
            &#34;databaseTableRstOptions&#34;] = table_json

        request_json[&#39;taskInfo&#39;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;browseOption&#34;][&#34;backupset&#34;][&#34;backupsetId&#34;] = int(self.backupsets.get(aux_backupset_name).backupset_id)

        request_json[&#39;taskInfo&#39;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;browseOption&#34;][&#34;backupset&#34;][&#34;backupsetName&#34;] = aux_backupset_name

        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;db2Option&#34;][
            &#34;restoreArchiveLogs&#34;] = False

        request_json[&#39;taskInfo&#39;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;fileOption&#34;][&#34;filterItem&#34;] = tables_path
        request_json[&#39;taskInfo&#39;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;fileOption&#34;][&#34;sourceItem&#34;] = tables_path
        return self._process_restore_response(request_json)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.instances.db2instance.DB2Instance"><code class="flex name class">
<span>class <span class="ident">DB2Instance</span></span>
<span>(</span><span>agent_object, instance_name, instance_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Derived class from Instance Base class, representing a DB2 instance,
and to perform operations on that Instance.</p>
<p>Initialise the instance object.</p>
<h2 id="args">Args</h2>
<p>agent_object
(object)
&ndash;
instance of the Agent class</p>
<p>instance_name
(str)
&ndash;
name of the instance</p>
<p>instance_id
(str)
&ndash;
id of the instance
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Instance class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/db2instance.py#L64-L629" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class DB2Instance(Instance):
    &#34;&#34;&#34; Derived class from Instance Base class, representing a DB2 instance,
        and to perform operations on that Instance.&#34;&#34;&#34;

    @property
    def version(self):
        &#34;&#34;&#34;returns db2 version

        Returns:
            (str) -- db2 version value in string

        &#34;&#34;&#34;
        return self._properties.get(&#39;version&#39;, &#34;&#34;)

    @property
    def home_directory(self):
        &#34;&#34;&#34;
        returns db2 home directory

        Returns:
            (str) - string of db2_home

        &#34;&#34;&#34;
        return self._properties.get(&#39;db2Instance&#39;, {}).get(&#39;homeDirectory&#39;, &#34;&#34;)

    @property
    def user_name(self):
        &#34;&#34;&#34;
                returns db2 user name

                Returns:
                    (str)  - String containing db2 user

        &#34;&#34;&#34;
        return self._properties.get(
            &#39;db2Instance&#39;, {}).get(&#39;userAccount&#39;, {}).get(&#39;userName&#39;, &#34;&#34;)

    @property
    def data_backup_storage_policy(self):
        &#34;&#34;&#34; returns data backup storage policy

            Returns:
                (str) -- Storage policy name from db2 instance level

        &#34;&#34;&#34;
        return self._properties.get(&#39;db2Instance&#39;, {}).get(
            &#39;DB2StorageDevice&#39;, {}).get(&#39;dataBackupStoragePolicy&#39;, {}).get(&#39;storagePolicyName&#39;, &#34;&#34;)

    @property
    def command_line_storage_policy(self):
        &#34;&#34;&#34;returns commandline storage policy

            Returns:
                (str)  --  Command line sp name from db2 instance level

        &#34;&#34;&#34;
        return self._properties.get(&#39;db2Instance&#39;, {}).get(
            &#39;DB2StorageDevice&#39;, {}).get(&#39;commandLineStoragePolicy&#39;, {}).get(&#39;storagePolicyName&#39;, &#34;&#34;)

    @property
    def log_backup_storage_policy(self):
        &#34;&#34;&#34;
        returns log backup storage policy

            Returns:
                (str)  -- Log backup SP name from instance level

        &#34;&#34;&#34;
        return self._properties.get(&#39;db2Instance&#39;, {}).get(
            &#39;DB2StorageDevice&#39;, {}).get(&#39;logBackupStoragePolicy&#39;, {}).get(&#39;storagePolicyName&#39;, &#34;&#34;)

    def _restore_destination_json(self, value):
        &#34;&#34;&#34;setter for the Db2 Destination options in restore JSON&#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        self._destination_restore_json = {
            &#34;destinationInstance&#34;: {
                &#34;clientName&#34;: value.get(&#34;dest_client_name&#34;, &#34;&#34;),
                &#34;instanceName&#34;: value.get(&#34;dest_instance_name&#34;, &#34;&#34;),
                &#34;backupsetName&#34;: value.get(&#34;dest_backupset_name&#34;, &#34;&#34;),
                &#34;appName&#34;: &#34;DB2&#34;
            },
            &#34;destClient&#34;: {
                &#34;clientName&#34;: value.get(&#34;dest_client_name&#34;, &#34;&#34;)
            }
        }

    def _db2_restore_options_json(self, value):
        &#34;&#34;&#34;setter for  the db2 options of in restore JSON
            Args:
                value (dict) -- Dictionary of options need to be set for restore

        &#34;&#34;&#34;
        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        self.db2_options_restore_json = {
            &#34;restoreType&#34;: value.get(&#34;restore_type&#34;, 0),
            &#34;restoreLevel&#34;: value.get(&#34;restore_level&#34;, 0),
            &#34;redirect&#34;: value.get(&#34;redirect&#34;, False),
            &#34;rollForwardPending&#34;: value.get(&#34;rollforward_pending&#34;, False),
            &#34;restoreArchiveLogs&#34;: value.get(&#34;restore_archive_logs&#34;, True),
            &#34;rollForward&#34;: value.get(&#34;roll_forward&#34;, True),
            &#34;restoreIncremental&#34;: value.get(&#34;restore_incremental&#34;, False),
            &#34;archiveLogLSN&#34;: value.get(&#34;archivelog_lsn&#34;, False),
            &#34;archiveLogTime&#34;: value.get(&#34;archive_log_time&#34;, False),
            &#34;startLSN&#34;: value.get(&#34;start_lsn&#34;, False),
            &#34;endLSN&#34;: value.get(&#34;end_lsn&#34;, False),
            &#34;logTimeStart&#34;: value.get(&#34;logtime_start&#34;, False),
            &#34;logTimeEnd&#34;: value.get(&#34;logtime_end&#34;, False),
            &#34;rollForwardToEnd&#34;: value.get(&#34;roll_forward_to_end&#34;, 1),
            &#34;useAlternateLogFile&#34;: value.get(&#34;use_alternate_logfile&#34;, False),
            &#34;restoreData&#34;: value.get(&#34;restore_data&#34;, True),
            &#34;restoreOnline&#34;: value.get(&#34;restore_online&#34;, False),
            &#34;targetDb&#34;: value.get(&#34;target_db&#34;, &#34; &#34;),
            &#34;targetPath&#34;: value.get(&#34;target_path&#34;, &#34; &#34;),
            &#34;reportFile&#34;: value.get(&#34;report_file&#34;, &#34; &#34;),
            &#34;buffers&#34;: value.get(&#34;buffers&#34;, 2),
            &#34;bufferSize&#34;: value.get(&#34;buffer_size&#34;, 1024),
            &#34;rollForwardDir&#34;: value.get(&#34;roll_forward_dir&#34;, &#34; &#34;),
            &#34;recoverDb&#34;: value.get(&#34;recover_db&#34;, False),
            &#34;dbHistoryFilepath&#34;: value.get(&#34;db_history_filepath&#34;, False),
            &#34;storagePath&#34;: value.get(&#34;storage_path&#34;, False),
            &#34;parallelism&#34;: value.get(&#34;parallelism&#34;, 0),
            &#34;useSnapRestore&#34;: value.get(&#34;use_snap_restore&#34;, False),
            &#34;useLatestImage&#34;: value.get(&#34;use_latest_image&#34;, True),
            &#34;tableViewRestore&#34;: value.get(&#34;table_view_restore&#34;, False),
            &#34;useLogTarget&#34;: value.get(&#34;use_log_target&#34;, False),
            &#34;cloneRecovery&#34;: value.get(&#34;clone_recovery&#34;, False)
        }

        if value.get(&#34;archive_log_time&#34;, False):
            self.db2_options_restore_json[&#34;logTimeRange&#34;] = dict()
            self.db2_options_restore_json[&#34;logTimeRange&#34;][&#34;fromTimeValue&#34;] = value.get(&#34;from_time_value&#34;, 0)
            self.db2_options_restore_json[&#34;logTimeRange&#34;][&#34;toTimeValue&#34;] = value.get(&#34;to_time_value&#34;, 0)

        if value.get(&#34;archivelog_lsn&#34;, False):
            self.db2_options_restore_json[&#34;startLSNNum&#34;] = value.get(&#34;start_lsn_num&#34;, 1)
            self.db2_options_restore_json[&#34;endLSNNum&#34;] = value.get(&#34;end_lsn_num&#34;, 1)

        return self.db2_options_restore_json

    def _restore_json(self, **kwargs):
        &#34;&#34;&#34;Returns the JSON request to pass to the API as per the options selected by the user.

            Args:
                kwargs   (dict)  --  list of options need to be set for restore

            Returns:
                dict - JSON request to pass to the API

        &#34;&#34;&#34;
        rest_json = super(DB2Instance, self)._restore_json(**kwargs)
        restore_option = {}
        if kwargs.get(&#34;restore_option&#34;):
            restore_option = kwargs[&#34;restore_option&#34;]
            for key in kwargs:
                if not key == &#34;restore_option&#34;:
                    restore_option[key] = kwargs[key]
        else:
            restore_option = kwargs

        json = self._db2_restore_options_json(restore_option)
        rest_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;db2Option&#34;] = json

        return rest_json

    def restore_entire_database(
            self,
            dest_client_name,
            dest_instance_name,
            dest_database_name,
            **kwargs
    ):
        &#34;&#34;&#34;Restores the db2 database

            Args:

                dest_client_name        (str)   --  destination client name

                dest_instance_name      (str)   --  destination db2 instance name of
                destination on destination client

                dest_database_name      (str)   -- destination database name

                restore_type            (str)   -- db2 restore type

                    default: &#34;ENTIREDB&#34;

                recover_db              (bool)  -- recover database flag

                    default: True

                restore_incremental     (bool)  -- Restore incremental flag

                    default: True

                restore_data            (bool)  -- Restore data or not
                    default: True

                copy_precedence         (int)   -- Copy precedence to perform restore from
                    default : None

                roll_forward            (bool)  -- Rollforward database or not
                    default: True

                restore_logs (bool)  -   Restore the logs or not
                default: True

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;

        recover_db = kwargs.get(&#34;recover_db&#34;, True)
        restore_incremental = kwargs.get(&#34;restore_incremental&#34;, True)
        restore_data = kwargs.get(&#34;restore_data&#34;, True)
        copy_precedence = kwargs.get(&#34;copy_precedence&#34;, None)
        roll_forward = kwargs.get(&#34;roll_forward&#34;, True)
        restore_logs = kwargs.get(&#34;restore_logs&#34;, True)
        restore_type = kwargs.get(&#34;restore_type&#34;, &#39;ENTIREDB&#39;)
        start_lsn_num = kwargs.get(&#34;startLSNNum&#34;, 1)
        end_lsn_num = kwargs.get(&#34;endLSNNum&#34;, 1)
        end_lsn = kwargs.get(&#34;endLSN&#34;, False)
        start_lsn = kwargs.get(&#34;startLSN&#34;, False)
        archivelog_lsn = kwargs.get(&#34;archiveLogLSN&#34;, False)
        archive_log_time = kwargs.get(&#34;archiveLogTime&#34;, False)
        logtime_start = kwargs.get(&#34;logTimeStart&#34;, False)
        logtime_end = kwargs.get(&#34;logTimeEnd&#34;, False)
        from_time_value = kwargs.get(&#34;fromTimeValue&#34;, 0)
        to_time_value = kwargs.get(&#34;toTimeValue&#34;, 0)


        if &#34;entiredb&#34; in restore_type.lower():
            restore_type = 0

        request_json = self._restore_json(
            dest_client_name=dest_client_name,
            dest_instance_name=dest_instance_name,
            dest_backupset_name=dest_database_name,
            target_db=dest_database_name,
            restore_type=restore_type,
            recover_db=recover_db,
            restore_incremental=restore_incremental,
            restore_data=restore_data,
            copy_precedence=copy_precedence,
            roll_forward=roll_forward,
            rollforward_pending=not roll_forward,
            restore_archive_logs=restore_logs,
            start_lsn_num=start_lsn_num,
            end_lsn_num=end_lsn_num,
            archivelog_lsn=archivelog_lsn,
            start_lsn=start_lsn,
            end_lsn=end_lsn,
            archive_log_time=archive_log_time,
            logtime_start=logtime_start,
            logtime_end=logtime_end,
            from_time_value=from_time_value,
            to_time_value=to_time_value
        )

        return self._process_restore_response(request_json)

    def restore_out_of_place(
            self,
            dest_client_name,
            dest_instance_name,
            dest_backupset_name,
            target_path,
            **kwargs):
        &#34;&#34;&#34;Restores the DB2 data/log files specified in the input paths
        list to the same location.

            Args:
                dest_client_name        (str)   --  destination client name where files are to be
                restored

                dest_instance_name      (str)   --  destination db2 instance name of
                destination client

                dest_backupset_name     (str)   --  destination db2 backupset name of
                destination client

                target_path             (str)   --  Destination DB restore path

                copy_precedence         (int)   --  copy precedence value of storage policy copy
                    default: None

                from_time               (str)   --  time to retore the contents after
                    format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time                 (str)   --  time to retore the contents before
                    format: YYYY-MM-DD HH:MM:SS

                    default: None

                redirect_enabled         (bool)  --  boolean to specify if redirect restore is
                enabled

                    default: False

                redirect_storage_group_path           (dict)   --  Path specified for each storage group
                in advanced restore options in order to perform redirect restore
                    format: {&#39;Storage Group Name&#39;: &#39;Redirect Path&#39;}

                    default: None

                 redirect_tablespace_path           (dict)   --  Path specified for each tablespace in advanced
                 restore options in order to perform redirect restore
                    format: {&#39;Tablespace name&#39;: &#39;Redirect Path&#39;}

                    default: None

                destination_path        (str)   --  destinath path for restore
                    default: None

                restore_data            (bool)  -- Restore data or not

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;

        copy_precedence = kwargs.get(&#39;copy_precedence&#39;, None)
        from_time = kwargs.get(&#39;from_time&#39;, None)
        to_time = kwargs.get(&#39;to_time&#39;, None)
        redirect_enabled = kwargs.get(&#39;redirect_enabled&#39;, False)
        redirect_tablespace_path = kwargs.get(&#39;redirect_tablespace_path&#39;, None)
        redirect_storage_group_path = kwargs.get(&#39;redirect_storage_group_path&#39;, None)
        rollforward = kwargs.get(&#39;rollforward&#39;, True)
        restoreArchiveLogs = kwargs.get(&#39;restoreArchiveLogs&#39;, False)
        restore_incremental = kwargs.get(&#39;restore_incremental&#39;, True)
        restore_data = kwargs.get(&#39;restore_data&#39;, True)

        if redirect_enabled:
            if not (isinstance(redirect_tablespace_path, dict) or isinstance(redirect_tablespace_path, str)) and \
                    not isinstance(redirect_storage_group_path, dict):
                raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        request_json = self._restore_json(
            dest_client_name=dest_client_name,
            dest_instance_name=dest_instance_name,
            dest_backupset_name=dest_backupset_name,
            target_db=dest_backupset_name,
            target_path=target_path,
            copy_precedence=copy_precedence,
            from_time=from_time,
            to_time=to_time,
            redirect=redirect_enabled,
            redirect_storage_group_path=redirect_storage_group_path,
            redirect_tablespace_path=redirect_tablespace_path,
            rollforward_pending=not rollforward,
            restore_archive_logs=restoreArchiveLogs,
            roll_forward=rollforward,
            restore_incremental=restore_incremental,
            storage_path=True,
            restore_data=restore_data)

        if redirect_storage_group_path:
            storagePaths = []
            storageGroup = {&#34;storageGroup&#34;: []}

            for name, path in redirect_storage_group_path.items():
                if isinstance(path, str):
                    storageGroup[&#34;storageGroup&#34;].append({&#34;groupName&#34;: name, &#34;stoPaths&#34;: [path]})
                    storagePaths = [path]
                else:
                    storageGroup[&#34;storageGroup&#34;].append({&#34;groupName&#34;: name, &#34;stoPaths&#34;: path})
                    storagePaths = [path[0]]

            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;db2Option&#34;][
                &#34;redirectStorageGroups&#34;] = True
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;db2Option&#34;][
                &#34;storagePaths&#34;] = storagePaths
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;db2Option&#34;][
                &#34;storageGroupInfo&#34;] = storageGroup

        if redirect_tablespace_path:
            if isinstance(redirect_tablespace_path, str):
                request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;db2Option&#34;][
                    &#34;redirectAllPaths&#34;] = redirect_tablespace_path
                request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;db2Option&#34;][
                    &#34;redirectAllTableSpacesSelected&#34;] = True
                request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;db2Option&#34;][
                    &#34;redirectAllTableSpacesValue&#34;] = redirect_tablespace_path
            else:
                redirect_info = []
                for tablespace, path in redirect_tablespace_path.items():
                    table_string = &#34;%s\t1\t%s\t6\t25600\t1\t1&#34; % (tablespace, path)
                    redirect_info.append(table_string)
                request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;db2Option&#34;][
                    &#34;redirectInfo&#34;] = redirect_info

        return self._process_restore_response(request_json)

    def restore_table_level(
            self,
            aux_client_name,
            aux_instance_name,
            aux_backupset_name,
            dest_client_name,
            dest_instance_name,
            dest_backupset_name,
            target_path,
            staging_path,
            tables_path,
            user_name,
            password,
            **kwargs
        ):
        &#34;&#34;&#34;
        Performs DB2 table level restore
            Args:
                aux_client_name         (str)   --  auxiliary client name where files are to be restored
                aux_instance_name       (str)   --  auxiliary instance name where files are to be restored
                aux_backupset_name      (str)   --  auxiliary backupset name where files are to be restored
                dest_client_name        (str)   --  destination client name where files are to be restored
                dest_instance_name      (str)   --  destination db2 instance name of destination client
                dest_backupset_name     (str)   --  destination db2 backupset name of destination client

                target_path             (str)   --  Destination DB restore path

                src_backupset_name       (str)   --  Source Backupset Name

                staging_path             (str)   -- Staging Path

                user_name                (str)   -- Destination User name

                password                 (str)  --  Destination User Password

                tables_path             (list)   -- List of tables path
                    Example:
                        Unix:  [&#39;/+tblview+/instance_name/database_name/schema_name/table_name/**&#39;]
                        Windows: [&#34;\\+tblview+\\instance_name\\database_name\\schema_name\\table_name\\**&#34;]

                copy_precedence         (int)   --  copy precedence value of storage policy copy
                    default: None

                from_time               (str)   --  time to retore the contents after
                    format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time                 (str)   --  time to retore the contents before
                    format: YYYY-MM-DD HH:MM:SS

                    default: None

                rollForward             (bool)   --   Rollforward or not
                    default: True

                destination_path        (str)   --  destinath path for restore
                    default: None

                server_port              (int)   -- Server Port Destination instance
                    default: 50000

                generateAuthorizationDDL    (bool)  -- Generate Authorization DDL
                    default: False

                extractDDLStatements        (bool)  --  Extracts DDL statement or not
                    default: True

                clearAuxiliary              (bool)  -- Cleanup auxilliary or not
                    default: True

                dropTable                   (bool)  -- Drop table for import
                    default: False


            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        copy_precedence = kwargs.get(&#39;copy_precedence&#39;, None)
        from_time = kwargs.get(&#39;from_time&#39;, None)
        to_time = kwargs.get(&#39;to_time&#39;, None)
        rollforward = kwargs.get(&#39;rollforward&#39;, True)
        restoreArchiveLogs = kwargs.get(&#39;restoreArchiveLogs&#39;, False)

        request_json = self._restore_json(
            dest_client_name=aux_client_name,
            dest_instance_name=aux_instance_name,
            dest_backupset_name=aux_backupset_name,
            target_db=aux_backupset_name,
            target_path=target_path,
            copy_precedence=copy_precedence,
            from_time=from_time,
            to_time=to_time,
            rollforward_pending=not rollforward,
            restoreArchiveLogs=restoreArchiveLogs,
            roll_forward=rollforward,
            storage_path=True,
            table_view_restore=True)

        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;db2Option&#34;][
            &#34;storagePaths&#34;] = [target_path]

        password = b64encode(password.encode()).decode()

        table_json = {
            &#34;additionalExportParameter&#34;: kwargs.get(&#34;additionalExportParameter&#34;, &#34;&#34;),
            &#34;serverPort&#34;: kwargs.get(&#34;server_port&#34;, 50000),
            &#34;generateAuthorizationDDL&#34;: kwargs.get(&#34;generateAuthorizationDDL&#34;, False),
            &#34;importInstance&#34;: dest_instance_name,
            &#34;extractDDLStatements&#34;: kwargs.get(&#34;extractDDLStatements&#34;, True),
            &#34;useAdditionalExportParameters&#34;: kwargs.get(&#34;useAdditionalExportParameters&#34;, False),
            &#34;auxiliaryInstance&#34;: False,
            &#34;clearAuxiliary&#34;: kwargs.get(&#34;clearAuxiliary&#34;, True),
            &#34;importDatabase&#34;: dest_backupset_name,
            &#34;importToWhere&#34;: 2,
            &#34;dropTable&#34;: kwargs.get(&#34;dropTable&#34;, False),
            &#34;stagingPath&#34;: staging_path,
            &#34;importDbClient&#34;: {&#34;clientName&#34;: dest_client_name},
            &#34;importUserInfo&#34;: {&#34;userName&#34;: user_name, &#34;password&#34;: password}
        }

        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;db2Option&#34;][
            &#34;storagePaths&#34;] = [target_path]

        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;db2Option&#34;][
            &#34;databaseTableRstOptions&#34;] = table_json

        request_json[&#39;taskInfo&#39;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;browseOption&#34;][&#34;backupset&#34;][&#34;backupsetId&#34;] = int(self.backupsets.get(aux_backupset_name).backupset_id)

        request_json[&#39;taskInfo&#39;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;browseOption&#34;][&#34;backupset&#34;][&#34;backupsetName&#34;] = aux_backupset_name

        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;db2Option&#34;][
            &#34;restoreArchiveLogs&#34;] = False

        request_json[&#39;taskInfo&#39;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;fileOption&#34;][&#34;filterItem&#34;] = tables_path
        request_json[&#39;taskInfo&#39;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;fileOption&#34;][&#34;sourceItem&#34;] = tables_path
        return self._process_restore_response(request_json)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.instance.Instance" href="../instance.html#cvpysdk.instance.Instance">Instance</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.instances.db2instance.DB2Instance.command_line_storage_policy"><code class="name">var <span class="ident">command_line_storage_policy</span></code></dt>
<dd>
<div class="desc"><p>returns commandline storage policy</p>
<h2 id="returns">Returns</h2>
<p>(str)
&ndash;
Command line sp name from db2 instance level</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/db2instance.py#L112-L121" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def command_line_storage_policy(self):
    &#34;&#34;&#34;returns commandline storage policy

        Returns:
            (str)  --  Command line sp name from db2 instance level

    &#34;&#34;&#34;
    return self._properties.get(&#39;db2Instance&#39;, {}).get(
        &#39;DB2StorageDevice&#39;, {}).get(&#39;commandLineStoragePolicy&#39;, {}).get(&#39;storagePolicyName&#39;, &#34;&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.db2instance.DB2Instance.data_backup_storage_policy"><code class="name">var <span class="ident">data_backup_storage_policy</span></code></dt>
<dd>
<div class="desc"><p>returns data backup storage policy</p>
<h2 id="returns">Returns</h2>
<p>(str) &ndash; Storage policy name from db2 instance level</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/db2instance.py#L101-L110" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def data_backup_storage_policy(self):
    &#34;&#34;&#34; returns data backup storage policy

        Returns:
            (str) -- Storage policy name from db2 instance level

    &#34;&#34;&#34;
    return self._properties.get(&#39;db2Instance&#39;, {}).get(
        &#39;DB2StorageDevice&#39;, {}).get(&#39;dataBackupStoragePolicy&#39;, {}).get(&#39;storagePolicyName&#39;, &#34;&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.db2instance.DB2Instance.home_directory"><code class="name">var <span class="ident">home_directory</span></code></dt>
<dd>
<div class="desc"><p>returns db2 home directory</p>
<h2 id="returns">Returns</h2>
<p>(str) - string of db2_home</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/db2instance.py#L78-L87" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def home_directory(self):
    &#34;&#34;&#34;
    returns db2 home directory

    Returns:
        (str) - string of db2_home

    &#34;&#34;&#34;
    return self._properties.get(&#39;db2Instance&#39;, {}).get(&#39;homeDirectory&#39;, &#34;&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.db2instance.DB2Instance.log_backup_storage_policy"><code class="name">var <span class="ident">log_backup_storage_policy</span></code></dt>
<dd>
<div class="desc"><p>returns log backup storage policy</p>
<pre><code>Returns:
    (str)  -- Log backup SP name from instance level
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/db2instance.py#L123-L133" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def log_backup_storage_policy(self):
    &#34;&#34;&#34;
    returns log backup storage policy

        Returns:
            (str)  -- Log backup SP name from instance level

    &#34;&#34;&#34;
    return self._properties.get(&#39;db2Instance&#39;, {}).get(
        &#39;DB2StorageDevice&#39;, {}).get(&#39;logBackupStoragePolicy&#39;, {}).get(&#39;storagePolicyName&#39;, &#34;&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.db2instance.DB2Instance.user_name"><code class="name">var <span class="ident">user_name</span></code></dt>
<dd>
<div class="desc"><p>returns db2 user name</p>
<h2 id="returns">Returns</h2>
<p>(str)
- String containing db2 user</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/db2instance.py#L89-L99" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def user_name(self):
    &#34;&#34;&#34;
            returns db2 user name

            Returns:
                (str)  - String containing db2 user

    &#34;&#34;&#34;
    return self._properties.get(
        &#39;db2Instance&#39;, {}).get(&#39;userAccount&#39;, {}).get(&#39;userName&#39;, &#34;&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.db2instance.DB2Instance.version"><code class="name">var <span class="ident">version</span></code></dt>
<dd>
<div class="desc"><p>returns db2 version</p>
<h2 id="returns">Returns</h2>
<p>(str) &ndash; db2 version value in string</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/db2instance.py#L68-L76" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def version(self):
    &#34;&#34;&#34;returns db2 version

    Returns:
        (str) -- db2 version value in string

    &#34;&#34;&#34;
    return self._properties.get(&#39;version&#39;, &#34;&#34;)</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.instances.db2instance.DB2Instance.restore_entire_database"><code class="name flex">
<span>def <span class="ident">restore_entire_database</span></span>(<span>self, dest_client_name, dest_instance_name, dest_database_name, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the db2 database</p>
<h2 id="args">Args</h2>
<p>dest_client_name
(str)
&ndash;
destination client name</p>
<p>dest_instance_name
(str)
&ndash;
destination db2 instance name of
destination on destination client</p>
<p>dest_database_name
(str)
&ndash; destination database name</p>
<p>restore_type
(str)
&ndash; db2 restore type</p>
<pre><code>default: "ENTIREDB"
</code></pre>
<p>recover_db
(bool)
&ndash; recover database flag</p>
<pre><code>default: True
</code></pre>
<p>restore_incremental
(bool)
&ndash; Restore incremental flag</p>
<pre><code>default: True
</code></pre>
<p>restore_data
(bool)
&ndash; Restore data or not
default: True</p>
<p>copy_precedence
(int)
&ndash; Copy precedence to perform restore from
default : None</p>
<p>roll_forward
(bool)
&ndash; Rollforward database or not
default: True</p>
<dl>
<dt>restore_logs (bool)
-
Restore the logs or not</dt>
<dt><strong><code>default</code></strong></dt>
<dd>True</dd>
</dl>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if failed to initialize job

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/db2instance.py#L233-L336" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_entire_database(
        self,
        dest_client_name,
        dest_instance_name,
        dest_database_name,
        **kwargs
):
    &#34;&#34;&#34;Restores the db2 database

        Args:

            dest_client_name        (str)   --  destination client name

            dest_instance_name      (str)   --  destination db2 instance name of
            destination on destination client

            dest_database_name      (str)   -- destination database name

            restore_type            (str)   -- db2 restore type

                default: &#34;ENTIREDB&#34;

            recover_db              (bool)  -- recover database flag

                default: True

            restore_incremental     (bool)  -- Restore incremental flag

                default: True

            restore_data            (bool)  -- Restore data or not
                default: True

            copy_precedence         (int)   -- Copy precedence to perform restore from
                default : None

            roll_forward            (bool)  -- Rollforward database or not
                default: True

            restore_logs (bool)  -   Restore the logs or not
            default: True

        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:

                if failed to initialize job

                if response is empty

                if response is not success

    &#34;&#34;&#34;

    recover_db = kwargs.get(&#34;recover_db&#34;, True)
    restore_incremental = kwargs.get(&#34;restore_incremental&#34;, True)
    restore_data = kwargs.get(&#34;restore_data&#34;, True)
    copy_precedence = kwargs.get(&#34;copy_precedence&#34;, None)
    roll_forward = kwargs.get(&#34;roll_forward&#34;, True)
    restore_logs = kwargs.get(&#34;restore_logs&#34;, True)
    restore_type = kwargs.get(&#34;restore_type&#34;, &#39;ENTIREDB&#39;)
    start_lsn_num = kwargs.get(&#34;startLSNNum&#34;, 1)
    end_lsn_num = kwargs.get(&#34;endLSNNum&#34;, 1)
    end_lsn = kwargs.get(&#34;endLSN&#34;, False)
    start_lsn = kwargs.get(&#34;startLSN&#34;, False)
    archivelog_lsn = kwargs.get(&#34;archiveLogLSN&#34;, False)
    archive_log_time = kwargs.get(&#34;archiveLogTime&#34;, False)
    logtime_start = kwargs.get(&#34;logTimeStart&#34;, False)
    logtime_end = kwargs.get(&#34;logTimeEnd&#34;, False)
    from_time_value = kwargs.get(&#34;fromTimeValue&#34;, 0)
    to_time_value = kwargs.get(&#34;toTimeValue&#34;, 0)


    if &#34;entiredb&#34; in restore_type.lower():
        restore_type = 0

    request_json = self._restore_json(
        dest_client_name=dest_client_name,
        dest_instance_name=dest_instance_name,
        dest_backupset_name=dest_database_name,
        target_db=dest_database_name,
        restore_type=restore_type,
        recover_db=recover_db,
        restore_incremental=restore_incremental,
        restore_data=restore_data,
        copy_precedence=copy_precedence,
        roll_forward=roll_forward,
        rollforward_pending=not roll_forward,
        restore_archive_logs=restore_logs,
        start_lsn_num=start_lsn_num,
        end_lsn_num=end_lsn_num,
        archivelog_lsn=archivelog_lsn,
        start_lsn=start_lsn,
        end_lsn=end_lsn,
        archive_log_time=archive_log_time,
        logtime_start=logtime_start,
        logtime_end=logtime_end,
        from_time_value=from_time_value,
        to_time_value=to_time_value
    )

    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.db2instance.DB2Instance.restore_out_of_place"><code class="name flex">
<span>def <span class="ident">restore_out_of_place</span></span>(<span>self, dest_client_name, dest_instance_name, dest_backupset_name, target_path, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the DB2 data/log files specified in the input paths
list to the same location.</p>
<pre><code>Args:
    dest_client_name        (str)   --  destination client name where files are to be
    restored

    dest_instance_name      (str)   --  destination db2 instance name of
    destination client

    dest_backupset_name     (str)   --  destination db2 backupset name of
    destination client

    target_path             (str)   --  Destination DB restore path

    copy_precedence         (int)   --  copy precedence value of storage policy copy
        default: None

    from_time               (str)   --  time to retore the contents after
        format: YYYY-MM-DD HH:MM:SS

        default: None

    to_time                 (str)   --  time to retore the contents before
        format: YYYY-MM-DD HH:MM:SS

        default: None

    redirect_enabled         (bool)  --  boolean to specify if redirect restore is
    enabled

        default: False

    redirect_storage_group_path           (dict)   --  Path specified for each storage group
    in advanced restore options in order to perform redirect restore
        format: {'Storage Group Name': 'Redirect Path'}

        default: None

     redirect_tablespace_path           (dict)   --  Path specified for each tablespace in advanced
     restore options in order to perform redirect restore
        format: {'Tablespace name': 'Redirect Path'}

        default: None

    destination_path        (str)   --  destinath path for restore
        default: None

    restore_data            (bool)  -- Restore data or not

Returns:
    object - instance of the Job class for this restore job

Raises:
    SDKException:
        if failed to initialize job

        if response is empty

        if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/db2instance.py#L338-L478" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_out_of_place(
        self,
        dest_client_name,
        dest_instance_name,
        dest_backupset_name,
        target_path,
        **kwargs):
    &#34;&#34;&#34;Restores the DB2 data/log files specified in the input paths
    list to the same location.

        Args:
            dest_client_name        (str)   --  destination client name where files are to be
            restored

            dest_instance_name      (str)   --  destination db2 instance name of
            destination client

            dest_backupset_name     (str)   --  destination db2 backupset name of
            destination client

            target_path             (str)   --  Destination DB restore path

            copy_precedence         (int)   --  copy precedence value of storage policy copy
                default: None

            from_time               (str)   --  time to retore the contents after
                format: YYYY-MM-DD HH:MM:SS

                default: None

            to_time                 (str)   --  time to retore the contents before
                format: YYYY-MM-DD HH:MM:SS

                default: None

            redirect_enabled         (bool)  --  boolean to specify if redirect restore is
            enabled

                default: False

            redirect_storage_group_path           (dict)   --  Path specified for each storage group
            in advanced restore options in order to perform redirect restore
                format: {&#39;Storage Group Name&#39;: &#39;Redirect Path&#39;}

                default: None

             redirect_tablespace_path           (dict)   --  Path specified for each tablespace in advanced
             restore options in order to perform redirect restore
                format: {&#39;Tablespace name&#39;: &#39;Redirect Path&#39;}

                default: None

            destination_path        (str)   --  destinath path for restore
                default: None

            restore_data            (bool)  -- Restore data or not

        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:
                if failed to initialize job

                if response is empty

                if response is not success

    &#34;&#34;&#34;

    copy_precedence = kwargs.get(&#39;copy_precedence&#39;, None)
    from_time = kwargs.get(&#39;from_time&#39;, None)
    to_time = kwargs.get(&#39;to_time&#39;, None)
    redirect_enabled = kwargs.get(&#39;redirect_enabled&#39;, False)
    redirect_tablespace_path = kwargs.get(&#39;redirect_tablespace_path&#39;, None)
    redirect_storage_group_path = kwargs.get(&#39;redirect_storage_group_path&#39;, None)
    rollforward = kwargs.get(&#39;rollforward&#39;, True)
    restoreArchiveLogs = kwargs.get(&#39;restoreArchiveLogs&#39;, False)
    restore_incremental = kwargs.get(&#39;restore_incremental&#39;, True)
    restore_data = kwargs.get(&#39;restore_data&#39;, True)

    if redirect_enabled:
        if not (isinstance(redirect_tablespace_path, dict) or isinstance(redirect_tablespace_path, str)) and \
                not isinstance(redirect_storage_group_path, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

    request_json = self._restore_json(
        dest_client_name=dest_client_name,
        dest_instance_name=dest_instance_name,
        dest_backupset_name=dest_backupset_name,
        target_db=dest_backupset_name,
        target_path=target_path,
        copy_precedence=copy_precedence,
        from_time=from_time,
        to_time=to_time,
        redirect=redirect_enabled,
        redirect_storage_group_path=redirect_storage_group_path,
        redirect_tablespace_path=redirect_tablespace_path,
        rollforward_pending=not rollforward,
        restore_archive_logs=restoreArchiveLogs,
        roll_forward=rollforward,
        restore_incremental=restore_incremental,
        storage_path=True,
        restore_data=restore_data)

    if redirect_storage_group_path:
        storagePaths = []
        storageGroup = {&#34;storageGroup&#34;: []}

        for name, path in redirect_storage_group_path.items():
            if isinstance(path, str):
                storageGroup[&#34;storageGroup&#34;].append({&#34;groupName&#34;: name, &#34;stoPaths&#34;: [path]})
                storagePaths = [path]
            else:
                storageGroup[&#34;storageGroup&#34;].append({&#34;groupName&#34;: name, &#34;stoPaths&#34;: path})
                storagePaths = [path[0]]

        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;db2Option&#34;][
            &#34;redirectStorageGroups&#34;] = True
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;db2Option&#34;][
            &#34;storagePaths&#34;] = storagePaths
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;db2Option&#34;][
            &#34;storageGroupInfo&#34;] = storageGroup

    if redirect_tablespace_path:
        if isinstance(redirect_tablespace_path, str):
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;db2Option&#34;][
                &#34;redirectAllPaths&#34;] = redirect_tablespace_path
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;db2Option&#34;][
                &#34;redirectAllTableSpacesSelected&#34;] = True
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;db2Option&#34;][
                &#34;redirectAllTableSpacesValue&#34;] = redirect_tablespace_path
        else:
            redirect_info = []
            for tablespace, path in redirect_tablespace_path.items():
                table_string = &#34;%s\t1\t%s\t6\t25600\t1\t1&#34; % (tablespace, path)
                redirect_info.append(table_string)
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;db2Option&#34;][
                &#34;redirectInfo&#34;] = redirect_info

    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.db2instance.DB2Instance.restore_table_level"><code class="name flex">
<span>def <span class="ident">restore_table_level</span></span>(<span>self, aux_client_name, aux_instance_name, aux_backupset_name, dest_client_name, dest_instance_name, dest_backupset_name, target_path, staging_path, tables_path, user_name, password, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Performs DB2 table level restore
Args:
aux_client_name
(str)
&ndash;
auxiliary client name where files are to be restored
aux_instance_name
(str)
&ndash;
auxiliary instance name where files are to be restored
aux_backupset_name
(str)
&ndash;
auxiliary backupset name where files are to be restored
dest_client_name
(str)
&ndash;
destination client name where files are to be restored
dest_instance_name
(str)
&ndash;
destination db2 instance name of destination client
dest_backupset_name
(str)
&ndash;
destination db2 backupset name of destination client</p>
<pre><code>    target_path             (str)   --  Destination DB restore path

    src_backupset_name       (str)   --  Source Backupset Name

    staging_path             (str)   -- Staging Path

    user_name                (str)   -- Destination User name

    password                 (str)  --  Destination User Password

    tables_path             (list)   -- List of tables path
        Example:
            Unix:  ['/+tblview+/instance_name/database_name/schema_name/table_name/**']
            Windows: ["\+tblview+\instance_name\database_name\schema_name\table_name\**"]

    copy_precedence         (int)   --  copy precedence value of storage policy copy
        default: None

    from_time               (str)   --  time to retore the contents after
        format: YYYY-MM-DD HH:MM:SS

        default: None

    to_time                 (str)   --  time to retore the contents before
        format: YYYY-MM-DD HH:MM:SS

        default: None

    rollForward             (bool)   --   Rollforward or not
        default: True

    destination_path        (str)   --  destinath path for restore
        default: None

    server_port              (int)   -- Server Port Destination instance
        default: 50000

    generateAuthorizationDDL    (bool)  -- Generate Authorization DDL
        default: False

    extractDDLStatements        (bool)  --  Extracts DDL statement or not
        default: True

    clearAuxiliary              (bool)  -- Cleanup auxilliary or not
        default: True

    dropTable                   (bool)  -- Drop table for import
        default: False


Returns:
    object - instance of the Job class for this restore job

Raises:
    SDKException:
        if failed to initialize job

        if response is empty

        if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/db2instance.py#L480-L629" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_table_level(
        self,
        aux_client_name,
        aux_instance_name,
        aux_backupset_name,
        dest_client_name,
        dest_instance_name,
        dest_backupset_name,
        target_path,
        staging_path,
        tables_path,
        user_name,
        password,
        **kwargs
    ):
    &#34;&#34;&#34;
    Performs DB2 table level restore
        Args:
            aux_client_name         (str)   --  auxiliary client name where files are to be restored
            aux_instance_name       (str)   --  auxiliary instance name where files are to be restored
            aux_backupset_name      (str)   --  auxiliary backupset name where files are to be restored
            dest_client_name        (str)   --  destination client name where files are to be restored
            dest_instance_name      (str)   --  destination db2 instance name of destination client
            dest_backupset_name     (str)   --  destination db2 backupset name of destination client

            target_path             (str)   --  Destination DB restore path

            src_backupset_name       (str)   --  Source Backupset Name

            staging_path             (str)   -- Staging Path

            user_name                (str)   -- Destination User name

            password                 (str)  --  Destination User Password

            tables_path             (list)   -- List of tables path
                Example:
                    Unix:  [&#39;/+tblview+/instance_name/database_name/schema_name/table_name/**&#39;]
                    Windows: [&#34;\\+tblview+\\instance_name\\database_name\\schema_name\\table_name\\**&#34;]

            copy_precedence         (int)   --  copy precedence value of storage policy copy
                default: None

            from_time               (str)   --  time to retore the contents after
                format: YYYY-MM-DD HH:MM:SS

                default: None

            to_time                 (str)   --  time to retore the contents before
                format: YYYY-MM-DD HH:MM:SS

                default: None

            rollForward             (bool)   --   Rollforward or not
                default: True

            destination_path        (str)   --  destinath path for restore
                default: None

            server_port              (int)   -- Server Port Destination instance
                default: 50000

            generateAuthorizationDDL    (bool)  -- Generate Authorization DDL
                default: False

            extractDDLStatements        (bool)  --  Extracts DDL statement or not
                default: True

            clearAuxiliary              (bool)  -- Cleanup auxilliary or not
                default: True

            dropTable                   (bool)  -- Drop table for import
                default: False


        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:
                if failed to initialize job

                if response is empty

                if response is not success

    &#34;&#34;&#34;
    copy_precedence = kwargs.get(&#39;copy_precedence&#39;, None)
    from_time = kwargs.get(&#39;from_time&#39;, None)
    to_time = kwargs.get(&#39;to_time&#39;, None)
    rollforward = kwargs.get(&#39;rollforward&#39;, True)
    restoreArchiveLogs = kwargs.get(&#39;restoreArchiveLogs&#39;, False)

    request_json = self._restore_json(
        dest_client_name=aux_client_name,
        dest_instance_name=aux_instance_name,
        dest_backupset_name=aux_backupset_name,
        target_db=aux_backupset_name,
        target_path=target_path,
        copy_precedence=copy_precedence,
        from_time=from_time,
        to_time=to_time,
        rollforward_pending=not rollforward,
        restoreArchiveLogs=restoreArchiveLogs,
        roll_forward=rollforward,
        storage_path=True,
        table_view_restore=True)

    request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;db2Option&#34;][
        &#34;storagePaths&#34;] = [target_path]

    password = b64encode(password.encode()).decode()

    table_json = {
        &#34;additionalExportParameter&#34;: kwargs.get(&#34;additionalExportParameter&#34;, &#34;&#34;),
        &#34;serverPort&#34;: kwargs.get(&#34;server_port&#34;, 50000),
        &#34;generateAuthorizationDDL&#34;: kwargs.get(&#34;generateAuthorizationDDL&#34;, False),
        &#34;importInstance&#34;: dest_instance_name,
        &#34;extractDDLStatements&#34;: kwargs.get(&#34;extractDDLStatements&#34;, True),
        &#34;useAdditionalExportParameters&#34;: kwargs.get(&#34;useAdditionalExportParameters&#34;, False),
        &#34;auxiliaryInstance&#34;: False,
        &#34;clearAuxiliary&#34;: kwargs.get(&#34;clearAuxiliary&#34;, True),
        &#34;importDatabase&#34;: dest_backupset_name,
        &#34;importToWhere&#34;: 2,
        &#34;dropTable&#34;: kwargs.get(&#34;dropTable&#34;, False),
        &#34;stagingPath&#34;: staging_path,
        &#34;importDbClient&#34;: {&#34;clientName&#34;: dest_client_name},
        &#34;importUserInfo&#34;: {&#34;userName&#34;: user_name, &#34;password&#34;: password}
    }

    request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;db2Option&#34;][
        &#34;storagePaths&#34;] = [target_path]

    request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;db2Option&#34;][
        &#34;databaseTableRstOptions&#34;] = table_json

    request_json[&#39;taskInfo&#39;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
        &#34;browseOption&#34;][&#34;backupset&#34;][&#34;backupsetId&#34;] = int(self.backupsets.get(aux_backupset_name).backupset_id)

    request_json[&#39;taskInfo&#39;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
        &#34;browseOption&#34;][&#34;backupset&#34;][&#34;backupsetName&#34;] = aux_backupset_name

    request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;db2Option&#34;][
        &#34;restoreArchiveLogs&#34;] = False

    request_json[&#39;taskInfo&#39;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
        &#34;fileOption&#34;][&#34;filterItem&#34;] = tables_path
    request_json[&#39;taskInfo&#39;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
        &#34;fileOption&#34;][&#34;sourceItem&#34;] = tables_path
    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.instance.Instance" href="../instance.html#cvpysdk.instance.Instance">Instance</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.instance.Instance.backupsets" href="../instance.html#cvpysdk.instance.Instance.backupsets">backupsets</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.browse" href="../instance.html#cvpysdk.instance.Instance.browse">browse</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.credentials" href="../instance.html#cvpysdk.instance.Instance.credentials">credentials</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.find" href="../instance.html#cvpysdk.instance.Instance.find">find</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.instance_id" href="../instance.html#cvpysdk.instance.Instance.instance_id">instance_id</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.instance_name" href="../instance.html#cvpysdk.instance.Instance.instance_name">instance_name</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.name" href="../instance.html#cvpysdk.instance.Instance.name">name</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.properties" href="../instance.html#cvpysdk.instance.Instance.properties">properties</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.refresh" href="../instance.html#cvpysdk.instance.Instance.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.subclients" href="../instance.html#cvpysdk.instance.Instance.subclients">subclients</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.update_properties" href="../instance.html#cvpysdk.instance.Instance.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#db2instance">DB2Instance:</a></li>
<li><a href="#db2instance-instance-attributes">DB2Instance instance Attributes:</a></li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.instances" href="index.html">cvpysdk.instances</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.instances.db2instance.DB2Instance" href="#cvpysdk.instances.db2instance.DB2Instance">DB2Instance</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.instances.db2instance.DB2Instance.command_line_storage_policy" href="#cvpysdk.instances.db2instance.DB2Instance.command_line_storage_policy">command_line_storage_policy</a></code></li>
<li><code><a title="cvpysdk.instances.db2instance.DB2Instance.data_backup_storage_policy" href="#cvpysdk.instances.db2instance.DB2Instance.data_backup_storage_policy">data_backup_storage_policy</a></code></li>
<li><code><a title="cvpysdk.instances.db2instance.DB2Instance.home_directory" href="#cvpysdk.instances.db2instance.DB2Instance.home_directory">home_directory</a></code></li>
<li><code><a title="cvpysdk.instances.db2instance.DB2Instance.log_backup_storage_policy" href="#cvpysdk.instances.db2instance.DB2Instance.log_backup_storage_policy">log_backup_storage_policy</a></code></li>
<li><code><a title="cvpysdk.instances.db2instance.DB2Instance.restore_entire_database" href="#cvpysdk.instances.db2instance.DB2Instance.restore_entire_database">restore_entire_database</a></code></li>
<li><code><a title="cvpysdk.instances.db2instance.DB2Instance.restore_out_of_place" href="#cvpysdk.instances.db2instance.DB2Instance.restore_out_of_place">restore_out_of_place</a></code></li>
<li><code><a title="cvpysdk.instances.db2instance.DB2Instance.restore_table_level" href="#cvpysdk.instances.db2instance.DB2Instance.restore_table_level">restore_table_level</a></code></li>
<li><code><a title="cvpysdk.instances.db2instance.DB2Instance.user_name" href="#cvpysdk.instances.db2instance.DB2Instance.user_name">user_name</a></code></li>
<li><code><a title="cvpysdk.instances.db2instance.DB2Instance.version" href="#cvpysdk.instances.db2instance.DB2Instance.version">version</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>