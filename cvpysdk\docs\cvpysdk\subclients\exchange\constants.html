<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.subclients.exchange.constants API documentation</title>
<meta name="description" content="Helper file to maintain all the constants for MS Exchange subclient …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.subclients.exchange.constants</code></h1>
</header>
<section id="section-intro">
<p>Helper file to maintain all the constants for MS Exchange subclient.</p>
<p>ExchangeConstants
-
Maintains constants for MS Exchange subclient.</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/constants.py#L1-L299" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Helper file to maintain all the constants for MS Exchange subclient.

ExchangeConstants  -   Maintains constants for MS Exchange subclient.
&#34;&#34;&#34;
from enum import Enum

class ExchangeConstants:
    &#34;&#34;&#34;Class to maintain all the Exchange subclient related constants.&#34;&#34;&#34;

    SEAARCH_PROCESSING_INFO = {
        &#34;resultOffset&#34;: 0,
        &#34;pageSize&#34;: 100,
        &#34;queryParams&#34;: None,
        &#34;sortParams&#34;: [
            {
                &#34;sortDirection&#34;: 0, &#34;sortField&#34;: &#34;FROM_DISPLAY&#34;
            }
        ]
    }

    ADVANCED_SEARCH_GROUP = {
        &#34;commonFilter&#34;: [
            {
                &#34;filter&#34;: {
                    &#34;interFilterOP&#34;: 2,
                    &#34;filters&#34;: [
                        {
                            &#34;field&#34;: &#34;CISTATE&#34;,
                            &#34;intraFieldOp&#34;: 0,
                            &#34;fieldValues&#34;: {
                                &#34;values&#34;: [
                                    &#34;1&#34;
                                ]
                            }
                        },
                        {
                            &#34;field&#34;: &#34;IS_VISIBLE&#34;,
                            &#34;intraFieldOp&#34;: 0,
                            &#34;fieldValues&#34;: {
                                &#34;values&#34;: [
                                    &#34;true&#34;
                                ]
                            }
                        }
                    ]
                }
            }
        ],
        &#34;fileFilter&#34;: [],
        &#34;emailFilter&#34;: [
            {
                &#34;interGroupOP&#34;: 2,
                &#34;filter&#34;: {
                    &#34;interFilterOP&#34;: 2,
                    &#34;filters&#34;: [
                        {
                            &#34;field&#34;: &#34;IS_VISIBLE&#34;,
                            &#34;intraFieldOp&#34;: 0,
                            &#34;fieldValues&#34;: {
                                &#34;values&#34;: [
                                    &#34;true&#34;
                                ]
                            }
                        },
                        {
                            &#34;field&#34;: &#34;EXCH_VALID_AFID&#34;,
                            &#34;intraFieldOp&#34;: 0,
                            &#34;fieldValues&#34;: {
                                &#34;values&#34;: [
                                    &#34;true&#34;
                                ]
                            }
                        },
                        {
                            &#34;field&#34;: &#34;DATA_TYPE&#34;,
                            &#34;intraFieldOp&#34;: 0,
                            &#34;fieldValues&#34;: {
                                &#34;values&#34;: [
                                    &#34;2&#34;
                                ]
                            }
                        }

                    ]
                }
            }
        ],
        &#34;galaxyFilter&#34;: [
            {
                &#34;appIdList&#34;: None
            }
        ]
    }

    FIND_MAILBOX_REQUEST_DATA = {
        &#34;mode&#34;: 4,
        &#34;advSearchGrp&#34;: ADVANCED_SEARCH_GROUP,
        &#34;searchProcessingInfo&#34;: SEAARCH_PROCESSING_INFO,
        &#34;facetRequests&#34;: {
            &#34;facetRequest&#34;: None
        }
    }

    FIND_MBX_QUERY_DEFAULT_PARAMS = {
        &#34;RESPONSE_FIELD_LIST&#34;: &#34;COMMCELLNO,AFILEID,AFILEOFFSET,BACKUPTIME,SIZEINKB,MODIFIEDTIME,&#34;
                               &#34;CONTENTID,LINKS,EMAIL_SUBJECT,FROM_DISPLAY,TO_DISPLAY,FOLDER,&#34;
                               &#34;EMAIL_IMPORTANCE,CUSTODIAN,OWNER,CVSTUB,DATA_TYPE,PARENT_GUID,&#34;
                               &#34;CISTATE,EMAIL_ATTACHMENTS,HAS_ATTACHMENT,EMAIL_MODIFIED_TIME,&#34;
                               &#34;IS_VISIBLE,EXCH_MIGRATED,EXCH_MBX_PROPERTY_TYPE,SRC_APP_GUID,&#34;
                               &#34;ExtractAttempt_i&#34;,
        &#34;SHOW_EMAILS_ONLY&#34;: &#34;true&#34;, &#34;SUPPORT_SOLR_ONLY&#34;: &#34;true&#34;, &#34;ENABLE_FOLDERBROWSE&#34;: &#34;off&#34;,
        &#34;ENABLE_MIXEDVIEWSEARCH&#34;: &#34;true&#34;}

    FIND_MBX_DEFAULT_FACET = {&#34;MODIFIEDTIME&#34;, &#34;SIZEINKB&#34;, &#34;FOLDER_PATH&#34;}

    SEARCH_IN_RESTORE_PAYLOAD = {
        &#34;mode&#34;: 4,
        &#34;advSearchGrp&#34;: {
            &#34;commonFilter&#34;: [
                {
                    &#34;filter&#34;: {
                        &#34;interFilterOP&#34;: 2,
                        &#34;filters&#34;: [
                            {
                                &#34;field&#34;: &#34;CISTATE&#34;,
                                &#34;intraFieldOp&#34;: 0,
                                &#34;fieldValues&#34;: {
                                    &#34;values&#34;: [&#34;1&#34;]
                                }
                            },
                            {
                                &#34;field&#34;: &#34;IS_VISIBLE&#34;,
                                &#34;intraFieldOp&#34;: 0,
                                &#34;fieldValues&#34;: {
                                    &#34;values&#34;: [&#34;true&#34;]
                                }
                            }
                        ]
                    }
                }
            ],
            &#34;fileFilter&#34;: [],
            &#34;emailFilter&#34;: [
                {
                    &#34;interGroupOP&#34;: 2,
                    &#34;filter&#34;: {
                        &#34;interFilterOP&#34;: 2,
                        &#34;filters&#34;: [
                            {
                                &#34;field&#34;: &#34;IS_VISIBLE&#34;,
                                &#34;intraFieldOp&#34;: 0,
                                &#34;fieldValues&#34;: {
                                    &#34;values&#34;: [&#34;true&#34;]
                                }
                            },
                            {
                                &#34;field&#34;: &#34;EXCH_VALID_AFID&#34;,
                                &#34;intraFieldOp&#34;: 0,
                                &#34;fieldValues&#34;: {
                                    &#34;values&#34;: [&#34;true&#34;]
                                }
                            },
                            {
                                &#34;field&#34;: &#34;DATA_TYPE&#34;,
                                &#34;intraFieldOp&#34;: 0,
                                &#34;fieldValues&#34;: {
                                    &#34;values&#34;: [&#34;2&#34;]
                                }
                            }
                        ]
                    }
                }
            ],
            &#34;galaxyFilter&#34;: [
                {
                    &#34;appIdList&#34;: []
                }
            ],
            &#34;cvSearchKeyword&#34;: {
                &#34;isExactWordsOptionSelected&#34;: False,
                &#34;keyword&#34;: &#34;&#34;
            }
        },
        &#34;searchProcessingInfo&#34;: {
            &#34;resultOffset&#34;: 0,
            &#34;pageSize&#34;: 15,
            &#34;queryParams&#34;: [
                {
                    &#34;param&#34;: &#34;RESPONSE_FIELD_LIST&#34;,
                    &#34;value&#34;: &#34;COMMCELLNO,AFILEID,AFILEOFFSET,BACKUPTIME,SIZEINKB,MODIFIEDTIME,CONTENTID,LINKS,EMAIL_SUBJECT,FROM_DISPLAY,TO_DISPLAY,FOLDER,EMAIL_IMPORTANCE,CUSTODIAN,OWNER,CVSTUB,DATA_TYPE,PARENT_GUID,CISTATE,EMAIL_ATTACHMENTS,HAS_ATTACHMENT,EMAIL_MODIFIED_TIME,IS_VISIBLE,EXCH_MIGRATED,EXCH_MBX_PROPERTY_TYPE,SRC_APP_GUID,ExtractAttempt_i&#34;
                },
                {
                    &#34;param&#34;: &#34;DO_NOT_AUDIT&#34;,
                    &#34;value&#34;: &#34;false&#34;
                },
                {
                    &#34;param&#34;: &#34;ENABLE_HIGHLIGHTING&#34;,
                    &#34;value&#34;: &#34;false&#34;
                },
                {
                    &#34;param&#34;: &#34;SHOW_EMAILS_ONLY&#34;,
                    &#34;value&#34;: &#34;true&#34;
                },
                {
                    &#34;param&#34;: &#34;ENABLE_DEFAULTFACETS&#34;,
                    &#34;value&#34;: &#34;false&#34;
                },
                {
                    &#34;param&#34;: &#34;SUPPORT_SOLR_ONLY&#34;,
                    &#34;value&#34;: &#34;true&#34;
                },
                {
                    &#34;param&#34;: &#34;ENABLE_FOLDERBROWSE&#34;,
                    &#34;value&#34;: &#34;off&#34;
                },
                {
                    &#34;param&#34;: &#34;ENABLE_MIXEDVIEWSEARCH&#34;,
                    &#34;value&#34;: &#34;true&#34;
                },
                {
                    &#34;param&#34;: &#34;ENABLE_NAVIGATION&#34;,
                    &#34;value&#34;: &#34;on&#34;
                }
            ],
            &#34;sortParams&#34;: [
                {
                    &#34;sortDirection&#34;: 0,
                    &#34;sortField&#34;: &#34;EMAIL_SUBJECT&#34;
                }
            ]
        },
        &#34;facetRequests&#34;: {
            &#34;facetRequest&#34;: [
                {&#34;name&#34;: &#34;MODIFIEDTIME&#34;},
                {&#34;name&#34;: &#34;SIZEINKB&#34;},
                {&#34;name&#34;: &#34;CUSTODIAN&#34;},
                {&#34;name&#34;: &#34;HAS_ATTACHMENT&#34;},
                {&#34;name&#34;: &#34;FOLDER_PATH&#34;}
            ]
        }
    }

class JobOptionKeys(Enum):
    &#34;&#34;&#34;Enum to specify job option keys&#34;&#34;&#34;
    RESTORE_DESTINATION = &#34;Restore destination&#34;
    DESTINATION = &#34;Destination&#34;
    IF_MESSAGE_EXISTS = &#34;If the message exists&#34;
    INCLUDE_DELETED_ITEMS = &#34;Include deleted items&#34;
    MATCH_DESTINATION_USER = &#34;Match destination user based on the email address&#34;
    STUB_REHYDRATION = &#34;Stub rehydration&#34;
    STUB_REHYDRATION_OPTION = &#34;Stub rehydration option&#34;
    MAILBOX_LEVEL_REPORTING = &#34;Mailbox level reporting&#34;
    EMAIL_LEVEL_REPORTING = &#34;Email level reporting&#34;
    OLD_RECALL_LINK = &#34;Old recall link&#34;
    NEW_RECALL_LINK = &#34;New recall link&#34;
    EXCHANGE_RESTORE_CHOICE = &#34;exchangeRestoreChoice&#34;
    EXCHANGE_RESTORE_DRIVE = &#34;exchangeRestoreDrive&#34;
    IS_JOURNAL_REPORT = &#34;isJournalReport&#34;
    PST_FILE_PATH = &#34;pstFilePath&#34;
    TARGET_MAILBOX = &#34;stubRehydration&#34;

class JobOptionValues(Enum):
    &#34;&#34;&#34;Enum to specify job option values&#34;&#34;&#34;
    SKIP = &#34;Skip&#34;
    DISABLED = &#34;Disabled&#34;
    ENABLED = &#34;Enabled&#34;
    RECOVER_STUBS = &#34;Recover stubs&#34;
    STUB_REPORTING = &#34;Stub reporting&#34;
    UPDATE_RECALL_LINK = &#34;Update recall link&#34;
    EXCHANGE = &#34;Exchange&#34;
    ORIGINAL_LOCATION = &#34;Original Location&#34;

class JobOptionIntegers(Enum):
    &#34;&#34;&#34;Enum to specify job option integers&#34;&#34;&#34;
    EXCHANGE_RESTORE_CHOICE = 1
    EXCHANGE_RESTORE_DRIVE = 1
    RECOVER_STUBS = 0
    STUB_REPORTING = 1
    UPDATE_RECALL_LINK = 2</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.subclients.exchange.constants.ExchangeConstants"><code class="flex name class">
<span>class <span class="ident">ExchangeConstants</span></span>
</code></dt>
<dd>
<div class="desc"><p>Class to maintain all the Exchange subclient related constants.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/constants.py#L25-L259" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class ExchangeConstants:
    &#34;&#34;&#34;Class to maintain all the Exchange subclient related constants.&#34;&#34;&#34;

    SEAARCH_PROCESSING_INFO = {
        &#34;resultOffset&#34;: 0,
        &#34;pageSize&#34;: 100,
        &#34;queryParams&#34;: None,
        &#34;sortParams&#34;: [
            {
                &#34;sortDirection&#34;: 0, &#34;sortField&#34;: &#34;FROM_DISPLAY&#34;
            }
        ]
    }

    ADVANCED_SEARCH_GROUP = {
        &#34;commonFilter&#34;: [
            {
                &#34;filter&#34;: {
                    &#34;interFilterOP&#34;: 2,
                    &#34;filters&#34;: [
                        {
                            &#34;field&#34;: &#34;CISTATE&#34;,
                            &#34;intraFieldOp&#34;: 0,
                            &#34;fieldValues&#34;: {
                                &#34;values&#34;: [
                                    &#34;1&#34;
                                ]
                            }
                        },
                        {
                            &#34;field&#34;: &#34;IS_VISIBLE&#34;,
                            &#34;intraFieldOp&#34;: 0,
                            &#34;fieldValues&#34;: {
                                &#34;values&#34;: [
                                    &#34;true&#34;
                                ]
                            }
                        }
                    ]
                }
            }
        ],
        &#34;fileFilter&#34;: [],
        &#34;emailFilter&#34;: [
            {
                &#34;interGroupOP&#34;: 2,
                &#34;filter&#34;: {
                    &#34;interFilterOP&#34;: 2,
                    &#34;filters&#34;: [
                        {
                            &#34;field&#34;: &#34;IS_VISIBLE&#34;,
                            &#34;intraFieldOp&#34;: 0,
                            &#34;fieldValues&#34;: {
                                &#34;values&#34;: [
                                    &#34;true&#34;
                                ]
                            }
                        },
                        {
                            &#34;field&#34;: &#34;EXCH_VALID_AFID&#34;,
                            &#34;intraFieldOp&#34;: 0,
                            &#34;fieldValues&#34;: {
                                &#34;values&#34;: [
                                    &#34;true&#34;
                                ]
                            }
                        },
                        {
                            &#34;field&#34;: &#34;DATA_TYPE&#34;,
                            &#34;intraFieldOp&#34;: 0,
                            &#34;fieldValues&#34;: {
                                &#34;values&#34;: [
                                    &#34;2&#34;
                                ]
                            }
                        }

                    ]
                }
            }
        ],
        &#34;galaxyFilter&#34;: [
            {
                &#34;appIdList&#34;: None
            }
        ]
    }

    FIND_MAILBOX_REQUEST_DATA = {
        &#34;mode&#34;: 4,
        &#34;advSearchGrp&#34;: ADVANCED_SEARCH_GROUP,
        &#34;searchProcessingInfo&#34;: SEAARCH_PROCESSING_INFO,
        &#34;facetRequests&#34;: {
            &#34;facetRequest&#34;: None
        }
    }

    FIND_MBX_QUERY_DEFAULT_PARAMS = {
        &#34;RESPONSE_FIELD_LIST&#34;: &#34;COMMCELLNO,AFILEID,AFILEOFFSET,BACKUPTIME,SIZEINKB,MODIFIEDTIME,&#34;
                               &#34;CONTENTID,LINKS,EMAIL_SUBJECT,FROM_DISPLAY,TO_DISPLAY,FOLDER,&#34;
                               &#34;EMAIL_IMPORTANCE,CUSTODIAN,OWNER,CVSTUB,DATA_TYPE,PARENT_GUID,&#34;
                               &#34;CISTATE,EMAIL_ATTACHMENTS,HAS_ATTACHMENT,EMAIL_MODIFIED_TIME,&#34;
                               &#34;IS_VISIBLE,EXCH_MIGRATED,EXCH_MBX_PROPERTY_TYPE,SRC_APP_GUID,&#34;
                               &#34;ExtractAttempt_i&#34;,
        &#34;SHOW_EMAILS_ONLY&#34;: &#34;true&#34;, &#34;SUPPORT_SOLR_ONLY&#34;: &#34;true&#34;, &#34;ENABLE_FOLDERBROWSE&#34;: &#34;off&#34;,
        &#34;ENABLE_MIXEDVIEWSEARCH&#34;: &#34;true&#34;}

    FIND_MBX_DEFAULT_FACET = {&#34;MODIFIEDTIME&#34;, &#34;SIZEINKB&#34;, &#34;FOLDER_PATH&#34;}

    SEARCH_IN_RESTORE_PAYLOAD = {
        &#34;mode&#34;: 4,
        &#34;advSearchGrp&#34;: {
            &#34;commonFilter&#34;: [
                {
                    &#34;filter&#34;: {
                        &#34;interFilterOP&#34;: 2,
                        &#34;filters&#34;: [
                            {
                                &#34;field&#34;: &#34;CISTATE&#34;,
                                &#34;intraFieldOp&#34;: 0,
                                &#34;fieldValues&#34;: {
                                    &#34;values&#34;: [&#34;1&#34;]
                                }
                            },
                            {
                                &#34;field&#34;: &#34;IS_VISIBLE&#34;,
                                &#34;intraFieldOp&#34;: 0,
                                &#34;fieldValues&#34;: {
                                    &#34;values&#34;: [&#34;true&#34;]
                                }
                            }
                        ]
                    }
                }
            ],
            &#34;fileFilter&#34;: [],
            &#34;emailFilter&#34;: [
                {
                    &#34;interGroupOP&#34;: 2,
                    &#34;filter&#34;: {
                        &#34;interFilterOP&#34;: 2,
                        &#34;filters&#34;: [
                            {
                                &#34;field&#34;: &#34;IS_VISIBLE&#34;,
                                &#34;intraFieldOp&#34;: 0,
                                &#34;fieldValues&#34;: {
                                    &#34;values&#34;: [&#34;true&#34;]
                                }
                            },
                            {
                                &#34;field&#34;: &#34;EXCH_VALID_AFID&#34;,
                                &#34;intraFieldOp&#34;: 0,
                                &#34;fieldValues&#34;: {
                                    &#34;values&#34;: [&#34;true&#34;]
                                }
                            },
                            {
                                &#34;field&#34;: &#34;DATA_TYPE&#34;,
                                &#34;intraFieldOp&#34;: 0,
                                &#34;fieldValues&#34;: {
                                    &#34;values&#34;: [&#34;2&#34;]
                                }
                            }
                        ]
                    }
                }
            ],
            &#34;galaxyFilter&#34;: [
                {
                    &#34;appIdList&#34;: []
                }
            ],
            &#34;cvSearchKeyword&#34;: {
                &#34;isExactWordsOptionSelected&#34;: False,
                &#34;keyword&#34;: &#34;&#34;
            }
        },
        &#34;searchProcessingInfo&#34;: {
            &#34;resultOffset&#34;: 0,
            &#34;pageSize&#34;: 15,
            &#34;queryParams&#34;: [
                {
                    &#34;param&#34;: &#34;RESPONSE_FIELD_LIST&#34;,
                    &#34;value&#34;: &#34;COMMCELLNO,AFILEID,AFILEOFFSET,BACKUPTIME,SIZEINKB,MODIFIEDTIME,CONTENTID,LINKS,EMAIL_SUBJECT,FROM_DISPLAY,TO_DISPLAY,FOLDER,EMAIL_IMPORTANCE,CUSTODIAN,OWNER,CVSTUB,DATA_TYPE,PARENT_GUID,CISTATE,EMAIL_ATTACHMENTS,HAS_ATTACHMENT,EMAIL_MODIFIED_TIME,IS_VISIBLE,EXCH_MIGRATED,EXCH_MBX_PROPERTY_TYPE,SRC_APP_GUID,ExtractAttempt_i&#34;
                },
                {
                    &#34;param&#34;: &#34;DO_NOT_AUDIT&#34;,
                    &#34;value&#34;: &#34;false&#34;
                },
                {
                    &#34;param&#34;: &#34;ENABLE_HIGHLIGHTING&#34;,
                    &#34;value&#34;: &#34;false&#34;
                },
                {
                    &#34;param&#34;: &#34;SHOW_EMAILS_ONLY&#34;,
                    &#34;value&#34;: &#34;true&#34;
                },
                {
                    &#34;param&#34;: &#34;ENABLE_DEFAULTFACETS&#34;,
                    &#34;value&#34;: &#34;false&#34;
                },
                {
                    &#34;param&#34;: &#34;SUPPORT_SOLR_ONLY&#34;,
                    &#34;value&#34;: &#34;true&#34;
                },
                {
                    &#34;param&#34;: &#34;ENABLE_FOLDERBROWSE&#34;,
                    &#34;value&#34;: &#34;off&#34;
                },
                {
                    &#34;param&#34;: &#34;ENABLE_MIXEDVIEWSEARCH&#34;,
                    &#34;value&#34;: &#34;true&#34;
                },
                {
                    &#34;param&#34;: &#34;ENABLE_NAVIGATION&#34;,
                    &#34;value&#34;: &#34;on&#34;
                }
            ],
            &#34;sortParams&#34;: [
                {
                    &#34;sortDirection&#34;: 0,
                    &#34;sortField&#34;: &#34;EMAIL_SUBJECT&#34;
                }
            ]
        },
        &#34;facetRequests&#34;: {
            &#34;facetRequest&#34;: [
                {&#34;name&#34;: &#34;MODIFIEDTIME&#34;},
                {&#34;name&#34;: &#34;SIZEINKB&#34;},
                {&#34;name&#34;: &#34;CUSTODIAN&#34;},
                {&#34;name&#34;: &#34;HAS_ATTACHMENT&#34;},
                {&#34;name&#34;: &#34;FOLDER_PATH&#34;}
            ]
        }
    }</code></pre>
</details>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.subclients.exchange.constants.ExchangeConstants.ADVANCED_SEARCH_GROUP"><code class="name">var <span class="ident">ADVANCED_SEARCH_GROUP</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.exchange.constants.ExchangeConstants.FIND_MAILBOX_REQUEST_DATA"><code class="name">var <span class="ident">FIND_MAILBOX_REQUEST_DATA</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.exchange.constants.ExchangeConstants.FIND_MBX_DEFAULT_FACET"><code class="name">var <span class="ident">FIND_MBX_DEFAULT_FACET</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.exchange.constants.ExchangeConstants.FIND_MBX_QUERY_DEFAULT_PARAMS"><code class="name">var <span class="ident">FIND_MBX_QUERY_DEFAULT_PARAMS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.exchange.constants.ExchangeConstants.SEAARCH_PROCESSING_INFO"><code class="name">var <span class="ident">SEAARCH_PROCESSING_INFO</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.exchange.constants.ExchangeConstants.SEARCH_IN_RESTORE_PAYLOAD"><code class="name">var <span class="ident">SEARCH_IN_RESTORE_PAYLOAD</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.subclients.exchange.constants.JobOptionIntegers"><code class="flex name class">
<span>class <span class="ident">JobOptionIntegers</span></span>
<span>(</span><span>value, names=None, *, module=None, qualname=None, type=None, start=1)</span>
</code></dt>
<dd>
<div class="desc"><p>Enum to specify job option integers</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/constants.py#L291-L297" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class JobOptionIntegers(Enum):
    &#34;&#34;&#34;Enum to specify job option integers&#34;&#34;&#34;
    EXCHANGE_RESTORE_CHOICE = 1
    EXCHANGE_RESTORE_DRIVE = 1
    RECOVER_STUBS = 0
    STUB_REPORTING = 1
    UPDATE_RECALL_LINK = 2</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li>enum.Enum</li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.subclients.exchange.constants.JobOptionIntegers.EXCHANGE_RESTORE_CHOICE"><code class="name">var <span class="ident">EXCHANGE_RESTORE_CHOICE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.exchange.constants.JobOptionIntegers.EXCHANGE_RESTORE_DRIVE"><code class="name">var <span class="ident">EXCHANGE_RESTORE_DRIVE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.exchange.constants.JobOptionIntegers.RECOVER_STUBS"><code class="name">var <span class="ident">RECOVER_STUBS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.exchange.constants.JobOptionIntegers.STUB_REPORTING"><code class="name">var <span class="ident">STUB_REPORTING</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.exchange.constants.JobOptionIntegers.UPDATE_RECALL_LINK"><code class="name">var <span class="ident">UPDATE_RECALL_LINK</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.subclients.exchange.constants.JobOptionKeys"><code class="flex name class">
<span>class <span class="ident">JobOptionKeys</span></span>
<span>(</span><span>value, names=None, *, module=None, qualname=None, type=None, start=1)</span>
</code></dt>
<dd>
<div class="desc"><p>Enum to specify job option keys</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/constants.py#L261-L278" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class JobOptionKeys(Enum):
    &#34;&#34;&#34;Enum to specify job option keys&#34;&#34;&#34;
    RESTORE_DESTINATION = &#34;Restore destination&#34;
    DESTINATION = &#34;Destination&#34;
    IF_MESSAGE_EXISTS = &#34;If the message exists&#34;
    INCLUDE_DELETED_ITEMS = &#34;Include deleted items&#34;
    MATCH_DESTINATION_USER = &#34;Match destination user based on the email address&#34;
    STUB_REHYDRATION = &#34;Stub rehydration&#34;
    STUB_REHYDRATION_OPTION = &#34;Stub rehydration option&#34;
    MAILBOX_LEVEL_REPORTING = &#34;Mailbox level reporting&#34;
    EMAIL_LEVEL_REPORTING = &#34;Email level reporting&#34;
    OLD_RECALL_LINK = &#34;Old recall link&#34;
    NEW_RECALL_LINK = &#34;New recall link&#34;
    EXCHANGE_RESTORE_CHOICE = &#34;exchangeRestoreChoice&#34;
    EXCHANGE_RESTORE_DRIVE = &#34;exchangeRestoreDrive&#34;
    IS_JOURNAL_REPORT = &#34;isJournalReport&#34;
    PST_FILE_PATH = &#34;pstFilePath&#34;
    TARGET_MAILBOX = &#34;stubRehydration&#34;</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li>enum.Enum</li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.subclients.exchange.constants.JobOptionKeys.DESTINATION"><code class="name">var <span class="ident">DESTINATION</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.exchange.constants.JobOptionKeys.EMAIL_LEVEL_REPORTING"><code class="name">var <span class="ident">EMAIL_LEVEL_REPORTING</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.exchange.constants.JobOptionKeys.EXCHANGE_RESTORE_CHOICE"><code class="name">var <span class="ident">EXCHANGE_RESTORE_CHOICE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.exchange.constants.JobOptionKeys.EXCHANGE_RESTORE_DRIVE"><code class="name">var <span class="ident">EXCHANGE_RESTORE_DRIVE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.exchange.constants.JobOptionKeys.IF_MESSAGE_EXISTS"><code class="name">var <span class="ident">IF_MESSAGE_EXISTS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.exchange.constants.JobOptionKeys.INCLUDE_DELETED_ITEMS"><code class="name">var <span class="ident">INCLUDE_DELETED_ITEMS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.exchange.constants.JobOptionKeys.IS_JOURNAL_REPORT"><code class="name">var <span class="ident">IS_JOURNAL_REPORT</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.exchange.constants.JobOptionKeys.MAILBOX_LEVEL_REPORTING"><code class="name">var <span class="ident">MAILBOX_LEVEL_REPORTING</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.exchange.constants.JobOptionKeys.MATCH_DESTINATION_USER"><code class="name">var <span class="ident">MATCH_DESTINATION_USER</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.exchange.constants.JobOptionKeys.NEW_RECALL_LINK"><code class="name">var <span class="ident">NEW_RECALL_LINK</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.exchange.constants.JobOptionKeys.OLD_RECALL_LINK"><code class="name">var <span class="ident">OLD_RECALL_LINK</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.exchange.constants.JobOptionKeys.PST_FILE_PATH"><code class="name">var <span class="ident">PST_FILE_PATH</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.exchange.constants.JobOptionKeys.RESTORE_DESTINATION"><code class="name">var <span class="ident">RESTORE_DESTINATION</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.exchange.constants.JobOptionKeys.STUB_REHYDRATION"><code class="name">var <span class="ident">STUB_REHYDRATION</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.exchange.constants.JobOptionKeys.STUB_REHYDRATION_OPTION"><code class="name">var <span class="ident">STUB_REHYDRATION_OPTION</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.exchange.constants.JobOptionKeys.TARGET_MAILBOX"><code class="name">var <span class="ident">TARGET_MAILBOX</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
<dt id="cvpysdk.subclients.exchange.constants.JobOptionValues"><code class="flex name class">
<span>class <span class="ident">JobOptionValues</span></span>
<span>(</span><span>value, names=None, *, module=None, qualname=None, type=None, start=1)</span>
</code></dt>
<dd>
<div class="desc"><p>Enum to specify job option values</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/constants.py#L280-L289" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class JobOptionValues(Enum):
    &#34;&#34;&#34;Enum to specify job option values&#34;&#34;&#34;
    SKIP = &#34;Skip&#34;
    DISABLED = &#34;Disabled&#34;
    ENABLED = &#34;Enabled&#34;
    RECOVER_STUBS = &#34;Recover stubs&#34;
    STUB_REPORTING = &#34;Stub reporting&#34;
    UPDATE_RECALL_LINK = &#34;Update recall link&#34;
    EXCHANGE = &#34;Exchange&#34;
    ORIGINAL_LOCATION = &#34;Original Location&#34;</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li>enum.Enum</li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.subclients.exchange.constants.JobOptionValues.DISABLED"><code class="name">var <span class="ident">DISABLED</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.exchange.constants.JobOptionValues.ENABLED"><code class="name">var <span class="ident">ENABLED</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.exchange.constants.JobOptionValues.EXCHANGE"><code class="name">var <span class="ident">EXCHANGE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.exchange.constants.JobOptionValues.ORIGINAL_LOCATION"><code class="name">var <span class="ident">ORIGINAL_LOCATION</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.exchange.constants.JobOptionValues.RECOVER_STUBS"><code class="name">var <span class="ident">RECOVER_STUBS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.exchange.constants.JobOptionValues.SKIP"><code class="name">var <span class="ident">SKIP</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.exchange.constants.JobOptionValues.STUB_REPORTING"><code class="name">var <span class="ident">STUB_REPORTING</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.exchange.constants.JobOptionValues.UPDATE_RECALL_LINK"><code class="name">var <span class="ident">UPDATE_RECALL_LINK</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.subclients.exchange" href="index.html">cvpysdk.subclients.exchange</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.subclients.exchange.constants.ExchangeConstants" href="#cvpysdk.subclients.exchange.constants.ExchangeConstants">ExchangeConstants</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.subclients.exchange.constants.ExchangeConstants.ADVANCED_SEARCH_GROUP" href="#cvpysdk.subclients.exchange.constants.ExchangeConstants.ADVANCED_SEARCH_GROUP">ADVANCED_SEARCH_GROUP</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.constants.ExchangeConstants.FIND_MAILBOX_REQUEST_DATA" href="#cvpysdk.subclients.exchange.constants.ExchangeConstants.FIND_MAILBOX_REQUEST_DATA">FIND_MAILBOX_REQUEST_DATA</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.constants.ExchangeConstants.FIND_MBX_DEFAULT_FACET" href="#cvpysdk.subclients.exchange.constants.ExchangeConstants.FIND_MBX_DEFAULT_FACET">FIND_MBX_DEFAULT_FACET</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.constants.ExchangeConstants.FIND_MBX_QUERY_DEFAULT_PARAMS" href="#cvpysdk.subclients.exchange.constants.ExchangeConstants.FIND_MBX_QUERY_DEFAULT_PARAMS">FIND_MBX_QUERY_DEFAULT_PARAMS</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.constants.ExchangeConstants.SEAARCH_PROCESSING_INFO" href="#cvpysdk.subclients.exchange.constants.ExchangeConstants.SEAARCH_PROCESSING_INFO">SEAARCH_PROCESSING_INFO</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.constants.ExchangeConstants.SEARCH_IN_RESTORE_PAYLOAD" href="#cvpysdk.subclients.exchange.constants.ExchangeConstants.SEARCH_IN_RESTORE_PAYLOAD">SEARCH_IN_RESTORE_PAYLOAD</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.subclients.exchange.constants.JobOptionIntegers" href="#cvpysdk.subclients.exchange.constants.JobOptionIntegers">JobOptionIntegers</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.subclients.exchange.constants.JobOptionIntegers.EXCHANGE_RESTORE_CHOICE" href="#cvpysdk.subclients.exchange.constants.JobOptionIntegers.EXCHANGE_RESTORE_CHOICE">EXCHANGE_RESTORE_CHOICE</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.constants.JobOptionIntegers.EXCHANGE_RESTORE_DRIVE" href="#cvpysdk.subclients.exchange.constants.JobOptionIntegers.EXCHANGE_RESTORE_DRIVE">EXCHANGE_RESTORE_DRIVE</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.constants.JobOptionIntegers.RECOVER_STUBS" href="#cvpysdk.subclients.exchange.constants.JobOptionIntegers.RECOVER_STUBS">RECOVER_STUBS</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.constants.JobOptionIntegers.STUB_REPORTING" href="#cvpysdk.subclients.exchange.constants.JobOptionIntegers.STUB_REPORTING">STUB_REPORTING</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.constants.JobOptionIntegers.UPDATE_RECALL_LINK" href="#cvpysdk.subclients.exchange.constants.JobOptionIntegers.UPDATE_RECALL_LINK">UPDATE_RECALL_LINK</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.subclients.exchange.constants.JobOptionKeys" href="#cvpysdk.subclients.exchange.constants.JobOptionKeys">JobOptionKeys</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.subclients.exchange.constants.JobOptionKeys.DESTINATION" href="#cvpysdk.subclients.exchange.constants.JobOptionKeys.DESTINATION">DESTINATION</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.constants.JobOptionKeys.EMAIL_LEVEL_REPORTING" href="#cvpysdk.subclients.exchange.constants.JobOptionKeys.EMAIL_LEVEL_REPORTING">EMAIL_LEVEL_REPORTING</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.constants.JobOptionKeys.EXCHANGE_RESTORE_CHOICE" href="#cvpysdk.subclients.exchange.constants.JobOptionKeys.EXCHANGE_RESTORE_CHOICE">EXCHANGE_RESTORE_CHOICE</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.constants.JobOptionKeys.EXCHANGE_RESTORE_DRIVE" href="#cvpysdk.subclients.exchange.constants.JobOptionKeys.EXCHANGE_RESTORE_DRIVE">EXCHANGE_RESTORE_DRIVE</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.constants.JobOptionKeys.IF_MESSAGE_EXISTS" href="#cvpysdk.subclients.exchange.constants.JobOptionKeys.IF_MESSAGE_EXISTS">IF_MESSAGE_EXISTS</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.constants.JobOptionKeys.INCLUDE_DELETED_ITEMS" href="#cvpysdk.subclients.exchange.constants.JobOptionKeys.INCLUDE_DELETED_ITEMS">INCLUDE_DELETED_ITEMS</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.constants.JobOptionKeys.IS_JOURNAL_REPORT" href="#cvpysdk.subclients.exchange.constants.JobOptionKeys.IS_JOURNAL_REPORT">IS_JOURNAL_REPORT</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.constants.JobOptionKeys.MAILBOX_LEVEL_REPORTING" href="#cvpysdk.subclients.exchange.constants.JobOptionKeys.MAILBOX_LEVEL_REPORTING">MAILBOX_LEVEL_REPORTING</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.constants.JobOptionKeys.MATCH_DESTINATION_USER" href="#cvpysdk.subclients.exchange.constants.JobOptionKeys.MATCH_DESTINATION_USER">MATCH_DESTINATION_USER</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.constants.JobOptionKeys.NEW_RECALL_LINK" href="#cvpysdk.subclients.exchange.constants.JobOptionKeys.NEW_RECALL_LINK">NEW_RECALL_LINK</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.constants.JobOptionKeys.OLD_RECALL_LINK" href="#cvpysdk.subclients.exchange.constants.JobOptionKeys.OLD_RECALL_LINK">OLD_RECALL_LINK</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.constants.JobOptionKeys.PST_FILE_PATH" href="#cvpysdk.subclients.exchange.constants.JobOptionKeys.PST_FILE_PATH">PST_FILE_PATH</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.constants.JobOptionKeys.RESTORE_DESTINATION" href="#cvpysdk.subclients.exchange.constants.JobOptionKeys.RESTORE_DESTINATION">RESTORE_DESTINATION</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.constants.JobOptionKeys.STUB_REHYDRATION" href="#cvpysdk.subclients.exchange.constants.JobOptionKeys.STUB_REHYDRATION">STUB_REHYDRATION</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.constants.JobOptionKeys.STUB_REHYDRATION_OPTION" href="#cvpysdk.subclients.exchange.constants.JobOptionKeys.STUB_REHYDRATION_OPTION">STUB_REHYDRATION_OPTION</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.constants.JobOptionKeys.TARGET_MAILBOX" href="#cvpysdk.subclients.exchange.constants.JobOptionKeys.TARGET_MAILBOX">TARGET_MAILBOX</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.subclients.exchange.constants.JobOptionValues" href="#cvpysdk.subclients.exchange.constants.JobOptionValues">JobOptionValues</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.subclients.exchange.constants.JobOptionValues.DISABLED" href="#cvpysdk.subclients.exchange.constants.JobOptionValues.DISABLED">DISABLED</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.constants.JobOptionValues.ENABLED" href="#cvpysdk.subclients.exchange.constants.JobOptionValues.ENABLED">ENABLED</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.constants.JobOptionValues.EXCHANGE" href="#cvpysdk.subclients.exchange.constants.JobOptionValues.EXCHANGE">EXCHANGE</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.constants.JobOptionValues.ORIGINAL_LOCATION" href="#cvpysdk.subclients.exchange.constants.JobOptionValues.ORIGINAL_LOCATION">ORIGINAL_LOCATION</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.constants.JobOptionValues.RECOVER_STUBS" href="#cvpysdk.subclients.exchange.constants.JobOptionValues.RECOVER_STUBS">RECOVER_STUBS</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.constants.JobOptionValues.SKIP" href="#cvpysdk.subclients.exchange.constants.JobOptionValues.SKIP">SKIP</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.constants.JobOptionValues.STUB_REPORTING" href="#cvpysdk.subclients.exchange.constants.JobOptionValues.STUB_REPORTING">STUB_REPORTING</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.constants.JobOptionValues.UPDATE_RECALL_LINK" href="#cvpysdk.subclients.exchange.constants.JobOptionValues.UPDATE_RECALL_LINK">UPDATE_RECALL_LINK</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>