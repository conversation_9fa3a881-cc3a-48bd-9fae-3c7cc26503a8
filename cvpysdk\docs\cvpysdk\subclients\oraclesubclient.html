<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.subclients.oraclesubclient API documentation</title>
<meta name="description" content="File for operating on a Oracle Subclient …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.subclients.oraclesubclient</code></h1>
</header>
<section id="section-intro">
<p>File for operating on a Oracle Subclient</p>
<p>OracleSubclient is the only class defined in this file.</p>
<p>OracleSubclient: Derived class from DatabaseSubclient Base class, representing an Oracle subclient,
and to perform operations on that subclient</p>
<h2 id="oraclesubclient">Oraclesubclient</h2>
<p><strong>init</strong>()
&ndash;
constructor for the class</p>
<p>_get_subclient_properties()
&ndash;
gets the subclient related properties of
Oracle subclient</p>
<p>_get_subclient_properties_json()
&ndash;
returns subclient property json for oracle</p>
<p>data()
&ndash;
Getter and Setter for enabling data mode in oracle</p>
<p>selective_online_full()
&ndash;
Getter and Setter to enable selective online option</p>
<p>set_backupcopy_interface()
&ndash;
Setter for the backupcopy interface</p>
<p>data_stream()
&ndash;
Getter and Setter for data stream</p>
<p>backup()
&ndash;
Performs backup database</p>
<p>restore()
&ndash;
Performs restore databases</p>
<p>restore_in_place()
&ndash;
Performs restore for oracle logical dump</p>
<p>backup_archive_log()
&ndash;
Getter ans Setter for enaling/disabling
archive log mode</p>
<p>archive_delete()
&ndash;
Getter and Setter for archive delete</p>
<p>archive_files_per_bfs()
&ndash;
Getter and Setter for archive files per BFS</p>
<p>oracle_tag()
&ndash;
Getter and Setter for oracle tag</p>
<p>skip_offline()
&ndash;
Getter and Setter for skip offline option</p>
<p>skip_read_only()
&ndash;
Getter and Setter for skip read only option</p>
<p>skip_inaccessible()
&ndash;
Getter and Setter for skip inaccessible option</p>
<p>data_sp()
&ndash;
Getters and setters for data storage policy</p>
<p>_get_oracle_restore_json
&ndash;
To get restore JSON for an oracle instance</p>
<p>_oracle_backup_json
&ndash;
Get backup JSON for oracle instance</p>
<p>is_snapenabled()
&ndash;
Check if intellisnap has been enabled in the subclient</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/oraclesubclient.py#L1-L704" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;
File for operating on a Oracle Subclient

OracleSubclient is the only class defined in this file.

OracleSubclient: Derived class from DatabaseSubclient Base class, representing an Oracle subclient,
                        and to perform operations on that subclient

OracleSubclient:
    __init__()                          --  constructor for the class

    _get_subclient_properties()         --  gets the subclient related properties of
                                            Oracle subclient

    _get_subclient_properties_json()    --  returns subclient property json for oracle

    data()                              --  Getter and Setter for enabling data mode in oracle

    selective_online_full()             --  Getter and Setter to enable selective online option

    set_backupcopy_interface()          --  Setter for the backupcopy interface

    data_stream()                       --  Getter and Setter for data stream

    backup()                            --  Performs backup database

    restore()                           --  Performs restore databases

    restore_in_place()                  --  Performs restore for oracle logical dump

    backup_archive_log()                --  Getter ans Setter for enaling/disabling
                                            archive log mode

    archive_delete()                    --  Getter and Setter for archive delete

    archive_files_per_bfs()             --  Getter and Setter for archive files per BFS

    oracle_tag()                        --  Getter and Setter for oracle tag

    skip_offline()                      --  Getter and Setter for skip offline option

    skip_read_only()                    --  Getter and Setter for skip read only option

    skip_inaccessible()                 --  Getter and Setter for skip inaccessible option

    data_sp()                           --  Getters and setters for data storage policy

    _get_oracle_restore_json            --  To get restore JSON for an oracle instance

    _oracle_backup_json                 --  Get backup JSON for oracle instance

    is_snapenabled()                    --  Check if intellisnap has been enabled in the subclient

&#34;&#34;&#34;
from __future__ import unicode_literals
from .dbsubclient import DatabaseSubclient
from ..exception import SDKException
from ..constants import InstanceBackupType


class OracleSubclient(DatabaseSubclient):
    &#34;&#34;&#34;
    OracleSubclient is a class to work on Oracle subclients

    &#34;&#34;&#34;

    def __init__(self, backupset_object, subclient_name, subclient_id=None):
        &#34;&#34;&#34;
        Constructor for the class

        Args:
            backupset_object  (object)  -- instance of the Backupset class
            subclient_name    (str)     -- name of the subclient
            subclient_id      (str)     -- id of the subclient
        &#34;&#34;&#34;
        super(OracleSubclient, self).__init__(
            backupset_object, subclient_name, subclient_id)
        self._get_subclient_properties()
        #self._oracle_properties = {}

    def _oracle_backup_json(
            self,
            backup_level=&#34;full&#34;,
            schedule_pattern=None,
            options=None):
        &#34;&#34;&#34;Runs a backup job for the subclient of the level specified.

            Args:
                backup_level            (str)   --  level of backup the user wish to run
                                                    Full / Incremental

                schedule_pattern (dict) -- scheduling options to be included for the task

                        Please refer schedules.schedulePattern.createSchedule()
                                                                    doc for the types of Jsons

                options          (dict) --  dictionary containing other oracle options

            Returns:
                dict    -- dict containing request JSON

        &#34;&#34;&#34;
        oracle_options = {
            &#34;oracleOptions&#34;: {}
        }
        if options is not None:
            oracle_options.update(options)
        request_json = self._backup_json(
            backup_level,
            False,
            &#34;BEFORE_SYNTH&#34;,
            schedule_pattern=schedule_pattern
        )

        # Add option to run RMAN cumulatives
        oracle_options[&#34;oracleOptions&#34;][&#34;cumulative&#34;] = True

        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;backupOpts&#34;].update(
            oracle_options
        )
        return request_json

    def _get_subclient_properties(self):
        &#34;&#34;&#34;Gets the subclient  related properties of Oracle subclient.
        &#34;&#34;&#34;
        if not bool(self._subclient_properties):
            super(OracleSubclient, self)._get_subclient_properties()
        self._oracle_subclient_properties = self._subclient_properties.get(&#34;oracleSubclientProp&#34;)

    def _get_subclient_properties_json(self):
        &#34;&#34;&#34;returns subclient property json for oracle
           Returns:
                dict - all subclient properties put inside a dict
        &#34;&#34;&#34;
        subclient_json = {
            &#34;subClientProperties&#34;:
                {
                    &#34;proxyClient&#34;: self._proxyClient,
                    &#34;subClientEntity&#34;: self._subClientEntity,
                    &#34;commonProperties&#34;: self._commonProperties,
                    &#34;oracleSubclientProp&#34;: self._oracle_subclient_properties,
                }
        }
        return subclient_json

    def set_prop_for_orcle_subclient(self, storage_policy, snap_engine=None, archivefilebfs=32):
        &#34;&#34;&#34;Updates the subclient properties.

            Args:

                storage_policy      (str)   --  name of the storage policy to be associated
                with the subclient

                snap_engine         (str)   --  Snap Engine to be set for subclient (optional)

                    default: None

            Raises:
                SDKException:
                    if storage policy argument is not of type string

                    if failed to update subclient

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        if not archivefilebfs and (self.archive_files_per_bfs == &#39;0&#39;):
            self.archive_files_per_bfs = 32
        else:
            self.archive_files_per_bfs = archivefilebfs

        self.data_stream = 2

        self.storage_policy = storage_policy
        if snap_engine:
            self.enable_intelli_snap(snap_engine)

    @property
    def data(self):
        &#34;&#34;&#34;
        Getter to fetch if data enabled in oracle subclient or not

            Returns:
                bool     --  True if data is enabled on the subclient. Else False

        &#34;&#34;&#34;
        return self._oracle_subclient_properties.get(&#34;data&#34;)

    @data.setter
    def data(self, data):
        &#34;&#34;&#34;
        Enables  data for oracle subclient

            Args:
                data      (bool) --   True if data to be enabled on the subclient. Else False
        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_oracle_subclient_properties[&#39;data&#39;]&#34;, data)

    @property
    def backup_archive_log(self):
        &#34;&#34;&#34;
        Getter to fetch if archive log backup enabled or not

            Returns:
                    bool     --  True if archivelog is enabled on the subclient. Else False

        &#34;&#34;&#34;
        return self._oracle_subclient_properties.get(&#34;backupArchiveLog&#34;)

    @backup_archive_log.setter
    def backup_archive_log(self, backup_archive_log):
        &#34;&#34;&#34;
        Setter to enable backup archive log in oracle subclient

            Args:
                backup_archive_log    (bool)    --  True if archive log to be enabled
                                                    on the subclient.Else False
        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_oracle_subclient_properties[&#39;backupArchiveLog&#39;]&#34;, backup_archive_log)

    @property
    def archive_delete(self):
        &#34;&#34;&#34;
        Getter to fetch if archive delete is enabled or not

            Returns:
                    bool     --  True if archive delete is enabled on the subclient, Else False

        &#34;&#34;&#34;
        return self._oracle_subclient_properties.get(&#34;archiveDelete&#34;)

    @archive_delete.setter
    def archive_delete(self, archive_delete):
        &#34;&#34;&#34;
        Setter to enable backup delete in oracle subclient

            Args:
                archive_delete    (bool)    --  True if archive delete to be enabled
                                                on the subclient, Else False
        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_oracle_subclient_properties[&#39;archiveDelete&#39;]&#34;, archive_delete)

    @property
    def selective_online_full(self):
        &#34;&#34;&#34;
        Getter to fetch if selective online full enabled or not

            Returns:
                    bool     --  True if selective online is enabled on the subclient. Else False

        &#34;&#34;&#34;
        return self._oracle_subclient_properties.get(&#34;selectiveOnlineFull&#34;)

    @selective_online_full.setter
    def selective_online_full(self, selective_online_full):
        &#34;&#34;&#34;
        Setter to enable backup archive log in oracle subclient

            Args:
                selective_online_full    (bool)    --  True if selective online to be enabled
                                                        on the subclient.Else False
        &#34;&#34;&#34;
        self.backup_archive_log = True
        self._set_subclient_properties(
            &#34;_oracle_subclient_properties[&#39;selectiveOnlineFull&#39;]&#34;, selective_online_full)

    @property
    def archive_files_per_bfs(self):
        &#34;&#34;&#34;
        Getter to fetch archive files per BFS

            Returns:
                    (int)    --     value for archive files per BFS
        &#34;&#34;&#34;
        return self._oracle_subclient_properties.get(&#34;archiveFilesPerBFS&#34;)

    @archive_files_per_bfs.setter
    def archive_files_per_bfs(self, archive_files_per_bfs=32):
        &#34;&#34;&#34;
        Setter to set parameter  archive files per BFS

            Args:
               archive_files_per_bfs    (int)    --     value for archive files per BFS
                                                        default : 32
        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_oracle_subclient_properties[&#39;archiveFilesPerBFS&#39;]&#34;, archive_files_per_bfs)

    @property
    def oracle_tag(self):
        &#34;&#34;&#34;
        Getter to fetch oracle tag

            Returns:
                    (int)    --     value for oracle tag
        &#34;&#34;&#34;
        return self._oracle_subclient_properties.get(&#34;oracleTag&#34;)

    @oracle_tag.setter
    def oracle_tag(self, oracle_tag):
        &#34;&#34;&#34;
        Setter to set parameter for oracle tag

            Args:
               oracle_tag    (int)    --     value for oracle tag
        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_oracle_subclient_properties[&#39;oracleTag&#39;]&#34;, oracle_tag)

    @property
    def skip_offline(self):
        &#34;&#34;&#34;
        Getter to fetch if skip offline is enabled or not

            Returns:
                    (bool)    --     True if the option is enabled, False if it is disabled
        &#34;&#34;&#34;
        return self._oracle_subclient_properties.get(&#34;skipOffline&#34;)

    @skip_offline.setter
    def skip_offline(self, skip_offline=False):
        &#34;&#34;&#34;
        Setter to set skip offline option

            Args:
               skip_offline    (bool)    --     True to enable the skip offline option,
                                                False to disable it.
                                                default=False
        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_oracle_subclient_properties[&#39;skipOffline&#39;]&#34;, skip_offline)

    @property
    def skip_read_only(self):
        &#34;&#34;&#34;
        Getter to fetch if skip read only is enabled or not

            Returns:
                    (bool)    --     True if the option is enabled, False if it is disabled
        &#34;&#34;&#34;
        return self._oracle_subclient_properties.get(&#34;skipReadOnly&#34;)

    @skip_read_only.setter
    def skip_read_only(self, skip_read_only=False):
        &#34;&#34;&#34;
        Setter to set skip read only option

            Args:
               skip_read_only    (bool)    --     True to enable the skip read only option,
                                                  False to disable it.
                                                  default=False

        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_oracle_subclient_properties[&#39;skipReadOnly&#39;]&#34;, skip_read_only)

    @property
    def skip_inaccessible(self):
        &#34;&#34;&#34;
        Getter to fetch if skip inaccessible is enabled or not

            Returns:
                    (bool)    --     True if the option is enabled, False if it is disabled
        &#34;&#34;&#34;
        return self._oracle_subclient_properties.get(&#34;skipInaccessible&#34;)

    @skip_inaccessible.setter
    def skip_inaccessible(self, skip_inaccessible=False):
        &#34;&#34;&#34;
        Setter to set skip inaccessible option

            Args:
               skip_inaccessible    (bool)    --    True to enable the skip inaccessible option,
                                                    False to disable it.
                                                    default=False

        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_oracle_subclient_properties[&#39;skipInaccessible&#39;]&#34;, skip_inaccessible)

    @property
    def data_stream(self):
        &#34;&#34;&#34;
        Getter to fetch data stream count

            Returns:
                    int     --  data backup stream count at subclient level

        &#34;&#34;&#34;
        return self._oracle_subclient_properties.get(&#34;dataThresholdStreams&#34;)

    @data_stream.setter
    def data_stream(self, data_stream=1):
        &#34;&#34;&#34;
        Setter to set data backup stream count at subclient level

            Args:
                data_stream    (int)    --  data backup stream count at subclient level
                                            default = 1
        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_oracle_subclient_properties[&#39;dataThresholdStreams&#39;]&#34;, data_stream)

    @property
    def data_sp(self):
        &#34;&#34;&#34;
        Getter for data storage policy

        Returns:
            string - string representing data storage policy
        &#34;&#34;&#34;
        return self._commonProperties[&#39;storageDevice&#39;][
            &#39;dataBackupStoragePolicy&#39;][&#39;storagePolicyName&#39;]

    @property
    def is_table_browse_enabled(self):
        &#34;&#34;&#34;
        Getter to check whether the subclient has table browse enabled

        Returns:
            Bool - True if table browse is enabled on the subclient. Else False
        &#34;&#34;&#34;
        # return self._oracle_subclient_properties[&#39;enableTableBrowse&#39;]
        return self._subclient_properties[&#39;oracleSubclientProp&#39;][&#39;enableTableBrowse&#39;]

    @property
    def is_snapenabled(self):
        &#34;&#34;&#34;
        Getter to check whether the subclient has snap enabled

        Returns:
            Bool - True if snap is enabled on the subclient. Else False

        &#34;&#34;&#34;
        return self._subclient_properties[&#39;commonProperties&#39;][&#39;snapCopyInfo&#39;][&#39;isSnapBackupEnabled&#39;]

    def enable_table_browse(self):
        &#34;&#34;&#34;
        Enables Table Browse for the subclient.

        Raises:
            SDKException:
                if failed to enable tablebrowse for subclient

        &#34;&#34;&#34;

        self._set_subclient_properties(&#34;_oracle_subclient_properties[&#39;enableTableBrowse&#39;]&#34;, True)

    def disable_table_browse(self):
        &#34;&#34;&#34;Disables Table Browse for the subclient.
            Raises:
                SDKException:
                        if failed to disable tablebrowse for subclient
        &#34;&#34;&#34;

        self._set_subclient_properties(
            &#34;_oracle_subclient_properties[&#39;enableTableBrowse&#39;]&#34;, False
        )

    def set_backupcopy_interface(self, interface):
        &#34;&#34;&#34;Sets the backup copy interafce for the subclient.

            Args:
                interface (str) -- type of the backup copy interface

            Raises:
                SDKException:
                    if failed to disable intelli snap for subclient
        &#34;&#34;&#34;

        if interface in self._backupcopy_interfaces:
            interface = self._backupcopy_interfaces[interface]
            self._commonProperties[&#39;snapCopyInfo&#39;][&#39;backupCopyInterface&#39;] = interface
        else:
            raise SDKException(&#34;Subclient&#34;, &#34;101&#34;)

    @property
    def find(self, *args, **kwargs):
        raise AttributeError(&#34;&#39;{0}&#39; object has no attribute &#39;{1}&#39;&#34;.format(
            self.__class__.__name__, &#39;find&#39;))

    def backup(
            self,
            backup_level=InstanceBackupType.FULL.value,
            cumulative=False,
            schedule_pattern=None,
            options=None):
        &#34;&#34;&#34;

        Args:

            backup_level            (str)   --  level of backup the user wish to run
                                                Full / Incremental
                                                    default: Full

            cumulative (Bool) -- True if cumulative backup is required
                                    default: False

            schedule_pattern (dict) -- scheduling options to be included for the task

                        Please refer schedules.schedulePattern.createSchedule()
                                                                    doc for the types of Jsons

            options          (dict) --  dictionary containing other oracle options

        Returns:
            object - instance of the Job class for this backup job if its an immediate Job

                     instance of the Schedule class for the backup job if its a scheduled Job

        Raises:
            SDKException:
                if backup level is incorrect

                if response is empty

                if response does not succeed

        &#34;&#34;&#34;
        if backup_level not in [&#39;full&#39;, &#39;incremental&#39;]:
            raise SDKException(r&#39;Subclient&#39;, r&#39;103&#39;)

        if not (cumulative or schedule_pattern or options):
            return super(OracleSubclient, self).backup(backup_level)

        if cumulative:
            backup_level = InstanceBackupType.CUMULATIVE.value
        request_json = self._oracle_backup_json(
            backup_level,
            schedule_pattern,
            options
        )
        backup_service = self._commcell_object._services[&#39;CREATE_TASK&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, backup_service, request_json
        )
        return self._process_backup_response(flag, response)

    def inline_backupcopy(self, backup_level=InstanceBackupType.FULL.value):
        &#34;&#34;&#34;Performs inline backupcopy on an oracle subclient

        Args:
            backup_level (str)  -- Level of backup. Can be full or incremental
                default: full

        Returns:
            object -- instance of Job class

        Raises:
            SDKException:
                if backup level is incorrect

                if response is empty

                if response does not succeed

        &#34;&#34;&#34;
        if backup_level not in [&#39;full&#39;, &#39;incremental&#39;]:
            raise SDKException(r&#39;Subclient&#39;, r&#39;103&#39;)

        backupcopy_level = 1

        backup_opts = {
            &#34;dataOpt&#34;: {
                &#34;skipCatalogPhaseForSnapBackup&#34;: True,
                &#34;createBackupCopyImmediately&#34;: True,
                &#34;useCatalogServer&#34;: True,
                &#34;followMountPoints&#34;: True,
                &#34;enableIndexCheckPointing&#34;: True,
                &#34;backupCopyType&#34;: 2,
                &#34;enforceTransactionLogUsage&#34;: True,
                &#34;skipConsistencyCheck&#34;: False,
                &#34;collectVMGranularRecoveryMetadataForBkpCopy&#34;: False,
                &#34;createNewIndex&#34;: False,
                &#34;verifySynthFull&#34;: True
            }
        }

        request_json = self._backup_json(
            backupcopy_level,
            incremental_backup=False,
            incremental_level=backupcopy_level,
            advanced_options=backup_opts,
            schedule_pattern=None)

        backup_service = self._commcell_object._services[&#39;CREATE_TASK&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, backup_service, request_json
        )
        return self._process_backup_response(flag, response)

    def restore(
            self,
            files=None,
            destination_client=None,
            common_options=None,
            browse_option=None,
            oracle_options=None, tag=None):
        &#34;&#34;&#34;Performs restore the entire/partial database using latest backup/backupcopy

        Args:
            files               (dict) -- dictionary containing file options
                default -- None

            destination_client  (str) -- destination client name
                default -- None

            common_options      (dict) -- common options to be passed on for restore
                default -- None

            browse_option       (dict) -- dictionary containing browse options

            oracle_options      (dict) -- dictionary containing other oracle options
                default -- By default it restores the controlfile and datafiles
                                from latest backup

            tag                 (str)  --  Type of the restore to be performed
                default:    None

                Example: {
                            &#34;resetLogs&#34;: 1,
                            &#34;switchDatabaseMode&#34;: True,
                            &#34;noCatalog&#34;: True,
                            &#34;restoreControlFile&#34;: True,
                            &#34;recover&#34;: True,
                            &#34;recoverFrom&#34;: 3,
                            &#34;restoreData&#34;: True,
                            &#34;restoreFrom&#34;: 3

                        }

        Returns:
            object -- Job containing restore details
        &#34;&#34;&#34;
        return self._backupset_object._instance_object.restore(files, destination_client,
                                                               common_options, browse_option,
                                                               oracle_options, tag)

    def restore_in_place(
            self,
            db_password=None,
            database_list=None,
            dest_client_name=None,
            dest_instance_name=None,
            destination_path=None):
        &#34;&#34;&#34;
        Method to restore the logical dump

            Args:

                db_password             (str)  -- password for oracle database

                database_list           (List) -- List of databases

                dest_client_name        (str)  -- Destination Client name

                dest_instance_name      (str)  -- Destination Instance name

                destination_path        (str)   --  destination path for restore

                    default: None

            Returns:
                object -- Job containing restore details

        &#34;&#34;&#34;
        instance_object = self._instance_object
        if dest_client_name is None:
            dest_client_name = instance_object._agent_object._client_object.client_name

        if dest_instance_name is None:
            dest_instance_name = instance_object.instance_name

        instance_object._restore_association = self._subclient_properties[&#34;subClientEntity&#34;]

        return instance_object.restore_in_place(
            db_password,
            database_list,
            dest_client_name,
            dest_instance_name,
            dest_path=destination_path)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.subclients.oraclesubclient.OracleSubclient"><code class="flex name class">
<span>class <span class="ident">OracleSubclient</span></span>
<span>(</span><span>backupset_object, subclient_name, subclient_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>OracleSubclient is a class to work on Oracle subclients</p>
<p>Constructor for the class</p>
<h2 id="args">Args</h2>
<p>backupset_object
(object)
&ndash; instance of the Backupset class
subclient_name
(str)
&ndash; name of the subclient
subclient_id
(str)
&ndash; id of the subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/oraclesubclient.py#L79-L704" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class OracleSubclient(DatabaseSubclient):
    &#34;&#34;&#34;
    OracleSubclient is a class to work on Oracle subclients

    &#34;&#34;&#34;

    def __init__(self, backupset_object, subclient_name, subclient_id=None):
        &#34;&#34;&#34;
        Constructor for the class

        Args:
            backupset_object  (object)  -- instance of the Backupset class
            subclient_name    (str)     -- name of the subclient
            subclient_id      (str)     -- id of the subclient
        &#34;&#34;&#34;
        super(OracleSubclient, self).__init__(
            backupset_object, subclient_name, subclient_id)
        self._get_subclient_properties()
        #self._oracle_properties = {}

    def _oracle_backup_json(
            self,
            backup_level=&#34;full&#34;,
            schedule_pattern=None,
            options=None):
        &#34;&#34;&#34;Runs a backup job for the subclient of the level specified.

            Args:
                backup_level            (str)   --  level of backup the user wish to run
                                                    Full / Incremental

                schedule_pattern (dict) -- scheduling options to be included for the task

                        Please refer schedules.schedulePattern.createSchedule()
                                                                    doc for the types of Jsons

                options          (dict) --  dictionary containing other oracle options

            Returns:
                dict    -- dict containing request JSON

        &#34;&#34;&#34;
        oracle_options = {
            &#34;oracleOptions&#34;: {}
        }
        if options is not None:
            oracle_options.update(options)
        request_json = self._backup_json(
            backup_level,
            False,
            &#34;BEFORE_SYNTH&#34;,
            schedule_pattern=schedule_pattern
        )

        # Add option to run RMAN cumulatives
        oracle_options[&#34;oracleOptions&#34;][&#34;cumulative&#34;] = True

        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;backupOpts&#34;].update(
            oracle_options
        )
        return request_json

    def _get_subclient_properties(self):
        &#34;&#34;&#34;Gets the subclient  related properties of Oracle subclient.
        &#34;&#34;&#34;
        if not bool(self._subclient_properties):
            super(OracleSubclient, self)._get_subclient_properties()
        self._oracle_subclient_properties = self._subclient_properties.get(&#34;oracleSubclientProp&#34;)

    def _get_subclient_properties_json(self):
        &#34;&#34;&#34;returns subclient property json for oracle
           Returns:
                dict - all subclient properties put inside a dict
        &#34;&#34;&#34;
        subclient_json = {
            &#34;subClientProperties&#34;:
                {
                    &#34;proxyClient&#34;: self._proxyClient,
                    &#34;subClientEntity&#34;: self._subClientEntity,
                    &#34;commonProperties&#34;: self._commonProperties,
                    &#34;oracleSubclientProp&#34;: self._oracle_subclient_properties,
                }
        }
        return subclient_json

    def set_prop_for_orcle_subclient(self, storage_policy, snap_engine=None, archivefilebfs=32):
        &#34;&#34;&#34;Updates the subclient properties.

            Args:

                storage_policy      (str)   --  name of the storage policy to be associated
                with the subclient

                snap_engine         (str)   --  Snap Engine to be set for subclient (optional)

                    default: None

            Raises:
                SDKException:
                    if storage policy argument is not of type string

                    if failed to update subclient

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        if not archivefilebfs and (self.archive_files_per_bfs == &#39;0&#39;):
            self.archive_files_per_bfs = 32
        else:
            self.archive_files_per_bfs = archivefilebfs

        self.data_stream = 2

        self.storage_policy = storage_policy
        if snap_engine:
            self.enable_intelli_snap(snap_engine)

    @property
    def data(self):
        &#34;&#34;&#34;
        Getter to fetch if data enabled in oracle subclient or not

            Returns:
                bool     --  True if data is enabled on the subclient. Else False

        &#34;&#34;&#34;
        return self._oracle_subclient_properties.get(&#34;data&#34;)

    @data.setter
    def data(self, data):
        &#34;&#34;&#34;
        Enables  data for oracle subclient

            Args:
                data      (bool) --   True if data to be enabled on the subclient. Else False
        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_oracle_subclient_properties[&#39;data&#39;]&#34;, data)

    @property
    def backup_archive_log(self):
        &#34;&#34;&#34;
        Getter to fetch if archive log backup enabled or not

            Returns:
                    bool     --  True if archivelog is enabled on the subclient. Else False

        &#34;&#34;&#34;
        return self._oracle_subclient_properties.get(&#34;backupArchiveLog&#34;)

    @backup_archive_log.setter
    def backup_archive_log(self, backup_archive_log):
        &#34;&#34;&#34;
        Setter to enable backup archive log in oracle subclient

            Args:
                backup_archive_log    (bool)    --  True if archive log to be enabled
                                                    on the subclient.Else False
        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_oracle_subclient_properties[&#39;backupArchiveLog&#39;]&#34;, backup_archive_log)

    @property
    def archive_delete(self):
        &#34;&#34;&#34;
        Getter to fetch if archive delete is enabled or not

            Returns:
                    bool     --  True if archive delete is enabled on the subclient, Else False

        &#34;&#34;&#34;
        return self._oracle_subclient_properties.get(&#34;archiveDelete&#34;)

    @archive_delete.setter
    def archive_delete(self, archive_delete):
        &#34;&#34;&#34;
        Setter to enable backup delete in oracle subclient

            Args:
                archive_delete    (bool)    --  True if archive delete to be enabled
                                                on the subclient, Else False
        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_oracle_subclient_properties[&#39;archiveDelete&#39;]&#34;, archive_delete)

    @property
    def selective_online_full(self):
        &#34;&#34;&#34;
        Getter to fetch if selective online full enabled or not

            Returns:
                    bool     --  True if selective online is enabled on the subclient. Else False

        &#34;&#34;&#34;
        return self._oracle_subclient_properties.get(&#34;selectiveOnlineFull&#34;)

    @selective_online_full.setter
    def selective_online_full(self, selective_online_full):
        &#34;&#34;&#34;
        Setter to enable backup archive log in oracle subclient

            Args:
                selective_online_full    (bool)    --  True if selective online to be enabled
                                                        on the subclient.Else False
        &#34;&#34;&#34;
        self.backup_archive_log = True
        self._set_subclient_properties(
            &#34;_oracle_subclient_properties[&#39;selectiveOnlineFull&#39;]&#34;, selective_online_full)

    @property
    def archive_files_per_bfs(self):
        &#34;&#34;&#34;
        Getter to fetch archive files per BFS

            Returns:
                    (int)    --     value for archive files per BFS
        &#34;&#34;&#34;
        return self._oracle_subclient_properties.get(&#34;archiveFilesPerBFS&#34;)

    @archive_files_per_bfs.setter
    def archive_files_per_bfs(self, archive_files_per_bfs=32):
        &#34;&#34;&#34;
        Setter to set parameter  archive files per BFS

            Args:
               archive_files_per_bfs    (int)    --     value for archive files per BFS
                                                        default : 32
        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_oracle_subclient_properties[&#39;archiveFilesPerBFS&#39;]&#34;, archive_files_per_bfs)

    @property
    def oracle_tag(self):
        &#34;&#34;&#34;
        Getter to fetch oracle tag

            Returns:
                    (int)    --     value for oracle tag
        &#34;&#34;&#34;
        return self._oracle_subclient_properties.get(&#34;oracleTag&#34;)

    @oracle_tag.setter
    def oracle_tag(self, oracle_tag):
        &#34;&#34;&#34;
        Setter to set parameter for oracle tag

            Args:
               oracle_tag    (int)    --     value for oracle tag
        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_oracle_subclient_properties[&#39;oracleTag&#39;]&#34;, oracle_tag)

    @property
    def skip_offline(self):
        &#34;&#34;&#34;
        Getter to fetch if skip offline is enabled or not

            Returns:
                    (bool)    --     True if the option is enabled, False if it is disabled
        &#34;&#34;&#34;
        return self._oracle_subclient_properties.get(&#34;skipOffline&#34;)

    @skip_offline.setter
    def skip_offline(self, skip_offline=False):
        &#34;&#34;&#34;
        Setter to set skip offline option

            Args:
               skip_offline    (bool)    --     True to enable the skip offline option,
                                                False to disable it.
                                                default=False
        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_oracle_subclient_properties[&#39;skipOffline&#39;]&#34;, skip_offline)

    @property
    def skip_read_only(self):
        &#34;&#34;&#34;
        Getter to fetch if skip read only is enabled or not

            Returns:
                    (bool)    --     True if the option is enabled, False if it is disabled
        &#34;&#34;&#34;
        return self._oracle_subclient_properties.get(&#34;skipReadOnly&#34;)

    @skip_read_only.setter
    def skip_read_only(self, skip_read_only=False):
        &#34;&#34;&#34;
        Setter to set skip read only option

            Args:
               skip_read_only    (bool)    --     True to enable the skip read only option,
                                                  False to disable it.
                                                  default=False

        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_oracle_subclient_properties[&#39;skipReadOnly&#39;]&#34;, skip_read_only)

    @property
    def skip_inaccessible(self):
        &#34;&#34;&#34;
        Getter to fetch if skip inaccessible is enabled or not

            Returns:
                    (bool)    --     True if the option is enabled, False if it is disabled
        &#34;&#34;&#34;
        return self._oracle_subclient_properties.get(&#34;skipInaccessible&#34;)

    @skip_inaccessible.setter
    def skip_inaccessible(self, skip_inaccessible=False):
        &#34;&#34;&#34;
        Setter to set skip inaccessible option

            Args:
               skip_inaccessible    (bool)    --    True to enable the skip inaccessible option,
                                                    False to disable it.
                                                    default=False

        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_oracle_subclient_properties[&#39;skipInaccessible&#39;]&#34;, skip_inaccessible)

    @property
    def data_stream(self):
        &#34;&#34;&#34;
        Getter to fetch data stream count

            Returns:
                    int     --  data backup stream count at subclient level

        &#34;&#34;&#34;
        return self._oracle_subclient_properties.get(&#34;dataThresholdStreams&#34;)

    @data_stream.setter
    def data_stream(self, data_stream=1):
        &#34;&#34;&#34;
        Setter to set data backup stream count at subclient level

            Args:
                data_stream    (int)    --  data backup stream count at subclient level
                                            default = 1
        &#34;&#34;&#34;
        self._set_subclient_properties(
            &#34;_oracle_subclient_properties[&#39;dataThresholdStreams&#39;]&#34;, data_stream)

    @property
    def data_sp(self):
        &#34;&#34;&#34;
        Getter for data storage policy

        Returns:
            string - string representing data storage policy
        &#34;&#34;&#34;
        return self._commonProperties[&#39;storageDevice&#39;][
            &#39;dataBackupStoragePolicy&#39;][&#39;storagePolicyName&#39;]

    @property
    def is_table_browse_enabled(self):
        &#34;&#34;&#34;
        Getter to check whether the subclient has table browse enabled

        Returns:
            Bool - True if table browse is enabled on the subclient. Else False
        &#34;&#34;&#34;
        # return self._oracle_subclient_properties[&#39;enableTableBrowse&#39;]
        return self._subclient_properties[&#39;oracleSubclientProp&#39;][&#39;enableTableBrowse&#39;]

    @property
    def is_snapenabled(self):
        &#34;&#34;&#34;
        Getter to check whether the subclient has snap enabled

        Returns:
            Bool - True if snap is enabled on the subclient. Else False

        &#34;&#34;&#34;
        return self._subclient_properties[&#39;commonProperties&#39;][&#39;snapCopyInfo&#39;][&#39;isSnapBackupEnabled&#39;]

    def enable_table_browse(self):
        &#34;&#34;&#34;
        Enables Table Browse for the subclient.

        Raises:
            SDKException:
                if failed to enable tablebrowse for subclient

        &#34;&#34;&#34;

        self._set_subclient_properties(&#34;_oracle_subclient_properties[&#39;enableTableBrowse&#39;]&#34;, True)

    def disable_table_browse(self):
        &#34;&#34;&#34;Disables Table Browse for the subclient.
            Raises:
                SDKException:
                        if failed to disable tablebrowse for subclient
        &#34;&#34;&#34;

        self._set_subclient_properties(
            &#34;_oracle_subclient_properties[&#39;enableTableBrowse&#39;]&#34;, False
        )

    def set_backupcopy_interface(self, interface):
        &#34;&#34;&#34;Sets the backup copy interafce for the subclient.

            Args:
                interface (str) -- type of the backup copy interface

            Raises:
                SDKException:
                    if failed to disable intelli snap for subclient
        &#34;&#34;&#34;

        if interface in self._backupcopy_interfaces:
            interface = self._backupcopy_interfaces[interface]
            self._commonProperties[&#39;snapCopyInfo&#39;][&#39;backupCopyInterface&#39;] = interface
        else:
            raise SDKException(&#34;Subclient&#34;, &#34;101&#34;)

    @property
    def find(self, *args, **kwargs):
        raise AttributeError(&#34;&#39;{0}&#39; object has no attribute &#39;{1}&#39;&#34;.format(
            self.__class__.__name__, &#39;find&#39;))

    def backup(
            self,
            backup_level=InstanceBackupType.FULL.value,
            cumulative=False,
            schedule_pattern=None,
            options=None):
        &#34;&#34;&#34;

        Args:

            backup_level            (str)   --  level of backup the user wish to run
                                                Full / Incremental
                                                    default: Full

            cumulative (Bool) -- True if cumulative backup is required
                                    default: False

            schedule_pattern (dict) -- scheduling options to be included for the task

                        Please refer schedules.schedulePattern.createSchedule()
                                                                    doc for the types of Jsons

            options          (dict) --  dictionary containing other oracle options

        Returns:
            object - instance of the Job class for this backup job if its an immediate Job

                     instance of the Schedule class for the backup job if its a scheduled Job

        Raises:
            SDKException:
                if backup level is incorrect

                if response is empty

                if response does not succeed

        &#34;&#34;&#34;
        if backup_level not in [&#39;full&#39;, &#39;incremental&#39;]:
            raise SDKException(r&#39;Subclient&#39;, r&#39;103&#39;)

        if not (cumulative or schedule_pattern or options):
            return super(OracleSubclient, self).backup(backup_level)

        if cumulative:
            backup_level = InstanceBackupType.CUMULATIVE.value
        request_json = self._oracle_backup_json(
            backup_level,
            schedule_pattern,
            options
        )
        backup_service = self._commcell_object._services[&#39;CREATE_TASK&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, backup_service, request_json
        )
        return self._process_backup_response(flag, response)

    def inline_backupcopy(self, backup_level=InstanceBackupType.FULL.value):
        &#34;&#34;&#34;Performs inline backupcopy on an oracle subclient

        Args:
            backup_level (str)  -- Level of backup. Can be full or incremental
                default: full

        Returns:
            object -- instance of Job class

        Raises:
            SDKException:
                if backup level is incorrect

                if response is empty

                if response does not succeed

        &#34;&#34;&#34;
        if backup_level not in [&#39;full&#39;, &#39;incremental&#39;]:
            raise SDKException(r&#39;Subclient&#39;, r&#39;103&#39;)

        backupcopy_level = 1

        backup_opts = {
            &#34;dataOpt&#34;: {
                &#34;skipCatalogPhaseForSnapBackup&#34;: True,
                &#34;createBackupCopyImmediately&#34;: True,
                &#34;useCatalogServer&#34;: True,
                &#34;followMountPoints&#34;: True,
                &#34;enableIndexCheckPointing&#34;: True,
                &#34;backupCopyType&#34;: 2,
                &#34;enforceTransactionLogUsage&#34;: True,
                &#34;skipConsistencyCheck&#34;: False,
                &#34;collectVMGranularRecoveryMetadataForBkpCopy&#34;: False,
                &#34;createNewIndex&#34;: False,
                &#34;verifySynthFull&#34;: True
            }
        }

        request_json = self._backup_json(
            backupcopy_level,
            incremental_backup=False,
            incremental_level=backupcopy_level,
            advanced_options=backup_opts,
            schedule_pattern=None)

        backup_service = self._commcell_object._services[&#39;CREATE_TASK&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, backup_service, request_json
        )
        return self._process_backup_response(flag, response)

    def restore(
            self,
            files=None,
            destination_client=None,
            common_options=None,
            browse_option=None,
            oracle_options=None, tag=None):
        &#34;&#34;&#34;Performs restore the entire/partial database using latest backup/backupcopy

        Args:
            files               (dict) -- dictionary containing file options
                default -- None

            destination_client  (str) -- destination client name
                default -- None

            common_options      (dict) -- common options to be passed on for restore
                default -- None

            browse_option       (dict) -- dictionary containing browse options

            oracle_options      (dict) -- dictionary containing other oracle options
                default -- By default it restores the controlfile and datafiles
                                from latest backup

            tag                 (str)  --  Type of the restore to be performed
                default:    None

                Example: {
                            &#34;resetLogs&#34;: 1,
                            &#34;switchDatabaseMode&#34;: True,
                            &#34;noCatalog&#34;: True,
                            &#34;restoreControlFile&#34;: True,
                            &#34;recover&#34;: True,
                            &#34;recoverFrom&#34;: 3,
                            &#34;restoreData&#34;: True,
                            &#34;restoreFrom&#34;: 3

                        }

        Returns:
            object -- Job containing restore details
        &#34;&#34;&#34;
        return self._backupset_object._instance_object.restore(files, destination_client,
                                                               common_options, browse_option,
                                                               oracle_options, tag)

    def restore_in_place(
            self,
            db_password=None,
            database_list=None,
            dest_client_name=None,
            dest_instance_name=None,
            destination_path=None):
        &#34;&#34;&#34;
        Method to restore the logical dump

            Args:

                db_password             (str)  -- password for oracle database

                database_list           (List) -- List of databases

                dest_client_name        (str)  -- Destination Client name

                dest_instance_name      (str)  -- Destination Instance name

                destination_path        (str)   --  destination path for restore

                    default: None

            Returns:
                object -- Job containing restore details

        &#34;&#34;&#34;
        instance_object = self._instance_object
        if dest_client_name is None:
            dest_client_name = instance_object._agent_object._client_object.client_name

        if dest_instance_name is None:
            dest_instance_name = instance_object.instance_name

        instance_object._restore_association = self._subclient_properties[&#34;subClientEntity&#34;]

        return instance_object.restore_in_place(
            db_password,
            database_list,
            dest_client_name,
            dest_instance_name,
            dest_path=destination_path)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient" href="dbsubclient.html#cvpysdk.subclients.dbsubclient.DatabaseSubclient">DatabaseSubclient</a></li>
<li><a title="cvpysdk.subclient.Subclient" href="../subclient.html#cvpysdk.subclient.Subclient">Subclient</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.subclients.oraclesubclient.OracleSubclient.archive_delete"><code class="name">var <span class="ident">archive_delete</span></code></dt>
<dd>
<div class="desc"><p>Getter to fetch if archive delete is enabled or not</p>
<pre><code>Returns:
        bool     --  True if archive delete is enabled on the subclient, Else False
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/oraclesubclient.py#L243-L252" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def archive_delete(self):
    &#34;&#34;&#34;
    Getter to fetch if archive delete is enabled or not

        Returns:
                bool     --  True if archive delete is enabled on the subclient, Else False

    &#34;&#34;&#34;
    return self._oracle_subclient_properties.get(&#34;archiveDelete&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.oraclesubclient.OracleSubclient.archive_files_per_bfs"><code class="name">var <span class="ident">archive_files_per_bfs</span></code></dt>
<dd>
<div class="desc"><p>Getter to fetch archive files per BFS</p>
<pre><code>Returns:
        (int)    --     value for archive files per BFS
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/oraclesubclient.py#L290-L298" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def archive_files_per_bfs(self):
    &#34;&#34;&#34;
    Getter to fetch archive files per BFS

        Returns:
                (int)    --     value for archive files per BFS
    &#34;&#34;&#34;
    return self._oracle_subclient_properties.get(&#34;archiveFilesPerBFS&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.oraclesubclient.OracleSubclient.backup_archive_log"><code class="name">var <span class="ident">backup_archive_log</span></code></dt>
<dd>
<div class="desc"><p>Getter to fetch if archive log backup enabled or not</p>
<pre><code>Returns:
        bool     --  True if archivelog is enabled on the subclient. Else False
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/oraclesubclient.py#L220-L229" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def backup_archive_log(self):
    &#34;&#34;&#34;
    Getter to fetch if archive log backup enabled or not

        Returns:
                bool     --  True if archivelog is enabled on the subclient. Else False

    &#34;&#34;&#34;
    return self._oracle_subclient_properties.get(&#34;backupArchiveLog&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.oraclesubclient.OracleSubclient.data"><code class="name">var <span class="ident">data</span></code></dt>
<dd>
<div class="desc"><p>Getter to fetch if data enabled in oracle subclient or not</p>
<pre><code>Returns:
    bool     --  True if data is enabled on the subclient. Else False
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/oraclesubclient.py#L198-L207" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def data(self):
    &#34;&#34;&#34;
    Getter to fetch if data enabled in oracle subclient or not

        Returns:
            bool     --  True if data is enabled on the subclient. Else False

    &#34;&#34;&#34;
    return self._oracle_subclient_properties.get(&#34;data&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.oraclesubclient.OracleSubclient.data_sp"><code class="name">var <span class="ident">data_sp</span></code></dt>
<dd>
<div class="desc"><p>Getter for data storage policy</p>
<h2 id="returns">Returns</h2>
<p>string - string representing data storage policy</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/oraclesubclient.py#L427-L436" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def data_sp(self):
    &#34;&#34;&#34;
    Getter for data storage policy

    Returns:
        string - string representing data storage policy
    &#34;&#34;&#34;
    return self._commonProperties[&#39;storageDevice&#39;][
        &#39;dataBackupStoragePolicy&#39;][&#39;storagePolicyName&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.oraclesubclient.OracleSubclient.data_stream"><code class="name">var <span class="ident">data_stream</span></code></dt>
<dd>
<div class="desc"><p>Getter to fetch data stream count</p>
<pre><code>Returns:
        int     --  data backup stream count at subclient level
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/oraclesubclient.py#L404-L413" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def data_stream(self):
    &#34;&#34;&#34;
    Getter to fetch data stream count

        Returns:
                int     --  data backup stream count at subclient level

    &#34;&#34;&#34;
    return self._oracle_subclient_properties.get(&#34;dataThresholdStreams&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.oraclesubclient.OracleSubclient.is_snapenabled"><code class="name">var <span class="ident">is_snapenabled</span></code></dt>
<dd>
<div class="desc"><p>Getter to check whether the subclient has snap enabled</p>
<h2 id="returns">Returns</h2>
<p>Bool - True if snap is enabled on the subclient. Else False</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/oraclesubclient.py#L449-L458" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_snapenabled(self):
    &#34;&#34;&#34;
    Getter to check whether the subclient has snap enabled

    Returns:
        Bool - True if snap is enabled on the subclient. Else False

    &#34;&#34;&#34;
    return self._subclient_properties[&#39;commonProperties&#39;][&#39;snapCopyInfo&#39;][&#39;isSnapBackupEnabled&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.oraclesubclient.OracleSubclient.is_table_browse_enabled"><code class="name">var <span class="ident">is_table_browse_enabled</span></code></dt>
<dd>
<div class="desc"><p>Getter to check whether the subclient has table browse enabled</p>
<h2 id="returns">Returns</h2>
<p>Bool - True if table browse is enabled on the subclient. Else False</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/oraclesubclient.py#L438-L447" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_table_browse_enabled(self):
    &#34;&#34;&#34;
    Getter to check whether the subclient has table browse enabled

    Returns:
        Bool - True if table browse is enabled on the subclient. Else False
    &#34;&#34;&#34;
    # return self._oracle_subclient_properties[&#39;enableTableBrowse&#39;]
    return self._subclient_properties[&#39;oracleSubclientProp&#39;][&#39;enableTableBrowse&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.oraclesubclient.OracleSubclient.oracle_tag"><code class="name">var <span class="ident">oracle_tag</span></code></dt>
<dd>
<div class="desc"><p>Getter to fetch oracle tag</p>
<pre><code>Returns:
        (int)    --     value for oracle tag
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/oraclesubclient.py#L312-L320" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def oracle_tag(self):
    &#34;&#34;&#34;
    Getter to fetch oracle tag

        Returns:
                (int)    --     value for oracle tag
    &#34;&#34;&#34;
    return self._oracle_subclient_properties.get(&#34;oracleTag&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.oraclesubclient.OracleSubclient.selective_online_full"><code class="name">var <span class="ident">selective_online_full</span></code></dt>
<dd>
<div class="desc"><p>Getter to fetch if selective online full enabled or not</p>
<pre><code>Returns:
        bool     --  True if selective online is enabled on the subclient. Else False
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/oraclesubclient.py#L266-L275" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def selective_online_full(self):
    &#34;&#34;&#34;
    Getter to fetch if selective online full enabled or not

        Returns:
                bool     --  True if selective online is enabled on the subclient. Else False

    &#34;&#34;&#34;
    return self._oracle_subclient_properties.get(&#34;selectiveOnlineFull&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.oraclesubclient.OracleSubclient.skip_inaccessible"><code class="name">var <span class="ident">skip_inaccessible</span></code></dt>
<dd>
<div class="desc"><p>Getter to fetch if skip inaccessible is enabled or not</p>
<pre><code>Returns:
        (bool)    --     True if the option is enabled, False if it is disabled
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/oraclesubclient.py#L380-L388" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def skip_inaccessible(self):
    &#34;&#34;&#34;
    Getter to fetch if skip inaccessible is enabled or not

        Returns:
                (bool)    --     True if the option is enabled, False if it is disabled
    &#34;&#34;&#34;
    return self._oracle_subclient_properties.get(&#34;skipInaccessible&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.oraclesubclient.OracleSubclient.skip_offline"><code class="name">var <span class="ident">skip_offline</span></code></dt>
<dd>
<div class="desc"><p>Getter to fetch if skip offline is enabled or not</p>
<pre><code>Returns:
        (bool)    --     True if the option is enabled, False if it is disabled
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/oraclesubclient.py#L333-L341" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def skip_offline(self):
    &#34;&#34;&#34;
    Getter to fetch if skip offline is enabled or not

        Returns:
                (bool)    --     True if the option is enabled, False if it is disabled
    &#34;&#34;&#34;
    return self._oracle_subclient_properties.get(&#34;skipOffline&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.oraclesubclient.OracleSubclient.skip_read_only"><code class="name">var <span class="ident">skip_read_only</span></code></dt>
<dd>
<div class="desc"><p>Getter to fetch if skip read only is enabled or not</p>
<pre><code>Returns:
        (bool)    --     True if the option is enabled, False if it is disabled
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/oraclesubclient.py#L356-L364" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def skip_read_only(self):
    &#34;&#34;&#34;
    Getter to fetch if skip read only is enabled or not

        Returns:
                (bool)    --     True if the option is enabled, False if it is disabled
    &#34;&#34;&#34;
    return self._oracle_subclient_properties.get(&#34;skipReadOnly&#34;)</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.subclients.oraclesubclient.OracleSubclient.backup"><code class="name flex">
<span>def <span class="ident">backup</span></span>(<span>self, backup_level='full', cumulative=False, schedule_pattern=None, options=None)</span>
</code></dt>
<dd>
<div class="desc"><h2 id="args">Args</h2>
<p>backup_level
(str)
&ndash;
level of backup the user wish to run
Full / Incremental
default: Full</p>
<p>cumulative (Bool) &ndash; True if cumulative backup is required
default: False</p>
<p>schedule_pattern (dict) &ndash; scheduling options to be included for the task</p>
<pre><code>        Please refer schedules.schedulePattern.createSchedule()
                                                    doc for the types of Jsons
</code></pre>
<p>options
(dict) &ndash;
dictionary containing other oracle options</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this backup job if its an immediate Job</p>
<pre><code>     instance of the Schedule class for the backup job if its a scheduled Job
</code></pre>
<h2 id="raises">Raises</h2>
<p>SDKException:
if backup level is incorrect</p>
<pre><code>if response is empty

if response does not succeed
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/oraclesubclient.py#L505-L560" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def backup(
        self,
        backup_level=InstanceBackupType.FULL.value,
        cumulative=False,
        schedule_pattern=None,
        options=None):
    &#34;&#34;&#34;

    Args:

        backup_level            (str)   --  level of backup the user wish to run
                                            Full / Incremental
                                                default: Full

        cumulative (Bool) -- True if cumulative backup is required
                                default: False

        schedule_pattern (dict) -- scheduling options to be included for the task

                    Please refer schedules.schedulePattern.createSchedule()
                                                                doc for the types of Jsons

        options          (dict) --  dictionary containing other oracle options

    Returns:
        object - instance of the Job class for this backup job if its an immediate Job

                 instance of the Schedule class for the backup job if its a scheduled Job

    Raises:
        SDKException:
            if backup level is incorrect

            if response is empty

            if response does not succeed

    &#34;&#34;&#34;
    if backup_level not in [&#39;full&#39;, &#39;incremental&#39;]:
        raise SDKException(r&#39;Subclient&#39;, r&#39;103&#39;)

    if not (cumulative or schedule_pattern or options):
        return super(OracleSubclient, self).backup(backup_level)

    if cumulative:
        backup_level = InstanceBackupType.CUMULATIVE.value
    request_json = self._oracle_backup_json(
        backup_level,
        schedule_pattern,
        options
    )
    backup_service = self._commcell_object._services[&#39;CREATE_TASK&#39;]
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, backup_service, request_json
    )
    return self._process_backup_response(flag, response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.oraclesubclient.OracleSubclient.disable_table_browse"><code class="name flex">
<span>def <span class="ident">disable_table_browse</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Disables Table Browse for the subclient.</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to disable tablebrowse for subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/oraclesubclient.py#L472-L481" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def disable_table_browse(self):
    &#34;&#34;&#34;Disables Table Browse for the subclient.
        Raises:
            SDKException:
                    if failed to disable tablebrowse for subclient
    &#34;&#34;&#34;

    self._set_subclient_properties(
        &#34;_oracle_subclient_properties[&#39;enableTableBrowse&#39;]&#34;, False
    )</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.oraclesubclient.OracleSubclient.enable_table_browse"><code class="name flex">
<span>def <span class="ident">enable_table_browse</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Enables Table Browse for the subclient.</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to enable tablebrowse for subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/oraclesubclient.py#L460-L470" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable_table_browse(self):
    &#34;&#34;&#34;
    Enables Table Browse for the subclient.

    Raises:
        SDKException:
            if failed to enable tablebrowse for subclient

    &#34;&#34;&#34;

    self._set_subclient_properties(&#34;_oracle_subclient_properties[&#39;enableTableBrowse&#39;]&#34;, True)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.oraclesubclient.OracleSubclient.inline_backupcopy"><code class="name flex">
<span>def <span class="ident">inline_backupcopy</span></span>(<span>self, backup_level='full')</span>
</code></dt>
<dd>
<div class="desc"><p>Performs inline backupcopy on an oracle subclient</p>
<h2 id="args">Args</h2>
<p>backup_level (str)
&ndash; Level of backup. Can be full or incremental
default: full</p>
<h2 id="returns">Returns</h2>
<p>object &ndash; instance of Job class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if backup level is incorrect</p>
<pre><code>if response is empty

if response does not succeed
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/oraclesubclient.py#L562-L613" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def inline_backupcopy(self, backup_level=InstanceBackupType.FULL.value):
    &#34;&#34;&#34;Performs inline backupcopy on an oracle subclient

    Args:
        backup_level (str)  -- Level of backup. Can be full or incremental
            default: full

    Returns:
        object -- instance of Job class

    Raises:
        SDKException:
            if backup level is incorrect

            if response is empty

            if response does not succeed

    &#34;&#34;&#34;
    if backup_level not in [&#39;full&#39;, &#39;incremental&#39;]:
        raise SDKException(r&#39;Subclient&#39;, r&#39;103&#39;)

    backupcopy_level = 1

    backup_opts = {
        &#34;dataOpt&#34;: {
            &#34;skipCatalogPhaseForSnapBackup&#34;: True,
            &#34;createBackupCopyImmediately&#34;: True,
            &#34;useCatalogServer&#34;: True,
            &#34;followMountPoints&#34;: True,
            &#34;enableIndexCheckPointing&#34;: True,
            &#34;backupCopyType&#34;: 2,
            &#34;enforceTransactionLogUsage&#34;: True,
            &#34;skipConsistencyCheck&#34;: False,
            &#34;collectVMGranularRecoveryMetadataForBkpCopy&#34;: False,
            &#34;createNewIndex&#34;: False,
            &#34;verifySynthFull&#34;: True
        }
    }

    request_json = self._backup_json(
        backupcopy_level,
        incremental_backup=False,
        incremental_level=backupcopy_level,
        advanced_options=backup_opts,
        schedule_pattern=None)

    backup_service = self._commcell_object._services[&#39;CREATE_TASK&#39;]
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, backup_service, request_json
    )
    return self._process_backup_response(flag, response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.oraclesubclient.OracleSubclient.restore"><code class="name flex">
<span>def <span class="ident">restore</span></span>(<span>self, files=None, destination_client=None, common_options=None, browse_option=None, oracle_options=None, tag=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Performs restore the entire/partial database using latest backup/backupcopy</p>
<h2 id="args">Args</h2>
<p>files
(dict) &ndash; dictionary containing file options
default &ndash; None</p>
<p>destination_client
(str) &ndash; destination client name
default &ndash; None</p>
<p>common_options
(dict) &ndash; common options to be passed on for restore
default &ndash; None</p>
<p>browse_option
(dict) &ndash; dictionary containing browse options</p>
<p>oracle_options
(dict) &ndash; dictionary containing other oracle options
default &ndash; By default it restores the controlfile and datafiles
from latest backup</p>
<p>tag
(str)
&ndash;
Type of the restore to be performed
default:
None</p>
<pre><code>Example: {
            "resetLogs": 1,
            "switchDatabaseMode": True,
            "noCatalog": True,
            "restoreControlFile": True,
            "recover": True,
            "recoverFrom": 3,
            "restoreData": True,
            "restoreFrom": 3

        }
</code></pre>
<h2 id="returns">Returns</h2>
<p>object &ndash; Job containing restore details</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/oraclesubclient.py#L615-L660" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore(
        self,
        files=None,
        destination_client=None,
        common_options=None,
        browse_option=None,
        oracle_options=None, tag=None):
    &#34;&#34;&#34;Performs restore the entire/partial database using latest backup/backupcopy

    Args:
        files               (dict) -- dictionary containing file options
            default -- None

        destination_client  (str) -- destination client name
            default -- None

        common_options      (dict) -- common options to be passed on for restore
            default -- None

        browse_option       (dict) -- dictionary containing browse options

        oracle_options      (dict) -- dictionary containing other oracle options
            default -- By default it restores the controlfile and datafiles
                            from latest backup

        tag                 (str)  --  Type of the restore to be performed
            default:    None

            Example: {
                        &#34;resetLogs&#34;: 1,
                        &#34;switchDatabaseMode&#34;: True,
                        &#34;noCatalog&#34;: True,
                        &#34;restoreControlFile&#34;: True,
                        &#34;recover&#34;: True,
                        &#34;recoverFrom&#34;: 3,
                        &#34;restoreData&#34;: True,
                        &#34;restoreFrom&#34;: 3

                    }

    Returns:
        object -- Job containing restore details
    &#34;&#34;&#34;
    return self._backupset_object._instance_object.restore(files, destination_client,
                                                           common_options, browse_option,
                                                           oracle_options, tag)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.oraclesubclient.OracleSubclient.restore_in_place"><code class="name flex">
<span>def <span class="ident">restore_in_place</span></span>(<span>self, db_password=None, database_list=None, dest_client_name=None, dest_instance_name=None, destination_path=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to restore the logical dump</p>
<pre><code>Args:

    db_password             (str)  -- password for oracle database

    database_list           (List) -- List of databases

    dest_client_name        (str)  -- Destination Client name

    dest_instance_name      (str)  -- Destination Instance name

    destination_path        (str)   --  destination path for restore

        default: None

Returns:
    object -- Job containing restore details
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/oraclesubclient.py#L662-L704" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_in_place(
        self,
        db_password=None,
        database_list=None,
        dest_client_name=None,
        dest_instance_name=None,
        destination_path=None):
    &#34;&#34;&#34;
    Method to restore the logical dump

        Args:

            db_password             (str)  -- password for oracle database

            database_list           (List) -- List of databases

            dest_client_name        (str)  -- Destination Client name

            dest_instance_name      (str)  -- Destination Instance name

            destination_path        (str)   --  destination path for restore

                default: None

        Returns:
            object -- Job containing restore details

    &#34;&#34;&#34;
    instance_object = self._instance_object
    if dest_client_name is None:
        dest_client_name = instance_object._agent_object._client_object.client_name

    if dest_instance_name is None:
        dest_instance_name = instance_object.instance_name

    instance_object._restore_association = self._subclient_properties[&#34;subClientEntity&#34;]

    return instance_object.restore_in_place(
        db_password,
        database_list,
        dest_client_name,
        dest_instance_name,
        dest_path=destination_path)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.oraclesubclient.OracleSubclient.set_backupcopy_interface"><code class="name flex">
<span>def <span class="ident">set_backupcopy_interface</span></span>(<span>self, interface)</span>
</code></dt>
<dd>
<div class="desc"><p>Sets the backup copy interafce for the subclient.</p>
<h2 id="args">Args</h2>
<p>interface (str) &ndash; type of the backup copy interface</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to disable intelli snap for subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/oraclesubclient.py#L483-L498" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_backupcopy_interface(self, interface):
    &#34;&#34;&#34;Sets the backup copy interafce for the subclient.

        Args:
            interface (str) -- type of the backup copy interface

        Raises:
            SDKException:
                if failed to disable intelli snap for subclient
    &#34;&#34;&#34;

    if interface in self._backupcopy_interfaces:
        interface = self._backupcopy_interfaces[interface]
        self._commonProperties[&#39;snapCopyInfo&#39;][&#39;backupCopyInterface&#39;] = interface
    else:
        raise SDKException(&#34;Subclient&#34;, &#34;101&#34;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.oraclesubclient.OracleSubclient.set_prop_for_orcle_subclient"><code class="name flex">
<span>def <span class="ident">set_prop_for_orcle_subclient</span></span>(<span>self, storage_policy, snap_engine=None, archivefilebfs=32)</span>
</code></dt>
<dd>
<div class="desc"><p>Updates the subclient properties.</p>
<h2 id="args">Args</h2>
<p>storage_policy
(str)
&ndash;
name of the storage policy to be associated
with the subclient</p>
<p>snap_engine
(str)
&ndash;
Snap Engine to be set for subclient (optional)</p>
<pre><code>default: None
</code></pre>
<h2 id="raises">Raises</h2>
<p>SDKException:
if storage policy argument is not of type string</p>
<pre><code>if failed to update subclient

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/oraclesubclient.py#L164-L196" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_prop_for_orcle_subclient(self, storage_policy, snap_engine=None, archivefilebfs=32):
    &#34;&#34;&#34;Updates the subclient properties.

        Args:

            storage_policy      (str)   --  name of the storage policy to be associated
            with the subclient

            snap_engine         (str)   --  Snap Engine to be set for subclient (optional)

                default: None

        Raises:
            SDKException:
                if storage policy argument is not of type string

                if failed to update subclient

                if response is empty

                if response is not success

    &#34;&#34;&#34;
    if not archivefilebfs and (self.archive_files_per_bfs == &#39;0&#39;):
        self.archive_files_per_bfs = 32
    else:
        self.archive_files_per_bfs = archivefilebfs

    self.data_stream = 2

    self.storage_policy = storage_policy
    if snap_engine:
        self.enable_intelli_snap(snap_engine)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient" href="dbsubclient.html#cvpysdk.subclients.dbsubclient.DatabaseSubclient">DatabaseSubclient</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.allow_multiple_readers" href="../subclient.html#cvpysdk.subclient.Subclient.allow_multiple_readers">allow_multiple_readers</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.browse" href="../subclient.html#cvpysdk.subclient.Subclient.browse">browse</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.data_readers" href="../subclient.html#cvpysdk.subclient.Subclient.data_readers">data_readers</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.deduplication_options" href="../subclient.html#cvpysdk.subclient.Subclient.deduplication_options">deduplication_options</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.description" href="../subclient.html#cvpysdk.subclient.Subclient.description">description</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.disable_backup" href="../subclient.html#cvpysdk.subclient.Subclient.disable_backup">disable_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.disable_intelli_snap" href="../subclient.html#cvpysdk.subclient.Subclient.disable_intelli_snap">disable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.display_name" href="../subclient.html#cvpysdk.subclient.Subclient.display_name">display_name</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.enable_backup" href="../subclient.html#cvpysdk.subclient.Subclient.enable_backup">enable_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.enable_backup_at_time" href="../subclient.html#cvpysdk.subclient.Subclient.enable_backup_at_time">enable_backup_at_time</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.enable_intelli_snap" href="../subclient.html#cvpysdk.subclient.Subclient.enable_intelli_snap">enable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.enable_trueup" href="../subclient.html#cvpysdk.subclient.Subclient.enable_trueup">enable_trueup</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.enable_trueup_days" href="../subclient.html#cvpysdk.subclient.Subclient.enable_trueup_days">enable_trueup_days</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.encryption_flag" href="../subclient.html#cvpysdk.subclient.Subclient.encryption_flag">encryption_flag</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.exclude_from_sla" href="../subclient.html#cvpysdk.subclient.Subclient.exclude_from_sla">exclude_from_sla</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.find" href="../subclient.html#cvpysdk.subclient.Subclient.find">find</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.find_latest_job" href="../subclient.html#cvpysdk.subclient.Subclient.find_latest_job">find_latest_job</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.get_ma_associated_storagepolicy" href="../subclient.html#cvpysdk.subclient.Subclient.get_ma_associated_storagepolicy">get_ma_associated_storagepolicy</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.is_backup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_backup_enabled">is_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.is_blocklevel_backup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_blocklevel_backup_enabled">is_blocklevel_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.is_default_subclient" href="../subclient.html#cvpysdk.subclient.Subclient.is_default_subclient">is_default_subclient</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.is_intelli_snap_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_intelli_snap_enabled">is_intelli_snap_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.is_on_demand_subclient" href="../subclient.html#cvpysdk.subclient.Subclient.is_on_demand_subclient">is_on_demand_subclient</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.is_trueup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_trueup_enabled">is_trueup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.last_backup_time" href="../subclient.html#cvpysdk.subclient.Subclient.last_backup_time">last_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.list_media" href="../subclient.html#cvpysdk.subclient.Subclient.list_media">list_media</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.log_backup_storage_policy" href="dbsubclient.html#cvpysdk.subclients.dbsubclient.DatabaseSubclient.log_backup_storage_policy">log_backup_storage_policy</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.name" href="../subclient.html#cvpysdk.subclient.Subclient.name">name</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.network_agent" href="../subclient.html#cvpysdk.subclient.Subclient.network_agent">network_agent</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.next_backup_time" href="../subclient.html#cvpysdk.subclient.Subclient.next_backup_time">next_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.plan" href="../subclient.html#cvpysdk.subclient.Subclient.plan">plan</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.properties" href="../subclient.html#cvpysdk.subclient.Subclient.properties">properties</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.read_buffer_size" href="../subclient.html#cvpysdk.subclient.Subclient.read_buffer_size">read_buffer_size</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.refresh" href="../subclient.html#cvpysdk.subclient.Subclient.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.restore_out_of_place" href="../subclient.html#cvpysdk.subclient.Subclient.restore_out_of_place">restore_out_of_place</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.run_content_indexing" href="../subclient.html#cvpysdk.subclient.Subclient.run_content_indexing">run_content_indexing</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.set_backup_nodes" href="../subclient.html#cvpysdk.subclient.Subclient.set_backup_nodes">set_backup_nodes</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.set_proxy_for_snap" href="../subclient.html#cvpysdk.subclient.Subclient.set_proxy_for_snap">set_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.snapshot_engine_name" href="../subclient.html#cvpysdk.subclient.Subclient.snapshot_engine_name">snapshot_engine_name</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.software_compression" href="../subclient.html#cvpysdk.subclient.Subclient.software_compression">software_compression</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.storage_ma" href="../subclient.html#cvpysdk.subclient.Subclient.storage_ma">storage_ma</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.storage_ma_id" href="../subclient.html#cvpysdk.subclient.Subclient.storage_ma_id">storage_ma_id</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.storage_policy" href="../subclient.html#cvpysdk.subclient.Subclient.storage_policy">storage_policy</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.subclient_guid" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_guid">subclient_guid</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.subclient_id" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_id">subclient_id</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.subclient_name" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_name">subclient_name</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.unset_proxy_for_snap" href="../subclient.html#cvpysdk.subclient.Subclient.unset_proxy_for_snap">unset_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.dbsubclient.DatabaseSubclient.update_properties" href="../subclient.html#cvpysdk.subclient.Subclient.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.subclients" href="index.html">cvpysdk.subclients</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.subclients.oraclesubclient.OracleSubclient" href="#cvpysdk.subclients.oraclesubclient.OracleSubclient">OracleSubclient</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.subclients.oraclesubclient.OracleSubclient.archive_delete" href="#cvpysdk.subclients.oraclesubclient.OracleSubclient.archive_delete">archive_delete</a></code></li>
<li><code><a title="cvpysdk.subclients.oraclesubclient.OracleSubclient.archive_files_per_bfs" href="#cvpysdk.subclients.oraclesubclient.OracleSubclient.archive_files_per_bfs">archive_files_per_bfs</a></code></li>
<li><code><a title="cvpysdk.subclients.oraclesubclient.OracleSubclient.backup" href="#cvpysdk.subclients.oraclesubclient.OracleSubclient.backup">backup</a></code></li>
<li><code><a title="cvpysdk.subclients.oraclesubclient.OracleSubclient.backup_archive_log" href="#cvpysdk.subclients.oraclesubclient.OracleSubclient.backup_archive_log">backup_archive_log</a></code></li>
<li><code><a title="cvpysdk.subclients.oraclesubclient.OracleSubclient.data" href="#cvpysdk.subclients.oraclesubclient.OracleSubclient.data">data</a></code></li>
<li><code><a title="cvpysdk.subclients.oraclesubclient.OracleSubclient.data_sp" href="#cvpysdk.subclients.oraclesubclient.OracleSubclient.data_sp">data_sp</a></code></li>
<li><code><a title="cvpysdk.subclients.oraclesubclient.OracleSubclient.data_stream" href="#cvpysdk.subclients.oraclesubclient.OracleSubclient.data_stream">data_stream</a></code></li>
<li><code><a title="cvpysdk.subclients.oraclesubclient.OracleSubclient.disable_table_browse" href="#cvpysdk.subclients.oraclesubclient.OracleSubclient.disable_table_browse">disable_table_browse</a></code></li>
<li><code><a title="cvpysdk.subclients.oraclesubclient.OracleSubclient.enable_table_browse" href="#cvpysdk.subclients.oraclesubclient.OracleSubclient.enable_table_browse">enable_table_browse</a></code></li>
<li><code><a title="cvpysdk.subclients.oraclesubclient.OracleSubclient.inline_backupcopy" href="#cvpysdk.subclients.oraclesubclient.OracleSubclient.inline_backupcopy">inline_backupcopy</a></code></li>
<li><code><a title="cvpysdk.subclients.oraclesubclient.OracleSubclient.is_snapenabled" href="#cvpysdk.subclients.oraclesubclient.OracleSubclient.is_snapenabled">is_snapenabled</a></code></li>
<li><code><a title="cvpysdk.subclients.oraclesubclient.OracleSubclient.is_table_browse_enabled" href="#cvpysdk.subclients.oraclesubclient.OracleSubclient.is_table_browse_enabled">is_table_browse_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.oraclesubclient.OracleSubclient.oracle_tag" href="#cvpysdk.subclients.oraclesubclient.OracleSubclient.oracle_tag">oracle_tag</a></code></li>
<li><code><a title="cvpysdk.subclients.oraclesubclient.OracleSubclient.restore" href="#cvpysdk.subclients.oraclesubclient.OracleSubclient.restore">restore</a></code></li>
<li><code><a title="cvpysdk.subclients.oraclesubclient.OracleSubclient.restore_in_place" href="#cvpysdk.subclients.oraclesubclient.OracleSubclient.restore_in_place">restore_in_place</a></code></li>
<li><code><a title="cvpysdk.subclients.oraclesubclient.OracleSubclient.selective_online_full" href="#cvpysdk.subclients.oraclesubclient.OracleSubclient.selective_online_full">selective_online_full</a></code></li>
<li><code><a title="cvpysdk.subclients.oraclesubclient.OracleSubclient.set_backupcopy_interface" href="#cvpysdk.subclients.oraclesubclient.OracleSubclient.set_backupcopy_interface">set_backupcopy_interface</a></code></li>
<li><code><a title="cvpysdk.subclients.oraclesubclient.OracleSubclient.set_prop_for_orcle_subclient" href="#cvpysdk.subclients.oraclesubclient.OracleSubclient.set_prop_for_orcle_subclient">set_prop_for_orcle_subclient</a></code></li>
<li><code><a title="cvpysdk.subclients.oraclesubclient.OracleSubclient.skip_inaccessible" href="#cvpysdk.subclients.oraclesubclient.OracleSubclient.skip_inaccessible">skip_inaccessible</a></code></li>
<li><code><a title="cvpysdk.subclients.oraclesubclient.OracleSubclient.skip_offline" href="#cvpysdk.subclients.oraclesubclient.OracleSubclient.skip_offline">skip_offline</a></code></li>
<li><code><a title="cvpysdk.subclients.oraclesubclient.OracleSubclient.skip_read_only" href="#cvpysdk.subclients.oraclesubclient.OracleSubclient.skip_read_only">skip_read_only</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>