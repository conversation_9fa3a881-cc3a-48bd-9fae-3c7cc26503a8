<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.subclients.virtualserver.kubernetes API documentation</title>
<meta name="description" content="File for operating on a Virtual Server Kubernetes Subclient …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.subclients.virtualserver.kubernetes</code></h1>
</header>
<section id="section-intro">
<p>File for operating on a Virtual Server Kubernetes Subclient.</p>
<p>KubernetesVirtualServerSubclient and ApplicationGroups are the only class defined in this file.</p>
<p>Class: KubernetesVirtualServerSubclient:
Derived class from VirtualServerSubClient Base
class,representing a Kubernetes Subclient,
and to perform operations on that Subclient</p>
<pre><code>KubernetesVirtualServerSubclient:

    __init__(
        backupset_object,
        subclient_name,
        subclient_id)           --  initialize object of Kubernetes subclient class,
                                    associated with the VirtualServer subclient

    full_vm_restore_in_place()  --  restores the pod specified by the user to
                                    the same location

    full_vm_restore_out_of_place() -- restores the pod specified to the provided
                                      Kubernetes psuedoclient

    _prepare_kubernetes_restore_json() -- Restore json prep method for kubernetes

    _json_restore_volumeRstOption()  -- Restores json for volumeRstOptions

    set_advanced_vm_restore_options() -- Advanced VM restore options

    _json_restore_virtualServerRstOption() -- json for VirtualServerRst options for Kubernetes.

    disk_restore()                  --  Function to restore disk.

    enable_intelli_snap()           --  Enables Intellisnap on subclient

    guest_file_restore()            --  Restore the files and folders to file system destionation
                                        or to target PVC

    guest_files_browse()            --  Browse files in a application at any point in time

    namespace_restore_in_place()    --  Perform a namespace level restore in-place

    namespace_restore_out_of_place()--  Perform a namespace level restore out-of-place
</code></pre>
<p>Class: ApplicationGroups:
Derived class from Subclients Base
class,representing a Kubernetes ApplicationGroups,
and to perform operations on that ApplicationGroups</p>
<pre><code>ApplicationGroups:

    __init__(class_object)           --  initialize object of Kubernetes subclient class,
                                        associated with the VirtualServer subclient

    browse()                         -- Browse cluster for namespace, applications, volumes, or labels

    get_children_node()              -- Construct the json object for content and filter

    create_application_group()       --       creates application group
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/kubernetes.py#L1-L1651" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;File for operating on a Virtual Server Kubernetes Subclient.

KubernetesVirtualServerSubclient and ApplicationGroups are the only class defined in this file.

Class: KubernetesVirtualServerSubclient:    Derived class from VirtualServerSubClient Base
                                            class,representing a Kubernetes Subclient,
                                            and to perform operations on that Subclient

    KubernetesVirtualServerSubclient:

        __init__(
            backupset_object,
            subclient_name,
            subclient_id)           --  initialize object of Kubernetes subclient class,
                                        associated with the VirtualServer subclient

        full_vm_restore_in_place()  --  restores the pod specified by the user to
                                        the same location

        full_vm_restore_out_of_place() -- restores the pod specified to the provided
                                          Kubernetes psuedoclient

        _prepare_kubernetes_restore_json() -- Restore json prep method for kubernetes

        _json_restore_volumeRstOption()  -- Restores json for volumeRstOptions

        set_advanced_vm_restore_options() -- Advanced VM restore options

        _json_restore_virtualServerRstOption() -- json for VirtualServerRst options for Kubernetes.

        disk_restore()                  --  Function to restore disk.

        enable_intelli_snap()           --  Enables Intellisnap on subclient

        guest_file_restore()            --  Restore the files and folders to file system destionation
                                            or to target PVC

        guest_files_browse()            --  Browse files in a application at any point in time

        namespace_restore_in_place()    --  Perform a namespace level restore in-place

        namespace_restore_out_of_place()--  Perform a namespace level restore out-of-place


Class: ApplicationGroups:                Derived class from Subclients Base
                                            class,representing a Kubernetes ApplicationGroups,
                                            and to perform operations on that ApplicationGroups

    ApplicationGroups:

        __init__(class_object)           --  initialize object of Kubernetes subclient class,
                                            associated with the VirtualServer subclient

        browse()                         -- Browse cluster for namespace, applications, volumes, or labels

        get_children_node()              -- Construct the json object for content and filter

        create_application_group()       --       creates application group


&#34;&#34;&#34;

import copy
from cvpysdk.subclients.vssubclient import VirtualServerSubclient
from cvpysdk.virtualmachinepolicies import VirtualMachinePolicy
from ...exception import SDKException
from ...subclient import Subclients


class KubernetesVirtualServerSubclient(VirtualServerSubclient):
    &#34;&#34;&#34;Derived class from VirtualServerSubclient Base class.
       This represents a Kubernetes virtual server subclient,
       and can perform restore operations on only that subclient.

    &#34;&#34;&#34;

    def __init__(self, backupset_object, subclient_name, subclient_id=None):
        &#34;&#34;&#34;Initialize the Instance object for the given Virtual Server instance.
        Args
        class_object (backupset_object, subclient_name, subclient_id)  --  instance of the
                                         backupset class, subclient name, subclient id

        &#34;&#34;&#34;
        super(KubernetesVirtualServerSubclient, self).__init__(
            backupset_object, subclient_name, subclient_id)
        self.diskExtension = [&#34;.yaml&#34;]

        self._disk_option = {
            &#39;Original&#39;: 0,
            &#39;Thick Lazy Zero&#39;: 1,
            &#39;Thin&#39;: 2,
            &#39;Thick Eager Zero&#39;: 3
        }

        self._transport_mode = {
            &#39;Auto&#39;: 0,
            &#39;SAN&#39;: 1,
            &#39;Hot Add&#39;: 2,
            &#39;NBD&#39;: 5,
            &#39;NBD SSL&#39;: 4
        }

    def full_app_restore_out_of_place(
            self,
            apps_to_restore,
            restore_namespace,
            restored_app_name=None,
            kubernetes_client=None,
            storage_class=None,
            overwrite=True,
            copy_precedence=0,
            proxy_client=None,
    ):
        &#34;&#34;&#34;Restores the FULL Application specified in the input list
            to the provided Kubernetes client at the specified namespace with storage class.
            If the provided client name is none then it restores the Full Application
            to the source Kubernetes client and corresponding namespace and storage class.

            Args:
                apps_to_restore         (list)  --  List of Applications that is to be restored

                restored_app_name       (dict)  --  Dictionary mapping new name of Applications

                kubernetes_client       (str)   --  Name of the Kubernetes client where the Application should be restored
                                                    Restores to the source Kubernetes client if this value is not specified

                storage_class           (str)   --  Storage class for the PVC to be restored with.
                                                    Uses source storage class if not specified.

                restore_namespace       (str)   --  Target namespace where Applications are to be restored

                overwrite               (bool)  --  overwrite the existing Applications if exists
                                                    default: True

                copy_precedence          (int)  --  copy precedence value
                                                      default: 0

                proxy_client              (str)    --  destination proxy client

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if inputs are not of correct type as per definition

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;

        restore_option = {}

        # check mandatory input parameters are correct
        if apps_to_restore and not isinstance(apps_to_restore, list):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        # populating proxy client. It assumes the proxy controller added in instance
        # properties if not specified
        if proxy_client is not None:
            restore_option[&#39;client&#39;] = proxy_client

        if restored_app_name:
            if not(isinstance(apps_to_restore, list) or
                   isinstance(restored_app_name, dict)):
                raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
            restore_option[&#39;restore_new_name&#39;] = restored_app_name

        if not kubernetes_client:
            kubernetes_client = self._client_object.client_name

        restore_option_copy = restore_option.copy()

        self._set_restore_inputs(
            restore_option,
            in_place=False,
            vcenter_client=kubernetes_client,
            datastore=storage_class,
            esx_host=kubernetes_client,
            datacenter=restore_namespace,
            unconditional_overwrite=overwrite,
            vm_to_restore=self._set_vm_to_restore(apps_to_restore),
            copy_precedence=copy_precedence,
            volume_level_restore=1,
            source_item=[]
        )

        request_json = self._prepare_kubernetes_restore_json(restore_option)
        return self._process_restore_response(request_json)


    def _prepare_kubernetes_restore_json(self, restore_option):
        &#34;&#34;&#34;
        Prepare Full Application restore Json with all getters

        Args:
            restore_option - dictionary with all Application restore options

        value:
            restore_option:

                preserve_level           (bool)   - set the preserve level in restore

                unconditional_overwrite  (bool)  - unconditionally overwrite the disk
                                                    in the restore path

                destination_path          (str)- path where the disk needs to be
                                                 restored

                client_name               (str)  - client where the disk needs to be
                                                   restored

                destination_vendor         (str) - vendor id of the Hypervisor

                destination_disktype       (str) - type of disk needs to be restored
                                                   like VHDX,VHD,VMDK

                source_item                 (str)- GUID of Application from which disk needs to
                                                   be restored
                                                   eg:
                                                   \\5F9FA60C-0A89-4BD9-9D02-C5ACB42745EA

                copy_precedence_applicable  (bool)- True if needs copy_precedence to
                                                    be honoured else False

                copy_precedence            (int) - the copy id from which browse and
                                                   restore needs to be performed

                datastore                   (str) - Storage class which the Application PVC needs to be
                                                    restored with

                disks   (list of dict)      - list with dict for each disk in Application
                                                eg: [{
                                                        name:&#34;pvc-1&#34;
                                                        datastore:&#34;storageclass-1&#34;
                                                    }
                                                    {
                                                        name:&#34;pvc-2&#34;
                                                        datastore:&#34;storageclass-2&#34;
                                                    }
                                                ]
                guid                    (str)    - GUID of the Application needs to be restored

                new_name                (str)    - New name for the Application to be restored

                esx_host                (str)    - client name where Application need to be restored

                name                    (str)    - name of the Application to be restored

        returns:
              request_json        -complete json for perfomring Full Application Restore
                                   options

        &#34;&#34;&#34;
        if restore_option is None:
            restore_option = {}
        restore_option[&#39;paths&#39;] = []

        if &#34;destination_vendor&#34; not in restore_option:
            restore_option[&#34;destination_vendor&#34;] = \
                self._backupset_object._instance_object._vendor_id

        if restore_option[&#39;copy_precedence&#39;]:
            restore_option[&#39;copy_precedence_applicable&#39;] = True

        # set all the restore defaults
        self._set_restore_defaults(restore_option)

        # set the setters
        self._backupset_object._instance_object._restore_association = self._subClientEntity
        self._json_restore_virtualServerRstOption(restore_option)
        self._json_restore_diskLevelVMRestoreOption(restore_option)
        self._json_vcenter_instance(restore_option)

        _new_name_dict = restore_option.get(&#39;restore_new_name&#39;, {})
        for _each_vm_to_restore in restore_option[&#39;vm_to_restore&#39;]:
            restore_option[&#34;new_name&#34;] = _new_name_dict.get(_each_vm_to_restore, _each_vm_to_restore)
            namespace_app_map = restore_option.get(&#39;namespace_app_map&#39;, {})
            datacenter = restore_option.get(&#39;datacenter&#39;, None)

            if not namespace_app_map:
                # FOR : Full Application Restores Restores
                # If namespace_app_map is not passed the it is a full application restore
                # so &#39;datacenter&#39; should be passed. Nothing to do here
                pass

            elif _each_vm_to_restore in namespace_app_map:
                # FOR : Namespace Level Restore
                # If _each_vm_to_restore is in namespace_app_map which means it&#39;s an application
                # and not a namespace, so we need to pass &#39;datacenter&#39; to advanced restore options

                app_ns = namespace_app_map.get(_each_vm_to_restore)

                # Getting the target namespace if restoring to a new namespace name
                target_ns = _new_name_dict[app_ns]
                restore_option[&#39;datacenter&#39;] = target_ns
            else:
                # FOR : Namespace Level Restore
                # If it&#39;s a namespace, then there is no &#39;datacenter&#39; needed so pop it
                if &#39;datacenter&#39; in restore_option:
                    restore_option.pop(&#39;datacenter&#39;)
            self.set_advanced_vm_restore_options(_each_vm_to_restore, restore_option)
        # prepare json
        request_json = self._restore_json(restore_option=restore_option)
        self._virtualserver_option_restore_json[&#34;diskLevelVMRestoreOption&#34;][
            &#34;advancedRestoreOptions&#34;] = self._advanced_restore_option_list
        self._advanced_restore_option_list = []
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;virtualServerRstOption&#34;] = self._virtualserver_option_restore_json
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;volumeRstOption&#34;] = self._json_restore_volumeRstOption(
                restore_option)

        return request_json

    def _json_restore_volumeRstOption(self, value):
        &#34;&#34;&#34;setter for  the Volume restore option for in restore json&#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        return{
            &#34;volumeLeveRestore&#34;: False,
            &#34;volumeLevelRestoreType&#34;: value.get(&#34;volume_level_restore&#34;, 0)
        }

    def _json_restore_virtualServerRstOption(self, value):
        &#34;&#34;&#34;
            setter for  the Virtual server restore  option in restore json
        &#34;&#34;&#34;
        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        self._virtualserver_option_restore_json = {
            &#34;isDiskBrowse&#34;: value.get(&#34;disk_browse&#34;, True),
            &#34;isFileBrowse&#34;: value.get(&#34;file_browse&#34;, False),
            &#34;isVolumeBrowse&#34;: False,
            &#34;isVirtualLab&#34;: value.get(&#34;virtual_lab&#34;, False),
            &#34;esxServer&#34;: value.get(&#34;esx_server&#34;, &#34;&#34;),
            &#34;isAttachToNewVM&#34;: value.get(&#34;attach_to_new_vm&#34;, False),
            &#34;viewType&#34;: &#34;DEFAULT&#34;,
            &#34;isBlockLevelReplication&#34;: value.get(&#34;block_level&#34;, False),
            &#34;datacenter&#34;: value.get(&#34;datacenter&#34;, &#34;&#34;)
        }

        if value.get(&#39;replication_guid&#39;):
            self._virtualserver_option_restore_json[&#39;replicationGuid&#39;] = value[&#39;replication_guid&#39;]

    def _json_restore_advancedRestoreOptions(self, value):
        &#34;&#34;&#34;setter for the Virtual server restore  option in restore json&#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        self._advanced_option_restore_json = {
            &#34;disks&#34;: value.get(&#34;disks&#34;, []),
            &#34;guid&#34;: value.get(&#34;guid&#34;, &#34;&#34;),
            &#34;newGuid&#34;: value.get(&#34;new_guid&#34;, &#34;&#34;),
            &#34;newName&#34;: value.get(&#34;new_name&#34;, &#34;&#34;),
            &#34;esxHost&#34;: value.get(&#34;esx_host&#34;, &#34;&#34;),
            &#34;projectId&#34;: value.get(&#34;project_id&#34;, &#34;&#34;),
            &#34;cluster&#34;: value.get(&#34;cluster&#34;, &#34;&#34;),
            &#34;name&#34;: value.get(&#34;name&#34;, &#34;&#34;),
            &#34;nics&#34;: value.get(&#34;nics&#34;, []),
            &#34;vmIPAddressOptions&#34;: value.get(&#34;vm_ip_address_options&#34;, []),
            &#34;FolderPath&#34;: value.get(&#34;FolderPath&#34;, &#34;&#34;),
            &#34;resourcePoolPath&#34;: value.get(&#34;ResourcePool&#34;, &#34;&#34;),
            &#34;volumeType&#34;: value.get(&#34;volumeType&#34;, &#34;Auto&#34;),
            &#34;endUserVMRestore&#34;: value.get(&#34;end_user_vm_restore&#34;, False)     # Required by Kubernetes Disk Level Restore
        }

        value_dict = {
            &#34;createPublicIP&#34;: [&#34;createPublicIP&#34;, [&#34;createPublicIP&#34;, &#34;&#34;]],
            &#34;restoreAsManagedVM&#34;: [&#34;restoreAsManagedVM&#34;, [&#34;restoreAsManagedVM&#34;, &#34;&#34;]],
            &#34;destination_os_name&#34;: [&#34;osName&#34;, [&#34;destination_os_name&#34;, &#34;AUTO&#34;]],
            &#34;resourcePoolPath&#34;: [&#34;resourcePoolPath&#34;, [&#34;resourcePoolPath&#34;, &#34;&#34;]],
            &#34;datacenter&#34;: [&#34;datacenter&#34;, [&#34;datacenter&#34;, &#34;&#34;]],
            &#34;terminationProtected&#34;: [&#34;terminationProtected&#34;, [&#34;terminationProtected&#34;, False]],
            &#34;securityGroups&#34;: [&#34;securityGroups&#34;, [&#34;securityGroups&#34;, &#34;&#34;]],
            &#34;keyPairList&#34;: [&#34;keyPairList&#34;, [&#34;keyPairList&#34;, &#34;&#34;]]
        }

        for key in value_dict:
            if key in value:
                inner_key = value_dict[key][0]
                val1, val2 = value_dict[key][1][0], value_dict[key][1][1]
                self._advanced_option_restore_json[inner_key] = value.get(val1, val2)

        if &#34;vmSize&#34; in value:
            val1, val2 = (&#34;instanceSize&#34;, &#34;&#34;) if not value[&#34;vmSize&#34;] else (&#34;vmSize&#34;, &#34;vmSize&#34;)
            self._advanced_option_restore_json[&#34;vmSize&#34;] = value.get(val1, val2)
        if &#34;ami&#34; in value and value[&#34;ami&#34;] is not None:
            self._advanced_option_restore_json[&#34;templateId&#34;] = value[&#34;ami&#34;][&#34;templateId&#34;]
            self._advanced_option_restore_json[&#34;templateName&#34;] = value[&#34;ami&#34;][&#34;templateName&#34;]
        if &#34;iamRole&#34; in value and value[&#34;iamRole&#34;] is not None:
            self._advanced_option_restore_json[&#34;roleInfo&#34;] = {
                &#34;name&#34;: value[&#34;iamRole&#34;]
            }
        if self._instance_object.instance_name == &#39;openstack&#39;:
            if &#34;securityGroups&#34; in value and value[&#34;securityGroups&#34;] is not None:
                self._advanced_option_restore_json[&#34;securityGroups&#34;] = [{&#34;groupName&#34;: value[&#34;securityGroups&#34;]}]
        if &#34;destComputerName&#34; in value and value[&#34;destComputerName&#34;] is not None:
            self._advanced_option_restore_json[&#34;destComputerName&#34;] = value[&#34;destComputerName&#34;]
        if &#34;destComputerUserName&#34; in value and value[&#34;destComputerUserName&#34;] is not None:
            self._advanced_option_restore_json[&#34;destComputerUserName&#34;] = value[&#34;destComputerUserName&#34;]
        if &#34;instanceAdminPassword&#34; in value and value[&#34;instanceAdminPassword&#34;] is not None:
            self._advanced_option_restore_json[&#34;instanceAdminPassword&#34;] = value[&#34;instanceAdminPassword&#34;]

        if self.disk_pattern.datastore.value == &#34;DestinationPath&#34;:
            self._advanced_option_restore_json[&#34;DestinationPath&#34;] = value.get(&#34;datastore&#34;, &#34;&#34;)

        else:
            self._advanced_option_restore_json[&#34;Datastore&#34;] = value.get(&#34;datastore&#34;, &#34;&#34;)

        if &#34;datacenter&#34; in value:   # Required for Kubernetes Disk Level Restore
            self._advanced_option_restore_json[&#39;datacenter&#39;] = value[&#34;datacenter&#34;]

        if value.get(&#39;block_level&#39;):
            self._advanced_option_restore_json[&#34;blrRecoveryOpts&#34;] = \
                self._json_restore_blrRecoveryOpts(value)

        temp_dict = copy.deepcopy(self._advanced_option_restore_json)
        return temp_dict

    def _get_app_pvc(self, application_id):
        &#34;&#34;&#34;Get the dictionary of PVCs in the applications with storage class info

            Args:

                application_id      (str)       --  Application GUID to get PVC

            Returns:

                List of dicts with PVC information
        &#34;&#34;&#34;
        app_disks, disk_metadata = self.browse(&#39;\\&#39; + application_id)
        pvc_path_list = [disk for disk in app_disks if disk.split(&#39;.&#39;)[-1] != &#39;yaml&#39;]

        pvc_list = []

        for pvc in pvc_path_list:
            temp_dict = {}
            pvc_metadata = disk_metadata[pvc]
            vs_metadata = pvc_metadata[&#39;advanced_data&#39;][&#39;browseMetaData&#39;][&#39;virtualServerMetaData&#39;]
            temp_dict[&#39;name&#39;] = pvc_metadata[&#39;name&#39;]
            temp_dict[&#39;storageclass&#39;] = vs_metadata[&#39;datastore&#39;]
            pvc_list.append(temp_dict)

        return pvc_list

    def set_advanced_vm_restore_options(self, vm_to_restore, restore_option):
        &#34;&#34;&#34;
        set the advanced restore options for all vm in restore
        param

            vm_to_restore               - Name of the Application to restore

            restore_option              - restore options that need to be set for advanced restore option

            power_on                    - power on the Application after restore

            add_to_failover             - Register the Application to Failover Cluster

            datastore                   - Datastore where the Application needs to be restored

            disks   (list of dict)      - list with dict for each disk in Application
                                            eg: [{
                                                        name:&#34;pvc-1&#34;
                                                        datastore:&#34;storageclass-1&#34;
                                                    }
                                                    {
                                                        name:&#34;pvc-2&#34;
                                                        datastore:&#34;storageclass-2&#34;
                                                    }
                                                ]
            guid                        - GUID of the Application needs to be restored

            new_name                    - New name for the Application to be restored

            esx_host                    - client name where it need to be restored

            name                        - name of the Application to be restored

        &#34;&#34;&#34;

        # Set the new name for the restored Application.
        # If new_name is not given, it restores the Application with same name
        # with suffix Delete.
        vm_names, vm_ids = self._get_vm_ids_and_names_dict_from_browse()
        browse_result = self.vm_files_browse()
        application_id = vm_ids[vm_to_restore]

        # vs metadata from browse result
        _metadata = browse_result[1][(&#39;\\&#39; + vm_to_restore)]
        vs_metadata = _metadata[&#34;advanced_data&#34;][&#34;browseMetaData&#34;][&#34;virtualServerMetaData&#34;]
        if restore_option[&#39;in_place&#39;]:
            folder_path = vs_metadata.get(&#34;inventoryPath&#34;, &#39;&#39;)
            instance_size = vs_metadata.get(&#34;instanceSize&#34;, &#39;&#39;)
        else:
            folder_path = &#39;&#39;
            instance_size = &#39;&#39;
        if restore_option.get(&#39;resourcePoolPath&#39;):
            restore_option[&#39;resourcePoolPath&#39;] = vs_metadata[&#39;resourcePoolPath&#39;]
        if restore_option.get(&#39;datacenter&#39;):
            restore_option[&#39;datacenter&#39;] = restore_option.get(&#39;datacenter&#39;)
        if restore_option.get(&#39;terminationProtected&#39;):
            restore_option[&#39;terminationProtected&#39;] = vs_metadata.get(&#39;terminationProtected&#39;, &#39;&#39;)
        if restore_option.get(&#39;iamRole&#39;):
            restore_option[&#39;iamRole&#39;] = vs_metadata.get(&#39;role&#39;, &#39;&#39;)
        if restore_option.get(&#39;securityGroups&#39;):
            _security_groups = self._find_security_groups(vs_metadata[&#39;networkSecurityGroups&#39;])
            restore_option[&#39;securityGroups&#39;] = _security_groups
        if restore_option.get(&#39;keyPairList&#39;):
            _keypair_list = self._find_keypair_list(vs_metadata[&#39;loginKeyPairs&#39;])
            restore_option[&#39;keyPairList&#39;] = _keypair_list

        # populate restore source item
        restore_option[&#39;paths&#39;].append(&#34;\\&#34; + application_id)
        restore_option[&#39;name&#39;] = vm_to_restore
        restore_option[&#39;guid&#39;] = application_id
        restore_option[&#34;FolderPath&#34;] = folder_path
        restore_option[&#34;ResourcePool&#34;] = &#34;/&#34;

        # populate restore disk and datastore
        vm_disks = []
        new_name = vm_to_restore

        storage_class_map = restore_option.get(&#39;storage_class_map&#39;, None)

        # To populate disk list for each app in case of namespace restore
        if storage_class_map:
            pvc_list = self._get_app_pvc(application_id)
            for pvc in pvc_list:
                storageclass_name = pvc[&#39;storageclass&#39;]
                pvc_name = pvc[&#39;name&#39;]

                # If &#39;datastore&#39; is passed then it&#39;s full app restore, else
                # it is namespace level restore.
                # Namespace level restore can be passed with storage class mapping
                if storageclass_name in storage_class_map:
                    storageclass_name = storage_class_map[storageclass_name]
                _disk_dict = self._disk_dict_pattern(pvc_name, storageclass_name, pvc_name)
                vm_disks.append(_disk_dict)

        restore_option[&#34;disks&#34;] = vm_disks

        self._set_restore_inputs(
            restore_option,
            esx_host=restore_option.get(&#39;esx_host&#39;) or vs_metadata[&#39;esxHost&#39;],
            instance_size=restore_option.get(&#39;instanceSize&#39;, instance_size),
            new_name=restore_option.get(&#39;new_name&#39;, &#34;Delete&#34; + vm_to_restore)
        )

        temp_dict = self._json_restore_advancedRestoreOptions(restore_option)
        self._advanced_restore_option_list.append(temp_dict)

    def full_app_restore_in_place(
            self,
            apps_to_restore=None,
            overwrite=True,
            copy_precedence=0,
            proxy_client=None):
        &#34;&#34;&#34;Restores the FULL Application specified in the input list
            to the location same as the actual location of the Application in Kubernetes cluster.

            Args:
                apps_to_restore     (list)      --  List of applications to restore

                overwrite           (bool)      --  overwrite the existing Applications if exists
                                                    default: True

                copy_precedence     (int)       --  copy precedence value
                                                    default: 0

                proxy_client        (str)       --  proxy client to be used for restore
                                                    default: proxy added in application group/cluster

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if inputs are not of correct type as per definition

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;

        restore_option = {}
        # check input parameters are correct
        if apps_to_restore and not isinstance(apps_to_restore, list):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        if copy_precedence:
            restore_option[&#39;copy_precedence_applicable&#39;] = True

        if proxy_client is not None:
            restore_option[&#39;client&#39;] = proxy_client

        kubernetes_host = self._client_object.client_name

        # set attr for all the option in restore xml from user inputs
        self._set_restore_inputs(
            restore_option,
            vm_to_restore=self._set_vm_to_restore(apps_to_restore),
            in_place=True,
            esx_host=kubernetes_host,
            volume_level_restore=1,
            unconditional_overwrite=overwrite,
            copy_precedence=copy_precedence
        )

        request_json = self._prepare_kubernetes_inplace_restore_json(restore_option)
        return self._process_restore_response(request_json)

    def _prepare_kubernetes_inplace_restore_json(self, restore_option):
        &#34;&#34;&#34;
        Prepare Full Application restore in-place Json with all getters

        Args:
            restore_option - dictionary with all Application restore options

        value:
            restore_option:

                preserve_level           (bool)   - set the preserve level in restore

                unconditional_overwrite  (bool)  - unconditionally overwrite the disk
                                                    in the restore path

                destination_path          (str)- path where the disk needs to be
                                                 restored

                client_name               (str)  - client where the disk needs to be
                                                   restored

                destination_vendor         (str) - vendor id of the Hypervisor

                destination_disktype       (str) - type of disk needs to be restored
                                                   like VHDX,VHD,VMDK

                source_item                 (str)- GUID of Application from which disk needs to
                                                   be restored
                                                   eg:
                                                   \\5F9FA60C-0A89-4BD9-9D02-C5ACB42745EA

                copy_precedence_applicable  (bool)- True if needs copy_precedence to
                                                    be honoured else False

                copy_precedence            (int) - the copy id from which browse and
                                                   restore needs to be performed

                datastore                   (str) - Storage class which the Application PVC needs to be
                                                    restored with

                disks   (list of dict)      - list with dict for each disk in Application
                                                eg: [{
                                                        name:&#34;pvc-1&#34;
                                                        datastore:&#34;storageclass-1&#34;
                                                    }
                                                    {
                                                        name:&#34;pvc-2&#34;
                                                        datastore:&#34;storageclass-2&#34;
                                                    }
                                                ]
                guid                    (str)    - GUID of the Application needs to be restored

                new_name                (str)    - New name for the Application to be restored

                esx_host                (str)    - client name where Application need to be restored

                name                    (str)    - name of the Application to be restored

        returns:
              request_json        -complete json for perfomring Full VM Restore
                                   options

        &#34;&#34;&#34;
        if restore_option is None:
            restore_option = {}
        restore_option[&#39;paths&#39;] = []

        if &#34;destination_vendor&#34; not in restore_option:
            restore_option[&#34;destination_vendor&#34;] = \
                self._backupset_object._instance_object._vendor_id

        if restore_option.get(&#39;copy_precedence&#39;):
            restore_option[&#39;copy_precedence_applicable&#39;] = True

        # set all the restore defaults
        self._set_restore_defaults(restore_option)

        # set the setters
        self._backupset_object._instance_object._restore_association = self._subClientEntity
        self._json_restore_virtualServerRstOption(restore_option)
        self._json_restore_diskLevelVMRestoreOption(restore_option)
        self._json_vcenter_instance(restore_option)

        for _each_vm_to_restore in restore_option[&#39;vm_to_restore&#39;]:
            if not restore_option[&#34;in_place&#34;]:
                if &#39;disk_type&#39; in restore_option:
                    restore_option[&#39;restoreAsManagedVM&#39;] = restore_option[&#39;disk_type&#39;][_each_vm_to_restore]
                if (&#34;restore_new_name&#34; in restore_option and
                        restore_option[&#34;restore_new_name&#34;] is not None):
                    restore_option[&#34;new_name&#34;] = restore_option[&#34;restore_new_name&#34;] + _each_vm_to_restore
                else:
                    restore_option[&#34;new_name&#34;] = &#34;del&#34; + _each_vm_to_restore
            else:
                restore_option[&#34;new_name&#34;] = _each_vm_to_restore
            self.set_advanced_vm_restore_options(_each_vm_to_restore, restore_option)
        # prepare json
        request_json = self._restore_json(restore_option=restore_option)
        self._virtualserver_option_restore_json[&#34;diskLevelVMRestoreOption&#34;][
            &#34;advancedRestoreOptions&#34;] = self._advanced_restore_option_list
        self._advanced_restore_option_list = []
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;virtualServerRstOption&#34;] = self._virtualserver_option_restore_json
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;volumeRstOption&#34;] = self._json_restore_volumeRstOption(
                restore_option)
        return request_json

    def disk_restore(self,
                     application_name,
                     destination_path,
                     disk_name=None,
                     proxy_client=None,
                     **kwargs):
        &#34;&#34;&#34;Restores the disk specified in the input paths list to the same location

            Args:
                application_name             (str)    --  Name of the Application added in subclient content
                                                        whose  disk is selected for restore

                destination_path        (str)    --  Staging (destination) path to restore the
                                                        disk.

                disk_name                 (list)    --  name of the disk which has to be restored
                                                        (only yaml files permitted - enter full
                                                        name of the disk)
                                                        default: None
                proxy_client        (str)    --  Destination proxy client to be used
                                                        default: None

            Kwargs:

                Allows parameters to modify disk restore --

                copy_precedence            (int)    --  SP copy precedence from which browse has to

                media_agent         (str)   -- MA needs to use for disk browse
                    default :Storage policy MA

                snap_proxy          (str)   -- proxy need to be used for disk
                                                    restores from snap
                    default :proxy in instance or subclient

                disk_extension      (str)   -- Extension of disk file (Default: &#39;.yaml&#39;)

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if inputs are not passed in proper expected format

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;

        vm_names, vm_ids = self._get_vm_ids_and_names_dict_from_browse()
        _disk_restore_option = {}

        copy_precedence = kwargs.get(&#34;copy_precedence&#34;, 0)
        disk_extn = kwargs.get(&#34;disk_extension&#34;, &#39;.yaml&#39;)
        unconditional_overwrite = kwargs.get(&#39;unconditional_overwrite&#39;, False)
        show_deleted_files = kwargs.get(&#39;show_deleted_files&#39;, False)

        # Volume level restore values -
        # 4 - Manifest Restore
        volume_level_restore = kwargs.get(&#34;volume_level_restore&#34;, 4)

        if not disk_name:
            disk_name = []
        else:
            disk_extn = self._get_disk_extension(disk_name)

        # check if inputs are correct
        if not (isinstance(application_name, str) and
                isinstance(destination_path, str) and
                isinstance(disk_name, list)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if copy_precedence:
            _disk_restore_option[&#39;copy_precedence_applicable&#39;] = True

        # fetching all disks from the vm
        disk_list, disk_info_dict = self.disk_level_browse(
            &#34;\\&#34; + vm_ids[application_name])

        # Filter out disks with specified extension from disk list
        disk_list = list(filter(lambda name: self._get_disk_extension([name]) == disk_extn, disk_list))
        disk_info_dict = { disk : disk_info_dict[disk] for disk in disk_list }

        if not disk_name:  # if disk names are not provided, restore all disks
            for each_disk_path in disk_list:
                disk_name.append(each_disk_path.split(&#39;\\&#39;)[-1])

        else:  # else, check if the given application has a disk with the list of disks in disk_name.
            for each_disk in disk_name:
                # disk path has GUID in case of files, and application name in case of manifests
                each_disk_path = &#34;\\&#34; + \
                                 (vm_ids[application_name] if volume_level_restore != 4 else application_name) + \
                                 &#34;\\&#34; + each_disk.split(&#34;\\&#34;)[-1]
                if each_disk_path not in disk_list:
                    raise SDKException(&#39;Subclient&#39;, &#39;111&#39;)

        _disk_restore_option[&#34;destination_vendor&#34;] = \
            self._backupset_object._instance_object._vendor_id

        if proxy_client is not None:
            _disk_restore_option[&#39;client&#39;] = proxy_client
        else:
            _disk_restore_option[&#39;client&#39;] = self._backupset_object._instance_object.co_ordinator

        # set Source item List
        src_item_list = []
        for each_disk in disk_name:
            src_item_list.append(&#34;\\&#34; + vm_ids[application_name] + &#34;\\&#34; + each_disk.split(&#34;\\&#34;)[-1])

        _disk_restore_option[&#39;paths&#39;] = src_item_list
        _disk_restore_option[&#39;unconditional_overwrite&#39;] = unconditional_overwrite
        _disk_restore_option[&#39;show_deleted_files&#39;] = show_deleted_files

        # Populate volume level restore options
        _disk_restore_option[&#39;volume_level_restore&#39;] = volume_level_restore

        self._set_restore_inputs(
            _disk_restore_option,
            in_place=False,
            copy_precedence=copy_precedence,
            destination_path=destination_path,
            paths=src_item_list
        )

        request_json = self._prepare_disk_restore_json(_disk_restore_option)
        return self._process_restore_response(request_json)

    def enable_intelli_snap(self, snap_engine_name=None, proxy_options=None, snapshot_engine_id =None):
        &#34;&#34;&#34;Enables Intelli Snap for the subclient.

            Args:
                snap_engine_name    (str)   --  Snap Engine Name

                proxy_options       (str)    -- to set proxy for Kubernetes

                snapshot_engine_id   (int)   -- Snapshot engine id

            Raises:
                SDKException:
                    if failed to enable intelli snap for subclient
        &#34;&#34;&#34;
        if snapshot_engine_id is None:
            snapshot_engine_id = 82

        properties_dict = {
            &#34;isSnapBackupEnabled&#34;: True,
            &#34;snapToTapeSelectedEngine&#34;: {
                &#34;snapShotEngineId&#34;: snapshot_engine_id,
                &#34;snapShotEngineName&#34;: snap_engine_name
            }
        }
        if proxy_options is not None:
            if &#34;snap_proxy&#34; in proxy_options:
                properties_dict[&#34;snapToTapeProxyToUse&#34;] = {
                    &#34;clientName&#34;: proxy_options[&#34;snap_proxy&#34;]
                }

            if &#34;backupcopy_proxy&#34; in proxy_options:
                properties_dict[&#34;useSeparateProxyForSnapToTape&#34;] = True
                properties_dict[&#34;separateProxyForSnapToTape&#34;] = {
                    &#34;clientName&#34;: proxy_options[&#34;backupcopy_proxy&#34;]
                }

            if &#34;use_source_if_proxy_unreachable&#34; in proxy_options:
                properties_dict[&#34;snapToTapeProxyToUseSource&#34;] = True

        self._set_subclient_properties(
            &#34;_commonProperties[&#39;snapCopyInfo&#39;]&#34;, properties_dict)

    def guest_file_restore(self,
                           application_name,
                           destination_path,
                           volume_level_restore,
                           disk_name=None,
                           proxy_client=None,
                           restore_list=None,
                           restore_pvc_guid=None,
                           **kwargs):
        &#34;&#34;&#34;perform Guest file restore of the provided path

        Args:
            application_name_name   (str)   --  Name of the source application
            destination_path        (str)   --  Path at the destination to restore at
            volume_level_restore    (str)   --  Flag to denote volume_level_restore
                                                Accepted values -
                                                6 for restore to PVC
                                                7 for FS Destination restore
            disk_name               (str)   --  Name of the source PVC
            proxy_client            (str)   --  Access node for restore
            restore_list            (str)   --  List of files or folders to restore. Contains Full path
                                                of files or folders relative to PVC mount point.
                                                Eg. if /tmp is the mount point with files or folder /tmp/folder1/file1,
                                                restore list should have format &#39;folder1/file1&#39;
            restore_pvc_guid        (str)   --  strGUID of the target PVC

        Kwargs:
            copy_precedence         (int)   --  To set copy precedence for restore
            disk_extension          (str)   --  Extention of the disk
            unconditional_overwrite (int)   --  To set unconditional overwrite for restore
            show_deleted_files      (bool)  --  Whether to show deleted files in browse
            in_place                (bool)  --  If restore job is inplace

        Raises:
            SDK Exception if
                -inputs are not of correct type as per definition

                -invalid volume_level_restore passed
        &#34;&#34;&#34;
        vm_names, vm_ids = self._get_vm_ids_and_names_dict_from_browse()
        _guest_file_rst_options = {}
        _advanced_restore_options = {}

        copy_precedence = kwargs.get(&#34;copy_precedence&#34;, 0)
        disk_extn = kwargs.get(&#34;disk_extension&#34;, &#39;&#39;)
        overwrite = kwargs.get(&#34;unconditional_overwrite&#34;, 1)
        unconditional_overwrite = kwargs.get(&#39;unconditional_overwrite&#39;, False)
        show_deleted_files = kwargs.get(&#39;show_deleted_files&#39;, False)
        in_place = kwargs.get(&#39;in_place&#39;, False)

        # check if inputs are correct
        if not (isinstance(application_name, str) and
                isinstance(destination_path, str) and
                isinstance(disk_name, str)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        if volume_level_restore not in [6, 7]:
            raise SDKException(&#34;Subclient&#34;, &#34;102&#34;, &#34;Invalid volume level restore type passed&#34;)

        if copy_precedence:
            _guest_file_rst_options[&#39;copy_precedence_applicable&#39;] = True

        # fetching all disks from the application
        disk_list, disk_info_dict = self.disk_level_browse(
            &#34;\\&#34; + vm_ids[application_name])

        # Filter out disks with specified extension from disk list
        disk_list = list(filter(lambda name: self._get_disk_extension([name]) == disk_extn, disk_list))
        disk_info_dict = {disk: disk_info_dict[disk] for disk in disk_list}

        _guest_file_rst_options[&#34;destination_vendor&#34;] = \
            self._backupset_object._instance_object._vendor_id

        if proxy_client is not None:
            _guest_file_rst_options[&#39;client&#39;] = proxy_client
        else:
            _guest_file_rst_options[&#39;client&#39;] = self._backupset_object._instance_object.co_ordinator

        # set Source item List
        src_item_list = []
        for each_item in restore_list:
            item = &#34;\\&#34;.join(each_item.split(&#39;/&#39;))
            src_item_list.append( &#34;\\&#34; + vm_ids[application_name] + &#34;\\&#34; + disk_name + &#34;\\&#34; + item)

        _guest_file_rst_options[&#39;paths&#39;] = src_item_list

        if volume_level_restore == 6:

            if in_place:
                restore_pvc_guid = &#34;{}`PersistentVolumeClaim`{}&#34;.format(
                    vm_ids[application_name].split(&#39;`&#39;)[0], disk_name
                )
                new_name = disk_name

            else:
                new_name = restore_pvc_guid.split(&#39;`&#39;)[-2]
                _advanced_restore_options[&#39;datacenter&#39;] = &#34;none&#34;

            new_guid = restore_pvc_guid

        else:

            new_guid = &#34;{}`PersistentVolumeClaim`{}&#34;.format(
                vm_ids[application_name].split(&#39;`&#39;)[0], disk_name
            )
            new_name = disk_name

        _guest_file_rst_options[&#39;in_place&#39;] = in_place
        _guest_file_rst_options[&#39;volume_level_restore&#39;] = volume_level_restore
        _guest_file_rst_options[&#39;unconditional_overwrite&#39;] = unconditional_overwrite
        _guest_file_rst_options[&#39;show_deleted_files&#39;] = show_deleted_files

        _advanced_restore_options[&#39;new_guid&#39;] = new_guid
        _advanced_restore_options[&#39;new_name&#39;] = new_name
        _advanced_restore_options[&#39;name&#39;] = disk_name
        _advanced_restore_options[&#39;guid&#39;] = vm_ids[application_name]
        _advanced_restore_options[&#39;end_user_vm_restore&#39;] = True

        # set advanced restore options disks
        _disk_dict = self._disk_dict_pattern(disk_name, &#34;&#34;)
        _advanced_restore_options[&#39;disks&#39;] = [_disk_dict]

        advanced_options_dict = self._json_restore_advancedRestoreOptions(_advanced_restore_options)
        self._advanced_restore_option_list.append(advanced_options_dict)

        self._set_restore_inputs(
            _guest_file_rst_options,
            in_place=False,
            copy_precedence=copy_precedence,
            destination_path=destination_path,
            paths=src_item_list
        )

        request_json = self._prepare_disk_restore_json(_guest_file_rst_options)

        # Populate the advancedRestoreOptions section
        self._virtualserver_option_restore_json[&#34;diskLevelVMRestoreOption&#34;][
            &#34;advancedRestoreOptions&#34;] = self._advanced_restore_option_list
        self._advanced_restore_option_list = []

        return self._process_restore_response(request_json)

    def guest_files_browse(
            self,
            application_path=&#39;\\&#39;,
            show_deleted_files=False,
            restore_index=True,
            from_date=0,
            to_date=0,
            copy_precedence=0,
            media_agent=&#34;&#34;):
        &#34;&#34;&#34;Browses the Files and Folders inside a Virtual Machine in the time
           range specified.

            Args:
                application_path    (str)   --  folder path to get the contents
                                                of
                                                default: &#39;\\&#39;;
                                                returns the root of the Backup
                                                content

                show_deleted_files  (bool)  --  include deleted files in the
                                                content or not default: False

                restore_index       (bool)  --  restore index if it is not cached
                                                default: True

                from_date           (int)   --  date to get the contents after
                                                format: dd/MM/YYYY

                                                gets contents from 01/01/1970
                                                if not specified
                                                default: 0

                to_date             (int)  --  date to get the contents before
                                               format: dd/MM/YYYY

                                               gets contents till current day
                                               if not specified
                                               default: 0

                copy_precedence     (int)   --  copy precedence to be used
                                                    for browsing

                media_agent         (str)   --  Browse MA via with Browse has to happen.
                                                It can be MA different than Storage Policy MA

            Returns:
                list - list of all folders or files with their full paths
                       inside the input path

                dict - path along with the details like name, file/folder,
                       size, modification time

            Raises:
                SDKException:
                    if from date value is incorrect

                    if to date value is incorrect

                    if to date is less than from date

                    if failed to browse content

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        return self.browse_in_time(
            vm_path, show_deleted_files, restore_index, False, from_date, to_date, copy_precedence,
            vm_files_browse=False, media_agent=media_agent)

    def _get_apps_in_namespace(self, namespaces):
        &#34;&#34;&#34;Get the list of applications to be restored with the namespace level restore

            Args:
                namespaces      (list)  -   List of namespaces

            Returns:

                  list of applictations to be restored with namespaces
        &#34;&#34;&#34;

        apps_to_restore = []
        namespace_app_dict = {}
        app, app_dict = self.browse()
        for app_path, metadata in app_dict.items():
            app_name = metadata[&#39;name&#39;]
            app_id = metadata[&#39;snap_display_name&#39;]
            app_ns = app_id.split(&#39;`&#39;)[0]
            app_type = app_id.split(&#39;`&#39;)[1]
            if app_type != &#39;Namespace&#39; and app_ns in namespaces:
                apps_to_restore.append(app_name)
                namespace_app_dict[app_name] = app_ns

        return apps_to_restore, namespace_app_dict

    def namespace_restore_out_of_place(
            self,
            namespace_to_restore,
            target_namespace_name={},
            target_cluster_name=None,
            storage_class_map=None,
            overwrite=True,
            copy_precedence=0,
            proxy_client=None
    ):
        &#34;&#34;&#34;Perform a namespace-level restore out-of-place

            Args:

                namespace_to_restore        (list)  --  List of namespaces to restore

                target_namespace_name       (dict)  --  Target namespace name to perform restore at
                                                        Eg. {&#39;namespace1&#39;: &#39;namespace1-rst&#39;}

                target_cluster_name         (str)   --  Name of the target cluster to restore at

                storage_class_map           (dict)  --  Mapping of storage classes for transformation
                                                        Eg. {&#39;rook-ceph-block&#39; : &#39;azurefile&#39;}

                overwrite                   (bool)  --  Overwrite the existing namespace
                                                        Default: true

                copy_precedence             (int)   --  Copy preceedence value

                proxy_client                (str)   --  Name of the proxy client to launch restore
                                                        Default : None (Automatic)
            Returns:

                object - instance of the Job class for this restore job

            Raises:

                SDKException:

                    if inputs are not of correct type as per definition

                    if failed to initialize job

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;

        restore_options = {}

        # Check mandatory input parameters are correct
        if namespace_to_restore and not type(namespace_to_restore) is list:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        # Populating proxy client. It automatically fetches proxy controller from subclient/instance level
        # property if not specified
        if proxy_client is not None:
            restore_options[&#39;client&#39;] = proxy_client

        restore_new_name = {}
        apps_to_restore = []

        if not type(target_namespace_name) is dict:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        for ns in namespace_to_restore:
            if ns not in target_namespace_name:
                target_namespace_name[ns] = ns
        restore_new_name.update(target_namespace_name)

        namespace_apps, namespace_app_map = self._get_apps_in_namespace(namespace_to_restore)
        apps_to_restore.extend(namespace_apps)

        for app in apps_to_restore:
            restore_new_name[app] = app
        apps_to_restore.extend(namespace_to_restore)

        restore_options[&#39;restore_new_name&#39;] = restore_new_name
        restore_options[&#39;namespace_app_map&#39;] = namespace_app_map

        if not target_cluster_name:
            target_cluster_name = self._client_object.client_name

        self.reinitialize_vm_names_browse()

        self._set_restore_inputs(
            restore_options,
            in_place=False,
            vcenter_client=target_cluster_name,
            esx_host=target_cluster_name,
            esx_server=None,
            unconditional_overwrite=overwrite,
            power_on=True,
            vm_to_restore=self._set_vm_to_restore(apps_to_restore),
            disk_option=self._disk_option[&#39;Original&#39;],
            transport_mode=self._transport_mode[&#39;Auto&#39;],
            copy_precedence=copy_precedence,
            volume_level_restore=1,
            source_item=[],
            source_ip=None,
            destination_ip=None,
            network=None,
            storage_class_map=storage_class_map
        )

        request_json = self._prepare_kubernetes_restore_json(restore_options)
        return self._process_restore_response(request_json)

    def namespace_restore_in_place(
            self,
            namespace_to_restore,
            overwrite=True,
            copy_precedence=0,
            proxy_client=None
    ):
        &#34;&#34;&#34;Perform a namespace-level restore in-place

            Args:

                namespace_to_restore        (list)  --  List of namespaces to restore

                overwrite                   (bool)  --  Overwrite the existing namespace
                                                        Default: true

                copy_precedence             (int)   --  Copy preceedence value

                proxy_client                (str)   --  Name of the proxy client to launch restore
                                                        Default : None (Automatic)
            Returns:

                object - instance of the Job class for this restore job

            Raises:

                SDKException:

                    if inputs are not of correct type as per definition

                    if failed to initialize job

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;

        restore_options = {}

        # Check mandatory input parameters are correct
        if namespace_to_restore and not type(namespace_to_restore) is list:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        # Populating proxy client. It automatically fetches proxy controller from subclient/instance level
        # property if not specified
        if proxy_client is not None:
            restore_options[&#39;client&#39;] = proxy_client

        apps_to_restore = []

        namespace_apps, namespace_app_map = self._get_apps_in_namespace(namespace_to_restore)
        apps_to_restore.extend(namespace_apps)
        apps_to_restore.extend(namespace_to_restore)

        client_name = self._client_object.client_name
        self._set_restore_inputs(
            restore_options,
            vm_to_restore=self._set_vm_to_restore(apps_to_restore),
            in_place=True,
            esx_host=client_name,
            esx_server_name=&#34;&#34;,
            volume_level_restore=1,
            unconditional_overwrite=overwrite,
            disk_option=self._disk_option[&#39;Original&#39;],
            transport_mode=self._transport_mode[&#39;Auto&#39;],
            copy_precedence=copy_precedence
        )

        request_json = self._prepare_kubernetes_inplace_restore_json(restore_options)
        return self._process_restore_response(request_json)


class ApplicationGroups(Subclients):

    &#39;&#39;&#39; Class to create Kubernetes Application groups
        Derived from Subclients class
    Args:
        class_object  of Backupset class
     &#39;&#39;&#39;

    def __init__(self, class_object):

        super(ApplicationGroups, self).__init__(class_object)

    def __do_browse(self, browse_type=&#34;Applications&#34;, namespace=None, ns_guid=None):
        &#34;&#34;&#34;Do GET browse request based on the browse type
            Args:
                browse_type     (str)   --  Type of browse (mandatory if namespace is not None)
                                            Accepted values - Namespaces, Applications, Volumes, Labels
                namespace       (str)   --  Namespace to browse

                ns_guid         (str)   --  Namespace GUID of namespace to browse
        &#34;&#34;&#34;

        browse_type_dict = {
            &#34;Namespaces&#34;: &#34;GET_K8S_NS_BROWSE&#34;,
            &#34;Applications&#34;: &#34;GET_K8S_APP_BROWSE&#34;,
            &#34;Volumes&#34;: &#34;GET_K8S_VOLUME_BROWSE&#34;,
            &#34;Labels&#34;: &#34;GET_K8S_LABEL_BROWSE&#34;
        }

        if not (namespace and ns_guid):
            service = browse_type_dict[&#34;Namespaces&#34;]
            parameters = int(self._client_object.client_id)
        else:
            service = browse_type_dict[browse_type]
            parameters = (namespace, ns_guid, int(self._client_object.client_id))

        flag, get_browse = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._services[service] % parameters
        )

        if flag:
            if get_browse and get_browse.json():
                browse_json = get_browse.json()
                if not &#39;inventoryInfo&#39; in browse_json:
                    raise SDKException(
                        &#39;Subclient&#39;,
                        &#39;102&#39;,
                        &#34;Failed to browse cluster content\nContent returned does not have inventoryInfo&#34;
                    )
                else:
                    return browse_json[&#39;inventoryInfo&#39;]
            else:
                raise SDKException(
                    &#39;Subclient&#39;,
                    &#39;102&#39;,
                    &#39;Failed to browse cluster content\nInvalid response&#39;
                )
        else:
            try:
                # If browse fails then append raise HTTP exception to get error reason
                get_browse.raise_for_status()
            except Exception as exp:
                raise SDKException(
                    &#39;Subclient&#39;,
                    &#39;102&#39;,
                    f&#39;Failed to browse cluster content\nError: &#34;{exp}&#34;&#39;
                )

    def __get_children_json(self, app_name, app_type, browse_type, browse_response=None, selector=False):
        &#34;&#34;&#34;Private method to return the json object for the application
            Args:
                app_name    (str)   --  Name of the application
                app_type    (str)   --  Application type (FOLDER/VM/Selector)
                browse_type (str)   --  Browse type of application
                browse_response (str)   --  Browse response from discovery
                selector    (str)   --  If content is a label selector
        &#34;&#34;&#34;

        # JSON format is different in case of applications and selectors
        if selector:
            if browse_type == &#34;Applications&#34;:
                # Casting Applications to Application since `Applications` is not recognized for selectors
                browse_type = &#34;Application&#34;
            if browse_type not in [&#39;Application&#39;, &#39;Namespaces&#39;, &#39;Volumes&#39;]:
                raise SDKException(
                    &#39;Subclient&#39;,
                    &#39;102&#39;,
                    &#39;Invalid browse type for Selector.&#39;
                )

            return {
                &#34;equalsOrNotEquals&#34;: True,
                &#34;displayName&#34;: f&#34;{browse_type}:{app_name}&#34;,
                &#34;value&#34;: f&#34;{browse_type}:{app_name}&#34;,
                &#34;allOrAnyChildren&#34;: True,
                &#34;type&#34;: app_type,
                &#34;name&#34;: app_type
            }

        else:
            for app_item in browse_response:
                # Iterate over each application in browse response to get strGUID of selected app
                if app_item[&#39;name&#39;] == app_name:
                    if browse_type == &#34;Volumes&#34; and app_type == &#34;FOLDER&#34;:
                        app_item[&#39;strGUID&#39;].replace(&#39;Namespace&#39;, &#39;Volumes&#39;)
                    return {
                            &#34;equalsOrNotEquals&#34;: True,
                            &#34;displayName&#34;: app_item[&#39;name&#39;],
                            &#34;allOrAnyChildren&#34;: True,
                            &#34;type&#34;: app_type,
                            &#34;name&#34;: app_item[&#39;strGUID&#39;]
                    }
            else:
                # If for loop completes without returning, then app does not exist in browse
                raise SDKException(
                    &#39;Subclient&#39;,
                    &#39;102&#39;,
                    f&#39;Searched element [{app_name}] not found in browse.&#39;
                )

    def get_children_node(self, content):
        &#34;&#34;&#34;Construct and return the json object for content
            Args:
                content     (list)      --  Content to parse and construct json object
                                            Check create_application_group for usage.
        &#34;&#34;&#34;

        # List of accepted browse types for application and selector
        app_browse_list = [&#39;Applications&#39;, &#39;Labels&#39;, &#39;Volumes&#39;]
        selector_browse_list = [&#39;Applications&#39;, &#39;Application&#39;, &#39;Namespaces&#39;, &#39;Volumes&#39;]

        if not type(content) is list:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;, &#39;Invalid data type for content.&#39;)

        children = []
        for item in content:
            if not type(item) is str:
                raise SDKException(&#39;Subclient&#39;, &#39;101&#39;, &#39;Invalid data type for content.&#39;)

            # Split first `:` to get content type (Application or Selector).
            if item.find(&#39;:&#39;) &lt; 0:
                item = &#39;Application:&#39; + item
            content_type, content_item = item.split(&#39;:&#39;, 1)

            # Split second `:` to get Browse type and app value
            if content_item.find(&#39;:&#39;) &lt; 0:
                content_item = &#39;Applications:&#39; + content_item
            browse_type, app = content_item.split(&#39;:&#39;)

            # Format check
            if (content_type == &#39;Selector&#39; and browse_type not in selector_browse_list) or \
                (content_type == &#39;Application&#39; and browse_type not in app_browse_list):
                raise SDKException(&#39;Subclient&#39;, &#39;101&#39;, &#39;Invalid string format for content.&#39;)

            browse_response = &#34;&#34;
            app_type = &#34;&#34;
            selector = False

            if content_type == &#39;Selector&#39;:
                app_type = &#34;Selector&#34;
                browse_response=None
                app_name = app
                selector = True

            elif content_type == &#39;Application&#39;:
                app_split = app.split(&#39;/&#39;)
                app_name = app_split[-1]
                if len(app_split) &gt; 1:

                    # If split length is &gt; 1, then fetch browse response of application
                    browse_response = self.browse(browse_type=browse_type, namespace=app_split[0])
                    app_type = &#34;VM&#34;
                else:

                    # If split length = 1, then fetch browse response of namespace
                    browse_response = self.browse(browse_type=&#34;Namespaces&#34;)
                    app_type = &#34;FOLDER&#34;
            else:
                raise SDKException(&#39;Subclient&#39;, &#39;101&#39;, &#39;Invalid content type.&#39;)

            children.append(self.__get_children_json(
                    app_name=app_name,
                    app_type=app_type,
                    browse_type=browse_type,
                    browse_response=browse_response,
                    selector=selector
                )
            )

        return children

    def browse(self, browse_type=&#39;Namespaces&#39;, namespace=None):
        &#34;&#34;&#34;Browse cluster content
            Args:
                browse_type     (str)   --  Browse type to perform
                                            Accepted values - Namespaces, Appilcations, Volumes, Labels
                namespace       (str)   --  Namespace to browse in
        &#34;&#34;&#34;

        if browse_type not in [&#34;Namespaces&#34;, &#34;Applications&#34;, &#34;Volumes&#34;, &#34;Labels&#34;]:
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;101&#39;,
                f&#39;Invalid value passed for browse_type [{browse_type}]&#39;
            )

        all_namespaces = self.__do_browse(browse_type=&#34;Namespaces&#34;)

        # If namespace is not passed, or browse_type is Namespaces, return browse response from namespaces
        if browse_type == &#34;Namespaces&#34; or not namespace:
            return all_namespaces

        ns_guid = &#34;&#34;
        for ns in all_namespaces:
            if ns[&#39;name&#39;] == namespace:
                ns_guid = ns[&#39;strGUID&#39;]
                break
        else:
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                f&#34;Could not fetch namespace GUID for namespace [{namespace}]&#34;
            )

        # Encoding ` characters for URL
        ns_guid = ns_guid.replace(&#39;`&#39;, &#39;%60&#39;)

        return self.__do_browse(browse_type=browse_type, namespace=namespace, ns_guid=ns_guid)

    def create_application_group(self,
                                 content,
                                 plan_name=None,
                                 filter=None,
                                 subclient_name=&#34;automation&#34;):

        &#34;&#34;&#34;Create application / Kubernetes Subclient.

            Args:
                client_id               (str)       --  Client id

                content                 (list)      --  Subclient content. Format &#39;ContentType:BrowseType:namespace/app&#39;
                                                        Should be a list of strings with above format.

                                                        Valid ContentType -
                                                            Application, Selector.
                                                            If not specified, default is &#39;Application&#39;
                                                        Valid BrowseType for Application ContentType -
                                                            Applications, Volumes, Labels
                                                            If not specified, default is &#39;Applications&#39;
                                                        Valid BrowseType for Selector ContentType -
                                                            Application, Applications, Volumes, Namespaces
                                                            If not specified, default is &#39;Namespaces&#39;

                                                        Examples -
                                                            1. ns001 --  Format : namespace
                                                            2. ns001/app001 --  Format : namespace/app
                                                            3. Volumes:ns001/pvc001 --  Format : BrowseType:namespace/app
                                                            4. Selector:Namespaces:app=demo -n ns004 --  Format : ContentType:BrowseType:namespace
                                                            5. [&#39;Application:Volumes:nsvol/vol001&#39;, &#39;nsvol02/app1&#39;]
                                                            ...

                plan_name               (str)       --  Plan name

                filter                  (list)      --  filter for subclient content.
                                                        See &#39;content&#39; for format and examples

                subclient_name          (str)       --  Subclient name you want to create Subclient

        &#34;&#34;&#34;

        content_children = []
        filter_children = []

        # Get the json objects for content and filters
        content_children.extend(self.get_children_node(content))
        if filter:
            filter_children.extend(self.get_children_node(filter))

        plan_id = int(self._commcell_object.plans[str(plan_name.lower())])

        app_create_json = {
            &#34;subClientProperties&#34;: {
                &#34;vmContentOperationType&#34;: &#39;ADD&#39;,
                &#34;vmContent&#34;: {
                    &#34;children&#34;: content_children
                },
                &#34;subClientEntity&#34;: {
                    &#34;clientId&#34;: int(self._client_object.client_id),
                    &#34;appName&#34;: &#34;Virtual Server&#34;,
                    &#34;applicationId&#34;: 106,
                    &#34;subclientName&#34;: subclient_name
                },
                &#34;planEntity&#34;: {
                    &#34;planId&#34;: plan_id
                },
                &#34;commonProperties&#34;: {
                    &#34;enableBackup&#34;: True,
                    &#34;numberOfBackupStreams&#34;: 5,
                    &#34;isSnapbackupEnabled&#34;: True,
                    &#34;snapCopyInfo&#34;: {
                        &#34;transportModeForVMWare&#34;: 0,
                        &#34;isSnapBackupEnabled&#34;: False
                    }
                },
                &#34;vsaSubclientProp&#34;: {
                    &#34;autoDetectVMOwner&#34;: False,
                    &#34;quiesceGuestFileSystemAndApplications&#34;: True
                }
            }
        }

        if filter:
            # If filter is passed for subclient, additional flags are added to create subclient json
            app_create_json[&#39;subClientProperties&#39;][&#39;vmFilterOperationType&#39;] = &#39;ADD&#39;
            app_create_json[&#39;subClientProperties&#39;][&#39;vmDiskFilterOperationType&#39;] = &#39;ADD&#39;
            app_create_json[&#39;subClientProperties&#39;][&#39;vmFilter&#39;] = { &#39;children&#39; : filter_children}

        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._services[&#39;ADD_SUBCLIENT&#39;],
                                                           app_create_json)
        if flag == False:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.subclients.virtualserver.kubernetes.ApplicationGroups"><code class="flex name class">
<span>class <span class="ident">ApplicationGroups</span></span>
<span>(</span><span>class_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class to create Kubernetes Application groups
Derived from Subclients class</p>
<h2 id="args">Args</h2>
<p>class_object
of Backupset class</p>
<p>Initialize the Subclients object for the given backupset.</p>
<h2 id="args_1">Args</h2>
<p>class_object
(object)
&ndash;
instance of the Agent / Instance / Backupset class</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the Subclients class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if class object is not an instance of Agent / Instance / Backupset</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/kubernetes.py#L1331-L1651" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class ApplicationGroups(Subclients):

    &#39;&#39;&#39; Class to create Kubernetes Application groups
        Derived from Subclients class
    Args:
        class_object  of Backupset class
     &#39;&#39;&#39;

    def __init__(self, class_object):

        super(ApplicationGroups, self).__init__(class_object)

    def __do_browse(self, browse_type=&#34;Applications&#34;, namespace=None, ns_guid=None):
        &#34;&#34;&#34;Do GET browse request based on the browse type
            Args:
                browse_type     (str)   --  Type of browse (mandatory if namespace is not None)
                                            Accepted values - Namespaces, Applications, Volumes, Labels
                namespace       (str)   --  Namespace to browse

                ns_guid         (str)   --  Namespace GUID of namespace to browse
        &#34;&#34;&#34;

        browse_type_dict = {
            &#34;Namespaces&#34;: &#34;GET_K8S_NS_BROWSE&#34;,
            &#34;Applications&#34;: &#34;GET_K8S_APP_BROWSE&#34;,
            &#34;Volumes&#34;: &#34;GET_K8S_VOLUME_BROWSE&#34;,
            &#34;Labels&#34;: &#34;GET_K8S_LABEL_BROWSE&#34;
        }

        if not (namespace and ns_guid):
            service = browse_type_dict[&#34;Namespaces&#34;]
            parameters = int(self._client_object.client_id)
        else:
            service = browse_type_dict[browse_type]
            parameters = (namespace, ns_guid, int(self._client_object.client_id))

        flag, get_browse = self._cvpysdk_object.make_request(
            &#39;GET&#39;, self._services[service] % parameters
        )

        if flag:
            if get_browse and get_browse.json():
                browse_json = get_browse.json()
                if not &#39;inventoryInfo&#39; in browse_json:
                    raise SDKException(
                        &#39;Subclient&#39;,
                        &#39;102&#39;,
                        &#34;Failed to browse cluster content\nContent returned does not have inventoryInfo&#34;
                    )
                else:
                    return browse_json[&#39;inventoryInfo&#39;]
            else:
                raise SDKException(
                    &#39;Subclient&#39;,
                    &#39;102&#39;,
                    &#39;Failed to browse cluster content\nInvalid response&#39;
                )
        else:
            try:
                # If browse fails then append raise HTTP exception to get error reason
                get_browse.raise_for_status()
            except Exception as exp:
                raise SDKException(
                    &#39;Subclient&#39;,
                    &#39;102&#39;,
                    f&#39;Failed to browse cluster content\nError: &#34;{exp}&#34;&#39;
                )

    def __get_children_json(self, app_name, app_type, browse_type, browse_response=None, selector=False):
        &#34;&#34;&#34;Private method to return the json object for the application
            Args:
                app_name    (str)   --  Name of the application
                app_type    (str)   --  Application type (FOLDER/VM/Selector)
                browse_type (str)   --  Browse type of application
                browse_response (str)   --  Browse response from discovery
                selector    (str)   --  If content is a label selector
        &#34;&#34;&#34;

        # JSON format is different in case of applications and selectors
        if selector:
            if browse_type == &#34;Applications&#34;:
                # Casting Applications to Application since `Applications` is not recognized for selectors
                browse_type = &#34;Application&#34;
            if browse_type not in [&#39;Application&#39;, &#39;Namespaces&#39;, &#39;Volumes&#39;]:
                raise SDKException(
                    &#39;Subclient&#39;,
                    &#39;102&#39;,
                    &#39;Invalid browse type for Selector.&#39;
                )

            return {
                &#34;equalsOrNotEquals&#34;: True,
                &#34;displayName&#34;: f&#34;{browse_type}:{app_name}&#34;,
                &#34;value&#34;: f&#34;{browse_type}:{app_name}&#34;,
                &#34;allOrAnyChildren&#34;: True,
                &#34;type&#34;: app_type,
                &#34;name&#34;: app_type
            }

        else:
            for app_item in browse_response:
                # Iterate over each application in browse response to get strGUID of selected app
                if app_item[&#39;name&#39;] == app_name:
                    if browse_type == &#34;Volumes&#34; and app_type == &#34;FOLDER&#34;:
                        app_item[&#39;strGUID&#39;].replace(&#39;Namespace&#39;, &#39;Volumes&#39;)
                    return {
                            &#34;equalsOrNotEquals&#34;: True,
                            &#34;displayName&#34;: app_item[&#39;name&#39;],
                            &#34;allOrAnyChildren&#34;: True,
                            &#34;type&#34;: app_type,
                            &#34;name&#34;: app_item[&#39;strGUID&#39;]
                    }
            else:
                # If for loop completes without returning, then app does not exist in browse
                raise SDKException(
                    &#39;Subclient&#39;,
                    &#39;102&#39;,
                    f&#39;Searched element [{app_name}] not found in browse.&#39;
                )

    def get_children_node(self, content):
        &#34;&#34;&#34;Construct and return the json object for content
            Args:
                content     (list)      --  Content to parse and construct json object
                                            Check create_application_group for usage.
        &#34;&#34;&#34;

        # List of accepted browse types for application and selector
        app_browse_list = [&#39;Applications&#39;, &#39;Labels&#39;, &#39;Volumes&#39;]
        selector_browse_list = [&#39;Applications&#39;, &#39;Application&#39;, &#39;Namespaces&#39;, &#39;Volumes&#39;]

        if not type(content) is list:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;, &#39;Invalid data type for content.&#39;)

        children = []
        for item in content:
            if not type(item) is str:
                raise SDKException(&#39;Subclient&#39;, &#39;101&#39;, &#39;Invalid data type for content.&#39;)

            # Split first `:` to get content type (Application or Selector).
            if item.find(&#39;:&#39;) &lt; 0:
                item = &#39;Application:&#39; + item
            content_type, content_item = item.split(&#39;:&#39;, 1)

            # Split second `:` to get Browse type and app value
            if content_item.find(&#39;:&#39;) &lt; 0:
                content_item = &#39;Applications:&#39; + content_item
            browse_type, app = content_item.split(&#39;:&#39;)

            # Format check
            if (content_type == &#39;Selector&#39; and browse_type not in selector_browse_list) or \
                (content_type == &#39;Application&#39; and browse_type not in app_browse_list):
                raise SDKException(&#39;Subclient&#39;, &#39;101&#39;, &#39;Invalid string format for content.&#39;)

            browse_response = &#34;&#34;
            app_type = &#34;&#34;
            selector = False

            if content_type == &#39;Selector&#39;:
                app_type = &#34;Selector&#34;
                browse_response=None
                app_name = app
                selector = True

            elif content_type == &#39;Application&#39;:
                app_split = app.split(&#39;/&#39;)
                app_name = app_split[-1]
                if len(app_split) &gt; 1:

                    # If split length is &gt; 1, then fetch browse response of application
                    browse_response = self.browse(browse_type=browse_type, namespace=app_split[0])
                    app_type = &#34;VM&#34;
                else:

                    # If split length = 1, then fetch browse response of namespace
                    browse_response = self.browse(browse_type=&#34;Namespaces&#34;)
                    app_type = &#34;FOLDER&#34;
            else:
                raise SDKException(&#39;Subclient&#39;, &#39;101&#39;, &#39;Invalid content type.&#39;)

            children.append(self.__get_children_json(
                    app_name=app_name,
                    app_type=app_type,
                    browse_type=browse_type,
                    browse_response=browse_response,
                    selector=selector
                )
            )

        return children

    def browse(self, browse_type=&#39;Namespaces&#39;, namespace=None):
        &#34;&#34;&#34;Browse cluster content
            Args:
                browse_type     (str)   --  Browse type to perform
                                            Accepted values - Namespaces, Appilcations, Volumes, Labels
                namespace       (str)   --  Namespace to browse in
        &#34;&#34;&#34;

        if browse_type not in [&#34;Namespaces&#34;, &#34;Applications&#34;, &#34;Volumes&#34;, &#34;Labels&#34;]:
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;101&#39;,
                f&#39;Invalid value passed for browse_type [{browse_type}]&#39;
            )

        all_namespaces = self.__do_browse(browse_type=&#34;Namespaces&#34;)

        # If namespace is not passed, or browse_type is Namespaces, return browse response from namespaces
        if browse_type == &#34;Namespaces&#34; or not namespace:
            return all_namespaces

        ns_guid = &#34;&#34;
        for ns in all_namespaces:
            if ns[&#39;name&#39;] == namespace:
                ns_guid = ns[&#39;strGUID&#39;]
                break
        else:
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                f&#34;Could not fetch namespace GUID for namespace [{namespace}]&#34;
            )

        # Encoding ` characters for URL
        ns_guid = ns_guid.replace(&#39;`&#39;, &#39;%60&#39;)

        return self.__do_browse(browse_type=browse_type, namespace=namespace, ns_guid=ns_guid)

    def create_application_group(self,
                                 content,
                                 plan_name=None,
                                 filter=None,
                                 subclient_name=&#34;automation&#34;):

        &#34;&#34;&#34;Create application / Kubernetes Subclient.

            Args:
                client_id               (str)       --  Client id

                content                 (list)      --  Subclient content. Format &#39;ContentType:BrowseType:namespace/app&#39;
                                                        Should be a list of strings with above format.

                                                        Valid ContentType -
                                                            Application, Selector.
                                                            If not specified, default is &#39;Application&#39;
                                                        Valid BrowseType for Application ContentType -
                                                            Applications, Volumes, Labels
                                                            If not specified, default is &#39;Applications&#39;
                                                        Valid BrowseType for Selector ContentType -
                                                            Application, Applications, Volumes, Namespaces
                                                            If not specified, default is &#39;Namespaces&#39;

                                                        Examples -
                                                            1. ns001 --  Format : namespace
                                                            2. ns001/app001 --  Format : namespace/app
                                                            3. Volumes:ns001/pvc001 --  Format : BrowseType:namespace/app
                                                            4. Selector:Namespaces:app=demo -n ns004 --  Format : ContentType:BrowseType:namespace
                                                            5. [&#39;Application:Volumes:nsvol/vol001&#39;, &#39;nsvol02/app1&#39;]
                                                            ...

                plan_name               (str)       --  Plan name

                filter                  (list)      --  filter for subclient content.
                                                        See &#39;content&#39; for format and examples

                subclient_name          (str)       --  Subclient name you want to create Subclient

        &#34;&#34;&#34;

        content_children = []
        filter_children = []

        # Get the json objects for content and filters
        content_children.extend(self.get_children_node(content))
        if filter:
            filter_children.extend(self.get_children_node(filter))

        plan_id = int(self._commcell_object.plans[str(plan_name.lower())])

        app_create_json = {
            &#34;subClientProperties&#34;: {
                &#34;vmContentOperationType&#34;: &#39;ADD&#39;,
                &#34;vmContent&#34;: {
                    &#34;children&#34;: content_children
                },
                &#34;subClientEntity&#34;: {
                    &#34;clientId&#34;: int(self._client_object.client_id),
                    &#34;appName&#34;: &#34;Virtual Server&#34;,
                    &#34;applicationId&#34;: 106,
                    &#34;subclientName&#34;: subclient_name
                },
                &#34;planEntity&#34;: {
                    &#34;planId&#34;: plan_id
                },
                &#34;commonProperties&#34;: {
                    &#34;enableBackup&#34;: True,
                    &#34;numberOfBackupStreams&#34;: 5,
                    &#34;isSnapbackupEnabled&#34;: True,
                    &#34;snapCopyInfo&#34;: {
                        &#34;transportModeForVMWare&#34;: 0,
                        &#34;isSnapBackupEnabled&#34;: False
                    }
                },
                &#34;vsaSubclientProp&#34;: {
                    &#34;autoDetectVMOwner&#34;: False,
                    &#34;quiesceGuestFileSystemAndApplications&#34;: True
                }
            }
        }

        if filter:
            # If filter is passed for subclient, additional flags are added to create subclient json
            app_create_json[&#39;subClientProperties&#39;][&#39;vmFilterOperationType&#39;] = &#39;ADD&#39;
            app_create_json[&#39;subClientProperties&#39;][&#39;vmDiskFilterOperationType&#39;] = &#39;ADD&#39;
            app_create_json[&#39;subClientProperties&#39;][&#39;vmFilter&#39;] = { &#39;children&#39; : filter_children}

        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._services[&#39;ADD_SUBCLIENT&#39;],
                                                           app_create_json)
        if flag == False:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.subclient.Subclients" href="../../subclient.html#cvpysdk.subclient.Subclients">Subclients</a></li>
</ul>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.subclients.virtualserver.kubernetes.ApplicationGroups.browse"><code class="name flex">
<span>def <span class="ident">browse</span></span>(<span>self, browse_type='Namespaces', namespace=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Browse cluster content</p>
<h2 id="args">Args</h2>
<p>browse_type
(str)
&ndash;
Browse type to perform
Accepted values - Namespaces, Appilcations, Volumes, Labels
namespace
(str)
&ndash;
Namespace to browse in</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/kubernetes.py#L1522-L1558" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def browse(self, browse_type=&#39;Namespaces&#39;, namespace=None):
    &#34;&#34;&#34;Browse cluster content
        Args:
            browse_type     (str)   --  Browse type to perform
                                        Accepted values - Namespaces, Appilcations, Volumes, Labels
            namespace       (str)   --  Namespace to browse in
    &#34;&#34;&#34;

    if browse_type not in [&#34;Namespaces&#34;, &#34;Applications&#34;, &#34;Volumes&#34;, &#34;Labels&#34;]:
        raise SDKException(
            &#39;Subclient&#39;,
            &#39;101&#39;,
            f&#39;Invalid value passed for browse_type [{browse_type}]&#39;
        )

    all_namespaces = self.__do_browse(browse_type=&#34;Namespaces&#34;)

    # If namespace is not passed, or browse_type is Namespaces, return browse response from namespaces
    if browse_type == &#34;Namespaces&#34; or not namespace:
        return all_namespaces

    ns_guid = &#34;&#34;
    for ns in all_namespaces:
        if ns[&#39;name&#39;] == namespace:
            ns_guid = ns[&#39;strGUID&#39;]
            break
    else:
        raise SDKException(
            &#39;Subclient&#39;,
            &#39;102&#39;,
            f&#34;Could not fetch namespace GUID for namespace [{namespace}]&#34;
        )

    # Encoding ` characters for URL
    ns_guid = ns_guid.replace(&#39;`&#39;, &#39;%60&#39;)

    return self.__do_browse(browse_type=browse_type, namespace=namespace, ns_guid=ns_guid)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.virtualserver.kubernetes.ApplicationGroups.create_application_group"><code class="name flex">
<span>def <span class="ident">create_application_group</span></span>(<span>self, content, plan_name=None, filter=None, subclient_name='automation')</span>
</code></dt>
<dd>
<div class="desc"><p>Create application / Kubernetes Subclient.</p>
<h2 id="args">Args</h2>
<p>client_id
(str)
&ndash;
Client id</p>
<p>content
(list)
&ndash;
Subclient content. Format 'ContentType:BrowseType:namespace/app'
Should be a list of strings with above format.</p>
<pre><code>                                    Valid ContentType -
                                        Application, Selector.
                                        If not specified, default is 'Application'
                                    Valid BrowseType for Application ContentType -
                                        Applications, Volumes, Labels
                                        If not specified, default is 'Applications'
                                    Valid BrowseType for Selector ContentType -
                                        Application, Applications, Volumes, Namespaces
                                        If not specified, default is 'Namespaces'

                                    Examples -
                                        1. ns001 --  Format : namespace
                                        2. ns001/app001 --  Format : namespace/app
                                        3. Volumes:ns001/pvc001 --  Format : BrowseType:namespace/app
                                        4. Selector:Namespaces:app=demo -n ns004 --  Format : ContentType:BrowseType:namespace
                                        5. ['Application:Volumes:nsvol/vol001', 'nsvol02/app1']
                                        ...
</code></pre>
<p>plan_name
(str)
&ndash;
Plan name</p>
<p>filter
(list)
&ndash;
filter for subclient content.
See 'content' for format and examples</p>
<p>subclient_name
(str)
&ndash;
Subclient name you want to create Subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/kubernetes.py#L1560-L1651" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def create_application_group(self,
                             content,
                             plan_name=None,
                             filter=None,
                             subclient_name=&#34;automation&#34;):

    &#34;&#34;&#34;Create application / Kubernetes Subclient.

        Args:
            client_id               (str)       --  Client id

            content                 (list)      --  Subclient content. Format &#39;ContentType:BrowseType:namespace/app&#39;
                                                    Should be a list of strings with above format.

                                                    Valid ContentType -
                                                        Application, Selector.
                                                        If not specified, default is &#39;Application&#39;
                                                    Valid BrowseType for Application ContentType -
                                                        Applications, Volumes, Labels
                                                        If not specified, default is &#39;Applications&#39;
                                                    Valid BrowseType for Selector ContentType -
                                                        Application, Applications, Volumes, Namespaces
                                                        If not specified, default is &#39;Namespaces&#39;

                                                    Examples -
                                                        1. ns001 --  Format : namespace
                                                        2. ns001/app001 --  Format : namespace/app
                                                        3. Volumes:ns001/pvc001 --  Format : BrowseType:namespace/app
                                                        4. Selector:Namespaces:app=demo -n ns004 --  Format : ContentType:BrowseType:namespace
                                                        5. [&#39;Application:Volumes:nsvol/vol001&#39;, &#39;nsvol02/app1&#39;]
                                                        ...

            plan_name               (str)       --  Plan name

            filter                  (list)      --  filter for subclient content.
                                                    See &#39;content&#39; for format and examples

            subclient_name          (str)       --  Subclient name you want to create Subclient

    &#34;&#34;&#34;

    content_children = []
    filter_children = []

    # Get the json objects for content and filters
    content_children.extend(self.get_children_node(content))
    if filter:
        filter_children.extend(self.get_children_node(filter))

    plan_id = int(self._commcell_object.plans[str(plan_name.lower())])

    app_create_json = {
        &#34;subClientProperties&#34;: {
            &#34;vmContentOperationType&#34;: &#39;ADD&#39;,
            &#34;vmContent&#34;: {
                &#34;children&#34;: content_children
            },
            &#34;subClientEntity&#34;: {
                &#34;clientId&#34;: int(self._client_object.client_id),
                &#34;appName&#34;: &#34;Virtual Server&#34;,
                &#34;applicationId&#34;: 106,
                &#34;subclientName&#34;: subclient_name
            },
            &#34;planEntity&#34;: {
                &#34;planId&#34;: plan_id
            },
            &#34;commonProperties&#34;: {
                &#34;enableBackup&#34;: True,
                &#34;numberOfBackupStreams&#34;: 5,
                &#34;isSnapbackupEnabled&#34;: True,
                &#34;snapCopyInfo&#34;: {
                    &#34;transportModeForVMWare&#34;: 0,
                    &#34;isSnapBackupEnabled&#34;: False
                }
            },
            &#34;vsaSubclientProp&#34;: {
                &#34;autoDetectVMOwner&#34;: False,
                &#34;quiesceGuestFileSystemAndApplications&#34;: True
            }
        }
    }

    if filter:
        # If filter is passed for subclient, additional flags are added to create subclient json
        app_create_json[&#39;subClientProperties&#39;][&#39;vmFilterOperationType&#39;] = &#39;ADD&#39;
        app_create_json[&#39;subClientProperties&#39;][&#39;vmDiskFilterOperationType&#39;] = &#39;ADD&#39;
        app_create_json[&#39;subClientProperties&#39;][&#39;vmFilter&#39;] = { &#39;children&#39; : filter_children}

    flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._services[&#39;ADD_SUBCLIENT&#39;],
                                                       app_create_json)
    if flag == False:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.virtualserver.kubernetes.ApplicationGroups.get_children_node"><code class="name flex">
<span>def <span class="ident">get_children_node</span></span>(<span>self, content)</span>
</code></dt>
<dd>
<div class="desc"><p>Construct and return the json object for content</p>
<h2 id="args">Args</h2>
<p>content
(list)
&ndash;
Content to parse and construct json object
Check create_application_group for usage.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/kubernetes.py#L1451-L1520" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_children_node(self, content):
    &#34;&#34;&#34;Construct and return the json object for content
        Args:
            content     (list)      --  Content to parse and construct json object
                                        Check create_application_group for usage.
    &#34;&#34;&#34;

    # List of accepted browse types for application and selector
    app_browse_list = [&#39;Applications&#39;, &#39;Labels&#39;, &#39;Volumes&#39;]
    selector_browse_list = [&#39;Applications&#39;, &#39;Application&#39;, &#39;Namespaces&#39;, &#39;Volumes&#39;]

    if not type(content) is list:
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;, &#39;Invalid data type for content.&#39;)

    children = []
    for item in content:
        if not type(item) is str:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;, &#39;Invalid data type for content.&#39;)

        # Split first `:` to get content type (Application or Selector).
        if item.find(&#39;:&#39;) &lt; 0:
            item = &#39;Application:&#39; + item
        content_type, content_item = item.split(&#39;:&#39;, 1)

        # Split second `:` to get Browse type and app value
        if content_item.find(&#39;:&#39;) &lt; 0:
            content_item = &#39;Applications:&#39; + content_item
        browse_type, app = content_item.split(&#39;:&#39;)

        # Format check
        if (content_type == &#39;Selector&#39; and browse_type not in selector_browse_list) or \
            (content_type == &#39;Application&#39; and browse_type not in app_browse_list):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;, &#39;Invalid string format for content.&#39;)

        browse_response = &#34;&#34;
        app_type = &#34;&#34;
        selector = False

        if content_type == &#39;Selector&#39;:
            app_type = &#34;Selector&#34;
            browse_response=None
            app_name = app
            selector = True

        elif content_type == &#39;Application&#39;:
            app_split = app.split(&#39;/&#39;)
            app_name = app_split[-1]
            if len(app_split) &gt; 1:

                # If split length is &gt; 1, then fetch browse response of application
                browse_response = self.browse(browse_type=browse_type, namespace=app_split[0])
                app_type = &#34;VM&#34;
            else:

                # If split length = 1, then fetch browse response of namespace
                browse_response = self.browse(browse_type=&#34;Namespaces&#34;)
                app_type = &#34;FOLDER&#34;
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;, &#39;Invalid content type.&#39;)

        children.append(self.__get_children_json(
                app_name=app_name,
                app_type=app_type,
                browse_type=browse_type,
                browse_response=browse_response,
                selector=selector
            )
        )

    return children</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.subclient.Subclients" href="../../subclient.html#cvpysdk.subclient.Subclients">Subclients</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.subclient.Subclients.add" href="../../subclient.html#cvpysdk.subclient.Subclients.add">add</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclients.add_mysql_subclient" href="../../subclient.html#cvpysdk.subclient.Subclients.add_mysql_subclient">add_mysql_subclient</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclients.add_onedrive_subclient" href="../../subclient.html#cvpysdk.subclient.Subclients.add_onedrive_subclient">add_onedrive_subclient</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclients.add_oracle_logical_dump_subclient" href="../../subclient.html#cvpysdk.subclient.Subclients.add_oracle_logical_dump_subclient">add_oracle_logical_dump_subclient</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclients.add_postgresql_subclient" href="../../subclient.html#cvpysdk.subclient.Subclients.add_postgresql_subclient">add_postgresql_subclient</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclients.add_virtual_server_subclient" href="../../subclient.html#cvpysdk.subclient.Subclients.add_virtual_server_subclient">add_virtual_server_subclient</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclients.all_subclients" href="../../subclient.html#cvpysdk.subclient.Subclients.all_subclients">all_subclients</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclients.default_subclient" href="../../subclient.html#cvpysdk.subclient.Subclients.default_subclient">default_subclient</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclients.delete" href="../../subclient.html#cvpysdk.subclient.Subclients.delete">delete</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclients.get" href="../../subclient.html#cvpysdk.subclient.Subclients.get">get</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclients.has_subclient" href="../../subclient.html#cvpysdk.subclient.Subclients.has_subclient">has_subclient</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclients.refresh" href="../../subclient.html#cvpysdk.subclient.Subclients.refresh">refresh</a></code></li>
</ul>
</li>
</ul>
</dd>
<dt id="cvpysdk.subclients.virtualserver.kubernetes.KubernetesVirtualServerSubclient"><code class="flex name class">
<span>class <span class="ident">KubernetesVirtualServerSubclient</span></span>
<span>(</span><span>backupset_object, subclient_name, subclient_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Derived class from VirtualServerSubclient Base class.
This represents a Kubernetes virtual server subclient,
and can perform restore operations on only that subclient.</p>
<p>Initialize the Instance object for the given Virtual Server instance.
Args
class_object (backupset_object, subclient_name, subclient_id)
&ndash;
instance of the
backupset class, subclient name, subclient id</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/kubernetes.py#L88-L1328" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class KubernetesVirtualServerSubclient(VirtualServerSubclient):
    &#34;&#34;&#34;Derived class from VirtualServerSubclient Base class.
       This represents a Kubernetes virtual server subclient,
       and can perform restore operations on only that subclient.

    &#34;&#34;&#34;

    def __init__(self, backupset_object, subclient_name, subclient_id=None):
        &#34;&#34;&#34;Initialize the Instance object for the given Virtual Server instance.
        Args
        class_object (backupset_object, subclient_name, subclient_id)  --  instance of the
                                         backupset class, subclient name, subclient id

        &#34;&#34;&#34;
        super(KubernetesVirtualServerSubclient, self).__init__(
            backupset_object, subclient_name, subclient_id)
        self.diskExtension = [&#34;.yaml&#34;]

        self._disk_option = {
            &#39;Original&#39;: 0,
            &#39;Thick Lazy Zero&#39;: 1,
            &#39;Thin&#39;: 2,
            &#39;Thick Eager Zero&#39;: 3
        }

        self._transport_mode = {
            &#39;Auto&#39;: 0,
            &#39;SAN&#39;: 1,
            &#39;Hot Add&#39;: 2,
            &#39;NBD&#39;: 5,
            &#39;NBD SSL&#39;: 4
        }

    def full_app_restore_out_of_place(
            self,
            apps_to_restore,
            restore_namespace,
            restored_app_name=None,
            kubernetes_client=None,
            storage_class=None,
            overwrite=True,
            copy_precedence=0,
            proxy_client=None,
    ):
        &#34;&#34;&#34;Restores the FULL Application specified in the input list
            to the provided Kubernetes client at the specified namespace with storage class.
            If the provided client name is none then it restores the Full Application
            to the source Kubernetes client and corresponding namespace and storage class.

            Args:
                apps_to_restore         (list)  --  List of Applications that is to be restored

                restored_app_name       (dict)  --  Dictionary mapping new name of Applications

                kubernetes_client       (str)   --  Name of the Kubernetes client where the Application should be restored
                                                    Restores to the source Kubernetes client if this value is not specified

                storage_class           (str)   --  Storage class for the PVC to be restored with.
                                                    Uses source storage class if not specified.

                restore_namespace       (str)   --  Target namespace where Applications are to be restored

                overwrite               (bool)  --  overwrite the existing Applications if exists
                                                    default: True

                copy_precedence          (int)  --  copy precedence value
                                                      default: 0

                proxy_client              (str)    --  destination proxy client

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if inputs are not of correct type as per definition

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;

        restore_option = {}

        # check mandatory input parameters are correct
        if apps_to_restore and not isinstance(apps_to_restore, list):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        # populating proxy client. It assumes the proxy controller added in instance
        # properties if not specified
        if proxy_client is not None:
            restore_option[&#39;client&#39;] = proxy_client

        if restored_app_name:
            if not(isinstance(apps_to_restore, list) or
                   isinstance(restored_app_name, dict)):
                raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
            restore_option[&#39;restore_new_name&#39;] = restored_app_name

        if not kubernetes_client:
            kubernetes_client = self._client_object.client_name

        restore_option_copy = restore_option.copy()

        self._set_restore_inputs(
            restore_option,
            in_place=False,
            vcenter_client=kubernetes_client,
            datastore=storage_class,
            esx_host=kubernetes_client,
            datacenter=restore_namespace,
            unconditional_overwrite=overwrite,
            vm_to_restore=self._set_vm_to_restore(apps_to_restore),
            copy_precedence=copy_precedence,
            volume_level_restore=1,
            source_item=[]
        )

        request_json = self._prepare_kubernetes_restore_json(restore_option)
        return self._process_restore_response(request_json)


    def _prepare_kubernetes_restore_json(self, restore_option):
        &#34;&#34;&#34;
        Prepare Full Application restore Json with all getters

        Args:
            restore_option - dictionary with all Application restore options

        value:
            restore_option:

                preserve_level           (bool)   - set the preserve level in restore

                unconditional_overwrite  (bool)  - unconditionally overwrite the disk
                                                    in the restore path

                destination_path          (str)- path where the disk needs to be
                                                 restored

                client_name               (str)  - client where the disk needs to be
                                                   restored

                destination_vendor         (str) - vendor id of the Hypervisor

                destination_disktype       (str) - type of disk needs to be restored
                                                   like VHDX,VHD,VMDK

                source_item                 (str)- GUID of Application from which disk needs to
                                                   be restored
                                                   eg:
                                                   \\5F9FA60C-0A89-4BD9-9D02-C5ACB42745EA

                copy_precedence_applicable  (bool)- True if needs copy_precedence to
                                                    be honoured else False

                copy_precedence            (int) - the copy id from which browse and
                                                   restore needs to be performed

                datastore                   (str) - Storage class which the Application PVC needs to be
                                                    restored with

                disks   (list of dict)      - list with dict for each disk in Application
                                                eg: [{
                                                        name:&#34;pvc-1&#34;
                                                        datastore:&#34;storageclass-1&#34;
                                                    }
                                                    {
                                                        name:&#34;pvc-2&#34;
                                                        datastore:&#34;storageclass-2&#34;
                                                    }
                                                ]
                guid                    (str)    - GUID of the Application needs to be restored

                new_name                (str)    - New name for the Application to be restored

                esx_host                (str)    - client name where Application need to be restored

                name                    (str)    - name of the Application to be restored

        returns:
              request_json        -complete json for perfomring Full Application Restore
                                   options

        &#34;&#34;&#34;
        if restore_option is None:
            restore_option = {}
        restore_option[&#39;paths&#39;] = []

        if &#34;destination_vendor&#34; not in restore_option:
            restore_option[&#34;destination_vendor&#34;] = \
                self._backupset_object._instance_object._vendor_id

        if restore_option[&#39;copy_precedence&#39;]:
            restore_option[&#39;copy_precedence_applicable&#39;] = True

        # set all the restore defaults
        self._set_restore_defaults(restore_option)

        # set the setters
        self._backupset_object._instance_object._restore_association = self._subClientEntity
        self._json_restore_virtualServerRstOption(restore_option)
        self._json_restore_diskLevelVMRestoreOption(restore_option)
        self._json_vcenter_instance(restore_option)

        _new_name_dict = restore_option.get(&#39;restore_new_name&#39;, {})
        for _each_vm_to_restore in restore_option[&#39;vm_to_restore&#39;]:
            restore_option[&#34;new_name&#34;] = _new_name_dict.get(_each_vm_to_restore, _each_vm_to_restore)
            namespace_app_map = restore_option.get(&#39;namespace_app_map&#39;, {})
            datacenter = restore_option.get(&#39;datacenter&#39;, None)

            if not namespace_app_map:
                # FOR : Full Application Restores Restores
                # If namespace_app_map is not passed the it is a full application restore
                # so &#39;datacenter&#39; should be passed. Nothing to do here
                pass

            elif _each_vm_to_restore in namespace_app_map:
                # FOR : Namespace Level Restore
                # If _each_vm_to_restore is in namespace_app_map which means it&#39;s an application
                # and not a namespace, so we need to pass &#39;datacenter&#39; to advanced restore options

                app_ns = namespace_app_map.get(_each_vm_to_restore)

                # Getting the target namespace if restoring to a new namespace name
                target_ns = _new_name_dict[app_ns]
                restore_option[&#39;datacenter&#39;] = target_ns
            else:
                # FOR : Namespace Level Restore
                # If it&#39;s a namespace, then there is no &#39;datacenter&#39; needed so pop it
                if &#39;datacenter&#39; in restore_option:
                    restore_option.pop(&#39;datacenter&#39;)
            self.set_advanced_vm_restore_options(_each_vm_to_restore, restore_option)
        # prepare json
        request_json = self._restore_json(restore_option=restore_option)
        self._virtualserver_option_restore_json[&#34;diskLevelVMRestoreOption&#34;][
            &#34;advancedRestoreOptions&#34;] = self._advanced_restore_option_list
        self._advanced_restore_option_list = []
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;virtualServerRstOption&#34;] = self._virtualserver_option_restore_json
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;volumeRstOption&#34;] = self._json_restore_volumeRstOption(
                restore_option)

        return request_json

    def _json_restore_volumeRstOption(self, value):
        &#34;&#34;&#34;setter for  the Volume restore option for in restore json&#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        return{
            &#34;volumeLeveRestore&#34;: False,
            &#34;volumeLevelRestoreType&#34;: value.get(&#34;volume_level_restore&#34;, 0)
        }

    def _json_restore_virtualServerRstOption(self, value):
        &#34;&#34;&#34;
            setter for  the Virtual server restore  option in restore json
        &#34;&#34;&#34;
        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        self._virtualserver_option_restore_json = {
            &#34;isDiskBrowse&#34;: value.get(&#34;disk_browse&#34;, True),
            &#34;isFileBrowse&#34;: value.get(&#34;file_browse&#34;, False),
            &#34;isVolumeBrowse&#34;: False,
            &#34;isVirtualLab&#34;: value.get(&#34;virtual_lab&#34;, False),
            &#34;esxServer&#34;: value.get(&#34;esx_server&#34;, &#34;&#34;),
            &#34;isAttachToNewVM&#34;: value.get(&#34;attach_to_new_vm&#34;, False),
            &#34;viewType&#34;: &#34;DEFAULT&#34;,
            &#34;isBlockLevelReplication&#34;: value.get(&#34;block_level&#34;, False),
            &#34;datacenter&#34;: value.get(&#34;datacenter&#34;, &#34;&#34;)
        }

        if value.get(&#39;replication_guid&#39;):
            self._virtualserver_option_restore_json[&#39;replicationGuid&#39;] = value[&#39;replication_guid&#39;]

    def _json_restore_advancedRestoreOptions(self, value):
        &#34;&#34;&#34;setter for the Virtual server restore  option in restore json&#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        self._advanced_option_restore_json = {
            &#34;disks&#34;: value.get(&#34;disks&#34;, []),
            &#34;guid&#34;: value.get(&#34;guid&#34;, &#34;&#34;),
            &#34;newGuid&#34;: value.get(&#34;new_guid&#34;, &#34;&#34;),
            &#34;newName&#34;: value.get(&#34;new_name&#34;, &#34;&#34;),
            &#34;esxHost&#34;: value.get(&#34;esx_host&#34;, &#34;&#34;),
            &#34;projectId&#34;: value.get(&#34;project_id&#34;, &#34;&#34;),
            &#34;cluster&#34;: value.get(&#34;cluster&#34;, &#34;&#34;),
            &#34;name&#34;: value.get(&#34;name&#34;, &#34;&#34;),
            &#34;nics&#34;: value.get(&#34;nics&#34;, []),
            &#34;vmIPAddressOptions&#34;: value.get(&#34;vm_ip_address_options&#34;, []),
            &#34;FolderPath&#34;: value.get(&#34;FolderPath&#34;, &#34;&#34;),
            &#34;resourcePoolPath&#34;: value.get(&#34;ResourcePool&#34;, &#34;&#34;),
            &#34;volumeType&#34;: value.get(&#34;volumeType&#34;, &#34;Auto&#34;),
            &#34;endUserVMRestore&#34;: value.get(&#34;end_user_vm_restore&#34;, False)     # Required by Kubernetes Disk Level Restore
        }

        value_dict = {
            &#34;createPublicIP&#34;: [&#34;createPublicIP&#34;, [&#34;createPublicIP&#34;, &#34;&#34;]],
            &#34;restoreAsManagedVM&#34;: [&#34;restoreAsManagedVM&#34;, [&#34;restoreAsManagedVM&#34;, &#34;&#34;]],
            &#34;destination_os_name&#34;: [&#34;osName&#34;, [&#34;destination_os_name&#34;, &#34;AUTO&#34;]],
            &#34;resourcePoolPath&#34;: [&#34;resourcePoolPath&#34;, [&#34;resourcePoolPath&#34;, &#34;&#34;]],
            &#34;datacenter&#34;: [&#34;datacenter&#34;, [&#34;datacenter&#34;, &#34;&#34;]],
            &#34;terminationProtected&#34;: [&#34;terminationProtected&#34;, [&#34;terminationProtected&#34;, False]],
            &#34;securityGroups&#34;: [&#34;securityGroups&#34;, [&#34;securityGroups&#34;, &#34;&#34;]],
            &#34;keyPairList&#34;: [&#34;keyPairList&#34;, [&#34;keyPairList&#34;, &#34;&#34;]]
        }

        for key in value_dict:
            if key in value:
                inner_key = value_dict[key][0]
                val1, val2 = value_dict[key][1][0], value_dict[key][1][1]
                self._advanced_option_restore_json[inner_key] = value.get(val1, val2)

        if &#34;vmSize&#34; in value:
            val1, val2 = (&#34;instanceSize&#34;, &#34;&#34;) if not value[&#34;vmSize&#34;] else (&#34;vmSize&#34;, &#34;vmSize&#34;)
            self._advanced_option_restore_json[&#34;vmSize&#34;] = value.get(val1, val2)
        if &#34;ami&#34; in value and value[&#34;ami&#34;] is not None:
            self._advanced_option_restore_json[&#34;templateId&#34;] = value[&#34;ami&#34;][&#34;templateId&#34;]
            self._advanced_option_restore_json[&#34;templateName&#34;] = value[&#34;ami&#34;][&#34;templateName&#34;]
        if &#34;iamRole&#34; in value and value[&#34;iamRole&#34;] is not None:
            self._advanced_option_restore_json[&#34;roleInfo&#34;] = {
                &#34;name&#34;: value[&#34;iamRole&#34;]
            }
        if self._instance_object.instance_name == &#39;openstack&#39;:
            if &#34;securityGroups&#34; in value and value[&#34;securityGroups&#34;] is not None:
                self._advanced_option_restore_json[&#34;securityGroups&#34;] = [{&#34;groupName&#34;: value[&#34;securityGroups&#34;]}]
        if &#34;destComputerName&#34; in value and value[&#34;destComputerName&#34;] is not None:
            self._advanced_option_restore_json[&#34;destComputerName&#34;] = value[&#34;destComputerName&#34;]
        if &#34;destComputerUserName&#34; in value and value[&#34;destComputerUserName&#34;] is not None:
            self._advanced_option_restore_json[&#34;destComputerUserName&#34;] = value[&#34;destComputerUserName&#34;]
        if &#34;instanceAdminPassword&#34; in value and value[&#34;instanceAdminPassword&#34;] is not None:
            self._advanced_option_restore_json[&#34;instanceAdminPassword&#34;] = value[&#34;instanceAdminPassword&#34;]

        if self.disk_pattern.datastore.value == &#34;DestinationPath&#34;:
            self._advanced_option_restore_json[&#34;DestinationPath&#34;] = value.get(&#34;datastore&#34;, &#34;&#34;)

        else:
            self._advanced_option_restore_json[&#34;Datastore&#34;] = value.get(&#34;datastore&#34;, &#34;&#34;)

        if &#34;datacenter&#34; in value:   # Required for Kubernetes Disk Level Restore
            self._advanced_option_restore_json[&#39;datacenter&#39;] = value[&#34;datacenter&#34;]

        if value.get(&#39;block_level&#39;):
            self._advanced_option_restore_json[&#34;blrRecoveryOpts&#34;] = \
                self._json_restore_blrRecoveryOpts(value)

        temp_dict = copy.deepcopy(self._advanced_option_restore_json)
        return temp_dict

    def _get_app_pvc(self, application_id):
        &#34;&#34;&#34;Get the dictionary of PVCs in the applications with storage class info

            Args:

                application_id      (str)       --  Application GUID to get PVC

            Returns:

                List of dicts with PVC information
        &#34;&#34;&#34;
        app_disks, disk_metadata = self.browse(&#39;\\&#39; + application_id)
        pvc_path_list = [disk for disk in app_disks if disk.split(&#39;.&#39;)[-1] != &#39;yaml&#39;]

        pvc_list = []

        for pvc in pvc_path_list:
            temp_dict = {}
            pvc_metadata = disk_metadata[pvc]
            vs_metadata = pvc_metadata[&#39;advanced_data&#39;][&#39;browseMetaData&#39;][&#39;virtualServerMetaData&#39;]
            temp_dict[&#39;name&#39;] = pvc_metadata[&#39;name&#39;]
            temp_dict[&#39;storageclass&#39;] = vs_metadata[&#39;datastore&#39;]
            pvc_list.append(temp_dict)

        return pvc_list

    def set_advanced_vm_restore_options(self, vm_to_restore, restore_option):
        &#34;&#34;&#34;
        set the advanced restore options for all vm in restore
        param

            vm_to_restore               - Name of the Application to restore

            restore_option              - restore options that need to be set for advanced restore option

            power_on                    - power on the Application after restore

            add_to_failover             - Register the Application to Failover Cluster

            datastore                   - Datastore where the Application needs to be restored

            disks   (list of dict)      - list with dict for each disk in Application
                                            eg: [{
                                                        name:&#34;pvc-1&#34;
                                                        datastore:&#34;storageclass-1&#34;
                                                    }
                                                    {
                                                        name:&#34;pvc-2&#34;
                                                        datastore:&#34;storageclass-2&#34;
                                                    }
                                                ]
            guid                        - GUID of the Application needs to be restored

            new_name                    - New name for the Application to be restored

            esx_host                    - client name where it need to be restored

            name                        - name of the Application to be restored

        &#34;&#34;&#34;

        # Set the new name for the restored Application.
        # If new_name is not given, it restores the Application with same name
        # with suffix Delete.
        vm_names, vm_ids = self._get_vm_ids_and_names_dict_from_browse()
        browse_result = self.vm_files_browse()
        application_id = vm_ids[vm_to_restore]

        # vs metadata from browse result
        _metadata = browse_result[1][(&#39;\\&#39; + vm_to_restore)]
        vs_metadata = _metadata[&#34;advanced_data&#34;][&#34;browseMetaData&#34;][&#34;virtualServerMetaData&#34;]
        if restore_option[&#39;in_place&#39;]:
            folder_path = vs_metadata.get(&#34;inventoryPath&#34;, &#39;&#39;)
            instance_size = vs_metadata.get(&#34;instanceSize&#34;, &#39;&#39;)
        else:
            folder_path = &#39;&#39;
            instance_size = &#39;&#39;
        if restore_option.get(&#39;resourcePoolPath&#39;):
            restore_option[&#39;resourcePoolPath&#39;] = vs_metadata[&#39;resourcePoolPath&#39;]
        if restore_option.get(&#39;datacenter&#39;):
            restore_option[&#39;datacenter&#39;] = restore_option.get(&#39;datacenter&#39;)
        if restore_option.get(&#39;terminationProtected&#39;):
            restore_option[&#39;terminationProtected&#39;] = vs_metadata.get(&#39;terminationProtected&#39;, &#39;&#39;)
        if restore_option.get(&#39;iamRole&#39;):
            restore_option[&#39;iamRole&#39;] = vs_metadata.get(&#39;role&#39;, &#39;&#39;)
        if restore_option.get(&#39;securityGroups&#39;):
            _security_groups = self._find_security_groups(vs_metadata[&#39;networkSecurityGroups&#39;])
            restore_option[&#39;securityGroups&#39;] = _security_groups
        if restore_option.get(&#39;keyPairList&#39;):
            _keypair_list = self._find_keypair_list(vs_metadata[&#39;loginKeyPairs&#39;])
            restore_option[&#39;keyPairList&#39;] = _keypair_list

        # populate restore source item
        restore_option[&#39;paths&#39;].append(&#34;\\&#34; + application_id)
        restore_option[&#39;name&#39;] = vm_to_restore
        restore_option[&#39;guid&#39;] = application_id
        restore_option[&#34;FolderPath&#34;] = folder_path
        restore_option[&#34;ResourcePool&#34;] = &#34;/&#34;

        # populate restore disk and datastore
        vm_disks = []
        new_name = vm_to_restore

        storage_class_map = restore_option.get(&#39;storage_class_map&#39;, None)

        # To populate disk list for each app in case of namespace restore
        if storage_class_map:
            pvc_list = self._get_app_pvc(application_id)
            for pvc in pvc_list:
                storageclass_name = pvc[&#39;storageclass&#39;]
                pvc_name = pvc[&#39;name&#39;]

                # If &#39;datastore&#39; is passed then it&#39;s full app restore, else
                # it is namespace level restore.
                # Namespace level restore can be passed with storage class mapping
                if storageclass_name in storage_class_map:
                    storageclass_name = storage_class_map[storageclass_name]
                _disk_dict = self._disk_dict_pattern(pvc_name, storageclass_name, pvc_name)
                vm_disks.append(_disk_dict)

        restore_option[&#34;disks&#34;] = vm_disks

        self._set_restore_inputs(
            restore_option,
            esx_host=restore_option.get(&#39;esx_host&#39;) or vs_metadata[&#39;esxHost&#39;],
            instance_size=restore_option.get(&#39;instanceSize&#39;, instance_size),
            new_name=restore_option.get(&#39;new_name&#39;, &#34;Delete&#34; + vm_to_restore)
        )

        temp_dict = self._json_restore_advancedRestoreOptions(restore_option)
        self._advanced_restore_option_list.append(temp_dict)

    def full_app_restore_in_place(
            self,
            apps_to_restore=None,
            overwrite=True,
            copy_precedence=0,
            proxy_client=None):
        &#34;&#34;&#34;Restores the FULL Application specified in the input list
            to the location same as the actual location of the Application in Kubernetes cluster.

            Args:
                apps_to_restore     (list)      --  List of applications to restore

                overwrite           (bool)      --  overwrite the existing Applications if exists
                                                    default: True

                copy_precedence     (int)       --  copy precedence value
                                                    default: 0

                proxy_client        (str)       --  proxy client to be used for restore
                                                    default: proxy added in application group/cluster

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if inputs are not of correct type as per definition

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;

        restore_option = {}
        # check input parameters are correct
        if apps_to_restore and not isinstance(apps_to_restore, list):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        if copy_precedence:
            restore_option[&#39;copy_precedence_applicable&#39;] = True

        if proxy_client is not None:
            restore_option[&#39;client&#39;] = proxy_client

        kubernetes_host = self._client_object.client_name

        # set attr for all the option in restore xml from user inputs
        self._set_restore_inputs(
            restore_option,
            vm_to_restore=self._set_vm_to_restore(apps_to_restore),
            in_place=True,
            esx_host=kubernetes_host,
            volume_level_restore=1,
            unconditional_overwrite=overwrite,
            copy_precedence=copy_precedence
        )

        request_json = self._prepare_kubernetes_inplace_restore_json(restore_option)
        return self._process_restore_response(request_json)

    def _prepare_kubernetes_inplace_restore_json(self, restore_option):
        &#34;&#34;&#34;
        Prepare Full Application restore in-place Json with all getters

        Args:
            restore_option - dictionary with all Application restore options

        value:
            restore_option:

                preserve_level           (bool)   - set the preserve level in restore

                unconditional_overwrite  (bool)  - unconditionally overwrite the disk
                                                    in the restore path

                destination_path          (str)- path where the disk needs to be
                                                 restored

                client_name               (str)  - client where the disk needs to be
                                                   restored

                destination_vendor         (str) - vendor id of the Hypervisor

                destination_disktype       (str) - type of disk needs to be restored
                                                   like VHDX,VHD,VMDK

                source_item                 (str)- GUID of Application from which disk needs to
                                                   be restored
                                                   eg:
                                                   \\5F9FA60C-0A89-4BD9-9D02-C5ACB42745EA

                copy_precedence_applicable  (bool)- True if needs copy_precedence to
                                                    be honoured else False

                copy_precedence            (int) - the copy id from which browse and
                                                   restore needs to be performed

                datastore                   (str) - Storage class which the Application PVC needs to be
                                                    restored with

                disks   (list of dict)      - list with dict for each disk in Application
                                                eg: [{
                                                        name:&#34;pvc-1&#34;
                                                        datastore:&#34;storageclass-1&#34;
                                                    }
                                                    {
                                                        name:&#34;pvc-2&#34;
                                                        datastore:&#34;storageclass-2&#34;
                                                    }
                                                ]
                guid                    (str)    - GUID of the Application needs to be restored

                new_name                (str)    - New name for the Application to be restored

                esx_host                (str)    - client name where Application need to be restored

                name                    (str)    - name of the Application to be restored

        returns:
              request_json        -complete json for perfomring Full VM Restore
                                   options

        &#34;&#34;&#34;
        if restore_option is None:
            restore_option = {}
        restore_option[&#39;paths&#39;] = []

        if &#34;destination_vendor&#34; not in restore_option:
            restore_option[&#34;destination_vendor&#34;] = \
                self._backupset_object._instance_object._vendor_id

        if restore_option.get(&#39;copy_precedence&#39;):
            restore_option[&#39;copy_precedence_applicable&#39;] = True

        # set all the restore defaults
        self._set_restore_defaults(restore_option)

        # set the setters
        self._backupset_object._instance_object._restore_association = self._subClientEntity
        self._json_restore_virtualServerRstOption(restore_option)
        self._json_restore_diskLevelVMRestoreOption(restore_option)
        self._json_vcenter_instance(restore_option)

        for _each_vm_to_restore in restore_option[&#39;vm_to_restore&#39;]:
            if not restore_option[&#34;in_place&#34;]:
                if &#39;disk_type&#39; in restore_option:
                    restore_option[&#39;restoreAsManagedVM&#39;] = restore_option[&#39;disk_type&#39;][_each_vm_to_restore]
                if (&#34;restore_new_name&#34; in restore_option and
                        restore_option[&#34;restore_new_name&#34;] is not None):
                    restore_option[&#34;new_name&#34;] = restore_option[&#34;restore_new_name&#34;] + _each_vm_to_restore
                else:
                    restore_option[&#34;new_name&#34;] = &#34;del&#34; + _each_vm_to_restore
            else:
                restore_option[&#34;new_name&#34;] = _each_vm_to_restore
            self.set_advanced_vm_restore_options(_each_vm_to_restore, restore_option)
        # prepare json
        request_json = self._restore_json(restore_option=restore_option)
        self._virtualserver_option_restore_json[&#34;diskLevelVMRestoreOption&#34;][
            &#34;advancedRestoreOptions&#34;] = self._advanced_restore_option_list
        self._advanced_restore_option_list = []
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;virtualServerRstOption&#34;] = self._virtualserver_option_restore_json
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;volumeRstOption&#34;] = self._json_restore_volumeRstOption(
                restore_option)
        return request_json

    def disk_restore(self,
                     application_name,
                     destination_path,
                     disk_name=None,
                     proxy_client=None,
                     **kwargs):
        &#34;&#34;&#34;Restores the disk specified in the input paths list to the same location

            Args:
                application_name             (str)    --  Name of the Application added in subclient content
                                                        whose  disk is selected for restore

                destination_path        (str)    --  Staging (destination) path to restore the
                                                        disk.

                disk_name                 (list)    --  name of the disk which has to be restored
                                                        (only yaml files permitted - enter full
                                                        name of the disk)
                                                        default: None
                proxy_client        (str)    --  Destination proxy client to be used
                                                        default: None

            Kwargs:

                Allows parameters to modify disk restore --

                copy_precedence            (int)    --  SP copy precedence from which browse has to

                media_agent         (str)   -- MA needs to use for disk browse
                    default :Storage policy MA

                snap_proxy          (str)   -- proxy need to be used for disk
                                                    restores from snap
                    default :proxy in instance or subclient

                disk_extension      (str)   -- Extension of disk file (Default: &#39;.yaml&#39;)

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if inputs are not passed in proper expected format

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;

        vm_names, vm_ids = self._get_vm_ids_and_names_dict_from_browse()
        _disk_restore_option = {}

        copy_precedence = kwargs.get(&#34;copy_precedence&#34;, 0)
        disk_extn = kwargs.get(&#34;disk_extension&#34;, &#39;.yaml&#39;)
        unconditional_overwrite = kwargs.get(&#39;unconditional_overwrite&#39;, False)
        show_deleted_files = kwargs.get(&#39;show_deleted_files&#39;, False)

        # Volume level restore values -
        # 4 - Manifest Restore
        volume_level_restore = kwargs.get(&#34;volume_level_restore&#34;, 4)

        if not disk_name:
            disk_name = []
        else:
            disk_extn = self._get_disk_extension(disk_name)

        # check if inputs are correct
        if not (isinstance(application_name, str) and
                isinstance(destination_path, str) and
                isinstance(disk_name, list)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if copy_precedence:
            _disk_restore_option[&#39;copy_precedence_applicable&#39;] = True

        # fetching all disks from the vm
        disk_list, disk_info_dict = self.disk_level_browse(
            &#34;\\&#34; + vm_ids[application_name])

        # Filter out disks with specified extension from disk list
        disk_list = list(filter(lambda name: self._get_disk_extension([name]) == disk_extn, disk_list))
        disk_info_dict = { disk : disk_info_dict[disk] for disk in disk_list }

        if not disk_name:  # if disk names are not provided, restore all disks
            for each_disk_path in disk_list:
                disk_name.append(each_disk_path.split(&#39;\\&#39;)[-1])

        else:  # else, check if the given application has a disk with the list of disks in disk_name.
            for each_disk in disk_name:
                # disk path has GUID in case of files, and application name in case of manifests
                each_disk_path = &#34;\\&#34; + \
                                 (vm_ids[application_name] if volume_level_restore != 4 else application_name) + \
                                 &#34;\\&#34; + each_disk.split(&#34;\\&#34;)[-1]
                if each_disk_path not in disk_list:
                    raise SDKException(&#39;Subclient&#39;, &#39;111&#39;)

        _disk_restore_option[&#34;destination_vendor&#34;] = \
            self._backupset_object._instance_object._vendor_id

        if proxy_client is not None:
            _disk_restore_option[&#39;client&#39;] = proxy_client
        else:
            _disk_restore_option[&#39;client&#39;] = self._backupset_object._instance_object.co_ordinator

        # set Source item List
        src_item_list = []
        for each_disk in disk_name:
            src_item_list.append(&#34;\\&#34; + vm_ids[application_name] + &#34;\\&#34; + each_disk.split(&#34;\\&#34;)[-1])

        _disk_restore_option[&#39;paths&#39;] = src_item_list
        _disk_restore_option[&#39;unconditional_overwrite&#39;] = unconditional_overwrite
        _disk_restore_option[&#39;show_deleted_files&#39;] = show_deleted_files

        # Populate volume level restore options
        _disk_restore_option[&#39;volume_level_restore&#39;] = volume_level_restore

        self._set_restore_inputs(
            _disk_restore_option,
            in_place=False,
            copy_precedence=copy_precedence,
            destination_path=destination_path,
            paths=src_item_list
        )

        request_json = self._prepare_disk_restore_json(_disk_restore_option)
        return self._process_restore_response(request_json)

    def enable_intelli_snap(self, snap_engine_name=None, proxy_options=None, snapshot_engine_id =None):
        &#34;&#34;&#34;Enables Intelli Snap for the subclient.

            Args:
                snap_engine_name    (str)   --  Snap Engine Name

                proxy_options       (str)    -- to set proxy for Kubernetes

                snapshot_engine_id   (int)   -- Snapshot engine id

            Raises:
                SDKException:
                    if failed to enable intelli snap for subclient
        &#34;&#34;&#34;
        if snapshot_engine_id is None:
            snapshot_engine_id = 82

        properties_dict = {
            &#34;isSnapBackupEnabled&#34;: True,
            &#34;snapToTapeSelectedEngine&#34;: {
                &#34;snapShotEngineId&#34;: snapshot_engine_id,
                &#34;snapShotEngineName&#34;: snap_engine_name
            }
        }
        if proxy_options is not None:
            if &#34;snap_proxy&#34; in proxy_options:
                properties_dict[&#34;snapToTapeProxyToUse&#34;] = {
                    &#34;clientName&#34;: proxy_options[&#34;snap_proxy&#34;]
                }

            if &#34;backupcopy_proxy&#34; in proxy_options:
                properties_dict[&#34;useSeparateProxyForSnapToTape&#34;] = True
                properties_dict[&#34;separateProxyForSnapToTape&#34;] = {
                    &#34;clientName&#34;: proxy_options[&#34;backupcopy_proxy&#34;]
                }

            if &#34;use_source_if_proxy_unreachable&#34; in proxy_options:
                properties_dict[&#34;snapToTapeProxyToUseSource&#34;] = True

        self._set_subclient_properties(
            &#34;_commonProperties[&#39;snapCopyInfo&#39;]&#34;, properties_dict)

    def guest_file_restore(self,
                           application_name,
                           destination_path,
                           volume_level_restore,
                           disk_name=None,
                           proxy_client=None,
                           restore_list=None,
                           restore_pvc_guid=None,
                           **kwargs):
        &#34;&#34;&#34;perform Guest file restore of the provided path

        Args:
            application_name_name   (str)   --  Name of the source application
            destination_path        (str)   --  Path at the destination to restore at
            volume_level_restore    (str)   --  Flag to denote volume_level_restore
                                                Accepted values -
                                                6 for restore to PVC
                                                7 for FS Destination restore
            disk_name               (str)   --  Name of the source PVC
            proxy_client            (str)   --  Access node for restore
            restore_list            (str)   --  List of files or folders to restore. Contains Full path
                                                of files or folders relative to PVC mount point.
                                                Eg. if /tmp is the mount point with files or folder /tmp/folder1/file1,
                                                restore list should have format &#39;folder1/file1&#39;
            restore_pvc_guid        (str)   --  strGUID of the target PVC

        Kwargs:
            copy_precedence         (int)   --  To set copy precedence for restore
            disk_extension          (str)   --  Extention of the disk
            unconditional_overwrite (int)   --  To set unconditional overwrite for restore
            show_deleted_files      (bool)  --  Whether to show deleted files in browse
            in_place                (bool)  --  If restore job is inplace

        Raises:
            SDK Exception if
                -inputs are not of correct type as per definition

                -invalid volume_level_restore passed
        &#34;&#34;&#34;
        vm_names, vm_ids = self._get_vm_ids_and_names_dict_from_browse()
        _guest_file_rst_options = {}
        _advanced_restore_options = {}

        copy_precedence = kwargs.get(&#34;copy_precedence&#34;, 0)
        disk_extn = kwargs.get(&#34;disk_extension&#34;, &#39;&#39;)
        overwrite = kwargs.get(&#34;unconditional_overwrite&#34;, 1)
        unconditional_overwrite = kwargs.get(&#39;unconditional_overwrite&#39;, False)
        show_deleted_files = kwargs.get(&#39;show_deleted_files&#39;, False)
        in_place = kwargs.get(&#39;in_place&#39;, False)

        # check if inputs are correct
        if not (isinstance(application_name, str) and
                isinstance(destination_path, str) and
                isinstance(disk_name, str)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        if volume_level_restore not in [6, 7]:
            raise SDKException(&#34;Subclient&#34;, &#34;102&#34;, &#34;Invalid volume level restore type passed&#34;)

        if copy_precedence:
            _guest_file_rst_options[&#39;copy_precedence_applicable&#39;] = True

        # fetching all disks from the application
        disk_list, disk_info_dict = self.disk_level_browse(
            &#34;\\&#34; + vm_ids[application_name])

        # Filter out disks with specified extension from disk list
        disk_list = list(filter(lambda name: self._get_disk_extension([name]) == disk_extn, disk_list))
        disk_info_dict = {disk: disk_info_dict[disk] for disk in disk_list}

        _guest_file_rst_options[&#34;destination_vendor&#34;] = \
            self._backupset_object._instance_object._vendor_id

        if proxy_client is not None:
            _guest_file_rst_options[&#39;client&#39;] = proxy_client
        else:
            _guest_file_rst_options[&#39;client&#39;] = self._backupset_object._instance_object.co_ordinator

        # set Source item List
        src_item_list = []
        for each_item in restore_list:
            item = &#34;\\&#34;.join(each_item.split(&#39;/&#39;))
            src_item_list.append( &#34;\\&#34; + vm_ids[application_name] + &#34;\\&#34; + disk_name + &#34;\\&#34; + item)

        _guest_file_rst_options[&#39;paths&#39;] = src_item_list

        if volume_level_restore == 6:

            if in_place:
                restore_pvc_guid = &#34;{}`PersistentVolumeClaim`{}&#34;.format(
                    vm_ids[application_name].split(&#39;`&#39;)[0], disk_name
                )
                new_name = disk_name

            else:
                new_name = restore_pvc_guid.split(&#39;`&#39;)[-2]
                _advanced_restore_options[&#39;datacenter&#39;] = &#34;none&#34;

            new_guid = restore_pvc_guid

        else:

            new_guid = &#34;{}`PersistentVolumeClaim`{}&#34;.format(
                vm_ids[application_name].split(&#39;`&#39;)[0], disk_name
            )
            new_name = disk_name

        _guest_file_rst_options[&#39;in_place&#39;] = in_place
        _guest_file_rst_options[&#39;volume_level_restore&#39;] = volume_level_restore
        _guest_file_rst_options[&#39;unconditional_overwrite&#39;] = unconditional_overwrite
        _guest_file_rst_options[&#39;show_deleted_files&#39;] = show_deleted_files

        _advanced_restore_options[&#39;new_guid&#39;] = new_guid
        _advanced_restore_options[&#39;new_name&#39;] = new_name
        _advanced_restore_options[&#39;name&#39;] = disk_name
        _advanced_restore_options[&#39;guid&#39;] = vm_ids[application_name]
        _advanced_restore_options[&#39;end_user_vm_restore&#39;] = True

        # set advanced restore options disks
        _disk_dict = self._disk_dict_pattern(disk_name, &#34;&#34;)
        _advanced_restore_options[&#39;disks&#39;] = [_disk_dict]

        advanced_options_dict = self._json_restore_advancedRestoreOptions(_advanced_restore_options)
        self._advanced_restore_option_list.append(advanced_options_dict)

        self._set_restore_inputs(
            _guest_file_rst_options,
            in_place=False,
            copy_precedence=copy_precedence,
            destination_path=destination_path,
            paths=src_item_list
        )

        request_json = self._prepare_disk_restore_json(_guest_file_rst_options)

        # Populate the advancedRestoreOptions section
        self._virtualserver_option_restore_json[&#34;diskLevelVMRestoreOption&#34;][
            &#34;advancedRestoreOptions&#34;] = self._advanced_restore_option_list
        self._advanced_restore_option_list = []

        return self._process_restore_response(request_json)

    def guest_files_browse(
            self,
            application_path=&#39;\\&#39;,
            show_deleted_files=False,
            restore_index=True,
            from_date=0,
            to_date=0,
            copy_precedence=0,
            media_agent=&#34;&#34;):
        &#34;&#34;&#34;Browses the Files and Folders inside a Virtual Machine in the time
           range specified.

            Args:
                application_path    (str)   --  folder path to get the contents
                                                of
                                                default: &#39;\\&#39;;
                                                returns the root of the Backup
                                                content

                show_deleted_files  (bool)  --  include deleted files in the
                                                content or not default: False

                restore_index       (bool)  --  restore index if it is not cached
                                                default: True

                from_date           (int)   --  date to get the contents after
                                                format: dd/MM/YYYY

                                                gets contents from 01/01/1970
                                                if not specified
                                                default: 0

                to_date             (int)  --  date to get the contents before
                                               format: dd/MM/YYYY

                                               gets contents till current day
                                               if not specified
                                               default: 0

                copy_precedence     (int)   --  copy precedence to be used
                                                    for browsing

                media_agent         (str)   --  Browse MA via with Browse has to happen.
                                                It can be MA different than Storage Policy MA

            Returns:
                list - list of all folders or files with their full paths
                       inside the input path

                dict - path along with the details like name, file/folder,
                       size, modification time

            Raises:
                SDKException:
                    if from date value is incorrect

                    if to date value is incorrect

                    if to date is less than from date

                    if failed to browse content

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        return self.browse_in_time(
            vm_path, show_deleted_files, restore_index, False, from_date, to_date, copy_precedence,
            vm_files_browse=False, media_agent=media_agent)

    def _get_apps_in_namespace(self, namespaces):
        &#34;&#34;&#34;Get the list of applications to be restored with the namespace level restore

            Args:
                namespaces      (list)  -   List of namespaces

            Returns:

                  list of applictations to be restored with namespaces
        &#34;&#34;&#34;

        apps_to_restore = []
        namespace_app_dict = {}
        app, app_dict = self.browse()
        for app_path, metadata in app_dict.items():
            app_name = metadata[&#39;name&#39;]
            app_id = metadata[&#39;snap_display_name&#39;]
            app_ns = app_id.split(&#39;`&#39;)[0]
            app_type = app_id.split(&#39;`&#39;)[1]
            if app_type != &#39;Namespace&#39; and app_ns in namespaces:
                apps_to_restore.append(app_name)
                namespace_app_dict[app_name] = app_ns

        return apps_to_restore, namespace_app_dict

    def namespace_restore_out_of_place(
            self,
            namespace_to_restore,
            target_namespace_name={},
            target_cluster_name=None,
            storage_class_map=None,
            overwrite=True,
            copy_precedence=0,
            proxy_client=None
    ):
        &#34;&#34;&#34;Perform a namespace-level restore out-of-place

            Args:

                namespace_to_restore        (list)  --  List of namespaces to restore

                target_namespace_name       (dict)  --  Target namespace name to perform restore at
                                                        Eg. {&#39;namespace1&#39;: &#39;namespace1-rst&#39;}

                target_cluster_name         (str)   --  Name of the target cluster to restore at

                storage_class_map           (dict)  --  Mapping of storage classes for transformation
                                                        Eg. {&#39;rook-ceph-block&#39; : &#39;azurefile&#39;}

                overwrite                   (bool)  --  Overwrite the existing namespace
                                                        Default: true

                copy_precedence             (int)   --  Copy preceedence value

                proxy_client                (str)   --  Name of the proxy client to launch restore
                                                        Default : None (Automatic)
            Returns:

                object - instance of the Job class for this restore job

            Raises:

                SDKException:

                    if inputs are not of correct type as per definition

                    if failed to initialize job

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;

        restore_options = {}

        # Check mandatory input parameters are correct
        if namespace_to_restore and not type(namespace_to_restore) is list:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        # Populating proxy client. It automatically fetches proxy controller from subclient/instance level
        # property if not specified
        if proxy_client is not None:
            restore_options[&#39;client&#39;] = proxy_client

        restore_new_name = {}
        apps_to_restore = []

        if not type(target_namespace_name) is dict:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        for ns in namespace_to_restore:
            if ns not in target_namespace_name:
                target_namespace_name[ns] = ns
        restore_new_name.update(target_namespace_name)

        namespace_apps, namespace_app_map = self._get_apps_in_namespace(namespace_to_restore)
        apps_to_restore.extend(namespace_apps)

        for app in apps_to_restore:
            restore_new_name[app] = app
        apps_to_restore.extend(namespace_to_restore)

        restore_options[&#39;restore_new_name&#39;] = restore_new_name
        restore_options[&#39;namespace_app_map&#39;] = namespace_app_map

        if not target_cluster_name:
            target_cluster_name = self._client_object.client_name

        self.reinitialize_vm_names_browse()

        self._set_restore_inputs(
            restore_options,
            in_place=False,
            vcenter_client=target_cluster_name,
            esx_host=target_cluster_name,
            esx_server=None,
            unconditional_overwrite=overwrite,
            power_on=True,
            vm_to_restore=self._set_vm_to_restore(apps_to_restore),
            disk_option=self._disk_option[&#39;Original&#39;],
            transport_mode=self._transport_mode[&#39;Auto&#39;],
            copy_precedence=copy_precedence,
            volume_level_restore=1,
            source_item=[],
            source_ip=None,
            destination_ip=None,
            network=None,
            storage_class_map=storage_class_map
        )

        request_json = self._prepare_kubernetes_restore_json(restore_options)
        return self._process_restore_response(request_json)

    def namespace_restore_in_place(
            self,
            namespace_to_restore,
            overwrite=True,
            copy_precedence=0,
            proxy_client=None
    ):
        &#34;&#34;&#34;Perform a namespace-level restore in-place

            Args:

                namespace_to_restore        (list)  --  List of namespaces to restore

                overwrite                   (bool)  --  Overwrite the existing namespace
                                                        Default: true

                copy_precedence             (int)   --  Copy preceedence value

                proxy_client                (str)   --  Name of the proxy client to launch restore
                                                        Default : None (Automatic)
            Returns:

                object - instance of the Job class for this restore job

            Raises:

                SDKException:

                    if inputs are not of correct type as per definition

                    if failed to initialize job

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;

        restore_options = {}

        # Check mandatory input parameters are correct
        if namespace_to_restore and not type(namespace_to_restore) is list:
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        # Populating proxy client. It automatically fetches proxy controller from subclient/instance level
        # property if not specified
        if proxy_client is not None:
            restore_options[&#39;client&#39;] = proxy_client

        apps_to_restore = []

        namespace_apps, namespace_app_map = self._get_apps_in_namespace(namespace_to_restore)
        apps_to_restore.extend(namespace_apps)
        apps_to_restore.extend(namespace_to_restore)

        client_name = self._client_object.client_name
        self._set_restore_inputs(
            restore_options,
            vm_to_restore=self._set_vm_to_restore(apps_to_restore),
            in_place=True,
            esx_host=client_name,
            esx_server_name=&#34;&#34;,
            volume_level_restore=1,
            unconditional_overwrite=overwrite,
            disk_option=self._disk_option[&#39;Original&#39;],
            transport_mode=self._transport_mode[&#39;Auto&#39;],
            copy_precedence=copy_precedence
        )

        request_json = self._prepare_kubernetes_inplace_restore_json(restore_options)
        return self._process_restore_response(request_json)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient">VirtualServerSubclient</a></li>
<li><a title="cvpysdk.subclient.Subclient" href="../../subclient.html#cvpysdk.subclient.Subclient">Subclient</a></li>
</ul>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.subclients.virtualserver.kubernetes.KubernetesVirtualServerSubclient.disk_restore"><code class="name flex">
<span>def <span class="ident">disk_restore</span></span>(<span>self, application_name, destination_path, disk_name=None, proxy_client=None, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the disk specified in the input paths list to the same location</p>
<h2 id="args">Args</h2>
<p>application_name
(str)
&ndash;
Name of the Application added in subclient content
whose
disk is selected for restore</p>
<p>destination_path
(str)
&ndash;
Staging (destination) path to restore the
disk.</p>
<p>disk_name
(list)
&ndash;
name of the disk which has to be restored
(only yaml files permitted - enter full
name of the disk)
default: None
proxy_client
(str)
&ndash;
Destination proxy client to be used
default: None</p>
<h2 id="kwargs">Kwargs</h2>
<p>Allows parameters to modify disk restore &ndash;</p>
<p>copy_precedence
(int)
&ndash;
SP copy precedence from which browse has to</p>
<p>media_agent
(str)
&ndash; MA needs to use for disk browse
default :Storage policy MA</p>
<p>snap_proxy
(str)
&ndash; proxy need to be used for disk
restores from snap
default :proxy in instance or subclient</p>
<p>disk_extension
(str)
&ndash; Extension of disk file (Default: '.yaml')</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if inputs are not passed in proper expected format</p>
<pre><code>if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/kubernetes.py#L747-L872" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def disk_restore(self,
                 application_name,
                 destination_path,
                 disk_name=None,
                 proxy_client=None,
                 **kwargs):
    &#34;&#34;&#34;Restores the disk specified in the input paths list to the same location

        Args:
            application_name             (str)    --  Name of the Application added in subclient content
                                                    whose  disk is selected for restore

            destination_path        (str)    --  Staging (destination) path to restore the
                                                    disk.

            disk_name                 (list)    --  name of the disk which has to be restored
                                                    (only yaml files permitted - enter full
                                                    name of the disk)
                                                    default: None
            proxy_client        (str)    --  Destination proxy client to be used
                                                    default: None

        Kwargs:

            Allows parameters to modify disk restore --

            copy_precedence            (int)    --  SP copy precedence from which browse has to

            media_agent         (str)   -- MA needs to use for disk browse
                default :Storage policy MA

            snap_proxy          (str)   -- proxy need to be used for disk
                                                restores from snap
                default :proxy in instance or subclient

            disk_extension      (str)   -- Extension of disk file (Default: &#39;.yaml&#39;)

        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:
                if inputs are not passed in proper expected format

                if response is empty

                if response is not success
    &#34;&#34;&#34;

    vm_names, vm_ids = self._get_vm_ids_and_names_dict_from_browse()
    _disk_restore_option = {}

    copy_precedence = kwargs.get(&#34;copy_precedence&#34;, 0)
    disk_extn = kwargs.get(&#34;disk_extension&#34;, &#39;.yaml&#39;)
    unconditional_overwrite = kwargs.get(&#39;unconditional_overwrite&#39;, False)
    show_deleted_files = kwargs.get(&#39;show_deleted_files&#39;, False)

    # Volume level restore values -
    # 4 - Manifest Restore
    volume_level_restore = kwargs.get(&#34;volume_level_restore&#34;, 4)

    if not disk_name:
        disk_name = []
    else:
        disk_extn = self._get_disk_extension(disk_name)

    # check if inputs are correct
    if not (isinstance(application_name, str) and
            isinstance(destination_path, str) and
            isinstance(disk_name, list)):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    if copy_precedence:
        _disk_restore_option[&#39;copy_precedence_applicable&#39;] = True

    # fetching all disks from the vm
    disk_list, disk_info_dict = self.disk_level_browse(
        &#34;\\&#34; + vm_ids[application_name])

    # Filter out disks with specified extension from disk list
    disk_list = list(filter(lambda name: self._get_disk_extension([name]) == disk_extn, disk_list))
    disk_info_dict = { disk : disk_info_dict[disk] for disk in disk_list }

    if not disk_name:  # if disk names are not provided, restore all disks
        for each_disk_path in disk_list:
            disk_name.append(each_disk_path.split(&#39;\\&#39;)[-1])

    else:  # else, check if the given application has a disk with the list of disks in disk_name.
        for each_disk in disk_name:
            # disk path has GUID in case of files, and application name in case of manifests
            each_disk_path = &#34;\\&#34; + \
                             (vm_ids[application_name] if volume_level_restore != 4 else application_name) + \
                             &#34;\\&#34; + each_disk.split(&#34;\\&#34;)[-1]
            if each_disk_path not in disk_list:
                raise SDKException(&#39;Subclient&#39;, &#39;111&#39;)

    _disk_restore_option[&#34;destination_vendor&#34;] = \
        self._backupset_object._instance_object._vendor_id

    if proxy_client is not None:
        _disk_restore_option[&#39;client&#39;] = proxy_client
    else:
        _disk_restore_option[&#39;client&#39;] = self._backupset_object._instance_object.co_ordinator

    # set Source item List
    src_item_list = []
    for each_disk in disk_name:
        src_item_list.append(&#34;\\&#34; + vm_ids[application_name] + &#34;\\&#34; + each_disk.split(&#34;\\&#34;)[-1])

    _disk_restore_option[&#39;paths&#39;] = src_item_list
    _disk_restore_option[&#39;unconditional_overwrite&#39;] = unconditional_overwrite
    _disk_restore_option[&#39;show_deleted_files&#39;] = show_deleted_files

    # Populate volume level restore options
    _disk_restore_option[&#39;volume_level_restore&#39;] = volume_level_restore

    self._set_restore_inputs(
        _disk_restore_option,
        in_place=False,
        copy_precedence=copy_precedence,
        destination_path=destination_path,
        paths=src_item_list
    )

    request_json = self._prepare_disk_restore_json(_disk_restore_option)
    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.virtualserver.kubernetes.KubernetesVirtualServerSubclient.enable_intelli_snap"><code class="name flex">
<span>def <span class="ident">enable_intelli_snap</span></span>(<span>self, snap_engine_name=None, proxy_options=None, snapshot_engine_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Enables Intelli Snap for the subclient.</p>
<h2 id="args">Args</h2>
<p>snap_engine_name
(str)
&ndash;
Snap Engine Name</p>
<p>proxy_options
(str)
&ndash; to set proxy for Kubernetes</p>
<p>snapshot_engine_id
(int)
&ndash; Snapshot engine id</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to enable intelli snap for subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/kubernetes.py#L874-L914" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable_intelli_snap(self, snap_engine_name=None, proxy_options=None, snapshot_engine_id =None):
    &#34;&#34;&#34;Enables Intelli Snap for the subclient.

        Args:
            snap_engine_name    (str)   --  Snap Engine Name

            proxy_options       (str)    -- to set proxy for Kubernetes

            snapshot_engine_id   (int)   -- Snapshot engine id

        Raises:
            SDKException:
                if failed to enable intelli snap for subclient
    &#34;&#34;&#34;
    if snapshot_engine_id is None:
        snapshot_engine_id = 82

    properties_dict = {
        &#34;isSnapBackupEnabled&#34;: True,
        &#34;snapToTapeSelectedEngine&#34;: {
            &#34;snapShotEngineId&#34;: snapshot_engine_id,
            &#34;snapShotEngineName&#34;: snap_engine_name
        }
    }
    if proxy_options is not None:
        if &#34;snap_proxy&#34; in proxy_options:
            properties_dict[&#34;snapToTapeProxyToUse&#34;] = {
                &#34;clientName&#34;: proxy_options[&#34;snap_proxy&#34;]
            }

        if &#34;backupcopy_proxy&#34; in proxy_options:
            properties_dict[&#34;useSeparateProxyForSnapToTape&#34;] = True
            properties_dict[&#34;separateProxyForSnapToTape&#34;] = {
                &#34;clientName&#34;: proxy_options[&#34;backupcopy_proxy&#34;]
            }

        if &#34;use_source_if_proxy_unreachable&#34; in proxy_options:
            properties_dict[&#34;snapToTapeProxyToUseSource&#34;] = True

    self._set_subclient_properties(
        &#34;_commonProperties[&#39;snapCopyInfo&#39;]&#34;, properties_dict)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.virtualserver.kubernetes.KubernetesVirtualServerSubclient.full_app_restore_in_place"><code class="name flex">
<span>def <span class="ident">full_app_restore_in_place</span></span>(<span>self, apps_to_restore=None, overwrite=True, copy_precedence=0, proxy_client=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the FULL Application specified in the input list
to the location same as the actual location of the Application in Kubernetes cluster.</p>
<h2 id="args">Args</h2>
<p>apps_to_restore
(list)
&ndash;
List of applications to restore</p>
<p>overwrite
(bool)
&ndash;
overwrite the existing Applications if exists
default: True</p>
<p>copy_precedence
(int)
&ndash;
copy precedence value
default: 0</p>
<p>proxy_client
(str)
&ndash;
proxy client to be used for restore
default: proxy added in application group/cluster</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if inputs are not of correct type as per definition</p>
<pre><code>if failed to initialize job

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/kubernetes.py#L578-L638" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def full_app_restore_in_place(
        self,
        apps_to_restore=None,
        overwrite=True,
        copy_precedence=0,
        proxy_client=None):
    &#34;&#34;&#34;Restores the FULL Application specified in the input list
        to the location same as the actual location of the Application in Kubernetes cluster.

        Args:
            apps_to_restore     (list)      --  List of applications to restore

            overwrite           (bool)      --  overwrite the existing Applications if exists
                                                default: True

            copy_precedence     (int)       --  copy precedence value
                                                default: 0

            proxy_client        (str)       --  proxy client to be used for restore
                                                default: proxy added in application group/cluster

        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:
                if inputs are not of correct type as per definition

                if failed to initialize job

                if response is empty

                if response is not success

    &#34;&#34;&#34;

    restore_option = {}
    # check input parameters are correct
    if apps_to_restore and not isinstance(apps_to_restore, list):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
    if copy_precedence:
        restore_option[&#39;copy_precedence_applicable&#39;] = True

    if proxy_client is not None:
        restore_option[&#39;client&#39;] = proxy_client

    kubernetes_host = self._client_object.client_name

    # set attr for all the option in restore xml from user inputs
    self._set_restore_inputs(
        restore_option,
        vm_to_restore=self._set_vm_to_restore(apps_to_restore),
        in_place=True,
        esx_host=kubernetes_host,
        volume_level_restore=1,
        unconditional_overwrite=overwrite,
        copy_precedence=copy_precedence
    )

    request_json = self._prepare_kubernetes_inplace_restore_json(restore_option)
    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.virtualserver.kubernetes.KubernetesVirtualServerSubclient.full_app_restore_out_of_place"><code class="name flex">
<span>def <span class="ident">full_app_restore_out_of_place</span></span>(<span>self, apps_to_restore, restore_namespace, restored_app_name=None, kubernetes_client=None, storage_class=None, overwrite=True, copy_precedence=0, proxy_client=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the FULL Application specified in the input list
to the provided Kubernetes client at the specified namespace with storage class.
If the provided client name is none then it restores the Full Application
to the source Kubernetes client and corresponding namespace and storage class.</p>
<h2 id="args">Args</h2>
<p>apps_to_restore
(list)
&ndash;
List of Applications that is to be restored</p>
<p>restored_app_name
(dict)
&ndash;
Dictionary mapping new name of Applications</p>
<p>kubernetes_client
(str)
&ndash;
Name of the Kubernetes client where the Application should be restored
Restores to the source Kubernetes client if this value is not specified</p>
<p>storage_class
(str)
&ndash;
Storage class for the PVC to be restored with.
Uses source storage class if not specified.</p>
<p>restore_namespace
(str)
&ndash;
Target namespace where Applications are to be restored</p>
<p>overwrite
(bool)
&ndash;
overwrite the existing Applications if exists
default: True</p>
<p>copy_precedence
(int)
&ndash;
copy precedence value
default: 0</p>
<p>proxy_client
(str)
&ndash;
destination proxy client</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if inputs are not of correct type as per definition</p>
<pre><code>if failed to initialize job

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/kubernetes.py#L121-L210" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def full_app_restore_out_of_place(
        self,
        apps_to_restore,
        restore_namespace,
        restored_app_name=None,
        kubernetes_client=None,
        storage_class=None,
        overwrite=True,
        copy_precedence=0,
        proxy_client=None,
):
    &#34;&#34;&#34;Restores the FULL Application specified in the input list
        to the provided Kubernetes client at the specified namespace with storage class.
        If the provided client name is none then it restores the Full Application
        to the source Kubernetes client and corresponding namespace and storage class.

        Args:
            apps_to_restore         (list)  --  List of Applications that is to be restored

            restored_app_name       (dict)  --  Dictionary mapping new name of Applications

            kubernetes_client       (str)   --  Name of the Kubernetes client where the Application should be restored
                                                Restores to the source Kubernetes client if this value is not specified

            storage_class           (str)   --  Storage class for the PVC to be restored with.
                                                Uses source storage class if not specified.

            restore_namespace       (str)   --  Target namespace where Applications are to be restored

            overwrite               (bool)  --  overwrite the existing Applications if exists
                                                default: True

            copy_precedence          (int)  --  copy precedence value
                                                  default: 0

            proxy_client              (str)    --  destination proxy client

        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:
                if inputs are not of correct type as per definition

                if failed to initialize job

                if response is empty

                if response is not success

    &#34;&#34;&#34;

    restore_option = {}

    # check mandatory input parameters are correct
    if apps_to_restore and not isinstance(apps_to_restore, list):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    # populating proxy client. It assumes the proxy controller added in instance
    # properties if not specified
    if proxy_client is not None:
        restore_option[&#39;client&#39;] = proxy_client

    if restored_app_name:
        if not(isinstance(apps_to_restore, list) or
               isinstance(restored_app_name, dict)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        restore_option[&#39;restore_new_name&#39;] = restored_app_name

    if not kubernetes_client:
        kubernetes_client = self._client_object.client_name

    restore_option_copy = restore_option.copy()

    self._set_restore_inputs(
        restore_option,
        in_place=False,
        vcenter_client=kubernetes_client,
        datastore=storage_class,
        esx_host=kubernetes_client,
        datacenter=restore_namespace,
        unconditional_overwrite=overwrite,
        vm_to_restore=self._set_vm_to_restore(apps_to_restore),
        copy_precedence=copy_precedence,
        volume_level_restore=1,
        source_item=[]
    )

    request_json = self._prepare_kubernetes_restore_json(restore_option)
    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.virtualserver.kubernetes.KubernetesVirtualServerSubclient.guest_file_restore"><code class="name flex">
<span>def <span class="ident">guest_file_restore</span></span>(<span>self, application_name, destination_path, volume_level_restore, disk_name=None, proxy_client=None, restore_list=None, restore_pvc_guid=None, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>perform Guest file restore of the provided path</p>
<h2 id="args">Args</h2>
<p>application_name_name
(str)
&ndash;
Name of the source application
destination_path
(str)
&ndash;
Path at the destination to restore at
volume_level_restore
(str)
&ndash;
Flag to denote volume_level_restore
Accepted values -
6 for restore to PVC
7 for FS Destination restore
disk_name
(str)
&ndash;
Name of the source PVC
proxy_client
(str)
&ndash;
Access node for restore
restore_list
(str)
&ndash;
List of files or folders to restore. Contains Full path
of files or folders relative to PVC mount point.
Eg. if /tmp is the mount point with files or folder /tmp/folder1/file1,
restore list should have format 'folder1/file1'
restore_pvc_guid
(str)
&ndash;
strGUID of the target PVC</p>
<h2 id="kwargs">Kwargs</h2>
<p>copy_precedence
(int)
&ndash;
To set copy precedence for restore
disk_extension
(str)
&ndash;
Extention of the disk
unconditional_overwrite (int)
&ndash;
To set unconditional overwrite for restore
show_deleted_files
(bool)
&ndash;
Whether to show deleted files in browse
in_place
(bool)
&ndash;
If restore job is inplace</p>
<h2 id="raises">Raises</h2>
<p>SDK Exception if
-inputs are not of correct type as per definition</p>
<pre><code>-invalid volume_level_restore passed
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/kubernetes.py#L916-L1055" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def guest_file_restore(self,
                       application_name,
                       destination_path,
                       volume_level_restore,
                       disk_name=None,
                       proxy_client=None,
                       restore_list=None,
                       restore_pvc_guid=None,
                       **kwargs):
    &#34;&#34;&#34;perform Guest file restore of the provided path

    Args:
        application_name_name   (str)   --  Name of the source application
        destination_path        (str)   --  Path at the destination to restore at
        volume_level_restore    (str)   --  Flag to denote volume_level_restore
                                            Accepted values -
                                            6 for restore to PVC
                                            7 for FS Destination restore
        disk_name               (str)   --  Name of the source PVC
        proxy_client            (str)   --  Access node for restore
        restore_list            (str)   --  List of files or folders to restore. Contains Full path
                                            of files or folders relative to PVC mount point.
                                            Eg. if /tmp is the mount point with files or folder /tmp/folder1/file1,
                                            restore list should have format &#39;folder1/file1&#39;
        restore_pvc_guid        (str)   --  strGUID of the target PVC

    Kwargs:
        copy_precedence         (int)   --  To set copy precedence for restore
        disk_extension          (str)   --  Extention of the disk
        unconditional_overwrite (int)   --  To set unconditional overwrite for restore
        show_deleted_files      (bool)  --  Whether to show deleted files in browse
        in_place                (bool)  --  If restore job is inplace

    Raises:
        SDK Exception if
            -inputs are not of correct type as per definition

            -invalid volume_level_restore passed
    &#34;&#34;&#34;
    vm_names, vm_ids = self._get_vm_ids_and_names_dict_from_browse()
    _guest_file_rst_options = {}
    _advanced_restore_options = {}

    copy_precedence = kwargs.get(&#34;copy_precedence&#34;, 0)
    disk_extn = kwargs.get(&#34;disk_extension&#34;, &#39;&#39;)
    overwrite = kwargs.get(&#34;unconditional_overwrite&#34;, 1)
    unconditional_overwrite = kwargs.get(&#39;unconditional_overwrite&#39;, False)
    show_deleted_files = kwargs.get(&#39;show_deleted_files&#39;, False)
    in_place = kwargs.get(&#39;in_place&#39;, False)

    # check if inputs are correct
    if not (isinstance(application_name, str) and
            isinstance(destination_path, str) and
            isinstance(disk_name, str)):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
    if volume_level_restore not in [6, 7]:
        raise SDKException(&#34;Subclient&#34;, &#34;102&#34;, &#34;Invalid volume level restore type passed&#34;)

    if copy_precedence:
        _guest_file_rst_options[&#39;copy_precedence_applicable&#39;] = True

    # fetching all disks from the application
    disk_list, disk_info_dict = self.disk_level_browse(
        &#34;\\&#34; + vm_ids[application_name])

    # Filter out disks with specified extension from disk list
    disk_list = list(filter(lambda name: self._get_disk_extension([name]) == disk_extn, disk_list))
    disk_info_dict = {disk: disk_info_dict[disk] for disk in disk_list}

    _guest_file_rst_options[&#34;destination_vendor&#34;] = \
        self._backupset_object._instance_object._vendor_id

    if proxy_client is not None:
        _guest_file_rst_options[&#39;client&#39;] = proxy_client
    else:
        _guest_file_rst_options[&#39;client&#39;] = self._backupset_object._instance_object.co_ordinator

    # set Source item List
    src_item_list = []
    for each_item in restore_list:
        item = &#34;\\&#34;.join(each_item.split(&#39;/&#39;))
        src_item_list.append( &#34;\\&#34; + vm_ids[application_name] + &#34;\\&#34; + disk_name + &#34;\\&#34; + item)

    _guest_file_rst_options[&#39;paths&#39;] = src_item_list

    if volume_level_restore == 6:

        if in_place:
            restore_pvc_guid = &#34;{}`PersistentVolumeClaim`{}&#34;.format(
                vm_ids[application_name].split(&#39;`&#39;)[0], disk_name
            )
            new_name = disk_name

        else:
            new_name = restore_pvc_guid.split(&#39;`&#39;)[-2]
            _advanced_restore_options[&#39;datacenter&#39;] = &#34;none&#34;

        new_guid = restore_pvc_guid

    else:

        new_guid = &#34;{}`PersistentVolumeClaim`{}&#34;.format(
            vm_ids[application_name].split(&#39;`&#39;)[0], disk_name
        )
        new_name = disk_name

    _guest_file_rst_options[&#39;in_place&#39;] = in_place
    _guest_file_rst_options[&#39;volume_level_restore&#39;] = volume_level_restore
    _guest_file_rst_options[&#39;unconditional_overwrite&#39;] = unconditional_overwrite
    _guest_file_rst_options[&#39;show_deleted_files&#39;] = show_deleted_files

    _advanced_restore_options[&#39;new_guid&#39;] = new_guid
    _advanced_restore_options[&#39;new_name&#39;] = new_name
    _advanced_restore_options[&#39;name&#39;] = disk_name
    _advanced_restore_options[&#39;guid&#39;] = vm_ids[application_name]
    _advanced_restore_options[&#39;end_user_vm_restore&#39;] = True

    # set advanced restore options disks
    _disk_dict = self._disk_dict_pattern(disk_name, &#34;&#34;)
    _advanced_restore_options[&#39;disks&#39;] = [_disk_dict]

    advanced_options_dict = self._json_restore_advancedRestoreOptions(_advanced_restore_options)
    self._advanced_restore_option_list.append(advanced_options_dict)

    self._set_restore_inputs(
        _guest_file_rst_options,
        in_place=False,
        copy_precedence=copy_precedence,
        destination_path=destination_path,
        paths=src_item_list
    )

    request_json = self._prepare_disk_restore_json(_guest_file_rst_options)

    # Populate the advancedRestoreOptions section
    self._virtualserver_option_restore_json[&#34;diskLevelVMRestoreOption&#34;][
        &#34;advancedRestoreOptions&#34;] = self._advanced_restore_option_list
    self._advanced_restore_option_list = []

    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.virtualserver.kubernetes.KubernetesVirtualServerSubclient.guest_files_browse"><code class="name flex">
<span>def <span class="ident">guest_files_browse</span></span>(<span>self, application_path='\\', show_deleted_files=False, restore_index=True, from_date=0, to_date=0, copy_precedence=0, media_agent='')</span>
</code></dt>
<dd>
<div class="desc"><p>Browses the Files and Folders inside a Virtual Machine in the time
range specified.</p>
<p>Args:
application_path
(str)
&ndash;
folder path to get the contents
of
default: '';
returns the root of the Backup
content</p>
<pre><code> show_deleted_files  (bool)  --  include deleted files in the
                                 content or not default: False

 restore_index       (bool)  --  restore index if it is not cached
                                 default: True

 from_date           (int)   --  date to get the contents after
                                 format: dd/MM/YYYY

                                 gets contents from 01/01/1970
                                 if not specified
                                 default: 0

 to_date             (int)  --  date to get the contents before
                                format: dd/MM/YYYY

                                gets contents till current day
                                if not specified
                                default: 0

 copy_precedence     (int)   --  copy precedence to be used
                                     for browsing

 media_agent         (str)   --  Browse MA via with Browse has to happen.
                                 It can be MA different than Storage Policy MA
</code></pre>
<p>Returns:
list - list of all folders or files with their full paths
inside the input path</p>
<pre><code> dict - path along with the details like name, file/folder,
        size, modification time
</code></pre>
<p>Raises:
SDKException:
if from date value is incorrect</p>
<pre><code>     if to date value is incorrect

     if to date is less than from date

     if failed to browse content

     if response is empty

     if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/kubernetes.py#L1057-L1125" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def guest_files_browse(
        self,
        application_path=&#39;\\&#39;,
        show_deleted_files=False,
        restore_index=True,
        from_date=0,
        to_date=0,
        copy_precedence=0,
        media_agent=&#34;&#34;):
    &#34;&#34;&#34;Browses the Files and Folders inside a Virtual Machine in the time
       range specified.

        Args:
            application_path    (str)   --  folder path to get the contents
                                            of
                                            default: &#39;\\&#39;;
                                            returns the root of the Backup
                                            content

            show_deleted_files  (bool)  --  include deleted files in the
                                            content or not default: False

            restore_index       (bool)  --  restore index if it is not cached
                                            default: True

            from_date           (int)   --  date to get the contents after
                                            format: dd/MM/YYYY

                                            gets contents from 01/01/1970
                                            if not specified
                                            default: 0

            to_date             (int)  --  date to get the contents before
                                           format: dd/MM/YYYY

                                           gets contents till current day
                                           if not specified
                                           default: 0

            copy_precedence     (int)   --  copy precedence to be used
                                                for browsing

            media_agent         (str)   --  Browse MA via with Browse has to happen.
                                            It can be MA different than Storage Policy MA

        Returns:
            list - list of all folders or files with their full paths
                   inside the input path

            dict - path along with the details like name, file/folder,
                   size, modification time

        Raises:
            SDKException:
                if from date value is incorrect

                if to date value is incorrect

                if to date is less than from date

                if failed to browse content

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    return self.browse_in_time(
        vm_path, show_deleted_files, restore_index, False, from_date, to_date, copy_precedence,
        vm_files_browse=False, media_agent=media_agent)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.virtualserver.kubernetes.KubernetesVirtualServerSubclient.namespace_restore_in_place"><code class="name flex">
<span>def <span class="ident">namespace_restore_in_place</span></span>(<span>self, namespace_to_restore, overwrite=True, copy_precedence=0, proxy_client=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Perform a namespace-level restore in-place</p>
<h2 id="args">Args</h2>
<p>namespace_to_restore
(list)
&ndash;
List of namespaces to restore</p>
<p>overwrite
(bool)
&ndash;
Overwrite the existing namespace
Default: true</p>
<p>copy_precedence
(int)
&ndash;
Copy preceedence value</p>
<p>proxy_client
(str)
&ndash;
Name of the proxy client to launch restore
Default : None (Automatic)</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if inputs are not of correct type as per definition

if failed to initialize job

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/kubernetes.py#L1259-L1328" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def namespace_restore_in_place(
        self,
        namespace_to_restore,
        overwrite=True,
        copy_precedence=0,
        proxy_client=None
):
    &#34;&#34;&#34;Perform a namespace-level restore in-place

        Args:

            namespace_to_restore        (list)  --  List of namespaces to restore

            overwrite                   (bool)  --  Overwrite the existing namespace
                                                    Default: true

            copy_precedence             (int)   --  Copy preceedence value

            proxy_client                (str)   --  Name of the proxy client to launch restore
                                                    Default : None (Automatic)
        Returns:

            object - instance of the Job class for this restore job

        Raises:

            SDKException:

                if inputs are not of correct type as per definition

                if failed to initialize job

                if response is empty

                if response is not success
    &#34;&#34;&#34;

    restore_options = {}

    # Check mandatory input parameters are correct
    if namespace_to_restore and not type(namespace_to_restore) is list:
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    # Populating proxy client. It automatically fetches proxy controller from subclient/instance level
    # property if not specified
    if proxy_client is not None:
        restore_options[&#39;client&#39;] = proxy_client

    apps_to_restore = []

    namespace_apps, namespace_app_map = self._get_apps_in_namespace(namespace_to_restore)
    apps_to_restore.extend(namespace_apps)
    apps_to_restore.extend(namespace_to_restore)

    client_name = self._client_object.client_name
    self._set_restore_inputs(
        restore_options,
        vm_to_restore=self._set_vm_to_restore(apps_to_restore),
        in_place=True,
        esx_host=client_name,
        esx_server_name=&#34;&#34;,
        volume_level_restore=1,
        unconditional_overwrite=overwrite,
        disk_option=self._disk_option[&#39;Original&#39;],
        transport_mode=self._transport_mode[&#39;Auto&#39;],
        copy_precedence=copy_precedence
    )

    request_json = self._prepare_kubernetes_inplace_restore_json(restore_options)
    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.virtualserver.kubernetes.KubernetesVirtualServerSubclient.namespace_restore_out_of_place"><code class="name flex">
<span>def <span class="ident">namespace_restore_out_of_place</span></span>(<span>self, namespace_to_restore, target_namespace_name={}, target_cluster_name=None, storage_class_map=None, overwrite=True, copy_precedence=0, proxy_client=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Perform a namespace-level restore out-of-place</p>
<h2 id="args">Args</h2>
<p>namespace_to_restore
(list)
&ndash;
List of namespaces to restore</p>
<p>target_namespace_name
(dict)
&ndash;
Target namespace name to perform restore at
Eg. {'namespace1': 'namespace1-rst'}</p>
<p>target_cluster_name
(str)
&ndash;
Name of the target cluster to restore at</p>
<p>storage_class_map
(dict)
&ndash;
Mapping of storage classes for transformation
Eg. {'rook-ceph-block' : 'azurefile'}</p>
<p>overwrite
(bool)
&ndash;
Overwrite the existing namespace
Default: true</p>
<p>copy_precedence
(int)
&ndash;
Copy preceedence value</p>
<p>proxy_client
(str)
&ndash;
Name of the proxy client to launch restore
Default : None (Automatic)</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if inputs are not of correct type as per definition

if failed to initialize job

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/kubernetes.py#L1152-L1257" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def namespace_restore_out_of_place(
        self,
        namespace_to_restore,
        target_namespace_name={},
        target_cluster_name=None,
        storage_class_map=None,
        overwrite=True,
        copy_precedence=0,
        proxy_client=None
):
    &#34;&#34;&#34;Perform a namespace-level restore out-of-place

        Args:

            namespace_to_restore        (list)  --  List of namespaces to restore

            target_namespace_name       (dict)  --  Target namespace name to perform restore at
                                                    Eg. {&#39;namespace1&#39;: &#39;namespace1-rst&#39;}

            target_cluster_name         (str)   --  Name of the target cluster to restore at

            storage_class_map           (dict)  --  Mapping of storage classes for transformation
                                                    Eg. {&#39;rook-ceph-block&#39; : &#39;azurefile&#39;}

            overwrite                   (bool)  --  Overwrite the existing namespace
                                                    Default: true

            copy_precedence             (int)   --  Copy preceedence value

            proxy_client                (str)   --  Name of the proxy client to launch restore
                                                    Default : None (Automatic)
        Returns:

            object - instance of the Job class for this restore job

        Raises:

            SDKException:

                if inputs are not of correct type as per definition

                if failed to initialize job

                if response is empty

                if response is not success
    &#34;&#34;&#34;

    restore_options = {}

    # Check mandatory input parameters are correct
    if namespace_to_restore and not type(namespace_to_restore) is list:
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    # Populating proxy client. It automatically fetches proxy controller from subclient/instance level
    # property if not specified
    if proxy_client is not None:
        restore_options[&#39;client&#39;] = proxy_client

    restore_new_name = {}
    apps_to_restore = []

    if not type(target_namespace_name) is dict:
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
    for ns in namespace_to_restore:
        if ns not in target_namespace_name:
            target_namespace_name[ns] = ns
    restore_new_name.update(target_namespace_name)

    namespace_apps, namespace_app_map = self._get_apps_in_namespace(namespace_to_restore)
    apps_to_restore.extend(namespace_apps)

    for app in apps_to_restore:
        restore_new_name[app] = app
    apps_to_restore.extend(namespace_to_restore)

    restore_options[&#39;restore_new_name&#39;] = restore_new_name
    restore_options[&#39;namespace_app_map&#39;] = namespace_app_map

    if not target_cluster_name:
        target_cluster_name = self._client_object.client_name

    self.reinitialize_vm_names_browse()

    self._set_restore_inputs(
        restore_options,
        in_place=False,
        vcenter_client=target_cluster_name,
        esx_host=target_cluster_name,
        esx_server=None,
        unconditional_overwrite=overwrite,
        power_on=True,
        vm_to_restore=self._set_vm_to_restore(apps_to_restore),
        disk_option=self._disk_option[&#39;Original&#39;],
        transport_mode=self._transport_mode[&#39;Auto&#39;],
        copy_precedence=copy_precedence,
        volume_level_restore=1,
        source_item=[],
        source_ip=None,
        destination_ip=None,
        network=None,
        storage_class_map=storage_class_map
    )

    request_json = self._prepare_kubernetes_restore_json(restore_options)
    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.virtualserver.kubernetes.KubernetesVirtualServerSubclient.set_advanced_vm_restore_options"><code class="name flex">
<span>def <span class="ident">set_advanced_vm_restore_options</span></span>(<span>self, vm_to_restore, restore_option)</span>
</code></dt>
<dd>
<div class="desc"><p>set the advanced restore options for all vm in restore
param</p>
<pre><code>vm_to_restore               - Name of the Application to restore

restore_option              - restore options that need to be set for advanced restore option

power_on                    - power on the Application after restore

add_to_failover             - Register the Application to Failover Cluster

datastore                   - Datastore where the Application needs to be restored

disks   (list of dict)      - list with dict for each disk in Application
                                eg: [{
                                            name:"pvc-1"
                                            datastore:"storageclass-1"
                                        }
                                        {
                                            name:"pvc-2"
                                            datastore:"storageclass-2"
                                        }
                                    ]
guid                        - GUID of the Application needs to be restored

new_name                    - New name for the Application to be restored

esx_host                    - client name where it need to be restored

name                        - name of the Application to be restored
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/virtualserver/kubernetes.py#L472-L576" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_advanced_vm_restore_options(self, vm_to_restore, restore_option):
    &#34;&#34;&#34;
    set the advanced restore options for all vm in restore
    param

        vm_to_restore               - Name of the Application to restore

        restore_option              - restore options that need to be set for advanced restore option

        power_on                    - power on the Application after restore

        add_to_failover             - Register the Application to Failover Cluster

        datastore                   - Datastore where the Application needs to be restored

        disks   (list of dict)      - list with dict for each disk in Application
                                        eg: [{
                                                    name:&#34;pvc-1&#34;
                                                    datastore:&#34;storageclass-1&#34;
                                                }
                                                {
                                                    name:&#34;pvc-2&#34;
                                                    datastore:&#34;storageclass-2&#34;
                                                }
                                            ]
        guid                        - GUID of the Application needs to be restored

        new_name                    - New name for the Application to be restored

        esx_host                    - client name where it need to be restored

        name                        - name of the Application to be restored

    &#34;&#34;&#34;

    # Set the new name for the restored Application.
    # If new_name is not given, it restores the Application with same name
    # with suffix Delete.
    vm_names, vm_ids = self._get_vm_ids_and_names_dict_from_browse()
    browse_result = self.vm_files_browse()
    application_id = vm_ids[vm_to_restore]

    # vs metadata from browse result
    _metadata = browse_result[1][(&#39;\\&#39; + vm_to_restore)]
    vs_metadata = _metadata[&#34;advanced_data&#34;][&#34;browseMetaData&#34;][&#34;virtualServerMetaData&#34;]
    if restore_option[&#39;in_place&#39;]:
        folder_path = vs_metadata.get(&#34;inventoryPath&#34;, &#39;&#39;)
        instance_size = vs_metadata.get(&#34;instanceSize&#34;, &#39;&#39;)
    else:
        folder_path = &#39;&#39;
        instance_size = &#39;&#39;
    if restore_option.get(&#39;resourcePoolPath&#39;):
        restore_option[&#39;resourcePoolPath&#39;] = vs_metadata[&#39;resourcePoolPath&#39;]
    if restore_option.get(&#39;datacenter&#39;):
        restore_option[&#39;datacenter&#39;] = restore_option.get(&#39;datacenter&#39;)
    if restore_option.get(&#39;terminationProtected&#39;):
        restore_option[&#39;terminationProtected&#39;] = vs_metadata.get(&#39;terminationProtected&#39;, &#39;&#39;)
    if restore_option.get(&#39;iamRole&#39;):
        restore_option[&#39;iamRole&#39;] = vs_metadata.get(&#39;role&#39;, &#39;&#39;)
    if restore_option.get(&#39;securityGroups&#39;):
        _security_groups = self._find_security_groups(vs_metadata[&#39;networkSecurityGroups&#39;])
        restore_option[&#39;securityGroups&#39;] = _security_groups
    if restore_option.get(&#39;keyPairList&#39;):
        _keypair_list = self._find_keypair_list(vs_metadata[&#39;loginKeyPairs&#39;])
        restore_option[&#39;keyPairList&#39;] = _keypair_list

    # populate restore source item
    restore_option[&#39;paths&#39;].append(&#34;\\&#34; + application_id)
    restore_option[&#39;name&#39;] = vm_to_restore
    restore_option[&#39;guid&#39;] = application_id
    restore_option[&#34;FolderPath&#34;] = folder_path
    restore_option[&#34;ResourcePool&#34;] = &#34;/&#34;

    # populate restore disk and datastore
    vm_disks = []
    new_name = vm_to_restore

    storage_class_map = restore_option.get(&#39;storage_class_map&#39;, None)

    # To populate disk list for each app in case of namespace restore
    if storage_class_map:
        pvc_list = self._get_app_pvc(application_id)
        for pvc in pvc_list:
            storageclass_name = pvc[&#39;storageclass&#39;]
            pvc_name = pvc[&#39;name&#39;]

            # If &#39;datastore&#39; is passed then it&#39;s full app restore, else
            # it is namespace level restore.
            # Namespace level restore can be passed with storage class mapping
            if storageclass_name in storage_class_map:
                storageclass_name = storage_class_map[storageclass_name]
            _disk_dict = self._disk_dict_pattern(pvc_name, storageclass_name, pvc_name)
            vm_disks.append(_disk_dict)

    restore_option[&#34;disks&#34;] = vm_disks

    self._set_restore_inputs(
        restore_option,
        esx_host=restore_option.get(&#39;esx_host&#39;) or vs_metadata[&#39;esxHost&#39;],
        instance_size=restore_option.get(&#39;instanceSize&#39;, instance_size),
        new_name=restore_option.get(&#39;new_name&#39;, &#34;Delete&#34; + vm_to_restore)
    )

    temp_dict = self._json_restore_advancedRestoreOptions(restore_option)
    self._advanced_restore_option_list.append(temp_dict)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient">VirtualServerSubclient</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.allow_multiple_readers" href="../../subclient.html#cvpysdk.subclient.Subclient.allow_multiple_readers">allow_multiple_readers</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.amazon_defaults" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.amazon_defaults">amazon_defaults</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.backup" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.backup">backup</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.browse" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.browse">browse</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.browse_in_time" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.browse_in_time">browse_in_time</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.cbtvalue" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.cbtvalue">cbtvalue</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.content" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.content">content</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.data_readers" href="../../subclient.html#cvpysdk.subclient.Subclient.data_readers">data_readers</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.deduplication_options" href="../../subclient.html#cvpysdk.subclient.Subclient.deduplication_options">deduplication_options</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.description" href="../../subclient.html#cvpysdk.subclient.Subclient.description">description</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.disable_backup" href="../../subclient.html#cvpysdk.subclient.Subclient.disable_backup">disable_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.disable_intelli_snap" href="../../subclient.html#cvpysdk.subclient.Subclient.disable_intelli_snap">disable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.disk_level_browse" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.disk_level_browse">disk_level_browse</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.disk_pattern" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.disk_pattern">disk_pattern</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.display_name" href="../../subclient.html#cvpysdk.subclient.Subclient.display_name">display_name</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.enable_backup" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_backup">enable_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.enable_backup_at_time" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_backup_at_time">enable_backup_at_time</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.enable_trueup" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_trueup">enable_trueup</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.enable_trueup_days" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_trueup_days">enable_trueup_days</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.encryption_flag" href="../../subclient.html#cvpysdk.subclient.Subclient.encryption_flag">encryption_flag</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.exclude_from_sla" href="../../subclient.html#cvpysdk.subclient.Subclient.exclude_from_sla">exclude_from_sla</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.find" href="../../subclient.html#cvpysdk.subclient.Subclient.find">find</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.find_latest_job" href="../../subclient.html#cvpysdk.subclient.Subclient.find_latest_job">find_latest_job</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.get_ma_associated_storagepolicy" href="../../subclient.html#cvpysdk.subclient.Subclient.get_ma_associated_storagepolicy">get_ma_associated_storagepolicy</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.get_nics_from_browse" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.get_nics_from_browse">get_nics_from_browse</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.index_server" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.index_server">index_server</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.instance_proxy" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.instance_proxy">instance_proxy</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.is_backup_enabled" href="../../subclient.html#cvpysdk.subclient.Subclient.is_backup_enabled">is_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.is_blocklevel_backup_enabled" href="../../subclient.html#cvpysdk.subclient.Subclient.is_blocklevel_backup_enabled">is_blocklevel_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.is_default_subclient" href="../../subclient.html#cvpysdk.subclient.Subclient.is_default_subclient">is_default_subclient</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.is_intelli_snap_enabled" href="../../subclient.html#cvpysdk.subclient.Subclient.is_intelli_snap_enabled">is_intelli_snap_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.is_on_demand_subclient" href="../../subclient.html#cvpysdk.subclient.Subclient.is_on_demand_subclient">is_on_demand_subclient</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.is_trueup_enabled" href="../../subclient.html#cvpysdk.subclient.Subclient.is_trueup_enabled">is_trueup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.last_backup_time" href="../../subclient.html#cvpysdk.subclient.Subclient.last_backup_time">last_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.list_media" href="../../subclient.html#cvpysdk.subclient.Subclient.list_media">list_media</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.live_sync" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.live_sync">live_sync</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.metadata" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.metadata">metadata</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.name" href="../../subclient.html#cvpysdk.subclient.Subclient.name">name</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.network_agent" href="../../subclient.html#cvpysdk.subclient.Subclient.network_agent">network_agent</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.next_backup_time" href="../../subclient.html#cvpysdk.subclient.Subclient.next_backup_time">next_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.parse_nics_xml" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.parse_nics_xml">parse_nics_xml</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.plan" href="../../subclient.html#cvpysdk.subclient.Subclient.plan">plan</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.preview_content" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.preview_content">preview_content</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.properties" href="../../subclient.html#cvpysdk.subclient.Subclient.properties">properties</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.quiesce_file_system" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.quiesce_file_system">quiesce_file_system</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.read_buffer_size" href="../../subclient.html#cvpysdk.subclient.Subclient.read_buffer_size">read_buffer_size</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.refresh" href="../../subclient.html#cvpysdk.subclient.Subclient.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.restore_in_place" href="../../subclient.html#cvpysdk.subclient.Subclient.restore_in_place">restore_in_place</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.restore_out_of_place" href="../../subclient.html#cvpysdk.subclient.Subclient.restore_out_of_place">restore_out_of_place</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.run_content_indexing" href="../../subclient.html#cvpysdk.subclient.Subclient.run_content_indexing">run_content_indexing</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.set_advanced_attach_disk_restore_options" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.set_advanced_attach_disk_restore_options">set_advanced_attach_disk_restore_options</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.set_backup_nodes" href="../../subclient.html#cvpysdk.subclient.Subclient.set_backup_nodes">set_backup_nodes</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.set_proxy_for_snap" href="../../subclient.html#cvpysdk.subclient.Subclient.set_proxy_for_snap">set_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.snapshot_engine_name" href="../../subclient.html#cvpysdk.subclient.Subclient.snapshot_engine_name">snapshot_engine_name</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.snapshot_storage_type" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.snapshot_storage_type">snapshot_storage_type</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.software_compression" href="../../subclient.html#cvpysdk.subclient.Subclient.software_compression">software_compression</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.storage_ma" href="../../subclient.html#cvpysdk.subclient.Subclient.storage_ma">storage_ma</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.storage_ma_id" href="../../subclient.html#cvpysdk.subclient.Subclient.storage_ma_id">storage_ma_id</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.storage_policy" href="../../subclient.html#cvpysdk.subclient.Subclient.storage_policy">storage_policy</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.subclient_guid" href="../../subclient.html#cvpysdk.subclient.Subclient.subclient_guid">subclient_guid</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.subclient_id" href="../../subclient.html#cvpysdk.subclient.Subclient.subclient_id">subclient_id</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.subclient_name" href="../../subclient.html#cvpysdk.subclient.Subclient.subclient_name">subclient_name</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.subclient_proxy" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.subclient_proxy">subclient_proxy</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.unset_proxy_for_snap" href="../../subclient.html#cvpysdk.subclient.Subclient.unset_proxy_for_snap">unset_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.update_properties" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.update_properties">update_properties</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.vm_diskfilter" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.vm_diskfilter">vm_diskfilter</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.vm_files_browse" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.vm_files_browse">vm_files_browse</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.vm_files_browse_in_time" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.vm_files_browse_in_time">vm_files_browse_in_time</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.vm_filter" href="../vssubclient.html#cvpysdk.subclients.vssubclient.VirtualServerSubclient.vm_filter">vm_filter</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.subclients.virtualserver" href="index.html">cvpysdk.subclients.virtualserver</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.subclients.virtualserver.kubernetes.ApplicationGroups" href="#cvpysdk.subclients.virtualserver.kubernetes.ApplicationGroups">ApplicationGroups</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.subclients.virtualserver.kubernetes.ApplicationGroups.browse" href="#cvpysdk.subclients.virtualserver.kubernetes.ApplicationGroups.browse">browse</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.kubernetes.ApplicationGroups.create_application_group" href="#cvpysdk.subclients.virtualserver.kubernetes.ApplicationGroups.create_application_group">create_application_group</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.kubernetes.ApplicationGroups.get_children_node" href="#cvpysdk.subclients.virtualserver.kubernetes.ApplicationGroups.get_children_node">get_children_node</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.subclients.virtualserver.kubernetes.KubernetesVirtualServerSubclient" href="#cvpysdk.subclients.virtualserver.kubernetes.KubernetesVirtualServerSubclient">KubernetesVirtualServerSubclient</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.subclients.virtualserver.kubernetes.KubernetesVirtualServerSubclient.disk_restore" href="#cvpysdk.subclients.virtualserver.kubernetes.KubernetesVirtualServerSubclient.disk_restore">disk_restore</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.kubernetes.KubernetesVirtualServerSubclient.enable_intelli_snap" href="#cvpysdk.subclients.virtualserver.kubernetes.KubernetesVirtualServerSubclient.enable_intelli_snap">enable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.kubernetes.KubernetesVirtualServerSubclient.full_app_restore_in_place" href="#cvpysdk.subclients.virtualserver.kubernetes.KubernetesVirtualServerSubclient.full_app_restore_in_place">full_app_restore_in_place</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.kubernetes.KubernetesVirtualServerSubclient.full_app_restore_out_of_place" href="#cvpysdk.subclients.virtualserver.kubernetes.KubernetesVirtualServerSubclient.full_app_restore_out_of_place">full_app_restore_out_of_place</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.kubernetes.KubernetesVirtualServerSubclient.guest_file_restore" href="#cvpysdk.subclients.virtualserver.kubernetes.KubernetesVirtualServerSubclient.guest_file_restore">guest_file_restore</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.kubernetes.KubernetesVirtualServerSubclient.guest_files_browse" href="#cvpysdk.subclients.virtualserver.kubernetes.KubernetesVirtualServerSubclient.guest_files_browse">guest_files_browse</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.kubernetes.KubernetesVirtualServerSubclient.namespace_restore_in_place" href="#cvpysdk.subclients.virtualserver.kubernetes.KubernetesVirtualServerSubclient.namespace_restore_in_place">namespace_restore_in_place</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.kubernetes.KubernetesVirtualServerSubclient.namespace_restore_out_of_place" href="#cvpysdk.subclients.virtualserver.kubernetes.KubernetesVirtualServerSubclient.namespace_restore_out_of_place">namespace_restore_out_of_place</a></code></li>
<li><code><a title="cvpysdk.subclients.virtualserver.kubernetes.KubernetesVirtualServerSubclient.set_advanced_vm_restore_options" href="#cvpysdk.subclients.virtualserver.kubernetes.KubernetesVirtualServerSubclient.set_advanced_vm_restore_options">set_advanced_vm_restore_options</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>