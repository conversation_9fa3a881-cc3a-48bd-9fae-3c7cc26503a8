<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.instances.mysqlinstance API documentation</title>
<meta name="description" content="File for operating on a MYSQL Instance …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.instances.mysqlinstance</code></h1>
</header>
<section id="section-intro">
<p>File for operating on a MYSQL Instance.</p>
<p>MYSQLInstance is the only class defined in this file.</p>
<p>MYSQLInstance: Derived class from Instance Base class, representing an
MYSQL instance, and to perform operations on that instance</p>
<h1 id="mysqlinstance">MYSQLInstance:</h1>
<pre><code>_get_instance_properties()      -- method to get the properties of the instance

_restore_json()                 -- returns the apppropriate JSON request to pass for
Restore In-Place

restore_in_place()              -- Restores the mysql data/log files specified in the
input paths list to the same location

restore_out_of_place()          -- method to perform out of place restore of MySQL data/log/recurring files to the
destination client.

_restore_browse_option_json()   -- setter for  browse option  property in restore

_restore_common_options_json()  -- setter for common options property in restore

_restore_destination_json()     -- setter for destination options property in restore

_restore_fileoption_json()      -- setter for file option property in restore

_restore_admin_option_json()    -- setter for admin option property in restore

_restore_mysql_option_json()    -- setter for MySQL restore option property in restore
</code></pre>
<h1 id="mysqlinstance-instance-attributes">MYSQLInstance instance Attributes:</h1>
<pre><code>**port**                            -- Returns the MySQL Server Port number

**mysql_username**                  -- Returns the MySQL Server username

**nt_username**                     -- Returns the MySQL Server nt username

**config_file**                     -- Returns the MySQL Server Config File location

**binary_directory**                -- Returns the MySQL Server Binary File location

**version**                         -- Returns the MySQL Server version number

**log_data_directory**              -- Returns the MySQL Server log data directory

**log_backup_sp_details**           -- Returns the MySQL Server Log backup SP details

**command_line_sp_details**         -- Returns the MySQL Server commandline SP details

**autodiscovery_enabled**           -- Returns the MySQL Server auto discovery enabled flag

**xtrabackup_bin_path**             -- Returns the MySQL Server xtrabackup bin path

**is_xtrabackup_enabled**           -- Returns the MySQL Server xtrabackup enabled flag

**proxy_options**                   -- Returns the MySQL Server proxy options

**mysql_enterprise_backup_binary_path** --  Returns the MySQL Enterprise backup binary path
details

**no_lock_status**                  --  Returns the No Lock check box status for MySQL Instance

**ssl_enabled**                     --  Returns(boolean) True/False based on SSL status
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/mysqlinstance.py#L1-L796" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;
File for operating on a MYSQL Instance.

MYSQLInstance is the only class defined in this file.

MYSQLInstance: Derived class from Instance Base class, representing an
                MYSQL instance, and to perform operations on that instance

MYSQLInstance:
==============

    _get_instance_properties()      -- method to get the properties of the instance

    _restore_json()                 -- returns the apppropriate JSON request to pass for
    Restore In-Place

    restore_in_place()              -- Restores the mysql data/log files specified in the
    input paths list to the same location

    restore_out_of_place()          -- method to perform out of place restore of MySQL data/log/recurring files to the
    destination client.

    _restore_browse_option_json()   -- setter for  browse option  property in restore

    _restore_common_options_json()  -- setter for common options property in restore

    _restore_destination_json()     -- setter for destination options property in restore

    _restore_fileoption_json()      -- setter for file option property in restore

    _restore_admin_option_json()    -- setter for admin option property in restore

    _restore_mysql_option_json()    -- setter for MySQL restore option property in restore


MYSQLInstance instance Attributes:
==================================

    **port**                            -- Returns the MySQL Server Port number

    **mysql_username**                  -- Returns the MySQL Server username

    **nt_username**                     -- Returns the MySQL Server nt username

    **config_file**                     -- Returns the MySQL Server Config File location

    **binary_directory**                -- Returns the MySQL Server Binary File location

    **version**                         -- Returns the MySQL Server version number

    **log_data_directory**              -- Returns the MySQL Server log data directory

    **log_backup_sp_details**           -- Returns the MySQL Server Log backup SP details

    **command_line_sp_details**         -- Returns the MySQL Server commandline SP details

    **autodiscovery_enabled**           -- Returns the MySQL Server auto discovery enabled flag

    **xtrabackup_bin_path**             -- Returns the MySQL Server xtrabackup bin path

    **is_xtrabackup_enabled**           -- Returns the MySQL Server xtrabackup enabled flag

    **proxy_options**                   -- Returns the MySQL Server proxy options

    **mysql_enterprise_backup_binary_path** --  Returns the MySQL Enterprise backup binary path
    details

    **no_lock_status**                  --  Returns the No Lock check box status for MySQL Instance

    **ssl_enabled**                     --  Returns(boolean) True/False based on SSL status

&#34;&#34;&#34;

from __future__ import unicode_literals
from ..instance import Instance
from ..exception import SDKException
from ..credential_manager import Credential


class MYSQLInstance(Instance):
    &#34;&#34;&#34;
    Class to represent a standalone MYSQL Instance
    &#34;&#34;&#34;

    def __init__(self, agent_object, instance_name, instance_id=None):
        &#34;&#34;&#34;Initialise the Subclient object.

            Args:
                agent_object    (object)  --  instance of the Agent class

                instance_name   (str)     --  name of the instance

                instance_id     (str)     --  id of the instance

                    default: None

            Returns:
                object - instance of the MYSQLInstance class

        &#34;&#34;&#34;
        self._browse_restore_json = None
        self._commonoption_restore_json = None
        self._destination_restore_json = None
        self._fileoption_restore_json = None
        self._instance = None
        self.admin_option_json = None
        self.mysql_restore_json = None
        super(MYSQLInstance, self).__init__(agent_object, instance_name, instance_id)

    @property
    def port(self):
        &#34;&#34;&#34;Returns the MySQL Server Port number.

        Returns:
            (str)   --  MySql server port number

        &#34;&#34;&#34;
        return self._properties.get(&#39;mySqlInstance&#39;, {}).get(&#39;port&#39;, None)

    @property
    def mysql_username(self):
        &#34;&#34;&#34;Returns the MySQL Server username.

        Returns:
            (str)   --  MySql server SA username

        &#34;&#34;&#34;
        credential_name = self._properties.get(&#39;credentialEntity&#39;, {}).get(&#39;credentialName&#39;, None)
        return Credential(self._commcell_object, credential_name).credential_user_name

    @property
    def nt_username(self):
        &#34;&#34;&#34;Returns the MySQL Server nt username.

        Returns:
            (str)   --  MySql server NT username

        &#34;&#34;&#34;
        return self._properties.get(&#39;mySqlInstance&#39;, {}).get(&#39;NTUser&#39;, {}).get(&#39;userName&#39;, None)

    @property
    def config_file(self):
        &#34;&#34;&#34;Returns the MySQL Server Config File location.

        Returns:
            (str)   --  MySql server config file location

        &#34;&#34;&#34;
        return self._properties.get(&#39;mySqlInstance&#39;, {}).get(&#39;ConfigFile&#39;, None)

    @property
    def binary_directory(self):
        &#34;&#34;&#34;Returns the MySQL Server Binary File location.

        Returns:
            (str)   --  MySql server binary directory

        &#34;&#34;&#34;
        return self._properties.get(&#39;mySqlInstance&#39;, {}).get(&#39;BinaryDirectory&#39;, None)

    @property
    def version(self):
        &#34;&#34;&#34;Returns the MySQL Server version number.

        Returns:
            (str)   --  MySql server version

        &#34;&#34;&#34;
        return self._properties.get(&#39;mySqlInstance&#39;, {}).get(&#39;version&#39;, None)

    @property
    def log_data_directory(self):
        &#34;&#34;&#34;Returns the MySQL Server log data directory.

        Returns:
            (str)   --  MySql server log directory path

        &#34;&#34;&#34;
        return self._properties.get(&#39;mySqlInstance&#39;, {}).get(&#39;LogDataDirectory&#39;, None)

    @property
    def log_backup_sp_details(self):
        &#34;&#34;&#34;Returns the MySQL Server Log backup SP details

        Returns:
            (dict)  --  MySql server log backup storage policy information

        &#34;&#34;&#34;
        log_storage_policy_name = self._properties.get(&#39;mySqlInstance&#39;, {}).get(
            &#39;logStoragePolicy&#39;, {}).get(&#39;storagePolicyName&#39;, None)
        log_storage_policy_id = self._properties.get(&#39;mySqlInstance&#39;, {}).get(
            &#39;logStoragePolicy&#39;, {}).get(&#39;storagePolicyId&#39;, None)

        log_sp = {&#34;storagePolicyName&#34;: log_storage_policy_name,
                  &#34;storagePolicyId&#34;: log_storage_policy_id}
        return log_sp

    @property
    def command_line_sp_details(self):
        &#34;&#34;&#34;Returns the MySQL Server commandline SP details.

        Returns:
            (dict)  --  MySql server commnadline storage policy information

        &#34;&#34;&#34;
        cmd_storage_policy_name = self._properties.get(&#39;mySqlInstance&#39;, {}).get(
            &#39;mysqlStorageDevice&#39;, {}).get(&#39;commandLineStoragePolicy&#39;, {}).get(
                &#39;storagePolicyName&#39;, None)
        cmd_storage_policy_id = self._properties.get(&#39;mySqlInstance&#39;, {}).get(
            &#39;mysqlStorageDevice&#39;, {}).get(&#39;commandLineStoragePolicy&#39;, {}).get(
                &#39;storagePolicyId&#39;, None)

        command_sp = {&#34;storagePolicyName&#34;: cmd_storage_policy_name,
                      &#34;storagePolicyId&#34;: cmd_storage_policy_id}
        return command_sp

    @property
    def autodiscovery_enabled(self):
        &#34;&#34;&#34;Returns the MySQL Server auto discovery enabled flag

        Returns:
            (bool)  --  True if auto discovery enabled
                        False if auto discovery not enabled

        &#34;&#34;&#34;
        return self._properties.get(&#39;mySqlInstance&#39;, {}).get(&#39;EnableAutoDiscovery&#39;, False)

    @autodiscovery_enabled.setter
    def autodiscovery_enabled(self, value):
        &#34;&#34;&#34;
        Sets the auto discovery attribute to True or False
        value (bool)   --  True or False

        &#34;&#34;&#34;
        properties = self._properties
        update = {
            &#34;EnableAutoDiscovery&#34;: value
        }
        properties[&#39;mySqlInstance&#39;] = update
        self.update_properties(properties)

    @property
    def xtrabackup_bin_path(self):
        &#34;&#34;&#34;Returns the MySQL Server xtrabackup bin path

        Returns:
            (str)   --  MySql server xtrabackup binary path

        &#34;&#34;&#34;
        return self._properties.get(
            &#39;mySqlInstance&#39;, {}).get(
                &#39;xtraBackupSettings&#39;, {}).get(&#39;xtraBackupBinPath&#39;, &#34;&#34;)

    @property
    def is_xtrabackup_enabled(self):
        &#34;&#34;&#34;Returns the MySQL Server xtrabackup enabled flag

        Returns:
            (bool)  --  True if xtrabackup is enabled
                        False if xtrabackup is not enabled

        &#34;&#34;&#34;
        return self._properties.get(
            &#39;mySqlInstance&#39;, {}).get(
                &#39;xtraBackupSettings&#39;, {}).get(&#39;enableXtraBackup&#39;, False)

    @property
    def proxy_options(self):
        &#34;&#34;&#34;Returns the MySQL Server proxy options

        Returns:
            (dict)  --  MySql server proxy information

        &#34;&#34;&#34;
        proxy_settings = self._properties.get(&#39;mySqlInstance&#39;, {}).get(&#39;proxySettings&#39;, {})
        proxy_opt = {
            &#34;isUseSSL&#34;: proxy_settings.get(&#39;isUseSSL&#39;, False),
            &#34;isProxyEnabled&#34;: proxy_settings.get(&#39;isProxyEnabled&#39;, False),
            &#34;runBackupOnProxy&#34;: proxy_settings.get(&#39;runBackupOnProxy&#39;, False),
            &#34;instanceId&#34;: proxy_settings.get(&#39;proxyInstance&#39;, {}).get(&#39;instanceId&#39;, None),
            &#34;instanceName&#34;: proxy_settings.get(&#39;proxyInstance&#39;, {}).get(&#39;instanceName&#39;, None),
            &#34;clientId&#34;: proxy_settings.get(&#39;proxyInstance&#39;, {}).get(&#39;clientId&#39;, None),
            &#34;clientName&#34;: proxy_settings.get(&#39;proxyInstance&#39;, {}).get(&#39;clientName&#39;, None)}
        return proxy_opt

    @property
    def mysql_enterprise_backup_binary_path(self):
        &#34;&#34;&#34; Returns the MySQL Enterprise backup binary path detail

            Return Type: dict

        &#34;&#34;&#34;
        meb_settings = self._properties.get(&#39;mySqlInstance&#39;, {}).get(&#39;mebSettings&#39;, {})
        return meb_settings

    @mysql_enterprise_backup_binary_path.setter
    def mysql_enterprise_backup_binary_path(self, value):
        &#34;&#34;&#34; Setter for MySQL Enterprise backup binary path

            Args:

                value (str)  -- Contains the MySQL Enterprise backup binary path to be updated
                in MySQL Instance property

        &#34;&#34;&#34;
        if not isinstance(value, str):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        properties = self._properties
        meb_bin_path_update = {
            &#34;enableMEB&#34;: False if value == &#39;&#39; else True,
            &#34;mebBinPath&#34;: value
        }
        properties[&#39;mySqlInstance&#39;][&#39;mebSettings&#39;] = meb_bin_path_update
        self.update_properties(properties)

    @property
    def no_lock_status(self):
        &#34;&#34;&#34; Returns the status of No Lock Checkbox in MySQL Instance

            Returns:
            (bool)  --  True if No Lock checkbox is enabled
                        False if No Lock checkbox is disabled

        &#34;&#34;&#34;
        return self._properties.get(&#39;mySqlInstance&#39;, {}).get(&#39;EnableNoLocking&#39;, False)

    @no_lock_status.setter
    def no_lock_status(self, value):
        &#34;&#34;&#34; Setter for No Lock property in MySQL Instance

            Args:

                value (bool)  -- True or False to enable or disable the No Lock
                property in MySQL Instance

        &#34;&#34;&#34;
        if not isinstance(value, bool):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        properties = self._properties
        properties[&#39;mySqlInstance&#39;][&#39;EnableNoLocking&#39;] = value
        self.update_properties(properties)

    @property
    def ssl_enabled(self):
        &#34;&#34;&#34; Returns(boolean) True/False based on SSL status &#34;&#34;&#34;
        return self._properties.get(&#39;mySqlInstance&#39;, {}).get(&#39;sslEnabled&#39;, False)

    def _get_instance_properties(self):
        &#34;&#34;&#34;Gets the properties of this instance.

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        super(MYSQLInstance, self)._get_instance_properties()
        self._instance = {
            &#34;type&#34;: 0,
            &#34;clientName&#34;: self._agent_object._client_object.client_name,
            &#34;clientSidePackage&#34;: True,
            &#34;subclientName&#34;: &#34;&#34;,
            &#34;backupsetName&#34;: &#34;defaultDummyBackupSet&#34;,
            &#34;instanceName&#34;: self.instance_name,
            &#34;appName&#34;: self._agent_object.agent_name,
            &#34;consumeLicense&#34;: True
        }

    def _restore_json(self, **kwargs):
        &#34;&#34;&#34;Returns the JSON request to pass to the API as per the options selected by the user.

            Args:
                kwargs   (list)  --  list of options need to be set for restore

            Returns:
                dict - JSON request to pass to the API

        &#34;&#34;&#34;
        rest_json = super(MYSQLInstance, self)._restore_json(**kwargs)
        restore_option = {}
        if kwargs.get(&#34;restore_option&#34;):
            restore_option = kwargs[&#34;restore_option&#34;]
            for key in kwargs:
                if not key == &#34;restore_option&#34;:
                    restore_option[key] = kwargs[key]
        else:
            restore_option.update(kwargs)

        if restore_option[&#34;from_time&#34;] is None:
            restore_option[&#34;from_time&#34;] = {}

        if restore_option[&#34;to_time&#34;] is None:
            restore_option[&#34;to_time&#34;] = {}

        self._restore_admin_option_json(restore_option)
        self._restore_mysql_option_json(restore_option)
        rest_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;mySqlRstOption&#34;] = self.mysql_restore_json
        rest_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;adminOpts&#34;] = self.admin_option_json
        return rest_json

    def restore_in_place(
            self,
            path=None,
            staging=None,
            dest_client_name=None,
            dest_instance_name=None,
            data_restore=True,
            log_restore=False,
            overwrite=True,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            media_agent=None,
            table_level_restore=False,
            clone_env=False,
            clone_options=None,
            redirect_enabled=False,
            redirect_path=None,
            browse_jobid=None):
        &#34;&#34;&#34;Restores the mysql data/log files specified in the input paths list to the same location.

            Args:
                path                    (list)  --  list of database/databases to be restored

                    default: None

                staging                 (str)   --  staging location for mysql logs during restores

                    default: None

                dest_client_name        (str)   --  destination client name where files are to be
                restored

                    default: None

                dest_instance_name      (str)   --  destination mysql instance name of destination
                client

                    default: None

                data_restore            (bool)  --  for data only/data+log restore

                    default: True

                log_restore             (bool)  --  for log only/data+log restore

                    default: False

                overwrite               (bool)  --  unconditional overwrite files during restore

                    default: True

                copy_precedence         (int)   --  copy precedence value of storage policy copy

                    default: None

                from_time               (str)   --  time to retore the contents after
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time                 (str)   --  time to retore the contents before
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                media_agent             (str)   --  media agent associated

                    default: None

                table_level_restore     (bool)  --  Table level restore flag

                    default: False

                clone_env               (bool)  --  boolean to specify whether the database
                should be cloned or not

                    default: False

                clone_options           (dict)  --  clone restore options passed in a dict

                    default: None

                    Accepted format: {
                                        &#34;stagingLocaion&#34;: &#34;/gk_snap&#34;,
                                        &#34;forceCleanup&#34;: True,
                                        &#34;port&#34;: &#34;5595&#34;,
                                        &#34;libDirectory&#34;: &#34;&#34;,
                                        &#34;isInstanceSelected&#34;: True,
                                        &#34;reservationPeriodS&#34;: 3600,
                                        &#34;user&#34;: &#34;&#34;,
                                        &#34;binaryDirectory&#34;: &#34;/usr/bin&#34;

                                     }

                redirect_enabled         (bool)  --  boolean to specify if redirect restore is
                enabled

                    default: False

                redirect_path           (str)   --  Path specified in advanced restore options
                in order to perform redirect restore

                    default: None

                browse_jobid           (int)   --  Browse jobid to browse and restore from

                    default: None

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        if not (isinstance(path, list) and
                isinstance(overwrite, bool)):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if path == []:
            raise SDKException(&#39;Instance&#39;, &#39;104&#39;)

        if dest_client_name is None:
            dest_client_name = self._agent_object._client_object.client_name

        if dest_instance_name is None:
            dest_instance_name = self.instance_name

        request_json = self._restore_json(
            paths=path,
            staging=staging,
            dest_client_name=dest_client_name,
            dest_instance_name=dest_instance_name,
            data_restore=data_restore,
            log_restore=log_restore,
            overwrite=overwrite,
            copy_precedence=copy_precedence,
            from_time=from_time,
            to_time=to_time,
            media_agent=media_agent,
            table_level_restore=table_level_restore,
            clone_env=clone_env,
            clone_options=clone_options,
            redirect_enabled=redirect_enabled,
            redirect_path=redirect_path,
            browse_jobid=browse_jobid)

        return self._process_restore_response(request_json)

    def restore_out_of_place(
            self,
            path=None,
            staging=None,
            dest_client_name=None,
            dest_instance_name=None,
            data_restore=True,
            log_restore=False,
            overwrite=True,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            media_agent=None,
            table_level_restore=False,
            clone_env=False,
            clone_options=None,
            redirect_enabled=False,
            redirect_path=None,
            browse_jobid=None,
            recurringRestore=False):
        &#34;&#34;&#34;
        Method to perform out of place restore of MySQL data/log/recurring files to the destination client location.

            Args:
                path                    (list)  --  list of database/databases to be restored
                staging                 (str)   --  staging location for mysql logs during restores
                dest_client_name        (str)   --  destination client name where files are to be restored
                dest_instance_name      (str)   --  destination mysql instance name of destination client
                data_restore            (bool)  --  for data only/data+log restore
                log_restore             (bool)  --  for log only/data+log restore
                overwrite               (bool)  --  unconditional overwrite files during restore
                copy_precedence         (int)   --  copy precedence value of storage policy copy
                from_time               (str)   --  time to restore the contents after
                        format: YYYY-MM-DD HH:MM:SS
                to_time                 (str)   --  time to restore the contents before
                        format: YYYY-MM-DD HH:MM:SS
                media_agent             (str)   --  media agent associated
                table_level_restore     (bool)  --  Table level restore flag
                clone_env               (bool)  --  boolean to specify whether the database should be cloned or not
                clone_options           (dict)  --  clone restore options passed in a dict
                redirect_enabled         (bool)  --  boolean to specify if redirect restore is enabled
                redirect_path           (str)   --  Path specified in advanced restore options
                in order to perform redirect restore
                browse_jobid           (int)   --  Browse jobid to browse and restore from
                recurringRestore       (bool)  --  for Recurring restore

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if paths is not a list

                    if failed to initialize job

                    if destination client name is empty

                    if destination Instance name empty

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if not (isinstance(path, list) and
                isinstance(overwrite, bool)):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if path == []:
            raise SDKException(&#39;Instance&#39;, &#39;104&#39;)

        if dest_client_name is None:
            raise SDKException(&#39;Client&#39;, &#39;102&#39;,
                               &#34;The destination client name is missing. &#34;
                               &#34;Please provide a valid destination client name to proceed&#34;)

        if dest_instance_name is None:
            raise SDKException(&#39;Instance&#39;, &#39;102&#39;,
                               &#34;The destination Instance name is missing. &#34;
                               &#34;Please provide a valid destination Instance name to proceed&#34;)

        request_json = self._restore_json(
            paths=path,
            staging=staging,
            dest_client_name=dest_client_name,
            dest_instance_name=dest_instance_name,
            data_restore=data_restore,
            log_restore=log_restore,
            overwrite=overwrite,
            copy_precedence=copy_precedence,
            from_time=from_time,
            to_time=to_time,
            media_agent=media_agent,
            table_level_restore=table_level_restore,
            clone_env=clone_env,
            clone_options=clone_options,
            redirect_enabled=redirect_enabled,
            redirect_path=redirect_path,
            browse_jobid=browse_jobid,
            recurringRestore=recurringRestore)

        return self._process_restore_response(request_json)

    def _restore_browse_option_json(self, value):
        &#34;&#34;&#34;setter for the Browse options for restore in Json&#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        super(MYSQLInstance, self)._restore_browse_option_json(value)
        self._browse_restore_json[&#39;backupset&#39;] = {
            &#34;clientName&#34;: self._agent_object._client_object.client_name,
            &#34;backupsetName&#34;: &#34;defaultDummyBackupSet&#34;
        }

        if value.get(&#34;browse_jobid&#34;):
            self._browse_restore_json[&#39;browseJobId&#39;] = value.get(&#34;browse_jobid&#34;)

        if value.get(&#34;from_time&#34;) and value.get(&#34;to_time&#34;):
            self._browse_restore_json[&#34;timeRange&#34;] = {&#34;fromTime&#34; : value.get(&#34;from_time&#34;),
                                                      &#34;toTime&#34; : value.get(&#34;to_time&#34;)}

    def _restore_common_options_json(self, value):
        &#34;&#34;&#34;setter for the Common options in restore JSON&#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        self._commonoption_restore_json = {
            &#34;restoreToDisk&#34;: False,
            &#34;onePassRestore&#34;: False,
            &#34;revert&#34;: False,
            &#34;syncRestore&#34;: False
        }

    def _restore_destination_json(self, value):
        &#34;&#34;&#34;setter for the MySQL Destination options in restore JSON&#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        self._destination_restore_json = {
            &#34;destinationInstance&#34;: {
                &#34;clientName&#34;: value.get(&#34;dest_client_name&#34;, &#34;&#34;),
                &#34;instanceName&#34;: value.get(&#34;dest_instance_name&#34;, &#34;&#34;),
                &#34;appName&#34;: &#34;MySQL&#34;
            },
            &#34;destClient&#34;: {
                &#34;clientName&#34;: value.get(&#34;dest_client_name&#34;, &#34;&#34;)
            }
        }

    def _restore_fileoption_json(self, value):
        &#34;&#34;&#34;setter for the fileoption restore option in restore JSON&#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        self._fileoption_restore_json = {
            &#34;sourceItem&#34;: value.get(&#34;paths&#34;, [])
        }

    def _restore_admin_option_json(self, value):
        &#34;&#34;&#34;setter for the admin restore option in restore JSON&#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        self.admin_option_json = {
            &#34;contentIndexingOption&#34;: {
                &#34;subClientBasedAnalytics&#34;: False
            }
        }

    def _restore_mysql_option_json(self, value):
        &#34;&#34;&#34;setter for the mysql restore option in restore JSON&#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        self.mysql_restore_json = {
            &#34;destinationFolder&#34;: &#34;&#34;,
            &#34;data&#34;: value.get(&#34;data_restore&#34;, True),
            &#34;log&#34;: value.get(&#34;log_restore&#34;, True),
            &#34;recurringRestore&#34;: value.get(&#34;recurringRestore&#34;, False),
            &#34;temporaryStagingLocation&#34;: value.get(&#34;staging&#34;, &#34;&#34;),
            &#34;dataStagingLocation&#34;: &#34;&#34;,
            &#34;logRestoreType&#34;: 0,
            &#34;tableLevelRestore&#34;: value.get(&#34;table_level_restore&#34;, False),
            &#34;pointofTime&#34;: True if value.get(&#34;to_time&#34;) else False,
            &#34;instanceRestore&#34;: True,
            &#34;isCloneRestore&#34;: value.get(&#34;clone_env&#34;, False),
            &#34;fromTime&#34;: value.get(&#34;from_time&#34;, {}),
            &#34;refTime&#34;: value.get(&#34;to_time&#34;, {}),
            &#34;destinationServer&#34;: {
                &#34;name&#34;: &#34;&#34;
            }
        }
        if value.get(&#34;table_level_restore&#34;):
            self.mysql_restore_json[&#39;dropTable&#39;] = True
            self.mysql_restore_json[&#39;instanceRestore&#39;] = False

        if value.get(&#34;clone_env&#34;, False):
            self.mysql_restore_json[&#34;cloneOptions&#34;] = value.get(&#34;clone_options&#34;, &#34;&#34;)

        if value.get(&#34;redirect_path&#34;):
            self.mysql_restore_json[&#34;redirectEnabled&#34;] = True
            self.mysql_restore_json[&#34;redirectItems&#34;] = [value.get(&#34;redirect_path&#34;)]

        if value.get(&#34;from_time&#34;):
            self.mysql_restore_json[&#34;fromTime&#34;] = {&#34;time&#34; : value.get(&#34;to_time&#34;)}

        if value.get(&#34;to_time&#34;):
            self.mysql_restore_json[&#34;refTime&#34;] = {&#34;time&#34; : value.get(&#34;to_time&#34;)}

        if value.get(&#34;to_time&#34;):
            self.mysql_restore_json[&#34;pointInTime&#34;] = {&#34;time&#34; : value.get(&#34;to_time&#34;)}

        if value.get(&#34;dest_instance_name&#34;):
            self.mysql_restore_json[&#34;destinationServer&#34;] = {&#34;name&#34;: value.get(
                &#34;dest_instance_name&#34;)}</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.instances.mysqlinstance.MYSQLInstance"><code class="flex name class">
<span>class <span class="ident">MYSQLInstance</span></span>
<span>(</span><span>agent_object, instance_name, instance_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class to represent a standalone MYSQL Instance</p>
<p>Initialise the Subclient object.</p>
<h2 id="args">Args</h2>
<p>agent_object
(object)
&ndash;
instance of the Agent class</p>
<p>instance_name
(str)
&ndash;
name of the instance</p>
<p>instance_id
(str)
&ndash;
id of the instance</p>
<pre><code>default: None
</code></pre>
<h2 id="returns">Returns</h2>
<p>object - instance of the MYSQLInstance class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/mysqlinstance.py#L98-L796" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class MYSQLInstance(Instance):
    &#34;&#34;&#34;
    Class to represent a standalone MYSQL Instance
    &#34;&#34;&#34;

    def __init__(self, agent_object, instance_name, instance_id=None):
        &#34;&#34;&#34;Initialise the Subclient object.

            Args:
                agent_object    (object)  --  instance of the Agent class

                instance_name   (str)     --  name of the instance

                instance_id     (str)     --  id of the instance

                    default: None

            Returns:
                object - instance of the MYSQLInstance class

        &#34;&#34;&#34;
        self._browse_restore_json = None
        self._commonoption_restore_json = None
        self._destination_restore_json = None
        self._fileoption_restore_json = None
        self._instance = None
        self.admin_option_json = None
        self.mysql_restore_json = None
        super(MYSQLInstance, self).__init__(agent_object, instance_name, instance_id)

    @property
    def port(self):
        &#34;&#34;&#34;Returns the MySQL Server Port number.

        Returns:
            (str)   --  MySql server port number

        &#34;&#34;&#34;
        return self._properties.get(&#39;mySqlInstance&#39;, {}).get(&#39;port&#39;, None)

    @property
    def mysql_username(self):
        &#34;&#34;&#34;Returns the MySQL Server username.

        Returns:
            (str)   --  MySql server SA username

        &#34;&#34;&#34;
        credential_name = self._properties.get(&#39;credentialEntity&#39;, {}).get(&#39;credentialName&#39;, None)
        return Credential(self._commcell_object, credential_name).credential_user_name

    @property
    def nt_username(self):
        &#34;&#34;&#34;Returns the MySQL Server nt username.

        Returns:
            (str)   --  MySql server NT username

        &#34;&#34;&#34;
        return self._properties.get(&#39;mySqlInstance&#39;, {}).get(&#39;NTUser&#39;, {}).get(&#39;userName&#39;, None)

    @property
    def config_file(self):
        &#34;&#34;&#34;Returns the MySQL Server Config File location.

        Returns:
            (str)   --  MySql server config file location

        &#34;&#34;&#34;
        return self._properties.get(&#39;mySqlInstance&#39;, {}).get(&#39;ConfigFile&#39;, None)

    @property
    def binary_directory(self):
        &#34;&#34;&#34;Returns the MySQL Server Binary File location.

        Returns:
            (str)   --  MySql server binary directory

        &#34;&#34;&#34;
        return self._properties.get(&#39;mySqlInstance&#39;, {}).get(&#39;BinaryDirectory&#39;, None)

    @property
    def version(self):
        &#34;&#34;&#34;Returns the MySQL Server version number.

        Returns:
            (str)   --  MySql server version

        &#34;&#34;&#34;
        return self._properties.get(&#39;mySqlInstance&#39;, {}).get(&#39;version&#39;, None)

    @property
    def log_data_directory(self):
        &#34;&#34;&#34;Returns the MySQL Server log data directory.

        Returns:
            (str)   --  MySql server log directory path

        &#34;&#34;&#34;
        return self._properties.get(&#39;mySqlInstance&#39;, {}).get(&#39;LogDataDirectory&#39;, None)

    @property
    def log_backup_sp_details(self):
        &#34;&#34;&#34;Returns the MySQL Server Log backup SP details

        Returns:
            (dict)  --  MySql server log backup storage policy information

        &#34;&#34;&#34;
        log_storage_policy_name = self._properties.get(&#39;mySqlInstance&#39;, {}).get(
            &#39;logStoragePolicy&#39;, {}).get(&#39;storagePolicyName&#39;, None)
        log_storage_policy_id = self._properties.get(&#39;mySqlInstance&#39;, {}).get(
            &#39;logStoragePolicy&#39;, {}).get(&#39;storagePolicyId&#39;, None)

        log_sp = {&#34;storagePolicyName&#34;: log_storage_policy_name,
                  &#34;storagePolicyId&#34;: log_storage_policy_id}
        return log_sp

    @property
    def command_line_sp_details(self):
        &#34;&#34;&#34;Returns the MySQL Server commandline SP details.

        Returns:
            (dict)  --  MySql server commnadline storage policy information

        &#34;&#34;&#34;
        cmd_storage_policy_name = self._properties.get(&#39;mySqlInstance&#39;, {}).get(
            &#39;mysqlStorageDevice&#39;, {}).get(&#39;commandLineStoragePolicy&#39;, {}).get(
                &#39;storagePolicyName&#39;, None)
        cmd_storage_policy_id = self._properties.get(&#39;mySqlInstance&#39;, {}).get(
            &#39;mysqlStorageDevice&#39;, {}).get(&#39;commandLineStoragePolicy&#39;, {}).get(
                &#39;storagePolicyId&#39;, None)

        command_sp = {&#34;storagePolicyName&#34;: cmd_storage_policy_name,
                      &#34;storagePolicyId&#34;: cmd_storage_policy_id}
        return command_sp

    @property
    def autodiscovery_enabled(self):
        &#34;&#34;&#34;Returns the MySQL Server auto discovery enabled flag

        Returns:
            (bool)  --  True if auto discovery enabled
                        False if auto discovery not enabled

        &#34;&#34;&#34;
        return self._properties.get(&#39;mySqlInstance&#39;, {}).get(&#39;EnableAutoDiscovery&#39;, False)

    @autodiscovery_enabled.setter
    def autodiscovery_enabled(self, value):
        &#34;&#34;&#34;
        Sets the auto discovery attribute to True or False
        value (bool)   --  True or False

        &#34;&#34;&#34;
        properties = self._properties
        update = {
            &#34;EnableAutoDiscovery&#34;: value
        }
        properties[&#39;mySqlInstance&#39;] = update
        self.update_properties(properties)

    @property
    def xtrabackup_bin_path(self):
        &#34;&#34;&#34;Returns the MySQL Server xtrabackup bin path

        Returns:
            (str)   --  MySql server xtrabackup binary path

        &#34;&#34;&#34;
        return self._properties.get(
            &#39;mySqlInstance&#39;, {}).get(
                &#39;xtraBackupSettings&#39;, {}).get(&#39;xtraBackupBinPath&#39;, &#34;&#34;)

    @property
    def is_xtrabackup_enabled(self):
        &#34;&#34;&#34;Returns the MySQL Server xtrabackup enabled flag

        Returns:
            (bool)  --  True if xtrabackup is enabled
                        False if xtrabackup is not enabled

        &#34;&#34;&#34;
        return self._properties.get(
            &#39;mySqlInstance&#39;, {}).get(
                &#39;xtraBackupSettings&#39;, {}).get(&#39;enableXtraBackup&#39;, False)

    @property
    def proxy_options(self):
        &#34;&#34;&#34;Returns the MySQL Server proxy options

        Returns:
            (dict)  --  MySql server proxy information

        &#34;&#34;&#34;
        proxy_settings = self._properties.get(&#39;mySqlInstance&#39;, {}).get(&#39;proxySettings&#39;, {})
        proxy_opt = {
            &#34;isUseSSL&#34;: proxy_settings.get(&#39;isUseSSL&#39;, False),
            &#34;isProxyEnabled&#34;: proxy_settings.get(&#39;isProxyEnabled&#39;, False),
            &#34;runBackupOnProxy&#34;: proxy_settings.get(&#39;runBackupOnProxy&#39;, False),
            &#34;instanceId&#34;: proxy_settings.get(&#39;proxyInstance&#39;, {}).get(&#39;instanceId&#39;, None),
            &#34;instanceName&#34;: proxy_settings.get(&#39;proxyInstance&#39;, {}).get(&#39;instanceName&#39;, None),
            &#34;clientId&#34;: proxy_settings.get(&#39;proxyInstance&#39;, {}).get(&#39;clientId&#39;, None),
            &#34;clientName&#34;: proxy_settings.get(&#39;proxyInstance&#39;, {}).get(&#39;clientName&#39;, None)}
        return proxy_opt

    @property
    def mysql_enterprise_backup_binary_path(self):
        &#34;&#34;&#34; Returns the MySQL Enterprise backup binary path detail

            Return Type: dict

        &#34;&#34;&#34;
        meb_settings = self._properties.get(&#39;mySqlInstance&#39;, {}).get(&#39;mebSettings&#39;, {})
        return meb_settings

    @mysql_enterprise_backup_binary_path.setter
    def mysql_enterprise_backup_binary_path(self, value):
        &#34;&#34;&#34; Setter for MySQL Enterprise backup binary path

            Args:

                value (str)  -- Contains the MySQL Enterprise backup binary path to be updated
                in MySQL Instance property

        &#34;&#34;&#34;
        if not isinstance(value, str):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        properties = self._properties
        meb_bin_path_update = {
            &#34;enableMEB&#34;: False if value == &#39;&#39; else True,
            &#34;mebBinPath&#34;: value
        }
        properties[&#39;mySqlInstance&#39;][&#39;mebSettings&#39;] = meb_bin_path_update
        self.update_properties(properties)

    @property
    def no_lock_status(self):
        &#34;&#34;&#34; Returns the status of No Lock Checkbox in MySQL Instance

            Returns:
            (bool)  --  True if No Lock checkbox is enabled
                        False if No Lock checkbox is disabled

        &#34;&#34;&#34;
        return self._properties.get(&#39;mySqlInstance&#39;, {}).get(&#39;EnableNoLocking&#39;, False)

    @no_lock_status.setter
    def no_lock_status(self, value):
        &#34;&#34;&#34; Setter for No Lock property in MySQL Instance

            Args:

                value (bool)  -- True or False to enable or disable the No Lock
                property in MySQL Instance

        &#34;&#34;&#34;
        if not isinstance(value, bool):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        properties = self._properties
        properties[&#39;mySqlInstance&#39;][&#39;EnableNoLocking&#39;] = value
        self.update_properties(properties)

    @property
    def ssl_enabled(self):
        &#34;&#34;&#34; Returns(boolean) True/False based on SSL status &#34;&#34;&#34;
        return self._properties.get(&#39;mySqlInstance&#39;, {}).get(&#39;sslEnabled&#39;, False)

    def _get_instance_properties(self):
        &#34;&#34;&#34;Gets the properties of this instance.

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        super(MYSQLInstance, self)._get_instance_properties()
        self._instance = {
            &#34;type&#34;: 0,
            &#34;clientName&#34;: self._agent_object._client_object.client_name,
            &#34;clientSidePackage&#34;: True,
            &#34;subclientName&#34;: &#34;&#34;,
            &#34;backupsetName&#34;: &#34;defaultDummyBackupSet&#34;,
            &#34;instanceName&#34;: self.instance_name,
            &#34;appName&#34;: self._agent_object.agent_name,
            &#34;consumeLicense&#34;: True
        }

    def _restore_json(self, **kwargs):
        &#34;&#34;&#34;Returns the JSON request to pass to the API as per the options selected by the user.

            Args:
                kwargs   (list)  --  list of options need to be set for restore

            Returns:
                dict - JSON request to pass to the API

        &#34;&#34;&#34;
        rest_json = super(MYSQLInstance, self)._restore_json(**kwargs)
        restore_option = {}
        if kwargs.get(&#34;restore_option&#34;):
            restore_option = kwargs[&#34;restore_option&#34;]
            for key in kwargs:
                if not key == &#34;restore_option&#34;:
                    restore_option[key] = kwargs[key]
        else:
            restore_option.update(kwargs)

        if restore_option[&#34;from_time&#34;] is None:
            restore_option[&#34;from_time&#34;] = {}

        if restore_option[&#34;to_time&#34;] is None:
            restore_option[&#34;to_time&#34;] = {}

        self._restore_admin_option_json(restore_option)
        self._restore_mysql_option_json(restore_option)
        rest_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;mySqlRstOption&#34;] = self.mysql_restore_json
        rest_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;adminOpts&#34;] = self.admin_option_json
        return rest_json

    def restore_in_place(
            self,
            path=None,
            staging=None,
            dest_client_name=None,
            dest_instance_name=None,
            data_restore=True,
            log_restore=False,
            overwrite=True,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            media_agent=None,
            table_level_restore=False,
            clone_env=False,
            clone_options=None,
            redirect_enabled=False,
            redirect_path=None,
            browse_jobid=None):
        &#34;&#34;&#34;Restores the mysql data/log files specified in the input paths list to the same location.

            Args:
                path                    (list)  --  list of database/databases to be restored

                    default: None

                staging                 (str)   --  staging location for mysql logs during restores

                    default: None

                dest_client_name        (str)   --  destination client name where files are to be
                restored

                    default: None

                dest_instance_name      (str)   --  destination mysql instance name of destination
                client

                    default: None

                data_restore            (bool)  --  for data only/data+log restore

                    default: True

                log_restore             (bool)  --  for log only/data+log restore

                    default: False

                overwrite               (bool)  --  unconditional overwrite files during restore

                    default: True

                copy_precedence         (int)   --  copy precedence value of storage policy copy

                    default: None

                from_time               (str)   --  time to retore the contents after
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                to_time                 (str)   --  time to retore the contents before
                        format: YYYY-MM-DD HH:MM:SS

                    default: None

                media_agent             (str)   --  media agent associated

                    default: None

                table_level_restore     (bool)  --  Table level restore flag

                    default: False

                clone_env               (bool)  --  boolean to specify whether the database
                should be cloned or not

                    default: False

                clone_options           (dict)  --  clone restore options passed in a dict

                    default: None

                    Accepted format: {
                                        &#34;stagingLocaion&#34;: &#34;/gk_snap&#34;,
                                        &#34;forceCleanup&#34;: True,
                                        &#34;port&#34;: &#34;5595&#34;,
                                        &#34;libDirectory&#34;: &#34;&#34;,
                                        &#34;isInstanceSelected&#34;: True,
                                        &#34;reservationPeriodS&#34;: 3600,
                                        &#34;user&#34;: &#34;&#34;,
                                        &#34;binaryDirectory&#34;: &#34;/usr/bin&#34;

                                     }

                redirect_enabled         (bool)  --  boolean to specify if redirect restore is
                enabled

                    default: False

                redirect_path           (str)   --  Path specified in advanced restore options
                in order to perform redirect restore

                    default: None

                browse_jobid           (int)   --  Browse jobid to browse and restore from

                    default: None

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        if not (isinstance(path, list) and
                isinstance(overwrite, bool)):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if path == []:
            raise SDKException(&#39;Instance&#39;, &#39;104&#39;)

        if dest_client_name is None:
            dest_client_name = self._agent_object._client_object.client_name

        if dest_instance_name is None:
            dest_instance_name = self.instance_name

        request_json = self._restore_json(
            paths=path,
            staging=staging,
            dest_client_name=dest_client_name,
            dest_instance_name=dest_instance_name,
            data_restore=data_restore,
            log_restore=log_restore,
            overwrite=overwrite,
            copy_precedence=copy_precedence,
            from_time=from_time,
            to_time=to_time,
            media_agent=media_agent,
            table_level_restore=table_level_restore,
            clone_env=clone_env,
            clone_options=clone_options,
            redirect_enabled=redirect_enabled,
            redirect_path=redirect_path,
            browse_jobid=browse_jobid)

        return self._process_restore_response(request_json)

    def restore_out_of_place(
            self,
            path=None,
            staging=None,
            dest_client_name=None,
            dest_instance_name=None,
            data_restore=True,
            log_restore=False,
            overwrite=True,
            copy_precedence=None,
            from_time=None,
            to_time=None,
            media_agent=None,
            table_level_restore=False,
            clone_env=False,
            clone_options=None,
            redirect_enabled=False,
            redirect_path=None,
            browse_jobid=None,
            recurringRestore=False):
        &#34;&#34;&#34;
        Method to perform out of place restore of MySQL data/log/recurring files to the destination client location.

            Args:
                path                    (list)  --  list of database/databases to be restored
                staging                 (str)   --  staging location for mysql logs during restores
                dest_client_name        (str)   --  destination client name where files are to be restored
                dest_instance_name      (str)   --  destination mysql instance name of destination client
                data_restore            (bool)  --  for data only/data+log restore
                log_restore             (bool)  --  for log only/data+log restore
                overwrite               (bool)  --  unconditional overwrite files during restore
                copy_precedence         (int)   --  copy precedence value of storage policy copy
                from_time               (str)   --  time to restore the contents after
                        format: YYYY-MM-DD HH:MM:SS
                to_time                 (str)   --  time to restore the contents before
                        format: YYYY-MM-DD HH:MM:SS
                media_agent             (str)   --  media agent associated
                table_level_restore     (bool)  --  Table level restore flag
                clone_env               (bool)  --  boolean to specify whether the database should be cloned or not
                clone_options           (dict)  --  clone restore options passed in a dict
                redirect_enabled         (bool)  --  boolean to specify if redirect restore is enabled
                redirect_path           (str)   --  Path specified in advanced restore options
                in order to perform redirect restore
                browse_jobid           (int)   --  Browse jobid to browse and restore from
                recurringRestore       (bool)  --  for Recurring restore

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if paths is not a list

                    if failed to initialize job

                    if destination client name is empty

                    if destination Instance name empty

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        if not (isinstance(path, list) and
                isinstance(overwrite, bool)):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        if path == []:
            raise SDKException(&#39;Instance&#39;, &#39;104&#39;)

        if dest_client_name is None:
            raise SDKException(&#39;Client&#39;, &#39;102&#39;,
                               &#34;The destination client name is missing. &#34;
                               &#34;Please provide a valid destination client name to proceed&#34;)

        if dest_instance_name is None:
            raise SDKException(&#39;Instance&#39;, &#39;102&#39;,
                               &#34;The destination Instance name is missing. &#34;
                               &#34;Please provide a valid destination Instance name to proceed&#34;)

        request_json = self._restore_json(
            paths=path,
            staging=staging,
            dest_client_name=dest_client_name,
            dest_instance_name=dest_instance_name,
            data_restore=data_restore,
            log_restore=log_restore,
            overwrite=overwrite,
            copy_precedence=copy_precedence,
            from_time=from_time,
            to_time=to_time,
            media_agent=media_agent,
            table_level_restore=table_level_restore,
            clone_env=clone_env,
            clone_options=clone_options,
            redirect_enabled=redirect_enabled,
            redirect_path=redirect_path,
            browse_jobid=browse_jobid,
            recurringRestore=recurringRestore)

        return self._process_restore_response(request_json)

    def _restore_browse_option_json(self, value):
        &#34;&#34;&#34;setter for the Browse options for restore in Json&#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        super(MYSQLInstance, self)._restore_browse_option_json(value)
        self._browse_restore_json[&#39;backupset&#39;] = {
            &#34;clientName&#34;: self._agent_object._client_object.client_name,
            &#34;backupsetName&#34;: &#34;defaultDummyBackupSet&#34;
        }

        if value.get(&#34;browse_jobid&#34;):
            self._browse_restore_json[&#39;browseJobId&#39;] = value.get(&#34;browse_jobid&#34;)

        if value.get(&#34;from_time&#34;) and value.get(&#34;to_time&#34;):
            self._browse_restore_json[&#34;timeRange&#34;] = {&#34;fromTime&#34; : value.get(&#34;from_time&#34;),
                                                      &#34;toTime&#34; : value.get(&#34;to_time&#34;)}

    def _restore_common_options_json(self, value):
        &#34;&#34;&#34;setter for the Common options in restore JSON&#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        self._commonoption_restore_json = {
            &#34;restoreToDisk&#34;: False,
            &#34;onePassRestore&#34;: False,
            &#34;revert&#34;: False,
            &#34;syncRestore&#34;: False
        }

    def _restore_destination_json(self, value):
        &#34;&#34;&#34;setter for the MySQL Destination options in restore JSON&#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        self._destination_restore_json = {
            &#34;destinationInstance&#34;: {
                &#34;clientName&#34;: value.get(&#34;dest_client_name&#34;, &#34;&#34;),
                &#34;instanceName&#34;: value.get(&#34;dest_instance_name&#34;, &#34;&#34;),
                &#34;appName&#34;: &#34;MySQL&#34;
            },
            &#34;destClient&#34;: {
                &#34;clientName&#34;: value.get(&#34;dest_client_name&#34;, &#34;&#34;)
            }
        }

    def _restore_fileoption_json(self, value):
        &#34;&#34;&#34;setter for the fileoption restore option in restore JSON&#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        self._fileoption_restore_json = {
            &#34;sourceItem&#34;: value.get(&#34;paths&#34;, [])
        }

    def _restore_admin_option_json(self, value):
        &#34;&#34;&#34;setter for the admin restore option in restore JSON&#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        self.admin_option_json = {
            &#34;contentIndexingOption&#34;: {
                &#34;subClientBasedAnalytics&#34;: False
            }
        }

    def _restore_mysql_option_json(self, value):
        &#34;&#34;&#34;setter for the mysql restore option in restore JSON&#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        self.mysql_restore_json = {
            &#34;destinationFolder&#34;: &#34;&#34;,
            &#34;data&#34;: value.get(&#34;data_restore&#34;, True),
            &#34;log&#34;: value.get(&#34;log_restore&#34;, True),
            &#34;recurringRestore&#34;: value.get(&#34;recurringRestore&#34;, False),
            &#34;temporaryStagingLocation&#34;: value.get(&#34;staging&#34;, &#34;&#34;),
            &#34;dataStagingLocation&#34;: &#34;&#34;,
            &#34;logRestoreType&#34;: 0,
            &#34;tableLevelRestore&#34;: value.get(&#34;table_level_restore&#34;, False),
            &#34;pointofTime&#34;: True if value.get(&#34;to_time&#34;) else False,
            &#34;instanceRestore&#34;: True,
            &#34;isCloneRestore&#34;: value.get(&#34;clone_env&#34;, False),
            &#34;fromTime&#34;: value.get(&#34;from_time&#34;, {}),
            &#34;refTime&#34;: value.get(&#34;to_time&#34;, {}),
            &#34;destinationServer&#34;: {
                &#34;name&#34;: &#34;&#34;
            }
        }
        if value.get(&#34;table_level_restore&#34;):
            self.mysql_restore_json[&#39;dropTable&#39;] = True
            self.mysql_restore_json[&#39;instanceRestore&#39;] = False

        if value.get(&#34;clone_env&#34;, False):
            self.mysql_restore_json[&#34;cloneOptions&#34;] = value.get(&#34;clone_options&#34;, &#34;&#34;)

        if value.get(&#34;redirect_path&#34;):
            self.mysql_restore_json[&#34;redirectEnabled&#34;] = True
            self.mysql_restore_json[&#34;redirectItems&#34;] = [value.get(&#34;redirect_path&#34;)]

        if value.get(&#34;from_time&#34;):
            self.mysql_restore_json[&#34;fromTime&#34;] = {&#34;time&#34; : value.get(&#34;to_time&#34;)}

        if value.get(&#34;to_time&#34;):
            self.mysql_restore_json[&#34;refTime&#34;] = {&#34;time&#34; : value.get(&#34;to_time&#34;)}

        if value.get(&#34;to_time&#34;):
            self.mysql_restore_json[&#34;pointInTime&#34;] = {&#34;time&#34; : value.get(&#34;to_time&#34;)}

        if value.get(&#34;dest_instance_name&#34;):
            self.mysql_restore_json[&#34;destinationServer&#34;] = {&#34;name&#34;: value.get(
                &#34;dest_instance_name&#34;)}</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.instance.Instance" href="../instance.html#cvpysdk.instance.Instance">Instance</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.instances.mysqlinstance.MYSQLInstance.autodiscovery_enabled"><code class="name">var <span class="ident">autodiscovery_enabled</span></code></dt>
<dd>
<div class="desc"><p>Returns the MySQL Server auto discovery enabled flag</p>
<h2 id="returns">Returns</h2>
<p>(bool)
&ndash;
True if auto discovery enabled
False if auto discovery not enabled</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/mysqlinstance.py#L235-L244" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def autodiscovery_enabled(self):
    &#34;&#34;&#34;Returns the MySQL Server auto discovery enabled flag

    Returns:
        (bool)  --  True if auto discovery enabled
                    False if auto discovery not enabled

    &#34;&#34;&#34;
    return self._properties.get(&#39;mySqlInstance&#39;, {}).get(&#39;EnableAutoDiscovery&#39;, False)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.mysqlinstance.MYSQLInstance.binary_directory"><code class="name">var <span class="ident">binary_directory</span></code></dt>
<dd>
<div class="desc"><p>Returns the MySQL Server Binary File location.</p>
<h2 id="returns">Returns</h2>
<p>(str)
&ndash;
MySql server binary directory</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/mysqlinstance.py#L169-L177" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def binary_directory(self):
    &#34;&#34;&#34;Returns the MySQL Server Binary File location.

    Returns:
        (str)   --  MySql server binary directory

    &#34;&#34;&#34;
    return self._properties.get(&#39;mySqlInstance&#39;, {}).get(&#39;BinaryDirectory&#39;, None)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.mysqlinstance.MYSQLInstance.command_line_sp_details"><code class="name">var <span class="ident">command_line_sp_details</span></code></dt>
<dd>
<div class="desc"><p>Returns the MySQL Server commandline SP details.</p>
<h2 id="returns">Returns</h2>
<p>(dict)
&ndash;
MySql server commnadline storage policy information</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/mysqlinstance.py#L216-L233" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def command_line_sp_details(self):
    &#34;&#34;&#34;Returns the MySQL Server commandline SP details.

    Returns:
        (dict)  --  MySql server commnadline storage policy information

    &#34;&#34;&#34;
    cmd_storage_policy_name = self._properties.get(&#39;mySqlInstance&#39;, {}).get(
        &#39;mysqlStorageDevice&#39;, {}).get(&#39;commandLineStoragePolicy&#39;, {}).get(
            &#39;storagePolicyName&#39;, None)
    cmd_storage_policy_id = self._properties.get(&#39;mySqlInstance&#39;, {}).get(
        &#39;mysqlStorageDevice&#39;, {}).get(&#39;commandLineStoragePolicy&#39;, {}).get(
            &#39;storagePolicyId&#39;, None)

    command_sp = {&#34;storagePolicyName&#34;: cmd_storage_policy_name,
                  &#34;storagePolicyId&#34;: cmd_storage_policy_id}
    return command_sp</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.mysqlinstance.MYSQLInstance.config_file"><code class="name">var <span class="ident">config_file</span></code></dt>
<dd>
<div class="desc"><p>Returns the MySQL Server Config File location.</p>
<h2 id="returns">Returns</h2>
<p>(str)
&ndash;
MySql server config file location</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/mysqlinstance.py#L159-L167" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def config_file(self):
    &#34;&#34;&#34;Returns the MySQL Server Config File location.

    Returns:
        (str)   --  MySql server config file location

    &#34;&#34;&#34;
    return self._properties.get(&#39;mySqlInstance&#39;, {}).get(&#39;ConfigFile&#39;, None)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.mysqlinstance.MYSQLInstance.is_xtrabackup_enabled"><code class="name">var <span class="ident">is_xtrabackup_enabled</span></code></dt>
<dd>
<div class="desc"><p>Returns the MySQL Server xtrabackup enabled flag</p>
<h2 id="returns">Returns</h2>
<p>(bool)
&ndash;
True if xtrabackup is enabled
False if xtrabackup is not enabled</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/mysqlinstance.py#L272-L283" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_xtrabackup_enabled(self):
    &#34;&#34;&#34;Returns the MySQL Server xtrabackup enabled flag

    Returns:
        (bool)  --  True if xtrabackup is enabled
                    False if xtrabackup is not enabled

    &#34;&#34;&#34;
    return self._properties.get(
        &#39;mySqlInstance&#39;, {}).get(
            &#39;xtraBackupSettings&#39;, {}).get(&#39;enableXtraBackup&#39;, False)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.mysqlinstance.MYSQLInstance.log_backup_sp_details"><code class="name">var <span class="ident">log_backup_sp_details</span></code></dt>
<dd>
<div class="desc"><p>Returns the MySQL Server Log backup SP details</p>
<h2 id="returns">Returns</h2>
<p>(dict)
&ndash;
MySql server log backup storage policy information</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/mysqlinstance.py#L199-L214" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def log_backup_sp_details(self):
    &#34;&#34;&#34;Returns the MySQL Server Log backup SP details

    Returns:
        (dict)  --  MySql server log backup storage policy information

    &#34;&#34;&#34;
    log_storage_policy_name = self._properties.get(&#39;mySqlInstance&#39;, {}).get(
        &#39;logStoragePolicy&#39;, {}).get(&#39;storagePolicyName&#39;, None)
    log_storage_policy_id = self._properties.get(&#39;mySqlInstance&#39;, {}).get(
        &#39;logStoragePolicy&#39;, {}).get(&#39;storagePolicyId&#39;, None)

    log_sp = {&#34;storagePolicyName&#34;: log_storage_policy_name,
              &#34;storagePolicyId&#34;: log_storage_policy_id}
    return log_sp</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.mysqlinstance.MYSQLInstance.log_data_directory"><code class="name">var <span class="ident">log_data_directory</span></code></dt>
<dd>
<div class="desc"><p>Returns the MySQL Server log data directory.</p>
<h2 id="returns">Returns</h2>
<p>(str)
&ndash;
MySql server log directory path</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/mysqlinstance.py#L189-L197" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def log_data_directory(self):
    &#34;&#34;&#34;Returns the MySQL Server log data directory.

    Returns:
        (str)   --  MySql server log directory path

    &#34;&#34;&#34;
    return self._properties.get(&#39;mySqlInstance&#39;, {}).get(&#39;LogDataDirectory&#39;, None)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.mysqlinstance.MYSQLInstance.mysql_enterprise_backup_binary_path"><code class="name">var <span class="ident">mysql_enterprise_backup_binary_path</span></code></dt>
<dd>
<div class="desc"><p>Returns the MySQL Enterprise backup binary path detail</p>
<p>Return Type: dict</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/mysqlinstance.py#L304-L312" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def mysql_enterprise_backup_binary_path(self):
    &#34;&#34;&#34; Returns the MySQL Enterprise backup binary path detail

        Return Type: dict

    &#34;&#34;&#34;
    meb_settings = self._properties.get(&#39;mySqlInstance&#39;, {}).get(&#39;mebSettings&#39;, {})
    return meb_settings</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.mysqlinstance.MYSQLInstance.mysql_username"><code class="name">var <span class="ident">mysql_username</span></code></dt>
<dd>
<div class="desc"><p>Returns the MySQL Server username.</p>
<h2 id="returns">Returns</h2>
<p>(str)
&ndash;
MySql server SA username</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/mysqlinstance.py#L138-L147" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def mysql_username(self):
    &#34;&#34;&#34;Returns the MySQL Server username.

    Returns:
        (str)   --  MySql server SA username

    &#34;&#34;&#34;
    credential_name = self._properties.get(&#39;credentialEntity&#39;, {}).get(&#39;credentialName&#39;, None)
    return Credential(self._commcell_object, credential_name).credential_user_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.mysqlinstance.MYSQLInstance.no_lock_status"><code class="name">var <span class="ident">no_lock_status</span></code></dt>
<dd>
<div class="desc"><p>Returns the status of No Lock Checkbox in MySQL Instance</p>
<p>Returns:
(bool)
&ndash;
True if No Lock checkbox is enabled
False if No Lock checkbox is disabled</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/mysqlinstance.py#L334-L343" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def no_lock_status(self):
    &#34;&#34;&#34; Returns the status of No Lock Checkbox in MySQL Instance

        Returns:
        (bool)  --  True if No Lock checkbox is enabled
                    False if No Lock checkbox is disabled

    &#34;&#34;&#34;
    return self._properties.get(&#39;mySqlInstance&#39;, {}).get(&#39;EnableNoLocking&#39;, False)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.mysqlinstance.MYSQLInstance.nt_username"><code class="name">var <span class="ident">nt_username</span></code></dt>
<dd>
<div class="desc"><p>Returns the MySQL Server nt username.</p>
<h2 id="returns">Returns</h2>
<p>(str)
&ndash;
MySql server NT username</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/mysqlinstance.py#L149-L157" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def nt_username(self):
    &#34;&#34;&#34;Returns the MySQL Server nt username.

    Returns:
        (str)   --  MySql server NT username

    &#34;&#34;&#34;
    return self._properties.get(&#39;mySqlInstance&#39;, {}).get(&#39;NTUser&#39;, {}).get(&#39;userName&#39;, None)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.mysqlinstance.MYSQLInstance.port"><code class="name">var <span class="ident">port</span></code></dt>
<dd>
<div class="desc"><p>Returns the MySQL Server Port number.</p>
<h2 id="returns">Returns</h2>
<p>(str)
&ndash;
MySql server port number</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/mysqlinstance.py#L128-L136" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def port(self):
    &#34;&#34;&#34;Returns the MySQL Server Port number.

    Returns:
        (str)   --  MySql server port number

    &#34;&#34;&#34;
    return self._properties.get(&#39;mySqlInstance&#39;, {}).get(&#39;port&#39;, None)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.mysqlinstance.MYSQLInstance.proxy_options"><code class="name">var <span class="ident">proxy_options</span></code></dt>
<dd>
<div class="desc"><p>Returns the MySQL Server proxy options</p>
<h2 id="returns">Returns</h2>
<p>(dict)
&ndash;
MySql server proxy information</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/mysqlinstance.py#L285-L302" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def proxy_options(self):
    &#34;&#34;&#34;Returns the MySQL Server proxy options

    Returns:
        (dict)  --  MySql server proxy information

    &#34;&#34;&#34;
    proxy_settings = self._properties.get(&#39;mySqlInstance&#39;, {}).get(&#39;proxySettings&#39;, {})
    proxy_opt = {
        &#34;isUseSSL&#34;: proxy_settings.get(&#39;isUseSSL&#39;, False),
        &#34;isProxyEnabled&#34;: proxy_settings.get(&#39;isProxyEnabled&#39;, False),
        &#34;runBackupOnProxy&#34;: proxy_settings.get(&#39;runBackupOnProxy&#39;, False),
        &#34;instanceId&#34;: proxy_settings.get(&#39;proxyInstance&#39;, {}).get(&#39;instanceId&#39;, None),
        &#34;instanceName&#34;: proxy_settings.get(&#39;proxyInstance&#39;, {}).get(&#39;instanceName&#39;, None),
        &#34;clientId&#34;: proxy_settings.get(&#39;proxyInstance&#39;, {}).get(&#39;clientId&#39;, None),
        &#34;clientName&#34;: proxy_settings.get(&#39;proxyInstance&#39;, {}).get(&#39;clientName&#39;, None)}
    return proxy_opt</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.mysqlinstance.MYSQLInstance.ssl_enabled"><code class="name">var <span class="ident">ssl_enabled</span></code></dt>
<dd>
<div class="desc"><p>Returns(boolean) True/False based on SSL status</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/mysqlinstance.py#L361-L364" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def ssl_enabled(self):
    &#34;&#34;&#34; Returns(boolean) True/False based on SSL status &#34;&#34;&#34;
    return self._properties.get(&#39;mySqlInstance&#39;, {}).get(&#39;sslEnabled&#39;, False)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.mysqlinstance.MYSQLInstance.version"><code class="name">var <span class="ident">version</span></code></dt>
<dd>
<div class="desc"><p>Returns the MySQL Server version number.</p>
<h2 id="returns">Returns</h2>
<p>(str)
&ndash;
MySql server version</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/mysqlinstance.py#L179-L187" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def version(self):
    &#34;&#34;&#34;Returns the MySQL Server version number.

    Returns:
        (str)   --  MySql server version

    &#34;&#34;&#34;
    return self._properties.get(&#39;mySqlInstance&#39;, {}).get(&#39;version&#39;, None)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.mysqlinstance.MYSQLInstance.xtrabackup_bin_path"><code class="name">var <span class="ident">xtrabackup_bin_path</span></code></dt>
<dd>
<div class="desc"><p>Returns the MySQL Server xtrabackup bin path</p>
<h2 id="returns">Returns</h2>
<p>(str)
&ndash;
MySql server xtrabackup binary path</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/mysqlinstance.py#L260-L270" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def xtrabackup_bin_path(self):
    &#34;&#34;&#34;Returns the MySQL Server xtrabackup bin path

    Returns:
        (str)   --  MySql server xtrabackup binary path

    &#34;&#34;&#34;
    return self._properties.get(
        &#39;mySqlInstance&#39;, {}).get(
            &#39;xtraBackupSettings&#39;, {}).get(&#39;xtraBackupBinPath&#39;, &#34;&#34;)</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.instances.mysqlinstance.MYSQLInstance.restore_in_place"><code class="name flex">
<span>def <span class="ident">restore_in_place</span></span>(<span>self, path=None, staging=None, dest_client_name=None, dest_instance_name=None, data_restore=True, log_restore=False, overwrite=True, copy_precedence=None, from_time=None, to_time=None, media_agent=None, table_level_restore=False, clone_env=False, clone_options=None, redirect_enabled=False, redirect_path=None, browse_jobid=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the mysql data/log files specified in the input paths list to the same location.</p>
<h2 id="args">Args</h2>
<p>path
(list)
&ndash;
list of database/databases to be restored</p>
<pre><code>default: None
</code></pre>
<p>staging
(str)
&ndash;
staging location for mysql logs during restores</p>
<pre><code>default: None
</code></pre>
<p>dest_client_name
(str)
&ndash;
destination client name where files are to be
restored</p>
<pre><code>default: None
</code></pre>
<p>dest_instance_name
(str)
&ndash;
destination mysql instance name of destination
client</p>
<pre><code>default: None
</code></pre>
<p>data_restore
(bool)
&ndash;
for data only/data+log restore</p>
<pre><code>default: True
</code></pre>
<p>log_restore
(bool)
&ndash;
for log only/data+log restore</p>
<pre><code>default: False
</code></pre>
<p>overwrite
(bool)
&ndash;
unconditional overwrite files during restore</p>
<pre><code>default: True
</code></pre>
<p>copy_precedence
(int)
&ndash;
copy precedence value of storage policy copy</p>
<pre><code>default: None
</code></pre>
<p>from_time
(str)
&ndash;
time to retore the contents after
format: YYYY-MM-DD HH:MM:SS</p>
<pre><code>default: None
</code></pre>
<p>to_time
(str)
&ndash;
time to retore the contents before
format: YYYY-MM-DD HH:MM:SS</p>
<pre><code>default: None
</code></pre>
<p>media_agent
(str)
&ndash;
media agent associated</p>
<pre><code>default: None
</code></pre>
<p>table_level_restore
(bool)
&ndash;
Table level restore flag</p>
<pre><code>default: False
</code></pre>
<p>clone_env
(bool)
&ndash;
boolean to specify whether the database
should be cloned or not</p>
<pre><code>default: False
</code></pre>
<p>clone_options
(dict)
&ndash;
clone restore options passed in a dict</p>
<pre><code>default: None

Accepted format: {
                    "stagingLocaion": "/gk_snap",
                    "forceCleanup": True,
                    "port": "5595",
                    "libDirectory": "",
                    "isInstanceSelected": True,
                    "reservationPeriodS": 3600,
                    "user": "",
                    "binaryDirectory": "/usr/bin"

                 }
</code></pre>
<p>redirect_enabled
(bool)
&ndash;
boolean to specify if redirect restore is
enabled</p>
<pre><code>default: False
</code></pre>
<p>redirect_path
(str)
&ndash;
Path specified in advanced restore options
in order to perform redirect restore</p>
<pre><code>default: None
</code></pre>
<p>browse_jobid
(int)
&ndash;
Browse jobid to browse and restore from</p>
<pre><code>default: None
</code></pre>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if paths is not a list</p>
<pre><code>if failed to initialize job

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/mysqlinstance.py#L421-L576" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_in_place(
        self,
        path=None,
        staging=None,
        dest_client_name=None,
        dest_instance_name=None,
        data_restore=True,
        log_restore=False,
        overwrite=True,
        copy_precedence=None,
        from_time=None,
        to_time=None,
        media_agent=None,
        table_level_restore=False,
        clone_env=False,
        clone_options=None,
        redirect_enabled=False,
        redirect_path=None,
        browse_jobid=None):
    &#34;&#34;&#34;Restores the mysql data/log files specified in the input paths list to the same location.

        Args:
            path                    (list)  --  list of database/databases to be restored

                default: None

            staging                 (str)   --  staging location for mysql logs during restores

                default: None

            dest_client_name        (str)   --  destination client name where files are to be
            restored

                default: None

            dest_instance_name      (str)   --  destination mysql instance name of destination
            client

                default: None

            data_restore            (bool)  --  for data only/data+log restore

                default: True

            log_restore             (bool)  --  for log only/data+log restore

                default: False

            overwrite               (bool)  --  unconditional overwrite files during restore

                default: True

            copy_precedence         (int)   --  copy precedence value of storage policy copy

                default: None

            from_time               (str)   --  time to retore the contents after
                    format: YYYY-MM-DD HH:MM:SS

                default: None

            to_time                 (str)   --  time to retore the contents before
                    format: YYYY-MM-DD HH:MM:SS

                default: None

            media_agent             (str)   --  media agent associated

                default: None

            table_level_restore     (bool)  --  Table level restore flag

                default: False

            clone_env               (bool)  --  boolean to specify whether the database
            should be cloned or not

                default: False

            clone_options           (dict)  --  clone restore options passed in a dict

                default: None

                Accepted format: {
                                    &#34;stagingLocaion&#34;: &#34;/gk_snap&#34;,
                                    &#34;forceCleanup&#34;: True,
                                    &#34;port&#34;: &#34;5595&#34;,
                                    &#34;libDirectory&#34;: &#34;&#34;,
                                    &#34;isInstanceSelected&#34;: True,
                                    &#34;reservationPeriodS&#34;: 3600,
                                    &#34;user&#34;: &#34;&#34;,
                                    &#34;binaryDirectory&#34;: &#34;/usr/bin&#34;

                                 }

            redirect_enabled         (bool)  --  boolean to specify if redirect restore is
            enabled

                default: False

            redirect_path           (str)   --  Path specified in advanced restore options
            in order to perform redirect restore

                default: None

            browse_jobid           (int)   --  Browse jobid to browse and restore from

                default: None

        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:
                if paths is not a list

                if failed to initialize job

                if response is empty

                if response is not success

    &#34;&#34;&#34;
    if not (isinstance(path, list) and
            isinstance(overwrite, bool)):
        raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

    if path == []:
        raise SDKException(&#39;Instance&#39;, &#39;104&#39;)

    if dest_client_name is None:
        dest_client_name = self._agent_object._client_object.client_name

    if dest_instance_name is None:
        dest_instance_name = self.instance_name

    request_json = self._restore_json(
        paths=path,
        staging=staging,
        dest_client_name=dest_client_name,
        dest_instance_name=dest_instance_name,
        data_restore=data_restore,
        log_restore=log_restore,
        overwrite=overwrite,
        copy_precedence=copy_precedence,
        from_time=from_time,
        to_time=to_time,
        media_agent=media_agent,
        table_level_restore=table_level_restore,
        clone_env=clone_env,
        clone_options=clone_options,
        redirect_enabled=redirect_enabled,
        redirect_path=redirect_path,
        browse_jobid=browse_jobid)

    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.mysqlinstance.MYSQLInstance.restore_out_of_place"><code class="name flex">
<span>def <span class="ident">restore_out_of_place</span></span>(<span>self, path=None, staging=None, dest_client_name=None, dest_instance_name=None, data_restore=True, log_restore=False, overwrite=True, copy_precedence=None, from_time=None, to_time=None, media_agent=None, table_level_restore=False, clone_env=False, clone_options=None, redirect_enabled=False, redirect_path=None, browse_jobid=None, recurringRestore=False)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to perform out of place restore of MySQL data/log/recurring files to the destination client location.</p>
<pre><code>Args:
    path                    (list)  --  list of database/databases to be restored
    staging                 (str)   --  staging location for mysql logs during restores
    dest_client_name        (str)   --  destination client name where files are to be restored
    dest_instance_name      (str)   --  destination mysql instance name of destination client
    data_restore            (bool)  --  for data only/data+log restore
    log_restore             (bool)  --  for log only/data+log restore
    overwrite               (bool)  --  unconditional overwrite files during restore
    copy_precedence         (int)   --  copy precedence value of storage policy copy
    from_time               (str)   --  time to restore the contents after
            format: YYYY-MM-DD HH:MM:SS
    to_time                 (str)   --  time to restore the contents before
            format: YYYY-MM-DD HH:MM:SS
    media_agent             (str)   --  media agent associated
    table_level_restore     (bool)  --  Table level restore flag
    clone_env               (bool)  --  boolean to specify whether the database should be cloned or not
    clone_options           (dict)  --  clone restore options passed in a dict
    redirect_enabled         (bool)  --  boolean to specify if redirect restore is enabled
    redirect_path           (str)   --  Path specified in advanced restore options
    in order to perform redirect restore
    browse_jobid           (int)   --  Browse jobid to browse and restore from
    recurringRestore       (bool)  --  for Recurring restore

Returns:
    object - instance of the Job class for this restore job

Raises:
    SDKException:
        if paths is not a list

        if failed to initialize job

        if destination client name is empty

        if destination Instance name empty

        if response is empty

        if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/mysqlinstance.py#L578-L678" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_out_of_place(
        self,
        path=None,
        staging=None,
        dest_client_name=None,
        dest_instance_name=None,
        data_restore=True,
        log_restore=False,
        overwrite=True,
        copy_precedence=None,
        from_time=None,
        to_time=None,
        media_agent=None,
        table_level_restore=False,
        clone_env=False,
        clone_options=None,
        redirect_enabled=False,
        redirect_path=None,
        browse_jobid=None,
        recurringRestore=False):
    &#34;&#34;&#34;
    Method to perform out of place restore of MySQL data/log/recurring files to the destination client location.

        Args:
            path                    (list)  --  list of database/databases to be restored
            staging                 (str)   --  staging location for mysql logs during restores
            dest_client_name        (str)   --  destination client name where files are to be restored
            dest_instance_name      (str)   --  destination mysql instance name of destination client
            data_restore            (bool)  --  for data only/data+log restore
            log_restore             (bool)  --  for log only/data+log restore
            overwrite               (bool)  --  unconditional overwrite files during restore
            copy_precedence         (int)   --  copy precedence value of storage policy copy
            from_time               (str)   --  time to restore the contents after
                    format: YYYY-MM-DD HH:MM:SS
            to_time                 (str)   --  time to restore the contents before
                    format: YYYY-MM-DD HH:MM:SS
            media_agent             (str)   --  media agent associated
            table_level_restore     (bool)  --  Table level restore flag
            clone_env               (bool)  --  boolean to specify whether the database should be cloned or not
            clone_options           (dict)  --  clone restore options passed in a dict
            redirect_enabled         (bool)  --  boolean to specify if redirect restore is enabled
            redirect_path           (str)   --  Path specified in advanced restore options
            in order to perform redirect restore
            browse_jobid           (int)   --  Browse jobid to browse and restore from
            recurringRestore       (bool)  --  for Recurring restore

        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:
                if paths is not a list

                if failed to initialize job

                if destination client name is empty

                if destination Instance name empty

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    if not (isinstance(path, list) and
            isinstance(overwrite, bool)):
        raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

    if path == []:
        raise SDKException(&#39;Instance&#39;, &#39;104&#39;)

    if dest_client_name is None:
        raise SDKException(&#39;Client&#39;, &#39;102&#39;,
                           &#34;The destination client name is missing. &#34;
                           &#34;Please provide a valid destination client name to proceed&#34;)

    if dest_instance_name is None:
        raise SDKException(&#39;Instance&#39;, &#39;102&#39;,
                           &#34;The destination Instance name is missing. &#34;
                           &#34;Please provide a valid destination Instance name to proceed&#34;)

    request_json = self._restore_json(
        paths=path,
        staging=staging,
        dest_client_name=dest_client_name,
        dest_instance_name=dest_instance_name,
        data_restore=data_restore,
        log_restore=log_restore,
        overwrite=overwrite,
        copy_precedence=copy_precedence,
        from_time=from_time,
        to_time=to_time,
        media_agent=media_agent,
        table_level_restore=table_level_restore,
        clone_env=clone_env,
        clone_options=clone_options,
        redirect_enabled=redirect_enabled,
        redirect_path=redirect_path,
        browse_jobid=browse_jobid,
        recurringRestore=recurringRestore)

    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.instance.Instance" href="../instance.html#cvpysdk.instance.Instance">Instance</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.instance.Instance.backupsets" href="../instance.html#cvpysdk.instance.Instance.backupsets">backupsets</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.browse" href="../instance.html#cvpysdk.instance.Instance.browse">browse</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.credentials" href="../instance.html#cvpysdk.instance.Instance.credentials">credentials</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.find" href="../instance.html#cvpysdk.instance.Instance.find">find</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.instance_id" href="../instance.html#cvpysdk.instance.Instance.instance_id">instance_id</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.instance_name" href="../instance.html#cvpysdk.instance.Instance.instance_name">instance_name</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.name" href="../instance.html#cvpysdk.instance.Instance.name">name</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.properties" href="../instance.html#cvpysdk.instance.Instance.properties">properties</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.refresh" href="../instance.html#cvpysdk.instance.Instance.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.subclients" href="../instance.html#cvpysdk.instance.Instance.subclients">subclients</a></code></li>
<li><code><a title="cvpysdk.instance.Instance.update_properties" href="../instance.html#cvpysdk.instance.Instance.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#mysqlinstance">MYSQLInstance:</a></li>
<li><a href="#mysqlinstance-instance-attributes">MYSQLInstance instance Attributes:</a></li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.instances" href="index.html">cvpysdk.instances</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.instances.mysqlinstance.MYSQLInstance" href="#cvpysdk.instances.mysqlinstance.MYSQLInstance">MYSQLInstance</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.instances.mysqlinstance.MYSQLInstance.autodiscovery_enabled" href="#cvpysdk.instances.mysqlinstance.MYSQLInstance.autodiscovery_enabled">autodiscovery_enabled</a></code></li>
<li><code><a title="cvpysdk.instances.mysqlinstance.MYSQLInstance.binary_directory" href="#cvpysdk.instances.mysqlinstance.MYSQLInstance.binary_directory">binary_directory</a></code></li>
<li><code><a title="cvpysdk.instances.mysqlinstance.MYSQLInstance.command_line_sp_details" href="#cvpysdk.instances.mysqlinstance.MYSQLInstance.command_line_sp_details">command_line_sp_details</a></code></li>
<li><code><a title="cvpysdk.instances.mysqlinstance.MYSQLInstance.config_file" href="#cvpysdk.instances.mysqlinstance.MYSQLInstance.config_file">config_file</a></code></li>
<li><code><a title="cvpysdk.instances.mysqlinstance.MYSQLInstance.is_xtrabackup_enabled" href="#cvpysdk.instances.mysqlinstance.MYSQLInstance.is_xtrabackup_enabled">is_xtrabackup_enabled</a></code></li>
<li><code><a title="cvpysdk.instances.mysqlinstance.MYSQLInstance.log_backup_sp_details" href="#cvpysdk.instances.mysqlinstance.MYSQLInstance.log_backup_sp_details">log_backup_sp_details</a></code></li>
<li><code><a title="cvpysdk.instances.mysqlinstance.MYSQLInstance.log_data_directory" href="#cvpysdk.instances.mysqlinstance.MYSQLInstance.log_data_directory">log_data_directory</a></code></li>
<li><code><a title="cvpysdk.instances.mysqlinstance.MYSQLInstance.mysql_enterprise_backup_binary_path" href="#cvpysdk.instances.mysqlinstance.MYSQLInstance.mysql_enterprise_backup_binary_path">mysql_enterprise_backup_binary_path</a></code></li>
<li><code><a title="cvpysdk.instances.mysqlinstance.MYSQLInstance.mysql_username" href="#cvpysdk.instances.mysqlinstance.MYSQLInstance.mysql_username">mysql_username</a></code></li>
<li><code><a title="cvpysdk.instances.mysqlinstance.MYSQLInstance.no_lock_status" href="#cvpysdk.instances.mysqlinstance.MYSQLInstance.no_lock_status">no_lock_status</a></code></li>
<li><code><a title="cvpysdk.instances.mysqlinstance.MYSQLInstance.nt_username" href="#cvpysdk.instances.mysqlinstance.MYSQLInstance.nt_username">nt_username</a></code></li>
<li><code><a title="cvpysdk.instances.mysqlinstance.MYSQLInstance.port" href="#cvpysdk.instances.mysqlinstance.MYSQLInstance.port">port</a></code></li>
<li><code><a title="cvpysdk.instances.mysqlinstance.MYSQLInstance.proxy_options" href="#cvpysdk.instances.mysqlinstance.MYSQLInstance.proxy_options">proxy_options</a></code></li>
<li><code><a title="cvpysdk.instances.mysqlinstance.MYSQLInstance.restore_in_place" href="#cvpysdk.instances.mysqlinstance.MYSQLInstance.restore_in_place">restore_in_place</a></code></li>
<li><code><a title="cvpysdk.instances.mysqlinstance.MYSQLInstance.restore_out_of_place" href="#cvpysdk.instances.mysqlinstance.MYSQLInstance.restore_out_of_place">restore_out_of_place</a></code></li>
<li><code><a title="cvpysdk.instances.mysqlinstance.MYSQLInstance.ssl_enabled" href="#cvpysdk.instances.mysqlinstance.MYSQLInstance.ssl_enabled">ssl_enabled</a></code></li>
<li><code><a title="cvpysdk.instances.mysqlinstance.MYSQLInstance.version" href="#cvpysdk.instances.mysqlinstance.MYSQLInstance.version">version</a></code></li>
<li><code><a title="cvpysdk.instances.mysqlinstance.MYSQLInstance.xtrabackup_bin_path" href="#cvpysdk.instances.mysqlinstance.MYSQLInstance.xtrabackup_bin_path">xtrabackup_bin_path</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>