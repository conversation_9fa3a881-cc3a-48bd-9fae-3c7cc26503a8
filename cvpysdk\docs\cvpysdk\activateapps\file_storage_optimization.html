<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.activateapps.file_storage_optimization API documentation</title>
<meta name="description" content="Main file for performing operations on file storage optimization(FSO) app under Activate …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.activateapps.file_storage_optimization</code></h1>
</header>
<section id="section-intro">
<p>Main file for performing operations on file storage optimization(FSO) app under Activate.</p>
<p>'FsoTypes', 'FsoServers' , 'FsoServer', 'FsoServerGroups', 'FsoServerGroup' are 5 classes defined in this file</p>
<p>FsoTypes:
Class to represent different FSO types(Server/ServerGroup/Project)</p>
<p>FsoServers: Class to represent all FSO servers in the commcell</p>
<p>FsoServer:
Class to represent single FSO server in the commcell</p>
<p>FsoServerGroups:
Class to represent all FSO server groups in the commcell</p>
<p>FsoServerGroup:
Class to represent single FSO server group in the commcell</p>
<h2 id="fsoservers">Fsoservers</h2>
<p><strong>init</strong>()
&ndash;
initialise object of the FsoServers class</p>
<p>_response_not_success()
&ndash;
parses through the exception response, and raises SDKException</p>
<p>_get_all_fso_servers()
&ndash;
gets all the fso servers from the commcell</p>
<p>refresh()
&ndash;
refresh the FSO Servers associated with the commcell</p>
<p>has_server()
&ndash;
checks whether given server name exists in FSO or not</p>
<p>add_file_server()
&ndash;
adds file server to the FSO</p>
<p>get()
&ndash;
returns the FsoServer object for given server name</p>
<h2 id="fsoserver">Fsoserver</h2>
<p><strong>init</strong>()
&ndash;
initialise object of the FsoServer class</p>
<p>_response_not_success()
&ndash;
parses through the exception response, and raises SDKException</p>
<p>_get_server_properties()
&ndash;
gets FSO server details from the commcell</p>
<p>_get_schedule_object()
&ndash;
returns the schedule object for associated schedule</p>
<p>refresh()
&ndash;
refresh the FSO Server details</p>
<p>start_collection()
&ndash;
starts collection job on all data sources in this server</p>
<p>share()
&ndash;
shares server with given user name or group name</p>
<p>search()
&ndash;
returns the search response containing document details</p>
<p>add_schedule()
&ndash;
creates schedule for this fso server</p>
<p>delete_schedule()
&ndash;
deletes schedule for this fso server</p>
<h2 id="fsoserver-attributes">Fsoserver Attributes</h2>
<pre><code>**server_id**           --  returns the client id of the server

**server_details**      --  returns the server details

**data_sources**        --  returns the EdiscoveryDataSources object

**data_sources_name**   --  returns the list of data sources display name associated with this server

**total_data_sources**  --  returns the total number of data sources associated with this server

**total_doc_count**     --  returns the total document count from all data sources associated with this server

**schedule**            --  returns the schedule object for associated schedule with this server
</code></pre>
<h2 id="fsoservergroups">Fsoservergroups</h2>
<p><strong>init</strong>()
&ndash;
initialise object of the FsoServerGroups class</p>
<p>_response_not_success()
&ndash;
parses through the exception response, and raises SDKException</p>
<p>_get_all_fso_server_groups()
&ndash;
gets all the fso server groups from the commcell</p>
<p>refresh()
&ndash;
refresh the FSO Server groups associated with the commcell</p>
<p>has()
&ndash;
checks whether given server group name exists in FSO or not</p>
<p>get()
&ndash;
returns object of FsoServerGroup class</p>
<p>add_server_group()
&ndash;
adds server group to FSO</p>
<h2 id="fsoservergroup">Fsoservergroup</h2>
<p><strong>init</strong>()
&ndash;
initialise object of the FsoServerGroup class</p>
<p>_response_not_success()
&ndash;
parses through the exception response, and raises SDKException</p>
<p>_get_server_group_properties()
&ndash;
gets FSO server group details from the commcell</p>
<p>refresh()
&ndash;
refresh the FSO Server group details</p>
<p>has_server()
&ndash;
returns whether server name exists as part of server group or not</p>
<p>get()
&ndash;
returns the object of FsoServer class</p>
<p>start_collection()
&ndash;
starts collection job on all servers associated with this server group</p>
<p>search()
&ndash;
returns the search response containing document details</p>
<h2 id="fsoservergroup-attributes">FsoServerGroup Attributes:</h2>
<pre><code>**server_group_id**     --  returns the server group id

**server_group_props**  --  returns the properties of server group

**server_list**         --  returns the list of servers associated with this server group

**total_documents**     --  returns the total crawled document count for this server group
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/file_storage_optimization.py#L1-L1039" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Main file for performing operations on file storage optimization(FSO) app under Activate.

&#39;FsoTypes&#39;, &#39;FsoServers&#39; , &#39;FsoServer&#39;, &#39;FsoServerGroups&#39;, &#39;FsoServerGroup&#39; are 5 classes defined in this file

FsoTypes:  Class to represent different FSO types(Server/ServerGroup/Project)

FsoServers: Class to represent all FSO servers in the commcell

FsoServer:  Class to represent single FSO server in the commcell

FsoServerGroups:  Class to represent all FSO server groups in the commcell

FsoServerGroup:   Class to represent single FSO server group in the commcell

FsoServers:

    __init__()                          --  initialise object of the FsoServers class

     _response_not_success()            --  parses through the exception response, and raises SDKException

     _get_all_fso_servers()             --  gets all the fso servers from the commcell

     refresh()                          --  refresh the FSO Servers associated with the commcell

     has_server()                       --  checks whether given server name exists in FSO or not

     add_file_server()                  --  adds file server to the FSO

     get()                              --  returns the FsoServer object for given server name

FsoServer:

    __init__()                          --  initialise object of the FsoServer class

     _response_not_success()            --  parses through the exception response, and raises SDKException

     _get_server_properties()           --  gets FSO server details from the commcell

     _get_schedule_object()             --  returns the schedule object for associated schedule

     refresh()                          --  refresh the FSO Server details

     start_collection()                 --  starts collection job on all data sources in this server

     share()                            --  shares server with given user name or group name

     search()                           --  returns the search response containing document details

     add_schedule()                     --  creates schedule for this fso server

     delete_schedule()                  --  deletes schedule for this fso server

FsoServer Attributes
---------------------

    **server_id**           --  returns the client id of the server

    **server_details**      --  returns the server details

    **data_sources**        --  returns the EdiscoveryDataSources object

    **data_sources_name**   --  returns the list of data sources display name associated with this server

    **total_data_sources**  --  returns the total number of data sources associated with this server

    **total_doc_count**     --  returns the total document count from all data sources associated with this server

    **schedule**            --  returns the schedule object for associated schedule with this server

FsoServerGroups:

    __init__()                          --  initialise object of the FsoServerGroups class

     _response_not_success()            --  parses through the exception response, and raises SDKException

     _get_all_fso_server_groups()       --  gets all the fso server groups from the commcell

     refresh()                          --  refresh the FSO Server groups associated with the commcell

     has()                              --  checks whether given server group name exists in FSO or not

     get()                              --  returns object of FsoServerGroup class

     add_server_group()                 --  adds server group to FSO

FsoServerGroup:

    __init__()                          --  initialise object of the FsoServerGroup class

     _response_not_success()            --  parses through the exception response, and raises SDKException

     _get_server_group_properties()     --  gets FSO server group details from the commcell

     refresh()                          --  refresh the FSO Server group details

     has_server()                       --  returns whether server name exists as part of server group or not

     get()                              --  returns the object of FsoServer class

     start_collection()                 --  starts collection job on all servers associated with this server group

     search()                           --  returns the search response containing document details

FsoServerGroup Attributes:
---------------------------

    **server_group_id**     --  returns the server group id

    **server_group_props**  --  returns the properties of server group

    **server_list**         --  returns the list of servers associated with this server group

    **total_documents**     --  returns the total crawled document count for this server group

&#34;&#34;&#34;
import copy
import time
from enum import Enum

from ..schedules import Schedules

from ..activateapps.constants import EdiscoveryConstants

from ..activateapps.ediscovery_utils import EdiscoveryClients, EdiscoveryClientOperations, EdiscoveryDataSources

from ..exception import SDKException


class FsoTypes(Enum):
    &#34;&#34;&#34;Class to represent different FSO types(Server/ServerGroup/Project)&#34;&#34;&#34;
    SERVERS = 0
    SERVER_GROUPS = 1
    PROJECTS = 2


class FsoServers():
    &#34;&#34;&#34;Class for representing all FSO servers in the commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initializes an instance of the FsoServers class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

            Returns:
                object  -   instance of the FsoServers class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._fso_servers = None
        self._ediscovery_clients_obj = EdiscoveryClients(self._commcell_object, self)
        self._ediscovery_ds_obj = EdiscoveryDataSources(self._commcell_object, self)
        self.refresh()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def get(self, server_name):
        &#34;&#34;&#34;returns the FsoServer object for given server name

                Args:

                    server_name         (str)       --  Name of the server

                Returns:

                    obj --  Instance of FsoServer Class

                Raises:

                    SDKException:

                            if failed to find server in FSO App

                            if input is not valid

        &#34;&#34;&#34;
        if not isinstance(server_name, str):
            raise SDKException(&#39;FileStorageOptimization&#39;, &#39;101&#39;)
        if not self.has_server(server_name):
            raise SDKException(&#39;FileStorageOptimization&#39;, &#39;103&#39;)
        server_id = self._fso_servers[server_name.lower()][&#39;clientEntity&#39;][&#39;clientId&#39;]
        return FsoServer(commcell_object=self._commcell_object, server_name=server_name, server_id=server_id)

    def _get_all_fso_servers(self):
        &#34;&#34;&#34;Returns all the FSO servers found in the commcell

                Args:

                    None

                Returns:

                    dict        --  Containing FSO server details

                Raises;

                    SDKException:

                            if failed to get FSO servers details

                            if response is empty

                            if response is not success
        &#34;&#34;&#34;
        return self._ediscovery_clients_obj.get_ediscovery_clients()

    def refresh(self):
        &#34;&#34;&#34;Refresh the FSO Servers associated with the commcell.&#34;&#34;&#34;
        self._fso_servers = self._get_all_fso_servers()

    def add_file_server(self, server_name, data_source_name, inventory_name, plan_name,
                        source_type=EdiscoveryConstants.SourceType.BACKUP, **kwargs):
        &#34;&#34;&#34;Adds file system FSO server

                Args:

                    server_name         (str)       --  Server name which needs to be added

                    data_source_name    (str)       --  Name for data source

                    inventory_name      (str)       --  Inventory name which needs to be associated

                    plan_name           (str)       --  Plan name which needs to be associated with this data source

                    source_type         (enum)      --  Source type for crawl (Live source or Backedup)
                                                                Refer EdiscoveryConstants.SourceType

                Kwargs Arguments:

                    scan_type           (str)       --  Specifies scan type when source type is for backed up data
                                                                Supported values : quick | full

                    crawl_path          (list)      --  File path which needs to be crawl if source type is Live source

                    access_node         (str)       --  server name which needs to be used as access node
                                                            in case if server to be added is not a commvault client

                    country_name        (str)       --  country name where server is located (default : USA)

                    country_code        (str)       --  Country code (ISO 3166 2-letter code)

                    user_name           (str)       --  User name who has access to UNC path

                    password            (str)       --  base64 encoded password to access unc path

                    enable_monitoring   (str)       --  specifies whether to enable file monitoring or not for this


                Returns:

                    obj     --  Instance of FSOServer class

                Raises:

                      SDKException:

                            if plan/inventory/index server doesn&#39;t exists

                            if failed to add FSO server data source
        &#34;&#34;&#34;
        self._ediscovery_ds_obj.add_fs_data_source(
            server_name=server_name,
            data_source_name=data_source_name,
            inventory_name=inventory_name,
            plan_name=plan_name,
            source_type=source_type,
            **kwargs)
        is_commvault_client = self._commcell_object.clients.has_client(server_name)
        server_id = 0
        if not is_commvault_client:
            all_clients = self._commcell_object.clients.all_clients
            for client_name, client_details in all_clients.items():
                if client_name.lower().startswith(f&#34;{data_source_name.lower()}_&#34;):
                    server_id = client_details[&#39;id&#39;]
                    break
        else:
            server_id = self._commcell_object.clients.get(server_name).client_id
        return FsoServer(commcell_object=self._commcell_object, server_name=server_name,
                         server_id=server_id)

    def has_server(self, server_name):
        &#34;&#34;&#34;Checks if a server exists in the commcell with the input name for FSO or not

            Args:
                server_name (str)  --  name of the server

            Returns:
                bool - boolean output whether the FSO Server exists in the commcell or not

            Raises:
                SDKException:
                    if type of the server name argument is not string

        &#34;&#34;&#34;
        if not isinstance(server_name, str):
            raise SDKException(&#39;FileStorageOptimization&#39;, &#39;101&#39;)
        return self._fso_servers and server_name.lower() in self._fso_servers


class FsoServer():
    &#34;&#34;&#34;Class to represent single FSO Server in the commcell&#34;&#34;&#34;

    def __init__(self, commcell_object, server_name, server_id=None):
        &#34;&#34;&#34;Initializes an instance of the FsoServer class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

                server_name         (str)       --  Name of the server

                server_id           (int)       --  server client id

            Returns:
                object  -   instance of the FsoServer class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._server_name = server_name
        self._server_id = None
        self._server_props = None
        self._schedule_obj = None
        self._CREATE_POLICY = self._services[&#39;CREATE_UPDATE_SCHEDULE_POLICY&#39;]
        if server_id:
            self._server_id = server_id
        else:
            self._server_id = self._commcell_object.activate.file_storage_optimization().get(server_name).server_id
        self._ediscovery_client_ops = EdiscoveryClientOperations(self._commcell_object, self)
        self._ediscovery_data_srcs_obj = EdiscoveryDataSources(self._commcell_object, self)
        self.refresh()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def start_collection(self):
        &#34;&#34;&#34;Starts collection job on all data sources associated with this server

                Args:

                    None

                Return:

                    list    --  List of jobid&#39;s

                Raises:

                    SDKException:

                            if failed to start collection job

        &#34;&#34;&#34;
        request_json = copy.deepcopy(EdiscoveryConstants.START_CRAWL_SERVER_REQUEST_JSON)
        request_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;clientId&#39;] = self._server_id
        request_json[&#39;taskInfo&#39;][&#39;task&#39;][
            &#39;taskName&#39;] = f&#34;Cvpysdk_FSO_server_Crawl_{self._server_name}_{int(time.time())}&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._CREATE_POLICY, request_json
        )
        output = []
        if flag:
            if response.json() and &#39;jobIds&#39; in response.json():
                for node in response.json()[&#39;jobIds&#39;]:
                    output.append(node)
                return output
            raise SDKException(&#39;FileStorageOptimization&#39;, &#39;105&#39;)
        self._response_not_success(response)

    def _get_schedule_object(self):
        &#34;&#34;&#34;returns the schedule object for associated schedule for this fso server

            Args:
                None

            Returns:

                obj --  Instance of Schedule class

                None -- if no schedule exists

            Raises:

                SDKException:

                        if failed to find schedule details associated with this server
        &#34;&#34;&#34;
        scd_obj = Schedules(self)
        if scd_obj.has_schedule():
            return scd_obj.get()
        return None

    def _get_server_properties(self):
        &#34;&#34;&#34;gets FSO server details from the commcell

                Args:

                    None

                Returns:

                    dict    --  Containing FSO Server details

                Raises:

                     Raises;

                        SDKException:

                            if failed to get server details

        &#34;&#34;&#34;
        self._ediscovery_data_srcs_obj.refresh()  # do refresh before fetching so that doc count comes up fine
        return self._ediscovery_data_srcs_obj.ediscovery_client_props

    def search(self, criteria=None, attr_list=None, params=None):
        &#34;&#34;&#34;do searches on data source and returns document details

            Args:

                criteria        (str)      --  containing criteria for query
                                                    (Default : None - returns all docs)

                                                    Example :

                                                        Size:[10 TO 1024]
                                                        FileName:09_23*

                attr_list       (set)      --  Column names to be returned in results.
                                                     Acts as &#39;fl&#39; in query

                params          (dict)     --  Any other params which needs to be passed
                                                   Example : { &#34;start&#34; : &#34;0&#34; }

            Returns:

                int,list(dict),dict    --  Containing document count, document details &amp; facet details(if any)

            Raises:

                SDKException:

                        if failed to perform search

        &#34;&#34;&#34;
        return self._ediscovery_client_ops.search(criteria=criteria, attr_list=attr_list, params=params)

    def add_schedule(self, schedule_name, pattern_json):
        &#34;&#34;&#34;Creates the schedule and associate it with server

                        Args:

                            schedule_name       (str)       --  Schedule name

                            pattern_json        (dict)      --  Schedule pattern dict
                                                                (Refer to Create_schedule_pattern in schedule.py)

                        Raises:

                              SDKException:

                                    if input is not valid

                                    if failed to create schedule

        &#34;&#34;&#34;
        self._ediscovery_client_ops.schedule(schedule_name=schedule_name, pattern_json=pattern_json)
        self.refresh()

    def delete_schedule(self):
        &#34;&#34;&#34;Deletes the schedule associated with server

                        Args:

                            None

                        Raises:

                              SDKException:

                                    if failed to Delete schedule

        &#34;&#34;&#34;
        if not self._schedule_obj:
            raise SDKException(&#39;FileStorageOptimization&#39;, &#39;102&#39;, &#34;No schedule is associated to this FSO Server&#34;)
        Schedules(self).delete()
        self.refresh()

    def share(self, user_or_group_name, allow_edit_permission=False, is_user=True, ops_type=1):
        &#34;&#34;&#34;Shares Fso server with given user or user group in commcell

                Args:

                    user_or_group_name      (str)       --  Name of user or group

                    is_user                 (bool)      --  Denotes whether this is user or group name
                                                                default : True(User)

                    allow_edit_permission   (bool)      --  whether to give edit permission or not to user or group

                    ops_type                (int)       --  Operation type

                                                            Default : 1 (Add)

                                                            Supported : 1 (Add)
                                                                        3 (Delete)

                Returns:

                    None

                Raises:

                    SDKException:

                            if unable to update security associations

                            if response is empty or not success
        &#34;&#34;&#34;
        return self._ediscovery_client_ops.share(
            user_or_group_name=user_or_group_name,
            allow_edit_permission=allow_edit_permission,
            is_user=is_user,
            ops_type=ops_type)

    def refresh(self):
        &#34;&#34;&#34;Refresh the FSO Server details&#34;&#34;&#34;
        self._server_props = self._get_server_properties()
        self._schedule_obj = self._get_schedule_object()

    @property
    def schedule(self):
        &#34;&#34;&#34;returns the schedule object for associated schedule

                Returns:

                    obj     --  Instance of Schedule Class if schedule exists

                    None    --  If no schedule exists

        &#34;&#34;&#34;
        return self._schedule_obj

    @property
    def server_id(self):
        &#34;&#34;&#34;returns the server id

            Returns:

                int --  Server id

        &#34;&#34;&#34;
        return self._server_id

    @property
    def server_details(self):
        &#34;&#34;&#34;returns the server details

            Returns:

                dict --  Server details

        &#34;&#34;&#34;
        return self._server_props

    @property
    def data_sources_name(self):
        &#34;&#34;&#34;returns the associated data sources to this FSO server

            Returns:

                list --  names of data sources

        &#34;&#34;&#34;
        return self._ediscovery_data_srcs_obj.data_sources

    @property
    def data_sources(self):
        &#34;&#34;&#34;returns the EdiscoveryDataSources object associated to this server

            Returns:

                obj --  Instance of EdiscoveryDataSources Object

        &#34;&#34;&#34;
        return self._ediscovery_data_srcs_obj

    @property
    def total_data_sources(self):
        &#34;&#34;&#34;returns the total number of data sources associated with this server

            Returns:

                int --  total number of data sources

        &#34;&#34;&#34;
        return len(self._ediscovery_data_srcs_obj.data_sources)

    @property
    def total_doc_count(self):
        &#34;&#34;&#34;returns the total document count of all data sources for this server

            Returns:

                int --  Total crawled document count

        &#34;&#34;&#34;
        return self._ediscovery_data_srcs_obj.total_documents


class FsoServerGroups():
    &#34;&#34;&#34;Class for representing all FSO server groups in the commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initializes an instance of the FsoServerGroups class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

            Returns:
                object  -   instance of the FsoServerGroups class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._fso_server_groups = None
        self._ediscovery_clients_obj = EdiscoveryClients(self._commcell_object, self)
        self._ediscovery_ds_obj = EdiscoveryDataSources(self._commcell_object, self)
        self.refresh()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _get_all_fso_server_groups(self):
        &#34;&#34;&#34;Returns all the FSO server groups found in the commcell

                Args:

                    None

                Returns:

                    dict        --  Containing FSO server group details

                Raises;

                    SDKException:

                            if failed to get FSO server group details

                            if response is empty

                            if response is not success
        &#34;&#34;&#34;
        return self._ediscovery_clients_obj.get_ediscovery_clients()

    def refresh(self):
        &#34;&#34;&#34;Refresh the FSO Server groups associated with the commcell.&#34;&#34;&#34;
        self._fso_server_groups = self._get_all_fso_server_groups()

    def add_server_group(self, server_group_name, inventory_name, plan_name, **kwargs):
        &#34;&#34;&#34;adds server group to FSO

                Args:

                    server_group_name       (str)       --      Server group name

                    inventory_name          (str)       --  Inventory name which needs to be associated

                    plan_name               (str)       --  Plan name which needs to be associated with this data source

                 Kwargs Arguments:

                    country_name        (str)       --  country name where server is located (default: USA)

                    country_code        (str)       --  Country code (ISO 3166 2-letter code)

                Returns:

                    obj     --  Instance of FSOServerGroup class

                Raises:

                      SDKException:

                            if plan/inventory/index server doesn&#39;t exists

                            if failed to add FSO server group

        &#34;&#34;&#34;
        self._ediscovery_ds_obj.add_fs_data_source(
            server_name=server_group_name,
            data_source_name=server_group_name,
            inventory_name=inventory_name,
            plan_name=plan_name,
            **kwargs)
        return FsoServerGroup(
            self._commcell_object,
            server_group_name,
            server_id=self._commcell_object.client_groups.get(server_group_name).clientgroup_id)

    def has(self, server_group_name):
        &#34;&#34;&#34;Checks if a server group exists in the commcell with the input name for FSO or not

            Args:
                server_group_name (str)  --  name of the server group

            Returns:
                bool - boolean output whether the FSO Server group exists in the commcell or not

            Raises:
                SDKException:
                    if type of the server group name argument is not string

        &#34;&#34;&#34;
        if not isinstance(server_group_name, str):
            raise SDKException(&#39;FileStorageOptimization&#39;, &#39;101&#39;)
        return self._fso_server_groups and server_group_name.lower() in self._fso_server_groups

    def get(self, server_grp_name):
        &#34;&#34;&#34;returns the FsoServerGroup object for given server group name

                Args:

                    server_grp_name         (str)       --  Name of the server group

                Returns:

                    obj --  Instance of FsoServerGroup Class

                Raises:

                    SDKException:

                            if failed to find server group in FSO App

                            if input is not valid

        &#34;&#34;&#34;
        if not isinstance(server_grp_name, str):
            raise SDKException(&#39;FileStorageOptimization&#39;, &#39;101&#39;)
        if not self.has(server_grp_name):
            raise SDKException(&#39;FileStorageOptimization&#39;, &#39;106&#39;)
        server_id = self._fso_server_groups[server_grp_name.lower()][&#39;clientEntity&#39;][&#39;clientId&#39;]
        return FsoServerGroup(
            commcell_object=self._commcell_object,
            server_group_name=server_grp_name,
            server_id=server_id)


class FsoServerGroup():
    &#34;&#34;&#34;Class to represent single FSO Server group in the commcell&#34;&#34;&#34;

    def __init__(self, commcell_object, server_group_name, server_id=None):
        &#34;&#34;&#34;Initializes an instance of the FsoServerGroup class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

            Returns:
                object  -   instance of the FsoServerGroup class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._server_group_name = server_group_name
        self._server_id = None
        self._server_grp_props = None
        self._server_names = []
        self._total_doc = 0
        self._CREATE_POLICY = self._services[&#39;CREATE_UPDATE_SCHEDULE_POLICY&#39;]
        if server_id:
            self._server_id = server_id
        else:
            self._server_id = self._commcell_object.activate.file_storage_optimization(
                FsoTypes.SERVER_GROUPS).get(server_group_name).server_group_id
        self._ediscovery_clients_obj = EdiscoveryClients(self._commcell_object, self)
        self._ediscovery_client_ops = EdiscoveryClientOperations(self._commcell_object, self)
        self.refresh()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _get_server_group_properties(self):
        &#34;&#34;&#34;gets FSO server group details from the commcell

                Args:

                    None

                Returns:

                    dict    --  Containing FSO Server group details

                Raises:

                     Raises;

                        SDKException:

                            if failed to get server group details

        &#34;&#34;&#34;
        client_resp = self._ediscovery_clients_obj.get_ediscovery_clients()
        grp_resp = self._ediscovery_clients_obj.get_ediscovery_client_group_details()
        if &#39;nodeList&#39; in grp_resp:
            grp_resp = grp_resp[&#39;nodeList&#39;][0]
            if &#39;childs&#39; in grp_resp and &#39;customProperties&#39; in grp_resp[&#39;childs&#39;][0]:
                name_value_dict = grp_resp[&#39;childs&#39;][0][&#39;customProperties&#39;][&#39;nameValues&#39;]
                for prop in name_value_dict:
                    prop_name = prop.get(&#39;name&#39;)
                    if prop_name == EdiscoveryConstants.FIELD_DOCUMENT_COUNT:
                        self._total_doc = int(prop.get(&#39;value&#39;))
                        break
        self._server_names = []
        for key, value in client_resp.items():
            self._server_names.append(key)
        return client_resp

    def has_server(self, server_name):
        &#34;&#34;&#34;Checks if a server exists in the FSO Server group with the input name or not

            Args:
                server_name (str)  --  name of the server

            Returns:
                bool - boolean output whether the FSO Server exists in the server group or not

            Raises:
                SDKException:
                    if type of the server name argument is not string

        &#34;&#34;&#34;
        if not isinstance(server_name, str):
            raise SDKException(&#39;FileStorageOptimization&#39;, &#39;101&#39;)
        return self._server_grp_props and server_name.lower() in self._server_grp_props

    def start_collection(self):
        &#34;&#34;&#34;Starts collection job on all servers associated with this server group

                Args:

                    None

                Return:

                    list    --  List of jobid&#39;s

                Raises:

                    SDKException:

                            if failed to start collection job

        &#34;&#34;&#34;
        request_json = copy.deepcopy(EdiscoveryConstants.START_CRAWL_SERVER_REQUEST_JSON)
        # delete clientid key and add client group level key and entity type
        del request_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;clientId&#39;]
        request_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;clientGroupId&#39;] = self._server_id
        request_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;_type_&#39;] = 28  # server group level job collection
        request_json[&#39;taskInfo&#39;][&#39;task&#39;][
            &#39;taskName&#39;] = f&#34;Cvpysdk_FSO_server_Crawl_{self._server_group_name}_{int(time.time())}&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._CREATE_POLICY, request_json
        )
        output = []
        if flag:
            if response.json() and &#39;jobIds&#39; in response.json():
                for node in response.json()[&#39;jobIds&#39;]:
                    output.append(node)
                return output
            raise SDKException(&#39;FileStorageOptimization&#39;, &#39;107&#39;)
        self._response_not_success(response)

    def search(self, criteria=None, attr_list=None, params=None):
        &#34;&#34;&#34;do searches on client group data and returns document details

            Args:

                criteria        (str)      --  containing criteria for query
                                                    (Default : None - returns all docs)

                                                    Example :

                                                        Size:[10 TO 1024]
                                                        FileName:09_23*

                attr_list       (set)      --  Column names to be returned in results.
                                                     Acts as &#39;fl&#39; in query

                params          (dict)     --  Any other params which needs to be passed
                                                   Example : { &#34;start&#34; : &#34;0&#34; }

            Returns:

                int,list(dict),dict    --  Containing document count, document details &amp; facet details(if any)

            Raises:

                SDKException:

                        if failed to perform search

        &#34;&#34;&#34;
        return self._ediscovery_client_ops.search(criteria=criteria, attr_list=attr_list, params=params)

    def get(self, server_name):
        &#34;&#34;&#34;returns the FsoServer object for given server name

                Args:

                    server_name         (str)       --  Name of the server

                Returns:

                    obj --  Instance of FsoServer Class

                Raises:

                    SDKException:

                            if failed to find server in FSO server group

                            if input is not valid

        &#34;&#34;&#34;
        if not isinstance(server_name, str):
            raise SDKException(&#39;FileStorageOptimization&#39;, &#39;101&#39;)
        if not self.has_server(server_name):
            raise SDKException(&#39;FileStorageOptimization&#39;, &#39;103&#39;)
        server_id = self._server_grp_props[server_name.lower()][&#39;clientEntity&#39;][&#39;clientId&#39;]
        return FsoServer(commcell_object=self._commcell_object, server_name=server_name, server_id=server_id)

    def refresh(self):
        &#34;&#34;&#34;Refresh the FSO Server group details&#34;&#34;&#34;
        self._server_grp_props = self._get_server_group_properties()

    @property
    def server_group_id(self):
        &#34;&#34;&#34;returns the client group id

            Returns:

                int --  Server group id

        &#34;&#34;&#34;
        return self._server_id

    @property
    def server_group_props(self):
        &#34;&#34;&#34;returns the server group properties

            Returns:

                dict --  Server group details

        &#34;&#34;&#34;
        return self._server_grp_props

    @property
    def server_list(self):
        &#34;&#34;&#34;returns the list of server which is part of this server group

            Returns:

                list --  Server names

        &#34;&#34;&#34;
        return self._server_names

    @property
    def total_documents(self):
        &#34;&#34;&#34;returns the total documents count for this server group

            Returns:

                int --  Total document count for this server group

        &#34;&#34;&#34;
        return self._total_doc</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.activateapps.file_storage_optimization.FsoServer"><code class="flex name class">
<span>class <span class="ident">FsoServer</span></span>
<span>(</span><span>commcell_object, server_name, server_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class to represent single FSO Server in the commcell</p>
<p>Initializes an instance of the FsoServer class.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the commcell class</p>
<p>server_name
(str)
&ndash;
Name of the server</p>
<p>server_id
(int)
&ndash;
server client id</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the FsoServer class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/file_storage_optimization.py#L330-L647" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class FsoServer():
    &#34;&#34;&#34;Class to represent single FSO Server in the commcell&#34;&#34;&#34;

    def __init__(self, commcell_object, server_name, server_id=None):
        &#34;&#34;&#34;Initializes an instance of the FsoServer class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

                server_name         (str)       --  Name of the server

                server_id           (int)       --  server client id

            Returns:
                object  -   instance of the FsoServer class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._server_name = server_name
        self._server_id = None
        self._server_props = None
        self._schedule_obj = None
        self._CREATE_POLICY = self._services[&#39;CREATE_UPDATE_SCHEDULE_POLICY&#39;]
        if server_id:
            self._server_id = server_id
        else:
            self._server_id = self._commcell_object.activate.file_storage_optimization().get(server_name).server_id
        self._ediscovery_client_ops = EdiscoveryClientOperations(self._commcell_object, self)
        self._ediscovery_data_srcs_obj = EdiscoveryDataSources(self._commcell_object, self)
        self.refresh()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def start_collection(self):
        &#34;&#34;&#34;Starts collection job on all data sources associated with this server

                Args:

                    None

                Return:

                    list    --  List of jobid&#39;s

                Raises:

                    SDKException:

                            if failed to start collection job

        &#34;&#34;&#34;
        request_json = copy.deepcopy(EdiscoveryConstants.START_CRAWL_SERVER_REQUEST_JSON)
        request_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;clientId&#39;] = self._server_id
        request_json[&#39;taskInfo&#39;][&#39;task&#39;][
            &#39;taskName&#39;] = f&#34;Cvpysdk_FSO_server_Crawl_{self._server_name}_{int(time.time())}&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._CREATE_POLICY, request_json
        )
        output = []
        if flag:
            if response.json() and &#39;jobIds&#39; in response.json():
                for node in response.json()[&#39;jobIds&#39;]:
                    output.append(node)
                return output
            raise SDKException(&#39;FileStorageOptimization&#39;, &#39;105&#39;)
        self._response_not_success(response)

    def _get_schedule_object(self):
        &#34;&#34;&#34;returns the schedule object for associated schedule for this fso server

            Args:
                None

            Returns:

                obj --  Instance of Schedule class

                None -- if no schedule exists

            Raises:

                SDKException:

                        if failed to find schedule details associated with this server
        &#34;&#34;&#34;
        scd_obj = Schedules(self)
        if scd_obj.has_schedule():
            return scd_obj.get()
        return None

    def _get_server_properties(self):
        &#34;&#34;&#34;gets FSO server details from the commcell

                Args:

                    None

                Returns:

                    dict    --  Containing FSO Server details

                Raises:

                     Raises;

                        SDKException:

                            if failed to get server details

        &#34;&#34;&#34;
        self._ediscovery_data_srcs_obj.refresh()  # do refresh before fetching so that doc count comes up fine
        return self._ediscovery_data_srcs_obj.ediscovery_client_props

    def search(self, criteria=None, attr_list=None, params=None):
        &#34;&#34;&#34;do searches on data source and returns document details

            Args:

                criteria        (str)      --  containing criteria for query
                                                    (Default : None - returns all docs)

                                                    Example :

                                                        Size:[10 TO 1024]
                                                        FileName:09_23*

                attr_list       (set)      --  Column names to be returned in results.
                                                     Acts as &#39;fl&#39; in query

                params          (dict)     --  Any other params which needs to be passed
                                                   Example : { &#34;start&#34; : &#34;0&#34; }

            Returns:

                int,list(dict),dict    --  Containing document count, document details &amp; facet details(if any)

            Raises:

                SDKException:

                        if failed to perform search

        &#34;&#34;&#34;
        return self._ediscovery_client_ops.search(criteria=criteria, attr_list=attr_list, params=params)

    def add_schedule(self, schedule_name, pattern_json):
        &#34;&#34;&#34;Creates the schedule and associate it with server

                        Args:

                            schedule_name       (str)       --  Schedule name

                            pattern_json        (dict)      --  Schedule pattern dict
                                                                (Refer to Create_schedule_pattern in schedule.py)

                        Raises:

                              SDKException:

                                    if input is not valid

                                    if failed to create schedule

        &#34;&#34;&#34;
        self._ediscovery_client_ops.schedule(schedule_name=schedule_name, pattern_json=pattern_json)
        self.refresh()

    def delete_schedule(self):
        &#34;&#34;&#34;Deletes the schedule associated with server

                        Args:

                            None

                        Raises:

                              SDKException:

                                    if failed to Delete schedule

        &#34;&#34;&#34;
        if not self._schedule_obj:
            raise SDKException(&#39;FileStorageOptimization&#39;, &#39;102&#39;, &#34;No schedule is associated to this FSO Server&#34;)
        Schedules(self).delete()
        self.refresh()

    def share(self, user_or_group_name, allow_edit_permission=False, is_user=True, ops_type=1):
        &#34;&#34;&#34;Shares Fso server with given user or user group in commcell

                Args:

                    user_or_group_name      (str)       --  Name of user or group

                    is_user                 (bool)      --  Denotes whether this is user or group name
                                                                default : True(User)

                    allow_edit_permission   (bool)      --  whether to give edit permission or not to user or group

                    ops_type                (int)       --  Operation type

                                                            Default : 1 (Add)

                                                            Supported : 1 (Add)
                                                                        3 (Delete)

                Returns:

                    None

                Raises:

                    SDKException:

                            if unable to update security associations

                            if response is empty or not success
        &#34;&#34;&#34;
        return self._ediscovery_client_ops.share(
            user_or_group_name=user_or_group_name,
            allow_edit_permission=allow_edit_permission,
            is_user=is_user,
            ops_type=ops_type)

    def refresh(self):
        &#34;&#34;&#34;Refresh the FSO Server details&#34;&#34;&#34;
        self._server_props = self._get_server_properties()
        self._schedule_obj = self._get_schedule_object()

    @property
    def schedule(self):
        &#34;&#34;&#34;returns the schedule object for associated schedule

                Returns:

                    obj     --  Instance of Schedule Class if schedule exists

                    None    --  If no schedule exists

        &#34;&#34;&#34;
        return self._schedule_obj

    @property
    def server_id(self):
        &#34;&#34;&#34;returns the server id

            Returns:

                int --  Server id

        &#34;&#34;&#34;
        return self._server_id

    @property
    def server_details(self):
        &#34;&#34;&#34;returns the server details

            Returns:

                dict --  Server details

        &#34;&#34;&#34;
        return self._server_props

    @property
    def data_sources_name(self):
        &#34;&#34;&#34;returns the associated data sources to this FSO server

            Returns:

                list --  names of data sources

        &#34;&#34;&#34;
        return self._ediscovery_data_srcs_obj.data_sources

    @property
    def data_sources(self):
        &#34;&#34;&#34;returns the EdiscoveryDataSources object associated to this server

            Returns:

                obj --  Instance of EdiscoveryDataSources Object

        &#34;&#34;&#34;
        return self._ediscovery_data_srcs_obj

    @property
    def total_data_sources(self):
        &#34;&#34;&#34;returns the total number of data sources associated with this server

            Returns:

                int --  total number of data sources

        &#34;&#34;&#34;
        return len(self._ediscovery_data_srcs_obj.data_sources)

    @property
    def total_doc_count(self):
        &#34;&#34;&#34;returns the total document count of all data sources for this server

            Returns:

                int --  Total crawled document count

        &#34;&#34;&#34;
        return self._ediscovery_data_srcs_obj.total_documents</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.activateapps.file_storage_optimization.FsoServer.data_sources"><code class="name">var <span class="ident">data_sources</span></code></dt>
<dd>
<div class="desc"><p>returns the EdiscoveryDataSources object associated to this server</p>
<h2 id="returns">Returns</h2>
<p>obj &ndash;
Instance of EdiscoveryDataSources Object</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/file_storage_optimization.py#L616-L625" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def data_sources(self):
    &#34;&#34;&#34;returns the EdiscoveryDataSources object associated to this server

        Returns:

            obj --  Instance of EdiscoveryDataSources Object

    &#34;&#34;&#34;
    return self._ediscovery_data_srcs_obj</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.file_storage_optimization.FsoServer.data_sources_name"><code class="name">var <span class="ident">data_sources_name</span></code></dt>
<dd>
<div class="desc"><p>returns the associated data sources to this FSO server</p>
<h2 id="returns">Returns</h2>
<p>list &ndash;
names of data sources</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/file_storage_optimization.py#L605-L614" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def data_sources_name(self):
    &#34;&#34;&#34;returns the associated data sources to this FSO server

        Returns:

            list --  names of data sources

    &#34;&#34;&#34;
    return self._ediscovery_data_srcs_obj.data_sources</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.file_storage_optimization.FsoServer.schedule"><code class="name">var <span class="ident">schedule</span></code></dt>
<dd>
<div class="desc"><p>returns the schedule object for associated schedule</p>
<h2 id="returns">Returns</h2>
<p>obj
&ndash;
Instance of Schedule Class if schedule exists</p>
<p>None
&ndash;
If no schedule exists</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/file_storage_optimization.py#L570-L581" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def schedule(self):
    &#34;&#34;&#34;returns the schedule object for associated schedule

            Returns:

                obj     --  Instance of Schedule Class if schedule exists

                None    --  If no schedule exists

    &#34;&#34;&#34;
    return self._schedule_obj</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.file_storage_optimization.FsoServer.server_details"><code class="name">var <span class="ident">server_details</span></code></dt>
<dd>
<div class="desc"><p>returns the server details</p>
<h2 id="returns">Returns</h2>
<p>dict &ndash;
Server details</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/file_storage_optimization.py#L594-L603" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def server_details(self):
    &#34;&#34;&#34;returns the server details

        Returns:

            dict --  Server details

    &#34;&#34;&#34;
    return self._server_props</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.file_storage_optimization.FsoServer.server_id"><code class="name">var <span class="ident">server_id</span></code></dt>
<dd>
<div class="desc"><p>returns the server id</p>
<h2 id="returns">Returns</h2>
<p>int &ndash;
Server id</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/file_storage_optimization.py#L583-L592" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def server_id(self):
    &#34;&#34;&#34;returns the server id

        Returns:

            int --  Server id

    &#34;&#34;&#34;
    return self._server_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.file_storage_optimization.FsoServer.total_data_sources"><code class="name">var <span class="ident">total_data_sources</span></code></dt>
<dd>
<div class="desc"><p>returns the total number of data sources associated with this server</p>
<h2 id="returns">Returns</h2>
<p>int &ndash;
total number of data sources</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/file_storage_optimization.py#L627-L636" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def total_data_sources(self):
    &#34;&#34;&#34;returns the total number of data sources associated with this server

        Returns:

            int --  total number of data sources

    &#34;&#34;&#34;
    return len(self._ediscovery_data_srcs_obj.data_sources)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.file_storage_optimization.FsoServer.total_doc_count"><code class="name">var <span class="ident">total_doc_count</span></code></dt>
<dd>
<div class="desc"><p>returns the total document count of all data sources for this server</p>
<h2 id="returns">Returns</h2>
<p>int &ndash;
Total crawled document count</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/file_storage_optimization.py#L638-L647" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def total_doc_count(self):
    &#34;&#34;&#34;returns the total document count of all data sources for this server

        Returns:

            int --  Total crawled document count

    &#34;&#34;&#34;
    return self._ediscovery_data_srcs_obj.total_documents</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.activateapps.file_storage_optimization.FsoServer.add_schedule"><code class="name flex">
<span>def <span class="ident">add_schedule</span></span>(<span>self, schedule_name, pattern_json)</span>
</code></dt>
<dd>
<div class="desc"><p>Creates the schedule and associate it with server</p>
<h2 id="args">Args</h2>
<p>schedule_name
(str)
&ndash;
Schedule name</p>
<p>pattern_json
(dict)
&ndash;
Schedule pattern dict
(Refer to Create_schedule_pattern in schedule.py)</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>  if input is not valid

  if failed to create schedule
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/file_storage_optimization.py#L487-L507" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_schedule(self, schedule_name, pattern_json):
    &#34;&#34;&#34;Creates the schedule and associate it with server

                    Args:

                        schedule_name       (str)       --  Schedule name

                        pattern_json        (dict)      --  Schedule pattern dict
                                                            (Refer to Create_schedule_pattern in schedule.py)

                    Raises:

                          SDKException:

                                if input is not valid

                                if failed to create schedule

    &#34;&#34;&#34;
    self._ediscovery_client_ops.schedule(schedule_name=schedule_name, pattern_json=pattern_json)
    self.refresh()</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.file_storage_optimization.FsoServer.delete_schedule"><code class="name flex">
<span>def <span class="ident">delete_schedule</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes the schedule associated with server</p>
<h2 id="args">Args</h2>
<p>None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>  if failed to Delete schedule
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/file_storage_optimization.py#L509-L526" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete_schedule(self):
    &#34;&#34;&#34;Deletes the schedule associated with server

                    Args:

                        None

                    Raises:

                          SDKException:

                                if failed to Delete schedule

    &#34;&#34;&#34;
    if not self._schedule_obj:
        raise SDKException(&#39;FileStorageOptimization&#39;, &#39;102&#39;, &#34;No schedule is associated to this FSO Server&#34;)
    Schedules(self).delete()
    self.refresh()</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.file_storage_optimization.FsoServer.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the FSO Server details</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/file_storage_optimization.py#L565-L568" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the FSO Server details&#34;&#34;&#34;
    self._server_props = self._get_server_properties()
    self._schedule_obj = self._get_schedule_object()</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.file_storage_optimization.FsoServer.search"><code class="name flex">
<span>def <span class="ident">search</span></span>(<span>self, criteria=None, attr_list=None, params=None)</span>
</code></dt>
<dd>
<div class="desc"><p>do searches on data source and returns document details</p>
<h2 id="args">Args</h2>
<p>criteria
(str)
&ndash;
containing criteria for query
(Default : None - returns all docs)</p>
<pre><code>                                Example :

                                    Size:[10 TO 1024]
                                    FileName:09_23*
</code></pre>
<p>attr_list
(set)
&ndash;
Column names to be returned in results.
Acts as 'fl' in query</p>
<p>params
(dict)
&ndash;
Any other params which needs to be passed
Example : { "start" : "0" }</p>
<h2 id="returns">Returns</h2>
<p>int,list(dict),dict
&ndash;
Containing document count, document details &amp; facet details(if any)</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if failed to perform search
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/file_storage_optimization.py#L455-L485" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def search(self, criteria=None, attr_list=None, params=None):
    &#34;&#34;&#34;do searches on data source and returns document details

        Args:

            criteria        (str)      --  containing criteria for query
                                                (Default : None - returns all docs)

                                                Example :

                                                    Size:[10 TO 1024]
                                                    FileName:09_23*

            attr_list       (set)      --  Column names to be returned in results.
                                                 Acts as &#39;fl&#39; in query

            params          (dict)     --  Any other params which needs to be passed
                                               Example : { &#34;start&#34; : &#34;0&#34; }

        Returns:

            int,list(dict),dict    --  Containing document count, document details &amp; facet details(if any)

        Raises:

            SDKException:

                    if failed to perform search

    &#34;&#34;&#34;
    return self._ediscovery_client_ops.search(criteria=criteria, attr_list=attr_list, params=params)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.file_storage_optimization.FsoServer.share"><code class="name flex">
<span>def <span class="ident">share</span></span>(<span>self, user_or_group_name, allow_edit_permission=False, is_user=True, ops_type=1)</span>
</code></dt>
<dd>
<div class="desc"><p>Shares Fso server with given user or user group in commcell</p>
<h2 id="args">Args</h2>
<p>user_or_group_name
(str)
&ndash;
Name of user or group</p>
<p>is_user
(bool)
&ndash;
Denotes whether this is user or group name
default : True(User)</p>
<p>allow_edit_permission
(bool)
&ndash;
whether to give edit permission or not to user or group</p>
<p>ops_type
(int)
&ndash;
Operation type</p>
<pre><code>                                    Default : 1 (Add)

                                    Supported : 1 (Add)
                                                3 (Delete)
</code></pre>
<h2 id="returns">Returns</h2>
<p>None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if unable to update security associations

    if response is empty or not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/file_storage_optimization.py#L528-L563" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def share(self, user_or_group_name, allow_edit_permission=False, is_user=True, ops_type=1):
    &#34;&#34;&#34;Shares Fso server with given user or user group in commcell

            Args:

                user_or_group_name      (str)       --  Name of user or group

                is_user                 (bool)      --  Denotes whether this is user or group name
                                                            default : True(User)

                allow_edit_permission   (bool)      --  whether to give edit permission or not to user or group

                ops_type                (int)       --  Operation type

                                                        Default : 1 (Add)

                                                        Supported : 1 (Add)
                                                                    3 (Delete)

            Returns:

                None

            Raises:

                SDKException:

                        if unable to update security associations

                        if response is empty or not success
    &#34;&#34;&#34;
    return self._ediscovery_client_ops.share(
        user_or_group_name=user_or_group_name,
        allow_edit_permission=allow_edit_permission,
        is_user=is_user,
        ops_type=ops_type)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.file_storage_optimization.FsoServer.start_collection"><code class="name flex">
<span>def <span class="ident">start_collection</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Starts collection job on all data sources associated with this server</p>
<h2 id="args">Args</h2>
<p>None</p>
<h2 id="return">Return</h2>
<p>list
&ndash;
List of jobid's</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if failed to start collection job
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/file_storage_optimization.py#L375-L407" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def start_collection(self):
    &#34;&#34;&#34;Starts collection job on all data sources associated with this server

            Args:

                None

            Return:

                list    --  List of jobid&#39;s

            Raises:

                SDKException:

                        if failed to start collection job

    &#34;&#34;&#34;
    request_json = copy.deepcopy(EdiscoveryConstants.START_CRAWL_SERVER_REQUEST_JSON)
    request_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;clientId&#39;] = self._server_id
    request_json[&#39;taskInfo&#39;][&#39;task&#39;][
        &#39;taskName&#39;] = f&#34;Cvpysdk_FSO_server_Crawl_{self._server_name}_{int(time.time())}&#34;
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, self._CREATE_POLICY, request_json
    )
    output = []
    if flag:
        if response.json() and &#39;jobIds&#39; in response.json():
            for node in response.json()[&#39;jobIds&#39;]:
                output.append(node)
            return output
        raise SDKException(&#39;FileStorageOptimization&#39;, &#39;105&#39;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.activateapps.file_storage_optimization.FsoServerGroup"><code class="flex name class">
<span>class <span class="ident">FsoServerGroup</span></span>
<span>(</span><span>commcell_object, server_group_name, server_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class to represent single FSO Server group in the commcell</p>
<p>Initializes an instance of the FsoServerGroup class.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the commcell class</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the FsoServerGroup class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/file_storage_optimization.py#L800-L1039" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class FsoServerGroup():
    &#34;&#34;&#34;Class to represent single FSO Server group in the commcell&#34;&#34;&#34;

    def __init__(self, commcell_object, server_group_name, server_id=None):
        &#34;&#34;&#34;Initializes an instance of the FsoServerGroup class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

            Returns:
                object  -   instance of the FsoServerGroup class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._server_group_name = server_group_name
        self._server_id = None
        self._server_grp_props = None
        self._server_names = []
        self._total_doc = 0
        self._CREATE_POLICY = self._services[&#39;CREATE_UPDATE_SCHEDULE_POLICY&#39;]
        if server_id:
            self._server_id = server_id
        else:
            self._server_id = self._commcell_object.activate.file_storage_optimization(
                FsoTypes.SERVER_GROUPS).get(server_group_name).server_group_id
        self._ediscovery_clients_obj = EdiscoveryClients(self._commcell_object, self)
        self._ediscovery_client_ops = EdiscoveryClientOperations(self._commcell_object, self)
        self.refresh()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _get_server_group_properties(self):
        &#34;&#34;&#34;gets FSO server group details from the commcell

                Args:

                    None

                Returns:

                    dict    --  Containing FSO Server group details

                Raises:

                     Raises;

                        SDKException:

                            if failed to get server group details

        &#34;&#34;&#34;
        client_resp = self._ediscovery_clients_obj.get_ediscovery_clients()
        grp_resp = self._ediscovery_clients_obj.get_ediscovery_client_group_details()
        if &#39;nodeList&#39; in grp_resp:
            grp_resp = grp_resp[&#39;nodeList&#39;][0]
            if &#39;childs&#39; in grp_resp and &#39;customProperties&#39; in grp_resp[&#39;childs&#39;][0]:
                name_value_dict = grp_resp[&#39;childs&#39;][0][&#39;customProperties&#39;][&#39;nameValues&#39;]
                for prop in name_value_dict:
                    prop_name = prop.get(&#39;name&#39;)
                    if prop_name == EdiscoveryConstants.FIELD_DOCUMENT_COUNT:
                        self._total_doc = int(prop.get(&#39;value&#39;))
                        break
        self._server_names = []
        for key, value in client_resp.items():
            self._server_names.append(key)
        return client_resp

    def has_server(self, server_name):
        &#34;&#34;&#34;Checks if a server exists in the FSO Server group with the input name or not

            Args:
                server_name (str)  --  name of the server

            Returns:
                bool - boolean output whether the FSO Server exists in the server group or not

            Raises:
                SDKException:
                    if type of the server name argument is not string

        &#34;&#34;&#34;
        if not isinstance(server_name, str):
            raise SDKException(&#39;FileStorageOptimization&#39;, &#39;101&#39;)
        return self._server_grp_props and server_name.lower() in self._server_grp_props

    def start_collection(self):
        &#34;&#34;&#34;Starts collection job on all servers associated with this server group

                Args:

                    None

                Return:

                    list    --  List of jobid&#39;s

                Raises:

                    SDKException:

                            if failed to start collection job

        &#34;&#34;&#34;
        request_json = copy.deepcopy(EdiscoveryConstants.START_CRAWL_SERVER_REQUEST_JSON)
        # delete clientid key and add client group level key and entity type
        del request_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;clientId&#39;]
        request_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;clientGroupId&#39;] = self._server_id
        request_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;_type_&#39;] = 28  # server group level job collection
        request_json[&#39;taskInfo&#39;][&#39;task&#39;][
            &#39;taskName&#39;] = f&#34;Cvpysdk_FSO_server_Crawl_{self._server_group_name}_{int(time.time())}&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._CREATE_POLICY, request_json
        )
        output = []
        if flag:
            if response.json() and &#39;jobIds&#39; in response.json():
                for node in response.json()[&#39;jobIds&#39;]:
                    output.append(node)
                return output
            raise SDKException(&#39;FileStorageOptimization&#39;, &#39;107&#39;)
        self._response_not_success(response)

    def search(self, criteria=None, attr_list=None, params=None):
        &#34;&#34;&#34;do searches on client group data and returns document details

            Args:

                criteria        (str)      --  containing criteria for query
                                                    (Default : None - returns all docs)

                                                    Example :

                                                        Size:[10 TO 1024]
                                                        FileName:09_23*

                attr_list       (set)      --  Column names to be returned in results.
                                                     Acts as &#39;fl&#39; in query

                params          (dict)     --  Any other params which needs to be passed
                                                   Example : { &#34;start&#34; : &#34;0&#34; }

            Returns:

                int,list(dict),dict    --  Containing document count, document details &amp; facet details(if any)

            Raises:

                SDKException:

                        if failed to perform search

        &#34;&#34;&#34;
        return self._ediscovery_client_ops.search(criteria=criteria, attr_list=attr_list, params=params)

    def get(self, server_name):
        &#34;&#34;&#34;returns the FsoServer object for given server name

                Args:

                    server_name         (str)       --  Name of the server

                Returns:

                    obj --  Instance of FsoServer Class

                Raises:

                    SDKException:

                            if failed to find server in FSO server group

                            if input is not valid

        &#34;&#34;&#34;
        if not isinstance(server_name, str):
            raise SDKException(&#39;FileStorageOptimization&#39;, &#39;101&#39;)
        if not self.has_server(server_name):
            raise SDKException(&#39;FileStorageOptimization&#39;, &#39;103&#39;)
        server_id = self._server_grp_props[server_name.lower()][&#39;clientEntity&#39;][&#39;clientId&#39;]
        return FsoServer(commcell_object=self._commcell_object, server_name=server_name, server_id=server_id)

    def refresh(self):
        &#34;&#34;&#34;Refresh the FSO Server group details&#34;&#34;&#34;
        self._server_grp_props = self._get_server_group_properties()

    @property
    def server_group_id(self):
        &#34;&#34;&#34;returns the client group id

            Returns:

                int --  Server group id

        &#34;&#34;&#34;
        return self._server_id

    @property
    def server_group_props(self):
        &#34;&#34;&#34;returns the server group properties

            Returns:

                dict --  Server group details

        &#34;&#34;&#34;
        return self._server_grp_props

    @property
    def server_list(self):
        &#34;&#34;&#34;returns the list of server which is part of this server group

            Returns:

                list --  Server names

        &#34;&#34;&#34;
        return self._server_names

    @property
    def total_documents(self):
        &#34;&#34;&#34;returns the total documents count for this server group

            Returns:

                int --  Total document count for this server group

        &#34;&#34;&#34;
        return self._total_doc</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.activateapps.file_storage_optimization.FsoServerGroup.server_group_id"><code class="name">var <span class="ident">server_group_id</span></code></dt>
<dd>
<div class="desc"><p>returns the client group id</p>
<h2 id="returns">Returns</h2>
<p>int &ndash;
Server group id</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/file_storage_optimization.py#L997-L1006" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def server_group_id(self):
    &#34;&#34;&#34;returns the client group id

        Returns:

            int --  Server group id

    &#34;&#34;&#34;
    return self._server_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.file_storage_optimization.FsoServerGroup.server_group_props"><code class="name">var <span class="ident">server_group_props</span></code></dt>
<dd>
<div class="desc"><p>returns the server group properties</p>
<h2 id="returns">Returns</h2>
<p>dict &ndash;
Server group details</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/file_storage_optimization.py#L1008-L1017" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def server_group_props(self):
    &#34;&#34;&#34;returns the server group properties

        Returns:

            dict --  Server group details

    &#34;&#34;&#34;
    return self._server_grp_props</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.file_storage_optimization.FsoServerGroup.server_list"><code class="name">var <span class="ident">server_list</span></code></dt>
<dd>
<div class="desc"><p>returns the list of server which is part of this server group</p>
<h2 id="returns">Returns</h2>
<p>list &ndash;
Server names</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/file_storage_optimization.py#L1019-L1028" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def server_list(self):
    &#34;&#34;&#34;returns the list of server which is part of this server group

        Returns:

            list --  Server names

    &#34;&#34;&#34;
    return self._server_names</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.file_storage_optimization.FsoServerGroup.total_documents"><code class="name">var <span class="ident">total_documents</span></code></dt>
<dd>
<div class="desc"><p>returns the total documents count for this server group</p>
<h2 id="returns">Returns</h2>
<p>int &ndash;
Total document count for this server group</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/file_storage_optimization.py#L1030-L1039" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def total_documents(self):
    &#34;&#34;&#34;returns the total documents count for this server group

        Returns:

            int --  Total document count for this server group

    &#34;&#34;&#34;
    return self._total_doc</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.activateapps.file_storage_optimization.FsoServerGroup.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, server_name)</span>
</code></dt>
<dd>
<div class="desc"><p>returns the FsoServer object for given server name</p>
<h2 id="args">Args</h2>
<p>server_name
(str)
&ndash;
Name of the server</p>
<h2 id="returns">Returns</h2>
<p>obj &ndash;
Instance of FsoServer Class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if failed to find server in FSO server group

    if input is not valid
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/file_storage_optimization.py#L966-L991" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, server_name):
    &#34;&#34;&#34;returns the FsoServer object for given server name

            Args:

                server_name         (str)       --  Name of the server

            Returns:

                obj --  Instance of FsoServer Class

            Raises:

                SDKException:

                        if failed to find server in FSO server group

                        if input is not valid

    &#34;&#34;&#34;
    if not isinstance(server_name, str):
        raise SDKException(&#39;FileStorageOptimization&#39;, &#39;101&#39;)
    if not self.has_server(server_name):
        raise SDKException(&#39;FileStorageOptimization&#39;, &#39;103&#39;)
    server_id = self._server_grp_props[server_name.lower()][&#39;clientEntity&#39;][&#39;clientId&#39;]
    return FsoServer(commcell_object=self._commcell_object, server_name=server_name, server_id=server_id)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.file_storage_optimization.FsoServerGroup.has_server"><code class="name flex">
<span>def <span class="ident">has_server</span></span>(<span>self, server_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks if a server exists in the FSO Server group with the input name or not</p>
<h2 id="args">Args</h2>
<p>server_name (str)
&ndash;
name of the server</p>
<h2 id="returns">Returns</h2>
<p>bool - boolean output whether the FSO Server exists in the server group or not</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the server name argument is not string</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/file_storage_optimization.py#L879-L895" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_server(self, server_name):
    &#34;&#34;&#34;Checks if a server exists in the FSO Server group with the input name or not

        Args:
            server_name (str)  --  name of the server

        Returns:
            bool - boolean output whether the FSO Server exists in the server group or not

        Raises:
            SDKException:
                if type of the server name argument is not string

    &#34;&#34;&#34;
    if not isinstance(server_name, str):
        raise SDKException(&#39;FileStorageOptimization&#39;, &#39;101&#39;)
    return self._server_grp_props and server_name.lower() in self._server_grp_props</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.file_storage_optimization.FsoServerGroup.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the FSO Server group details</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/file_storage_optimization.py#L993-L995" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the FSO Server group details&#34;&#34;&#34;
    self._server_grp_props = self._get_server_group_properties()</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.file_storage_optimization.FsoServerGroup.search"><code class="name flex">
<span>def <span class="ident">search</span></span>(<span>self, criteria=None, attr_list=None, params=None)</span>
</code></dt>
<dd>
<div class="desc"><p>do searches on client group data and returns document details</p>
<h2 id="args">Args</h2>
<p>criteria
(str)
&ndash;
containing criteria for query
(Default : None - returns all docs)</p>
<pre><code>                                Example :

                                    Size:[10 TO 1024]
                                    FileName:09_23*
</code></pre>
<p>attr_list
(set)
&ndash;
Column names to be returned in results.
Acts as 'fl' in query</p>
<p>params
(dict)
&ndash;
Any other params which needs to be passed
Example : { "start" : "0" }</p>
<h2 id="returns">Returns</h2>
<p>int,list(dict),dict
&ndash;
Containing document count, document details &amp; facet details(if any)</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if failed to perform search
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/file_storage_optimization.py#L934-L964" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def search(self, criteria=None, attr_list=None, params=None):
    &#34;&#34;&#34;do searches on client group data and returns document details

        Args:

            criteria        (str)      --  containing criteria for query
                                                (Default : None - returns all docs)

                                                Example :

                                                    Size:[10 TO 1024]
                                                    FileName:09_23*

            attr_list       (set)      --  Column names to be returned in results.
                                                 Acts as &#39;fl&#39; in query

            params          (dict)     --  Any other params which needs to be passed
                                               Example : { &#34;start&#34; : &#34;0&#34; }

        Returns:

            int,list(dict),dict    --  Containing document count, document details &amp; facet details(if any)

        Raises:

            SDKException:

                    if failed to perform search

    &#34;&#34;&#34;
    return self._ediscovery_client_ops.search(criteria=criteria, attr_list=attr_list, params=params)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.file_storage_optimization.FsoServerGroup.start_collection"><code class="name flex">
<span>def <span class="ident">start_collection</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Starts collection job on all servers associated with this server group</p>
<h2 id="args">Args</h2>
<p>None</p>
<h2 id="return">Return</h2>
<p>list
&ndash;
List of jobid's</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if failed to start collection job
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/file_storage_optimization.py#L897-L932" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def start_collection(self):
    &#34;&#34;&#34;Starts collection job on all servers associated with this server group

            Args:

                None

            Return:

                list    --  List of jobid&#39;s

            Raises:

                SDKException:

                        if failed to start collection job

    &#34;&#34;&#34;
    request_json = copy.deepcopy(EdiscoveryConstants.START_CRAWL_SERVER_REQUEST_JSON)
    # delete clientid key and add client group level key and entity type
    del request_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;clientId&#39;]
    request_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;clientGroupId&#39;] = self._server_id
    request_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;_type_&#39;] = 28  # server group level job collection
    request_json[&#39;taskInfo&#39;][&#39;task&#39;][
        &#39;taskName&#39;] = f&#34;Cvpysdk_FSO_server_Crawl_{self._server_group_name}_{int(time.time())}&#34;
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, self._CREATE_POLICY, request_json
    )
    output = []
    if flag:
        if response.json() and &#39;jobIds&#39; in response.json():
            for node in response.json()[&#39;jobIds&#39;]:
                output.append(node)
            return output
        raise SDKException(&#39;FileStorageOptimization&#39;, &#39;107&#39;)
    self._response_not_success(response)</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.activateapps.file_storage_optimization.FsoServerGroups"><code class="flex name class">
<span>class <span class="ident">FsoServerGroups</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing all FSO server groups in the commcell.</p>
<p>Initializes an instance of the FsoServerGroups class.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the commcell class</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the FsoServerGroups class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/file_storage_optimization.py#L650-L797" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class FsoServerGroups():
    &#34;&#34;&#34;Class for representing all FSO server groups in the commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initializes an instance of the FsoServerGroups class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

            Returns:
                object  -   instance of the FsoServerGroups class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._fso_server_groups = None
        self._ediscovery_clients_obj = EdiscoveryClients(self._commcell_object, self)
        self._ediscovery_ds_obj = EdiscoveryDataSources(self._commcell_object, self)
        self.refresh()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _get_all_fso_server_groups(self):
        &#34;&#34;&#34;Returns all the FSO server groups found in the commcell

                Args:

                    None

                Returns:

                    dict        --  Containing FSO server group details

                Raises;

                    SDKException:

                            if failed to get FSO server group details

                            if response is empty

                            if response is not success
        &#34;&#34;&#34;
        return self._ediscovery_clients_obj.get_ediscovery_clients()

    def refresh(self):
        &#34;&#34;&#34;Refresh the FSO Server groups associated with the commcell.&#34;&#34;&#34;
        self._fso_server_groups = self._get_all_fso_server_groups()

    def add_server_group(self, server_group_name, inventory_name, plan_name, **kwargs):
        &#34;&#34;&#34;adds server group to FSO

                Args:

                    server_group_name       (str)       --      Server group name

                    inventory_name          (str)       --  Inventory name which needs to be associated

                    plan_name               (str)       --  Plan name which needs to be associated with this data source

                 Kwargs Arguments:

                    country_name        (str)       --  country name where server is located (default: USA)

                    country_code        (str)       --  Country code (ISO 3166 2-letter code)

                Returns:

                    obj     --  Instance of FSOServerGroup class

                Raises:

                      SDKException:

                            if plan/inventory/index server doesn&#39;t exists

                            if failed to add FSO server group

        &#34;&#34;&#34;
        self._ediscovery_ds_obj.add_fs_data_source(
            server_name=server_group_name,
            data_source_name=server_group_name,
            inventory_name=inventory_name,
            plan_name=plan_name,
            **kwargs)
        return FsoServerGroup(
            self._commcell_object,
            server_group_name,
            server_id=self._commcell_object.client_groups.get(server_group_name).clientgroup_id)

    def has(self, server_group_name):
        &#34;&#34;&#34;Checks if a server group exists in the commcell with the input name for FSO or not

            Args:
                server_group_name (str)  --  name of the server group

            Returns:
                bool - boolean output whether the FSO Server group exists in the commcell or not

            Raises:
                SDKException:
                    if type of the server group name argument is not string

        &#34;&#34;&#34;
        if not isinstance(server_group_name, str):
            raise SDKException(&#39;FileStorageOptimization&#39;, &#39;101&#39;)
        return self._fso_server_groups and server_group_name.lower() in self._fso_server_groups

    def get(self, server_grp_name):
        &#34;&#34;&#34;returns the FsoServerGroup object for given server group name

                Args:

                    server_grp_name         (str)       --  Name of the server group

                Returns:

                    obj --  Instance of FsoServerGroup Class

                Raises:

                    SDKException:

                            if failed to find server group in FSO App

                            if input is not valid

        &#34;&#34;&#34;
        if not isinstance(server_grp_name, str):
            raise SDKException(&#39;FileStorageOptimization&#39;, &#39;101&#39;)
        if not self.has(server_grp_name):
            raise SDKException(&#39;FileStorageOptimization&#39;, &#39;106&#39;)
        server_id = self._fso_server_groups[server_grp_name.lower()][&#39;clientEntity&#39;][&#39;clientId&#39;]
        return FsoServerGroup(
            commcell_object=self._commcell_object,
            server_group_name=server_grp_name,
            server_id=server_id)</code></pre>
</details>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.activateapps.file_storage_optimization.FsoServerGroups.add_server_group"><code class="name flex">
<span>def <span class="ident">add_server_group</span></span>(<span>self, server_group_name, inventory_name, plan_name, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>adds server group to FSO</p>
<h2 id="args">Args</h2>
<p>server_group_name
(str)
&ndash;
Server group name</p>
<p>inventory_name
(str)
&ndash;
Inventory name which needs to be associated</p>
<p>plan_name
(str)
&ndash;
Plan name which needs to be associated with this data source
Kwargs Arguments:</p>
<pre><code>country_name        (str)       --  country name where server is located (default: USA)

country_code        (str)       --  Country code (ISO 3166 2-letter code)
</code></pre>
<h2 id="returns">Returns</h2>
<p>obj
&ndash;
Instance of FSOServerGroup class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>  if plan/inventory/index server doesn't exists

  if failed to add FSO server group
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/file_storage_optimization.py#L710-L749" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_server_group(self, server_group_name, inventory_name, plan_name, **kwargs):
    &#34;&#34;&#34;adds server group to FSO

            Args:

                server_group_name       (str)       --      Server group name

                inventory_name          (str)       --  Inventory name which needs to be associated

                plan_name               (str)       --  Plan name which needs to be associated with this data source

             Kwargs Arguments:

                country_name        (str)       --  country name where server is located (default: USA)

                country_code        (str)       --  Country code (ISO 3166 2-letter code)

            Returns:

                obj     --  Instance of FSOServerGroup class

            Raises:

                  SDKException:

                        if plan/inventory/index server doesn&#39;t exists

                        if failed to add FSO server group

    &#34;&#34;&#34;
    self._ediscovery_ds_obj.add_fs_data_source(
        server_name=server_group_name,
        data_source_name=server_group_name,
        inventory_name=inventory_name,
        plan_name=plan_name,
        **kwargs)
    return FsoServerGroup(
        self._commcell_object,
        server_group_name,
        server_id=self._commcell_object.client_groups.get(server_group_name).clientgroup_id)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.file_storage_optimization.FsoServerGroups.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, server_grp_name)</span>
</code></dt>
<dd>
<div class="desc"><p>returns the FsoServerGroup object for given server group name</p>
<h2 id="args">Args</h2>
<p>server_grp_name
(str)
&ndash;
Name of the server group</p>
<h2 id="returns">Returns</h2>
<p>obj &ndash;
Instance of FsoServerGroup Class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if failed to find server group in FSO App

    if input is not valid
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/file_storage_optimization.py#L769-L797" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, server_grp_name):
    &#34;&#34;&#34;returns the FsoServerGroup object for given server group name

            Args:

                server_grp_name         (str)       --  Name of the server group

            Returns:

                obj --  Instance of FsoServerGroup Class

            Raises:

                SDKException:

                        if failed to find server group in FSO App

                        if input is not valid

    &#34;&#34;&#34;
    if not isinstance(server_grp_name, str):
        raise SDKException(&#39;FileStorageOptimization&#39;, &#39;101&#39;)
    if not self.has(server_grp_name):
        raise SDKException(&#39;FileStorageOptimization&#39;, &#39;106&#39;)
    server_id = self._fso_server_groups[server_grp_name.lower()][&#39;clientEntity&#39;][&#39;clientId&#39;]
    return FsoServerGroup(
        commcell_object=self._commcell_object,
        server_group_name=server_grp_name,
        server_id=server_id)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.file_storage_optimization.FsoServerGroups.has"><code class="name flex">
<span>def <span class="ident">has</span></span>(<span>self, server_group_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks if a server group exists in the commcell with the input name for FSO or not</p>
<h2 id="args">Args</h2>
<p>server_group_name (str)
&ndash;
name of the server group</p>
<h2 id="returns">Returns</h2>
<p>bool - boolean output whether the FSO Server group exists in the commcell or not</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the server group name argument is not string</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/file_storage_optimization.py#L751-L767" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has(self, server_group_name):
    &#34;&#34;&#34;Checks if a server group exists in the commcell with the input name for FSO or not

        Args:
            server_group_name (str)  --  name of the server group

        Returns:
            bool - boolean output whether the FSO Server group exists in the commcell or not

        Raises:
            SDKException:
                if type of the server group name argument is not string

    &#34;&#34;&#34;
    if not isinstance(server_group_name, str):
        raise SDKException(&#39;FileStorageOptimization&#39;, &#39;101&#39;)
    return self._fso_server_groups and server_group_name.lower() in self._fso_server_groups</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.file_storage_optimization.FsoServerGroups.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the FSO Server groups associated with the commcell.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/file_storage_optimization.py#L706-L708" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the FSO Server groups associated with the commcell.&#34;&#34;&#34;
    self._fso_server_groups = self._get_all_fso_server_groups()</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.activateapps.file_storage_optimization.FsoServers"><code class="flex name class">
<span>class <span class="ident">FsoServers</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing all FSO servers in the commcell.</p>
<p>Initializes an instance of the FsoServers class.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the commcell class</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the FsoServers class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/file_storage_optimization.py#L154-L327" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class FsoServers():
    &#34;&#34;&#34;Class for representing all FSO servers in the commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initializes an instance of the FsoServers class.

            Args:
                commcell_object     (object)    --  instance of the commcell class

            Returns:
                object  -   instance of the FsoServers class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._update_response_ = commcell_object._update_response_
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._fso_servers = None
        self._ediscovery_clients_obj = EdiscoveryClients(self._commcell_object, self)
        self._ediscovery_ds_obj = EdiscoveryDataSources(self._commcell_object, self)
        self.refresh()

    def _response_not_success(self, response):
        &#34;&#34;&#34;Helper function to raise an exception when reponse status is not 200 (OK).

            Args:
                response    (object)    --  response class object,

                received upon running an API request, using the `requests` python package

        &#34;&#34;&#34;
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def get(self, server_name):
        &#34;&#34;&#34;returns the FsoServer object for given server name

                Args:

                    server_name         (str)       --  Name of the server

                Returns:

                    obj --  Instance of FsoServer Class

                Raises:

                    SDKException:

                            if failed to find server in FSO App

                            if input is not valid

        &#34;&#34;&#34;
        if not isinstance(server_name, str):
            raise SDKException(&#39;FileStorageOptimization&#39;, &#39;101&#39;)
        if not self.has_server(server_name):
            raise SDKException(&#39;FileStorageOptimization&#39;, &#39;103&#39;)
        server_id = self._fso_servers[server_name.lower()][&#39;clientEntity&#39;][&#39;clientId&#39;]
        return FsoServer(commcell_object=self._commcell_object, server_name=server_name, server_id=server_id)

    def _get_all_fso_servers(self):
        &#34;&#34;&#34;Returns all the FSO servers found in the commcell

                Args:

                    None

                Returns:

                    dict        --  Containing FSO server details

                Raises;

                    SDKException:

                            if failed to get FSO servers details

                            if response is empty

                            if response is not success
        &#34;&#34;&#34;
        return self._ediscovery_clients_obj.get_ediscovery_clients()

    def refresh(self):
        &#34;&#34;&#34;Refresh the FSO Servers associated with the commcell.&#34;&#34;&#34;
        self._fso_servers = self._get_all_fso_servers()

    def add_file_server(self, server_name, data_source_name, inventory_name, plan_name,
                        source_type=EdiscoveryConstants.SourceType.BACKUP, **kwargs):
        &#34;&#34;&#34;Adds file system FSO server

                Args:

                    server_name         (str)       --  Server name which needs to be added

                    data_source_name    (str)       --  Name for data source

                    inventory_name      (str)       --  Inventory name which needs to be associated

                    plan_name           (str)       --  Plan name which needs to be associated with this data source

                    source_type         (enum)      --  Source type for crawl (Live source or Backedup)
                                                                Refer EdiscoveryConstants.SourceType

                Kwargs Arguments:

                    scan_type           (str)       --  Specifies scan type when source type is for backed up data
                                                                Supported values : quick | full

                    crawl_path          (list)      --  File path which needs to be crawl if source type is Live source

                    access_node         (str)       --  server name which needs to be used as access node
                                                            in case if server to be added is not a commvault client

                    country_name        (str)       --  country name where server is located (default : USA)

                    country_code        (str)       --  Country code (ISO 3166 2-letter code)

                    user_name           (str)       --  User name who has access to UNC path

                    password            (str)       --  base64 encoded password to access unc path

                    enable_monitoring   (str)       --  specifies whether to enable file monitoring or not for this


                Returns:

                    obj     --  Instance of FSOServer class

                Raises:

                      SDKException:

                            if plan/inventory/index server doesn&#39;t exists

                            if failed to add FSO server data source
        &#34;&#34;&#34;
        self._ediscovery_ds_obj.add_fs_data_source(
            server_name=server_name,
            data_source_name=data_source_name,
            inventory_name=inventory_name,
            plan_name=plan_name,
            source_type=source_type,
            **kwargs)
        is_commvault_client = self._commcell_object.clients.has_client(server_name)
        server_id = 0
        if not is_commvault_client:
            all_clients = self._commcell_object.clients.all_clients
            for client_name, client_details in all_clients.items():
                if client_name.lower().startswith(f&#34;{data_source_name.lower()}_&#34;):
                    server_id = client_details[&#39;id&#39;]
                    break
        else:
            server_id = self._commcell_object.clients.get(server_name).client_id
        return FsoServer(commcell_object=self._commcell_object, server_name=server_name,
                         server_id=server_id)

    def has_server(self, server_name):
        &#34;&#34;&#34;Checks if a server exists in the commcell with the input name for FSO or not

            Args:
                server_name (str)  --  name of the server

            Returns:
                bool - boolean output whether the FSO Server exists in the commcell or not

            Raises:
                SDKException:
                    if type of the server name argument is not string

        &#34;&#34;&#34;
        if not isinstance(server_name, str):
            raise SDKException(&#39;FileStorageOptimization&#39;, &#39;101&#39;)
        return self._fso_servers and server_name.lower() in self._fso_servers</code></pre>
</details>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.activateapps.file_storage_optimization.FsoServers.add_file_server"><code class="name flex">
<span>def <span class="ident">add_file_server</span></span>(<span>self, server_name, data_source_name, inventory_name, plan_name, source_type=SourceType.BACKUP, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds file system FSO server</p>
<h2 id="args">Args</h2>
<p>server_name
(str)
&ndash;
Server name which needs to be added</p>
<p>data_source_name
(str)
&ndash;
Name for data source</p>
<p>inventory_name
(str)
&ndash;
Inventory name which needs to be associated</p>
<p>plan_name
(str)
&ndash;
Plan name which needs to be associated with this data source</p>
<p>source_type
(enum)
&ndash;
Source type for crawl (Live source or Backedup)
Refer EdiscoveryConstants.SourceType
Kwargs Arguments:</p>
<pre><code>scan_type           (str)       --  Specifies scan type when source type is for backed up data
                                            Supported values : quick | full

crawl_path          (list)      --  File path which needs to be crawl if source type is Live source

access_node         (str)       --  server name which needs to be used as access node
                                        in case if server to be added is not a commvault client

country_name        (str)       --  country name where server is located (default : USA)

country_code        (str)       --  Country code (ISO 3166 2-letter code)

user_name           (str)       --  User name who has access to UNC path

password            (str)       --  base64 encoded password to access unc path

enable_monitoring   (str)       --  specifies whether to enable file monitoring or not for this
</code></pre>
<h2 id="returns">Returns</h2>
<p>obj
&ndash;
Instance of FSOServer class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>  if plan/inventory/index server doesn't exists

  if failed to add FSO server data source
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/file_storage_optimization.py#L241-L309" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_file_server(self, server_name, data_source_name, inventory_name, plan_name,
                    source_type=EdiscoveryConstants.SourceType.BACKUP, **kwargs):
    &#34;&#34;&#34;Adds file system FSO server

            Args:

                server_name         (str)       --  Server name which needs to be added

                data_source_name    (str)       --  Name for data source

                inventory_name      (str)       --  Inventory name which needs to be associated

                plan_name           (str)       --  Plan name which needs to be associated with this data source

                source_type         (enum)      --  Source type for crawl (Live source or Backedup)
                                                            Refer EdiscoveryConstants.SourceType

            Kwargs Arguments:

                scan_type           (str)       --  Specifies scan type when source type is for backed up data
                                                            Supported values : quick | full

                crawl_path          (list)      --  File path which needs to be crawl if source type is Live source

                access_node         (str)       --  server name which needs to be used as access node
                                                        in case if server to be added is not a commvault client

                country_name        (str)       --  country name where server is located (default : USA)

                country_code        (str)       --  Country code (ISO 3166 2-letter code)

                user_name           (str)       --  User name who has access to UNC path

                password            (str)       --  base64 encoded password to access unc path

                enable_monitoring   (str)       --  specifies whether to enable file monitoring or not for this


            Returns:

                obj     --  Instance of FSOServer class

            Raises:

                  SDKException:

                        if plan/inventory/index server doesn&#39;t exists

                        if failed to add FSO server data source
    &#34;&#34;&#34;
    self._ediscovery_ds_obj.add_fs_data_source(
        server_name=server_name,
        data_source_name=data_source_name,
        inventory_name=inventory_name,
        plan_name=plan_name,
        source_type=source_type,
        **kwargs)
    is_commvault_client = self._commcell_object.clients.has_client(server_name)
    server_id = 0
    if not is_commvault_client:
        all_clients = self._commcell_object.clients.all_clients
        for client_name, client_details in all_clients.items():
            if client_name.lower().startswith(f&#34;{data_source_name.lower()}_&#34;):
                server_id = client_details[&#39;id&#39;]
                break
    else:
        server_id = self._commcell_object.clients.get(server_name).client_id
    return FsoServer(commcell_object=self._commcell_object, server_name=server_name,
                     server_id=server_id)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.file_storage_optimization.FsoServers.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, server_name)</span>
</code></dt>
<dd>
<div class="desc"><p>returns the FsoServer object for given server name</p>
<h2 id="args">Args</h2>
<p>server_name
(str)
&ndash;
Name of the server</p>
<h2 id="returns">Returns</h2>
<p>obj &ndash;
Instance of FsoServer Class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if failed to find server in FSO App

    if input is not valid
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/file_storage_optimization.py#L187-L212" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, server_name):
    &#34;&#34;&#34;returns the FsoServer object for given server name

            Args:

                server_name         (str)       --  Name of the server

            Returns:

                obj --  Instance of FsoServer Class

            Raises:

                SDKException:

                        if failed to find server in FSO App

                        if input is not valid

    &#34;&#34;&#34;
    if not isinstance(server_name, str):
        raise SDKException(&#39;FileStorageOptimization&#39;, &#39;101&#39;)
    if not self.has_server(server_name):
        raise SDKException(&#39;FileStorageOptimization&#39;, &#39;103&#39;)
    server_id = self._fso_servers[server_name.lower()][&#39;clientEntity&#39;][&#39;clientId&#39;]
    return FsoServer(commcell_object=self._commcell_object, server_name=server_name, server_id=server_id)</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.file_storage_optimization.FsoServers.has_server"><code class="name flex">
<span>def <span class="ident">has_server</span></span>(<span>self, server_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks if a server exists in the commcell with the input name for FSO or not</p>
<h2 id="args">Args</h2>
<p>server_name (str)
&ndash;
name of the server</p>
<h2 id="returns">Returns</h2>
<p>bool - boolean output whether the FSO Server exists in the commcell or not</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the server name argument is not string</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/file_storage_optimization.py#L311-L327" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_server(self, server_name):
    &#34;&#34;&#34;Checks if a server exists in the commcell with the input name for FSO or not

        Args:
            server_name (str)  --  name of the server

        Returns:
            bool - boolean output whether the FSO Server exists in the commcell or not

        Raises:
            SDKException:
                if type of the server name argument is not string

    &#34;&#34;&#34;
    if not isinstance(server_name, str):
        raise SDKException(&#39;FileStorageOptimization&#39;, &#39;101&#39;)
    return self._fso_servers and server_name.lower() in self._fso_servers</code></pre>
</details>
</dd>
<dt id="cvpysdk.activateapps.file_storage_optimization.FsoServers.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the FSO Servers associated with the commcell.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/file_storage_optimization.py#L237-L239" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the FSO Servers associated with the commcell.&#34;&#34;&#34;
    self._fso_servers = self._get_all_fso_servers()</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.activateapps.file_storage_optimization.FsoTypes"><code class="flex name class">
<span>class <span class="ident">FsoTypes</span></span>
<span>(</span><span>value, names=None, *, module=None, qualname=None, type=None, start=1)</span>
</code></dt>
<dd>
<div class="desc"><p>Class to represent different FSO types(Server/ServerGroup/Project)</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/activateapps/file_storage_optimization.py#L147-L151" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class FsoTypes(Enum):
    &#34;&#34;&#34;Class to represent different FSO types(Server/ServerGroup/Project)&#34;&#34;&#34;
    SERVERS = 0
    SERVER_GROUPS = 1
    PROJECTS = 2</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li>enum.Enum</li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.activateapps.file_storage_optimization.FsoTypes.PROJECTS"><code class="name">var <span class="ident">PROJECTS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.file_storage_optimization.FsoTypes.SERVERS"><code class="name">var <span class="ident">SERVERS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.activateapps.file_storage_optimization.FsoTypes.SERVER_GROUPS"><code class="name">var <span class="ident">SERVER_GROUPS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#fsoserver-attributes">FsoServer Attributes</a></li>
<li><a href="#fsoservergroup-attributes">FsoServerGroup Attributes:</a></li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.activateapps" href="index.html">cvpysdk.activateapps</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.activateapps.file_storage_optimization.FsoServer" href="#cvpysdk.activateapps.file_storage_optimization.FsoServer">FsoServer</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.activateapps.file_storage_optimization.FsoServer.add_schedule" href="#cvpysdk.activateapps.file_storage_optimization.FsoServer.add_schedule">add_schedule</a></code></li>
<li><code><a title="cvpysdk.activateapps.file_storage_optimization.FsoServer.data_sources" href="#cvpysdk.activateapps.file_storage_optimization.FsoServer.data_sources">data_sources</a></code></li>
<li><code><a title="cvpysdk.activateapps.file_storage_optimization.FsoServer.data_sources_name" href="#cvpysdk.activateapps.file_storage_optimization.FsoServer.data_sources_name">data_sources_name</a></code></li>
<li><code><a title="cvpysdk.activateapps.file_storage_optimization.FsoServer.delete_schedule" href="#cvpysdk.activateapps.file_storage_optimization.FsoServer.delete_schedule">delete_schedule</a></code></li>
<li><code><a title="cvpysdk.activateapps.file_storage_optimization.FsoServer.refresh" href="#cvpysdk.activateapps.file_storage_optimization.FsoServer.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.activateapps.file_storage_optimization.FsoServer.schedule" href="#cvpysdk.activateapps.file_storage_optimization.FsoServer.schedule">schedule</a></code></li>
<li><code><a title="cvpysdk.activateapps.file_storage_optimization.FsoServer.search" href="#cvpysdk.activateapps.file_storage_optimization.FsoServer.search">search</a></code></li>
<li><code><a title="cvpysdk.activateapps.file_storage_optimization.FsoServer.server_details" href="#cvpysdk.activateapps.file_storage_optimization.FsoServer.server_details">server_details</a></code></li>
<li><code><a title="cvpysdk.activateapps.file_storage_optimization.FsoServer.server_id" href="#cvpysdk.activateapps.file_storage_optimization.FsoServer.server_id">server_id</a></code></li>
<li><code><a title="cvpysdk.activateapps.file_storage_optimization.FsoServer.share" href="#cvpysdk.activateapps.file_storage_optimization.FsoServer.share">share</a></code></li>
<li><code><a title="cvpysdk.activateapps.file_storage_optimization.FsoServer.start_collection" href="#cvpysdk.activateapps.file_storage_optimization.FsoServer.start_collection">start_collection</a></code></li>
<li><code><a title="cvpysdk.activateapps.file_storage_optimization.FsoServer.total_data_sources" href="#cvpysdk.activateapps.file_storage_optimization.FsoServer.total_data_sources">total_data_sources</a></code></li>
<li><code><a title="cvpysdk.activateapps.file_storage_optimization.FsoServer.total_doc_count" href="#cvpysdk.activateapps.file_storage_optimization.FsoServer.total_doc_count">total_doc_count</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.activateapps.file_storage_optimization.FsoServerGroup" href="#cvpysdk.activateapps.file_storage_optimization.FsoServerGroup">FsoServerGroup</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.activateapps.file_storage_optimization.FsoServerGroup.get" href="#cvpysdk.activateapps.file_storage_optimization.FsoServerGroup.get">get</a></code></li>
<li><code><a title="cvpysdk.activateapps.file_storage_optimization.FsoServerGroup.has_server" href="#cvpysdk.activateapps.file_storage_optimization.FsoServerGroup.has_server">has_server</a></code></li>
<li><code><a title="cvpysdk.activateapps.file_storage_optimization.FsoServerGroup.refresh" href="#cvpysdk.activateapps.file_storage_optimization.FsoServerGroup.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.activateapps.file_storage_optimization.FsoServerGroup.search" href="#cvpysdk.activateapps.file_storage_optimization.FsoServerGroup.search">search</a></code></li>
<li><code><a title="cvpysdk.activateapps.file_storage_optimization.FsoServerGroup.server_group_id" href="#cvpysdk.activateapps.file_storage_optimization.FsoServerGroup.server_group_id">server_group_id</a></code></li>
<li><code><a title="cvpysdk.activateapps.file_storage_optimization.FsoServerGroup.server_group_props" href="#cvpysdk.activateapps.file_storage_optimization.FsoServerGroup.server_group_props">server_group_props</a></code></li>
<li><code><a title="cvpysdk.activateapps.file_storage_optimization.FsoServerGroup.server_list" href="#cvpysdk.activateapps.file_storage_optimization.FsoServerGroup.server_list">server_list</a></code></li>
<li><code><a title="cvpysdk.activateapps.file_storage_optimization.FsoServerGroup.start_collection" href="#cvpysdk.activateapps.file_storage_optimization.FsoServerGroup.start_collection">start_collection</a></code></li>
<li><code><a title="cvpysdk.activateapps.file_storage_optimization.FsoServerGroup.total_documents" href="#cvpysdk.activateapps.file_storage_optimization.FsoServerGroup.total_documents">total_documents</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.activateapps.file_storage_optimization.FsoServerGroups" href="#cvpysdk.activateapps.file_storage_optimization.FsoServerGroups">FsoServerGroups</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.activateapps.file_storage_optimization.FsoServerGroups.add_server_group" href="#cvpysdk.activateapps.file_storage_optimization.FsoServerGroups.add_server_group">add_server_group</a></code></li>
<li><code><a title="cvpysdk.activateapps.file_storage_optimization.FsoServerGroups.get" href="#cvpysdk.activateapps.file_storage_optimization.FsoServerGroups.get">get</a></code></li>
<li><code><a title="cvpysdk.activateapps.file_storage_optimization.FsoServerGroups.has" href="#cvpysdk.activateapps.file_storage_optimization.FsoServerGroups.has">has</a></code></li>
<li><code><a title="cvpysdk.activateapps.file_storage_optimization.FsoServerGroups.refresh" href="#cvpysdk.activateapps.file_storage_optimization.FsoServerGroups.refresh">refresh</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.activateapps.file_storage_optimization.FsoServers" href="#cvpysdk.activateapps.file_storage_optimization.FsoServers">FsoServers</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.activateapps.file_storage_optimization.FsoServers.add_file_server" href="#cvpysdk.activateapps.file_storage_optimization.FsoServers.add_file_server">add_file_server</a></code></li>
<li><code><a title="cvpysdk.activateapps.file_storage_optimization.FsoServers.get" href="#cvpysdk.activateapps.file_storage_optimization.FsoServers.get">get</a></code></li>
<li><code><a title="cvpysdk.activateapps.file_storage_optimization.FsoServers.has_server" href="#cvpysdk.activateapps.file_storage_optimization.FsoServers.has_server">has_server</a></code></li>
<li><code><a title="cvpysdk.activateapps.file_storage_optimization.FsoServers.refresh" href="#cvpysdk.activateapps.file_storage_optimization.FsoServers.refresh">refresh</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.activateapps.file_storage_optimization.FsoTypes" href="#cvpysdk.activateapps.file_storage_optimization.FsoTypes">FsoTypes</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.activateapps.file_storage_optimization.FsoTypes.PROJECTS" href="#cvpysdk.activateapps.file_storage_optimization.FsoTypes.PROJECTS">PROJECTS</a></code></li>
<li><code><a title="cvpysdk.activateapps.file_storage_optimization.FsoTypes.SERVERS" href="#cvpysdk.activateapps.file_storage_optimization.FsoTypes.SERVERS">SERVERS</a></code></li>
<li><code><a title="cvpysdk.activateapps.file_storage_optimization.FsoTypes.SERVER_GROUPS" href="#cvpysdk.activateapps.file_storage_optimization.FsoTypes.SERVER_GROUPS">SERVER_GROUPS</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>