<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.subclients.exchange.usermailbox_subclient API documentation</title>
<meta name="description" content="File for operating on a UserMailbox Subclient …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.subclients.exchange.usermailbox_subclient</code></h1>
</header>
<section id="section-intro">
<p>File for operating on a UserMailbox Subclient.</p>
<p>UsermailboxSubclient is the only class defined in this file.</p>
<p>UsermailboxSubclient:
Derived class from ExchangeMailboxSubclient Base class, representing a
UserMailbox subclient, and to perform operations on that subclient</p>
<h1 id="usermailboxsubclient">UsermailboxSubclient:</h1>
<pre><code>_get_subclient_properties()         --  gets the properties of UserMailbox Subclient
_get_subclient_properties_json()    --  gets the properties JSON of UserMailbox Subclient
_get_discover_adgroups()            --  Get the discovered AD Groups
_get_discover_users()               --  Get the discovered users
_association_json_with_plan()       --  Create the Association JSON for
                                        associations using Exchange Plan
_association_mailboxes_json()       --  Association for particular mailboxes
_task_json_for_backup()             --  JSON for backup task for Exchange User mailbox Subclient
_backup_generic_items_json()        --  JSON to backup generic items
_search_user()                      --  Searches for the user in the discovered users list
</code></pre>
<h1 id="content-association-methods">Content Association Methods:</h1>
<pre><code>set_user_assocaition()              --  Set exchange users association
set_pst_association()               --  Create PST association for UserMailboxSubclient
set_fs_association_for_pst()        --  Helper method to create pst association for
                                        PST Ingestion by FS association
set_adgroup_associations()          --  Create Association for ADGroups
set_o365group_asscoiations()        --  Create O365 group association

delete_user_assocaition()           --  Delete User Association from content
delete_o365group_association()      --  Delete Office 365 Group Association
delete_database_assocaition()       --  Delete Exchange DB Association
delete_adgroup_assocaition          --  Delete association for an AD Group


enable_allusers_association()       --  Enable association for all mailboxes
disable_allusers_association()      --  Disable All Users Association

enable_auto_discover_association    --  Enable Association for Auto Discovered Content
                                        viz. All Public Folders/
                                            All Mailboxes/
                                            All Group Mailboxes
delete_auto_discover_association    --  Delete Association for Auto Discovered Content
                                       `viz. All Public Folders/
                                            All Mailboxes/
                                            All Group Mailboxes
enable_ews_support()                --  Enables EWS Support for backup for ON_PREM Mailboxes
</code></pre>
<h1 id="browse-restore-backup-methods">Browse/ Restore/ Backup Methods:</h1>
<pre><code>browse_mailboxes()                  --  Backup specific mailboxes
backup_generic_items()              --  Backup Generic Items
                                        viz. All Public Folders/
                                            All User Mailboxes/
                                            All Group Mailboxes
backup_mailboxes()                  --  Backup selected mailboxes
restore_in_place()                  --  runs in-place restore for the subclient
create_recovery_point()             --  Create a recovery point for a mailbox
restore_in_place_syntex()           --  runs an in-place restore for the Syntex client
find_mailbox()                      --  Performs search operation of a mailbox in browse
</code></pre>
<h1 id="user-mailbox-subclient-instance-attributes">User Mailbox Subclient Instance Attributes:</h1>
<pre><code>discover_users                          --  Dictionary of users discovered
discover_databases                      --  Dictionary of databases discovered
adgroups                                --  Dictionary of discovered AD Groups
o365groups                              --  Dictionary of discovered Office 365 Groups
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/usermailbox_subclient.py#L1-L1984" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;File for operating on a UserMailbox Subclient.

UsermailboxSubclient is the only class defined in this file.

UsermailboxSubclient:       Derived class from ExchangeMailboxSubclient Base class, representing a
                            UserMailbox subclient, and to perform operations on that subclient

UsermailboxSubclient:
======================

    _get_subclient_properties()         --  gets the properties of UserMailbox Subclient
    _get_subclient_properties_json()    --  gets the properties JSON of UserMailbox Subclient
    _get_discover_adgroups()            --  Get the discovered AD Groups
    _get_discover_users()               --  Get the discovered users
    _association_json_with_plan()       --  Create the Association JSON for
                                            associations using Exchange Plan
    _association_mailboxes_json()       --  Association for particular mailboxes
    _task_json_for_backup()             --  JSON for backup task for Exchange User mailbox Subclient
    _backup_generic_items_json()        --  JSON to backup generic items
    _search_user()                      --  Searches for the user in the discovered users list

Content Association Methods:
==============================

    set_user_assocaition()              --  Set exchange users association
    set_pst_association()               --  Create PST association for UserMailboxSubclient
    set_fs_association_for_pst()        --  Helper method to create pst association for
                                            PST Ingestion by FS association
    set_adgroup_associations()          --  Create Association for ADGroups
    set_o365group_asscoiations()        --  Create O365 group association

    delete_user_assocaition()           --  Delete User Association from content
    delete_o365group_association()      --  Delete Office 365 Group Association
    delete_database_assocaition()       --  Delete Exchange DB Association
    delete_adgroup_assocaition          --  Delete association for an AD Group


    enable_allusers_association()       --  Enable association for all mailboxes
    disable_allusers_association()      --  Disable All Users Association

    enable_auto_discover_association    --  Enable Association for Auto Discovered Content
                                            viz. All Public Folders/
                                                All Mailboxes/
                                                All Group Mailboxes
    delete_auto_discover_association    --  Delete Association for Auto Discovered Content
                                           `viz. All Public Folders/
                                                All Mailboxes/
                                                All Group Mailboxes
    enable_ews_support()                --  Enables EWS Support for backup for ON_PREM Mailboxes

Browse/ Restore/ Backup Methods:
==============================

    browse_mailboxes()                  --  Backup specific mailboxes
    backup_generic_items()              --  Backup Generic Items
                                            viz. All Public Folders/
                                                All User Mailboxes/
                                                All Group Mailboxes
    backup_mailboxes()                  --  Backup selected mailboxes
    restore_in_place()                  --  runs in-place restore for the subclient
    create_recovery_point()             --  Create a recovery point for a mailbox
    restore_in_place_syntex()           --  runs an in-place restore for the Syntex client
    find_mailbox()                      --  Performs search operation of a mailbox in browse



User Mailbox Subclient Instance Attributes:
==============================

    discover_users                          --  Dictionary of users discovered
    discover_databases                      --  Dictionary of databases discovered
    adgroups                                --  Dictionary of discovered AD Groups
    o365groups                              --  Dictionary of discovered Office 365 Groups
&#34;&#34;&#34;

from __future__ import unicode_literals
from ...exception import SDKException
from ..exchsubclient import ExchangeSubclient
from ...subclient import Subclients
from ...backupset import Backupsets
from .constants import ExchangeConstants

import time
import datetime


class UsermailboxSubclient(ExchangeSubclient):
    &#34;&#34;&#34;Derived class from ExchangeSubclient Base class.

        This represents a usermailbox subclient,
        and can perform discover and restore operations on only that subclient.

    &#34;&#34;&#34;

    def __init__(self, backupset_object, subclient_name, subclient_id=None):
        &#34;&#34;&#34;Initialize the Instance object for the given UserMailbox Subclient.

            Args:
                backupset_object    (object)    --  instance of the backupset class

                subclient_name      (str)       --  subclient name

                subclient_id        (int)       --  subclient id

        &#34;&#34;&#34;
        super(UsermailboxSubclient, self).__init__(
            backupset_object, subclient_name, subclient_id)

        self._instance_object = backupset_object._instance_object
        self._client_object = self._instance_object._agent_object._client_object
        self._SET_EMAIL_POLICY_ASSOCIATIONS = self._commcell_object._services[
            &#39;SET_EMAIL_POLICY_ASSOCIATIONS&#39;]
        self._SEARCH = (self._commcell_object._services[&#39;EMAIL_DISCOVERY&#39;] %
                        (int(self._backupset_object.backupset_id), &#39;User&#39;))

        self.refresh()

    def _policy_json(self, configuration_policy, policy_type):
        &#34;&#34;&#34;Creates policy Json based on configuration_policy name
        and policy_type

            Args:
                configuration_policy (str/object)  --  configuration policy name or
                object of congiguration policy class
                policy_type   (int)                --  configuration policy type

            Returns:
                list - list of the appropriate JSON for an agent to send to
                       the POST Subclient API
        &#34;&#34;&#34;

        from ...policies.configuration_policies import ConfigurationPolicy
        if not (isinstance(configuration_policy, (str, ConfigurationPolicy))):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if isinstance(configuration_policy, str):
            configuration_policy = ConfigurationPolicy(
                self._commcell_object, configuration_policy)

        policy_json = {
            &#34;policyType&#34;: 1,
            &#34;flags&#34;: 0,
            &#34;agentType&#34;: {
                &#34;appTypeId&#34;: 137
            },
            &#34;detail&#34;: {
                &#34;emailPolicy&#34;: {
                    &#34;emailPolicyType&#34;: policy_type
                }
            },
            &#34;policyEntity&#34;: {
                &#34;policyId&#34;: int(configuration_policy.configuration_policy_id),
                &#34;policyName&#34;: configuration_policy.configuration_policy_name
            }
        }

        return policy_json

    def _association_json(self, subclient_content, is_o365group=False):
        &#34;&#34;&#34;Constructs association json to create assocaition in UserMailbox Subclient.

            Args:
                subclient_content (dict)  --  dict of the Users to add to the subclient
                                             (dict of only policies in case of Office 365 groups)
                subclient_content = {

                        &#39;archive_policy&#39; : &#34;CIPLAN Archiving policy&#34;,

                        &#39;cleanup_policy&#39; : &#39;CIPLAN Clean-up policy&#39;,

                        &#39;retention_policy&#39;: &#39;CIPLAN Retention policy&#39;
                    }


            Returns:
                dict -- Association JSON request to pass to the API
        &#34;&#34;&#34;
        policy_types = {
            &#34;archive_policy&#34;: 1,
            &#34;cleanup_policy&#34;: 2,
            &#34;retention_policy&#34;: 3
        }

        email_policies = []

        if &#39;archive_policy&#39; in subclient_content:
            email_policies.append(self._policy_json(subclient_content.get(
                &#39;archive_policy&#39;), policy_types[&#39;archive_policy&#39;]))
        if &#39;cleanup_policy&#39; in subclient_content:
            email_policies.append(self._policy_json(subclient_content.get(
                &#39;cleanup_policy&#39;), policy_types[&#39;cleanup_policy&#39;]))
        if &#39;retention_policy&#39; in subclient_content:
            email_policies.append(self._policy_json(subclient_content.get(
                &#39;retention_policy&#39;), policy_types[&#39;retention_policy&#39;]))

        associations_json = {
            &#34;emailAssociation&#34;: {
                &#34;advanceOptions&#34;: {
                    &#34;enableAutoDiscovery&#34;: subclient_content.get(&#34;is_auto_discover_user&#34;,
                                                                 is_o365group)
                },
                &#34;subclientEntity&#34;: self._subClientEntity,
                &#34;policies&#34;: {
                    &#34;emailPolicies&#34;: email_policies
                }
            }
        }

        return associations_json

    def _association_json_with_plan(self, plan_details):
        &#34;&#34;&#34;Constructs association json with plan to create association in UserMailbox Subclient.
        
            Args: plan_details = {
                    &#39;plan_name&#39;: Plan Name,
                    &#39;plan_id&#39;: int or None (Optional)
                    }
                 Returns:
                    dict -- Association JSON request to pass to the API
        &#34;&#34;&#34;

        try:
            if not self._commcell_object.plans.has_plan(plan_details[&#39;plan_name&#39;]):
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;,
                                   &#39;Plan Name {} not found&#39;.format(plan_details[&#39;plan_name&#39;]))
            if &#39;plan_id&#39; not in plan_details or plan_details[&#39;plan_id&#39;] is None:
                plan_id = self._commcell_object.plans[plan_details[&#39;plan_name&#39;].lower()]
            else:
                plan_id = plan_details[&#39;plan_id&#39;]

        except KeyError as err:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))

        plan_details = {
            &#39;planId&#39;: int(plan_id)
        }

        association_json = {
            &#34;emailAssociation&#34;: {
                &#34;subclientEntity&#34;: self._subClientEntity,
                &#34;plan&#34;: plan_details
            }
        }
        return association_json

    def _association_mailboxes_json(self, mailbox_alias_names):
        &#34;&#34;&#34;
            Args:
                mailbox_alias_names(list): alias names of the mailboxes to backup
                    Example:
                        [&#39;aj&#39;, &#39;tkumar&#39;]
            Returns:
                mailboxes_json(list): Required details of mailboxes to backup
        &#34;&#34;&#34;
        mailboxes_json = []
        mailbox_alias_names = set(mailbox_alias_names)
        associated_mailboxes = self._users + self._o365groups

        for user in associated_mailboxes:
            if user[&#39;alias_name&#39;] in mailbox_alias_names:
                mailbox_info = {
                    &#34;aliasName&#34;: user[&#34;alias_name&#34;],
                    &#34;mailBoxType&#34;: user[&#39;mailbox_type&#39;],
                    &#34;databaseName&#34;: user[&#39;database_name&#39;],
                    &#34;displayName&#34;: user[&#39;display_name&#39;],
                    &#34;smtpAddress&#34;: user[&#39;smtp_address&#39;],
                    &#34;isAutoDiscoveredUser&#34;: True if user[&#39;is_auto_discover_user&#39;].lower() == &#39;true&#39; else False,
                    &#34;msExchRecipientTypeDetails&#34;: user[&#39;mailbox_type&#39;],
                    &#34;exchangeVersion&#34;: user[&#39;exchange_version&#39;],
                    &#34;exchangeServer&#34;: user[&#39;exchange_server&#39;],
                    &#34;lastArchiveJobRanTime&#34;: user[&#39;last_archive_job_ran_time&#39;],
                    &#34;user&#34;: {
                        &#34;userGUID&#34;: user[&#39;user_guid&#39;]
                    }
                }
                mailboxes_json.append(mailbox_info)
        return mailboxes_json

    def _task_json_for_backup(self, mailbox_alias_names, **kwargs):
        &#34;&#34;&#34;
        Args:
            mailbox_alias_names(list): alias names of the mailboxes to backup
                Sample Values
                    [&#39;aj&#39;, &#39;tkumar&#39;]

             **kwargs (dict) : Additional parameters
                items_selection_option (str) : Item Selection Option (Example: &#34;7&#34; for selecting backed up recently entities)
        Returns:
            task_json(dict): Task json required to pass to the API
        &#34;&#34;&#34;
        common_backup_options = None
        items_selection_option = kwargs.get(&#39;items_selection_option&#39;, &#39;&#39;)
        if items_selection_option != &#39;&#39;:
            common_backup_options = {&#39;itemsSelectionOption&#39;: items_selection_option}
        task_json = self._backup_json(backup_level=&#39;INCREMENTAL&#39;, incremental_backup=False,incremental_level=&#39;BEFORE_SYNTH&#39;,common_backup_options=common_backup_options)
        if mailbox_alias_names != None:
            associated_mailboxes_json = self._association_mailboxes_json(mailbox_alias_names)
            backup_options = {
                &#39;backupLevel&#39;: 2,  # Incremental
                &#39;incLevel&#39;: 1,
                &#39;exchOnePassOptions&#39;: {
                    &#39;mailBoxes&#39;: associated_mailboxes_json
                }
            }
            data_options = {
                &#34;useCatalogServer&#34;: False,
                &#34;followMountPoints&#34;: True,
                &#34;enforceTransactionLogUsage&#34;: False,
                &#34;skipConsistencyCheck&#34;: True,
                &#34;createNewIndex&#34;: False
            }
            task_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;backupOpts&#39;] = backup_options
            task_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;dataOpt&#39;] = data_options
        return task_json

    def _set_association_request(self, associations_json):
        &#34;&#34;&#34;
            Runs the emailAssociation POST API to set association

            Args:
                associations_json    (dict)  -- request json sent as payload

            Returns:
                (str, str):
                    str  -  error code received in the response

                    str  -  error message received

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._SET_EMAIL_POLICY_ASSOCIATIONS, associations_json
        )

        if flag:
            try:
                if response.json():
                    if response.json()[&#39;resp&#39;][&#39;errorCode&#39;] != 0:
                        error_message = response.json()[&#39;errorMessage&#39;]
                        output_string = &#39;Failed to create assocaition\nError: &#34;{0}&#34;&#39;
                        raise SDKException(
                            &#39;Subclient&#39;, &#39;102&#39;, output_string.format(error_message)
                        )
                    else:
                        self.refresh()
            except ValueError:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _update_association_request(self, associations_json):
        &#34;&#34;&#34;Runs the EmailAssocaition PUT API to update association

            Args:
                associations_json  (dict)  -- request json sent as payload

            Returns:
                (str, str):
                    str  -  error code received in the response

                    str  -  error message received

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;PUT&#39;, self._SET_EMAIL_POLICY_ASSOCIATIONS, associations_json
        )

        if flag:
            try:
                if response.json():
                    if response.json()[&#39;resp&#39;][&#39;errorCode&#39;] != 0:
                        error_message = response.json()[&#39;errorMessage&#39;]
                        output_string = &#39;Failed to create assocaition\nError: &#34;{0}&#34;&#39;
                        raise SDKException(
                            &#39;Subclient&#39;, &#39;102&#39;, output_string.format(error_message)
                        )
                    else:
                        self.refresh()
            except ValueError:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _get_discover_users(self, use_without_refresh_url=False, retry_attempts=0):
        &#34;&#34;&#34;Gets the discovered users from the Subclient .

            Args:
                use_without_refresh_url (boolean)   -   discovery without refresh cache

                retry_attempts(int)                 - retry for discovery

            Returns:
                list    -   list of discovered users associated with the subclient

        &#34;&#34;&#34;
        self._DISCOVERY = self._commcell_object._services[&#39;EMAIL_DISCOVERY&#39;] % (
            int(self._backupset_object.backupset_id), &#39;User&#39;
        )

        if use_without_refresh_url:
            self._DISCOVERY = self._commcell_object._services[&#39;EMAIL_DISCOVERY_WITHOUT_REFRESH&#39;] % (
                int(self._backupset_object.backupset_id), &#39;User&#39;
            )
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;GET&#39;, self._DISCOVERY)

        if flag:
            if response and response.json():
                discover_content = response.json()

                _error_code = discover_content.get(&#39;resp&#39;, {}).get(&#39;errorCode&#39;, 0)
                if _error_code == 469762468 or _error_code == 469762470:
                    # if discover_content.get(&#39;resp&#39;, {}).get(&#39;errorCode&#39;, 0) == 469762468:
                    time.sleep(10)  # the results might take some time depending on domains
                    if retry_attempts &gt; 10:
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Failed to perform discovery.&#39;)

                    return self._get_discover_users(use_without_refresh_url=True,
                                                    retry_attempts=retry_attempts + 1)

                if &#39;discoverInfo&#39; in discover_content.keys():
                    if &#39;mailBoxes&#39; in discover_content[&#39;discoverInfo&#39;]:
                        self._discover_users = discover_content[&#39;discoverInfo&#39;][&#39;mailBoxes&#39;]

                        return self._discover_users
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _search_user(self, user, retry_attempts=0):
        &#34;&#34;&#34;Searches for the user in the discovered users list .
            Args:
                user (str)              -   alias name/smtp address of the user to search for
            Returns:
                list    -   list of discovered users matching the search

        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;GET&#39;, f&#34;{self._SEARCH}&amp;search={user}&#34;)

        if flag:
            if response and response.json():
                search_content = response.json()

                _error_code = search_content.get(&#39;resp&#39;, {}).get(&#39;errorCode&#39;, 0)
                if _error_code == 469762468 or _error_code == 469762470:
                    # if search_content.get(&#39;resp&#39;, {}).get(&#39;errorCode&#39;, 0) == 469762468:
                    time.sleep(10)  # the results might take some time depending on domains
                    if retry_attempts &gt; 10:
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Failed to perform search and discovery.&#39;)
                    return self._search_user(user, retry_attempts=retry_attempts + 1)

                return search_content.get(&#39;discoverInfo&#39;, {}).get(&#39;mailBoxes&#39;, [])
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _get_discover_database(self):
        &#34;&#34;&#34;Gets the discovered databases from the Subclient .

            Returns:
                list    -   list of discovered databases associated with the subclient

        &#34;&#34;&#34;
        self._DISCOVERY = self._commcell_object._services[&#39;EMAIL_DISCOVERY&#39;] % (
            int(self._backupset_object.backupset_id), &#39;Database&#39;
        )

        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;GET&#39;, self._DISCOVERY)

        if flag:
            discover_content = response.json()
            if &#39;discoverInfo&#39; in discover_content.keys():
                if &#39;databases&#39; in discover_content[&#39;discoverInfo&#39;]:
                    discover_content = discover_content[&#39;discoverInfo&#39;][&#39;databases&#39;]
                return discover_content

        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _get_discover_adgroups(self):
        &#34;&#34;&#34;Gets the discovered adgroups from the Subclient .

            Returns:
                list    -   list of discovered adgroups associated with the subclient

        &#34;&#34;&#34;
        self._DISCOVERY = self._commcell_object._services[&#39;EMAIL_DISCOVERY&#39;] % (
            int(self._backupset_object.backupset_id), &#39;AD Group&#39;
        )
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;GET&#39;, self._DISCOVERY)

        if flag:
            discover_content = response.json()
            if &#39;discoverInfo&#39; in discover_content.keys():

                if &#39;adGroups&#39; in discover_content[&#39;discoverInfo&#39;]:
                    discover_content = discover_content[&#39;discoverInfo&#39;][&#39;adGroups&#39;]

                return discover_content

        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _get_user_assocaitions(self):
        &#34;&#34;&#34;Gets the appropriate users associations from the Subclient.

            Returns:
                list    -   list of users and groups associated with the subclient

        &#34;&#34;&#34;
        users = []
        groups = []

        self._EMAIL_POLICY_ASSOCIATIONS = self._commcell_object._services[
                                              &#39;GET_EMAIL_POLICY_ASSOCIATIONS&#39;] % (self.subclient_id, &#39;User&#39;)

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._EMAIL_POLICY_ASSOCIATIONS
        )

        if flag:
            subclient_content = response.json()

            if &#39;associations&#39; in subclient_content:
                children = subclient_content[&#39;associations&#39;]

                for child in children:
                    archive_policy = None
                    cleanup_policy = None
                    retention_policy = None
                    plan_name = None
                    plan_id = None
                    display_name = str(child[&#39;userMailBoxInfo&#39;][&#39;displayName&#39;])
                    alias_name = str(child[&#39;userMailBoxInfo&#39;][&#39;aliasName&#39;])
                    smtp_address = str(child[&#39;userMailBoxInfo&#39;][&#39;smtpAdrress&#39;])
                    database_name = str(child[&#39;userMailBoxInfo&#39;][&#39;databaseName&#39;])
                    exchange_server = str(child[&#39;userMailBoxInfo&#39;][&#39;exchangeServer&#39;])
                    user_guid = str(child[&#39;userMailBoxInfo&#39;][&#39;user&#39;][&#39;userGUID&#39;])
                    is_auto_discover_user = str(child[&#39;userMailBoxInfo&#39;][&#39;isAutoDiscoveredUser&#39;])
                    mailbox_type = int(child[&#39;userMailBoxInfo&#39;][&#39;msExchRecipientTypeDetails&#39;])
                    exchange_version = int(child[&#39;userMailBoxInfo&#39;][&#39;exchangeVersion&#39;])
                    last_archive_job_ran_time = child[&#39;userMailBoxInfo&#39;][&#39;lastArchiveJobRanTime&#39;]
                    if &#39;emailPolicies&#39; in child[&#39;policies&#39;]:
                        for policy in child[&#39;policies&#39;][&#39;emailPolicies&#39;]:
                            if policy[&#39;detail&#39;].get(&#39;emailPolicy&#39;, {}).get(&#39;emailPolicyType&#39;) == 1:
                                archive_policy = str(policy[&#39;policyEntity&#39;][&#39;policyName&#39;])
                            elif policy[&#39;detail&#39;].get(&#39;emailPolicy&#39;, {}).get(&#39;emailPolicyType&#39;) == 2:
                                cleanup_policy = str(policy[&#39;policyEntity&#39;][&#39;policyName&#39;])
                            elif policy[&#39;detail&#39;].get(&#39;emailPolicy&#39;, {}).get(&#39;emailPolicyType&#39;) == 3:
                                retention_policy = str(policy[&#39;policyEntity&#39;][&#39;policyName&#39;])
                    if &#39;plan&#39; in child:
                        plan_name = child.get(&#39;plan&#39;).get(&#39;planName&#39;)
                        plan_id = child.get(&#39;plan&#39;).get(&#39;planId&#39;)

                    temp_dict = {
                        &#39;display_name&#39;: display_name,
                        &#39;alias_name&#39;: alias_name,
                        &#39;smtp_address&#39;: smtp_address,
                        &#39;database_name&#39;: database_name,
                        &#39;exchange_server&#39;: exchange_server,
                        &#39;user_guid&#39;: user_guid,
                        &#39;is_auto_discover_user&#39;: is_auto_discover_user,
                        &#39;archive_policy&#39;: archive_policy,
                        &#39;cleanup_policy&#39;: cleanup_policy,
                        &#39;retention_policy&#39;: retention_policy,
                        &#39;plan_name&#39;: plan_name,
                        &#39;plan_id&#39;: plan_id,
                        &#39;mailbox_type&#39;: mailbox_type,
                        &#39;exchange_version&#39;: exchange_version,
                        &#39;last_archive_job_ran_time&#39;: last_archive_job_ran_time
                    }
                    if int(child[&#39;userMailBoxInfo&#39;][&#39;msExchRecipientTypeDetails&#39;]) == 36:
                        groups.append(temp_dict)
                    else:
                        users.append(temp_dict)

        return users, groups

    def _get_database_associations(self):
        &#34;&#34;&#34;Gets the appropriate database association from the Subclient.

            Returns:
                list    -   list of database associated with the subclient

        &#34;&#34;&#34;
        databases = []

        self._EMAIL_POLICY_ASSOCIATIONS = self._commcell_object._services[
                                              &#39;GET_EMAIL_POLICY_ASSOCIATIONS&#39;] % (self.subclient_id, &#39;Database&#39;)

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._EMAIL_POLICY_ASSOCIATIONS
        )

        if flag:
            subclient_content = response.json()

            if &#39;associations&#39; in subclient_content:
                children = subclient_content[&#39;associations&#39;]

                for child in children:
                    database_name = str(child[&#39;databaseInfo&#39;][&#39;databaseName&#39;])
                    exchange_server = str(child[&#39;databaseInfo&#39;][&#39;exchangeServer&#39;])
                    archive_policy = None
                    cleanup_policy = None
                    retention_policy = None
                    is_auto_discover_user = str(child[&#39;additionalOptions&#39;][&#39;enableAutoDiscovery&#39;])

                    for policy in child[&#39;policies&#39;][&#39;emailPolicies&#39;]:
                        if policy[&#39;detail&#39;][&#39;emailPolicy&#39;][&#39;emailPolicyType&#39;] == 1:
                            archive_policy = str(policy[&#39;policyEntity&#39;][&#39;policyName&#39;])
                        elif policy[&#39;detail&#39;][&#39;emailPolicy&#39;][&#39;emailPolicyType&#39;] == 2:
                            cleanup_policy = str(policy[&#39;policyEntity&#39;][&#39;policyName&#39;])
                        elif policy[&#39;detail&#39;][&#39;emailPolicy&#39;][&#39;emailPolicyType&#39;] == 3:
                            retention_policy = str(policy[&#39;policyEntity&#39;][&#39;policyName&#39;])

                    temp_dict = {
                        &#39;database_name&#39;: database_name,
                        &#39;exchange_server&#39;: exchange_server,
                        &#39;is_auto_discover_user&#39;: is_auto_discover_user,
                        &#39;archive_policy&#39;: archive_policy,
                        &#39;cleanup_policy&#39;: cleanup_policy,
                        &#39;retention_policy&#39;: retention_policy
                    }

                    databases.append(temp_dict)

        return databases

    def _get_adgroup_assocaitions(self):
        &#34;&#34;&#34;Gets the appropriate adgroup assocaitions from the Subclient.

            Returns:
                list    -   list of adgroups associated with the subclient

        &#34;&#34;&#34;
        adgroups = []

        self._EMAIL_POLICY_ASSOCIATIONS = self._commcell_object._services[
                                              &#39;GET_EMAIL_POLICY_ASSOCIATIONS&#39;] % (self.subclient_id, &#39;AD Group&#39;)

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._EMAIL_POLICY_ASSOCIATIONS
        )

        if flag:
            subclient_content = response.json()

            if &#39;associations&#39; in subclient_content:
                children = subclient_content[&#39;associations&#39;]

                for child in children:
                    archive_policy = None
                    cleanup_policy = None
                    retention_policy = None
                    adgroup_name = str(child[&#39;adGroupsInfo&#39;][&#39;adGroupName&#39;])
                    is_auto_discover_user = str(child[&#39;additionalOptions&#39;][&#39;enableAutoDiscovery&#39;])

                    for policy in child[&#39;policies&#39;][&#39;emailPolicies&#39;]:
                        if policy[&#39;detail&#39;][&#39;emailPolicy&#39;][&#39;emailPolicyType&#39;] == 1:
                            archive_policy = str(policy[&#39;policyEntity&#39;][&#39;policyName&#39;])
                        elif policy[&#39;detail&#39;][&#39;emailPolicy&#39;][&#39;emailPolicyType&#39;] == 2:
                            cleanup_policy = str(policy[&#39;policyEntity&#39;][&#39;policyName&#39;])
                        elif policy[&#39;detail&#39;][&#39;emailPolicy&#39;][&#39;emailPolicyType&#39;] == 3:
                            retention_policy = str(policy[&#39;policyEntity&#39;][&#39;policyName&#39;])

                    temp_dict = {
                        &#39;adgroup_name&#39;: adgroup_name,
                        &#39;is_auto_discover_user&#39;: is_auto_discover_user,
                        &#39;archive_policy&#39;: archive_policy,
                        &#39;cleanup_policy&#39;: cleanup_policy,
                        &#39;retention_policy&#39;: retention_policy
                    }

                    adgroups.append(temp_dict)

        return adgroups

    def _backup_generic_items_json(self, subclient_content):
        &#34;&#34;&#34;
            Create the JSON for Backing Up the Generic Items of any Exchange Online Client

            Args:
                subclient_content   (list)  List having dictionary of items to be backed up

                subclient_content = [
                    {
                    &#34;associationName&#34; : &#34;All Public Folders&#34;,
                    &#34;associationType&#34;:12
                    },
                    {
                    &#34;associationName&#34; : &#34;All Users&#34;,
                    &#34;associationType&#34;:12
                    }
                ]

            Returns:
                The JSON to create a backup task
        &#34;&#34;&#34;

        task_dict = {
            &#34;taskInfo&#34;: {
                &#34;associations&#34;: [
                    self._subClientEntity
                ],
                &#34;task&#34;: {
                    &#34;taskType&#34;: 1
                },
                &#34;subTasks&#34;: [
                    {
                        &#34;subTask&#34;: {
                            &#34;subTaskType&#34;: 2,
                            &#34;operationType&#34;: 2
                        },
                        &#34;options&#34;: {
                            &#34;backupOpts&#34;: {
                                &#34;backupLevel&#34;: 1,
                                &#34;incLevel&#34;: 1,
                                &#34;exchOnePassOptions&#34;: {
                                    &#34;genericAssociations&#34;: [
                                    ]
                                }
                            }
                        }
                    }
                ]
            }
        }
        task_dict[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;backupOpts&#34;][&#34;exchOnePassOptions&#34;][
            &#34;genericAssociations&#34;] = subclient_content
        return task_dict

    @property
    def discover_users(self):
        &#34;&#34;&#34;&#34;Returns the list of discovered users for the UserMailbox subclient.&#34;&#34;&#34;
        return self._discover_users

    @property
    def discover_databases(self):
        &#34;&#34;&#34;Returns the list of discovered databases for the UserMailbox subclient.&#34;&#34;&#34;
        return self._discover_databases

    @property
    def discover_adgroups(self):
        &#34;&#34;&#34;Returns the list of discovered AD groups for the UserMailbox subclient.&#34;&#34;&#34;
        return self._discover_adgroups

    @property
    def users(self):
        &#34;&#34;&#34;Returns the list of users associated with UserMailbox subclient.&#34;&#34;&#34;
        return self._users

    @property
    def databases(self):
        &#34;&#34;&#34;Returns the list of databases associated with the UserMailbox subclient.&#34;&#34;&#34;
        return self._databases

    @property
    def adgroups(self):
        &#34;&#34;&#34;Returns the list of AD groups associated with the UserMailbox subclient.&#34;&#34;&#34;
        return self._adgroups

    @property
    def o365groups(self):
        &#34;&#34;&#34;Returns the list of discovered O365 groups for the UserMailbox subclient.&#34;&#34;&#34;
        return self._o365groups

    def set_user_assocaition(self, subclient_content, use_policies=True):
        &#34;&#34;&#34;Create User assocaition for UserMailboxSubclient.

            Args:
                subclient_content   (dict)  --  dict of the Users to add to the subclient

                    subclient_content = {

                        &#39;mailboxNames&#39; : [&#34;AutoCi2&#34;],,

                        -- if use_policies is True --

                        &#39;archive_policy&#39; : &#34;CIPLAN Archiving policy&#34;,

                        &#39;cleanup_policy&#39; : &#39;CIPLAN Clean-up policy&#39;,

                        &#39;retention_policy&#39;: &#39;CIPLAN Retention policy&#39;

                        -- if use_policies is False --

                        &#39;plan_name&#39;: &#39;Exchange Plan Name&#39;,

                        &#39;plan_id&#39;: int or None (Optional)
                        --
                    }

        &#34;&#34;&#34;
        users = []

        if not isinstance(subclient_content, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if not (isinstance(subclient_content[&#39;mailboxNames&#39;], list)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        try:
            discover_users = self.discover_users

            for mailbox_item in subclient_content[&#39;mailboxNames&#39;]:

                for mb_item in discover_users:

                    if mailbox_item.lower() == mb_item[&#39;aliasName&#39;].lower() or \
                            mailbox_item.lower() == mb_item[&#39;smtpAdrress&#39;].lower():
                        mailbox_dict = {
                            &#39;smtpAdrress&#39;: mb_item[&#39;smtpAdrress&#39;],
                            &#39;aliasName&#39;: mb_item[&#39;aliasName&#39;],
                            &#39;mailBoxType&#39;: mb_item[&#39;mailBoxType&#39;],
                            &#39;displayName&#39;: mb_item[&#39;displayName&#39;],
                            &#39;exchangeServer&#39;: mb_item[&#39;exchangeServer&#39;],
                            &#39;isAutoDiscoveredUser&#39;: mb_item[&#39;isAutoDiscoveredUser&#39;],
                            &#34;associated&#34;: False,
                            &#39;databaseName&#39;: mb_item[&#39;databaseName&#39;],
                            &#34;exchangeVersion&#34;: mb_item[&#39;exchangeVersion&#39;],
                            # &#34;msExchRecipientTypeDetails&#34;: mb_item[&#39;msExchRecipientTypeDetails&#39;],
                            &#39;user&#39;: {
                                &#39;_type_&#39;: 13,
                                &#39;userGUID&#39;: mb_item[&#39;user&#39;][&#39;userGUID&#39;]
                            }
                        }
                        users.append(mailbox_dict)
                        break

        except KeyError as err:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))

        if len(users) != len(subclient_content[&#39;mailboxNames&#39;]):
            to_search_user = [s_user for s_user in subclient_content[&#39;mailboxNames&#39;]
                              if not any(s_user == f_user[&#39;smtpAdrress&#39;] for f_user in users)]
            for mailbox_item in to_search_user:
                search_users = self._search_user(mailbox_item)
                if len(search_users) != 0:
                    users.append(search_users[0])

        discover_info = {
            &#34;discoverByType&#34;: 1,
            &#34;mailBoxes&#34;: users
        }
        if use_policies:
            _association_json_ = self._association_json(subclient_content)
        else:
            _association_json_ = self._association_json_with_plan(subclient_content)
        _association_json_[&#34;emailAssociation&#34;][&#34;emailDiscoverinfo&#34;] = discover_info
        self._set_association_request(_association_json_)

    def set_pst_association(self, subclient_content):
        &#34;&#34;&#34;Create PST assocaition for UserMailboxSubclient.

            Args:
                subclient_content   (dict)  --  dict of the pst to add to the subclient

                    subclient_content = {

                        &#39;pstTaskName&#39; : &#34;Task Name for PST&#34;,

                        &#39;folders&#39; : [&#39;list of folders&#39;] //If pst ingestion by folder location,
                        &#39;fsContent&#39;: Dictionary of client, backupset, subclient
                        Ex: {&#39;client1&#39;:{&#39;backupset1&#39;:[subclient1], &#39;backupset2&#39;:None},
                            &#39;client2&#39;: None}
                        This would add subclient1, all subclients under backupset2 and
                        all backupsets under client2 to the association

                        &#39;pstOwnerManagement&#39; : {

                            &#39;defaultOwner&#39;: &#34;default owner if no owner is determined&#34;,

                            &#39;pstDestFolder&#39;: &#34;ingest psts under this folder&#34;,

                            &#39;usePSTNameToCreateChild&#39;: Boolean
                        }
                    }
        &#34;&#34;&#34;
        if not isinstance(subclient_content, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        try:
            if &#39;ownerSelectionOrder&#39; not in subclient_content[&#39;pstOwnerManagement&#39;]:
                subclient_content[&#39;pstOwnerManagement&#39;][&#39;ownerSelectionOrder&#39;] = [4, 1, 3]
            if &#39;createPstDestFolder&#39; not in subclient_content[&#39;pstOwnerManagement&#39;]:
                subclient_content[&#39;pstOwnerManagement&#39;][&#39;createPstDestFolder&#39;] = True
            if &#39;pstDestFolder&#39; not in subclient_content[&#39;pstOwnerManagement&#39;]:
                subclient_content[&#39;pstOwnerManagement&#39;][&#39;pstDestFolder&#39;] = (f&#39;Archived From &#39;
                                                                            f&#39;Automation&#39;)

            pst_dict = {
                &#39;pstTaskName&#39;: subclient_content[&#39;pstTaskName&#39;],
                &#39;taskType&#39;: 1,
                &#39;pstOwnerManagement&#39;: {
                    &#39;adProperty&#39;: &#34;&#34;,
                    &#39;startingFolderPath&#39;: &#34;&#34;,
                    &#39;pstStubsAction&#39;: 1,
                    &#39;managePSTStubs&#39;: False,
                    &#39;mergeintoMailBox&#39;: True,
                    &#39;pstOwnerBasedOnACL&#39;: True,
                    &#39;pstOwnerBasedOnLaptop&#39;: False,
                    &#39;usePSTNameToCreateChildForNoOwner&#39;: True,
                    &#39;createPstDestFolder&#39;:
                        subclient_content[&#34;pstOwnerManagement&#34;][&#34;createPstDestFolder&#34;],
                    &#39;orphanFolder&#39;: subclient_content[&#39;pstOwnerManagement&#39;][&#39;defaultOwner&#39;],
                    &#39;pstDestFolder&#39;: subclient_content[&#39;pstOwnerManagement&#39;][&#39;pstDestFolder&#39;],
                    &#39;usePSTNameToCreateChild&#39;:
                        subclient_content[&#39;pstOwnerManagement&#39;][&#39;usePSTNameToCreateChild&#39;],
                    &#39;ownerSelectionOrder&#39;:
                        subclient_content[&#34;pstOwnerManagement&#34;][&#34;ownerSelectionOrder&#34;]
                }
            }
            if &#39;folders&#39; in subclient_content:
                pst_dict[&#39;folders&#39;] = subclient_content[&#39;folders&#39;]
            elif &#39;fsContent&#39; in subclient_content:
                pst_dict[&#39;associations&#39;] = self.set_fs_association_for_pst(
                    subclient_content[&#39;fsContent&#39;])
                pst_dict[&#39;taskType&#39;] = 0
            subclient_entity = {&#34;_type_&#34;: 7, &#34;subclientId&#34;: int(self._subclient_id)}
            discover_info = {
                &#39;discoverByType&#39;: 9,
                &#39;pstIngestion&#39;: pst_dict
            }
            _assocaition_json_ = {
                &#34;emailAssociation&#34;:
                    {
                        &#34;emailDiscoverinfo&#34;: discover_info,
                        &#34;subclientEntity&#34;: subclient_entity
                    }
            }
            self._set_association_request(_assocaition_json_)

        except KeyError as err:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))

    def set_fs_association_for_pst(self, association):
        &#34;&#34;&#34;Helper method to create pst association for PST Ingestion by FS
            Args:
                association(dict) -- Dictionary of client, backupset, subclient
                                    Ex: {&#39;client1&#39;:{&#39;backupset1&#39;:[subclient1], &#39;backupset2&#39;:None},
                                        &#39;client2&#39;: None}
                                    This would add subclient1, all subclients under backupset2 and
                                    all backupsets under client2 to the association
        &#34;&#34;&#34;
        assoc_list = []
        client_dict, backupset_dict, sub_dict = dict(), dict(), dict()
        _type_id = {&#34;client&#34;: 3, &#34;subclient&#34;: 7, &#34;backupset&#34;: 6, &#34;apptype&#34;: 4}
        for client_name, backupsets in association.items():
            client_name = client_name.lower()
            client_obj = self._commcell_object.clients.get(client_name)

            client_dict = {&#34;commCellId&#34;: int(self._commcell_object._id),
                           &#34;commcellName&#34;: self._commcell_object.commserv_name,
                           &#34;clientName&#34;: client_name,
                           &#34;clientId&#34;: int(client_obj.client_id)
                           }
            agent = client_obj.agents.get(&#34;file system&#34;)

            if backupsets:
                for backupset_name, subclients in backupsets.items():
                    backupset_name = backupset_name.lower()
                    backupset_obj = agent.backupsets.get(backupset_name)
                    if not backupset_obj:
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#34;Backupset {0} not present in &#34;
                                                               &#34;&#34;.format(backupset_name))
                    backupset_dict = {&#34;backupsetName&#34;: backupset_obj.name,
                                      &#34;appName&#34;: &#34;File System&#34;,
                                      &#34;applicationId&#34;: int(agent.agent_id),
                                      &#34;backupsetId&#34;: int(backupset_obj.backupset_id),
                                      &#34;_type_&#34;: _type_id[&#34;backupset&#34;]
                                      }
                    backupset_dict.update(client_dict)
                    for subclient_name in subclients:
                        if subclient_name.lower() not in backupset_obj.subclients.all_subclients:
                            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;,
                                               &#34;Subclient %s not present in backupset %s&#34; %
                                               (str(subclient_name), str(backupset_name)))
                        subclient_name = subclient_name.lower()
                        sub_dict = {&#34;subclientId&#34;: int(backupset_obj.subclients.all_subclients[
                                                           subclient_name][&#39;id&#39;]),
                                    &#34;subclientName&#34;: subclient_name}
                        sub_dict.update(backupset_dict)
                        sub_dict[&#34;_type_&#34;] = _type_id[&#34;subclient&#34;]
                        assoc_list.append(sub_dict)
                    if not subclients:
                        assoc_list.append(backupset_dict)
            else:
                client_dict[&#34;_type_&#34;] = _type_id[&#34;client&#34;]
                assoc_list.append(client_dict)
        return assoc_list

    def set_database_assocaition(self, subclient_content, use_policies=True):
        &#34;&#34;&#34;Create Database assocaition for UserMailboxSubclient.

            Args:
                subclient_content   (dict)  --  dict of the databases to add to the subclient

                    For policy -
                    subclient_content = {

                        &#39;databaseNames&#39; : [&#34;Name of dbs as list&#34;],

                        &#39;is_auto_discover_user&#39; : True,

                        &#39;archive_policy&#39; : &#34;CIPLAN Archiving policy&#34;,

                        &#39;cleanup_policy&#39; : &#39;CIPLAN Clean-up policy&#39;,

                        &#39;retention_policy&#39;: &#39;CIPLAN Retention policy&#39;
                    }

                    For Plan -
                    subclient_content = { &#39;is_auto_discover_user&#39;: True,
                    &#34;plan_name&#34;: Name of plan, &#34;plan_id&#34;(optional) -- id of plan}

                use_policies(bool)  -- if we need to use policy or plan
                
                
        &#34;&#34;&#34;
        databases = []

        if not isinstance(subclient_content, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if not (isinstance(subclient_content[&#39;databaseNames&#39;], list) and
                isinstance(subclient_content[&#39;is_auto_discover_user&#39;], bool)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        try:
            discover_databases = self.discover_databases

            for database_item in subclient_content[&#39;databaseNames&#39;]:

                for db_item in discover_databases:

                    if database_item.lower() == db_item[&#39;databaseName&#39;].lower():
                        database_dict = {
                            &#39;exchangeServer&#39;: db_item[&#39;exchangeServer&#39;],
                            &#34;associated&#34;: False,
                            &#39;databaseName&#39;: db_item[&#39;databaseName&#39;],
                        }
                        databases.append(database_dict)

        except KeyError as err:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))

        discover_info = {
            &#34;discoverByType&#34;: 2,
            &#34;databases&#34;: databases
        }
        if use_policies:
            _association_json_ = self._association_json(subclient_content)
        else:
            _association_json_ = self._association_json_with_plan(subclient_content)
            _association_json_[&#34;emailAssociation&#34;][&#34;advanceOptions&#34;] = {
                &#34;enableAutoDiscovery&#34;: subclient_content.get(&#34;is_auto_discover_user&#34;, False)
            }
        _association_json_[&#34;emailAssociation&#34;][&#34;emailDiscoverinfo&#34;] = discover_info
        self._set_association_request(_association_json_)

    def set_adgroup_associations(self, subclient_content, use_policies=True):
        &#34;&#34;&#34;Create Ad groups assocaition for UserMailboxSubclient.

            Args:
                subclient_content   (dict)  --  dict of the adgroups to add to the subclient

                    subclient_content = {

                        &#39;adGroupNames&#39; : [&#34;List of adGroups&#34;],

                        &#39;is_auto_discover_user&#39; : True,

                        &#39;archive_policy&#39; : &#34;Archiving policy&#34;,

                        &#39;cleanup_policy&#39; : &#39;Clean-up policy&#39;,

                        &#39;retention_policy&#39;: &#39;Retention policy&#39;,
                    }
                        -- if use_policies is False --

                        &#39;plan_name&#39;: &#39;Exchange Plan Name&#39;,
                        &#39;is_auto_discover_user&#39; : True,
                        &#39;plan_id&#39;: int or None (Optional)
                        --

        &#34;&#34;&#34;
        adgroups = []

        if not isinstance(subclient_content, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if not (isinstance(subclient_content[&#39;adGroupNames&#39;], list) and
                isinstance(subclient_content[&#39;is_auto_discover_user&#39;], bool)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        try:
            discover_adgroups = self.discover_adgroups

            for adgroup_item in subclient_content[&#39;adGroupNames&#39;]:

                for ad_item in discover_adgroups:

                    if adgroup_item.lower() == ad_item[&#39;adGroupName&#39;].lower():
                        adgroup_dict = {
                            &#39;adGroupName&#39;: ad_item[&#39;adGroupName&#39;],
                        }
                        adgroups.append(adgroup_dict)

        except KeyError as err:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))

        discover_info = {
            &#34;discoverByType&#34;: 3,
            &#34;adGroups&#34;: adgroups
        }
        if use_policies:
            _assocaition_json_ = self._association_json(subclient_content)
        else:
            _assocaition_json_ = self._association_json_with_plan(subclient_content)
        _assocaition_json_[&#34;emailAssociation&#34;][&#34;emailDiscoverinfo&#34;] = discover_info
        _assocaition_json_[&#34;emailAssociation&#34;].update({&#34;emailStatus&#34;: 0})
        _assocaition_json_[&#34;emailAssociation&#34;].update({&#34;advanceOptions&#34;: {
            &#34;enableAutoDiscovery&#34;: subclient_content[&#34;is_auto_discover_user&#34;]
        }})
        self._set_association_request(_assocaition_json_)

    def set_o365group_asscoiations(self, subclient_content):
        &#34;&#34;&#34;Create O365 Group association for UserMailboxSubclient.
            Args:
                subclient_content   (dict)  --  dict of the policies to associate

                    subclient_content = {

                        &#39;archive_policy&#39; : &#34;CIPLAN Archiving policy&#34;,

                        &#39;cleanup_policy&#39; : &#39;CIPLAN Clean-up policy&#39;,

                        &#39;retention_policy&#39;: &#39;CIPLAN Retention policy&#39;
                    }
        &#34;&#34;&#34;
        discover_info = {
            &#34;discoverByType&#34;: 11,
            &#34;genericAssociations&#34;: [
                {
                    &#34;associationName&#34;: &#34;All O365 Group Mailboxes&#34;,
                    &#34;associationType&#34;: 11
                }
            ]
        }
        _assocaition_json_ = self._association_json(subclient_content, True)
        _assocaition_json_[&#34;emailAssociation&#34;][&#34;emailDiscoverinfo&#34;] = discover_info
        self._set_association_request(_assocaition_json_)

    def delete_user_assocaition(self, subclient_content, use_policies=True):
        &#34;&#34;&#34;delete User assocaition for UserMailboxSubclient.
            Args:
                subclient_content   (dict)  --  dict of the Users to delete from subclient
                    subclient_content = {
                        &#39;mailboxNames&#39; : [&#34;list of mailboxes alias name&#34;],
                        -- if use_policies is True --
                        &#39;archive_policy&#39; : &#34;CIPLAN Archiving policy&#34;,

                        &#39;cleanup_policy&#39; : &#39;CIPLAN Clean-up policy&#39;,

                        &#39;retention_policy&#39;: &#39;CIPLAN Retention policy&#39;
                        --
                        -- if use_policies is False --
                        &#39;plan_name&#39;: Plan Name,
                        &#39;plan_id&#39;: int or None (Optional)
                        --
                    }
                use_policies (bool) -- If True uses policies else uses Plan
        &#34;&#34;&#34;
        users = []

        if not isinstance(subclient_content, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if not (isinstance(subclient_content[&#39;mailboxNames&#39;], list)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        try:
            discover_users = self.discover_users

            for mailbox_item in subclient_content[&#39;mailboxNames&#39;]:

                for mb_item in discover_users:

                    if mailbox_item.lower() == mb_item[&#39;aliasName&#39;].lower():
                        mailbox_dict = {
                            &#39;smtpAdrress&#39;: mb_item[&#39;smtpAdrress&#39;],
                            &#39;aliasName&#39;: mb_item[&#39;aliasName&#39;],
                            &#39;mailBoxType&#39;: mb_item[&#39;mailBoxType&#39;],
                            &#39;displayName&#39;: mb_item[&#39;displayName&#39;],
                            &#39;exchangeServer&#39;: mb_item[&#39;exchangeServer&#39;],
                            &#39;isAutoDiscoveredUser&#39;: mb_item[&#39;isAutoDiscoveredUser&#39;],
                            &#34;associated&#34;: False,
                            &#39;databaseName&#39;: mb_item[&#39;databaseName&#39;],
                            &#34;exchangeVersion&#34;: mb_item[&#39;exchangeVersion&#39;],
                            &#34;exchangeServer&#34;: mb_item[&#39;exchangeServer&#39;],
                            &#39;user&#39;: {
                                &#39;_type_&#39;: 13,
                                &#39;userGUID&#39;: mb_item[&#39;user&#39;][&#39;userGUID&#39;]
                            }
                        }
                        users.append(mailbox_dict)
                        break

        except KeyError as err:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))
        discover_info = {
            &#34;discoverByType&#34;: 1,
            &#34;mailBoxes&#34;: users
        }
        if use_policies:
            _association_json_ = self._association_json(subclient_content)
        else:
            _association_json_ = self._association_json_with_plan(subclient_content)
        _association_json_[&#34;emailAssociation&#34;][&#34;emailStatus&#34;] = 1
        _association_json_[&#34;emailAssociation&#34;][&#34;emailDiscoverinfo&#34;] = discover_info
        self._update_association_request(_association_json_)

    def delete_o365group_association(self, subclient_content):
        &#34;&#34;&#34;delete O365 group association for UserMailboxSubclient.

            Args:
                subclient_content   (dict)  --  dict of the Users to delete from subclient

                    subclient_content = {

                        &#39;mailboxNames&#39; : [&#34;AutoCi2&#34;],

                        &#39;archive_policy&#39; : &#34;CIPLAN Archiving policy&#34;,

                        &#39;cleanup_policy&#39; : &#39;CIPLAN Clean-up policy&#39;,

                        &#39;retention_policy&#39;: &#39;CIPLAN Retention policy&#39;
                    }

        &#34;&#34;&#34;
        groups = []
        try:
            for mb_item in self.o365groups:
                mailbox_dict = {
                    &#39;smtpAdrress&#39;: mb_item[&#39;smtp_address&#39;],
                    &#39;aliasName&#39;: mb_item[&#39;alias_name&#39;],
                    &#39;mailBoxType&#39;: 1,
                    &#39;displayName&#39;: mb_item[&#39;display_name&#39;],
                    &#39;exchangeServer&#39;: &#34;&#34;,
                    &#39;isAutoDiscoveredUser&#39;: mb_item[&#39;is_auto_discover_user&#39;].lower() == &#39;true&#39;,
                    &#39;msExchRecipientTypeDetails&#39;: 36,
                    &#34;associated&#34;: False,
                    &#39;databaseName&#39;: mb_item[&#39;database_name&#39;],
                    &#39;user&#39;: {
                        &#39;_type_&#39;: 13,
                        &#39;userGUID&#39;: mb_item[&#39;user_guid&#39;]
                    }
                }
                groups.append(mailbox_dict)

        except KeyError as err:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))

        discover_info = {
            &#34;discoverByType&#34;: 1,
            &#34;mailBoxes&#34;: groups
        }
        _assocaition_json_ = self._association_json(subclient_content, True)
        _assocaition_json_[&#34;emailAssociation&#34;][&#34;emailStatus&#34;] = 1
        _assocaition_json_[&#34;emailAssociation&#34;][&#34;emailDiscoverinfo&#34;] = discover_info
        self._update_association_request(_assocaition_json_)

    def delete_database_assocaition(self, subclient_content, use_policies=True):
        &#34;&#34;&#34;Deletes Database assocaition for UserMailboxSubclient.

            Args:
                subclient_content   (dict)  --  dict of the databases to delete from subclient

                    For policy -
                    subclient_content = {

                        &#39;databaseNames&#39; : [&#34;List of names of db&#34;],

                        &#39;archive_policy&#39; : &#34;Archiving policy&#34;,

                        &#39;cleanup_policy&#39; : &#39;Clean-up policy&#39;,

                        &#39;retention_policy&#39;: &#39;Retention policy&#39;
                    }

                    For Plan -
                    subclient_content = { &#39;is_auto_discover_user&#39; : True,
                    &#34;plan_name&#34;: Name of plan, &#34;plan_id&#34;(optional) -- id of plan}

                use_policies(bool)  -- if we need to use policy or plan
        &#34;&#34;&#34;
        databases = []

        if not isinstance(subclient_content, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if not (isinstance(subclient_content[&#39;databaseNames&#39;], list) and
                isinstance(subclient_content[&#39;is_auto_discover_user&#39;], bool)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        try:
            discover_databases = self.discover_databases

            for database_item in subclient_content[&#39;databaseNames&#39;]:

                for db_item in discover_databases:

                    if database_item.lower() == db_item[&#39;databaseName&#39;].lower():
                        database_dict = {
                            &#39;exchangeServer&#39;: db_item[&#39;exchangeServer&#39;],
                            &#34;associated&#34;: False,
                            &#39;databaseName&#39;: db_item[&#39;databaseName&#39;],
                        }
                        databases.append(database_dict)

        except KeyError as err:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))

        discover_info = {
            &#34;discoverByType&#34;: 2,
            &#34;databases&#34;: databases
        }

        if use_policies:
            _association_json_ = self._association_json(subclient_content)
        else:
            _association_json_ = self._association_json_with_plan(subclient_content)

        _association_json_[&#34;emailAssociation&#34;][&#34;emailStatus&#34;] = 1
        _association_json_[&#34;emailAssociation&#34;][&#34;emailDiscoverinfo&#34;] = discover_info
        self._update_association_request(_association_json_)

    def delete_adgroup_assocaition(self, subclient_content, use_policies=True):
        &#34;&#34;&#34;Deletes Ad groups assocaition for UserMailboxSubclient.

            Args:
                subclient_content   (dict)  --  dict of the adgroups to delete from subclient

                    subclient_content = {

                        &#39;adGroupNames&#39; : [&#34;List of names of adgroups],

                        &#39;is_auto_discover_user&#39; : True,

                        &#39;archive_policy&#39; : &#34;CIPLAN Archiving policy&#34;,

                        &#39;cleanup_policy&#39; : &#39;CIPLAN Clean-up policy&#39;,

                        &#39;retention_policy&#39;: &#39;CIPLAN Retention policy&#39;,
                    }
                    For plan:
                        &#39;is_auto_discover_user&#39; : True,

                        &#39;plan_name&#39;: &#39;Exchange Plan Name&#39;,

                        &#39;plan_id&#39;: int or None (Optional)

                use_policies(bool)  -- if we need to use policy or plan
        &#34;&#34;&#34;
        adgroups = []

        if not isinstance(subclient_content, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if not (isinstance(subclient_content[&#39;adGroupNames&#39;], list) and
                isinstance(subclient_content[&#39;is_auto_discover_user&#39;], bool)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        try:
            discover_adgroups = self.discover_adgroups

            for adgroup_item in subclient_content[&#39;adGroupNames&#39;]:

                for ad_item in discover_adgroups:

                    if adgroup_item.lower() == ad_item[&#39;adGroupName&#39;].lower():
                        adgroup_dict = {
                            &#34;associated&#34;: False,
                            &#39;adGroupName&#39;: ad_item[&#39;adGroupName&#39;],
                        }
                        adgroups.append(adgroup_dict)

        except KeyError as err:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))

        discover_info = {
            &#34;discoverByType&#34;: 3,
            &#34;adGroups&#34;: adgroups
        }
        if use_policies:
            _assocaition_json_ = self._association_json(subclient_content)
        else:
            _assocaition_json_ = self._association_json_with_plan(subclient_content)
        _assocaition_json_[&#34;emailAssociation&#34;][&#34;emailStatus&#34;] = 1
        _assocaition_json_[&#34;emailAssociation&#34;][&#34;enableAutoDiscovery&#34;] = subclient_content[
            &#34;is_auto_discover_user&#34;]
        _assocaition_json_[&#34;emailAssociation&#34;][&#34;emailDiscoverinfo&#34;] = discover_info
        self._set_association_request(_assocaition_json_)

    def enable_allusers_associations(self, subclient_content, use_policies=True):
        &#34;&#34;&#34;Enable all users assocaition for UserMailboxSubclient.

            Args:
                subclient_content   (dict)  --  dict of the policies which needs to be
                assigned to all user assocaitions
                    For policy
                    subclient_content = {

                        &#39;is_auto_discover_user&#39; : True

                        &#39;archive_policy&#39; : &#34;CIPLAN Archiving policy&#34;,

                        &#39;cleanup_policy&#39; : &#39;CIPLAN Clean-up policy&#39;,

                        &#39;retention_policy&#39;: &#39;CIPLAN Retention policy&#39;,
                    }
    
                    For Plan -
                    subclient_content = { &#39;is_auto_discover_user&#39; : True,
                    &#34;plan_name&#34;: Name of plan, &#34;plan_id&#34;(optional) -- id of plan
                    }

                use_policies(bool)  -- if we need to use policy or plan

        &#34;&#34;&#34;

        if not isinstance(subclient_content, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        discover_info = {
            &#34;discoverByType&#34;: 8,
            &#34;genericAssociations&#34;: [
                {
                    &#34;associationName&#34;: &#34;All Users&#34;,
                    &#34;associationType&#34;: 8
                }
            ]
        }
        if use_policies:
            _association_json_ = self._association_json(subclient_content)
        else:
            _association_json_ = self._association_json_with_plan(plan_details=subclient_content)
        _association_json_[&#39;emailAssociation&#39;][&#39;advanceOptions&#39;] = {&#34;enableAutoDiscovery&#34;: True}
        _association_json_[&#34;emailAssociation&#34;][&#34;emailDiscoverinfo&#34;] = discover_info
        self._set_association_request(_association_json_)

    def disable_allusers_associations(self, use_policies=True, plan_details=None):
        &#34;&#34;&#34;Disables alluser assocaition for UserMailboxSubclient.
            use_policies (bool) -- If we need to use plan(False) or policy(True)
            plan_details(dict) -- { &#34;plan_name&#34;: Name of plan, &#34;plan_id&#34;(optional) -- id of plan}
        &#34;&#34;&#34;
        subclient_content = {
            &#39;is_auto_discover_user&#39;: True
        }
        discover_info = {
            &#34;discoverByType&#34;: 8,
            &#34;genericAssociations&#34;: [
                {
                    &#34;associationName&#34;: &#34;All Users&#34;,
                    &#34;associationType&#34;: 8
                }
            ]
        }

        if use_policies:
            _association_json_ = self._association_json(subclient_content)
        else:
            _association_json_ = self._association_json_with_plan(plan_details=plan_details)

        _association_json_[&#34;emailAssociation&#34;][&#34;emailDiscoverinfo&#34;] = discover_info
        _association_json_[&#34;emailAssociation&#34;][&#34;emailStatus&#34;] = 2
        _association_json_[&#39;emailAssociation&#39;][&#39;advanceOptions&#39;] = {&#34;enableAutoDiscovery&#34;: True}
        self._set_association_request(_association_json_)

    def enable_auto_discover_association(self, association_name, plan_name):
        &#34;&#34;&#34;Enable all users assocaition for UserMailboxSubclient.

                    Args:
                        association_name  (str)  --  Type of auto discover association
                            Valid Values:
                                &#34;All Users&#34;
                                &#34;All O365 Mailboxes&#34;
                                &#34;All Public Folders&#34;

                        plan_name  (str)  --  Name of the plan to associate with users/groups


                &#34;&#34;&#34;
        plan = self._commcell_object.plans.get(plan_name)

        association_dict = {&#34;All Users&#34;: 8,
                            &#34;All Office365 Groups&#34;: 11,
                            &#34;All Public Folders&#34;: 12
                            }

        _association_json = {
            &#34;emailAssociation&#34;: {
                &#34;emailStatus&#34;: 0,
                &#34;advanceOptions&#34;: {
                    &#34;enableAutoDiscovery&#34;: True
                },
                &#34;subclientEntity&#34;: self._subClientEntity,
                &#34;emailDiscoverinfo&#34;: {
                    &#34;discoverByType&#34;: association_dict[association_name],
                    &#34;genericAssociations&#34;: [
                        {
                            &#34;associationName&#34;: association_name,
                            &#34;associationType&#34;: association_dict[association_name]
                        }
                    ]
                },
                &#34;plan&#34;: {
                    &#34;planId&#34;: int(plan.plan_id)
                }
            }
        }
        self._set_association_request(_association_json)

    def delete_auto_discover_association(self, association_name, subclient_content, use_policies=True):
        &#34;&#34;&#34;
            Delete all users association for UserMailboxSubclient.

                    Args:
                        association_name  (str)  --  Type of auto discover association
                            Valid Values:
                                &#34;All Users&#34;
                                &#34;All O365 Mailboxes&#34;
                                &#34;All Public Folders&#34;

                        use_policies (bool) -- If we need to use plan(False) or policy(True)

                        subclient_content (dict) - containing the information of users/groups

                            if use_policies is True
                                subclient_content={
                                    &#34;is_auto_dicover_user&#34; (bool): True
                                    &#34;archive_policy&#34; (obj): Archive Policy object
                                    &#34;cleanup_policy&#34; (obj): Cleanup Policy Object
                                    &#34;retention_policy&#34; (obj): Retention Policy Object
                                }

                            if use_policies is False
                                subclient_content={
                                    &#34;is_auto_discover_user&#34; (bool): True,
                                    &#34;plan_name&#34; (str): Name of the exchange plan,
                                    &#34;plan_id&#34;: Id of the plan(optional)
                                }
        &#34;&#34;&#34;
        if not (isinstance(subclient_content, dict)):
            raise SDKException(&#34;Subclient&#34;, &#34;101&#34;)

        association_dict = {&#34;all users&#34;: 8,
                            &#34;all o365 group mailboxes&#34;: 11,
                            &#34;all public folders&#34;: 12
                            }

        if association_name.lower() not in association_dict:
            raise SDKException(&#34;Subclient&#34;, &#34;102&#34;, &#34;Invalid Association Name supplied&#34;)

        if use_policies:
            _association_json_ = self._association_json(subclient_content)
        else:
            planobject = self._commcell_object.plans.get(subclient_content[&#34;plan_name&#34;])
            _association_json_ = self._association_json_with_plan(plan_details=planobject)

        discover_info = {
            &#34;discoverByType&#34;: association_dict[association_name.lower()],
            &#34;genericAssociations&#34;: [
                {
                    &#34;associationName&#34;: association_name,
                    &#34;associationType&#34;: association_dict[association_name.lower()]
                }
            ]
        }
        _association_json_[&#34;emailAssociation&#34;][&#34;advanceOptions&#34;][&#34;enableAutoDiscovery&#34;] = True
        _association_json_[&#34;emailAssociation&#34;][&#34;emailStatus&#34;] = 1
        _association_json_[&#34;emailAssociation&#34;][&#34;emailDiscoverinfo&#34;] = discover_info
        self._set_association_request(_association_json_)

    def enable_ews_support(self, service_url):
        &#34;&#34;&#34;This function provides support for EWS protocol to backup on-prem mailboxes
            Args:
                service_url (string) -- EWS Connection URL for your exchange server
            Returns: None
        &#34;&#34;&#34;
        self.agentproperties = self._agent_object.properties
        self.agentproperties[&#34;onePassProperties&#34;][&#34;onePassProp&#34;][&#34;ewsDetails&#34;][&#34;bUseEWS&#34;] = True
        self.agentproperties[&#34;onePassProperties&#34;][&#34;onePassProp&#34;][&#34;ewsDetails&#34;][&#34;ewsConnectionUrl&#34;] = service_url
        self._agent_object.update_properties(self.agentproperties)

    def browse_mailboxes(self, retry_attempts=0):
        &#34;&#34;&#34;
        This function returns the mailboxes available for OOP restore
        return: dictionary containing mailbox info
        &#34;&#34;&#34;
        BROWSE_MAILBOXES = self._commcell_object._services[&#39;EMAIL_DISCOVERY_WITHOUT_REFRESH&#39;] % (
            int(self._backupset_object.backupset_id), &#39;User&#39;
        )
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;GET&#39;, BROWSE_MAILBOXES)
        if flag:
            if response and response.json():
                discover_content = response.json()
                if discover_content.get(&#39;resp&#39;, {}).get(&#39;errorCode&#39;, 0) == 469762468:
                    time.sleep(10)
                    if retry_attempts &gt; 10:
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Failed to perform browse operation.&#39;)
                    return self.browse_mailboxes(retry_attempts + 1)
                if &#39;discoverInfo&#39; in discover_content.keys():
                    if &#39;mailBoxes&#39; in discover_content[&#39;discoverInfo&#39;]:
                        mailboxes = discover_content[&#34;discoverInfo&#34;][&#34;mailBoxes&#34;]
                        return mailboxes
            else:
                raise SDKException(&#34;Response&#34;, &#34;102&#34;)
        else:
            response_string = self.commcell._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def backup_generic_items(self, subclient_content):
        &#34;&#34;&#34;
            Backups the Generic Items for any Exchange Online Client
            GGeneric Items:
                All Public Folders/ All O365 Group ailboxes/ All Users

            Args:
                subclient_content   (list)  List having dictionary of items to be backed up

                subclient_content = [
                    {
                    &#34;associationName&#34; : &#34;All Public Folders&#34;,
                    &#34;associationType&#34;:12
                    },
                    {
                    &#34;associationName&#34; : &#34;All Users&#34;,
                    &#34;associationType&#34;:12
                    }
                ]
        &#34;&#34;&#34;
        task_dict = self._backup_generic_items_json(subclient_content=subclient_content)

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;CREATE_TASK&#39;], task_dict
        )

        return self._process_backup_response(flag, response)

    def backup_mailboxes(self, mailbox_alias_names=None, **kwargs):
        &#34;&#34;&#34;
        Backup specific mailboxes.
        Args:
            mailbox_alias_names(list): alias names of all the mailboxes to backup
                Sample Values:
                    [&#39;aj&#39;, &#39;tkumar&#39;]
             **kwargs (dict) : Additional parameters
                    items_selection_option (str) : Item Selection Option (Example: &#34;7&#34; for selecting backed up recently entities)
        Returns:
            job(Job): instance of job class for the backup job
        &#34;&#34;&#34;
        task_json = self._task_json_for_backup(mailbox_alias_names, **kwargs)
        create_task = self._services[&#39;CREATE_TASK&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, create_task, task_json
        )
        return self._process_backup_response(flag, response)

    def create_recovery_point(self, mailbox_prop, job=None, job_id=None):
        &#34;&#34;&#34;
            Method to create a recovery point

            Arguments:
                mailbox_prop        (dict)--    Dictionary of mailbox properties for which the Recovery point is to be created
                Sample:
                {
                    &#39;mailbox_smtp&#39; : name of the mailbox for which recovery point is to be created
                    &#39;mailbox_guid&#39;: GUID of the mailbox
                    &#39;index_server&#39;: Name of the index server to be used to create index on
                }
                job                 (object)--  Backup Job to which restore point has to be created
                job_id              (int)--     Backup Job ID to which restore point is to be created

                Either pass the job object or the job_id

            Returns:
                res_dict            (dict)--    Dictionary of Response
                Format:
                {
                    &#39;rercovery_point_id&#39; : ID of the recovery point created,
                    &#39;recovery_point_job_id&#39;: Job ID for recovery point creation JOB
                }
        &#34;&#34;&#34;

        if (job == None and job_id == None):
            raise Exception(&#34;At least one value out of job or job_id should be passed&#34;)

        if (job == None and type(job_id) == int):
            job = self._commcell_object.job_controller.get(job_id)

        index_server = self._commcell_object.clients.get(mailbox_prop[&#39;index_server&#39;])
        index_server_id = index_server.client_id

        recovery_point_dict = {
            &#34;opType&#34;: 0,
            &#34;advOptions&#34;: {
                &#34;advConfig&#34;: {
                    &#34;browseAdvancedConfigReq&#34;: {
                        &#34;additionalFlags&#34;: [
                            {
                                &#34;flagType&#34;: 13,
                                &#34;value&#34;: &#34;{}&#34;.format(index_server_id),
                                &#34;key&#34;: &#34;RecoveryPointIndexServer&#34;
                            }
                        ]
                    },
                    &#34;applicationMining&#34;: {
                        &#34;appType&#34;: 137,
                        &#34;isApplicationMiningReq&#34;: True,
                        &#34;browseInitReq&#34;: {
                            &#34;bCreateRecoveryPoint&#34;: True,
                            &#34;jobId&#34;: int(job.job_id),
                            &#34;pointInTimeRange&#34;: {
                                &#34;fromTime&#34;: int(job.start_timestamp),
                                &#34;toTime&#34;: int(job.end_timestamp)
                            },
                            &#34;mbxInfo&#34;: [
                                {
                                    &#34;smtpAdrress&#34;: mailbox_prop[&#39;mailbox_smtp&#39;],
                                    &#34;mbxGUIDs&#34;: mailbox_prop[&#39;mailbox_guid&#39;]
                                }
                            ]
                        }
                    }
                }
            },
            &#34;paths&#34;: [
                {
                    &#34;path&#34;: &#34;\\MB\\{%s}&#34; % mailbox_prop[&#39;mailbox_guid&#39;]
                }
            ],
            &#34;entity&#34;: {
                &#34;subclientId&#34;: int(self.subclient_id),
                &#34;backupsetId&#34;: int(self._backupset_object.backupset_id),
                &#34;clientId&#34;: int(self._client_object.client_id)
            },
            &#34;timeRange&#34;: {
                &#34;fromTime&#34;: 0,
                &#34;toTime&#34;: int(job.start_timestamp)
            }
        }

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;BROWSE&#39;], recovery_point_dict
        )

        if flag:
            if response and response.json():
                browse_response = response.json()

                if &#39;browseResponses&#39; in browse_response.keys():
                    recovery_point_job_id = browse_response.get(&#39;browseResponses&#39;, [{}])[0].get(&#39;browseResult&#39;, {}).get(
                        &#39;advConfig&#39;, {}).get(
                        &#39;applicationMining&#39;, {}).get(&#39;browseInitResp&#39;, {}).get(&#39;recoveryPointJobID&#39;)

                    recovery_point_id = browse_response.get(&#39;browseResponses&#39;, [{}])[0].get(&#39;browseResult&#39;, {}).get(
                        &#39;advConfig&#39;, {}).get(
                        &#39;applicationMining&#39;, {}).get(&#39;browseInitResp&#39;, {}).get(&#39;recoveryPointID&#39;, {})

                    res_dict = {
                        &#39;recovery_point_job_id&#39;: recovery_point_job_id,
                        &#39;recovery_point_id&#39;: recovery_point_id
                    }
                    return res_dict
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;, response.json)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;, response.json)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def refresh(self):
        &#34;&#34;&#34;Refresh the User Mailbox Subclient.&#34;&#34;&#34;
        self._get_subclient_properties()
        self._discover_users = self._get_discover_users()
        self._discover_databases = self._get_discover_database()
        self._discover_adgroups = self._get_discover_adgroups()
        self._users, self._o365groups = self._get_user_assocaitions()
        self._databases = self._get_database_associations()
        self._adgroups = self._get_adgroup_assocaitions()

    def restore_in_place_syntex(self, **kwargs):
        &#34;&#34;&#34;Runs an in-place restore job on the specified Syntex Exchange pseudo client

             Kwargs:

                 paths (list)  --  list of paths of mailboxes/folders to restore

             Returns:

                Job object

            Raises:

                SDKException:

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

        &#34;&#34;&#34;
        paths = kwargs.get(&#39;paths&#39;, [])
        paths = self._filter_paths(paths)
        self._json_restore_exchange_restore_option({})
        self._json_backupset()
        restore_option = {&#34;paths&#34;: paths}

        self._instance_object._restore_association = self._subClientEntity
        request_json = self._restore_json(restore_option=restore_option)
        request_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;subclientName&#39;] = self.subclient_name
        request_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][
            &#39;backupsetName&#39;] = self._backupset_object.backupset_name
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][
            &#39;options&#39;][&#39;restoreOptions&#39;][&#39;exchangeOption&#39;] = self._exchange_option_restore_json
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;browseOption&#34;][&#39;backupset&#39;] = self._exchange_backupset_json

        mailboxes = self.browse_mailboxes()
        mailbox_details = {}
        for path in paths:
            mailbox_details[path] = next((mailbox for mailbox in mailboxes if mailbox[&#39;aliasName&#39;] == path), None)
        syntex_restore_items = []

        for key, value in mailbox_details.items():
            syntex_restore_items.append({
                &#34;displayName&#34;: value[&#34;displayName&#34;],
                &#34;guid&#34;: value[&#34;user&#34;][&#34;userGUID&#34;],
                &#34;rawId&#34;: value[&#34;user&#34;][&#34;userGUID&#34;],
                &#34;restoreType&#34;: 1
            })

        # Get the current time in UTC
        current_time = datetime.datetime.now(datetime.timezone.utc)
        current_timestamp = int(current_time.timestamp())
        current_iso_format = current_time.strftime(&#39;%Y-%m-%dT%H:%M:%S.%f&#39;)[:-3] + &#39;Z&#39;

        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;] = {}
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;][
            &#34;msSyntexRestoreOptions&#34;] = {
            &#34;msSyntexRestoreItems&#34;: {
                &#34;listMsSyntexRestoreItems&#34;: syntex_restore_items
            },
            &#34;restoreDate&#34;: {
                &#34;time&#34;: current_timestamp,
                &#34;timeValue&#34;: current_iso_format
            },
            &#34;restorePointId&#34;: &#34;&#34;,
            &#34;restoreType&#34;: 1,
            &#34;useFastRestorePoint&#34;: False
        }

        return self._process_restore_response(request_json)

    @staticmethod
    def _find_mailbox_query_params(query_params=None):
        &#34;&#34;&#34;
            Generates the query params for find mailbox request
                Args:
                    query_params   (dict)   -- Dict of key value pair
                        Default (None)
                Returns:
                    json for find malilbox query
        &#34;&#34;&#34;
        final_params = []
        default_params = ExchangeConstants.FIND_MBX_QUERY_DEFAULT_PARAMS
        if query_params:
            for param, value in query_params.items():
                if param in default_params:
                    default_params.pop(value)
                final_params.append({&#34;param&#34;: param, &#34;value&#34;: value})
        for param, value in default_params.items():
            final_params.append({&#34;param&#34;: param, &#34;value&#34;: value})
        return final_params

    @staticmethod
    def _find_mailbox_facets(facets=None):
        &#34;&#34;&#34;
            Generates the facet requests for find mailbox request
                Args:
                    facets   (list)   -- List of strings containing facets
                        Default (None)
                Returns:
                    json for find malilbox facets query
        &#34;&#34;&#34;
        final_params = []
        default_facet = ExchangeConstants.FIND_MBX_DEFAULT_FACET

        for facet in facets:
            if facet in default_facet:
                default_facet.remove(facet)
            final_params.append({&#34;name&#34;: facet})
        for facet in default_facet:
            final_params.append({&#34;name&#34;: facet})
        return final_params

    def find_mailbox(self, mailbox_smtp, **kwargs):
        &#34;&#34;&#34;
        Performs the find operation for a mailbox in browse
            Args:
                mailbox_smtp    (str) -- SMTP address of the mailbox to find

                kwargs                -- Optional key word args
            Returns:
                    response json for find mailbox
        &#34;&#34;&#34;

        data = ExchangeConstants.FIND_MAILBOX_REQUEST_DATA

        data[&#34;advSearchGrp&#34;][&#34;emailFilter&#34;][0][&#34;filter&#34;][&#34;filters&#34;].append(
            {
                &#34;field&#34;: &#34;CUSTODIAN&#34;,
                &#34;intraFieldOp&#34;: 0,
                &#34;fieldValues&#34;: {
                    &#34;values&#34;: [
                        mailbox_smtp
                    ]
                }
            }
        )
        data[&#34;advSearchGrp&#34;][&#34;galaxyFilter&#34;][0][&#34;appIdList&#34;] = [int(self.subclient_id)]
        data[&#34;facetRequests&#34;][&#34;facetRequest&#34;] = UsermailboxSubclient._find_mailbox_facets(
            kwargs.get(&#34;facets_params&#34;, None))
        data[&#34;searchProcessingInfo&#34;][&#34;pageSize&#34;] = int(kwargs.get(&#34;page_size&#34;, 100))
        data[&#34;searchProcessingInfo&#34;][&#34;queryParams&#34;] = UsermailboxSubclient._find_mailbox_query_params(
            kwargs.get(&#34;query_params&#34;, None))

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;DO_WEB_SEARCH&#39;], data
        )
        if flag:
            if response and response.json():
                return response.json()
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient"><code class="flex name class">
<span>class <span class="ident">UsermailboxSubclient</span></span>
<span>(</span><span>backupset_object, subclient_name, subclient_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Derived class from ExchangeSubclient Base class.</p>
<p>This represents a usermailbox subclient,
and can perform discover and restore operations on only that subclient.</p>
<p>Initialize the Instance object for the given UserMailbox Subclient.</p>
<h2 id="args">Args</h2>
<p>backupset_object
(object)
&ndash;
instance of the backupset class</p>
<p>subclient_name
(str)
&ndash;
subclient name</p>
<p>subclient_id
(int)
&ndash;
subclient id</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/usermailbox_subclient.py#L105-L1984" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class UsermailboxSubclient(ExchangeSubclient):
    &#34;&#34;&#34;Derived class from ExchangeSubclient Base class.

        This represents a usermailbox subclient,
        and can perform discover and restore operations on only that subclient.

    &#34;&#34;&#34;

    def __init__(self, backupset_object, subclient_name, subclient_id=None):
        &#34;&#34;&#34;Initialize the Instance object for the given UserMailbox Subclient.

            Args:
                backupset_object    (object)    --  instance of the backupset class

                subclient_name      (str)       --  subclient name

                subclient_id        (int)       --  subclient id

        &#34;&#34;&#34;
        super(UsermailboxSubclient, self).__init__(
            backupset_object, subclient_name, subclient_id)

        self._instance_object = backupset_object._instance_object
        self._client_object = self._instance_object._agent_object._client_object
        self._SET_EMAIL_POLICY_ASSOCIATIONS = self._commcell_object._services[
            &#39;SET_EMAIL_POLICY_ASSOCIATIONS&#39;]
        self._SEARCH = (self._commcell_object._services[&#39;EMAIL_DISCOVERY&#39;] %
                        (int(self._backupset_object.backupset_id), &#39;User&#39;))

        self.refresh()

    def _policy_json(self, configuration_policy, policy_type):
        &#34;&#34;&#34;Creates policy Json based on configuration_policy name
        and policy_type

            Args:
                configuration_policy (str/object)  --  configuration policy name or
                object of congiguration policy class
                policy_type   (int)                --  configuration policy type

            Returns:
                list - list of the appropriate JSON for an agent to send to
                       the POST Subclient API
        &#34;&#34;&#34;

        from ...policies.configuration_policies import ConfigurationPolicy
        if not (isinstance(configuration_policy, (str, ConfigurationPolicy))):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if isinstance(configuration_policy, str):
            configuration_policy = ConfigurationPolicy(
                self._commcell_object, configuration_policy)

        policy_json = {
            &#34;policyType&#34;: 1,
            &#34;flags&#34;: 0,
            &#34;agentType&#34;: {
                &#34;appTypeId&#34;: 137
            },
            &#34;detail&#34;: {
                &#34;emailPolicy&#34;: {
                    &#34;emailPolicyType&#34;: policy_type
                }
            },
            &#34;policyEntity&#34;: {
                &#34;policyId&#34;: int(configuration_policy.configuration_policy_id),
                &#34;policyName&#34;: configuration_policy.configuration_policy_name
            }
        }

        return policy_json

    def _association_json(self, subclient_content, is_o365group=False):
        &#34;&#34;&#34;Constructs association json to create assocaition in UserMailbox Subclient.

            Args:
                subclient_content (dict)  --  dict of the Users to add to the subclient
                                             (dict of only policies in case of Office 365 groups)
                subclient_content = {

                        &#39;archive_policy&#39; : &#34;CIPLAN Archiving policy&#34;,

                        &#39;cleanup_policy&#39; : &#39;CIPLAN Clean-up policy&#39;,

                        &#39;retention_policy&#39;: &#39;CIPLAN Retention policy&#39;
                    }


            Returns:
                dict -- Association JSON request to pass to the API
        &#34;&#34;&#34;
        policy_types = {
            &#34;archive_policy&#34;: 1,
            &#34;cleanup_policy&#34;: 2,
            &#34;retention_policy&#34;: 3
        }

        email_policies = []

        if &#39;archive_policy&#39; in subclient_content:
            email_policies.append(self._policy_json(subclient_content.get(
                &#39;archive_policy&#39;), policy_types[&#39;archive_policy&#39;]))
        if &#39;cleanup_policy&#39; in subclient_content:
            email_policies.append(self._policy_json(subclient_content.get(
                &#39;cleanup_policy&#39;), policy_types[&#39;cleanup_policy&#39;]))
        if &#39;retention_policy&#39; in subclient_content:
            email_policies.append(self._policy_json(subclient_content.get(
                &#39;retention_policy&#39;), policy_types[&#39;retention_policy&#39;]))

        associations_json = {
            &#34;emailAssociation&#34;: {
                &#34;advanceOptions&#34;: {
                    &#34;enableAutoDiscovery&#34;: subclient_content.get(&#34;is_auto_discover_user&#34;,
                                                                 is_o365group)
                },
                &#34;subclientEntity&#34;: self._subClientEntity,
                &#34;policies&#34;: {
                    &#34;emailPolicies&#34;: email_policies
                }
            }
        }

        return associations_json

    def _association_json_with_plan(self, plan_details):
        &#34;&#34;&#34;Constructs association json with plan to create association in UserMailbox Subclient.
        
            Args: plan_details = {
                    &#39;plan_name&#39;: Plan Name,
                    &#39;plan_id&#39;: int or None (Optional)
                    }
                 Returns:
                    dict -- Association JSON request to pass to the API
        &#34;&#34;&#34;

        try:
            if not self._commcell_object.plans.has_plan(plan_details[&#39;plan_name&#39;]):
                raise SDKException(&#39;Subclient&#39;, &#39;102&#39;,
                                   &#39;Plan Name {} not found&#39;.format(plan_details[&#39;plan_name&#39;]))
            if &#39;plan_id&#39; not in plan_details or plan_details[&#39;plan_id&#39;] is None:
                plan_id = self._commcell_object.plans[plan_details[&#39;plan_name&#39;].lower()]
            else:
                plan_id = plan_details[&#39;plan_id&#39;]

        except KeyError as err:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))

        plan_details = {
            &#39;planId&#39;: int(plan_id)
        }

        association_json = {
            &#34;emailAssociation&#34;: {
                &#34;subclientEntity&#34;: self._subClientEntity,
                &#34;plan&#34;: plan_details
            }
        }
        return association_json

    def _association_mailboxes_json(self, mailbox_alias_names):
        &#34;&#34;&#34;
            Args:
                mailbox_alias_names(list): alias names of the mailboxes to backup
                    Example:
                        [&#39;aj&#39;, &#39;tkumar&#39;]
            Returns:
                mailboxes_json(list): Required details of mailboxes to backup
        &#34;&#34;&#34;
        mailboxes_json = []
        mailbox_alias_names = set(mailbox_alias_names)
        associated_mailboxes = self._users + self._o365groups

        for user in associated_mailboxes:
            if user[&#39;alias_name&#39;] in mailbox_alias_names:
                mailbox_info = {
                    &#34;aliasName&#34;: user[&#34;alias_name&#34;],
                    &#34;mailBoxType&#34;: user[&#39;mailbox_type&#39;],
                    &#34;databaseName&#34;: user[&#39;database_name&#39;],
                    &#34;displayName&#34;: user[&#39;display_name&#39;],
                    &#34;smtpAddress&#34;: user[&#39;smtp_address&#39;],
                    &#34;isAutoDiscoveredUser&#34;: True if user[&#39;is_auto_discover_user&#39;].lower() == &#39;true&#39; else False,
                    &#34;msExchRecipientTypeDetails&#34;: user[&#39;mailbox_type&#39;],
                    &#34;exchangeVersion&#34;: user[&#39;exchange_version&#39;],
                    &#34;exchangeServer&#34;: user[&#39;exchange_server&#39;],
                    &#34;lastArchiveJobRanTime&#34;: user[&#39;last_archive_job_ran_time&#39;],
                    &#34;user&#34;: {
                        &#34;userGUID&#34;: user[&#39;user_guid&#39;]
                    }
                }
                mailboxes_json.append(mailbox_info)
        return mailboxes_json

    def _task_json_for_backup(self, mailbox_alias_names, **kwargs):
        &#34;&#34;&#34;
        Args:
            mailbox_alias_names(list): alias names of the mailboxes to backup
                Sample Values
                    [&#39;aj&#39;, &#39;tkumar&#39;]

             **kwargs (dict) : Additional parameters
                items_selection_option (str) : Item Selection Option (Example: &#34;7&#34; for selecting backed up recently entities)
        Returns:
            task_json(dict): Task json required to pass to the API
        &#34;&#34;&#34;
        common_backup_options = None
        items_selection_option = kwargs.get(&#39;items_selection_option&#39;, &#39;&#39;)
        if items_selection_option != &#39;&#39;:
            common_backup_options = {&#39;itemsSelectionOption&#39;: items_selection_option}
        task_json = self._backup_json(backup_level=&#39;INCREMENTAL&#39;, incremental_backup=False,incremental_level=&#39;BEFORE_SYNTH&#39;,common_backup_options=common_backup_options)
        if mailbox_alias_names != None:
            associated_mailboxes_json = self._association_mailboxes_json(mailbox_alias_names)
            backup_options = {
                &#39;backupLevel&#39;: 2,  # Incremental
                &#39;incLevel&#39;: 1,
                &#39;exchOnePassOptions&#39;: {
                    &#39;mailBoxes&#39;: associated_mailboxes_json
                }
            }
            data_options = {
                &#34;useCatalogServer&#34;: False,
                &#34;followMountPoints&#34;: True,
                &#34;enforceTransactionLogUsage&#34;: False,
                &#34;skipConsistencyCheck&#34;: True,
                &#34;createNewIndex&#34;: False
            }
            task_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;backupOpts&#39;] = backup_options
            task_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;dataOpt&#39;] = data_options
        return task_json

    def _set_association_request(self, associations_json):
        &#34;&#34;&#34;
            Runs the emailAssociation POST API to set association

            Args:
                associations_json    (dict)  -- request json sent as payload

            Returns:
                (str, str):
                    str  -  error code received in the response

                    str  -  error message received

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._SET_EMAIL_POLICY_ASSOCIATIONS, associations_json
        )

        if flag:
            try:
                if response.json():
                    if response.json()[&#39;resp&#39;][&#39;errorCode&#39;] != 0:
                        error_message = response.json()[&#39;errorMessage&#39;]
                        output_string = &#39;Failed to create assocaition\nError: &#34;{0}&#34;&#39;
                        raise SDKException(
                            &#39;Subclient&#39;, &#39;102&#39;, output_string.format(error_message)
                        )
                    else:
                        self.refresh()
            except ValueError:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _update_association_request(self, associations_json):
        &#34;&#34;&#34;Runs the EmailAssocaition PUT API to update association

            Args:
                associations_json  (dict)  -- request json sent as payload

            Returns:
                (str, str):
                    str  -  error code received in the response

                    str  -  error message received

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;PUT&#39;, self._SET_EMAIL_POLICY_ASSOCIATIONS, associations_json
        )

        if flag:
            try:
                if response.json():
                    if response.json()[&#39;resp&#39;][&#39;errorCode&#39;] != 0:
                        error_message = response.json()[&#39;errorMessage&#39;]
                        output_string = &#39;Failed to create assocaition\nError: &#34;{0}&#34;&#39;
                        raise SDKException(
                            &#39;Subclient&#39;, &#39;102&#39;, output_string.format(error_message)
                        )
                    else:
                        self.refresh()
            except ValueError:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _get_discover_users(self, use_without_refresh_url=False, retry_attempts=0):
        &#34;&#34;&#34;Gets the discovered users from the Subclient .

            Args:
                use_without_refresh_url (boolean)   -   discovery without refresh cache

                retry_attempts(int)                 - retry for discovery

            Returns:
                list    -   list of discovered users associated with the subclient

        &#34;&#34;&#34;
        self._DISCOVERY = self._commcell_object._services[&#39;EMAIL_DISCOVERY&#39;] % (
            int(self._backupset_object.backupset_id), &#39;User&#39;
        )

        if use_without_refresh_url:
            self._DISCOVERY = self._commcell_object._services[&#39;EMAIL_DISCOVERY_WITHOUT_REFRESH&#39;] % (
                int(self._backupset_object.backupset_id), &#39;User&#39;
            )
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;GET&#39;, self._DISCOVERY)

        if flag:
            if response and response.json():
                discover_content = response.json()

                _error_code = discover_content.get(&#39;resp&#39;, {}).get(&#39;errorCode&#39;, 0)
                if _error_code == 469762468 or _error_code == 469762470:
                    # if discover_content.get(&#39;resp&#39;, {}).get(&#39;errorCode&#39;, 0) == 469762468:
                    time.sleep(10)  # the results might take some time depending on domains
                    if retry_attempts &gt; 10:
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Failed to perform discovery.&#39;)

                    return self._get_discover_users(use_without_refresh_url=True,
                                                    retry_attempts=retry_attempts + 1)

                if &#39;discoverInfo&#39; in discover_content.keys():
                    if &#39;mailBoxes&#39; in discover_content[&#39;discoverInfo&#39;]:
                        self._discover_users = discover_content[&#39;discoverInfo&#39;][&#39;mailBoxes&#39;]

                        return self._discover_users
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _search_user(self, user, retry_attempts=0):
        &#34;&#34;&#34;Searches for the user in the discovered users list .
            Args:
                user (str)              -   alias name/smtp address of the user to search for
            Returns:
                list    -   list of discovered users matching the search

        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;GET&#39;, f&#34;{self._SEARCH}&amp;search={user}&#34;)

        if flag:
            if response and response.json():
                search_content = response.json()

                _error_code = search_content.get(&#39;resp&#39;, {}).get(&#39;errorCode&#39;, 0)
                if _error_code == 469762468 or _error_code == 469762470:
                    # if search_content.get(&#39;resp&#39;, {}).get(&#39;errorCode&#39;, 0) == 469762468:
                    time.sleep(10)  # the results might take some time depending on domains
                    if retry_attempts &gt; 10:
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Failed to perform search and discovery.&#39;)
                    return self._search_user(user, retry_attempts=retry_attempts + 1)

                return search_content.get(&#39;discoverInfo&#39;, {}).get(&#39;mailBoxes&#39;, [])
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _get_discover_database(self):
        &#34;&#34;&#34;Gets the discovered databases from the Subclient .

            Returns:
                list    -   list of discovered databases associated with the subclient

        &#34;&#34;&#34;
        self._DISCOVERY = self._commcell_object._services[&#39;EMAIL_DISCOVERY&#39;] % (
            int(self._backupset_object.backupset_id), &#39;Database&#39;
        )

        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;GET&#39;, self._DISCOVERY)

        if flag:
            discover_content = response.json()
            if &#39;discoverInfo&#39; in discover_content.keys():
                if &#39;databases&#39; in discover_content[&#39;discoverInfo&#39;]:
                    discover_content = discover_content[&#39;discoverInfo&#39;][&#39;databases&#39;]
                return discover_content

        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _get_discover_adgroups(self):
        &#34;&#34;&#34;Gets the discovered adgroups from the Subclient .

            Returns:
                list    -   list of discovered adgroups associated with the subclient

        &#34;&#34;&#34;
        self._DISCOVERY = self._commcell_object._services[&#39;EMAIL_DISCOVERY&#39;] % (
            int(self._backupset_object.backupset_id), &#39;AD Group&#39;
        )
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;GET&#39;, self._DISCOVERY)

        if flag:
            discover_content = response.json()
            if &#39;discoverInfo&#39; in discover_content.keys():

                if &#39;adGroups&#39; in discover_content[&#39;discoverInfo&#39;]:
                    discover_content = discover_content[&#39;discoverInfo&#39;][&#39;adGroups&#39;]

                return discover_content

        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _get_user_assocaitions(self):
        &#34;&#34;&#34;Gets the appropriate users associations from the Subclient.

            Returns:
                list    -   list of users and groups associated with the subclient

        &#34;&#34;&#34;
        users = []
        groups = []

        self._EMAIL_POLICY_ASSOCIATIONS = self._commcell_object._services[
                                              &#39;GET_EMAIL_POLICY_ASSOCIATIONS&#39;] % (self.subclient_id, &#39;User&#39;)

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._EMAIL_POLICY_ASSOCIATIONS
        )

        if flag:
            subclient_content = response.json()

            if &#39;associations&#39; in subclient_content:
                children = subclient_content[&#39;associations&#39;]

                for child in children:
                    archive_policy = None
                    cleanup_policy = None
                    retention_policy = None
                    plan_name = None
                    plan_id = None
                    display_name = str(child[&#39;userMailBoxInfo&#39;][&#39;displayName&#39;])
                    alias_name = str(child[&#39;userMailBoxInfo&#39;][&#39;aliasName&#39;])
                    smtp_address = str(child[&#39;userMailBoxInfo&#39;][&#39;smtpAdrress&#39;])
                    database_name = str(child[&#39;userMailBoxInfo&#39;][&#39;databaseName&#39;])
                    exchange_server = str(child[&#39;userMailBoxInfo&#39;][&#39;exchangeServer&#39;])
                    user_guid = str(child[&#39;userMailBoxInfo&#39;][&#39;user&#39;][&#39;userGUID&#39;])
                    is_auto_discover_user = str(child[&#39;userMailBoxInfo&#39;][&#39;isAutoDiscoveredUser&#39;])
                    mailbox_type = int(child[&#39;userMailBoxInfo&#39;][&#39;msExchRecipientTypeDetails&#39;])
                    exchange_version = int(child[&#39;userMailBoxInfo&#39;][&#39;exchangeVersion&#39;])
                    last_archive_job_ran_time = child[&#39;userMailBoxInfo&#39;][&#39;lastArchiveJobRanTime&#39;]
                    if &#39;emailPolicies&#39; in child[&#39;policies&#39;]:
                        for policy in child[&#39;policies&#39;][&#39;emailPolicies&#39;]:
                            if policy[&#39;detail&#39;].get(&#39;emailPolicy&#39;, {}).get(&#39;emailPolicyType&#39;) == 1:
                                archive_policy = str(policy[&#39;policyEntity&#39;][&#39;policyName&#39;])
                            elif policy[&#39;detail&#39;].get(&#39;emailPolicy&#39;, {}).get(&#39;emailPolicyType&#39;) == 2:
                                cleanup_policy = str(policy[&#39;policyEntity&#39;][&#39;policyName&#39;])
                            elif policy[&#39;detail&#39;].get(&#39;emailPolicy&#39;, {}).get(&#39;emailPolicyType&#39;) == 3:
                                retention_policy = str(policy[&#39;policyEntity&#39;][&#39;policyName&#39;])
                    if &#39;plan&#39; in child:
                        plan_name = child.get(&#39;plan&#39;).get(&#39;planName&#39;)
                        plan_id = child.get(&#39;plan&#39;).get(&#39;planId&#39;)

                    temp_dict = {
                        &#39;display_name&#39;: display_name,
                        &#39;alias_name&#39;: alias_name,
                        &#39;smtp_address&#39;: smtp_address,
                        &#39;database_name&#39;: database_name,
                        &#39;exchange_server&#39;: exchange_server,
                        &#39;user_guid&#39;: user_guid,
                        &#39;is_auto_discover_user&#39;: is_auto_discover_user,
                        &#39;archive_policy&#39;: archive_policy,
                        &#39;cleanup_policy&#39;: cleanup_policy,
                        &#39;retention_policy&#39;: retention_policy,
                        &#39;plan_name&#39;: plan_name,
                        &#39;plan_id&#39;: plan_id,
                        &#39;mailbox_type&#39;: mailbox_type,
                        &#39;exchange_version&#39;: exchange_version,
                        &#39;last_archive_job_ran_time&#39;: last_archive_job_ran_time
                    }
                    if int(child[&#39;userMailBoxInfo&#39;][&#39;msExchRecipientTypeDetails&#39;]) == 36:
                        groups.append(temp_dict)
                    else:
                        users.append(temp_dict)

        return users, groups

    def _get_database_associations(self):
        &#34;&#34;&#34;Gets the appropriate database association from the Subclient.

            Returns:
                list    -   list of database associated with the subclient

        &#34;&#34;&#34;
        databases = []

        self._EMAIL_POLICY_ASSOCIATIONS = self._commcell_object._services[
                                              &#39;GET_EMAIL_POLICY_ASSOCIATIONS&#39;] % (self.subclient_id, &#39;Database&#39;)

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._EMAIL_POLICY_ASSOCIATIONS
        )

        if flag:
            subclient_content = response.json()

            if &#39;associations&#39; in subclient_content:
                children = subclient_content[&#39;associations&#39;]

                for child in children:
                    database_name = str(child[&#39;databaseInfo&#39;][&#39;databaseName&#39;])
                    exchange_server = str(child[&#39;databaseInfo&#39;][&#39;exchangeServer&#39;])
                    archive_policy = None
                    cleanup_policy = None
                    retention_policy = None
                    is_auto_discover_user = str(child[&#39;additionalOptions&#39;][&#39;enableAutoDiscovery&#39;])

                    for policy in child[&#39;policies&#39;][&#39;emailPolicies&#39;]:
                        if policy[&#39;detail&#39;][&#39;emailPolicy&#39;][&#39;emailPolicyType&#39;] == 1:
                            archive_policy = str(policy[&#39;policyEntity&#39;][&#39;policyName&#39;])
                        elif policy[&#39;detail&#39;][&#39;emailPolicy&#39;][&#39;emailPolicyType&#39;] == 2:
                            cleanup_policy = str(policy[&#39;policyEntity&#39;][&#39;policyName&#39;])
                        elif policy[&#39;detail&#39;][&#39;emailPolicy&#39;][&#39;emailPolicyType&#39;] == 3:
                            retention_policy = str(policy[&#39;policyEntity&#39;][&#39;policyName&#39;])

                    temp_dict = {
                        &#39;database_name&#39;: database_name,
                        &#39;exchange_server&#39;: exchange_server,
                        &#39;is_auto_discover_user&#39;: is_auto_discover_user,
                        &#39;archive_policy&#39;: archive_policy,
                        &#39;cleanup_policy&#39;: cleanup_policy,
                        &#39;retention_policy&#39;: retention_policy
                    }

                    databases.append(temp_dict)

        return databases

    def _get_adgroup_assocaitions(self):
        &#34;&#34;&#34;Gets the appropriate adgroup assocaitions from the Subclient.

            Returns:
                list    -   list of adgroups associated with the subclient

        &#34;&#34;&#34;
        adgroups = []

        self._EMAIL_POLICY_ASSOCIATIONS = self._commcell_object._services[
                                              &#39;GET_EMAIL_POLICY_ASSOCIATIONS&#39;] % (self.subclient_id, &#39;AD Group&#39;)

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._EMAIL_POLICY_ASSOCIATIONS
        )

        if flag:
            subclient_content = response.json()

            if &#39;associations&#39; in subclient_content:
                children = subclient_content[&#39;associations&#39;]

                for child in children:
                    archive_policy = None
                    cleanup_policy = None
                    retention_policy = None
                    adgroup_name = str(child[&#39;adGroupsInfo&#39;][&#39;adGroupName&#39;])
                    is_auto_discover_user = str(child[&#39;additionalOptions&#39;][&#39;enableAutoDiscovery&#39;])

                    for policy in child[&#39;policies&#39;][&#39;emailPolicies&#39;]:
                        if policy[&#39;detail&#39;][&#39;emailPolicy&#39;][&#39;emailPolicyType&#39;] == 1:
                            archive_policy = str(policy[&#39;policyEntity&#39;][&#39;policyName&#39;])
                        elif policy[&#39;detail&#39;][&#39;emailPolicy&#39;][&#39;emailPolicyType&#39;] == 2:
                            cleanup_policy = str(policy[&#39;policyEntity&#39;][&#39;policyName&#39;])
                        elif policy[&#39;detail&#39;][&#39;emailPolicy&#39;][&#39;emailPolicyType&#39;] == 3:
                            retention_policy = str(policy[&#39;policyEntity&#39;][&#39;policyName&#39;])

                    temp_dict = {
                        &#39;adgroup_name&#39;: adgroup_name,
                        &#39;is_auto_discover_user&#39;: is_auto_discover_user,
                        &#39;archive_policy&#39;: archive_policy,
                        &#39;cleanup_policy&#39;: cleanup_policy,
                        &#39;retention_policy&#39;: retention_policy
                    }

                    adgroups.append(temp_dict)

        return adgroups

    def _backup_generic_items_json(self, subclient_content):
        &#34;&#34;&#34;
            Create the JSON for Backing Up the Generic Items of any Exchange Online Client

            Args:
                subclient_content   (list)  List having dictionary of items to be backed up

                subclient_content = [
                    {
                    &#34;associationName&#34; : &#34;All Public Folders&#34;,
                    &#34;associationType&#34;:12
                    },
                    {
                    &#34;associationName&#34; : &#34;All Users&#34;,
                    &#34;associationType&#34;:12
                    }
                ]

            Returns:
                The JSON to create a backup task
        &#34;&#34;&#34;

        task_dict = {
            &#34;taskInfo&#34;: {
                &#34;associations&#34;: [
                    self._subClientEntity
                ],
                &#34;task&#34;: {
                    &#34;taskType&#34;: 1
                },
                &#34;subTasks&#34;: [
                    {
                        &#34;subTask&#34;: {
                            &#34;subTaskType&#34;: 2,
                            &#34;operationType&#34;: 2
                        },
                        &#34;options&#34;: {
                            &#34;backupOpts&#34;: {
                                &#34;backupLevel&#34;: 1,
                                &#34;incLevel&#34;: 1,
                                &#34;exchOnePassOptions&#34;: {
                                    &#34;genericAssociations&#34;: [
                                    ]
                                }
                            }
                        }
                    }
                ]
            }
        }
        task_dict[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;backupOpts&#34;][&#34;exchOnePassOptions&#34;][
            &#34;genericAssociations&#34;] = subclient_content
        return task_dict

    @property
    def discover_users(self):
        &#34;&#34;&#34;&#34;Returns the list of discovered users for the UserMailbox subclient.&#34;&#34;&#34;
        return self._discover_users

    @property
    def discover_databases(self):
        &#34;&#34;&#34;Returns the list of discovered databases for the UserMailbox subclient.&#34;&#34;&#34;
        return self._discover_databases

    @property
    def discover_adgroups(self):
        &#34;&#34;&#34;Returns the list of discovered AD groups for the UserMailbox subclient.&#34;&#34;&#34;
        return self._discover_adgroups

    @property
    def users(self):
        &#34;&#34;&#34;Returns the list of users associated with UserMailbox subclient.&#34;&#34;&#34;
        return self._users

    @property
    def databases(self):
        &#34;&#34;&#34;Returns the list of databases associated with the UserMailbox subclient.&#34;&#34;&#34;
        return self._databases

    @property
    def adgroups(self):
        &#34;&#34;&#34;Returns the list of AD groups associated with the UserMailbox subclient.&#34;&#34;&#34;
        return self._adgroups

    @property
    def o365groups(self):
        &#34;&#34;&#34;Returns the list of discovered O365 groups for the UserMailbox subclient.&#34;&#34;&#34;
        return self._o365groups

    def set_user_assocaition(self, subclient_content, use_policies=True):
        &#34;&#34;&#34;Create User assocaition for UserMailboxSubclient.

            Args:
                subclient_content   (dict)  --  dict of the Users to add to the subclient

                    subclient_content = {

                        &#39;mailboxNames&#39; : [&#34;AutoCi2&#34;],,

                        -- if use_policies is True --

                        &#39;archive_policy&#39; : &#34;CIPLAN Archiving policy&#34;,

                        &#39;cleanup_policy&#39; : &#39;CIPLAN Clean-up policy&#39;,

                        &#39;retention_policy&#39;: &#39;CIPLAN Retention policy&#39;

                        -- if use_policies is False --

                        &#39;plan_name&#39;: &#39;Exchange Plan Name&#39;,

                        &#39;plan_id&#39;: int or None (Optional)
                        --
                    }

        &#34;&#34;&#34;
        users = []

        if not isinstance(subclient_content, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if not (isinstance(subclient_content[&#39;mailboxNames&#39;], list)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        try:
            discover_users = self.discover_users

            for mailbox_item in subclient_content[&#39;mailboxNames&#39;]:

                for mb_item in discover_users:

                    if mailbox_item.lower() == mb_item[&#39;aliasName&#39;].lower() or \
                            mailbox_item.lower() == mb_item[&#39;smtpAdrress&#39;].lower():
                        mailbox_dict = {
                            &#39;smtpAdrress&#39;: mb_item[&#39;smtpAdrress&#39;],
                            &#39;aliasName&#39;: mb_item[&#39;aliasName&#39;],
                            &#39;mailBoxType&#39;: mb_item[&#39;mailBoxType&#39;],
                            &#39;displayName&#39;: mb_item[&#39;displayName&#39;],
                            &#39;exchangeServer&#39;: mb_item[&#39;exchangeServer&#39;],
                            &#39;isAutoDiscoveredUser&#39;: mb_item[&#39;isAutoDiscoveredUser&#39;],
                            &#34;associated&#34;: False,
                            &#39;databaseName&#39;: mb_item[&#39;databaseName&#39;],
                            &#34;exchangeVersion&#34;: mb_item[&#39;exchangeVersion&#39;],
                            # &#34;msExchRecipientTypeDetails&#34;: mb_item[&#39;msExchRecipientTypeDetails&#39;],
                            &#39;user&#39;: {
                                &#39;_type_&#39;: 13,
                                &#39;userGUID&#39;: mb_item[&#39;user&#39;][&#39;userGUID&#39;]
                            }
                        }
                        users.append(mailbox_dict)
                        break

        except KeyError as err:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))

        if len(users) != len(subclient_content[&#39;mailboxNames&#39;]):
            to_search_user = [s_user for s_user in subclient_content[&#39;mailboxNames&#39;]
                              if not any(s_user == f_user[&#39;smtpAdrress&#39;] for f_user in users)]
            for mailbox_item in to_search_user:
                search_users = self._search_user(mailbox_item)
                if len(search_users) != 0:
                    users.append(search_users[0])

        discover_info = {
            &#34;discoverByType&#34;: 1,
            &#34;mailBoxes&#34;: users
        }
        if use_policies:
            _association_json_ = self._association_json(subclient_content)
        else:
            _association_json_ = self._association_json_with_plan(subclient_content)
        _association_json_[&#34;emailAssociation&#34;][&#34;emailDiscoverinfo&#34;] = discover_info
        self._set_association_request(_association_json_)

    def set_pst_association(self, subclient_content):
        &#34;&#34;&#34;Create PST assocaition for UserMailboxSubclient.

            Args:
                subclient_content   (dict)  --  dict of the pst to add to the subclient

                    subclient_content = {

                        &#39;pstTaskName&#39; : &#34;Task Name for PST&#34;,

                        &#39;folders&#39; : [&#39;list of folders&#39;] //If pst ingestion by folder location,
                        &#39;fsContent&#39;: Dictionary of client, backupset, subclient
                        Ex: {&#39;client1&#39;:{&#39;backupset1&#39;:[subclient1], &#39;backupset2&#39;:None},
                            &#39;client2&#39;: None}
                        This would add subclient1, all subclients under backupset2 and
                        all backupsets under client2 to the association

                        &#39;pstOwnerManagement&#39; : {

                            &#39;defaultOwner&#39;: &#34;default owner if no owner is determined&#34;,

                            &#39;pstDestFolder&#39;: &#34;ingest psts under this folder&#34;,

                            &#39;usePSTNameToCreateChild&#39;: Boolean
                        }
                    }
        &#34;&#34;&#34;
        if not isinstance(subclient_content, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        try:
            if &#39;ownerSelectionOrder&#39; not in subclient_content[&#39;pstOwnerManagement&#39;]:
                subclient_content[&#39;pstOwnerManagement&#39;][&#39;ownerSelectionOrder&#39;] = [4, 1, 3]
            if &#39;createPstDestFolder&#39; not in subclient_content[&#39;pstOwnerManagement&#39;]:
                subclient_content[&#39;pstOwnerManagement&#39;][&#39;createPstDestFolder&#39;] = True
            if &#39;pstDestFolder&#39; not in subclient_content[&#39;pstOwnerManagement&#39;]:
                subclient_content[&#39;pstOwnerManagement&#39;][&#39;pstDestFolder&#39;] = (f&#39;Archived From &#39;
                                                                            f&#39;Automation&#39;)

            pst_dict = {
                &#39;pstTaskName&#39;: subclient_content[&#39;pstTaskName&#39;],
                &#39;taskType&#39;: 1,
                &#39;pstOwnerManagement&#39;: {
                    &#39;adProperty&#39;: &#34;&#34;,
                    &#39;startingFolderPath&#39;: &#34;&#34;,
                    &#39;pstStubsAction&#39;: 1,
                    &#39;managePSTStubs&#39;: False,
                    &#39;mergeintoMailBox&#39;: True,
                    &#39;pstOwnerBasedOnACL&#39;: True,
                    &#39;pstOwnerBasedOnLaptop&#39;: False,
                    &#39;usePSTNameToCreateChildForNoOwner&#39;: True,
                    &#39;createPstDestFolder&#39;:
                        subclient_content[&#34;pstOwnerManagement&#34;][&#34;createPstDestFolder&#34;],
                    &#39;orphanFolder&#39;: subclient_content[&#39;pstOwnerManagement&#39;][&#39;defaultOwner&#39;],
                    &#39;pstDestFolder&#39;: subclient_content[&#39;pstOwnerManagement&#39;][&#39;pstDestFolder&#39;],
                    &#39;usePSTNameToCreateChild&#39;:
                        subclient_content[&#39;pstOwnerManagement&#39;][&#39;usePSTNameToCreateChild&#39;],
                    &#39;ownerSelectionOrder&#39;:
                        subclient_content[&#34;pstOwnerManagement&#34;][&#34;ownerSelectionOrder&#34;]
                }
            }
            if &#39;folders&#39; in subclient_content:
                pst_dict[&#39;folders&#39;] = subclient_content[&#39;folders&#39;]
            elif &#39;fsContent&#39; in subclient_content:
                pst_dict[&#39;associations&#39;] = self.set_fs_association_for_pst(
                    subclient_content[&#39;fsContent&#39;])
                pst_dict[&#39;taskType&#39;] = 0
            subclient_entity = {&#34;_type_&#34;: 7, &#34;subclientId&#34;: int(self._subclient_id)}
            discover_info = {
                &#39;discoverByType&#39;: 9,
                &#39;pstIngestion&#39;: pst_dict
            }
            _assocaition_json_ = {
                &#34;emailAssociation&#34;:
                    {
                        &#34;emailDiscoverinfo&#34;: discover_info,
                        &#34;subclientEntity&#34;: subclient_entity
                    }
            }
            self._set_association_request(_assocaition_json_)

        except KeyError as err:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))

    def set_fs_association_for_pst(self, association):
        &#34;&#34;&#34;Helper method to create pst association for PST Ingestion by FS
            Args:
                association(dict) -- Dictionary of client, backupset, subclient
                                    Ex: {&#39;client1&#39;:{&#39;backupset1&#39;:[subclient1], &#39;backupset2&#39;:None},
                                        &#39;client2&#39;: None}
                                    This would add subclient1, all subclients under backupset2 and
                                    all backupsets under client2 to the association
        &#34;&#34;&#34;
        assoc_list = []
        client_dict, backupset_dict, sub_dict = dict(), dict(), dict()
        _type_id = {&#34;client&#34;: 3, &#34;subclient&#34;: 7, &#34;backupset&#34;: 6, &#34;apptype&#34;: 4}
        for client_name, backupsets in association.items():
            client_name = client_name.lower()
            client_obj = self._commcell_object.clients.get(client_name)

            client_dict = {&#34;commCellId&#34;: int(self._commcell_object._id),
                           &#34;commcellName&#34;: self._commcell_object.commserv_name,
                           &#34;clientName&#34;: client_name,
                           &#34;clientId&#34;: int(client_obj.client_id)
                           }
            agent = client_obj.agents.get(&#34;file system&#34;)

            if backupsets:
                for backupset_name, subclients in backupsets.items():
                    backupset_name = backupset_name.lower()
                    backupset_obj = agent.backupsets.get(backupset_name)
                    if not backupset_obj:
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#34;Backupset {0} not present in &#34;
                                                               &#34;&#34;.format(backupset_name))
                    backupset_dict = {&#34;backupsetName&#34;: backupset_obj.name,
                                      &#34;appName&#34;: &#34;File System&#34;,
                                      &#34;applicationId&#34;: int(agent.agent_id),
                                      &#34;backupsetId&#34;: int(backupset_obj.backupset_id),
                                      &#34;_type_&#34;: _type_id[&#34;backupset&#34;]
                                      }
                    backupset_dict.update(client_dict)
                    for subclient_name in subclients:
                        if subclient_name.lower() not in backupset_obj.subclients.all_subclients:
                            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;,
                                               &#34;Subclient %s not present in backupset %s&#34; %
                                               (str(subclient_name), str(backupset_name)))
                        subclient_name = subclient_name.lower()
                        sub_dict = {&#34;subclientId&#34;: int(backupset_obj.subclients.all_subclients[
                                                           subclient_name][&#39;id&#39;]),
                                    &#34;subclientName&#34;: subclient_name}
                        sub_dict.update(backupset_dict)
                        sub_dict[&#34;_type_&#34;] = _type_id[&#34;subclient&#34;]
                        assoc_list.append(sub_dict)
                    if not subclients:
                        assoc_list.append(backupset_dict)
            else:
                client_dict[&#34;_type_&#34;] = _type_id[&#34;client&#34;]
                assoc_list.append(client_dict)
        return assoc_list

    def set_database_assocaition(self, subclient_content, use_policies=True):
        &#34;&#34;&#34;Create Database assocaition for UserMailboxSubclient.

            Args:
                subclient_content   (dict)  --  dict of the databases to add to the subclient

                    For policy -
                    subclient_content = {

                        &#39;databaseNames&#39; : [&#34;Name of dbs as list&#34;],

                        &#39;is_auto_discover_user&#39; : True,

                        &#39;archive_policy&#39; : &#34;CIPLAN Archiving policy&#34;,

                        &#39;cleanup_policy&#39; : &#39;CIPLAN Clean-up policy&#39;,

                        &#39;retention_policy&#39;: &#39;CIPLAN Retention policy&#39;
                    }

                    For Plan -
                    subclient_content = { &#39;is_auto_discover_user&#39;: True,
                    &#34;plan_name&#34;: Name of plan, &#34;plan_id&#34;(optional) -- id of plan}

                use_policies(bool)  -- if we need to use policy or plan
                
                
        &#34;&#34;&#34;
        databases = []

        if not isinstance(subclient_content, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if not (isinstance(subclient_content[&#39;databaseNames&#39;], list) and
                isinstance(subclient_content[&#39;is_auto_discover_user&#39;], bool)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        try:
            discover_databases = self.discover_databases

            for database_item in subclient_content[&#39;databaseNames&#39;]:

                for db_item in discover_databases:

                    if database_item.lower() == db_item[&#39;databaseName&#39;].lower():
                        database_dict = {
                            &#39;exchangeServer&#39;: db_item[&#39;exchangeServer&#39;],
                            &#34;associated&#34;: False,
                            &#39;databaseName&#39;: db_item[&#39;databaseName&#39;],
                        }
                        databases.append(database_dict)

        except KeyError as err:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))

        discover_info = {
            &#34;discoverByType&#34;: 2,
            &#34;databases&#34;: databases
        }
        if use_policies:
            _association_json_ = self._association_json(subclient_content)
        else:
            _association_json_ = self._association_json_with_plan(subclient_content)
            _association_json_[&#34;emailAssociation&#34;][&#34;advanceOptions&#34;] = {
                &#34;enableAutoDiscovery&#34;: subclient_content.get(&#34;is_auto_discover_user&#34;, False)
            }
        _association_json_[&#34;emailAssociation&#34;][&#34;emailDiscoverinfo&#34;] = discover_info
        self._set_association_request(_association_json_)

    def set_adgroup_associations(self, subclient_content, use_policies=True):
        &#34;&#34;&#34;Create Ad groups assocaition for UserMailboxSubclient.

            Args:
                subclient_content   (dict)  --  dict of the adgroups to add to the subclient

                    subclient_content = {

                        &#39;adGroupNames&#39; : [&#34;List of adGroups&#34;],

                        &#39;is_auto_discover_user&#39; : True,

                        &#39;archive_policy&#39; : &#34;Archiving policy&#34;,

                        &#39;cleanup_policy&#39; : &#39;Clean-up policy&#39;,

                        &#39;retention_policy&#39;: &#39;Retention policy&#39;,
                    }
                        -- if use_policies is False --

                        &#39;plan_name&#39;: &#39;Exchange Plan Name&#39;,
                        &#39;is_auto_discover_user&#39; : True,
                        &#39;plan_id&#39;: int or None (Optional)
                        --

        &#34;&#34;&#34;
        adgroups = []

        if not isinstance(subclient_content, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if not (isinstance(subclient_content[&#39;adGroupNames&#39;], list) and
                isinstance(subclient_content[&#39;is_auto_discover_user&#39;], bool)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        try:
            discover_adgroups = self.discover_adgroups

            for adgroup_item in subclient_content[&#39;adGroupNames&#39;]:

                for ad_item in discover_adgroups:

                    if adgroup_item.lower() == ad_item[&#39;adGroupName&#39;].lower():
                        adgroup_dict = {
                            &#39;adGroupName&#39;: ad_item[&#39;adGroupName&#39;],
                        }
                        adgroups.append(adgroup_dict)

        except KeyError as err:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))

        discover_info = {
            &#34;discoverByType&#34;: 3,
            &#34;adGroups&#34;: adgroups
        }
        if use_policies:
            _assocaition_json_ = self._association_json(subclient_content)
        else:
            _assocaition_json_ = self._association_json_with_plan(subclient_content)
        _assocaition_json_[&#34;emailAssociation&#34;][&#34;emailDiscoverinfo&#34;] = discover_info
        _assocaition_json_[&#34;emailAssociation&#34;].update({&#34;emailStatus&#34;: 0})
        _assocaition_json_[&#34;emailAssociation&#34;].update({&#34;advanceOptions&#34;: {
            &#34;enableAutoDiscovery&#34;: subclient_content[&#34;is_auto_discover_user&#34;]
        }})
        self._set_association_request(_assocaition_json_)

    def set_o365group_asscoiations(self, subclient_content):
        &#34;&#34;&#34;Create O365 Group association for UserMailboxSubclient.
            Args:
                subclient_content   (dict)  --  dict of the policies to associate

                    subclient_content = {

                        &#39;archive_policy&#39; : &#34;CIPLAN Archiving policy&#34;,

                        &#39;cleanup_policy&#39; : &#39;CIPLAN Clean-up policy&#39;,

                        &#39;retention_policy&#39;: &#39;CIPLAN Retention policy&#39;
                    }
        &#34;&#34;&#34;
        discover_info = {
            &#34;discoverByType&#34;: 11,
            &#34;genericAssociations&#34;: [
                {
                    &#34;associationName&#34;: &#34;All O365 Group Mailboxes&#34;,
                    &#34;associationType&#34;: 11
                }
            ]
        }
        _assocaition_json_ = self._association_json(subclient_content, True)
        _assocaition_json_[&#34;emailAssociation&#34;][&#34;emailDiscoverinfo&#34;] = discover_info
        self._set_association_request(_assocaition_json_)

    def delete_user_assocaition(self, subclient_content, use_policies=True):
        &#34;&#34;&#34;delete User assocaition for UserMailboxSubclient.
            Args:
                subclient_content   (dict)  --  dict of the Users to delete from subclient
                    subclient_content = {
                        &#39;mailboxNames&#39; : [&#34;list of mailboxes alias name&#34;],
                        -- if use_policies is True --
                        &#39;archive_policy&#39; : &#34;CIPLAN Archiving policy&#34;,

                        &#39;cleanup_policy&#39; : &#39;CIPLAN Clean-up policy&#39;,

                        &#39;retention_policy&#39;: &#39;CIPLAN Retention policy&#39;
                        --
                        -- if use_policies is False --
                        &#39;plan_name&#39;: Plan Name,
                        &#39;plan_id&#39;: int or None (Optional)
                        --
                    }
                use_policies (bool) -- If True uses policies else uses Plan
        &#34;&#34;&#34;
        users = []

        if not isinstance(subclient_content, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if not (isinstance(subclient_content[&#39;mailboxNames&#39;], list)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        try:
            discover_users = self.discover_users

            for mailbox_item in subclient_content[&#39;mailboxNames&#39;]:

                for mb_item in discover_users:

                    if mailbox_item.lower() == mb_item[&#39;aliasName&#39;].lower():
                        mailbox_dict = {
                            &#39;smtpAdrress&#39;: mb_item[&#39;smtpAdrress&#39;],
                            &#39;aliasName&#39;: mb_item[&#39;aliasName&#39;],
                            &#39;mailBoxType&#39;: mb_item[&#39;mailBoxType&#39;],
                            &#39;displayName&#39;: mb_item[&#39;displayName&#39;],
                            &#39;exchangeServer&#39;: mb_item[&#39;exchangeServer&#39;],
                            &#39;isAutoDiscoveredUser&#39;: mb_item[&#39;isAutoDiscoveredUser&#39;],
                            &#34;associated&#34;: False,
                            &#39;databaseName&#39;: mb_item[&#39;databaseName&#39;],
                            &#34;exchangeVersion&#34;: mb_item[&#39;exchangeVersion&#39;],
                            &#34;exchangeServer&#34;: mb_item[&#39;exchangeServer&#39;],
                            &#39;user&#39;: {
                                &#39;_type_&#39;: 13,
                                &#39;userGUID&#39;: mb_item[&#39;user&#39;][&#39;userGUID&#39;]
                            }
                        }
                        users.append(mailbox_dict)
                        break

        except KeyError as err:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))
        discover_info = {
            &#34;discoverByType&#34;: 1,
            &#34;mailBoxes&#34;: users
        }
        if use_policies:
            _association_json_ = self._association_json(subclient_content)
        else:
            _association_json_ = self._association_json_with_plan(subclient_content)
        _association_json_[&#34;emailAssociation&#34;][&#34;emailStatus&#34;] = 1
        _association_json_[&#34;emailAssociation&#34;][&#34;emailDiscoverinfo&#34;] = discover_info
        self._update_association_request(_association_json_)

    def delete_o365group_association(self, subclient_content):
        &#34;&#34;&#34;delete O365 group association for UserMailboxSubclient.

            Args:
                subclient_content   (dict)  --  dict of the Users to delete from subclient

                    subclient_content = {

                        &#39;mailboxNames&#39; : [&#34;AutoCi2&#34;],

                        &#39;archive_policy&#39; : &#34;CIPLAN Archiving policy&#34;,

                        &#39;cleanup_policy&#39; : &#39;CIPLAN Clean-up policy&#39;,

                        &#39;retention_policy&#39;: &#39;CIPLAN Retention policy&#39;
                    }

        &#34;&#34;&#34;
        groups = []
        try:
            for mb_item in self.o365groups:
                mailbox_dict = {
                    &#39;smtpAdrress&#39;: mb_item[&#39;smtp_address&#39;],
                    &#39;aliasName&#39;: mb_item[&#39;alias_name&#39;],
                    &#39;mailBoxType&#39;: 1,
                    &#39;displayName&#39;: mb_item[&#39;display_name&#39;],
                    &#39;exchangeServer&#39;: &#34;&#34;,
                    &#39;isAutoDiscoveredUser&#39;: mb_item[&#39;is_auto_discover_user&#39;].lower() == &#39;true&#39;,
                    &#39;msExchRecipientTypeDetails&#39;: 36,
                    &#34;associated&#34;: False,
                    &#39;databaseName&#39;: mb_item[&#39;database_name&#39;],
                    &#39;user&#39;: {
                        &#39;_type_&#39;: 13,
                        &#39;userGUID&#39;: mb_item[&#39;user_guid&#39;]
                    }
                }
                groups.append(mailbox_dict)

        except KeyError as err:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))

        discover_info = {
            &#34;discoverByType&#34;: 1,
            &#34;mailBoxes&#34;: groups
        }
        _assocaition_json_ = self._association_json(subclient_content, True)
        _assocaition_json_[&#34;emailAssociation&#34;][&#34;emailStatus&#34;] = 1
        _assocaition_json_[&#34;emailAssociation&#34;][&#34;emailDiscoverinfo&#34;] = discover_info
        self._update_association_request(_assocaition_json_)

    def delete_database_assocaition(self, subclient_content, use_policies=True):
        &#34;&#34;&#34;Deletes Database assocaition for UserMailboxSubclient.

            Args:
                subclient_content   (dict)  --  dict of the databases to delete from subclient

                    For policy -
                    subclient_content = {

                        &#39;databaseNames&#39; : [&#34;List of names of db&#34;],

                        &#39;archive_policy&#39; : &#34;Archiving policy&#34;,

                        &#39;cleanup_policy&#39; : &#39;Clean-up policy&#39;,

                        &#39;retention_policy&#39;: &#39;Retention policy&#39;
                    }

                    For Plan -
                    subclient_content = { &#39;is_auto_discover_user&#39; : True,
                    &#34;plan_name&#34;: Name of plan, &#34;plan_id&#34;(optional) -- id of plan}

                use_policies(bool)  -- if we need to use policy or plan
        &#34;&#34;&#34;
        databases = []

        if not isinstance(subclient_content, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if not (isinstance(subclient_content[&#39;databaseNames&#39;], list) and
                isinstance(subclient_content[&#39;is_auto_discover_user&#39;], bool)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        try:
            discover_databases = self.discover_databases

            for database_item in subclient_content[&#39;databaseNames&#39;]:

                for db_item in discover_databases:

                    if database_item.lower() == db_item[&#39;databaseName&#39;].lower():
                        database_dict = {
                            &#39;exchangeServer&#39;: db_item[&#39;exchangeServer&#39;],
                            &#34;associated&#34;: False,
                            &#39;databaseName&#39;: db_item[&#39;databaseName&#39;],
                        }
                        databases.append(database_dict)

        except KeyError as err:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))

        discover_info = {
            &#34;discoverByType&#34;: 2,
            &#34;databases&#34;: databases
        }

        if use_policies:
            _association_json_ = self._association_json(subclient_content)
        else:
            _association_json_ = self._association_json_with_plan(subclient_content)

        _association_json_[&#34;emailAssociation&#34;][&#34;emailStatus&#34;] = 1
        _association_json_[&#34;emailAssociation&#34;][&#34;emailDiscoverinfo&#34;] = discover_info
        self._update_association_request(_association_json_)

    def delete_adgroup_assocaition(self, subclient_content, use_policies=True):
        &#34;&#34;&#34;Deletes Ad groups assocaition for UserMailboxSubclient.

            Args:
                subclient_content   (dict)  --  dict of the adgroups to delete from subclient

                    subclient_content = {

                        &#39;adGroupNames&#39; : [&#34;List of names of adgroups],

                        &#39;is_auto_discover_user&#39; : True,

                        &#39;archive_policy&#39; : &#34;CIPLAN Archiving policy&#34;,

                        &#39;cleanup_policy&#39; : &#39;CIPLAN Clean-up policy&#39;,

                        &#39;retention_policy&#39;: &#39;CIPLAN Retention policy&#39;,
                    }
                    For plan:
                        &#39;is_auto_discover_user&#39; : True,

                        &#39;plan_name&#39;: &#39;Exchange Plan Name&#39;,

                        &#39;plan_id&#39;: int or None (Optional)

                use_policies(bool)  -- if we need to use policy or plan
        &#34;&#34;&#34;
        adgroups = []

        if not isinstance(subclient_content, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        if not (isinstance(subclient_content[&#39;adGroupNames&#39;], list) and
                isinstance(subclient_content[&#39;is_auto_discover_user&#39;], bool)):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        try:
            discover_adgroups = self.discover_adgroups

            for adgroup_item in subclient_content[&#39;adGroupNames&#39;]:

                for ad_item in discover_adgroups:

                    if adgroup_item.lower() == ad_item[&#39;adGroupName&#39;].lower():
                        adgroup_dict = {
                            &#34;associated&#34;: False,
                            &#39;adGroupName&#39;: ad_item[&#39;adGroupName&#39;],
                        }
                        adgroups.append(adgroup_dict)

        except KeyError as err:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))

        discover_info = {
            &#34;discoverByType&#34;: 3,
            &#34;adGroups&#34;: adgroups
        }
        if use_policies:
            _assocaition_json_ = self._association_json(subclient_content)
        else:
            _assocaition_json_ = self._association_json_with_plan(subclient_content)
        _assocaition_json_[&#34;emailAssociation&#34;][&#34;emailStatus&#34;] = 1
        _assocaition_json_[&#34;emailAssociation&#34;][&#34;enableAutoDiscovery&#34;] = subclient_content[
            &#34;is_auto_discover_user&#34;]
        _assocaition_json_[&#34;emailAssociation&#34;][&#34;emailDiscoverinfo&#34;] = discover_info
        self._set_association_request(_assocaition_json_)

    def enable_allusers_associations(self, subclient_content, use_policies=True):
        &#34;&#34;&#34;Enable all users assocaition for UserMailboxSubclient.

            Args:
                subclient_content   (dict)  --  dict of the policies which needs to be
                assigned to all user assocaitions
                    For policy
                    subclient_content = {

                        &#39;is_auto_discover_user&#39; : True

                        &#39;archive_policy&#39; : &#34;CIPLAN Archiving policy&#34;,

                        &#39;cleanup_policy&#39; : &#39;CIPLAN Clean-up policy&#39;,

                        &#39;retention_policy&#39;: &#39;CIPLAN Retention policy&#39;,
                    }
    
                    For Plan -
                    subclient_content = { &#39;is_auto_discover_user&#39; : True,
                    &#34;plan_name&#34;: Name of plan, &#34;plan_id&#34;(optional) -- id of plan
                    }

                use_policies(bool)  -- if we need to use policy or plan

        &#34;&#34;&#34;

        if not isinstance(subclient_content, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        discover_info = {
            &#34;discoverByType&#34;: 8,
            &#34;genericAssociations&#34;: [
                {
                    &#34;associationName&#34;: &#34;All Users&#34;,
                    &#34;associationType&#34;: 8
                }
            ]
        }
        if use_policies:
            _association_json_ = self._association_json(subclient_content)
        else:
            _association_json_ = self._association_json_with_plan(plan_details=subclient_content)
        _association_json_[&#39;emailAssociation&#39;][&#39;advanceOptions&#39;] = {&#34;enableAutoDiscovery&#34;: True}
        _association_json_[&#34;emailAssociation&#34;][&#34;emailDiscoverinfo&#34;] = discover_info
        self._set_association_request(_association_json_)

    def disable_allusers_associations(self, use_policies=True, plan_details=None):
        &#34;&#34;&#34;Disables alluser assocaition for UserMailboxSubclient.
            use_policies (bool) -- If we need to use plan(False) or policy(True)
            plan_details(dict) -- { &#34;plan_name&#34;: Name of plan, &#34;plan_id&#34;(optional) -- id of plan}
        &#34;&#34;&#34;
        subclient_content = {
            &#39;is_auto_discover_user&#39;: True
        }
        discover_info = {
            &#34;discoverByType&#34;: 8,
            &#34;genericAssociations&#34;: [
                {
                    &#34;associationName&#34;: &#34;All Users&#34;,
                    &#34;associationType&#34;: 8
                }
            ]
        }

        if use_policies:
            _association_json_ = self._association_json(subclient_content)
        else:
            _association_json_ = self._association_json_with_plan(plan_details=plan_details)

        _association_json_[&#34;emailAssociation&#34;][&#34;emailDiscoverinfo&#34;] = discover_info
        _association_json_[&#34;emailAssociation&#34;][&#34;emailStatus&#34;] = 2
        _association_json_[&#39;emailAssociation&#39;][&#39;advanceOptions&#39;] = {&#34;enableAutoDiscovery&#34;: True}
        self._set_association_request(_association_json_)

    def enable_auto_discover_association(self, association_name, plan_name):
        &#34;&#34;&#34;Enable all users assocaition for UserMailboxSubclient.

                    Args:
                        association_name  (str)  --  Type of auto discover association
                            Valid Values:
                                &#34;All Users&#34;
                                &#34;All O365 Mailboxes&#34;
                                &#34;All Public Folders&#34;

                        plan_name  (str)  --  Name of the plan to associate with users/groups


                &#34;&#34;&#34;
        plan = self._commcell_object.plans.get(plan_name)

        association_dict = {&#34;All Users&#34;: 8,
                            &#34;All Office365 Groups&#34;: 11,
                            &#34;All Public Folders&#34;: 12
                            }

        _association_json = {
            &#34;emailAssociation&#34;: {
                &#34;emailStatus&#34;: 0,
                &#34;advanceOptions&#34;: {
                    &#34;enableAutoDiscovery&#34;: True
                },
                &#34;subclientEntity&#34;: self._subClientEntity,
                &#34;emailDiscoverinfo&#34;: {
                    &#34;discoverByType&#34;: association_dict[association_name],
                    &#34;genericAssociations&#34;: [
                        {
                            &#34;associationName&#34;: association_name,
                            &#34;associationType&#34;: association_dict[association_name]
                        }
                    ]
                },
                &#34;plan&#34;: {
                    &#34;planId&#34;: int(plan.plan_id)
                }
            }
        }
        self._set_association_request(_association_json)

    def delete_auto_discover_association(self, association_name, subclient_content, use_policies=True):
        &#34;&#34;&#34;
            Delete all users association for UserMailboxSubclient.

                    Args:
                        association_name  (str)  --  Type of auto discover association
                            Valid Values:
                                &#34;All Users&#34;
                                &#34;All O365 Mailboxes&#34;
                                &#34;All Public Folders&#34;

                        use_policies (bool) -- If we need to use plan(False) or policy(True)

                        subclient_content (dict) - containing the information of users/groups

                            if use_policies is True
                                subclient_content={
                                    &#34;is_auto_dicover_user&#34; (bool): True
                                    &#34;archive_policy&#34; (obj): Archive Policy object
                                    &#34;cleanup_policy&#34; (obj): Cleanup Policy Object
                                    &#34;retention_policy&#34; (obj): Retention Policy Object
                                }

                            if use_policies is False
                                subclient_content={
                                    &#34;is_auto_discover_user&#34; (bool): True,
                                    &#34;plan_name&#34; (str): Name of the exchange plan,
                                    &#34;plan_id&#34;: Id of the plan(optional)
                                }
        &#34;&#34;&#34;
        if not (isinstance(subclient_content, dict)):
            raise SDKException(&#34;Subclient&#34;, &#34;101&#34;)

        association_dict = {&#34;all users&#34;: 8,
                            &#34;all o365 group mailboxes&#34;: 11,
                            &#34;all public folders&#34;: 12
                            }

        if association_name.lower() not in association_dict:
            raise SDKException(&#34;Subclient&#34;, &#34;102&#34;, &#34;Invalid Association Name supplied&#34;)

        if use_policies:
            _association_json_ = self._association_json(subclient_content)
        else:
            planobject = self._commcell_object.plans.get(subclient_content[&#34;plan_name&#34;])
            _association_json_ = self._association_json_with_plan(plan_details=planobject)

        discover_info = {
            &#34;discoverByType&#34;: association_dict[association_name.lower()],
            &#34;genericAssociations&#34;: [
                {
                    &#34;associationName&#34;: association_name,
                    &#34;associationType&#34;: association_dict[association_name.lower()]
                }
            ]
        }
        _association_json_[&#34;emailAssociation&#34;][&#34;advanceOptions&#34;][&#34;enableAutoDiscovery&#34;] = True
        _association_json_[&#34;emailAssociation&#34;][&#34;emailStatus&#34;] = 1
        _association_json_[&#34;emailAssociation&#34;][&#34;emailDiscoverinfo&#34;] = discover_info
        self._set_association_request(_association_json_)

    def enable_ews_support(self, service_url):
        &#34;&#34;&#34;This function provides support for EWS protocol to backup on-prem mailboxes
            Args:
                service_url (string) -- EWS Connection URL for your exchange server
            Returns: None
        &#34;&#34;&#34;
        self.agentproperties = self._agent_object.properties
        self.agentproperties[&#34;onePassProperties&#34;][&#34;onePassProp&#34;][&#34;ewsDetails&#34;][&#34;bUseEWS&#34;] = True
        self.agentproperties[&#34;onePassProperties&#34;][&#34;onePassProp&#34;][&#34;ewsDetails&#34;][&#34;ewsConnectionUrl&#34;] = service_url
        self._agent_object.update_properties(self.agentproperties)

    def browse_mailboxes(self, retry_attempts=0):
        &#34;&#34;&#34;
        This function returns the mailboxes available for OOP restore
        return: dictionary containing mailbox info
        &#34;&#34;&#34;
        BROWSE_MAILBOXES = self._commcell_object._services[&#39;EMAIL_DISCOVERY_WITHOUT_REFRESH&#39;] % (
            int(self._backupset_object.backupset_id), &#39;User&#39;
        )
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;GET&#39;, BROWSE_MAILBOXES)
        if flag:
            if response and response.json():
                discover_content = response.json()
                if discover_content.get(&#39;resp&#39;, {}).get(&#39;errorCode&#39;, 0) == 469762468:
                    time.sleep(10)
                    if retry_attempts &gt; 10:
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Failed to perform browse operation.&#39;)
                    return self.browse_mailboxes(retry_attempts + 1)
                if &#39;discoverInfo&#39; in discover_content.keys():
                    if &#39;mailBoxes&#39; in discover_content[&#39;discoverInfo&#39;]:
                        mailboxes = discover_content[&#34;discoverInfo&#34;][&#34;mailBoxes&#34;]
                        return mailboxes
            else:
                raise SDKException(&#34;Response&#34;, &#34;102&#34;)
        else:
            response_string = self.commcell._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def backup_generic_items(self, subclient_content):
        &#34;&#34;&#34;
            Backups the Generic Items for any Exchange Online Client
            GGeneric Items:
                All Public Folders/ All O365 Group ailboxes/ All Users

            Args:
                subclient_content   (list)  List having dictionary of items to be backed up

                subclient_content = [
                    {
                    &#34;associationName&#34; : &#34;All Public Folders&#34;,
                    &#34;associationType&#34;:12
                    },
                    {
                    &#34;associationName&#34; : &#34;All Users&#34;,
                    &#34;associationType&#34;:12
                    }
                ]
        &#34;&#34;&#34;
        task_dict = self._backup_generic_items_json(subclient_content=subclient_content)

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;CREATE_TASK&#39;], task_dict
        )

        return self._process_backup_response(flag, response)

    def backup_mailboxes(self, mailbox_alias_names=None, **kwargs):
        &#34;&#34;&#34;
        Backup specific mailboxes.
        Args:
            mailbox_alias_names(list): alias names of all the mailboxes to backup
                Sample Values:
                    [&#39;aj&#39;, &#39;tkumar&#39;]
             **kwargs (dict) : Additional parameters
                    items_selection_option (str) : Item Selection Option (Example: &#34;7&#34; for selecting backed up recently entities)
        Returns:
            job(Job): instance of job class for the backup job
        &#34;&#34;&#34;
        task_json = self._task_json_for_backup(mailbox_alias_names, **kwargs)
        create_task = self._services[&#39;CREATE_TASK&#39;]
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, create_task, task_json
        )
        return self._process_backup_response(flag, response)

    def create_recovery_point(self, mailbox_prop, job=None, job_id=None):
        &#34;&#34;&#34;
            Method to create a recovery point

            Arguments:
                mailbox_prop        (dict)--    Dictionary of mailbox properties for which the Recovery point is to be created
                Sample:
                {
                    &#39;mailbox_smtp&#39; : name of the mailbox for which recovery point is to be created
                    &#39;mailbox_guid&#39;: GUID of the mailbox
                    &#39;index_server&#39;: Name of the index server to be used to create index on
                }
                job                 (object)--  Backup Job to which restore point has to be created
                job_id              (int)--     Backup Job ID to which restore point is to be created

                Either pass the job object or the job_id

            Returns:
                res_dict            (dict)--    Dictionary of Response
                Format:
                {
                    &#39;rercovery_point_id&#39; : ID of the recovery point created,
                    &#39;recovery_point_job_id&#39;: Job ID for recovery point creation JOB
                }
        &#34;&#34;&#34;

        if (job == None and job_id == None):
            raise Exception(&#34;At least one value out of job or job_id should be passed&#34;)

        if (job == None and type(job_id) == int):
            job = self._commcell_object.job_controller.get(job_id)

        index_server = self._commcell_object.clients.get(mailbox_prop[&#39;index_server&#39;])
        index_server_id = index_server.client_id

        recovery_point_dict = {
            &#34;opType&#34;: 0,
            &#34;advOptions&#34;: {
                &#34;advConfig&#34;: {
                    &#34;browseAdvancedConfigReq&#34;: {
                        &#34;additionalFlags&#34;: [
                            {
                                &#34;flagType&#34;: 13,
                                &#34;value&#34;: &#34;{}&#34;.format(index_server_id),
                                &#34;key&#34;: &#34;RecoveryPointIndexServer&#34;
                            }
                        ]
                    },
                    &#34;applicationMining&#34;: {
                        &#34;appType&#34;: 137,
                        &#34;isApplicationMiningReq&#34;: True,
                        &#34;browseInitReq&#34;: {
                            &#34;bCreateRecoveryPoint&#34;: True,
                            &#34;jobId&#34;: int(job.job_id),
                            &#34;pointInTimeRange&#34;: {
                                &#34;fromTime&#34;: int(job.start_timestamp),
                                &#34;toTime&#34;: int(job.end_timestamp)
                            },
                            &#34;mbxInfo&#34;: [
                                {
                                    &#34;smtpAdrress&#34;: mailbox_prop[&#39;mailbox_smtp&#39;],
                                    &#34;mbxGUIDs&#34;: mailbox_prop[&#39;mailbox_guid&#39;]
                                }
                            ]
                        }
                    }
                }
            },
            &#34;paths&#34;: [
                {
                    &#34;path&#34;: &#34;\\MB\\{%s}&#34; % mailbox_prop[&#39;mailbox_guid&#39;]
                }
            ],
            &#34;entity&#34;: {
                &#34;subclientId&#34;: int(self.subclient_id),
                &#34;backupsetId&#34;: int(self._backupset_object.backupset_id),
                &#34;clientId&#34;: int(self._client_object.client_id)
            },
            &#34;timeRange&#34;: {
                &#34;fromTime&#34;: 0,
                &#34;toTime&#34;: int(job.start_timestamp)
            }
        }

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;BROWSE&#39;], recovery_point_dict
        )

        if flag:
            if response and response.json():
                browse_response = response.json()

                if &#39;browseResponses&#39; in browse_response.keys():
                    recovery_point_job_id = browse_response.get(&#39;browseResponses&#39;, [{}])[0].get(&#39;browseResult&#39;, {}).get(
                        &#39;advConfig&#39;, {}).get(
                        &#39;applicationMining&#39;, {}).get(&#39;browseInitResp&#39;, {}).get(&#39;recoveryPointJobID&#39;)

                    recovery_point_id = browse_response.get(&#39;browseResponses&#39;, [{}])[0].get(&#39;browseResult&#39;, {}).get(
                        &#39;advConfig&#39;, {}).get(
                        &#39;applicationMining&#39;, {}).get(&#39;browseInitResp&#39;, {}).get(&#39;recoveryPointID&#39;, {})

                    res_dict = {
                        &#39;recovery_point_job_id&#39;: recovery_point_job_id,
                        &#39;recovery_point_id&#39;: recovery_point_id
                    }
                    return res_dict
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;, response.json)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;, response.json)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def refresh(self):
        &#34;&#34;&#34;Refresh the User Mailbox Subclient.&#34;&#34;&#34;
        self._get_subclient_properties()
        self._discover_users = self._get_discover_users()
        self._discover_databases = self._get_discover_database()
        self._discover_adgroups = self._get_discover_adgroups()
        self._users, self._o365groups = self._get_user_assocaitions()
        self._databases = self._get_database_associations()
        self._adgroups = self._get_adgroup_assocaitions()

    def restore_in_place_syntex(self, **kwargs):
        &#34;&#34;&#34;Runs an in-place restore job on the specified Syntex Exchange pseudo client

             Kwargs:

                 paths (list)  --  list of paths of mailboxes/folders to restore

             Returns:

                Job object

            Raises:

                SDKException:

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

        &#34;&#34;&#34;
        paths = kwargs.get(&#39;paths&#39;, [])
        paths = self._filter_paths(paths)
        self._json_restore_exchange_restore_option({})
        self._json_backupset()
        restore_option = {&#34;paths&#34;: paths}

        self._instance_object._restore_association = self._subClientEntity
        request_json = self._restore_json(restore_option=restore_option)
        request_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;subclientName&#39;] = self.subclient_name
        request_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][
            &#39;backupsetName&#39;] = self._backupset_object.backupset_name
        request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][
            &#39;options&#39;][&#39;restoreOptions&#39;][&#39;exchangeOption&#39;] = self._exchange_option_restore_json
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;browseOption&#34;][&#39;backupset&#39;] = self._exchange_backupset_json

        mailboxes = self.browse_mailboxes()
        mailbox_details = {}
        for path in paths:
            mailbox_details[path] = next((mailbox for mailbox in mailboxes if mailbox[&#39;aliasName&#39;] == path), None)
        syntex_restore_items = []

        for key, value in mailbox_details.items():
            syntex_restore_items.append({
                &#34;displayName&#34;: value[&#34;displayName&#34;],
                &#34;guid&#34;: value[&#34;user&#34;][&#34;userGUID&#34;],
                &#34;rawId&#34;: value[&#34;user&#34;][&#34;userGUID&#34;],
                &#34;restoreType&#34;: 1
            })

        # Get the current time in UTC
        current_time = datetime.datetime.now(datetime.timezone.utc)
        current_timestamp = int(current_time.timestamp())
        current_iso_format = current_time.strftime(&#39;%Y-%m-%dT%H:%M:%S.%f&#39;)[:-3] + &#39;Z&#39;

        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;] = {}
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;][
            &#34;msSyntexRestoreOptions&#34;] = {
            &#34;msSyntexRestoreItems&#34;: {
                &#34;listMsSyntexRestoreItems&#34;: syntex_restore_items
            },
            &#34;restoreDate&#34;: {
                &#34;time&#34;: current_timestamp,
                &#34;timeValue&#34;: current_iso_format
            },
            &#34;restorePointId&#34;: &#34;&#34;,
            &#34;restoreType&#34;: 1,
            &#34;useFastRestorePoint&#34;: False
        }

        return self._process_restore_response(request_json)

    @staticmethod
    def _find_mailbox_query_params(query_params=None):
        &#34;&#34;&#34;
            Generates the query params for find mailbox request
                Args:
                    query_params   (dict)   -- Dict of key value pair
                        Default (None)
                Returns:
                    json for find malilbox query
        &#34;&#34;&#34;
        final_params = []
        default_params = ExchangeConstants.FIND_MBX_QUERY_DEFAULT_PARAMS
        if query_params:
            for param, value in query_params.items():
                if param in default_params:
                    default_params.pop(value)
                final_params.append({&#34;param&#34;: param, &#34;value&#34;: value})
        for param, value in default_params.items():
            final_params.append({&#34;param&#34;: param, &#34;value&#34;: value})
        return final_params

    @staticmethod
    def _find_mailbox_facets(facets=None):
        &#34;&#34;&#34;
            Generates the facet requests for find mailbox request
                Args:
                    facets   (list)   -- List of strings containing facets
                        Default (None)
                Returns:
                    json for find malilbox facets query
        &#34;&#34;&#34;
        final_params = []
        default_facet = ExchangeConstants.FIND_MBX_DEFAULT_FACET

        for facet in facets:
            if facet in default_facet:
                default_facet.remove(facet)
            final_params.append({&#34;name&#34;: facet})
        for facet in default_facet:
            final_params.append({&#34;name&#34;: facet})
        return final_params

    def find_mailbox(self, mailbox_smtp, **kwargs):
        &#34;&#34;&#34;
        Performs the find operation for a mailbox in browse
            Args:
                mailbox_smtp    (str) -- SMTP address of the mailbox to find

                kwargs                -- Optional key word args
            Returns:
                    response json for find mailbox
        &#34;&#34;&#34;

        data = ExchangeConstants.FIND_MAILBOX_REQUEST_DATA

        data[&#34;advSearchGrp&#34;][&#34;emailFilter&#34;][0][&#34;filter&#34;][&#34;filters&#34;].append(
            {
                &#34;field&#34;: &#34;CUSTODIAN&#34;,
                &#34;intraFieldOp&#34;: 0,
                &#34;fieldValues&#34;: {
                    &#34;values&#34;: [
                        mailbox_smtp
                    ]
                }
            }
        )
        data[&#34;advSearchGrp&#34;][&#34;galaxyFilter&#34;][0][&#34;appIdList&#34;] = [int(self.subclient_id)]
        data[&#34;facetRequests&#34;][&#34;facetRequest&#34;] = UsermailboxSubclient._find_mailbox_facets(
            kwargs.get(&#34;facets_params&#34;, None))
        data[&#34;searchProcessingInfo&#34;][&#34;pageSize&#34;] = int(kwargs.get(&#34;page_size&#34;, 100))
        data[&#34;searchProcessingInfo&#34;][&#34;queryParams&#34;] = UsermailboxSubclient._find_mailbox_query_params(
            kwargs.get(&#34;query_params&#34;, None))

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;DO_WEB_SEARCH&#39;], data
        )
        if flag:
            if response and response.json():
                return response.json()
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient" href="../exchsubclient.html#cvpysdk.subclients.exchsubclient.ExchangeSubclient">ExchangeSubclient</a></li>
<li><a title="cvpysdk.subclient.Subclient" href="../../subclient.html#cvpysdk.subclient.Subclient">Subclient</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.adgroups"><code class="name">var <span class="ident">adgroups</span></code></dt>
<dd>
<div class="desc"><p>Returns the list of AD groups associated with the UserMailbox subclient.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/usermailbox_subclient.py#L792-L795" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def adgroups(self):
    &#34;&#34;&#34;Returns the list of AD groups associated with the UserMailbox subclient.&#34;&#34;&#34;
    return self._adgroups</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.databases"><code class="name">var <span class="ident">databases</span></code></dt>
<dd>
<div class="desc"><p>Returns the list of databases associated with the UserMailbox subclient.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/usermailbox_subclient.py#L787-L790" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def databases(self):
    &#34;&#34;&#34;Returns the list of databases associated with the UserMailbox subclient.&#34;&#34;&#34;
    return self._databases</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.discover_adgroups"><code class="name">var <span class="ident">discover_adgroups</span></code></dt>
<dd>
<div class="desc"><p>Returns the list of discovered AD groups for the UserMailbox subclient.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/usermailbox_subclient.py#L777-L780" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def discover_adgroups(self):
    &#34;&#34;&#34;Returns the list of discovered AD groups for the UserMailbox subclient.&#34;&#34;&#34;
    return self._discover_adgroups</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.discover_databases"><code class="name">var <span class="ident">discover_databases</span></code></dt>
<dd>
<div class="desc"><p>Returns the list of discovered databases for the UserMailbox subclient.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/usermailbox_subclient.py#L772-L775" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def discover_databases(self):
    &#34;&#34;&#34;Returns the list of discovered databases for the UserMailbox subclient.&#34;&#34;&#34;
    return self._discover_databases</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.discover_users"><code class="name">var <span class="ident">discover_users</span></code></dt>
<dd>
<div class="desc"><p>"Returns the list of discovered users for the UserMailbox subclient.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/usermailbox_subclient.py#L767-L770" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def discover_users(self):
    &#34;&#34;&#34;&#34;Returns the list of discovered users for the UserMailbox subclient.&#34;&#34;&#34;
    return self._discover_users</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.o365groups"><code class="name">var <span class="ident">o365groups</span></code></dt>
<dd>
<div class="desc"><p>Returns the list of discovered O365 groups for the UserMailbox subclient.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/usermailbox_subclient.py#L797-L800" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def o365groups(self):
    &#34;&#34;&#34;Returns the list of discovered O365 groups for the UserMailbox subclient.&#34;&#34;&#34;
    return self._o365groups</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.users"><code class="name">var <span class="ident">users</span></code></dt>
<dd>
<div class="desc"><p>Returns the list of users associated with UserMailbox subclient.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/usermailbox_subclient.py#L782-L785" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def users(self):
    &#34;&#34;&#34;Returns the list of users associated with UserMailbox subclient.&#34;&#34;&#34;
    return self._users</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.backup_generic_items"><code class="name flex">
<span>def <span class="ident">backup_generic_items</span></span>(<span>self, subclient_content)</span>
</code></dt>
<dd>
<div class="desc"><p>Backups the Generic Items for any Exchange Online Client
GGeneric Items:
All Public Folders/ All O365 Group ailboxes/ All Users</p>
<h2 id="args">Args</h2>
<p>subclient_content
(list)
List having dictionary of items to be backed up</p>
<p>subclient_content = [
{
"associationName" : "All Public Folders",
"associationType":12
},
{
"associationName" : "All Users",
"associationType":12
}
]</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/usermailbox_subclient.py#L1658-L1684" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def backup_generic_items(self, subclient_content):
    &#34;&#34;&#34;
        Backups the Generic Items for any Exchange Online Client
        GGeneric Items:
            All Public Folders/ All O365 Group ailboxes/ All Users

        Args:
            subclient_content   (list)  List having dictionary of items to be backed up

            subclient_content = [
                {
                &#34;associationName&#34; : &#34;All Public Folders&#34;,
                &#34;associationType&#34;:12
                },
                {
                &#34;associationName&#34; : &#34;All Users&#34;,
                &#34;associationType&#34;:12
                }
            ]
    &#34;&#34;&#34;
    task_dict = self._backup_generic_items_json(subclient_content=subclient_content)

    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, self._services[&#39;CREATE_TASK&#39;], task_dict
    )

    return self._process_backup_response(flag, response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.backup_mailboxes"><code class="name flex">
<span>def <span class="ident">backup_mailboxes</span></span>(<span>self, mailbox_alias_names=None, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Backup specific mailboxes.</p>
<h2 id="args">Args</h2>
<p>mailbox_alias_names(list): alias names of all the mailboxes to backup
Sample Values:
['aj', 'tkumar']
**kwargs (dict) : Additional parameters
items_selection_option (str) : Item Selection Option (Example: "7" for selecting backed up recently entities)</p>
<h2 id="returns">Returns</h2>
<p>job(Job): instance of job class for the backup job</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/usermailbox_subclient.py#L1686-L1703" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def backup_mailboxes(self, mailbox_alias_names=None, **kwargs):
    &#34;&#34;&#34;
    Backup specific mailboxes.
    Args:
        mailbox_alias_names(list): alias names of all the mailboxes to backup
            Sample Values:
                [&#39;aj&#39;, &#39;tkumar&#39;]
         **kwargs (dict) : Additional parameters
                items_selection_option (str) : Item Selection Option (Example: &#34;7&#34; for selecting backed up recently entities)
    Returns:
        job(Job): instance of job class for the backup job
    &#34;&#34;&#34;
    task_json = self._task_json_for_backup(mailbox_alias_names, **kwargs)
    create_task = self._services[&#39;CREATE_TASK&#39;]
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, create_task, task_json
    )
    return self._process_backup_response(flag, response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.browse_mailboxes"><code class="name flex">
<span>def <span class="ident">browse_mailboxes</span></span>(<span>self, retry_attempts=0)</span>
</code></dt>
<dd>
<div class="desc"><p>This function returns the mailboxes available for OOP restore
return: dictionary containing mailbox info</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/usermailbox_subclient.py#L1631-L1656" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def browse_mailboxes(self, retry_attempts=0):
    &#34;&#34;&#34;
    This function returns the mailboxes available for OOP restore
    return: dictionary containing mailbox info
    &#34;&#34;&#34;
    BROWSE_MAILBOXES = self._commcell_object._services[&#39;EMAIL_DISCOVERY_WITHOUT_REFRESH&#39;] % (
        int(self._backupset_object.backupset_id), &#39;User&#39;
    )
    flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;GET&#39;, BROWSE_MAILBOXES)
    if flag:
        if response and response.json():
            discover_content = response.json()
            if discover_content.get(&#39;resp&#39;, {}).get(&#39;errorCode&#39;, 0) == 469762468:
                time.sleep(10)
                if retry_attempts &gt; 10:
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Failed to perform browse operation.&#39;)
                return self.browse_mailboxes(retry_attempts + 1)
            if &#39;discoverInfo&#39; in discover_content.keys():
                if &#39;mailBoxes&#39; in discover_content[&#39;discoverInfo&#39;]:
                    mailboxes = discover_content[&#34;discoverInfo&#34;][&#34;mailBoxes&#34;]
                    return mailboxes
        else:
            raise SDKException(&#34;Response&#34;, &#34;102&#34;)
    else:
        response_string = self.commcell._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.create_recovery_point"><code class="name flex">
<span>def <span class="ident">create_recovery_point</span></span>(<span>self, mailbox_prop, job=None, job_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Method to create a recovery point</p>
<h2 id="arguments">Arguments</h2>
<p>mailbox_prop
(dict)&ndash;
Dictionary of mailbox properties for which the Recovery point is to be created
Sample:
{
'mailbox_smtp' : name of the mailbox for which recovery point is to be created
'mailbox_guid': GUID of the mailbox
'index_server': Name of the index server to be used to create index on
}
job
(object)&ndash;
Backup Job to which restore point has to be created
job_id
(int)&ndash;
Backup Job ID to which restore point is to be created</p>
<p>Either pass the job object or the job_id</p>
<h2 id="returns">Returns</h2>
<p>res_dict
(dict)&ndash;
Dictionary of Response
Format:
{
'rercovery_point_id' : ID of the recovery point created,
'recovery_point_job_id': Job ID for recovery point creation JOB
}</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/usermailbox_subclient.py#L1705-L1817" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def create_recovery_point(self, mailbox_prop, job=None, job_id=None):
    &#34;&#34;&#34;
        Method to create a recovery point

        Arguments:
            mailbox_prop        (dict)--    Dictionary of mailbox properties for which the Recovery point is to be created
            Sample:
            {
                &#39;mailbox_smtp&#39; : name of the mailbox for which recovery point is to be created
                &#39;mailbox_guid&#39;: GUID of the mailbox
                &#39;index_server&#39;: Name of the index server to be used to create index on
            }
            job                 (object)--  Backup Job to which restore point has to be created
            job_id              (int)--     Backup Job ID to which restore point is to be created

            Either pass the job object or the job_id

        Returns:
            res_dict            (dict)--    Dictionary of Response
            Format:
            {
                &#39;rercovery_point_id&#39; : ID of the recovery point created,
                &#39;recovery_point_job_id&#39;: Job ID for recovery point creation JOB
            }
    &#34;&#34;&#34;

    if (job == None and job_id == None):
        raise Exception(&#34;At least one value out of job or job_id should be passed&#34;)

    if (job == None and type(job_id) == int):
        job = self._commcell_object.job_controller.get(job_id)

    index_server = self._commcell_object.clients.get(mailbox_prop[&#39;index_server&#39;])
    index_server_id = index_server.client_id

    recovery_point_dict = {
        &#34;opType&#34;: 0,
        &#34;advOptions&#34;: {
            &#34;advConfig&#34;: {
                &#34;browseAdvancedConfigReq&#34;: {
                    &#34;additionalFlags&#34;: [
                        {
                            &#34;flagType&#34;: 13,
                            &#34;value&#34;: &#34;{}&#34;.format(index_server_id),
                            &#34;key&#34;: &#34;RecoveryPointIndexServer&#34;
                        }
                    ]
                },
                &#34;applicationMining&#34;: {
                    &#34;appType&#34;: 137,
                    &#34;isApplicationMiningReq&#34;: True,
                    &#34;browseInitReq&#34;: {
                        &#34;bCreateRecoveryPoint&#34;: True,
                        &#34;jobId&#34;: int(job.job_id),
                        &#34;pointInTimeRange&#34;: {
                            &#34;fromTime&#34;: int(job.start_timestamp),
                            &#34;toTime&#34;: int(job.end_timestamp)
                        },
                        &#34;mbxInfo&#34;: [
                            {
                                &#34;smtpAdrress&#34;: mailbox_prop[&#39;mailbox_smtp&#39;],
                                &#34;mbxGUIDs&#34;: mailbox_prop[&#39;mailbox_guid&#39;]
                            }
                        ]
                    }
                }
            }
        },
        &#34;paths&#34;: [
            {
                &#34;path&#34;: &#34;\\MB\\{%s}&#34; % mailbox_prop[&#39;mailbox_guid&#39;]
            }
        ],
        &#34;entity&#34;: {
            &#34;subclientId&#34;: int(self.subclient_id),
            &#34;backupsetId&#34;: int(self._backupset_object.backupset_id),
            &#34;clientId&#34;: int(self._client_object.client_id)
        },
        &#34;timeRange&#34;: {
            &#34;fromTime&#34;: 0,
            &#34;toTime&#34;: int(job.start_timestamp)
        }
    }

    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, self._services[&#39;BROWSE&#39;], recovery_point_dict
    )

    if flag:
        if response and response.json():
            browse_response = response.json()

            if &#39;browseResponses&#39; in browse_response.keys():
                recovery_point_job_id = browse_response.get(&#39;browseResponses&#39;, [{}])[0].get(&#39;browseResult&#39;, {}).get(
                    &#39;advConfig&#39;, {}).get(
                    &#39;applicationMining&#39;, {}).get(&#39;browseInitResp&#39;, {}).get(&#39;recoveryPointJobID&#39;)

                recovery_point_id = browse_response.get(&#39;browseResponses&#39;, [{}])[0].get(&#39;browseResult&#39;, {}).get(
                    &#39;advConfig&#39;, {}).get(
                    &#39;applicationMining&#39;, {}).get(&#39;browseInitResp&#39;, {}).get(&#39;recoveryPointID&#39;, {})

                res_dict = {
                    &#39;recovery_point_job_id&#39;: recovery_point_job_id,
                    &#39;recovery_point_id&#39;: recovery_point_id
                }
                return res_dict
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;, response.json)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;, response.json)
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.delete_adgroup_assocaition"><code class="name flex">
<span>def <span class="ident">delete_adgroup_assocaition</span></span>(<span>self, subclient_content, use_policies=True)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes Ad groups assocaition for UserMailboxSubclient.</p>
<h2 id="args">Args</h2>
<p>subclient_content
(dict)
&ndash;
dict of the adgroups to delete from subclient</p>
<pre><code>subclient_content = {

    'adGroupNames' : ["List of names of adgroups],

    'is_auto_discover_user' : True,

    'archive_policy' : "CIPLAN Archiving policy",

    'cleanup_policy' : 'CIPLAN Clean-up policy',

    'retention_policy': 'CIPLAN Retention policy',
}
For plan:
    'is_auto_discover_user' : True,

    'plan_name': 'Exchange Plan Name',

    'plan_id': int or None (Optional)
</code></pre>
<p>use_policies(bool)
&ndash; if we need to use policy or plan</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/usermailbox_subclient.py#L1373-L1438" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete_adgroup_assocaition(self, subclient_content, use_policies=True):
    &#34;&#34;&#34;Deletes Ad groups assocaition for UserMailboxSubclient.

        Args:
            subclient_content   (dict)  --  dict of the adgroups to delete from subclient

                subclient_content = {

                    &#39;adGroupNames&#39; : [&#34;List of names of adgroups],

                    &#39;is_auto_discover_user&#39; : True,

                    &#39;archive_policy&#39; : &#34;CIPLAN Archiving policy&#34;,

                    &#39;cleanup_policy&#39; : &#39;CIPLAN Clean-up policy&#39;,

                    &#39;retention_policy&#39;: &#39;CIPLAN Retention policy&#39;,
                }
                For plan:
                    &#39;is_auto_discover_user&#39; : True,

                    &#39;plan_name&#39;: &#39;Exchange Plan Name&#39;,

                    &#39;plan_id&#39;: int or None (Optional)

            use_policies(bool)  -- if we need to use policy or plan
    &#34;&#34;&#34;
    adgroups = []

    if not isinstance(subclient_content, dict):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    if not (isinstance(subclient_content[&#39;adGroupNames&#39;], list) and
            isinstance(subclient_content[&#39;is_auto_discover_user&#39;], bool)):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    try:
        discover_adgroups = self.discover_adgroups

        for adgroup_item in subclient_content[&#39;adGroupNames&#39;]:

            for ad_item in discover_adgroups:

                if adgroup_item.lower() == ad_item[&#39;adGroupName&#39;].lower():
                    adgroup_dict = {
                        &#34;associated&#34;: False,
                        &#39;adGroupName&#39;: ad_item[&#39;adGroupName&#39;],
                    }
                    adgroups.append(adgroup_dict)

    except KeyError as err:
        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))

    discover_info = {
        &#34;discoverByType&#34;: 3,
        &#34;adGroups&#34;: adgroups
    }
    if use_policies:
        _assocaition_json_ = self._association_json(subclient_content)
    else:
        _assocaition_json_ = self._association_json_with_plan(subclient_content)
    _assocaition_json_[&#34;emailAssociation&#34;][&#34;emailStatus&#34;] = 1
    _assocaition_json_[&#34;emailAssociation&#34;][&#34;enableAutoDiscovery&#34;] = subclient_content[
        &#34;is_auto_discover_user&#34;]
    _assocaition_json_[&#34;emailAssociation&#34;][&#34;emailDiscoverinfo&#34;] = discover_info
    self._set_association_request(_assocaition_json_)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.delete_auto_discover_association"><code class="name flex">
<span>def <span class="ident">delete_auto_discover_association</span></span>(<span>self, association_name, subclient_content, use_policies=True)</span>
</code></dt>
<dd>
<div class="desc"><p>Delete all users association for UserMailboxSubclient.</p>
<pre><code>    Args:
        association_name  (str)  --  Type of auto discover association
            Valid Values:
                "All Users"
                "All O365 Mailboxes"
                "All Public Folders"

        use_policies (bool) -- If we need to use plan(False) or policy(True)

        subclient_content (dict) - containing the information of users/groups

            if use_policies is True
                subclient_content={
                    "is_auto_dicover_user" (bool): True
                    "archive_policy" (obj): Archive Policy object
                    "cleanup_policy" (obj): Cleanup Policy Object
                    "retention_policy" (obj): Retention Policy Object
                }

            if use_policies is False
                subclient_content={
                    "is_auto_discover_user" (bool): True,
                    "plan_name" (str): Name of the exchange plan,
                    "plan_id": Id of the plan(optional)
                }
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/usermailbox_subclient.py#L1559-L1618" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete_auto_discover_association(self, association_name, subclient_content, use_policies=True):
    &#34;&#34;&#34;
        Delete all users association for UserMailboxSubclient.

                Args:
                    association_name  (str)  --  Type of auto discover association
                        Valid Values:
                            &#34;All Users&#34;
                            &#34;All O365 Mailboxes&#34;
                            &#34;All Public Folders&#34;

                    use_policies (bool) -- If we need to use plan(False) or policy(True)

                    subclient_content (dict) - containing the information of users/groups

                        if use_policies is True
                            subclient_content={
                                &#34;is_auto_dicover_user&#34; (bool): True
                                &#34;archive_policy&#34; (obj): Archive Policy object
                                &#34;cleanup_policy&#34; (obj): Cleanup Policy Object
                                &#34;retention_policy&#34; (obj): Retention Policy Object
                            }

                        if use_policies is False
                            subclient_content={
                                &#34;is_auto_discover_user&#34; (bool): True,
                                &#34;plan_name&#34; (str): Name of the exchange plan,
                                &#34;plan_id&#34;: Id of the plan(optional)
                            }
    &#34;&#34;&#34;
    if not (isinstance(subclient_content, dict)):
        raise SDKException(&#34;Subclient&#34;, &#34;101&#34;)

    association_dict = {&#34;all users&#34;: 8,
                        &#34;all o365 group mailboxes&#34;: 11,
                        &#34;all public folders&#34;: 12
                        }

    if association_name.lower() not in association_dict:
        raise SDKException(&#34;Subclient&#34;, &#34;102&#34;, &#34;Invalid Association Name supplied&#34;)

    if use_policies:
        _association_json_ = self._association_json(subclient_content)
    else:
        planobject = self._commcell_object.plans.get(subclient_content[&#34;plan_name&#34;])
        _association_json_ = self._association_json_with_plan(plan_details=planobject)

    discover_info = {
        &#34;discoverByType&#34;: association_dict[association_name.lower()],
        &#34;genericAssociations&#34;: [
            {
                &#34;associationName&#34;: association_name,
                &#34;associationType&#34;: association_dict[association_name.lower()]
            }
        ]
    }
    _association_json_[&#34;emailAssociation&#34;][&#34;advanceOptions&#34;][&#34;enableAutoDiscovery&#34;] = True
    _association_json_[&#34;emailAssociation&#34;][&#34;emailStatus&#34;] = 1
    _association_json_[&#34;emailAssociation&#34;][&#34;emailDiscoverinfo&#34;] = discover_info
    self._set_association_request(_association_json_)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.delete_database_assocaition"><code class="name flex">
<span>def <span class="ident">delete_database_assocaition</span></span>(<span>self, subclient_content, use_policies=True)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes Database assocaition for UserMailboxSubclient.</p>
<h2 id="args">Args</h2>
<p>subclient_content
(dict)
&ndash;
dict of the databases to delete from subclient</p>
<pre><code>For policy -
subclient_content = {

    'databaseNames' : ["List of names of db"],

    'archive_policy' : "Archiving policy",

    'cleanup_policy' : 'Clean-up policy',

    'retention_policy': 'Retention policy'
}

For Plan -
subclient_content = { 'is_auto_discover_user' : True,
"plan_name": Name of plan, "plan_id"(optional) -- id of plan}
</code></pre>
<p>use_policies(bool)
&ndash; if we need to use policy or plan</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/usermailbox_subclient.py#L1308-L1371" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete_database_assocaition(self, subclient_content, use_policies=True):
    &#34;&#34;&#34;Deletes Database assocaition for UserMailboxSubclient.

        Args:
            subclient_content   (dict)  --  dict of the databases to delete from subclient

                For policy -
                subclient_content = {

                    &#39;databaseNames&#39; : [&#34;List of names of db&#34;],

                    &#39;archive_policy&#39; : &#34;Archiving policy&#34;,

                    &#39;cleanup_policy&#39; : &#39;Clean-up policy&#39;,

                    &#39;retention_policy&#39;: &#39;Retention policy&#39;
                }

                For Plan -
                subclient_content = { &#39;is_auto_discover_user&#39; : True,
                &#34;plan_name&#34;: Name of plan, &#34;plan_id&#34;(optional) -- id of plan}

            use_policies(bool)  -- if we need to use policy or plan
    &#34;&#34;&#34;
    databases = []

    if not isinstance(subclient_content, dict):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    if not (isinstance(subclient_content[&#39;databaseNames&#39;], list) and
            isinstance(subclient_content[&#39;is_auto_discover_user&#39;], bool)):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    try:
        discover_databases = self.discover_databases

        for database_item in subclient_content[&#39;databaseNames&#39;]:

            for db_item in discover_databases:

                if database_item.lower() == db_item[&#39;databaseName&#39;].lower():
                    database_dict = {
                        &#39;exchangeServer&#39;: db_item[&#39;exchangeServer&#39;],
                        &#34;associated&#34;: False,
                        &#39;databaseName&#39;: db_item[&#39;databaseName&#39;],
                    }
                    databases.append(database_dict)

    except KeyError as err:
        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))

    discover_info = {
        &#34;discoverByType&#34;: 2,
        &#34;databases&#34;: databases
    }

    if use_policies:
        _association_json_ = self._association_json(subclient_content)
    else:
        _association_json_ = self._association_json_with_plan(subclient_content)

    _association_json_[&#34;emailAssociation&#34;][&#34;emailStatus&#34;] = 1
    _association_json_[&#34;emailAssociation&#34;][&#34;emailDiscoverinfo&#34;] = discover_info
    self._update_association_request(_association_json_)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.delete_o365group_association"><code class="name flex">
<span>def <span class="ident">delete_o365group_association</span></span>(<span>self, subclient_content)</span>
</code></dt>
<dd>
<div class="desc"><p>delete O365 group association for UserMailboxSubclient.</p>
<h2 id="args">Args</h2>
<p>subclient_content
(dict)
&ndash;
dict of the Users to delete from subclient</p>
<pre><code>subclient_content = {

    'mailboxNames' : ["AutoCi2"],

    'archive_policy' : "CIPLAN Archiving policy",

    'cleanup_policy' : 'CIPLAN Clean-up policy',

    'retention_policy': 'CIPLAN Retention policy'
}
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/usermailbox_subclient.py#L1258-L1306" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete_o365group_association(self, subclient_content):
    &#34;&#34;&#34;delete O365 group association for UserMailboxSubclient.

        Args:
            subclient_content   (dict)  --  dict of the Users to delete from subclient

                subclient_content = {

                    &#39;mailboxNames&#39; : [&#34;AutoCi2&#34;],

                    &#39;archive_policy&#39; : &#34;CIPLAN Archiving policy&#34;,

                    &#39;cleanup_policy&#39; : &#39;CIPLAN Clean-up policy&#39;,

                    &#39;retention_policy&#39;: &#39;CIPLAN Retention policy&#39;
                }

    &#34;&#34;&#34;
    groups = []
    try:
        for mb_item in self.o365groups:
            mailbox_dict = {
                &#39;smtpAdrress&#39;: mb_item[&#39;smtp_address&#39;],
                &#39;aliasName&#39;: mb_item[&#39;alias_name&#39;],
                &#39;mailBoxType&#39;: 1,
                &#39;displayName&#39;: mb_item[&#39;display_name&#39;],
                &#39;exchangeServer&#39;: &#34;&#34;,
                &#39;isAutoDiscoveredUser&#39;: mb_item[&#39;is_auto_discover_user&#39;].lower() == &#39;true&#39;,
                &#39;msExchRecipientTypeDetails&#39;: 36,
                &#34;associated&#34;: False,
                &#39;databaseName&#39;: mb_item[&#39;database_name&#39;],
                &#39;user&#39;: {
                    &#39;_type_&#39;: 13,
                    &#39;userGUID&#39;: mb_item[&#39;user_guid&#39;]
                }
            }
            groups.append(mailbox_dict)

    except KeyError as err:
        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))

    discover_info = {
        &#34;discoverByType&#34;: 1,
        &#34;mailBoxes&#34;: groups
    }
    _assocaition_json_ = self._association_json(subclient_content, True)
    _assocaition_json_[&#34;emailAssociation&#34;][&#34;emailStatus&#34;] = 1
    _assocaition_json_[&#34;emailAssociation&#34;][&#34;emailDiscoverinfo&#34;] = discover_info
    self._update_association_request(_assocaition_json_)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.delete_user_assocaition"><code class="name flex">
<span>def <span class="ident">delete_user_assocaition</span></span>(<span>self, subclient_content, use_policies=True)</span>
</code></dt>
<dd>
<div class="desc"><p>delete User assocaition for UserMailboxSubclient.</p>
<h2 id="args">Args</h2>
<p>subclient_content
(dict)
&ndash;
dict of the Users to delete from subclient
subclient_content = {
'mailboxNames' : ["list of mailboxes alias name"],
&ndash; if use_policies is True &ndash;
'archive_policy' : "CIPLAN Archiving policy",</p>
<pre><code>    'cleanup_policy' : 'CIPLAN Clean-up policy',

    'retention_policy': 'CIPLAN Retention policy'
    --
    -- if use_policies is False --
    'plan_name': Plan Name,
    'plan_id': int or None (Optional)
    --
}
</code></pre>
<p>use_policies (bool) &ndash; If True uses policies else uses Plan</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/usermailbox_subclient.py#L1189-L1256" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete_user_assocaition(self, subclient_content, use_policies=True):
    &#34;&#34;&#34;delete User assocaition for UserMailboxSubclient.
        Args:
            subclient_content   (dict)  --  dict of the Users to delete from subclient
                subclient_content = {
                    &#39;mailboxNames&#39; : [&#34;list of mailboxes alias name&#34;],
                    -- if use_policies is True --
                    &#39;archive_policy&#39; : &#34;CIPLAN Archiving policy&#34;,

                    &#39;cleanup_policy&#39; : &#39;CIPLAN Clean-up policy&#39;,

                    &#39;retention_policy&#39;: &#39;CIPLAN Retention policy&#39;
                    --
                    -- if use_policies is False --
                    &#39;plan_name&#39;: Plan Name,
                    &#39;plan_id&#39;: int or None (Optional)
                    --
                }
            use_policies (bool) -- If True uses policies else uses Plan
    &#34;&#34;&#34;
    users = []

    if not isinstance(subclient_content, dict):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    if not (isinstance(subclient_content[&#39;mailboxNames&#39;], list)):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    try:
        discover_users = self.discover_users

        for mailbox_item in subclient_content[&#39;mailboxNames&#39;]:

            for mb_item in discover_users:

                if mailbox_item.lower() == mb_item[&#39;aliasName&#39;].lower():
                    mailbox_dict = {
                        &#39;smtpAdrress&#39;: mb_item[&#39;smtpAdrress&#39;],
                        &#39;aliasName&#39;: mb_item[&#39;aliasName&#39;],
                        &#39;mailBoxType&#39;: mb_item[&#39;mailBoxType&#39;],
                        &#39;displayName&#39;: mb_item[&#39;displayName&#39;],
                        &#39;exchangeServer&#39;: mb_item[&#39;exchangeServer&#39;],
                        &#39;isAutoDiscoveredUser&#39;: mb_item[&#39;isAutoDiscoveredUser&#39;],
                        &#34;associated&#34;: False,
                        &#39;databaseName&#39;: mb_item[&#39;databaseName&#39;],
                        &#34;exchangeVersion&#34;: mb_item[&#39;exchangeVersion&#39;],
                        &#34;exchangeServer&#34;: mb_item[&#39;exchangeServer&#39;],
                        &#39;user&#39;: {
                            &#39;_type_&#39;: 13,
                            &#39;userGUID&#39;: mb_item[&#39;user&#39;][&#39;userGUID&#39;]
                        }
                    }
                    users.append(mailbox_dict)
                    break

    except KeyError as err:
        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))
    discover_info = {
        &#34;discoverByType&#34;: 1,
        &#34;mailBoxes&#34;: users
    }
    if use_policies:
        _association_json_ = self._association_json(subclient_content)
    else:
        _association_json_ = self._association_json_with_plan(subclient_content)
    _association_json_[&#34;emailAssociation&#34;][&#34;emailStatus&#34;] = 1
    _association_json_[&#34;emailAssociation&#34;][&#34;emailDiscoverinfo&#34;] = discover_info
    self._update_association_request(_association_json_)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.disable_allusers_associations"><code class="name flex">
<span>def <span class="ident">disable_allusers_associations</span></span>(<span>self, use_policies=True, plan_details=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Disables alluser assocaition for UserMailboxSubclient.
use_policies (bool) &ndash; If we need to use plan(False) or policy(True)
plan_details(dict) &ndash; { "plan_name": Name of plan, "plan_id"(optional) &ndash; id of plan}</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/usermailbox_subclient.py#L1487-L1513" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def disable_allusers_associations(self, use_policies=True, plan_details=None):
    &#34;&#34;&#34;Disables alluser assocaition for UserMailboxSubclient.
        use_policies (bool) -- If we need to use plan(False) or policy(True)
        plan_details(dict) -- { &#34;plan_name&#34;: Name of plan, &#34;plan_id&#34;(optional) -- id of plan}
    &#34;&#34;&#34;
    subclient_content = {
        &#39;is_auto_discover_user&#39;: True
    }
    discover_info = {
        &#34;discoverByType&#34;: 8,
        &#34;genericAssociations&#34;: [
            {
                &#34;associationName&#34;: &#34;All Users&#34;,
                &#34;associationType&#34;: 8
            }
        ]
    }

    if use_policies:
        _association_json_ = self._association_json(subclient_content)
    else:
        _association_json_ = self._association_json_with_plan(plan_details=plan_details)

    _association_json_[&#34;emailAssociation&#34;][&#34;emailDiscoverinfo&#34;] = discover_info
    _association_json_[&#34;emailAssociation&#34;][&#34;emailStatus&#34;] = 2
    _association_json_[&#39;emailAssociation&#39;][&#39;advanceOptions&#39;] = {&#34;enableAutoDiscovery&#34;: True}
    self._set_association_request(_association_json_)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.enable_allusers_associations"><code class="name flex">
<span>def <span class="ident">enable_allusers_associations</span></span>(<span>self, subclient_content, use_policies=True)</span>
</code></dt>
<dd>
<div class="desc"><p>Enable all users assocaition for UserMailboxSubclient.</p>
<h2 id="args">Args</h2>
<p>subclient_content
(dict)
&ndash;
dict of the policies which needs to be
assigned to all user assocaitions
For policy
subclient_content = {</p>
<pre><code>    'is_auto_discover_user' : True

    'archive_policy' : "CIPLAN Archiving policy",

    'cleanup_policy' : 'CIPLAN Clean-up policy',

    'retention_policy': 'CIPLAN Retention policy',
}

For Plan -
subclient_content = { 'is_auto_discover_user' : True,
"plan_name": Name of plan, "plan_id"(optional) -- id of plan
}
</code></pre>
<p>use_policies(bool)
&ndash; if we need to use policy or plan</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/usermailbox_subclient.py#L1440-L1485" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable_allusers_associations(self, subclient_content, use_policies=True):
    &#34;&#34;&#34;Enable all users assocaition for UserMailboxSubclient.

        Args:
            subclient_content   (dict)  --  dict of the policies which needs to be
            assigned to all user assocaitions
                For policy
                subclient_content = {

                    &#39;is_auto_discover_user&#39; : True

                    &#39;archive_policy&#39; : &#34;CIPLAN Archiving policy&#34;,

                    &#39;cleanup_policy&#39; : &#39;CIPLAN Clean-up policy&#39;,

                    &#39;retention_policy&#39;: &#39;CIPLAN Retention policy&#39;,
                }

                For Plan -
                subclient_content = { &#39;is_auto_discover_user&#39; : True,
                &#34;plan_name&#34;: Name of plan, &#34;plan_id&#34;(optional) -- id of plan
                }

            use_policies(bool)  -- if we need to use policy or plan

    &#34;&#34;&#34;

    if not isinstance(subclient_content, dict):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    discover_info = {
        &#34;discoverByType&#34;: 8,
        &#34;genericAssociations&#34;: [
            {
                &#34;associationName&#34;: &#34;All Users&#34;,
                &#34;associationType&#34;: 8
            }
        ]
    }
    if use_policies:
        _association_json_ = self._association_json(subclient_content)
    else:
        _association_json_ = self._association_json_with_plan(plan_details=subclient_content)
    _association_json_[&#39;emailAssociation&#39;][&#39;advanceOptions&#39;] = {&#34;enableAutoDiscovery&#34;: True}
    _association_json_[&#34;emailAssociation&#34;][&#34;emailDiscoverinfo&#34;] = discover_info
    self._set_association_request(_association_json_)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.enable_auto_discover_association"><code class="name flex">
<span>def <span class="ident">enable_auto_discover_association</span></span>(<span>self, association_name, plan_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Enable all users assocaition for UserMailboxSubclient.</p>
<h2 id="args">Args</h2>
<p>association_name
(str)
&ndash;
Type of auto discover association
Valid Values:
"All Users"
"All O365 Mailboxes"
"All Public Folders"</p>
<p>plan_name
(str)
&ndash;
Name of the plan to associate with users/groups</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/usermailbox_subclient.py#L1515-L1557" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable_auto_discover_association(self, association_name, plan_name):
    &#34;&#34;&#34;Enable all users assocaition for UserMailboxSubclient.

                Args:
                    association_name  (str)  --  Type of auto discover association
                        Valid Values:
                            &#34;All Users&#34;
                            &#34;All O365 Mailboxes&#34;
                            &#34;All Public Folders&#34;

                    plan_name  (str)  --  Name of the plan to associate with users/groups


            &#34;&#34;&#34;
    plan = self._commcell_object.plans.get(plan_name)

    association_dict = {&#34;All Users&#34;: 8,
                        &#34;All Office365 Groups&#34;: 11,
                        &#34;All Public Folders&#34;: 12
                        }

    _association_json = {
        &#34;emailAssociation&#34;: {
            &#34;emailStatus&#34;: 0,
            &#34;advanceOptions&#34;: {
                &#34;enableAutoDiscovery&#34;: True
            },
            &#34;subclientEntity&#34;: self._subClientEntity,
            &#34;emailDiscoverinfo&#34;: {
                &#34;discoverByType&#34;: association_dict[association_name],
                &#34;genericAssociations&#34;: [
                    {
                        &#34;associationName&#34;: association_name,
                        &#34;associationType&#34;: association_dict[association_name]
                    }
                ]
            },
            &#34;plan&#34;: {
                &#34;planId&#34;: int(plan.plan_id)
            }
        }
    }
    self._set_association_request(_association_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.enable_ews_support"><code class="name flex">
<span>def <span class="ident">enable_ews_support</span></span>(<span>self, service_url)</span>
</code></dt>
<dd>
<div class="desc"><p>This function provides support for EWS protocol to backup on-prem mailboxes</p>
<h2 id="args">Args</h2>
<p>service_url (string) &ndash; EWS Connection URL for your exchange server
Returns: None</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/usermailbox_subclient.py#L1620-L1629" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable_ews_support(self, service_url):
    &#34;&#34;&#34;This function provides support for EWS protocol to backup on-prem mailboxes
        Args:
            service_url (string) -- EWS Connection URL for your exchange server
        Returns: None
    &#34;&#34;&#34;
    self.agentproperties = self._agent_object.properties
    self.agentproperties[&#34;onePassProperties&#34;][&#34;onePassProp&#34;][&#34;ewsDetails&#34;][&#34;bUseEWS&#34;] = True
    self.agentproperties[&#34;onePassProperties&#34;][&#34;onePassProp&#34;][&#34;ewsDetails&#34;][&#34;ewsConnectionUrl&#34;] = service_url
    self._agent_object.update_properties(self.agentproperties)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.find_mailbox"><code class="name flex">
<span>def <span class="ident">find_mailbox</span></span>(<span>self, mailbox_smtp, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Performs the find operation for a mailbox in browse
Args:
mailbox_smtp
(str) &ndash; SMTP address of the mailbox to find</p>
<pre><code>    kwargs                -- Optional key word args
Returns:
        response json for find mailbox
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/usermailbox_subclient.py#L1945-L1984" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def find_mailbox(self, mailbox_smtp, **kwargs):
    &#34;&#34;&#34;
    Performs the find operation for a mailbox in browse
        Args:
            mailbox_smtp    (str) -- SMTP address of the mailbox to find

            kwargs                -- Optional key word args
        Returns:
                response json for find mailbox
    &#34;&#34;&#34;

    data = ExchangeConstants.FIND_MAILBOX_REQUEST_DATA

    data[&#34;advSearchGrp&#34;][&#34;emailFilter&#34;][0][&#34;filter&#34;][&#34;filters&#34;].append(
        {
            &#34;field&#34;: &#34;CUSTODIAN&#34;,
            &#34;intraFieldOp&#34;: 0,
            &#34;fieldValues&#34;: {
                &#34;values&#34;: [
                    mailbox_smtp
                ]
            }
        }
    )
    data[&#34;advSearchGrp&#34;][&#34;galaxyFilter&#34;][0][&#34;appIdList&#34;] = [int(self.subclient_id)]
    data[&#34;facetRequests&#34;][&#34;facetRequest&#34;] = UsermailboxSubclient._find_mailbox_facets(
        kwargs.get(&#34;facets_params&#34;, None))
    data[&#34;searchProcessingInfo&#34;][&#34;pageSize&#34;] = int(kwargs.get(&#34;page_size&#34;, 100))
    data[&#34;searchProcessingInfo&#34;][&#34;queryParams&#34;] = UsermailboxSubclient._find_mailbox_query_params(
        kwargs.get(&#34;query_params&#34;, None))

    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, self._services[&#39;DO_WEB_SEARCH&#39;], data
    )
    if flag:
        if response and response.json():
            return response.json()
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the User Mailbox Subclient.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/usermailbox_subclient.py#L1819-L1827" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the User Mailbox Subclient.&#34;&#34;&#34;
    self._get_subclient_properties()
    self._discover_users = self._get_discover_users()
    self._discover_databases = self._get_discover_database()
    self._discover_adgroups = self._get_discover_adgroups()
    self._users, self._o365groups = self._get_user_assocaitions()
    self._databases = self._get_database_associations()
    self._adgroups = self._get_adgroup_assocaitions()</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.restore_in_place_syntex"><code class="name flex">
<span>def <span class="ident">restore_in_place_syntex</span></span>(<span>self, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Runs an in-place restore job on the specified Syntex Exchange pseudo client</p>
<p>Kwargs:</p>
<pre><code> paths (list)  --  list of paths of mailboxes/folders to restore
</code></pre>
<p>Returns:</p>
<pre><code>Job object
</code></pre>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if paths is not a list

if failed to initialize job

if response is empty
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/usermailbox_subclient.py#L1829-L1901" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_in_place_syntex(self, **kwargs):
    &#34;&#34;&#34;Runs an in-place restore job on the specified Syntex Exchange pseudo client

         Kwargs:

             paths (list)  --  list of paths of mailboxes/folders to restore

         Returns:

            Job object

        Raises:

            SDKException:

                if paths is not a list

                if failed to initialize job

                if response is empty

    &#34;&#34;&#34;
    paths = kwargs.get(&#39;paths&#39;, [])
    paths = self._filter_paths(paths)
    self._json_restore_exchange_restore_option({})
    self._json_backupset()
    restore_option = {&#34;paths&#34;: paths}

    self._instance_object._restore_association = self._subClientEntity
    request_json = self._restore_json(restore_option=restore_option)
    request_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;subclientName&#39;] = self.subclient_name
    request_json[&#39;taskInfo&#39;][&#39;associations&#39;][0][
        &#39;backupsetName&#39;] = self._backupset_object.backupset_name
    request_json[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][
        &#39;options&#39;][&#39;restoreOptions&#39;][&#39;exchangeOption&#39;] = self._exchange_option_restore_json
    request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
        &#34;restoreOptions&#34;][&#34;browseOption&#34;][&#39;backupset&#39;] = self._exchange_backupset_json

    mailboxes = self.browse_mailboxes()
    mailbox_details = {}
    for path in paths:
        mailbox_details[path] = next((mailbox for mailbox in mailboxes if mailbox[&#39;aliasName&#39;] == path), None)
    syntex_restore_items = []

    for key, value in mailbox_details.items():
        syntex_restore_items.append({
            &#34;displayName&#34;: value[&#34;displayName&#34;],
            &#34;guid&#34;: value[&#34;user&#34;][&#34;userGUID&#34;],
            &#34;rawId&#34;: value[&#34;user&#34;][&#34;userGUID&#34;],
            &#34;restoreType&#34;: 1
        })

    # Get the current time in UTC
    current_time = datetime.datetime.now(datetime.timezone.utc)
    current_timestamp = int(current_time.timestamp())
    current_iso_format = current_time.strftime(&#39;%Y-%m-%dT%H:%M:%S.%f&#39;)[:-3] + &#39;Z&#39;

    request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;] = {}
    request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;][
        &#34;msSyntexRestoreOptions&#34;] = {
        &#34;msSyntexRestoreItems&#34;: {
            &#34;listMsSyntexRestoreItems&#34;: syntex_restore_items
        },
        &#34;restoreDate&#34;: {
            &#34;time&#34;: current_timestamp,
            &#34;timeValue&#34;: current_iso_format
        },
        &#34;restorePointId&#34;: &#34;&#34;,
        &#34;restoreType&#34;: 1,
        &#34;useFastRestorePoint&#34;: False
    }

    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.set_adgroup_associations"><code class="name flex">
<span>def <span class="ident">set_adgroup_associations</span></span>(<span>self, subclient_content, use_policies=True)</span>
</code></dt>
<dd>
<div class="desc"><p>Create Ad groups assocaition for UserMailboxSubclient.</p>
<h2 id="args">Args</h2>
<p>subclient_content
(dict)
&ndash;
dict of the adgroups to add to the subclient</p>
<pre><code>subclient_content = {

    'adGroupNames' : ["List of adGroups"],

    'is_auto_discover_user' : True,

    'archive_policy' : "Archiving policy",

    'cleanup_policy' : 'Clean-up policy',

    'retention_policy': 'Retention policy',
}
    -- if use_policies is False --

    'plan_name': 'Exchange Plan Name',
    'is_auto_discover_user' : True,
    'plan_id': int or None (Optional)
    --
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/usermailbox_subclient.py#L1096-L1160" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_adgroup_associations(self, subclient_content, use_policies=True):
    &#34;&#34;&#34;Create Ad groups assocaition for UserMailboxSubclient.

        Args:
            subclient_content   (dict)  --  dict of the adgroups to add to the subclient

                subclient_content = {

                    &#39;adGroupNames&#39; : [&#34;List of adGroups&#34;],

                    &#39;is_auto_discover_user&#39; : True,

                    &#39;archive_policy&#39; : &#34;Archiving policy&#34;,

                    &#39;cleanup_policy&#39; : &#39;Clean-up policy&#39;,

                    &#39;retention_policy&#39;: &#39;Retention policy&#39;,
                }
                    -- if use_policies is False --

                    &#39;plan_name&#39;: &#39;Exchange Plan Name&#39;,
                    &#39;is_auto_discover_user&#39; : True,
                    &#39;plan_id&#39;: int or None (Optional)
                    --

    &#34;&#34;&#34;
    adgroups = []

    if not isinstance(subclient_content, dict):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    if not (isinstance(subclient_content[&#39;adGroupNames&#39;], list) and
            isinstance(subclient_content[&#39;is_auto_discover_user&#39;], bool)):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    try:
        discover_adgroups = self.discover_adgroups

        for adgroup_item in subclient_content[&#39;adGroupNames&#39;]:

            for ad_item in discover_adgroups:

                if adgroup_item.lower() == ad_item[&#39;adGroupName&#39;].lower():
                    adgroup_dict = {
                        &#39;adGroupName&#39;: ad_item[&#39;adGroupName&#39;],
                    }
                    adgroups.append(adgroup_dict)

    except KeyError as err:
        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))

    discover_info = {
        &#34;discoverByType&#34;: 3,
        &#34;adGroups&#34;: adgroups
    }
    if use_policies:
        _assocaition_json_ = self._association_json(subclient_content)
    else:
        _assocaition_json_ = self._association_json_with_plan(subclient_content)
    _assocaition_json_[&#34;emailAssociation&#34;][&#34;emailDiscoverinfo&#34;] = discover_info
    _assocaition_json_[&#34;emailAssociation&#34;].update({&#34;emailStatus&#34;: 0})
    _assocaition_json_[&#34;emailAssociation&#34;].update({&#34;advanceOptions&#34;: {
        &#34;enableAutoDiscovery&#34;: subclient_content[&#34;is_auto_discover_user&#34;]
    }})
    self._set_association_request(_assocaition_json_)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.set_database_assocaition"><code class="name flex">
<span>def <span class="ident">set_database_assocaition</span></span>(<span>self, subclient_content, use_policies=True)</span>
</code></dt>
<dd>
<div class="desc"><p>Create Database assocaition for UserMailboxSubclient.</p>
<h2 id="args">Args</h2>
<p>subclient_content
(dict)
&ndash;
dict of the databases to add to the subclient</p>
<pre><code>For policy -
subclient_content = {

    'databaseNames' : ["Name of dbs as list"],

    'is_auto_discover_user' : True,

    'archive_policy' : "CIPLAN Archiving policy",

    'cleanup_policy' : 'CIPLAN Clean-up policy',

    'retention_policy': 'CIPLAN Retention policy'
}

For Plan -
subclient_content = { 'is_auto_discover_user': True,
"plan_name": Name of plan, "plan_id"(optional) -- id of plan}
</code></pre>
<p>use_policies(bool)
&ndash; if we need to use policy or plan</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/usermailbox_subclient.py#L1027-L1094" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_database_assocaition(self, subclient_content, use_policies=True):
    &#34;&#34;&#34;Create Database assocaition for UserMailboxSubclient.

        Args:
            subclient_content   (dict)  --  dict of the databases to add to the subclient

                For policy -
                subclient_content = {

                    &#39;databaseNames&#39; : [&#34;Name of dbs as list&#34;],

                    &#39;is_auto_discover_user&#39; : True,

                    &#39;archive_policy&#39; : &#34;CIPLAN Archiving policy&#34;,

                    &#39;cleanup_policy&#39; : &#39;CIPLAN Clean-up policy&#39;,

                    &#39;retention_policy&#39;: &#39;CIPLAN Retention policy&#39;
                }

                For Plan -
                subclient_content = { &#39;is_auto_discover_user&#39;: True,
                &#34;plan_name&#34;: Name of plan, &#34;plan_id&#34;(optional) -- id of plan}

            use_policies(bool)  -- if we need to use policy or plan
            
            
    &#34;&#34;&#34;
    databases = []

    if not isinstance(subclient_content, dict):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    if not (isinstance(subclient_content[&#39;databaseNames&#39;], list) and
            isinstance(subclient_content[&#39;is_auto_discover_user&#39;], bool)):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    try:
        discover_databases = self.discover_databases

        for database_item in subclient_content[&#39;databaseNames&#39;]:

            for db_item in discover_databases:

                if database_item.lower() == db_item[&#39;databaseName&#39;].lower():
                    database_dict = {
                        &#39;exchangeServer&#39;: db_item[&#39;exchangeServer&#39;],
                        &#34;associated&#34;: False,
                        &#39;databaseName&#39;: db_item[&#39;databaseName&#39;],
                    }
                    databases.append(database_dict)

    except KeyError as err:
        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))

    discover_info = {
        &#34;discoverByType&#34;: 2,
        &#34;databases&#34;: databases
    }
    if use_policies:
        _association_json_ = self._association_json(subclient_content)
    else:
        _association_json_ = self._association_json_with_plan(subclient_content)
        _association_json_[&#34;emailAssociation&#34;][&#34;advanceOptions&#34;] = {
            &#34;enableAutoDiscovery&#34;: subclient_content.get(&#34;is_auto_discover_user&#34;, False)
        }
    _association_json_[&#34;emailAssociation&#34;][&#34;emailDiscoverinfo&#34;] = discover_info
    self._set_association_request(_association_json_)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.set_fs_association_for_pst"><code class="name flex">
<span>def <span class="ident">set_fs_association_for_pst</span></span>(<span>self, association)</span>
</code></dt>
<dd>
<div class="desc"><p>Helper method to create pst association for PST Ingestion by FS</p>
<h2 id="args">Args</h2>
<p>association(dict) &ndash; Dictionary of client, backupset, subclient
Ex: {'client1':{'backupset1':[subclient1], 'backupset2':None},
'client2': None}
This would add subclient1, all subclients under backupset2 and
all backupsets under client2 to the association</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/usermailbox_subclient.py#L971-L1025" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_fs_association_for_pst(self, association):
    &#34;&#34;&#34;Helper method to create pst association for PST Ingestion by FS
        Args:
            association(dict) -- Dictionary of client, backupset, subclient
                                Ex: {&#39;client1&#39;:{&#39;backupset1&#39;:[subclient1], &#39;backupset2&#39;:None},
                                    &#39;client2&#39;: None}
                                This would add subclient1, all subclients under backupset2 and
                                all backupsets under client2 to the association
    &#34;&#34;&#34;
    assoc_list = []
    client_dict, backupset_dict, sub_dict = dict(), dict(), dict()
    _type_id = {&#34;client&#34;: 3, &#34;subclient&#34;: 7, &#34;backupset&#34;: 6, &#34;apptype&#34;: 4}
    for client_name, backupsets in association.items():
        client_name = client_name.lower()
        client_obj = self._commcell_object.clients.get(client_name)

        client_dict = {&#34;commCellId&#34;: int(self._commcell_object._id),
                       &#34;commcellName&#34;: self._commcell_object.commserv_name,
                       &#34;clientName&#34;: client_name,
                       &#34;clientId&#34;: int(client_obj.client_id)
                       }
        agent = client_obj.agents.get(&#34;file system&#34;)

        if backupsets:
            for backupset_name, subclients in backupsets.items():
                backupset_name = backupset_name.lower()
                backupset_obj = agent.backupsets.get(backupset_name)
                if not backupset_obj:
                    raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#34;Backupset {0} not present in &#34;
                                                           &#34;&#34;.format(backupset_name))
                backupset_dict = {&#34;backupsetName&#34;: backupset_obj.name,
                                  &#34;appName&#34;: &#34;File System&#34;,
                                  &#34;applicationId&#34;: int(agent.agent_id),
                                  &#34;backupsetId&#34;: int(backupset_obj.backupset_id),
                                  &#34;_type_&#34;: _type_id[&#34;backupset&#34;]
                                  }
                backupset_dict.update(client_dict)
                for subclient_name in subclients:
                    if subclient_name.lower() not in backupset_obj.subclients.all_subclients:
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;,
                                           &#34;Subclient %s not present in backupset %s&#34; %
                                           (str(subclient_name), str(backupset_name)))
                    subclient_name = subclient_name.lower()
                    sub_dict = {&#34;subclientId&#34;: int(backupset_obj.subclients.all_subclients[
                                                       subclient_name][&#39;id&#39;]),
                                &#34;subclientName&#34;: subclient_name}
                    sub_dict.update(backupset_dict)
                    sub_dict[&#34;_type_&#34;] = _type_id[&#34;subclient&#34;]
                    assoc_list.append(sub_dict)
                if not subclients:
                    assoc_list.append(backupset_dict)
        else:
            client_dict[&#34;_type_&#34;] = _type_id[&#34;client&#34;]
            assoc_list.append(client_dict)
    return assoc_list</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.set_o365group_asscoiations"><code class="name flex">
<span>def <span class="ident">set_o365group_asscoiations</span></span>(<span>self, subclient_content)</span>
</code></dt>
<dd>
<div class="desc"><p>Create O365 Group association for UserMailboxSubclient.</p>
<h2 id="args">Args</h2>
<p>subclient_content
(dict)
&ndash;
dict of the policies to associate</p>
<pre><code>subclient_content = {

    'archive_policy' : "CIPLAN Archiving policy",

    'cleanup_policy' : 'CIPLAN Clean-up policy',

    'retention_policy': 'CIPLAN Retention policy'
}
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/usermailbox_subclient.py#L1162-L1187" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_o365group_asscoiations(self, subclient_content):
    &#34;&#34;&#34;Create O365 Group association for UserMailboxSubclient.
        Args:
            subclient_content   (dict)  --  dict of the policies to associate

                subclient_content = {

                    &#39;archive_policy&#39; : &#34;CIPLAN Archiving policy&#34;,

                    &#39;cleanup_policy&#39; : &#39;CIPLAN Clean-up policy&#39;,

                    &#39;retention_policy&#39;: &#39;CIPLAN Retention policy&#39;
                }
    &#34;&#34;&#34;
    discover_info = {
        &#34;discoverByType&#34;: 11,
        &#34;genericAssociations&#34;: [
            {
                &#34;associationName&#34;: &#34;All O365 Group Mailboxes&#34;,
                &#34;associationType&#34;: 11
            }
        ]
    }
    _assocaition_json_ = self._association_json(subclient_content, True)
    _assocaition_json_[&#34;emailAssociation&#34;][&#34;emailDiscoverinfo&#34;] = discover_info
    self._set_association_request(_assocaition_json_)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.set_pst_association"><code class="name flex">
<span>def <span class="ident">set_pst_association</span></span>(<span>self, subclient_content)</span>
</code></dt>
<dd>
<div class="desc"><p>Create PST assocaition for UserMailboxSubclient.</p>
<h2 id="args">Args</h2>
<p>subclient_content
(dict)
&ndash;
dict of the pst to add to the subclient</p>
<pre><code>subclient_content = {

    'pstTaskName' : "Task Name for PST",

    'folders' : ['list of folders'] //If pst ingestion by folder location,
    'fsContent': Dictionary of client, backupset, subclient
    Ex: {'client1':{'backupset1':[subclient1], 'backupset2':None},
        'client2': None}
    This would add subclient1, all subclients under backupset2 and
    all backupsets under client2 to the association

    'pstOwnerManagement' : {

        'defaultOwner': "default owner if no owner is determined",

        'pstDestFolder': "ingest psts under this folder",

        'usePSTNameToCreateChild': Boolean
    }
}
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/usermailbox_subclient.py#L887-L969" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_pst_association(self, subclient_content):
    &#34;&#34;&#34;Create PST assocaition for UserMailboxSubclient.

        Args:
            subclient_content   (dict)  --  dict of the pst to add to the subclient

                subclient_content = {

                    &#39;pstTaskName&#39; : &#34;Task Name for PST&#34;,

                    &#39;folders&#39; : [&#39;list of folders&#39;] //If pst ingestion by folder location,
                    &#39;fsContent&#39;: Dictionary of client, backupset, subclient
                    Ex: {&#39;client1&#39;:{&#39;backupset1&#39;:[subclient1], &#39;backupset2&#39;:None},
                        &#39;client2&#39;: None}
                    This would add subclient1, all subclients under backupset2 and
                    all backupsets under client2 to the association

                    &#39;pstOwnerManagement&#39; : {

                        &#39;defaultOwner&#39;: &#34;default owner if no owner is determined&#34;,

                        &#39;pstDestFolder&#39;: &#34;ingest psts under this folder&#34;,

                        &#39;usePSTNameToCreateChild&#39;: Boolean
                    }
                }
    &#34;&#34;&#34;
    if not isinstance(subclient_content, dict):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    try:
        if &#39;ownerSelectionOrder&#39; not in subclient_content[&#39;pstOwnerManagement&#39;]:
            subclient_content[&#39;pstOwnerManagement&#39;][&#39;ownerSelectionOrder&#39;] = [4, 1, 3]
        if &#39;createPstDestFolder&#39; not in subclient_content[&#39;pstOwnerManagement&#39;]:
            subclient_content[&#39;pstOwnerManagement&#39;][&#39;createPstDestFolder&#39;] = True
        if &#39;pstDestFolder&#39; not in subclient_content[&#39;pstOwnerManagement&#39;]:
            subclient_content[&#39;pstOwnerManagement&#39;][&#39;pstDestFolder&#39;] = (f&#39;Archived From &#39;
                                                                        f&#39;Automation&#39;)

        pst_dict = {
            &#39;pstTaskName&#39;: subclient_content[&#39;pstTaskName&#39;],
            &#39;taskType&#39;: 1,
            &#39;pstOwnerManagement&#39;: {
                &#39;adProperty&#39;: &#34;&#34;,
                &#39;startingFolderPath&#39;: &#34;&#34;,
                &#39;pstStubsAction&#39;: 1,
                &#39;managePSTStubs&#39;: False,
                &#39;mergeintoMailBox&#39;: True,
                &#39;pstOwnerBasedOnACL&#39;: True,
                &#39;pstOwnerBasedOnLaptop&#39;: False,
                &#39;usePSTNameToCreateChildForNoOwner&#39;: True,
                &#39;createPstDestFolder&#39;:
                    subclient_content[&#34;pstOwnerManagement&#34;][&#34;createPstDestFolder&#34;],
                &#39;orphanFolder&#39;: subclient_content[&#39;pstOwnerManagement&#39;][&#39;defaultOwner&#39;],
                &#39;pstDestFolder&#39;: subclient_content[&#39;pstOwnerManagement&#39;][&#39;pstDestFolder&#39;],
                &#39;usePSTNameToCreateChild&#39;:
                    subclient_content[&#39;pstOwnerManagement&#39;][&#39;usePSTNameToCreateChild&#39;],
                &#39;ownerSelectionOrder&#39;:
                    subclient_content[&#34;pstOwnerManagement&#34;][&#34;ownerSelectionOrder&#34;]
            }
        }
        if &#39;folders&#39; in subclient_content:
            pst_dict[&#39;folders&#39;] = subclient_content[&#39;folders&#39;]
        elif &#39;fsContent&#39; in subclient_content:
            pst_dict[&#39;associations&#39;] = self.set_fs_association_for_pst(
                subclient_content[&#39;fsContent&#39;])
            pst_dict[&#39;taskType&#39;] = 0
        subclient_entity = {&#34;_type_&#34;: 7, &#34;subclientId&#34;: int(self._subclient_id)}
        discover_info = {
            &#39;discoverByType&#39;: 9,
            &#39;pstIngestion&#39;: pst_dict
        }
        _assocaition_json_ = {
            &#34;emailAssociation&#34;:
                {
                    &#34;emailDiscoverinfo&#34;: discover_info,
                    &#34;subclientEntity&#34;: subclient_entity
                }
        }
        self._set_association_request(_assocaition_json_)

    except KeyError as err:
        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.set_user_assocaition"><code class="name flex">
<span>def <span class="ident">set_user_assocaition</span></span>(<span>self, subclient_content, use_policies=True)</span>
</code></dt>
<dd>
<div class="desc"><p>Create User assocaition for UserMailboxSubclient.</p>
<h2 id="args">Args</h2>
<p>subclient_content
(dict)
&ndash;
dict of the Users to add to the subclient</p>
<pre><code>subclient_content = {

    'mailboxNames' : ["AutoCi2"],,

    -- if use_policies is True --

    'archive_policy' : "CIPLAN Archiving policy",

    'cleanup_policy' : 'CIPLAN Clean-up policy',

    'retention_policy': 'CIPLAN Retention policy'

    -- if use_policies is False --

    'plan_name': 'Exchange Plan Name',

    'plan_id': int or None (Optional)
    --
}
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/exchange/usermailbox_subclient.py#L802-L885" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_user_assocaition(self, subclient_content, use_policies=True):
    &#34;&#34;&#34;Create User assocaition for UserMailboxSubclient.

        Args:
            subclient_content   (dict)  --  dict of the Users to add to the subclient

                subclient_content = {

                    &#39;mailboxNames&#39; : [&#34;AutoCi2&#34;],,

                    -- if use_policies is True --

                    &#39;archive_policy&#39; : &#34;CIPLAN Archiving policy&#34;,

                    &#39;cleanup_policy&#39; : &#39;CIPLAN Clean-up policy&#39;,

                    &#39;retention_policy&#39;: &#39;CIPLAN Retention policy&#39;

                    -- if use_policies is False --

                    &#39;plan_name&#39;: &#39;Exchange Plan Name&#39;,

                    &#39;plan_id&#39;: int or None (Optional)
                    --
                }

    &#34;&#34;&#34;
    users = []

    if not isinstance(subclient_content, dict):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    if not (isinstance(subclient_content[&#39;mailboxNames&#39;], list)):
        raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

    try:
        discover_users = self.discover_users

        for mailbox_item in subclient_content[&#39;mailboxNames&#39;]:

            for mb_item in discover_users:

                if mailbox_item.lower() == mb_item[&#39;aliasName&#39;].lower() or \
                        mailbox_item.lower() == mb_item[&#39;smtpAdrress&#39;].lower():
                    mailbox_dict = {
                        &#39;smtpAdrress&#39;: mb_item[&#39;smtpAdrress&#39;],
                        &#39;aliasName&#39;: mb_item[&#39;aliasName&#39;],
                        &#39;mailBoxType&#39;: mb_item[&#39;mailBoxType&#39;],
                        &#39;displayName&#39;: mb_item[&#39;displayName&#39;],
                        &#39;exchangeServer&#39;: mb_item[&#39;exchangeServer&#39;],
                        &#39;isAutoDiscoveredUser&#39;: mb_item[&#39;isAutoDiscoveredUser&#39;],
                        &#34;associated&#34;: False,
                        &#39;databaseName&#39;: mb_item[&#39;databaseName&#39;],
                        &#34;exchangeVersion&#34;: mb_item[&#39;exchangeVersion&#39;],
                        # &#34;msExchRecipientTypeDetails&#34;: mb_item[&#39;msExchRecipientTypeDetails&#39;],
                        &#39;user&#39;: {
                            &#39;_type_&#39;: 13,
                            &#39;userGUID&#39;: mb_item[&#39;user&#39;][&#39;userGUID&#39;]
                        }
                    }
                    users.append(mailbox_dict)
                    break

    except KeyError as err:
        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))

    if len(users) != len(subclient_content[&#39;mailboxNames&#39;]):
        to_search_user = [s_user for s_user in subclient_content[&#39;mailboxNames&#39;]
                          if not any(s_user == f_user[&#39;smtpAdrress&#39;] for f_user in users)]
        for mailbox_item in to_search_user:
            search_users = self._search_user(mailbox_item)
            if len(search_users) != 0:
                users.append(search_users[0])

    discover_info = {
        &#34;discoverByType&#34;: 1,
        &#34;mailBoxes&#34;: users
    }
    if use_policies:
        _association_json_ = self._association_json(subclient_content)
    else:
        _association_json_ = self._association_json_with_plan(subclient_content)
    _association_json_[&#34;emailAssociation&#34;][&#34;emailDiscoverinfo&#34;] = discover_info
    self._set_association_request(_association_json_)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient" href="../exchsubclient.html#cvpysdk.subclients.exchsubclient.ExchangeSubclient">ExchangeSubclient</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.ad_group_backup" href="../exchsubclient.html#cvpysdk.subclients.exchsubclient.ExchangeSubclient.ad_group_backup">ad_group_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.allow_multiple_readers" href="../../subclient.html#cvpysdk.subclient.Subclient.allow_multiple_readers">allow_multiple_readers</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.backup" href="../../subclient.html#cvpysdk.subclient.Subclient.backup">backup</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.browse" href="../../subclient.html#cvpysdk.subclient.Subclient.browse">browse</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.cleanup" href="../exchsubclient.html#cvpysdk.subclients.exchsubclient.ExchangeSubclient.cleanup">cleanup</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.data_readers" href="../../subclient.html#cvpysdk.subclient.Subclient.data_readers">data_readers</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.deduplication_options" href="../../subclient.html#cvpysdk.subclient.Subclient.deduplication_options">deduplication_options</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.description" href="../../subclient.html#cvpysdk.subclient.Subclient.description">description</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.disable_backup" href="../../subclient.html#cvpysdk.subclient.Subclient.disable_backup">disable_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.disable_intelli_snap" href="../../subclient.html#cvpysdk.subclient.Subclient.disable_intelli_snap">disable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.disk_restore" href="../exchsubclient.html#cvpysdk.subclients.exchsubclient.ExchangeSubclient.disk_restore">disk_restore</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.display_name" href="../../subclient.html#cvpysdk.subclient.Subclient.display_name">display_name</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.enable_backup" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_backup">enable_backup</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.enable_backup_at_time" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_backup_at_time">enable_backup_at_time</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.enable_intelli_snap" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_intelli_snap">enable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.enable_trueup" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_trueup">enable_trueup</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.enable_trueup_days" href="../../subclient.html#cvpysdk.subclient.Subclient.enable_trueup_days">enable_trueup_days</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.encryption_flag" href="../../subclient.html#cvpysdk.subclient.Subclient.encryption_flag">encryption_flag</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.exclude_from_sla" href="../../subclient.html#cvpysdk.subclient.Subclient.exclude_from_sla">exclude_from_sla</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.find" href="../../subclient.html#cvpysdk.subclient.Subclient.find">find</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.find_latest_job" href="../../subclient.html#cvpysdk.subclient.Subclient.find_latest_job">find_latest_job</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.get_ma_associated_storagepolicy" href="../../subclient.html#cvpysdk.subclient.Subclient.get_ma_associated_storagepolicy">get_ma_associated_storagepolicy</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.get_pst_backup_opt_json" href="../exchsubclient.html#cvpysdk.subclients.exchsubclient.ExchangeSubclient.get_pst_backup_opt_json">get_pst_backup_opt_json</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.get_pst_data_opt_json" href="../exchsubclient.html#cvpysdk.subclients.exchsubclient.ExchangeSubclient.get_pst_data_opt_json">get_pst_data_opt_json</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.get_pst_task_json" href="../exchsubclient.html#cvpysdk.subclients.exchsubclient.ExchangeSubclient.get_pst_task_json">get_pst_task_json</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.is_backup_enabled" href="../../subclient.html#cvpysdk.subclient.Subclient.is_backup_enabled">is_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.is_blocklevel_backup_enabled" href="../../subclient.html#cvpysdk.subclient.Subclient.is_blocklevel_backup_enabled">is_blocklevel_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.is_default_subclient" href="../../subclient.html#cvpysdk.subclient.Subclient.is_default_subclient">is_default_subclient</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.is_intelli_snap_enabled" href="../../subclient.html#cvpysdk.subclient.Subclient.is_intelli_snap_enabled">is_intelli_snap_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.is_on_demand_subclient" href="../../subclient.html#cvpysdk.subclient.Subclient.is_on_demand_subclient">is_on_demand_subclient</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.is_trueup_enabled" href="../../subclient.html#cvpysdk.subclient.Subclient.is_trueup_enabled">is_trueup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.last_backup_time" href="../../subclient.html#cvpysdk.subclient.Subclient.last_backup_time">last_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.list_media" href="../../subclient.html#cvpysdk.subclient.Subclient.list_media">list_media</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.name" href="../../subclient.html#cvpysdk.subclient.Subclient.name">name</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.network_agent" href="../../subclient.html#cvpysdk.subclient.Subclient.network_agent">network_agent</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.next_backup_time" href="../../subclient.html#cvpysdk.subclient.Subclient.next_backup_time">next_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.out_of_place_restore" href="../exchsubclient.html#cvpysdk.subclients.exchsubclient.ExchangeSubclient.out_of_place_restore">out_of_place_restore</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.plan" href="../../subclient.html#cvpysdk.subclient.Subclient.plan">plan</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.preview_backedup_file" href="../exchsubclient.html#cvpysdk.subclients.exchsubclient.ExchangeSubclient.preview_backedup_file">preview_backedup_file</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.properties" href="../../subclient.html#cvpysdk.subclient.Subclient.properties">properties</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.pst_ingestion" href="../exchsubclient.html#cvpysdk.subclients.exchsubclient.ExchangeSubclient.pst_ingestion">pst_ingestion</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.pst_restore" href="../exchsubclient.html#cvpysdk.subclients.exchsubclient.ExchangeSubclient.pst_restore">pst_restore</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.read_buffer_size" href="../../subclient.html#cvpysdk.subclient.Subclient.read_buffer_size">read_buffer_size</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.restore_in_place" href="../exchsubclient.html#cvpysdk.subclients.exchsubclient.ExchangeSubclient.restore_in_place">restore_in_place</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.restore_out_of_place" href="../../subclient.html#cvpysdk.subclient.Subclient.restore_out_of_place">restore_out_of_place</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.run_content_indexing" href="../../subclient.html#cvpysdk.subclient.Subclient.run_content_indexing">run_content_indexing</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.set_backup_nodes" href="../../subclient.html#cvpysdk.subclient.Subclient.set_backup_nodes">set_backup_nodes</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.set_proxy_for_snap" href="../../subclient.html#cvpysdk.subclient.Subclient.set_proxy_for_snap">set_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.snapshot_engine_name" href="../../subclient.html#cvpysdk.subclient.Subclient.snapshot_engine_name">snapshot_engine_name</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.software_compression" href="../../subclient.html#cvpysdk.subclient.Subclient.software_compression">software_compression</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.storage_ma" href="../../subclient.html#cvpysdk.subclient.Subclient.storage_ma">storage_ma</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.storage_ma_id" href="../../subclient.html#cvpysdk.subclient.Subclient.storage_ma_id">storage_ma_id</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.storage_policy" href="../../subclient.html#cvpysdk.subclient.Subclient.storage_policy">storage_policy</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.subclient_content_indexing" href="../exchsubclient.html#cvpysdk.subclients.exchsubclient.ExchangeSubclient.subclient_content_indexing">subclient_content_indexing</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.subclient_guid" href="../../subclient.html#cvpysdk.subclient.Subclient.subclient_guid">subclient_guid</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.subclient_id" href="../../subclient.html#cvpysdk.subclient.Subclient.subclient_id">subclient_id</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.subclient_name" href="../../subclient.html#cvpysdk.subclient.Subclient.subclient_name">subclient_name</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.unset_proxy_for_snap" href="../../subclient.html#cvpysdk.subclient.Subclient.unset_proxy_for_snap">unset_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclients.exchsubclient.ExchangeSubclient.update_properties" href="../../subclient.html#cvpysdk.subclient.Subclient.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#usermailboxsubclient">UsermailboxSubclient:</a></li>
<li><a href="#content-association-methods">Content Association Methods:</a></li>
<li><a href="#browse-restore-backup-methods">Browse/ Restore/ Backup Methods:</a></li>
<li><a href="#user-mailbox-subclient-instance-attributes">User Mailbox Subclient Instance Attributes:</a></li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.subclients.exchange" href="index.html">cvpysdk.subclients.exchange</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient" href="#cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient">UsermailboxSubclient</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.adgroups" href="#cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.adgroups">adgroups</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.backup_generic_items" href="#cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.backup_generic_items">backup_generic_items</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.backup_mailboxes" href="#cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.backup_mailboxes">backup_mailboxes</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.browse_mailboxes" href="#cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.browse_mailboxes">browse_mailboxes</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.create_recovery_point" href="#cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.create_recovery_point">create_recovery_point</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.databases" href="#cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.databases">databases</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.delete_adgroup_assocaition" href="#cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.delete_adgroup_assocaition">delete_adgroup_assocaition</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.delete_auto_discover_association" href="#cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.delete_auto_discover_association">delete_auto_discover_association</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.delete_database_assocaition" href="#cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.delete_database_assocaition">delete_database_assocaition</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.delete_o365group_association" href="#cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.delete_o365group_association">delete_o365group_association</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.delete_user_assocaition" href="#cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.delete_user_assocaition">delete_user_assocaition</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.disable_allusers_associations" href="#cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.disable_allusers_associations">disable_allusers_associations</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.discover_adgroups" href="#cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.discover_adgroups">discover_adgroups</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.discover_databases" href="#cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.discover_databases">discover_databases</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.discover_users" href="#cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.discover_users">discover_users</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.enable_allusers_associations" href="#cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.enable_allusers_associations">enable_allusers_associations</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.enable_auto_discover_association" href="#cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.enable_auto_discover_association">enable_auto_discover_association</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.enable_ews_support" href="#cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.enable_ews_support">enable_ews_support</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.find_mailbox" href="#cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.find_mailbox">find_mailbox</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.o365groups" href="#cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.o365groups">o365groups</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.refresh" href="#cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.restore_in_place_syntex" href="#cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.restore_in_place_syntex">restore_in_place_syntex</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.set_adgroup_associations" href="#cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.set_adgroup_associations">set_adgroup_associations</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.set_database_assocaition" href="#cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.set_database_assocaition">set_database_assocaition</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.set_fs_association_for_pst" href="#cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.set_fs_association_for_pst">set_fs_association_for_pst</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.set_o365group_asscoiations" href="#cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.set_o365group_asscoiations">set_o365group_asscoiations</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.set_pst_association" href="#cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.set_pst_association">set_pst_association</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.set_user_assocaition" href="#cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.set_user_assocaition">set_user_assocaition</a></code></li>
<li><code><a title="cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.users" href="#cvpysdk.subclients.exchange.usermailbox_subclient.UsermailboxSubclient.users">users</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>