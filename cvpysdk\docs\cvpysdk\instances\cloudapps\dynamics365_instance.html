<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.instances.cloudapps.dynamics365_instance API documentation</title>
<meta name="description" content="File for performing operations on a MS Dynamics 365 Instance …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.instances.cloudapps.dynamics365_instance</code></h1>
</header>
<section id="section-intro">
<p>File for performing operations on a MS Dynamics 365 Instance.</p>
<p>MSDynamics365Instance is the only class defined in this file.</p>
<h2 id="msdynamics365instance">Msdynamics365Instance</h2>
<p>Class derived from CloudAppsInstance Base class and representing a
Dynamics 365 CRM instance,</p>
<h2 id="msdynamics365instance_1">Msdynamics365Instance</h2>
<p><strong><em>*</em></strong><strong><em>*</em></strong><strong><em>
Methods
</em></strong><strong><em>*</em></strong><strong><em>*</em></strong></p>
<p>_get_instance_properties()
&ndash;
Instance class method overwritten to fetch cloud apps
instance properties</p>
<p>_get_instance_properties_json()
&ndash;
Returns the instance properties json</p>
<p>discover_content()
&ndash;
Discover content for the Dynamics 365 Instance</p>
<p><strong><em>*</em></strong><strong><em>*</em></strong><strong><em>
Properties
</em></strong><strong><em>*</em></strong><strong><em>*</em></strong></p>
<p>access_node
&ndash;
Name of the access node that the instance is associated with</p>
<p>idx_app_type
&ndash;
Returns the App type of the MS Dynamics 365 instance</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/dynamics365_instance.py#L1-L159" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;
    File for performing operations on a MS Dynamics 365 Instance.

MSDynamics365Instance is the only class defined in this file.

MSDynamics365Instance:
    Class derived from CloudAppsInstance Base class and representing a
        Dynamics 365 CRM instance,

MSDynamics365Instance:

    *****************                       Methods                      *****************

    _get_instance_properties()          --      Instance class method overwritten to fetch cloud apps
                                                    instance properties

    _get_instance_properties_json()     --      Returns the instance properties json

    discover_content()                  --      Discover content for the Dynamics 365 Instance

    *****************                       Properties                      *****************

    access_node                         --      Name of the access node that the instance is associated with

    idx_app_type                        --      Returns the App type of the MS Dynamics 365 instance
&#34;&#34;&#34;

from __future__ import unicode_literals
from ...exception import SDKException
from ..cainstance import CloudAppsInstance


class MSDynamics365Instance(CloudAppsInstance):
    &#34;&#34;&#34;Class for representing an Instance of the MSDynamics365 instance type.&#34;&#34;&#34;

    def _get_instance_properties(self):
        &#34;&#34;&#34;Gets the properties of this instance.

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        super(MSDynamics365Instance, self)._get_instance_properties()
        # Common properties for Cloud Apps
        self._ca_instance_type = None
        self._manage_content_automatically = None
        self._auto_discovery_enabled = None
        self._auto_discovery_mode = None

        # Dynamics 365 CRM instance related properties
        self._client_id = None
        self._tenant = None
        self._access_node = None
        self._index_server = None

        if &#39;cloudAppsInstance&#39; in self._properties:
            cloud_apps_instance = self._properties[&#39;cloudAppsInstance&#39;]
            self._ca_instance_type = cloud_apps_instance[&#39;instanceType&#39;]

            if &#39;v2CloudAppsInstance&#39; in cloud_apps_instance:
                d365_instance = cloud_apps_instance[&#39;v2CloudAppsInstance&#39;]

                self._manage_content_automatically = d365_instance[&#39;manageContentAutomatically&#39;]
                self._auto_discovery_enabled = d365_instance[&#39;isAutoDiscoveryEnabled&#39;]

                if &#39;clientId&#39; in d365_instance:
                    self._client_id = d365_instance.get(&#39;clientId&#39;)
                    self._tenant = d365_instance.get(&#39;tenant&#39;)
                else:
                    self._client_id = d365_instance.get(
                        &#39;azureAppList&#39;, {}).get(&#39;azureApps&#39;, [{}])[0].get(&#39;azureAppId&#39;)
                    self._tenant = d365_instance.get(
                        &#39;azureAppList&#39;, {}).get(&#39;azureApps&#39;, [{}])[0].get(&#39;azureDirectoryId&#39;)

                if self._client_id is None:
                    raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;Azure App has not been configured&#39;)

            if &#39;generalCloudProperties&#39; in cloud_apps_instance:
                general_cloud_properties = cloud_apps_instance[&#39;generalCloudProperties&#39;]
                self._access_node = general_cloud_properties.get(&#34;memberServers&#34;, {})[0].get(&#34;client&#34;, {}).get(
                    &#34;clientName&#34;, None)
                self._index_server = general_cloud_properties.get(&#34;indexServer&#34;, {}).get(&#34;clientName&#34;, None)

    def _get_instance_properties_json(self):
        &#34;&#34;&#34;Returns the instance properties json.&#34;&#34;&#34;

        return {&#39;instanceProperties&#39;: self._properties}

    @property
    def access_node(self):
        &#34;&#34;&#34;Returns the name of the access node for this MS Dynamics 365 instance&#34;&#34;&#34;
        return self._access_node

    @property
    def idx_app_type(self) -&gt; int:
        &#34;&#34;&#34;Returns the App type of the MS Dynamics 365 instance&#34;&#34;&#34;
        return 200127

    def discover_content(self, environment_discovery: bool = False):
        &#34;&#34;&#34;
            Run Discovery for a MS Dynamics 365 Instance
            Arguments:
                environment_discovery            (bool)--     Whether to run discovery for Dynamics 365 environments
                    If True
                        Discovery will run for Dynamics 365 environments
                    If False
                        Table level discovered content would be run
            Returns:
                discovered_content              (dict)--        Dictionary of the discovered content

        &#34;&#34;&#34;
        discovery_type: int
        if environment_discovery is False:
            discovery_type = 8
        else:
            discovery_type = 5

        url = self._services[&#39;GET_CLOUDAPPS_USERS&#39;] % (
            self.instance_id, self._agent_object._client_object.client_id, discovery_type)

        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;GET&#39;, url)

        if flag:
            if response and response.json():
                discover_content = response.json()

                if discover_content.get(&#39;error&#39;, {}).get(&#39;errorCode&#39;, 0) == -*********:
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, discover_content)

                if &#39;userAccounts&#39; in response.json():
                    _discover_content = discover_content[&#39;userAccounts&#39;]
                    return _discover_content

                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                response_string = self._commcell_object._update_response_(response.text)
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.instances.cloudapps.dynamics365_instance.MSDynamics365Instance"><code class="flex name class">
<span>class <span class="ident">MSDynamics365Instance</span></span>
<span>(</span><span>agent_object, instance_name, instance_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing an Instance of the MSDynamics365 instance type.</p>
<p>Initialise the instance object.</p>
<h2 id="args">Args</h2>
<p>agent_object
(object)
&ndash;
instance of the Agent class</p>
<p>instance_name
(str)
&ndash;
name of the instance</p>
<p>instance_id
(str)
&ndash;
id of the instance
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Instance class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/dynamics365_instance.py#L51-L159" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class MSDynamics365Instance(CloudAppsInstance):
    &#34;&#34;&#34;Class for representing an Instance of the MSDynamics365 instance type.&#34;&#34;&#34;

    def _get_instance_properties(self):
        &#34;&#34;&#34;Gets the properties of this instance.

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        super(MSDynamics365Instance, self)._get_instance_properties()
        # Common properties for Cloud Apps
        self._ca_instance_type = None
        self._manage_content_automatically = None
        self._auto_discovery_enabled = None
        self._auto_discovery_mode = None

        # Dynamics 365 CRM instance related properties
        self._client_id = None
        self._tenant = None
        self._access_node = None
        self._index_server = None

        if &#39;cloudAppsInstance&#39; in self._properties:
            cloud_apps_instance = self._properties[&#39;cloudAppsInstance&#39;]
            self._ca_instance_type = cloud_apps_instance[&#39;instanceType&#39;]

            if &#39;v2CloudAppsInstance&#39; in cloud_apps_instance:
                d365_instance = cloud_apps_instance[&#39;v2CloudAppsInstance&#39;]

                self._manage_content_automatically = d365_instance[&#39;manageContentAutomatically&#39;]
                self._auto_discovery_enabled = d365_instance[&#39;isAutoDiscoveryEnabled&#39;]

                if &#39;clientId&#39; in d365_instance:
                    self._client_id = d365_instance.get(&#39;clientId&#39;)
                    self._tenant = d365_instance.get(&#39;tenant&#39;)
                else:
                    self._client_id = d365_instance.get(
                        &#39;azureAppList&#39;, {}).get(&#39;azureApps&#39;, [{}])[0].get(&#39;azureAppId&#39;)
                    self._tenant = d365_instance.get(
                        &#39;azureAppList&#39;, {}).get(&#39;azureApps&#39;, [{}])[0].get(&#39;azureDirectoryId&#39;)

                if self._client_id is None:
                    raise SDKException(&#39;Instance&#39;, &#39;102&#39;, &#39;Azure App has not been configured&#39;)

            if &#39;generalCloudProperties&#39; in cloud_apps_instance:
                general_cloud_properties = cloud_apps_instance[&#39;generalCloudProperties&#39;]
                self._access_node = general_cloud_properties.get(&#34;memberServers&#34;, {})[0].get(&#34;client&#34;, {}).get(
                    &#34;clientName&#34;, None)
                self._index_server = general_cloud_properties.get(&#34;indexServer&#34;, {}).get(&#34;clientName&#34;, None)

    def _get_instance_properties_json(self):
        &#34;&#34;&#34;Returns the instance properties json.&#34;&#34;&#34;

        return {&#39;instanceProperties&#39;: self._properties}

    @property
    def access_node(self):
        &#34;&#34;&#34;Returns the name of the access node for this MS Dynamics 365 instance&#34;&#34;&#34;
        return self._access_node

    @property
    def idx_app_type(self) -&gt; int:
        &#34;&#34;&#34;Returns the App type of the MS Dynamics 365 instance&#34;&#34;&#34;
        return 200127

    def discover_content(self, environment_discovery: bool = False):
        &#34;&#34;&#34;
            Run Discovery for a MS Dynamics 365 Instance
            Arguments:
                environment_discovery            (bool)--     Whether to run discovery for Dynamics 365 environments
                    If True
                        Discovery will run for Dynamics 365 environments
                    If False
                        Table level discovered content would be run
            Returns:
                discovered_content              (dict)--        Dictionary of the discovered content

        &#34;&#34;&#34;
        discovery_type: int
        if environment_discovery is False:
            discovery_type = 8
        else:
            discovery_type = 5

        url = self._services[&#39;GET_CLOUDAPPS_USERS&#39;] % (
            self.instance_id, self._agent_object._client_object.client_id, discovery_type)

        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;GET&#39;, url)

        if flag:
            if response and response.json():
                discover_content = response.json()

                if discover_content.get(&#39;error&#39;, {}).get(&#39;errorCode&#39;, 0) == -*********:
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, discover_content)

                if &#39;userAccounts&#39; in response.json():
                    _discover_content = discover_content[&#39;userAccounts&#39;]
                    return _discover_content

                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                response_string = self._commcell_object._update_response_(response.text)
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.instances.cainstance.CloudAppsInstance" href="../cainstance.html#cvpysdk.instances.cainstance.CloudAppsInstance">CloudAppsInstance</a></li>
<li><a title="cvpysdk.instance.Instance" href="../../instance.html#cvpysdk.instance.Instance">Instance</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.instances.cloudapps.dynamics365_instance.MSDynamics365Instance.access_node"><code class="name">var <span class="ident">access_node</span></code></dt>
<dd>
<div class="desc"><p>Returns the name of the access node for this MS Dynamics 365 instance</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/dynamics365_instance.py#L110-L113" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def access_node(self):
    &#34;&#34;&#34;Returns the name of the access node for this MS Dynamics 365 instance&#34;&#34;&#34;
    return self._access_node</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.cloudapps.dynamics365_instance.MSDynamics365Instance.idx_app_type"><code class="name">var <span class="ident">idx_app_type</span> : int</code></dt>
<dd>
<div class="desc"><p>Returns the App type of the MS Dynamics 365 instance</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/dynamics365_instance.py#L115-L118" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def idx_app_type(self) -&gt; int:
    &#34;&#34;&#34;Returns the App type of the MS Dynamics 365 instance&#34;&#34;&#34;
    return 200127</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.instances.cloudapps.dynamics365_instance.MSDynamics365Instance.discover_content"><code class="name flex">
<span>def <span class="ident">discover_content</span></span>(<span>self, environment_discovery: bool = False)</span>
</code></dt>
<dd>
<div class="desc"><p>Run Discovery for a MS Dynamics 365 Instance</p>
<h2 id="arguments">Arguments</h2>
<p>environment_discovery
(bool)&ndash;
Whether to run discovery for Dynamics 365 environments
If True
Discovery will run for Dynamics 365 environments
If False
Table level discovered content would be run</p>
<h2 id="returns">Returns</h2>
<p>discovered_content
(dict)&ndash;
Dictionary of the discovered content</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/dynamics365_instance.py#L120-L159" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def discover_content(self, environment_discovery: bool = False):
    &#34;&#34;&#34;
        Run Discovery for a MS Dynamics 365 Instance
        Arguments:
            environment_discovery            (bool)--     Whether to run discovery for Dynamics 365 environments
                If True
                    Discovery will run for Dynamics 365 environments
                If False
                    Table level discovered content would be run
        Returns:
            discovered_content              (dict)--        Dictionary of the discovered content

    &#34;&#34;&#34;
    discovery_type: int
    if environment_discovery is False:
        discovery_type = 8
    else:
        discovery_type = 5

    url = self._services[&#39;GET_CLOUDAPPS_USERS&#39;] % (
        self.instance_id, self._agent_object._client_object.client_id, discovery_type)

    flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;GET&#39;, url)

    if flag:
        if response and response.json():
            discover_content = response.json()

            if discover_content.get(&#39;error&#39;, {}).get(&#39;errorCode&#39;, 0) == -*********:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, discover_content)

            if &#39;userAccounts&#39; in response.json():
                _discover_content = discover_content[&#39;userAccounts&#39;]
                return _discover_content

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.instances.cainstance.CloudAppsInstance" href="../cainstance.html#cvpysdk.instances.cainstance.CloudAppsInstance">CloudAppsInstance</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.backupsets" href="../../instance.html#cvpysdk.instance.Instance.backupsets">backupsets</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.browse" href="../../instance.html#cvpysdk.instance.Instance.browse">browse</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.credentials" href="../../instance.html#cvpysdk.instance.Instance.credentials">credentials</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.find" href="../../instance.html#cvpysdk.instance.Instance.find">find</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.instance_id" href="../../instance.html#cvpysdk.instance.Instance.instance_id">instance_id</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.instance_name" href="../../instance.html#cvpysdk.instance.Instance.instance_name">instance_name</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.name" href="../../instance.html#cvpysdk.instance.Instance.name">name</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.properties" href="../../instance.html#cvpysdk.instance.Instance.properties">properties</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.refresh" href="../../instance.html#cvpysdk.instance.Instance.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.subclients" href="../../instance.html#cvpysdk.instance.Instance.subclients">subclients</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.update_properties" href="../../instance.html#cvpysdk.instance.Instance.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.instances.cloudapps" href="index.html">cvpysdk.instances.cloudapps</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.instances.cloudapps.dynamics365_instance.MSDynamics365Instance" href="#cvpysdk.instances.cloudapps.dynamics365_instance.MSDynamics365Instance">MSDynamics365Instance</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.instances.cloudapps.dynamics365_instance.MSDynamics365Instance.access_node" href="#cvpysdk.instances.cloudapps.dynamics365_instance.MSDynamics365Instance.access_node">access_node</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.dynamics365_instance.MSDynamics365Instance.discover_content" href="#cvpysdk.instances.cloudapps.dynamics365_instance.MSDynamics365Instance.discover_content">discover_content</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.dynamics365_instance.MSDynamics365Instance.idx_app_type" href="#cvpysdk.instances.cloudapps.dynamics365_instance.MSDynamics365Instance.idx_app_type">idx_app_type</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>