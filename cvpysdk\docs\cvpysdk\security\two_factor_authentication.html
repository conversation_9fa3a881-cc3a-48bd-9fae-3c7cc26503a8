<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.security.two_factor_authentication API documentation</title>
<meta name="description" content="Helper file to manage two factor authentication settings on this commcell …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.security.two_factor_authentication</code></h1>
</header>
<section id="section-intro">
<p>Helper file to manage two factor authentication settings on this commcell</p>
<p>TwoFactorAuthentication is the only class defined in this file</p>
<h2 id="twofactorauthentication">Twofactorauthentication</h2>
<p><strong>init</strong>()
&ndash;
Initializes TwoFactorAuthentication class object.</p>
<p>refresh()
&ndash;
fetches the current tfa settings.</p>
<p>_get_tfa_info() &ndash;
Excutes get api on the server to fetch tfa info.</p>
<p>_process_response()
&ndash;
Process the response json</p>
<p>disable_tfa()
&ndash;
Disables tfa at commcell or organizaton level</p>
<p>enable_tfa()
&ndash;
Enables tfa at commcell or organization level</p>
<h1 id="twofactorauthentication-instance-attributes">TwoFactorAuthentication Instance Attributes</h1>
<pre><code>**is_tfa_enabled**      --      returns tfa status True or False

**tfa_enabled_user_groups** --  returns user groups on which tfa is enabled.
                                only if user group level tfa is enabled
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/two_factor_authentication.py#L1-L262" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Helper file to manage two factor authentication settings on this commcell

TwoFactorAuthentication is the only class defined in this file

TwoFactorAuthentication:

    __init__()      --      Initializes TwoFactorAuthentication class object.

    refresh()       --      fetches the current tfa settings.

    _get_tfa_info() --      Excutes get api on the server to fetch tfa info.

    _process_response()   --  Process the response json

    disable_tfa()   --      Disables tfa at commcell or organizaton level

    enable_tfa()    --      Enables tfa at commcell or organization level

TwoFactorAuthentication Instance Attributes
===========================================

    **is_tfa_enabled**      --      returns tfa status True or False

    **tfa_enabled_user_groups** --  returns user groups on which tfa is enabled.
                                    only if user group level tfa is enabled
&#34;&#34;&#34;

from ..exception import SDKException


class TwoFactorAuthentication:
    &#34;&#34;&#34;Class for managing the security associations roles on the commcell&#34;&#34;&#34;

    def __init__(self, commcell_object, organization_id=None):
        &#34;&#34;&#34;
        Initializes TwoFactorAuthentication class object

        Args:
            commcell_object     --      commcell class object.

            organization_id     --      id of the organization on which two factor authentication
                                        operations to be performed.
                default:None

        Raises:
            SDKException:
                if invalid args are sent.
        &#34;&#34;&#34;
        self._commcell = commcell_object
        self._tfa_status = None
        self._tfa_enabled_user_groups = None
        self._org_id = None
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_
        if organization_id:
            if isinstance(organization_id, (int, str)):
                self._org_id = organization_id
            else:
                raise SDKException(&#39;Security&#39;, &#39;101&#39;)
        self.refresh()

    def refresh(self):
        &#34;&#34;&#34;
        Refresh the properties of two factor authentication

        Returns:
            None
        &#34;&#34;&#34;
        self._get_tfa_info()

    def _get_tfa_info(self):
        &#34;&#34;&#34;
        Executes api on the server and fetches commcell/organization two factor authentication info.

        Returns:
            None

        Raises:
            SDKException:
                if failed to fetch details
                if response is emmpty
                if response is not success
        &#34;&#34;&#34;
        url = self._services[&#39;TFA&#39;]

        if self._org_id:
            url = self._services[&#39;ORG_TFA&#39;] % self._org_id

        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, url
        )

        if flag:
            if response.json() and &#39;twoFactorAuthenticationInfo&#39; in response.json():
                info = response.json().get(&#39;twoFactorAuthenticationInfo&#39;)

                if &#39;error&#39; in response.json() and &#39;errorCode&#39; in response.json().get(&#39;error&#39;):
                    if response.json().get(&#39;error&#39;).get(&#39;errorCode&#39;) != 0:
                        error_msg = response.json().get(&#39;error&#39;).get(&#39;errorCode&#39;).get(&#39;errorString&#39;)
                        raise SDKException(&#39;Security&#39;,
                                           &#39;102&#39;,
                                           &#39;Failed to get the tfa info. \nError {0}&#39;.format(error_msg))

                if &#39;mode&#39; in info:
                    if info.get(&#39;mode&#39;) == 0:
                        self._tfa_status, self._tfa_enabled_user_groups = False, []
                    if info.get(&#39;mode&#39;) in (1, 2):
                        self._tfa_status, self._tfa_enabled_user_groups = True, info.get(&#39;userGroups&#39;, [])
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _process_response(self, flag, response):
        &#34;&#34;&#34;
        Processes the flag and response json

        Args:

            flag    (int)   --  status of api execution

            response    (byte)  --  data received from server

        Returns:
            None

        Raises:
            SDKException:
                if failed to get required info
        &#34;&#34;&#34;
        if flag:
            if response.json():
                response_json = {}
                if &#39;response&#39; in response.json():
                    response_json = response.json()[&#39;response&#39;][0]
                if &#39;error&#39; in response.json():
                    response_json = response.json().get(&#39;error&#39;)
                if response_json.get(&#39;errorCode&#39;) != 0:
                    error_msg = response_json.get(&#39;errorString&#39;)
                    raise SDKException(&#39;Security&#39;,
                                       &#39;102&#39;,
                                       &#39;Failed to get the two factor authentication info.&#39;
                                       &#39; \nError {0}&#39;.format(error_msg))
                self.refresh()
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def disable_tfa(self):
        &#34;&#34;&#34;
         Disables two factor authentication at commcell/organization level

         Returns:
             None

        Raises:
            SDKException:
                if failed to disable tfa.
        &#34;&#34;&#34;
        url = self._services[&#39;TFA_DISABLE&#39;]
        if self._org_id:
            url = self._services[&#39;ORG_TFA_DISABLE&#39;] % self._org_id
        flag, response = self._cvpysdk_object.make_request(
            &#39;PUT&#39;, url
        )
        self._process_response(flag=flag, response=response)

    def enable_tfa(self, user_groups=None, usernameless=False, passwordless=False):
        &#34;&#34;&#34;
        Enables two factor authentication at commcell/organization level.

        Args:
            user_groups     (list)  --  user group names on which two factor authentication needs to be enabled
            usernameless    (bool)  --  allow usernameless login if True
            passwordless    (bool)  --  allow passwordless login if True

        Returns:
            None

        Raises:
            SDKException:
                if failed to enable tfa.
        &#34;&#34;&#34;
        url = self._services[&#39;TFA_ENABLE&#39;]

        if self._org_id:
            url = self._services[&#39;ORG_TFA_ENABLE&#39;] % self._org_id

        user_groups_list = []
        if user_groups:
            if isinstance(user_groups, list):
                for group in user_groups:
                    group_obj = self._commcell.user_groups.get(user_group_name=group)
                    user_groups_list.append({&#34;userGroupName&#34;: group_obj.name})
            else:
                raise SDKException(&#39;Security&#39;, &#39;101&#39;)

        payload = {
            &#34;twoFactorAuthenticationInfo&#34;: {
                &#34;mode&#34;: 2 if user_groups_list else 1,
                &#34;userGroups&#34;: user_groups_list,
                &#39;webAuthn&#39;: {
                    &#39;allowPasswordlessLogin&#39;: passwordless,
                    &#39;allowUsernamelessLogin&#39;: usernameless
                }
            }
        }

        if not self._org_id:
            payload = {
                &#34;commCellInfo&#34;: {
                    &#34;generalInfo&#34;: payload
                }
            }

        flag, response = self._cvpysdk_object.make_request(
            &#39;PUT&#39;, url, payload
        )
        self._process_response(flag=flag, response=response)

    @property
    def is_tfa_enabled(self):
        &#34;&#34;&#34;Returns status of two factor authentication(True/False)&#34;&#34;&#34;
        return self._tfa_status

    @property
    def tfa_enabled_user_groups(self):
        &#34;&#34;&#34;
        Returns list of user group names for which two factor authentication is enabled
            eg:-
            [
                {
                &#34;userGroupId&#34;: 1,
                &#34;userGroupName&#34;: &#34;dummy&#34;
                }
            ]
        &#34;&#34;&#34;
        return self._tfa_enabled_user_groups</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.security.two_factor_authentication.TwoFactorAuthentication"><code class="flex name class">
<span>class <span class="ident">TwoFactorAuthentication</span></span>
<span>(</span><span>commcell_object, organization_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for managing the security associations roles on the commcell</p>
<p>Initializes TwoFactorAuthentication class object</p>
<h2 id="args">Args</h2>
<p>commcell_object
&ndash;
commcell class object.</p>
<p>organization_id
&ndash;
id of the organization on which two factor authentication
operations to be performed.
default:None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if invalid args are sent.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/two_factor_authentication.py#L49-L262" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class TwoFactorAuthentication:
    &#34;&#34;&#34;Class for managing the security associations roles on the commcell&#34;&#34;&#34;

    def __init__(self, commcell_object, organization_id=None):
        &#34;&#34;&#34;
        Initializes TwoFactorAuthentication class object

        Args:
            commcell_object     --      commcell class object.

            organization_id     --      id of the organization on which two factor authentication
                                        operations to be performed.
                default:None

        Raises:
            SDKException:
                if invalid args are sent.
        &#34;&#34;&#34;
        self._commcell = commcell_object
        self._tfa_status = None
        self._tfa_enabled_user_groups = None
        self._org_id = None
        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_
        if organization_id:
            if isinstance(organization_id, (int, str)):
                self._org_id = organization_id
            else:
                raise SDKException(&#39;Security&#39;, &#39;101&#39;)
        self.refresh()

    def refresh(self):
        &#34;&#34;&#34;
        Refresh the properties of two factor authentication

        Returns:
            None
        &#34;&#34;&#34;
        self._get_tfa_info()

    def _get_tfa_info(self):
        &#34;&#34;&#34;
        Executes api on the server and fetches commcell/organization two factor authentication info.

        Returns:
            None

        Raises:
            SDKException:
                if failed to fetch details
                if response is emmpty
                if response is not success
        &#34;&#34;&#34;
        url = self._services[&#39;TFA&#39;]

        if self._org_id:
            url = self._services[&#39;ORG_TFA&#39;] % self._org_id

        flag, response = self._cvpysdk_object.make_request(
            &#39;GET&#39;, url
        )

        if flag:
            if response.json() and &#39;twoFactorAuthenticationInfo&#39; in response.json():
                info = response.json().get(&#39;twoFactorAuthenticationInfo&#39;)

                if &#39;error&#39; in response.json() and &#39;errorCode&#39; in response.json().get(&#39;error&#39;):
                    if response.json().get(&#39;error&#39;).get(&#39;errorCode&#39;) != 0:
                        error_msg = response.json().get(&#39;error&#39;).get(&#39;errorCode&#39;).get(&#39;errorString&#39;)
                        raise SDKException(&#39;Security&#39;,
                                           &#39;102&#39;,
                                           &#39;Failed to get the tfa info. \nError {0}&#39;.format(error_msg))

                if &#39;mode&#39; in info:
                    if info.get(&#39;mode&#39;) == 0:
                        self._tfa_status, self._tfa_enabled_user_groups = False, []
                    if info.get(&#39;mode&#39;) in (1, 2):
                        self._tfa_status, self._tfa_enabled_user_groups = True, info.get(&#39;userGroups&#39;, [])
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _process_response(self, flag, response):
        &#34;&#34;&#34;
        Processes the flag and response json

        Args:

            flag    (int)   --  status of api execution

            response    (byte)  --  data received from server

        Returns:
            None

        Raises:
            SDKException:
                if failed to get required info
        &#34;&#34;&#34;
        if flag:
            if response.json():
                response_json = {}
                if &#39;response&#39; in response.json():
                    response_json = response.json()[&#39;response&#39;][0]
                if &#39;error&#39; in response.json():
                    response_json = response.json().get(&#39;error&#39;)
                if response_json.get(&#39;errorCode&#39;) != 0:
                    error_msg = response_json.get(&#39;errorString&#39;)
                    raise SDKException(&#39;Security&#39;,
                                       &#39;102&#39;,
                                       &#39;Failed to get the two factor authentication info.&#39;
                                       &#39; \nError {0}&#39;.format(error_msg))
                self.refresh()
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def disable_tfa(self):
        &#34;&#34;&#34;
         Disables two factor authentication at commcell/organization level

         Returns:
             None

        Raises:
            SDKException:
                if failed to disable tfa.
        &#34;&#34;&#34;
        url = self._services[&#39;TFA_DISABLE&#39;]
        if self._org_id:
            url = self._services[&#39;ORG_TFA_DISABLE&#39;] % self._org_id
        flag, response = self._cvpysdk_object.make_request(
            &#39;PUT&#39;, url
        )
        self._process_response(flag=flag, response=response)

    def enable_tfa(self, user_groups=None, usernameless=False, passwordless=False):
        &#34;&#34;&#34;
        Enables two factor authentication at commcell/organization level.

        Args:
            user_groups     (list)  --  user group names on which two factor authentication needs to be enabled
            usernameless    (bool)  --  allow usernameless login if True
            passwordless    (bool)  --  allow passwordless login if True

        Returns:
            None

        Raises:
            SDKException:
                if failed to enable tfa.
        &#34;&#34;&#34;
        url = self._services[&#39;TFA_ENABLE&#39;]

        if self._org_id:
            url = self._services[&#39;ORG_TFA_ENABLE&#39;] % self._org_id

        user_groups_list = []
        if user_groups:
            if isinstance(user_groups, list):
                for group in user_groups:
                    group_obj = self._commcell.user_groups.get(user_group_name=group)
                    user_groups_list.append({&#34;userGroupName&#34;: group_obj.name})
            else:
                raise SDKException(&#39;Security&#39;, &#39;101&#39;)

        payload = {
            &#34;twoFactorAuthenticationInfo&#34;: {
                &#34;mode&#34;: 2 if user_groups_list else 1,
                &#34;userGroups&#34;: user_groups_list,
                &#39;webAuthn&#39;: {
                    &#39;allowPasswordlessLogin&#39;: passwordless,
                    &#39;allowUsernamelessLogin&#39;: usernameless
                }
            }
        }

        if not self._org_id:
            payload = {
                &#34;commCellInfo&#34;: {
                    &#34;generalInfo&#34;: payload
                }
            }

        flag, response = self._cvpysdk_object.make_request(
            &#39;PUT&#39;, url, payload
        )
        self._process_response(flag=flag, response=response)

    @property
    def is_tfa_enabled(self):
        &#34;&#34;&#34;Returns status of two factor authentication(True/False)&#34;&#34;&#34;
        return self._tfa_status

    @property
    def tfa_enabled_user_groups(self):
        &#34;&#34;&#34;
        Returns list of user group names for which two factor authentication is enabled
            eg:-
            [
                {
                &#34;userGroupId&#34;: 1,
                &#34;userGroupName&#34;: &#34;dummy&#34;
                }
            ]
        &#34;&#34;&#34;
        return self._tfa_enabled_user_groups</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.security.two_factor_authentication.TwoFactorAuthentication.is_tfa_enabled"><code class="name">var <span class="ident">is_tfa_enabled</span></code></dt>
<dd>
<div class="desc"><p>Returns status of two factor authentication(True/False)</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/two_factor_authentication.py#L245-L248" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_tfa_enabled(self):
    &#34;&#34;&#34;Returns status of two factor authentication(True/False)&#34;&#34;&#34;
    return self._tfa_status</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.two_factor_authentication.TwoFactorAuthentication.tfa_enabled_user_groups"><code class="name">var <span class="ident">tfa_enabled_user_groups</span></code></dt>
<dd>
<div class="desc"><p>Returns list of user group names for which two factor authentication is enabled
eg:-
[
{
"userGroupId": 1,
"userGroupName": "dummy"
}
]</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/two_factor_authentication.py#L250-L262" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def tfa_enabled_user_groups(self):
    &#34;&#34;&#34;
    Returns list of user group names for which two factor authentication is enabled
        eg:-
        [
            {
            &#34;userGroupId&#34;: 1,
            &#34;userGroupName&#34;: &#34;dummy&#34;
            }
        ]
    &#34;&#34;&#34;
    return self._tfa_enabled_user_groups</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.security.two_factor_authentication.TwoFactorAuthentication.disable_tfa"><code class="name flex">
<span>def <span class="ident">disable_tfa</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Disables two factor authentication at commcell/organization level</p>
<p>Returns:
None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to disable tfa.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/two_factor_authentication.py#L173-L190" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def disable_tfa(self):
    &#34;&#34;&#34;
     Disables two factor authentication at commcell/organization level

     Returns:
         None

    Raises:
        SDKException:
            if failed to disable tfa.
    &#34;&#34;&#34;
    url = self._services[&#39;TFA_DISABLE&#39;]
    if self._org_id:
        url = self._services[&#39;ORG_TFA_DISABLE&#39;] % self._org_id
    flag, response = self._cvpysdk_object.make_request(
        &#39;PUT&#39;, url
    )
    self._process_response(flag=flag, response=response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.two_factor_authentication.TwoFactorAuthentication.enable_tfa"><code class="name flex">
<span>def <span class="ident">enable_tfa</span></span>(<span>self, user_groups=None, usernameless=False, passwordless=False)</span>
</code></dt>
<dd>
<div class="desc"><p>Enables two factor authentication at commcell/organization level.</p>
<h2 id="args">Args</h2>
<p>user_groups
(list)
&ndash;
user group names on which two factor authentication needs to be enabled
usernameless
(bool)
&ndash;
allow usernameless login if True
passwordless
(bool)
&ndash;
allow passwordless login if True</p>
<h2 id="returns">Returns</h2>
<p>None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to enable tfa.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/two_factor_authentication.py#L192-L243" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable_tfa(self, user_groups=None, usernameless=False, passwordless=False):
    &#34;&#34;&#34;
    Enables two factor authentication at commcell/organization level.

    Args:
        user_groups     (list)  --  user group names on which two factor authentication needs to be enabled
        usernameless    (bool)  --  allow usernameless login if True
        passwordless    (bool)  --  allow passwordless login if True

    Returns:
        None

    Raises:
        SDKException:
            if failed to enable tfa.
    &#34;&#34;&#34;
    url = self._services[&#39;TFA_ENABLE&#39;]

    if self._org_id:
        url = self._services[&#39;ORG_TFA_ENABLE&#39;] % self._org_id

    user_groups_list = []
    if user_groups:
        if isinstance(user_groups, list):
            for group in user_groups:
                group_obj = self._commcell.user_groups.get(user_group_name=group)
                user_groups_list.append({&#34;userGroupName&#34;: group_obj.name})
        else:
            raise SDKException(&#39;Security&#39;, &#39;101&#39;)

    payload = {
        &#34;twoFactorAuthenticationInfo&#34;: {
            &#34;mode&#34;: 2 if user_groups_list else 1,
            &#34;userGroups&#34;: user_groups_list,
            &#39;webAuthn&#39;: {
                &#39;allowPasswordlessLogin&#39;: passwordless,
                &#39;allowUsernamelessLogin&#39;: usernameless
            }
        }
    }

    if not self._org_id:
        payload = {
            &#34;commCellInfo&#34;: {
                &#34;generalInfo&#34;: payload
            }
        }

    flag, response = self._cvpysdk_object.make_request(
        &#39;PUT&#39;, url, payload
    )
    self._process_response(flag=flag, response=response)</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.two_factor_authentication.TwoFactorAuthentication.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the properties of two factor authentication</p>
<h2 id="returns">Returns</h2>
<p>None</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/two_factor_authentication.py#L81-L88" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;
    Refresh the properties of two factor authentication

    Returns:
        None
    &#34;&#34;&#34;
    self._get_tfa_info()</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#twofactorauthentication-instance-attributes">TwoFactorAuthentication Instance Attributes</a></li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.security" href="index.html">cvpysdk.security</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.security.two_factor_authentication.TwoFactorAuthentication" href="#cvpysdk.security.two_factor_authentication.TwoFactorAuthentication">TwoFactorAuthentication</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.security.two_factor_authentication.TwoFactorAuthentication.disable_tfa" href="#cvpysdk.security.two_factor_authentication.TwoFactorAuthentication.disable_tfa">disable_tfa</a></code></li>
<li><code><a title="cvpysdk.security.two_factor_authentication.TwoFactorAuthentication.enable_tfa" href="#cvpysdk.security.two_factor_authentication.TwoFactorAuthentication.enable_tfa">enable_tfa</a></code></li>
<li><code><a title="cvpysdk.security.two_factor_authentication.TwoFactorAuthentication.is_tfa_enabled" href="#cvpysdk.security.two_factor_authentication.TwoFactorAuthentication.is_tfa_enabled">is_tfa_enabled</a></code></li>
<li><code><a title="cvpysdk.security.two_factor_authentication.TwoFactorAuthentication.refresh" href="#cvpysdk.security.two_factor_authentication.TwoFactorAuthentication.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.security.two_factor_authentication.TwoFactorAuthentication.tfa_enabled_user_groups" href="#cvpysdk.security.two_factor_authentication.TwoFactorAuthentication.tfa_enabled_user_groups">tfa_enabled_user_groups</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>