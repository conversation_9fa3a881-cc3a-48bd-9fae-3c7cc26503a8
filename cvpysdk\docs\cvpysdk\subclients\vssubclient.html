<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.subclients.vssubclient API documentation</title>
<meta name="description" content="File for operating on a Virtual Server Subclient …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.subclients.vssubclient</code></h1>
</header>
<section id="section-intro">
<p>File for operating on a Virtual Server Subclient.</p>
<p>VirualServerSubclient is the only class defined in this file.</p>
<p>VirtualServerSubclient: Derived class from the Subclient Base class, representing a
virtual server subclient, and to perform operations
on that subclient</p>
<h2 id="virtualserversubclient">Virtualserversubclient</h2>
<p>__get_subclient_properties()
&ndash;
gets the subclient
related
properties of VSA subclient.</p>
<p>_get_subclient_properties_json()
&ndash;
gets all the subclient
related
properties of VSA subclient.</p>
<p>_get_vm_ids_and_names_dict()
&ndash;
creates and returns 2 dictionaries,
along with the vm path</p>
<p>_parse_vm_path()
&ndash;
parses the path provided by user,
and replaces the VM Display Name with
the VM ID</p>
<p>_json_restore_virtualServerRstOption
&ndash; setter for Virtualserver
property in restore</p>
<p>_json_restore_diskLevelVMRestoreOption
&ndash; setter for diskLevel restore
property in restore</p>
<p>_json_restore_advancedRestoreOptions
&ndash; setter for advanced restore
property in restore</p>
<p>_json_restore_volumeRstOption
&ndash; setter for Volume restore
property in restore</p>
<p>_json_vcenter_instance
&ndash; setter for vcenter instance
json in restore</p>
<p>_json_nics_advancedRestoreOptions
&ndash; Setter for nics list for
advanced restore option json</p>
<p>_process_vsa_browse_response()
&ndash; processes the browse response
received from server,and
replaces the vm id with the vm
name</p>
<p>_process_restore_request()
&ndash; processes the Restore Request
and replaces the VM
display name with their ID
before passing to the API</p>
<p>_get_disk_Extension()
&ndash; Gets the Extension of disk
provided</p>
<p>_get_conversion_disk_Type()
&ndash; For source Disk gets the Disk
that can be converted to and
set its destination Vendor</p>
<p>_prepare_filelevel_restore_json()
&ndash; internal Method can be used by
subclasses for file level
restore Json</p>
<p>_prepare_disk_restore_json
&ndash; internal Method can be used by
subclasses for disk level
restore Json</p>
<p>_check_folder_in_browse
&ndash; Internal Method to check folder
is in browse from subclient</p>
<p>browse()
&ndash; gets the content of the backup
for this subclient at the vm
path specified</p>
<p>parse_nics_xml()
&ndash; gets the list of nics for a VM</p>
<p>get_nics_from_browse()
&ndash; Browses the vm to get the nics
info xml, gets the nics info
using the parse_nics_xml method
and prepares the dict for nics
json</p>
<p>disk_level_browse()
&ndash; browses the Disks of a Virtual
Machine</p>
<p>guest_files_browse()
&ndash; browses the Files and Folders
inside a Virtual Machine</p>
<p>vm_files_browse()
&ndash; browses the Files and Folders
of a Virtual Machine</p>
<p>vm_files_browse_in_time()
&ndash; browses the Files and Folders
of a Virtual Machine in the time
range specified</p>
<p>restore_out_of_place()
&ndash; restores the VM Guest Files
specified in the paths list to
the client, at the
specified destionation location</p>
<p>full_vm_restore_in_place()
&ndash; restores the VM specified by the
user to the same location</p>
<p>_full_vm_restore_update_json_for_v2
&ndash; modifies the restore json as per v2
subclient details and returns it</p>
<p>backup()
&ndash;
run a backup job for the subclient</p>
<p>_advanced_backup_options()
&ndash;
sets the advanced backup options</p>
<p>update_properties()
&ndash;
child method to add vsa specific properties to update properties</p>
<p>index_server
&ndash;
Property to get/set the indexserver client for the subclient</p>
<p>quiesce_file_system
&ndash; Property to get/set quiesce value for the vsa subclient</p>
<p>snapshot_storage_type
&ndash; Property to get snapshot storage type for the vsa subclient</p>
<p>To add a new Virtual Subclient,
create a class in a new module under virtualserver sub package</p>
<p>The new module which is created has to named in the following manner:
1. Name the module with the name of the Virtual Server without special characters
2.Spaces alone must be replaced with underscores('_')</p>
<p>For eg:</p>
<pre><code>The Virtual Server 'Red Hat Virtualization' is named as 'red_hat_virtualization.py'

The Virtual Server 'Hyper-V' is named as 'hyperv.py'
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/vssubclient.py#L1-L3130" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;File for operating on a Virtual Server Subclient.

VirualServerSubclient is the only class defined in this file.

VirtualServerSubclient: Derived class from the Subclient Base class, representing a
                            virtual server subclient, and to perform operations
                            on that subclient

VirtualServerSubclient:
    __get_subclient_properties()      --  gets the subclient  related
                                          properties of VSA subclient.

    _get_subclient_properties_json()  --  gets all the subclient  related
                                          properties of VSA subclient.

    _get_vm_ids_and_names_dict()      --  creates and returns 2 dictionaries,
                                          along with the vm path

    _parse_vm_path()                  --  parses the path provided by user,
                                          and replaces the VM Display Name with
                                          the VM ID

    _json_restore_virtualServerRstOption    -- setter for Virtualserver
                                               property in restore

    _json_restore_diskLevelVMRestoreOption  -- setter for diskLevel restore
                                               property in restore

    _json_restore_advancedRestoreOptions    -- setter for advanced restore
                                               property in restore

    _json_restore_volumeRstOption           -- setter for Volume restore
                                               property in restore

    _json_vcenter_instance                  -- setter for vcenter instance
                                               json in restore

    _json_nics_advancedRestoreOptions       -- Setter for nics list for
                                               advanced restore option json

    _process_vsa_browse_response()          -- processes the browse response
                                               received from server,and
                                               replaces the vm id with the vm
                                               name

    _process_restore_request()              -- processes the Restore Request
                                               and replaces the VM
                                               display name with their ID
                                               before passing to the API

    _get_disk_Extension()                   -- Gets the Extension of disk
                                               provided

    _get_conversion_disk_Type()             -- For source Disk gets the Disk
                                               that can be converted to and
                                               set its destination Vendor

    _prepare_filelevel_restore_json()       -- internal Method can be used by
                                               subclasses for file level
                                               restore Json

    _prepare_disk_restore_json              -- internal Method can be used by
                                               subclasses for disk level
                                               restore Json

    _check_folder_in_browse                 -- Internal Method to check folder
                                               is in browse from subclient

    browse()                                -- gets the content of the backup
                                               for this subclient at the vm
                                               path specified

    parse_nics_xml()                        -- gets the list of nics for a VM

    get_nics_from_browse()                  -- Browses the vm to get the nics
                                               info xml, gets the nics info
                                               using the parse_nics_xml method
                                               and prepares the dict for nics
                                               json

    disk_level_browse()                     -- browses the Disks of a Virtual
                                               Machine

    guest_files_browse()                    -- browses the Files and Folders
                                               inside a Virtual Machine


    vm_files_browse()                       -- browses the Files and Folders
                                               of a Virtual Machine

    vm_files_browse_in_time()               -- browses the Files and Folders
                                               of a Virtual Machine in the time
                                               range specified

    restore_out_of_place()                  -- restores the VM Guest Files
                                               specified in the paths list to
                                               the client, at the
                                               specified destionation location

    full_vm_restore_in_place()              -- restores the VM specified by the
                                               user to the same location

    _full_vm_restore_update_json_for_v2     -- modifies the restore json as per v2
                                                subclient details and returns it

    backup()                               --  run a backup job for the subclient

    _advanced_backup_options()              --  sets the advanced backup options

    update_properties()                       --  child method to add vsa specific properties to update properties

    index_server                            --  Property to get/set the indexserver client for the subclient

    quiesce_file_system                     -- Property to get/set quiesce value for the vsa subclient

    snapshot_storage_type                   -- Property to get snapshot storage type for the vsa subclient

To add a new Virtual Subclient,  create a class in a new module under virtualserver sub package


The new module which is created has to named in the following manner:
1. Name the module with the name of the Virtual Server without special characters
2.Spaces alone must be replaced with underscores(&#39;_&#39;)

For eg:

    The Virtual Server &#39;Red Hat Virtualization&#39; is named as &#39;red_hat_virtualization.py&#39;

    The Virtual Server &#39;Hyper-V&#39; is named as &#39;hyperv.py&#39;

&#34;&#34;&#34;

import os
import re
from enum import Enum
import copy
import xml.etree.ElementTree as ET
from importlib import import_module
from inspect import getmembers, isclass, isabstract

import xmltodict

from cvpysdk.plan import Plans
from ..exception import SDKException
from ..client import Client
from ..subclient import Subclient
from ..constants import VSAObjects, HypervisorType, VsInstanceType


class VirtualServerSubclient(Subclient):
    &#34;&#34;&#34;Derived class from Subclient Base class, representing a virtual server subclient,
        and to perform operations on that subclient.&#34;&#34;&#34;

    def __new__(cls, backupset_object, subclient_name, subclient_id=None):
        &#34;&#34;&#34;Decides which instance object needs to be created&#34;&#34;&#34;

        instance_name = VsInstanceType.VSINSTANCE_TYPE[backupset_object._instance_object._vsinstancetype]

        try:
            subclient_module = import_module(&#34;cvpysdk.subclients.virtualserver.{}&#34;.format(instance_name))
        except ImportError:
            subclient_module = import_module(&#34;cvpysdk.subclients.virtualserver.null&#34;)

        classes = getmembers(subclient_module, lambda m: isclass(m) and not isabstract(m))

        for name, _class in classes:
            if issubclass(_class, VirtualServerSubclient) and _class.__module__.rsplit(&#34;.&#34;, 1)[-1] == instance_name:
                return object.__new__(_class)

    def __init__(self, backupset_object, subclient_name, subclient_id=None):
        &#34;&#34;&#34;Initialize the Instance object for the given Virtual Server instance.

            Args:
                backupset_object    (object)    --  instance of the backupset class

                subclient_name      (str)       --  subclient name

                subclient_id        (int)       --  subclient id

        &#34;&#34;&#34;
        super(VirtualServerSubclient, self).__init__(
            backupset_object, subclient_name, subclient_id
        )

        self.content_types = {
            &#39;1&#39;: &#39;Host&#39;,
            &#39;2&#39;: &#39;Resource Pool&#39;,
            &#39;4&#39;: &#39;Datacenter&#39;,
            &#39;9&#39;: &#39;Virtual Machine&#39;,
            &#39;16&#39;: &#39;UnprotectedVMs&#39;,
            &#39;17&#39;: &#39;Root&#39;,
            &#39;34&#39;: &#39;Tag&#39;,
            &#39;35&#39;: &#39;TagCategory&#39;
        }

        self.filter_types = {
            &#39;1&#39;: &#39;Datastore&#39;,
            &#39;2&#39;: &#39;Virtual Disk Name/Pattern&#39;,
            &#39;3&#39;: &#39;Virtual Device Node&#39;,
            &#39;4&#39;: &#39;Container&#39;,
            &#39;5&#39;: &#39;Disk Label&#39;,
            &#39;6&#39;: &#39;Disk Type&#39;,
            &#39;9&#39;: &#39;Disk Tag Name/Value&#39;,
            &#39;10&#39;:&#39;Repository&#39;
        }

        self._disk_option = {
            &#39;original&#39;: 0,
            &#39;thicklazyzero&#39;: 1,
            &#39;thin&#39;: 2,
            &#39;thickeagerzero&#39;: 3
        }

        self._transport_mode = {
            &#39;auto&#39;: 0,
            &#39;san&#39;: 1,
            &#39;hotadd&#39;: 2,
            &#39;nbd&#39;: 5,
            &#39;nbdssl&#39;: 4
        }

        self._vm_names_browse = []
        self._vm_ids_browse = {}
        self._advanced_restore_option_list = []
        self._live_sync = None

    class disk_pattern(Enum):
        &#34;&#34;&#34;
        stores the disk pattern of all hypervisors
        &#34;&#34;&#34;
        name = &#34;name&#34;
        datastore = &#34;Datastore&#34;
        new_name = &#34;newName&#34;

    @property
    def content(self):
        &#34;&#34;&#34;Gets the appropriate content from the Subclient relevant to the user.

            Returns:
                list - list of content associated with the subclient

        &#34;&#34;&#34;
        content = []
        subclient_content = self._vmContent

        if &#39;children&#39; in subclient_content:
            children = subclient_content[&#39;children&#39;]
            content = self._get_content_list(children)
        return content

    @property
    def subclient_proxy(self):
        &#34;&#34;&#34;
            Gets the List of proxies at the Subclient

            Returns:
                    list         (list) :    Proxies at the subclient
        &#34;&#34;&#34;
        return self._get_subclient_proxies()

    @property
    def instance_proxy(self):
        &#34;&#34;&#34;
        Gets the proxy at instance level

        Returns:
                string          (string) :      Proxy at instane
        &#34;&#34;&#34;
        return self._proxyClient.get(&#39;clientName&#39;, None)

    @property
    def vm_filter(self):
        &#34;&#34;&#34;Gets the appropriate filter from the Subclient relevant to the user.

            Returns:
                list - list of filter associated with the subclient
        &#34;&#34;&#34;
        vm_filter = []
        if self._vmFilter:
            subclient_filter = self._vmFilter
            if &#39;children&#39; in subclient_filter:
                children = subclient_filter[&#39;children&#39;]
                vm_filter = self._get_content_list(children)
        return vm_filter

    @property
    def vm_diskfilter(self):
        &#34;&#34;&#34;Gets the appropriate Diskfilter from the Subclient relevant to the user.

            Returns:
                list - list of Diskfilter associated with the subclient

        &#34;&#34;&#34;
        vm_diskfilter = []
        if self._vmDiskFilter is not None:
            subclient_diskfilter = self._vmDiskFilter

            if &#39;filters&#39; in subclient_diskfilter:
                filters = subclient_diskfilter[&#39;filters&#39;]

                for child in filters:
                    filter_type_id = str(child[&#39;filterType&#39;])
                    filter_type = self.filter_types[str(child[&#39;filterType&#39;])]
                    vm_id = child[&#39;vmGuid&#39;] if &#39;vmGuid&#39; in child else None
                    filter_name = child[&#39;filter&#39;]
                    value = child[&#39;value&#39;]

                    temp_dict = {
                        &#39;filter&#39;: filter_name,
                        &#39;filterType&#39;: filter_type,
                        &#39;vmGuid&#39;: vm_id,
                        &#39;filterTypeId&#39;: filter_type_id,
                        &#39;value&#39;:value
                    }

                    vm_diskfilter.append(temp_dict)
        else:
            vm_diskfilter = self._vmDiskFilter

        if len(vm_diskfilter) == 0:
            vm_diskfilter = None
        return vm_diskfilter

    @property
    def metadata(self):
        &#34;&#34;&#34;
            Get if collect files/metadata value for given subclient.
            Returns status as True/False (string)
            Default: False for subclient which doesnt have the property
        &#34;&#34;&#34;
        collectdetails = r&#39;collectFileDetails&#39;
        if collectdetails in self._vsaSubclientProp:
            vsasubclient_collect_details = self._vsaSubclientProp[collectdetails]
        else:
            vsasubclient_collect_details = False
        return vsasubclient_collect_details

    @content.setter
    def content(self, subclient_content):
        &#34;&#34;&#34;Creates the list of content JSON to pass to the API to add/update
           content of a Virtual Server Subclient.

            Args:
                subclient_content (list)  --  list of the content to add to the
                subclient list should contain name and type
                (like VSAObjects.VMName, VSAObjects.DATASTORE )
                example:[
                            {
                            &#39;type&#39; : VSAObjects.VMNotes,
                            &#39;display_name&#39; : &#39;removed&#39;,
                            }
                        ]

                for Advance user:
                        where we need to have multiple constraints for a single
                        rule.
                        list should contain minimum 2 parameters (name, type,
                        true/False for equalsOrNotEquals) for a single
                        constraint
                        for power on/off, we need to specify one more
                        parameter i.e., true -on, false -off(as state variable)
                        example:
                        subclient_content = [{&#39;allOrAnyChildren&#39;: True, &#39;content&#39;: [
                            {&#39;equal_value&#39;: True, &#39;allOrAnyChildren&#39;: True, &#39;display_name&#39;: &#39;*abc*&#39;, &#39;type&#39;: &#39;VMName&#39;},
                            {&#39;equal_value&#39;: False, &#39;allOrAnyChildren&#39;: True, &#39;display_name&#39;: &#39;xyz&#39;, &#39;type&#39;: &#39;VMName&#39;}]},
                                      {&#39;allOrAnyChildren&#39;: False, &#39;content&#39;: [
                                          {&#39;equal_value&#39;: True, &#39;allOrAnyChildren&#39;: True, &#39;display_name&#39;: &#39;*12*&#39;,
                                           &#39;type&#39;: &#39;VMName&#39;},
                                          {&#39;equal_value&#39;: True, &#39;allOrAnyChildren&#39;: True, &#39;display_name&#39;: &#39;*34*&#39;,
                                           &#39;type&#39;: &#39;VMName&#39;}]}]
                        subclient_content = [
                                                [
                                                    {
                                                    &#39;type&#39; : VSAObjects.VMName,
                                                    &#39;display_name&#39; : &#39;VM*&#39;
                                                    }
                                                ],
                                                [
                                                    {
                                                      &#39;type&#39; : VSAObjects.VMNotes,
                                                      &#39;display_name&#39; : &#39;removed&#39;,
                                                    },
                                                    {
                                                      &#39;type&#39; : VSAObjects.VMPowerState,
                                                      &#39;state&#39;: &#39;false&#39;,
                                                    }
                                                ]
                                            ]

            Returns:
                list - list of the appropriate JSON for an agent to send to the
                       POST Subclient API
        &#34;&#34;&#34;
        content = []
        try:
            for entity in subclient_content:
                virtual_server_dict = dict()
                if not isinstance(entity, dict):
                    entity = {&#39;content&#39;: entity}
                elif &#39;content&#39; not in entity:
                    entity = {&#39;content&#39;: entity}
                virtual_server_dict[&#39;allOrAnyChildren&#39;] = entity.get(&#39;allOrAnyChildren&#39;, True)
                virtual_server_dict[&#39;equalsOrNotEquals&#39;] = entity.get(&#39;equalsOrNotEquals&#39;, True)
                virtual_server_dict[&#39;children&#39;] = []

                def add_childrens(item, multiple_rule=False):
                    &#34;&#34;&#34;
                    add contents in the hierarchy
                    Args:
                        item                (dict)  :   content&#39;s current item to be added
                        multiple_rule       (bool)  :   If multiple rule present or not

                    &#34;&#34;&#34;
                    temp = {
                        &#39;allOrAnyChildren&#39;: item.get(&#39;allOrAnyChildren&#39;, True),
                        &#39;equalsOrNotEquals&#39;: item.get(&#39;equal_value&#39;, True),
                        &#39;name&#39;: item.get(&#39;name&#39;, item.get(&#39;id&#39;, &#39;&#39;)),
                        &#39;displayName&#39;: item.get(&#39;display_name&#39;, &#39;&#39;),
                        &#39;path&#39;: &#39;&#39;,
                        &#39;type&#39;: item[&#39;type&#39;] if (
                                isinstance(item[&#39;type&#39;], int) or isinstance(item[&#39;type&#39;], str)) else
                        item[&#39;type&#39;].value   
                    }
                    if item[&#39;type&#39;] == VSAObjects.VMNotes:
                        temp[&#39;value&#39;] = item[&#39;display_name&#39;]
                        temp[&#39;displayName&#39;] = item[&#39;display_name&#39;]
                        temp[&#39;name&#39;] = &#34;Notes&#34;
                    if (item[&#39;type&#39;] ==
                            VSAObjects.VMPowerState and
                            item[&#39;state&#39;] == &#39;true&#39;):
                        temp[&#39;name&#39;] = &#34;PoweredState&#34;
                        temp[&#39;value&#39;] = &#34;1&#34;
                        temp[&#39;displayName&#39;] = &#34;Powered On&#34;
                    if (item[&#39;type&#39;] ==
                            VSAObjects.VMPowerState and
                            item[&#39;state&#39;] == &#39;false&#39;):
                        temp[&#39;name&#39;] = &#34;PoweredState&#34;
                        temp[&#39;value&#39;] = &#34;0&#34;
                        temp[&#39;displayName&#39;] = &#34;Powered Off&#34;
                    if item[&#39;type&#39;] == VSAObjects.VMCustomAttribute.name:
                        temp[&#39;value&#39;] = item.get(&#39;value&#39;, &#39;&#39;)
                    if multiple_rule:
                        virtual_server_dict.get(&#39;children&#39;).append(temp)
                    else:
                        content.append(temp)
                if not isinstance(entity, list):
                    entity = [entity]
                if len(entity[0][&#39;content&#39;]) == 1 or isinstance(entity[0][&#39;content&#39;], dict):
                    if isinstance(entity[0][&#39;content&#39;], list):
                        add_childrens(entity[0][&#39;content&#39;][0])
                    else:
                        add_childrens(entity[0][&#39;content&#39;])
                else:
                    for items in entity:
                        for item in items[&#39;content&#39;]:
                            add_childrens(item, True)
                        content.append(virtual_server_dict)
        except KeyError as err:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))

        vs_subclient_content = {
            &#34;children&#34;: content
        }

        self._set_subclient_properties(&#34;_vmContent&#34;, vs_subclient_content)

    @vm_filter.setter
    def vm_filter(self, subclient_filter):
        &#34;&#34;&#34;Creates the list of Filter JSON to pass to the API to update the
           VM_filter of a Virtual Server Subclient. i.e. it works in overwrite
           mode

            Args:
                subclient_filter (list)  --  list of the filter to add to the
                                             subclient

            Returns:
                list - list of the appropriate JSON for an agent to send to the
                       POST Subclient API
        &#34;&#34;&#34;
        vm_filter = []
        try:
            for entity in subclient_filter:
                virtual_server_dict = dict()
                if not isinstance(entity, dict):
                    entity = {&#39;content&#39;: entity}
                elif &#39;content&#39; not in entity:
                    entity = {&#39;content&#39;: entity}
                virtual_server_dict[&#39;allOrAnyChildren&#39;] = entity.get(&#39;allOrAnyChildren&#39;, True)
                virtual_server_dict[&#39;equalsOrNotEquals&#39;] = entity.get(&#39;equalsOrNotEquals&#39;, True)
                virtual_server_dict[&#39;children&#39;] = []

                def add_childrens(item, multiple_rule=False):
                    &#34;&#34;&#34;
                    add filters in the hierarchy
                    Args:
                        item                (dict)  :   content&#39;s filters item to be added
                        multiple_rule       (bool)  :   If multiple rule present or not

                    &#34;&#34;&#34;
                    temp = {
                        &#39;allOrAnyChildren&#39;: item.get(&#39;allOrAnyChildren&#39;, True),
                        &#39;equalsOrNotEquals&#39;: item.get(&#39;equal_value&#39;, True),
                        &#39;name&#39;: item.get(&#39;name&#39;, item.get(&#39;id&#39;, &#39;&#39;)),
                        &#39;displayName&#39;: item.get(&#39;display_name&#39;, &#39;&#39;),
                        &#39;path&#39;: &#39;&#39;,
                        &#39;type&#39;: item[&#39;type&#39;] if (
                                isinstance(item[&#39;type&#39;], int) or isinstance(item[&#39;type&#39;], str)) else
                        item[&#39;type&#39;].value   
                    }
                    if item[&#39;type&#39;] == VSAObjects.VMNotes:
                        temp[&#39;value&#39;] = item[&#39;display_name&#39;]
                        temp[&#39;displayName&#39;] = item[&#39;display_name&#39;]
                        temp[&#39;name&#39;] = &#34;Notes&#34;
                    if (item[&#39;type&#39;] ==
                            VSAObjects.VMPowerState and
                            item[&#39;state&#39;] == &#39;true&#39;):
                        temp[&#39;name&#39;] = &#34;PoweredState&#34;
                        temp[&#39;value&#39;] = &#34;1&#34;
                        temp[&#39;displayName&#39;] = &#34;Powered On&#34;
                    if (item[&#39;type&#39;] ==
                            VSAObjects.VMPowerState and
                            item[&#39;state&#39;] == &#39;false&#39;):
                        temp[&#39;name&#39;] = &#34;PoweredState&#34;
                        temp[&#39;value&#39;] = &#34;0&#34;
                        temp[&#39;displayName&#39;] = &#34;Powered Off&#34;
                    if item[&#39;type&#39;] == VSAObjects.VMCustomAttribute.name:
                        temp[&#39;value&#39;] = item.get(&#39;value&#39;, &#39;&#39;)
                    if multiple_rule:
                        virtual_server_dict.get(&#39;children&#39;).append(temp)
                    else:
                        vm_filter.append(temp)
                if not isinstance(entity, list):
                    entity = [entity]
                if len(entity[0][&#39;content&#39;]) == 1 or isinstance(entity[0][&#39;content&#39;], dict):
                    if isinstance(entity[0][&#39;content&#39;], list):
                        add_childrens(entity[0][&#39;content&#39;][0])
                    else:
                        add_childrens(entity[0][&#39;content&#39;])
                else:
                    for items in entity:
                        for item in items[&#39;content&#39;]:
                            add_childrens(item, True)
                        vm_filter.append(virtual_server_dict)            

        except KeyError as err:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;,
                               &#39;{} not given in content&#39;.format(err))

        vs_filter_content = {
            &#34;children&#34;: vm_filter
        }
        self._set_subclient_properties(&#34;_vmFilter&#34;, vs_filter_content)

    @vm_diskfilter.setter
    def vm_diskfilter(self, subclient_diskfilter):
        &#34;&#34;&#34;Creates the list of Disk Filter JSON to pass to the API to update
           the Disk_filter of a Virtual Server Subclient. i.e. it works in
           overwrite mode

            Args:
                subclient_diskfilter (list)  --  list of the Disk filter to add
                                                 to the subclient

            Returns:
                list - list of the appropriate JSON for an agent to send to
                       the POST Subclient API
        &#34;&#34;&#34;
        vm_diskfilter = []

        try:
            for temp_dict in subclient_diskfilter:
                if temp_dict.get(&#39;filterTypeId&#39;):
                    filter_type_id = temp_dict[&#39;filterTypeId&#39;]
                else:
                    filter_type_id = \
                        list(filter(lambda x: self.filter_types[x].lower() == temp_dict[&#39;filtertype&#39;].lower(),
                                    self.filter_types))[
                            0]

                virtual_server_dict = {
                    &#39;filter&#39;: temp_dict[&#39;filter&#39;],
                    &#39;filterType&#39;: filter_type_id,
                    &#39;vmGuid&#39;: temp_dict.get(&#39;vmGuid&#39;)
                }

                vm_diskfilter.append(virtual_server_dict)

        except KeyError as err:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;,
                               &#39;{} not given in content&#39;.format(err))

        vs_diskfilter_content = {
            &#34;filters&#34;: vm_diskfilter
        }
        self._set_subclient_properties(&#34;_vmDiskFilter&#34;, vs_diskfilter_content)

    @property
    def live_sync(self):
        &#34;&#34;&#34;Returns the instance of the VSALiveSync class&#34;&#34;&#34;
        if not self._live_sync:
            from .virtualserver.livesync.vsa_live_sync import VsaLiveSync
            self._live_sync = VsaLiveSync(self)

        return self._live_sync

    @property
    def index_server(self):
        &#34;&#34;&#34;Returns the index server client set for the subclient. None if no Index Server is set&#34;&#34;&#34;

        if &#39;indexSettings&#39; not in self._commonProperties:
            return None

        index_settings = self._commonProperties[&#39;indexSettings&#39;]
        index_server = None

        if (&#39;currentIndexServer&#39; in index_settings and
                &#39;clientName&#39; in index_settings[&#39;currentIndexServer&#39;]):
            index_server = index_settings[&#39;currentIndexServer&#39;][&#39;clientName&#39;]

        if index_server is None:
            return None

        return self._commcell_object.clients.get(index_server)

    @index_server.setter
    def index_server(self, value):
        &#34;&#34;&#34;Sets the index server client for the backupset

            Args:
                value   (object)    --  The index server client object to set

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;

        if not isinstance(value, Client):
            raise SDKException(&#39;Subclient&#39;, &#39;121&#39;)

        index_server_name = value.client_name

        self._set_subclient_properties(
            &#34;_commonProperties[&#39;indexSettings&#39;][&#39;currentIndexServer&#39;][&#39;clientName&#39;]&#34;,
            index_server_name)

    @property
    def quiesce_file_system(self):
        &#34;&#34;&#34;
            Gets the quiesce value set for the vsa subclient

        Returns:
            (Boolean)    True/False
        &#34;&#34;&#34;
        return self._vsaSubclientProp.get(&#39;quiesceGuestFileSystemAndApplications&#39;)

    @quiesce_file_system.setter
    def quiesce_file_system(self, value):
        &#34;&#34;&#34;
        Sets the quiesce value for the vsa subclient

        Args:
            value   (Boolean)   True/False

        &#34;&#34;&#34;
        update_properties = self.properties
        update_properties[&#39;vsaSubclientProp&#39;][&#39;quiesceGuestFileSystemAndApplications&#39;] = value
        self.update_properties(update_properties)

    @property
    def snapshot_storage_type(self):
        &#34;&#34;&#34;
            Gets the snapshot storage type set for the vsa subclient

        Returns:
            (Boolean)    True/False
        &#34;&#34;&#34;
        return self._vsaSubclientProp.get(&#39;snapshotStorageType&#39;)

    def _get_disk_provisioning_value(self, provisioningType):
        &#34;&#34;&#34;
         Returns the provisioning code for the selected type

        Args:
                provisioningType  (String) - Disk provisioning type

        return: (int) - diskProvisionValue

        &#34;&#34;&#34;
        # Defaults to &#34;original&#34;
        disk_provision_value = 0
        provisioningType = provisioningType.replace(&#34; &#34;, &#34;&#34;).lower()
        if provisioningType in self._disk_option:
            disk_provision_value = self._disk_option[provisioningType]
        return disk_provision_value

    @metadata.setter
    def metadata(self, value=True):
        &#34;&#34;&#34;
        Set given value of collectFileDetails/metadata (True/false) on the subclient

        Args:
                value   (str)    True/False

        &#34;&#34;&#34;
        collectdetails = r&#39;collectFileDetails&#39;
        if collectdetails in self._vsaSubclientProp:
            self._set_subclient_properties(&#34;_vsaSubclientProp[&#39;collectFileDetails&#39;]&#34;, value)

    @property
    def cbtvalue(self):
        &#34;&#34;&#34;
        Get CBT value for given subclient.

        Returns:
            (Boolean)    True/False

        &#34;&#34;&#34;
        return self._subclient_properties.get(&#39;vsaSubclientProp&#39;, {}).get(&#34;useChangedTrackingOnVM&#34;, False)


    @cbtvalue.setter
    def cbtvalue(self, value):
        &#34;&#34;&#34;
        Set CBT value for given subclient

        Args:
            value   (Boolean)   True/False

        &#34;&#34;&#34;
        update_properties = self.properties
        update_properties[&#34;vsaSubclientProp&#34;][&#39;useChangedTrackingOnVM&#39;] = value
        self.update_properties(update_properties)

    def update_properties(self, properties_dict):
        &#34;&#34;&#34;
        child method to add any specific attributes for vsa
        Args:
            properties_dict         (dict):     dict of all propterties of subclient
        &#34;&#34;&#34;
        properties_dict.update({
            &#34;vmFilterOperationType&#34;: &#34;OVERWRITE&#34;,
            &#34;vmContentOperationType&#34;: &#34;OVERWRITE&#34;,
            &#34;vmDiskFilterOperationType&#34;: &#34;OVERWRITE&#34;
        })
        super().update_properties(properties_dict)

    def _get_content_list(self, children):
        &#34;&#34;&#34;
        Gets the content in list format
        Args:
            children                            (list):     Content if the subclient

        Returns:
            content_list                        (list):     Content of the subclient
        &#34;&#34;&#34;

        content_list = []
        for child in children:
            path = child[&#39;path&#39;] if &#39;path&#39; in child else &#39;&#39;
            allOrAnyChildren = child[&#39;allOrAnyChildren&#39;] if &#39;allOrAnyChildren&#39; in child else None
            equalsOrNotEquals = child[&#39;equalsOrNotEquals&#39;] if &#39;equalsOrNotEquals&#39; in child else None
            _temp_list = []
            _temp_dict = {}
            if &#39;children&#39; in child:
                nested_children = child[&#39;children&#39;]
                for each_condition in nested_children:
                    display_name = each_condition[&#39;displayName&#39;]
                    content_type = VSAObjects(each_condition[&#39;type&#39;]).name if isinstance(each_condition[&#39;type&#39;],
                                                                                         int) else each_condition[
                        &#39;type&#39;]
                    vm_id = &#39;&#39; if each_condition.get(&#39;name&#39;, &#39;&#39;) in display_name else each_condition.get(&#39;name&#39;, &#39;&#39;)
                    temp_dict = {
                        &#39;equal_value&#39;: each_condition.get(&#39;equalsOrNotEquals&#39;, True),
                        &#39;allOrAnyChildren&#39;: each_condition.get(&#39;allOrAnyChildren&#39;, True),
                        &#39;display_name&#39;: display_name,
                        &#39;type&#39;: content_type
                    }
                    if content_type != &#39;VMCustomAttribute&#39;:
                        temp_dict.update({&#39;id&#39;: vm_id, &#39;path&#39;: path})
                    else:
                        temp_dict.update({&#39;name&#39;: vm_id, &#39;value&#39;: each_condition[&#39;value&#39;]})
                    _temp_list.append(temp_dict)
                _temp_dict[&#39;allOrAnyChildren&#39;] = allOrAnyChildren
                _temp_dict[&#39;equalsOrNotEquals&#39;] = equalsOrNotEquals
                _temp_dict[&#39;content&#39;] = _temp_list
                content_list.append(_temp_dict)
            else:
                display_name = child[&#39;displayName&#39;]
                content_type = VSAObjects(child[&#39;type&#39;]).name if isinstance(child[&#39;type&#39;], int) else child[&#39;type&#39;]
                vm_id = child.get(&#39;name&#39;, &#39;&#39;)
                temp_dict = {
                        &#39;equal_value&#39;: child[&#39;equalsOrNotEquals&#39;],
                        &#39;allOrAnyChildren&#39;: child.get(&#39;allOrAnyChildren&#39;, True),
                        &#39;display_name&#39;: display_name,
                        &#39;type&#39;: content_type
                    }
                if content_type != &#39;VMCustomAttribute&#39;:
                    temp_dict.update({&#39;id&#39;: vm_id, &#39;path&#39;: path})
                else:
                    temp_dict.update({&#39;name&#39;: vm_id, &#39;value&#39;: child[&#39;value&#39;]})
                content_list.append(temp_dict)
        return content_list

    def _get_subclient_properties(self):
        &#34;&#34;&#34;Gets the subclient  related properties of Virtual server subclient.

        &#34;&#34;&#34;

        self._vmDiskFilter = None
        self._vmFilter = None
        super(VirtualServerSubclient, self)._get_subclient_properties()

        if &#39;vmContent&#39; in self._subclient_properties:
            self._vmContent = self._subclient_properties[&#39;vmContent&#39;]
        if &#39;vmDiskFilter&#39; in self._subclient_properties:
            self._vmDiskFilter = self._subclient_properties[&#39;vmDiskFilter&#39;]
        if &#39;vmFilter&#39; in self._subclient_properties:
            self._vmFilter = self._subclient_properties[&#39;vmFilter&#39;]
        if &#39;vmBackupInfo&#39; in self._subclient_properties:
            self._vmBackupInfo = self._subclient_properties[&#39;vmBackupInfo&#39;]
        if &#39;vsaSubclientProp&#39; in self._subclient_properties:
            self._vsaSubclientProp = self._subclient_properties[&#39;vsaSubclientProp&#39;]

    def _get_subclient_content_(self):
        &#34;&#34;&#34;
        Returns the subclient content from property. Base class Abstract method
        implementation

        return:
            VM content  (dict)  -- Dictionary of VM Content with all details

        &#34;&#34;&#34;
        return self.content

    def _get_subclient_properties_json(self):
        &#34;&#34;&#34;get the all subclient related properties of this subclient.

           Returns:
                dict - all subclient properties put inside a dict

        &#34;&#34;&#34;
        subclient_json = {
            &#34;subClientProperties&#34;:
                {
                    &#34;vmContent&#34;: self._vmContent,
                    &#34;proxyClient&#34;: self._proxyClient,
                    &#34;subClientEntity&#34;: self._subClientEntity,
                    &#34;vmDiskFilter&#34;: self._vmDiskFilter,
                    &#34;vmFilter&#34;: self._vmFilter,
                    &#34;vmBackupInfo&#34;: self._vmBackupInfo,
                    &#34;vsaSubclientProp&#34;: self._vsaSubclientProp,
                    # &#34;content&#34;: self._content,
                    &#34;commonProperties&#34;: self._commonProperties,
                    &#34;vmContentOperationType&#34;: 1,
                    &#34;vmDiskFilterOperationType&#34;: 1,
                    &#34;vmFilterOperationType&#34;: 1
                }
        }
        return subclient_json

    def _disk_dict_pattern(self, name, datastore, new_name=None):
        &#34;&#34;&#34;
        set the disk dictionary of the hypervisor

        Args:
                name            (str)       --  name of the disk

                datastore       (str)       --  datastore where the disk has to be restored

                new_name        (str)       --  new name of the disk

            Returns:

                disk dictionary(dict)       -- Dictionary with key name, new name , datastore
                                                and corresponding
        &#34;&#34;&#34;

        if not new_name and not self._instance_object.instance_name == HypervisorType.GOOGLE_CLOUD.value.lower():
            new_name = name
        temp_disk_dict = {}
        temp_disk_dict[self.disk_pattern.name.value] = name
        temp_disk_dict[self.disk_pattern.datastore.value] = datastore
        temp_disk_dict[self.disk_pattern.new_name.value] = new_name
        return temp_disk_dict

    def _json_vcenter_instance(self, value):
        &#34;&#34;&#34; Setter for vcenter_instance JSON &#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        self._virtualserver_option_restore_json[&#34;vCenterInstance&#34;] = {
            &#34;clientName&#34;: value.get(&#34;destination_client_name&#34;, &#34;&#34;),
            &#34;instanceName&#34;: value.get(&#34;destination_instance&#34;, &#34;&#34;),
            &#34;appName&#34;: value.get(&#34;appName&#34;, &#34;Virtual Server&#34;)
        }

        if value.get(&#34;destination_instance_id&#34;) and value.get(&#34;destination_client_id&#34;):
            self._virtualserver_option_restore_json[&#34;vCenterInstance&#34;].update(
                {&#34;instanceId&#34;: value.get(&#34;destination_instance_id&#34;, 0),
                 &#34;clientId&#34;: value.get(&#34;destination_client_id&#34;, 0)}
            )

    def _json_restore_virtualServerRstOption(self, value):
        &#34;&#34;&#34;
            setter for  the Virtual server restore  option in restore json
        &#34;&#34;&#34;
        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        self._virtualserver_option_restore_json = {
            &#34;isDiskBrowse&#34;: value.get(&#34;disk_browse&#34;, True),
            &#34;isFileBrowse&#34;: value.get(&#34;file_browse&#34;, False),
            &#34;isVolumeBrowse&#34;: False,
            &#34;isVirtualLab&#34;: value.get(&#34;virtual_lab&#34;, False),
            &#34;esxServer&#34;: value.get(&#34;esx_server&#34;, &#34;&#34;),
            &#34;isAttachToNewVM&#34;: value.get(&#34;attach_to_new_vm&#34;, False),
            &#34;viewType&#34;: &#34;DEFAULT&#34;,
            &#34;isBlockLevelReplication&#34;: value.get(&#34;block_level&#34;, False)
        }

        if value.get(&#39;run_security_scan&#39;):
            self._virtualserver_option_restore_json[&#39;securityScanOptions&#39;] = {
                &#34;runSecurityScan&#34;: value.get(&#34;run_security_scan&#34;, False)
            }

        if value.get(&#39;replication_guid&#39;):
            self._virtualserver_option_restore_json[&#39;replicationGuid&#39;] = value[&#39;replication_guid&#39;]

    def _json_restore_virtualServerRstOption_filelevelrestoreoption(self, value):
        &#34;&#34;&#34;
            setter for  the File level restore option for agent less restore option in restore json
        &#34;&#34;&#34;
        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        return {
            &#34;serverName&#34;: value.get(&#34;server_name&#34;, &#39;&#39;),
            &#34;vmGuid&#34;: value.get(&#34;vm_guid&#34;, &#39;&#39;),
            &#34;vmName&#34;: value.get(&#34;vm_name&#34;, &#39;&#39;)
        }

    def _json_restore_guest_password(self, value):
        &#34;&#34;&#34;
            setter for vm credentials for agentless restore option in restore json
        &#34;&#34;&#34;
        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        return {
            &#34;userName&#34;: value.get(&#34;user_name&#34;, &#39;&#39;),
            &#34;password&#34;: value.get(&#34;password&#34;, &#39;&#39;)
        }

    def _json_nics_advancedRestoreOptions(self, vm_to_restore, value):
        &#34;&#34;&#34;
            Setter for nics list for advanced restore option json
        &#34;&#34;&#34;

        nics_dict_from_browse = self.get_nics_from_browse(copy_precedence=value.get(&#39;copy_precedence&#39;, 0))
        nics_list = []
        vm_nics_list = nics_dict_from_browse[vm_to_restore]
        for network_card_dict in vm_nics_list:
            if self._instance_object.instance_name == HypervisorType.GOOGLE_CLOUD.value.lower():
                current_project = network_card_dict.get(&#39;subnetId&#39;).split(&#39;/&#39;)[6]
                if value.get(&#39;project_id&#39;) is not None:
                    network_card_dict[&#39;subnetId&#39;] = value.get(&#39;subnetwork_nic&#39;)
                    network_card_dict[&#39;sourceNetwork&#39;] = value.get(&#39;networks_nic&#39;)
                    network_card_dict[&#39;publicIPaddress&#39;] = value.get(&#39;publicIPaddress&#39;)
                    network_card_dict[&#39;privateIPaddress&#39;] = value.get(&#39;privateIPaddress&#39;)

            _destnetwork = value.get(&#34;destination_network&#34;,
                                     value.get(&#39;network&#39;,
                                               network_card_dict[&#39;name&#39;]))

            nics = {
                &#34;subnetId&#34;: network_card_dict.get(&#39;subnetId&#39;, &#34;&#34;),
                &#34;sourceNetwork&#34;: network_card_dict[&#39;name&#39;],
                &#34;sourceNetworkId&#34;: network_card_dict.get(&#39;sourceNetwork&#39;, &#34;&#34;),
                &#34;name&#34;: (network_card_dict.get(&#39;sourceNetwork&#39;,
                                               &#34;&#34;) + _destnetwork) if self._instance_object.instance_name ==
                                                                      HypervisorType.GOOGLE_CLOUD.value.lower() and _destnetwork else
                network_card_dict[&#39;label&#39;],
                &#34;publicIPaddress&#34;: network_card_dict.get(&#34;publicIPaddress&#34;,&#34;&#34;),
                &#34;privateIPaddress&#34;: network_card_dict.get(&#34;privateIPaddress&#34;,&#34;&#34;),
                &#34;networkName&#34;: _destnetwork if _destnetwork else &#39;&#39;,
                &#34;destinationNetwork&#34;: _destnetwork if _destnetwork else network_card_dict[&#39;name&#39;]
            }

            # setting nics for azureRM instance
            if value.get(&#39;destination_instance&#39;).lower() == HypervisorType.AZURE_V2.value.lower():
                if value.get(&#39;subnet_id&#39;):
                    nics[&#34;subnetId&#34;] = value.get(&#39;subnet_id&#39;)
                    nics[&#34;networkName&#34;] = value.get(&#39;subnet_id&#39;).split(&#39;/&#39;)[0]
                    nics[&#34;networkDisplayName&#34;] = nics[&#34;networkName&#34;] + &#39;\\&#39; + value.get(&#39;subnet_id&#39;).split(&#39;/&#39;)[-1]
                elif &#34;networkDisplayName&#34; in value and &#39;networkrsg&#39; in value and &#39;destsubid&#39; in value:
                    nics[&#34;networkDisplayName&#34;] = value[&#34;networkDisplayName&#34;]
                    nics[&#34;networkName&#34;] = value[&#34;networkDisplayName&#34;].split(&#39;\\&#39;)[0]
                    modify_nics = value.get(&#39;subnetId&#39;, nics[&#39;subnetId&#39;]).split(&#39;/&#39;)
                    modify_nics[8] = nics[&#34;networkName&#34;]
                    modify_nics[4] = value[&#39;networkrsg&#39;]
                    modify_nics[2] = value[&#39;destsubid&#39;]
                    modify_nics[10] = value[&#34;networkDisplayName&#34;].split(&#39;\\&#39;)[1]
                    final_nics = &#34;&#34;
                    for each_info in modify_nics[1:]:
                        final_nics = final_nics + &#39;/&#39; + each_info
                    nics[&#34;subnetId&#34;] = final_nics
                    name = &#39;&#39;
                    for each_info in modify_nics[1:9]:
                        name = name + &#39;/&#39; + each_info
                    nics[&#34;name&#34;] = name

            nics_list.append(nics)

        return nics_list

    def _json_vmip_advanced_restore_options(self, value):
        &#34;&#34;&#34;
            Setting IP for destination vm
        &#34;&#34;&#34;
        vmip = []
        _asterisk = &#34;*.*.*.*&#34;
        vm_ip = {
            &#34;sourceIP&#34;: value.get(&#34;source_ip&#34;),
            &#34;sourceSubnet&#34;: value[&#34;source_subnet&#34;] if value.get(&#34;source_subnet&#34;) else _asterisk,
            &#34;sourceGateway&#34;: value[&#34;source_gateway&#34;] if value.get(&#34;source_gateway&#34;) else _asterisk,
            &#34;destinationIP&#34;: value.get(&#34;destination_ip&#34;),
            &#34;destinationSubnet&#34;: value[&#34;destination_subnet&#34;] if value.get(&#34;destination_subnet&#34;) else _asterisk,
            &#34;destinationGateway&#34;: value[&#34;destination_gateway&#34;] if value.get(&#34;destination_gateway&#34;) else _asterisk,
            &#34;primaryDNS&#34;: value.get(&#34;primary_dns&#34;, &#34;&#34;),
            &#34;alternateDNS&#34;: value.get(&#34;alternate_dns&#34;, &#34;&#34;),
            &#34;primaryWins&#34;: value.get(&#34;primare_wins&#34;, &#34;&#34;),
            &#34;altenameWins&#34;: value.get(&#34;alternate_wins&#34;, &#34;&#34;),
            &#34;useDhcp&#34;: False
        }
        vmip.append(vm_ip)
        return vmip

    def _json_restore_diskLevelVMRestoreOption(self, value):
        &#34;&#34;&#34;setter for  the disk Level VM Restore Option    in restore json&#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        vcenter_userpwd = &#39;&#39;
        if &#39;vmware&#39; in self._instance_object.instance_name:
            vcenter_userpwd = self._instance_object._user_name

        json_disklevel_option_restore = {
            &#34;esxServerName&#34;: value.get(&#34;esx_server&#34;, &#34;&#34;),
            &#34;vmFolderName&#34;: value.get(&#34;vm_folder&#34;, &#34;&#34;),
            &#34;dataCenterName&#34;: value.get(&#34;data_center&#34;, &#34;&#34;),
            &#34;hostOrCluster&#34;: value.get(&#34;host_cluster&#34;, &#34;&#34;),
            &#34;diskOption&#34;: value.get(&#34;disk_option&#34;, 0),
            &#34;vmName&#34;: &#34;&#34;,
            &#34;transportMode&#34;: value.get(&#34;transport_mode&#34;, 0),
            &#34;passUnconditionalOverride&#34;: value.get(&#34;unconditional_overwrite&#34;, False),
            &#34;powerOnVmAfterRestore&#34;: value.get(&#34;power_on&#34;, False),
            &#34;registerWithFailoverCluster&#34;: value.get(&#34;add_to_failover&#34;, False),
            &#34;userPassword&#34;: {&#34;userName&#34;: vcenter_userpwd or &#34;admin&#34;},
            &#34;redirectWritesToDatastore&#34;: value.get(&#34;redirectWritesToDatastore&#34;) or &#34;&#34;,
            &#34;delayMigrationMinutes&#34;: value.get(&#34;delayMigrationMinutes&#34;) or 0
        }
        if value[&#39;in_place&#39;]:
            json_disklevel_option_restore[&#34;dataStore&#34;] = {}
        if value.get(&#39;distribute_vm_workload&#39;):
            json_disklevel_option_restore[&#34;maxNumOfVMPerJob&#34;] = value[&#39;distribute_vm_workload&#39;]

        self._virtualserver_option_restore_json[&#34;diskLevelVMRestoreOption&#34;] = json_disklevel_option_restore

    def _json_restore_attach_diskLevelVMRestoreOption(self, value):
        &#34;&#34;&#34;setter for the attach disk Level VM Restore Option in restore json&#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        json_disklevel_option_restore = {
            &#34;esxServerName&#34;: value.get(&#34;esxHost&#34;, &#34;&#34;),
            &#34;diskOption&#34;: value.get(&#34;disk_option&#34;, 0),
            &#34;passUnconditionalOverride&#34;: value.get(&#34;unconditional_overwrite&#34;, False),
            &#34;powerOnVmAfterRestore&#34;: value.get(&#34;power_on&#34;, False),
            &#34;transportMode&#34;: value.get(&#34;transport_mode&#34;, 0),
            &#34;userPassword&#34;: {&#34;userName&#34;: value.get(&#34;userName&#34;,&#34;&#34;),&#34;password&#34;: value.get(&#34;password&#34;,&#34;&#34;)}
        }
        self._virtualserver_option_restore_json[&#34;diskLevelVMRestoreOption&#34;] = json_disklevel_option_restore

    def _json_restore_advancedRestoreOptions(self, value):
        &#34;&#34;&#34;setter for the Virtual server restore  option in restore json&#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        self._advanced_option_restore_json = {
            &#34;disks&#34;: value.get(&#34;disks&#34;, []),
            &#34;guid&#34;: value.get(&#34;guid&#34;, &#34;&#34;),
            &#34;newGuid&#34;: value.get(&#34;new_guid&#34;, &#34;&#34;),
            &#34;newName&#34;: value.get(&#34;new_name&#34;, &#34;&#34;),
            &#34;esxHost&#34;: value.get(&#34;esx_host&#34;, &#34;&#34;),
            &#34;projectId&#34;: value.get(&#34;project_id&#34;, &#34;&#34;),
            &#34;cluster&#34;: value.get(&#34;cluster&#34;, &#34;&#34;),
            &#34;name&#34;: value.get(&#34;name&#34;, &#34;&#34;),
            &#34;nics&#34;: value.get(&#34;nics&#34;, []),
            &#34;vmTags&#34;:  value.get(&#39;vmTags&#39;) or [],
            &#34;vmIPAddressOptions&#34;: value.get(&#34;vm_ip_address_options&#34;, []),
            &#34;FolderPath&#34;: value.get(&#34;FolderPath&#34;, &#34;&#34;),
            &#34;resourcePoolPath&#34;: value.get(&#34;ResourcePool&#34;, &#34;&#34;),
            &#34;restoreVMTags&#34;: False if value.get(&#39;vmTags&#39;) else True,
            &#34;volumeType&#34;: value.get(&#34;volumeType&#34;, &#34;Auto&#34;),
            &#34;vmCustomMetadata&#34;: value.get(&#34;vmCustomMetadata&#34;,[])
        }

        value_dict = {
            &#34;createPublicIP&#34;: [&#34;createPublicIP&#34;, [&#34;createPublicIP&#34;, &#34;&#34;]],
            &#34;restoreAsManagedVM&#34;: [&#34;restoreAsManagedVM&#34;, [&#34;restoreAsManagedVM&#34;, &#34;&#34;]],
            &#34;destination_os_name&#34;: [&#34;osName&#34;, [&#34;destination_os_name&#34;, &#34;AUTO&#34;]],
            &#34;resourcePoolPath&#34;: [&#34;resourcePoolPath&#34;, [&#34;resourcePoolPath&#34;, &#34;&#34;]],
            &#34;datacenter&#34;: [&#34;datacenter&#34;, [&#34;datacenter&#34;, &#34;&#34;]],
            &#34;terminationProtected&#34;: [&#34;terminationProtected&#34;, [&#34;terminationProtected&#34;, False]],
            &#34;securityGroups&#34;: [&#34;securityGroups&#34;, [&#34;securityGroups&#34;, &#34;&#34;]],
            &#34;keyPairList&#34;: [&#34;keyPairList&#34;, [&#34;keyPairList&#34;, &#34;&#34;]]
        }

        for key in value_dict:
            if key in value:
                inner_key = value_dict[key][0]
                val1, val2 = value_dict[key][1][0], value_dict[key][1][1]
                self._advanced_option_restore_json[inner_key] = value.get(val1, val2)

        if &#34;vmSize&#34; in value:
            val1, val2 = (&#34;instanceSize&#34;, &#34;&#34;) if not value[&#34;vmSize&#34;] else (&#34;vmSize&#34;, &#34;vmSize&#34;)
            self._advanced_option_restore_json[&#34;vmSize&#34;] = value.get(val1, val2)
        if &#34;ami&#34; in value and value[&#34;ami&#34;] is not None:
            self._advanced_option_restore_json[&#34;templateId&#34;] = value[&#34;ami&#34;][&#34;templateId&#34;]
            if value.get(&#39;ami&#39;, {}).get(&#39;templateName&#39;):
                self._advanced_option_restore_json[&#34;templateName&#34;] = value[&#34;ami&#34;][&#34;templateName&#34;]
        if &#34;iamRole&#34; in value and value[&#34;iamRole&#34;] is not None:
            self._advanced_option_restore_json[&#34;roleInfo&#34;] = {
                &#34;name&#34;: value[&#34;iamRole&#34;]
            }
        if value.get(&#34;serviceAccount&#34;, {}).get(&#34;email&#34;):
            self._advanced_option_restore_json[&#34;roleInfo&#34;] = {
                &#34;email&#34;: value.get(&#34;serviceAccount&#34;).get(&#34;email&#34;),
                &#34;name&#34;: value.get(&#34;serviceAccount&#34;).get(&#34;displayName&#34;),
                &#34;id&#34;: value.get(&#34;serviceAccount&#34;).get(&#34;uniqueId&#34;)
            }
        if self._instance_object.instance_name == &#39;openstack&#39;:
            if &#34;securityGroups&#34; in value and value[&#34;securityGroups&#34;] is not None:
                self._advanced_option_restore_json[&#34;securityGroups&#34;] = [{&#34;groupName&#34;: value[&#34;securityGroups&#34;]}]
        if &#34;destComputerName&#34; in value and value[&#34;destComputerName&#34;] is not None:
            self._advanced_option_restore_json[&#34;destComputerName&#34;] = value[&#34;destComputerName&#34;]
        if &#34;destComputerUserName&#34; in value and value[&#34;destComputerUserName&#34;] is not None:
            self._advanced_option_restore_json[&#34;destComputerUserName&#34;] = value[&#34;destComputerUserName&#34;]
        if &#34;instanceAdminPassword&#34; in value and value[&#34;instanceAdminPassword&#34;] is not None:
            self._advanced_option_restore_json[&#34;instanceAdminPassword&#34;] = value[&#34;instanceAdminPassword&#34;]

        if self.disk_pattern.datastore.value == &#34;DestinationPath&#34;:
            self._advanced_option_restore_json[&#34;DestinationPath&#34;] = value.get(&#34;datastore&#34;, &#34;&#34;)

        else:
            self._advanced_option_restore_json[&#34;Datastore&#34;] = value.get(&#34;datastore&#34;, &#34;&#34;)

        if value.get(&#39;block_level&#39;):
            self._advanced_option_restore_json[&#34;blrRecoveryOpts&#34;] = \
                self._json_restore_blrRecoveryOpts(value)

        temp_dict = copy.deepcopy(self._advanced_option_restore_json)
        return temp_dict

    def _json_restore_blrRecoveryOpts(self, value):
        &#34;&#34;&#34; setter for blr recovery options in block level json&#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        return {
            &#34;recoveryType&#34;: value.get(&#34;recovery_type&#34;, 1),
            &#34;granularV2&#34;: {
                &#34;ccrpInterval&#34;: value.get(&#34;ccrp_interval&#34;, 300),
                &#34;acrpInterval&#34;: value.get(&#34;acrp_interval&#34;, 0),
                &#34;maxRpInterval&#34;: value.get(&#34;max_RpInterval&#34;, 21600),
                &#34;rpMergeDelay&#34;: value.get(&#34;rp_merge_delay&#34;, 172800),
                &#34;rpRetention&#34;: value.get(&#34;rp_retention&#34;, 604800),
                &#34;maxRpStoreOfflineTime&#34;: value.get(&#34;max_RpStore_OfflineTime&#34;, 0),
                &#34;useOffPeakSchedule&#34;: value.get(&#34;use_OffPeak_Schedule&#34;, 0),
                &#34;rpStoreId&#34;: value.get(&#34;rpstore_id&#34;, &#34;&#34;),
                &#34;rpStoreName&#34;: value.get(&#34;rpstore_name&#34;, &#34;&#34;)
            }
        }

    def _json_restore_volumeRstOption(self, value):
        &#34;&#34;&#34;setter for  the Volume restore option for in restore json&#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        return{
            &#34;destinationVendor&#34;: value.get(&#34;destination_vendor&#34;, 0),
            &#34;volumeLeveRestore&#34;: False,
            &#34;volumeLevelRestoreType&#34;: value.get(&#34;volume_level_restore&#34;, 0),
            &#34;destinationDiskType&#34;: value.get(&#34;destination_disktype&#34;, 0)
        }

    def _get_vm_ids_and_names_dict(self):
        &#34;&#34;&#34;Parses through the subclient content and creates 2 dictionaries.

            Returns:
                dict    -   dictionary consisting of VM ID as Key and VM
                            Display Name as value

                dict    -   dictionary consisting of VM Display Name as Key and
                            VM ID as value
        &#34;&#34;&#34;
        vm_ids = {}
        vm_names = {}

        def _assign_vm_name_id(contents, _vm_ids, _vm_names):
            for _content in contents:
                if _content.get(&#39;content&#39;):
                    _vm_ids, _vm_names = _assign_vm_name_id(_content[&#39;content&#39;], _vm_ids, _vm_names)
                    continue
                if _content[&#39;type&#39;].lower() in (&#39;vm&#39;, &#39;virtual machine&#39;):
                    _vm_ids[_content[&#39;id&#39;]] = _content[&#39;display_name&#39;]
                    _vm_names[_content[&#39;display_name&#39;]] = _content[&#39;id&#39;]
                else:
                    _vm_ids = {}
                    _vm_names = {}
                    break
            return _vm_ids, _vm_names
        return _assign_vm_name_id(self.content, vm_ids, vm_names)

    def _get_vm_ids_and_names_dict_from_browse(self):
        &#34;&#34;&#34;Parses through the Browse content and get the VMs Backed up

            returns :
                vm_names    (list)  -- returns list of VMs backed up
                vm_ids      (dict)  -- returns id list of VMs backed up
        &#34;&#34;&#34;

        _vm_ids, _vm_names = self._get_vm_ids_and_names_dict()
        if not self._vm_names_browse:
            paths, paths_dict = self.browse()
            if not _vm_names:
                for key, val in paths_dict.items():
                    _vm_names[val[&#39;name&#39;]] = val[&#39;snap_display_name&#39;]
            for _each_path in paths_dict:
                _vm_id = _each_path.split(&#34;\\&#34;)[1]
                self._vm_names_browse.append(_vm_id)
                self._vm_ids_browse[_vm_id] = _vm_names[_vm_id]

        return self._vm_names_browse, self._vm_ids_browse

    def _parse_vm_path(self, vm_names, vm_path):
        &#34;&#34;&#34;Parses the path provided by user, and replaces the VM Display Name
           with the VM ID.

            Returns:
                str     -   string of path to run browse for
        &#34;&#34;&#34;
        if vm_path not in [&#39;\\&#39;, &#39;&#39;]:
            if not vm_path.startswith(&#39;\\&#39;):
                vm_path = &#39;\\&#39; + vm_path

            vm_path_list = vm_path.split(&#39;\\&#39;)

            for vm_name in vm_names:
                if vm_name in vm_path_list[1]:
                    vm_path = vm_path.replace(vm_path_list[1], vm_names[vm_name])
                    break

        return vm_path

    def _process_vsa_browse_response(self, vm_ids, browse_content):
        &#34;&#34;&#34;Processes the Browse response and replaces the VM ID with their
        display name before returning to user.

            Args:
                vm_ids          (dict)      --  dictionary with VM ID as Key
                                                and VM Name as value

                browse_content  (tuple)     --  browse response received from
                                                server

            Returns:
                list - list of all folders or files with their full
                       paths inside the input path

                dict - path along with the details like name, file/folder,
                       size, modification time
        &#34;&#34;&#34;
        for index, path in enumerate(browse_content[0]):
            if vm_ids:
                for vm_id in vm_ids:
                    if vm_id in path:
                        browse_content[0][index] = path.replace(vm_id, vm_ids[vm_id])

        temp_dict = {}

        for path in browse_content[1]:
            if vm_ids:
                for vm_id in vm_ids:
                    if vm_id in path:
                        temp_dict[path.replace(vm_id, vm_ids[vm_id])] = browse_content[1][path]

        return browse_content[0], temp_dict

    def _process_restore_request(self, vm_names, restore_content):
        &#34;&#34;&#34;Processes the Restore Request and replaces the VM display name with
           their ID before passing to the API.

            Args:
                vm_names            (dict)      --  dictionary with VM Name as
                                                    Key, VM ID as value

                restore_content     (tuple)    --  content to restore specified
                                                   by user

            Returns:
                list - list of all folders or files with their full paths
                       inside the input path
        &#34;&#34;&#34;
        for index, path in enumerate(restore_content):
            if vm_names:
                for vm_name in vm_names:
                    if vm_name in path:
                        restore_content[index] = path.replace(vm_name, vm_names[vm_name])

        return restore_content

    def browse(self, vm_path=&#39;\\&#39;,
               show_deleted_files=False,
               vm_disk_browse=False,
               vm_files_browse=False,
               operation=&#39;browse&#39;,
               copy_precedence=0,
               **kwargs
               ):
        &#34;&#34;&#34;Gets the content of the backup for this subclient at the path
           specified.

            Args:
                vm_path             (str)   --  vm path to get the contents of
                                                default: &#39;\\&#39;;
                                                returns the root of the Backup
                                                content

                show_deleted_files  (bool)  --  include deleted files in the
                                                content or not default: False

                vm_disk_browse      (bool)  --  browse virtual machine files
                                                e.g.; .vmdk files, etc.
                                                only applicable when browsing
                                                content inside a guest virtual
                                                machine
                                                default: False

                vm_files_browse      (bool)  -- browse files and folders
                                                default: True

                operation            (str)   -- Type of operation, browser of find

                copy_precedence      (int)   -- The copy precedence to do the operation from

            Kwargs(optional)

                live_browse           (bool)   -- set to True to get live browse content
                                                    even though file indexing is enabled

            Returns:
                list - list of all folders or files with their full paths
                       inside the input path

                dict - path along with the details like name, file/folder,
                       size, modification time

            Raises:
                SDKException:
                    if failed to browse content

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        vm_ids, vm_names = self._get_vm_ids_and_names_dict()

        if operation == &#39;find&#39;:
            # Return all VMs browse content for find operation
            vm_path_list = []
            browse_content_dict = {}
            if not vm_names:
                _vm_ids, vm_names = self._get_vm_ids_and_names_dict_from_browse()
            vm_paths = [&#39;\\&#39; + vm_id for vm_id in vm_names.values()]
            for vm_path in vm_paths:
                vm_path = self._parse_vm_path(vm_names, vm_path)
                browse_content = super(VirtualServerSubclient, self).browse(
                    show_deleted_files, vm_disk_browse, True, path=vm_path,
                    vs_file_browse=vm_files_browse, operation=operation,
                    copy_precedence=copy_precedence
                )
                vm_path_list += browse_content[0]
                browse_content_dict.update(browse_content[1])
            browse_content = (vm_path_list, browse_content_dict)

        else:
            vm_path = self._parse_vm_path(vm_names, vm_path)
            browse_content = super(VirtualServerSubclient, self).browse(
                show_deleted_files, vm_disk_browse, True, path=vm_path,
                vs_file_browse=vm_files_browse, operation=operation, **kwargs
            )

        if not vm_ids:
            for key, val in browse_content[1].items():
                vm_ids[val[&#39;snap_display_name&#39;]] = val[&#39;name&#39;]
        return self._process_vsa_browse_response(vm_ids, browse_content)

    def parse_nics_xml(self, input_xml):
        &#34;&#34;&#34;
            Gets the content of the backup for this subclient at the path
            specified.

            Args:
                input_xml : --   nics info xml per vm to parse the nics name
                                 and label

            Returns:
                nic_list:   --    list of all Nics for a VM

            Raise:
                SDKException:
                    if input parameter is not proper


        &#34;&#34;&#34;
        if not isinstance(input_xml, str):
            raise SDKException(&#34;Subclient&#34;, &#34;101&#34;)

        root = ET.fromstring(input_xml)

        nic_list = []

        for nic in root.findall(&#39;nic&#39;):
            name = nic.get(&#39;name&#39;)
            label = nic.get(&#39;label&#39;)
            subnet = nic.get(&#39;subnet&#39;)
            networkDisplayName = nic.get(&#39;networkDisplayName&#39;, &#34;&#34;)
            sourceNetwork = nic.get(&#39;id&#39;,&#34;&#34;)

            nic_info = {
                &#39;name&#39;: name,
                &#39;label&#39;: label,
                &#39;subnetId&#39;: subnet,
                &#39;networkDisplayName&#39;: networkDisplayName,
                &#39;sourceNetwork&#39;: sourceNetwork
            }
            nic_list.append(nic_info)

        return nic_list

    def get_nics_from_browse(self, copy_precedence=0):
        &#34;&#34;&#34;
            Browses the vm to get the nics info xml, gets the nics info using
            the parse_nics_xml method and prepares the dict for nics json

            Args:
                copy_precedence     (int)   --  The copy precedence to do browse from

            Returns:
                dict:   --   dict with key as vm_name and the value as the
                             nics info for that vm

        &#34;&#34;&#34;

        path, path_dict = self.browse(vm_disk_browse=True, copy_precedence=copy_precedence)

        nics_dict = {}
        nics = &#34;&#34;

        # Added for v2.1
        for vmpath in path:
            result = path_dict[vmpath]
            if (&#39;browseMetaData&#39; not in result[&#39;advanced_data&#39;]) or \
                    (&#39;virtualServerMetaData&#39; not in result[&#39;advanced_data&#39;][&#39;browseMetaData&#39;]) or \
                    (&#39;nics&#39; not in result[&#39;advanced_data&#39;][&#39;browseMetaData&#39;][&#39;virtualServerMetaData&#39;]):
                path, path_dict = self.browse(vm_disk_browse=True, operation=&#39;find&#39;, copy_precedence=copy_precedence)
        for vmpath in path:
            result = path_dict[vmpath]
            name = &#34;&#34;
            if &#39;name&#39; in result:
                name = result[&#39;name&#39;]
            if &#39;advanced_data&#39; in result:
                advanced_data = result[&#39;advanced_data&#39;]

                if &#39;browseMetaData&#39; in advanced_data:
                    browse_meta_data = advanced_data[&#39;browseMetaData&#39;]

                    if &#39;virtualServerMetaData&#39; in browse_meta_data:
                        virtual_server_metadata = browse_meta_data[&#39;virtualServerMetaData&#39;]

                        if &#39;nics&#39; in virtual_server_metadata:
                            nics = virtual_server_metadata[&#39;nics&#39;]

            nics_dict[name] = self.parse_nics_xml(nics)

        return nics_dict

    def browse_in_time(
            self,
            vm_path=&#39;\\&#39;,
            show_deleted_files=False,
            restore_index=True,
            vm_disk_browse=False,
            from_date=0,
            to_date=0,
            copy_precedence=0,
            vm_files_browse=False,
            media_agent=&#34;&#34;):
        &#34;&#34;&#34;Gets the content of the backup for this subclient
                at the path specified in the time range specified.

                Args:
                    vm_path             (str)   --  folder path to get the
                                                    contents of
                                                    default: &#39;\\&#39;
                                                    returns the root of the
                                                    Backup content

                    show_deleted_files  (bool)  --  include deleted files in
                                                    the content or not
                                                    default: False

                    restore_index       (bool)  --  restore index if it is not
                                                    cached  default: True

                    vm_disk_browse      (bool)  --  browse the VM disks or not
                                                    default: False

                    from_date           (int)   --  date to get the contents
                                                    after
                                                    format: dd/MM/YYYY
                                                    gets contents from
                                                    01/01/1970 if not specified
                                                    default: 0

                    to_date             (int)  --   date to get the contents
                                                    before
                                                    format: dd/MM/YYYY
                                                    gets contents till current
                                                    day if not specified
                                                    default: 0

                    copy_precedence     (int)   --  copy precedence to be used
                                                    for browsing

                    media_agent         (str)   --  Browse MA via with Browse has to happen.
                                                    It can be MA different than Storage Policy MA

                Returns:
                    list - list of all folders or files with their full paths
                           inside the input path

                    dict - path along with the details like name, file/folder,
                           size, modification time

                Raises:
                    SDKException:
                        if from date value is incorrect

                        if to date value is incorrect

                        if to date is less than from date

                        if failed to browse content

                        if response is empty

                        if response is not success
            &#34;&#34;&#34;
        vm_ids, vm_names = self._get_vm_ids_and_names_dict()
        vm_path = self._parse_vm_path(vm_names, vm_path)

        browse_content = super(VirtualServerSubclient, self).browse(
            show_deleted=show_deleted_files, restore_index=restore_index,
            vm_disk_browse=vm_disk_browse,
            from_time=from_date, to_time=to_date, copy_precedence=copy_precedence,
            path=vm_path, vs_file_browse=vm_files_browse, media_agent=media_agent)
        if not vm_ids:
            for key, val in browse_content[1].items():
                vm_ids[val[&#39;snap_display_name&#39;]] = val[&#39;name&#39;]
        return self._process_vsa_browse_response(vm_ids, browse_content)

    def disk_level_browse(self, vm_path=&#39;\\&#39;,
                          show_deleted_files=False,
                          restore_index=True,
                          from_date=0,
                          to_date=0,
                          copy_precedence=0):
        &#34;&#34;&#34;Browses the Disks of a Virtual Machine.

            Args:
                vm_path             (str)   --  vm path to get the contents of
                    default: &#39;\\&#39;; returns the root of the Backup content

                show_deleted_files  (bool)  --  include deleted files in the
                                                content or not default: False

                restore_index  (bool)  --       Restore index or not.
                                                default: True

                from_date           (int)   --  date to get the contents after
                                                format: dd/MM/YYYY
                                                gets contents from 01/01/1970
                                                if not specified
                                                default: 0

                to_date             (int)  --  date to get the contents before
                                               format: dd/MM/YYYY
                                               gets contents till current day
                                               if not specified
                                               default: 0

                copy_precedence     (int)   --  copy precedence to be used
                                                    for browsing

            Returns:
                list - list of all folders or files with their full paths
                       inside the input path

                dict - path along with the details like name, file/folder,
                       size, modification time

            Raises:
                SDKException:
                    if failed to browse content

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        browse_content = self.browse_in_time(
            vm_path, show_deleted_files, restore_index, True, from_date, to_date, copy_precedence
        )

        paths_list = []
        for path in browse_content[0]:
            if any(path.lower().endswith(Ext) for Ext in self.diskExtension):
                paths_list.append(path)

            elif os.path.splitext(path)[1] == &#34;&#34; and &#34;none&#34; in self.diskExtension:
                paths_list.append(path)

        paths_dict = {}

        for path in browse_content[1]:
            if any(path.lower().endswith(Ext) for Ext in self.diskExtension):
                paths_dict[path] = browse_content[1][path]
            elif os.path.splitext(path)[1] == &#34;&#34; and &#34;none&#34; in self.diskExtension:
                # assuming it as Fusion compute kind of hypervisors
                paths_dict[path] = browse_content[1][path]

        if paths_list and paths_dict:
            return paths_list, paths_dict
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;113&#39;)

    def guest_files_browse(
            self,
            vm_path=&#39;\\&#39;,
            show_deleted_files=False,
            restore_index=True,
            from_date=0,
            to_date=0,
            copy_precedence=0,
            media_agent=&#34;&#34;):
        &#34;&#34;&#34;Browses the Files and Folders inside a Virtual Machine in the time
           range specified.

            Args:
                vm_path             (str)   --  folder path to get the contents
                                                of
                                                default: &#39;\\&#39;;
                                                returns the root of the Backup
                                                content

                show_deleted_files  (bool)  --  include deleted files in the
                                                content or not default: False

                restore_index       (bool)  --  restore index if it is not cached
                                                default: True

                from_date           (int)   --  date to get the contents after
                                                format: dd/MM/YYYY

                                                gets contents from 01/01/1970
                                                if not specified
                                                default: 0

                to_date             (int)  --  date to get the contents before
                                               format: dd/MM/YYYY

                                               gets contents till current day
                                               if not specified
                                               default: 0

                copy_precedence     (int)   --  copy precedence to be used
                                                    for browsing

                media_agent         (str)   --  Browse MA via with Browse has to happen.
                                                It can be MA different than Storage Policy MA

            Returns:
                list - list of all folders or files with their full paths
                       inside the input path

                dict - path along with the details like name, file/folder,
                       size, modification time

            Raises:
                SDKException:
                    if from date value is incorrect

                    if to date value is incorrect

                    if to date is less than from date

                    if failed to browse content

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        return self.browse_in_time(
            vm_path, show_deleted_files, restore_index, False, from_date, to_date, copy_precedence,
            vm_files_browse=True, media_agent=media_agent)

    def _check_folder_in_browse(
            self,
            _vm_id,
            _folder_to_restore,
            from_date,
            to_date,
            copy_precedence,
            media_agent):
        &#34;&#34;&#34;
        Check if the particular folder is present in browse of the subclient
        in particular VM

        args:
            _vm_id      (str)     -- VM id from which folder has to be restored

            _folder_to_restore (str)     -- folder path which has to be restored

            from_date           (int)   --  date to get the contents after
                                                format: dd/MM/YYYY

                                                gets contents from 01/01/1970
                                                if not specified
                                                default: 0

            to_date             (int)  --  date to get the contents before
                                               format: dd/MM/YYYY

                                               gets contents till current day
                                               if not specified
                                               default: 0

            copy_precedence     (int)   --  copy precedence to be used
                                                    for browsing

            media_agent         (str)   --  Browse MA via with Browse has to hapeen .
                                                    It can be MA different than Storage Policy MA

        exception:
            raise exception
                if folder is not present in browse
        &#34;&#34;&#34;

        source_item = None

        _folder_to_restore = _folder_to_restore.replace(&#34;:&#34;, &#34;&#34;)
        _restore_folder_name = _folder_to_restore.split(&#34;\\&#34;)[-1]
        _folder_to_restore = _folder_to_restore.replace(&#34;\\&#34; + _restore_folder_name, &#34;&#34;)
        _source_path = &#39;\\&#39;.join([_vm_id, _folder_to_restore])

        _browse_files, _browse_files_dict = self.guest_files_browse(
            _source_path, from_date=from_date, to_date=to_date,
            copy_precedence=copy_precedence, media_agent=media_agent)

        for _path in _browse_files_dict:
            _browse_folder_name = _path.split(&#34;\\&#34;)[-1]
            if _browse_folder_name == _restore_folder_name:
                source_item = &#39;\\&#39;.join([_source_path, _restore_folder_name])
                source_item = &#39;\\&#39; + source_item
                break

        if source_item is None:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Browse failure: Folder not found in browse&#39;)

        return source_item

    def guest_file_restore(self, *args, **kwargs):
        &#34;&#34;&#34;perform Guest file restore of the provided path

        Args:
            options     (dict)  --  dictionary of guest file restores options

        &#34;&#34;&#34;
        if args and isinstance(args[0], dict):
            options = args[0]
        else:
            options = kwargs
        vm_name = options.get(&#39;vm_name&#39;, None)
        folder_to_restore = options.get(&#39;folder_to_restore&#39;, None)
        destination_client = options.get(&#39;destination_client&#39;, None)
        destination_path = options.get(&#39;destination_path&#39;, None)
        copy_precedence = options.get(&#39;copy_precedence&#39;, 0)
        preserve_level = options.get(&#39;preserve_level&#39;, 1)
        unconditional_overwrite = options.get(&#39;unconditional_overwrite&#39;, False)
        restore_ACL = options.get(&#39;restore_ACL&#39;, True)
        from_date = options.get(&#39;from_date&#39;, 0)
        to_date = options.get(&#39;to_date&#39;, 0)
        show_deleted_files = options.get(&#39;show_deleted_files&#39;, False)
        fbr_ma = options.get(&#39;fbr_ma&#39;, None)
        browse_ma = options.get(&#39;browse_ma&#39;, &#34;&#34;)
        agentless = options.get(&#39;agentless&#39;, &#34;&#34;)
        in_place = options.get(&#39;in_place&#39;, False)

        _vm_names, _vm_ids = self._get_vm_ids_and_names_dict_from_browse()
        _file_restore_option = {}
        _verify_path = options.get(&#39;verify_path&#39;, True)

        # check if inputs are correct
        if not(isinstance(destination_path, str) and
               (isinstance(vm_name, str))):
            raise SDKException(&#39;Subclient&#39;, &#39;105&#39;)

        if vm_name not in _vm_names:
            raise SDKException(&#39;Subclient&#39;, &#39;111&#39;)

        # check if client name is correct
        if destination_client is None:
            destination_client = self._backupset_object._instance_object.co_ordinator

        if fbr_ma:
            _file_restore_option[&#34;proxy_client&#34;] = fbr_ma

        _file_restore_option[&#34;client&#34;] = destination_client
        _file_restore_option[&#34;destination_path&#34;] = destination_path

        # process the folder to restore for browse
        if isinstance(folder_to_restore, list):
            _folder_to_restore_list = folder_to_restore

        elif isinstance(folder_to_restore, str):
            _folder_to_restore_list = []
            _folder_to_restore_list.append(folder_to_restore)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;105&#39;)

        _file_restore_option[&#34;paths&#34;] = []
        for _each_folder in _folder_to_restore_list:
            # check_folder_in_browse modifies path (removes colon) and verifies in browse results.
            # The modified path does not work for windows VM when file indexing is enabled
            # Set `verify_path` to False to skip this verification and use the restore path as is

            if _verify_path:
                _restore_item_path = self._check_folder_in_browse(
                    _vm_ids[vm_name],
                    &#34;%s&#34; % _each_folder,
                    from_date,
                    to_date,
                    copy_precedence,
                    media_agent=browse_ma
                )
            else:
                # Converting native path to VM path
                # C:\folder1 =&gt; \&lt;vm_guid&gt;\C:\folder1
                # /folder1/folder2 =&gt; \&lt;vm_guid&gt;\folder1\folder2

                _item_path = _each_folder.replace(&#39;/&#39;, &#39;\\&#39;)
                _item_path = _item_path[1:] if _item_path[0] == &#39;\\&#39; else _item_path
                _restore_item_path = &#39;\\&#39;.join([&#39;&#39;, _vm_ids[vm_name], _item_path])

            _file_restore_option[&#34;paths&#34;].append(_restore_item_path)

        # set the browse options
        _file_restore_option[&#34;disk_browse&#34;] = False
        _file_restore_option[&#34;file_browse&#34;] = True
        _file_restore_option[&#34;from_time&#34;] = from_date
        _file_restore_option[&#34;to_time&#34;] = to_date

        # set the common file level restore options
        _file_restore_option[&#34;striplevel_type&#34;] = &#34;PRESERVE_LEVEL&#34;
        _file_restore_option[&#34;preserve_level&#34;] = preserve_level
        _file_restore_option[&#34;unconditional_overwrite&#34;] = unconditional_overwrite
        _file_restore_option[&#34;restore_ACL&#34;] = restore_ACL
        _file_restore_option[&#34;in_place&#34;] = in_place

        # set the browse option
        _file_restore_option[&#34;copy_precedence_applicable&#34;] = True
        _file_restore_option[&#34;copy_precedence&#34;] = copy_precedence
        _file_restore_option[&#34;media_agent&#34;] = browse_ma

        # set agentless options
        if agentless:
            _file_restore_option[&#34;server_name&#34;] = agentless[&#39;vserver&#39;]
            _file_restore_option[&#34;vm_guid&#34;] = agentless[&#39;vm_guid&#39;]
            _file_restore_option[&#34;vm_name&#34;] = agentless[&#39;vm_name&#39;]
            _file_restore_option[&#34;user_name&#34;] = agentless[&#39;vm_user&#39;]
            _file_restore_option[&#34;password&#34;] = agentless[&#39;vm_pass&#39;]
            _file_restore_option[&#34;agentless&#34;] = True

        # prepare and execute the Json
        request_json = self._prepare_filelevel_restore_json(_file_restore_option)
        return self._process_restore_response(request_json)

    def vm_files_browse(self, vm_path=&#39;\\&#39;, show_deleted_files=False, operation=&#39;browse&#39;, copy_precedence=0):
        &#34;&#34;&#34;Browses the Files and Folders of a Virtual Machine.

            Args:
                vm_path             (str)   --  vm path to get the contents of
                                                default: &#39;\\&#39;;
                                                returns the root of the Backup
                                                content

                show_deleted_files  (bool)  --  include deleted files in the
                                                content or not
                                                default: False

                operation           (str)   --  The type of operation to perform (browse/find)

                copy_precedence     (int)   --  The copy precedence to do browse from

            Returns:
                list - list of all folders or files with their full paths
                       inside the input path

                dict - path along with the details like name, file/folder,
                       size, modification time

            Raises:
                SDKException:
                    if failed to browse content

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        return self.browse(vm_path, show_deleted_files, True, operation=operation, copy_precedence=copy_precedence)

    def vm_files_browse_in_time(
            self,
            vm_path=&#39;\\&#39;,
            show_deleted_files=False,
            restore_index=True,
            from_date=0,
            to_date=0):
        &#34;&#34;&#34;Browses the Files and Folders of a Virtual Machine in the time range
           specified.

            Args:
                vm_path             (str)   --  folder path to get the contents
                                                default: &#39;\\&#39;;
                                                returns the root of the Backup
                                                content

                show_deleted_files  (bool)  --  include deleted files in the
                                                content or not
                                                default: False

                restore_index       (bool)  --  restore index if it is not
                                                cached
                                                default: True

                from_date           (int)   --  date to get the contents after
                                                format: dd/MM/YYYY
                                                gets contents from 01/01/1970
                                                if not specified
                                                default: 0

                to_date             (int)  --   date to get the contents before
                                                format: dd/MM/YYYY
                                                gets contents till current day
                                                if not specified
                                                default: 0

            Returns:
                list - list of all folders or files with their full paths
                       inside the input path

                dict - path along with the details like name, file/folder,
                       size, modification time

            Raises:
                SDKException:
                    if from date value is incorrect

                    if to date value is incorrect

                    if to date is less than from date

                    if failed to browse content

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        return self.browse_in_time(
            vm_path, show_deleted_files, restore_index, True, from_date, to_date
        )

    def reinitialize_vm_names_browse(self):
        self._vm_names_browse = []
        self._get_vm_ids_and_names_dict_from_browse()

    def _get_disk_extension(self, disk_list):
        &#34;&#34;&#34;
        get the Extension of all disk in the list

        Args:
            disk_list   (LIST)  -- get the disk List

        return:
            extn_list   (LIST)  --  returns the Extension List of the disk list
        &#34;&#34;&#34;

        _extn_list = []
        for each_disk in disk_list:
            _disk_name, _extn_name = os.path.splitext(each_disk)
            _extn_list.append(_extn_name)

        _extn_list = list(set(_extn_list))

        if len(_extn_list) &gt; 1:
            return _extn_list
        else:
            return _extn_list[0]

    def _get_conversion_disk_Type(self, _src_disk_extn, _dest_disk_extn):
        &#34;&#34;&#34;
        return volume restore type and destination disk Type

        Args:
            src_disk_extn   (str)   --  source disk extension of the disk
            dest_disk_extn  (str)   --  Extension to which disk is converted

        return
            _vol_restore_type   (str)   -- value of Volume restore type
                                           parameter of the XML
            _dest_disk_type     (str)   -- value of destination Disk Type
                                           parameter of the XML
        &#34;&#34;&#34;

        disk_conversion = {
            &#34;vhdx&#34;: {
                &#34;vhd&#34;: (&#34;VIRTUAL_HARD_DISKS&#34;, &#34;VHD_DYNAMIC&#34;),
                &#34;vmdk&#34;: (&#34;VMDK_FILES&#34;, &#34;VMDK_VCB4&#34;)
            },
            &#34;vmdk&#34;: {
                &#34;vhd&#34;: (&#34;VIRTUAL_HARD_DISKS&#34;, &#34;VHD_DYNAMIC&#34;),
                &#34;vhdx&#34;: (&#34;VIRTUAL_HARD_DISKS&#34;, &#34;VHDX_DYNAMIC&#34;)
            }
        }
        _src_disk_extn = _src_disk_extn.lower().strip(&#34;.&#34;)
        _dest_disk_extn = _dest_disk_extn.lower().strip(&#34;.&#34;)

        return disk_conversion[_src_disk_extn][_dest_disk_extn]

    def _set_vm_to_restore(self, vm_to_restore=None, restore_option=None):
        &#34;&#34;&#34;
        check whether the VMs provided for restore is backued up else assume
                            Vm_to_restore with default

        Args:
            vm_to_restore   (list)      -- list of Vm to restore

            restore_option  (dict)      -- dict with all restore options

        return:
            vm_to_restore   (list)      -- Final list of Vm need to be restored

        &#34;&#34;&#34;
        if restore_option is None:
            restore_option = {}

        if not self._vm_names_browse:
            self._get_vm_ids_and_names_dict_from_browse()

        # set vms to restore
        if not vm_to_restore:
            vm_to_restore = restore_option.get(&#34;vm_to_restore&#34;, self._vm_ids_browse)
            _temp_res_list = vm_to_restore

        else:
            _temp_res_list = []
            if not isinstance(vm_to_restore, list):
                vm_to_restore = [vm_to_restore]
            for each_vm in vm_to_restore:
                _temp_res_list.append(each_vm)

        vm_to_restore = list(set(self._vm_names_browse) &amp; set(_temp_res_list))

        if not vm_to_restore:
            raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)

        return vm_to_restore

    def _set_restore_inputs(self, restore_option, **kwargs):
        &#34;&#34;&#34;
        set all the advanced properties of the subclient restore

        Args:
            restore_option  (dict)  -- restore option dictionary where advanced
                                            properties to be appended

            **kwargs                --  Keyword arguments with key as property name
                                            and its value
        &#34;&#34;&#34;
        for key in kwargs:
            if key not in restore_option or restore_option[key] is None:
                restore_option[key] = kwargs[key]

    def _get_subclient_proxies(self):
        &#34;&#34;&#34;
        Get the list of all the proxies on a selected subclient

        Returns:
            associated_proxies   (List)  --  returns the proxies list
        &#34;&#34;&#34;
        associated_proxies = []
        try:
            available_subclient_proxies = self._vsaSubclientProp[&#34;proxies&#34;][&#34;memberServers&#34;]
            if len(available_subclient_proxies) &gt; 0:
                for client in available_subclient_proxies:
                    if &#39;clientName&#39; in client[&#39;client&#39;]:
                        associated_proxies.append(client[&#34;client&#34;][&#34;clientName&#34;])
                    elif &#39;clientGroupName&#39; in client[&#39;client&#39;]:
                        client_group = self._commcell_object.client_groups.get(client[&#34;client&#34;][&#34;clientGroupName&#34;])
                        associated_proxies.extend(client_group.associated_clients)
        except KeyError:
            pass
        return list(dict.fromkeys(associated_proxies))

    def _set_restore_defaults(self, restore_option):
        &#34;&#34;&#34;
        :param restore_option:  dict with all restore input values
        &#34;&#34;&#34;

        if ((&#34;vcenter_client&#34; not in restore_option) or (
                restore_option[&#34;vcenter_client&#34;] is None)):
            instance_dict = self._backupset_object._instance_object._properties[&#39;instance&#39;]
            restore_option[&#34;destination_client_name&#34;] = instance_dict[&#34;clientName&#34;]
            restore_option[&#34;destination_instance&#34;] = instance_dict[&#34;instanceName&#34;]
            instance = self._backupset_object._instance_object

        else:
            client = self._commcell_object.clients.get(restore_option[&#34;vcenter_client&#34;])
            restore_option[&#34;destination_client_name&#34;] = restore_option[&#34;vcenter_client&#34;]
            restore_option[&#34;destination_client_id&#34;] = int(client.client_id)
            agent = client.agents.get(&#39;Virtual Server&#39;)
            instancekeys = next(iter(agent.instances._instances))
            instance = agent.instances.get(instancekeys)
            restore_option[&#34;destination_instance&#34;] = instance.instance_name
            restore_option[&#34;destination_instance_id&#34;] = int(instance.instance_id)

        if ((&#34;esx_server&#34; not in restore_option) or
                (restore_option[&#34;esx_server&#34;] is None)):
            restore_option[&#34;esx_server&#34;] = instance.server_host_name[0]

        if ((&#34;client_name&#34; not in restore_option) or
                (restore_option[&#34;client_name&#34;] is None)):
            subclient_proxy_list = self._get_subclient_proxies()

            if len(subclient_proxy_list) &gt; 0:
                restore_option[&#34;client&#34;] = subclient_proxy_list[0]
            else:
                restore_option[&#34;client&#34;] = instance.co_ordinator
        else:
            restore_option[&#34;client&#34;] = restore_option[&#34;client_name&#34;]

    def _set_vm_conversion_defaults(self, vcenter_client, restore_option):
        &#34;&#34;&#34;
        set all the VMconversion changews need to be performed
        Args:
            vcenter_client: Client Name to which it has to be converted

            restore_option: dictinoary where parameter needs to be set

        Returns:
            subclient :     (obj)   : object for the subclient class of virtual client
            raise exception:
             if client does not exist

        &#34;&#34;&#34;

        client = self._commcell_object.clients.get(vcenter_client)
        agent = client.agents.get(&#39;Virtual Server&#39;)
        instancekeys = next(iter(agent.instances._instances))
        instance = agent.instances.get(instancekeys)
        backupsetkeys = next(iter(instance.backupsets._backupsets))
        backupset = instance.backupsets.get(backupsetkeys)
        sckeys = next(iter(backupset.subclients._subclients))
        subclient = backupset.subclients.get(sckeys)

        # populating all defaults
        esx_server = instance.server_host_name[0]
        self.disk_pattern = subclient.disk_pattern
        restore_option[&#34;destination_vendor&#34;] = instance._vendor_id
        restore_option[&#34;backupset_client_name&#34;] = client.client_name

        if not subclient:
            raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)

        return subclient

    def set_advanced_vm_restore_options(self, vm_to_restore, restore_option):
        &#34;&#34;&#34;
        set the advanced restore options for all vm in restore
        :param

        vm_to_restore : Name of the VM to restore
        restore_option: restore options that need to be set for advanced restore option

            power_on                    - power on the VM after restore
            add_to_failover             - Register the VM to Failover Cluster
            datastore                   - Datastore where the VM needs to be restored

            disks   (list of dict)      - list with dict for each disk in VM
                                            eg: [{
                                                    name:&#34;disk1.vmdk&#34;
                                                    datastore:&#34;local&#34;
                                                }
                                                {
                                                    name:&#34;disk2.vmdk&#34;
                                                    datastore:&#34;local1&#34;
                                                }
                                            ]
            guid                        - GUID of the VM needs to be restored
            new_name                    - New name for the VM to be restored
            esx_host                    - esx_host or client name where it need to be restored
            name                        - name of the VM to be restored
        &#34;&#34;&#34;

        # Set the new name for the restored VM.
        # If new_name is not given, it restores the VM with same name
        # with suffix Delete.
        vm_names, vm_ids = self._get_vm_ids_and_names_dict_from_browse()
        copy_precedence = restore_option.get(&#39;copy_precedence&#39;, 0)
        browse_result = self.vm_files_browse(copy_precedence=copy_precedence)

        # vs metadata from browse result
        _metadata = browse_result[1][(&#39;\\&#39; + vm_to_restore)]

        if (&#39;browseMetaData&#39; not in _metadata[&#39;advanced_data&#39;]) or \
                (&#39;virtualServerMetaData&#39; not in _metadata[&#39;advanced_data&#39;][&#39;browseMetaData&#39;]) or \
                (&#39;nics&#39; not in _metadata[&#39;advanced_data&#39;][&#39;browseMetaData&#39;][&#39;virtualServerMetaData&#39;]):
            browse_result = self.vm_files_browse(operation=&#39;find&#39;, copy_precedence=copy_precedence)
            _metadata = browse_result[1][(&#39;\\&#39; + vm_to_restore)]

        vs_metadata = _metadata[&#34;advanced_data&#34;][&#34;browseMetaData&#34;][&#34;virtualServerMetaData&#34;]
        if restore_option[&#39;in_place&#39;]:
            folder_path = vs_metadata.get(&#34;inventoryPath&#34;, &#39;&#39;)
            instanceSize = vs_metadata.get(&#34;instanceSize&#34;, &#39;&#39;)
        else:
            folder_path = restore_option[&#39;folder_path&#39;] if restore_option.get(&#39;folder_path&#39;) else &#39;&#39;
            instanceSize = &#39;&#39;

        if &#39;resourcePoolPath&#39; in restore_option and restore_option[&#39;resourcePoolPath&#39;] is None:
            restore_option[&#39;resourcePoolPath&#39;] = vs_metadata[&#39;resourcePoolPath&#39;]
        if &#39;datacenter&#39; in restore_option and restore_option[&#39;datacenter&#39;] is None:
            restore_option[&#39;datacenter&#39;] = vs_metadata.get(&#39;dataCenter&#39;, &#39;&#39;)
        if (&#39;terminationProtected&#39; in restore_option and
                restore_option[&#39;terminationProtected&#39;] is None):
            restore_option[&#39;terminationProtected&#39;] = vs_metadata.get(&#39;terminationProtected&#39;, &#39;&#39;)
        if &#39;iamRole&#39; in restore_option and restore_option[&#39;iamRole&#39;] is None:
            restore_option[&#39;iamRole&#39;] = vs_metadata.get(&#39;role&#39;, &#39;&#39;)
        if &#39;securityGroups&#39; in restore_option and restore_option[&#39;securityGroups&#39;] is None:
            _security_groups = self._find_security_groups(vs_metadata[&#39;networkSecurityGroups&#39;])
            restore_option[&#39;securityGroups&#39;] = _security_groups
        if &#39;keyPairList&#39; in restore_option and restore_option[&#39;keyPairList&#39;] is None:
            _keypair_list = self._find_keypair_list(vs_metadata[&#39;loginKeyPairs&#39;])
            restore_option[&#39;keyPairList&#39;] = _keypair_list

        # populate restore source item
        restore_option[&#39;paths&#39;].append(&#34;\\&#34; + vm_ids[vm_to_restore])
        restore_option[&#39;name&#39;] = vm_to_restore
        restore_option[&#39;guid&#39;] = vm_ids[vm_to_restore]
        restore_option[&#34;FolderPath&#34;] = folder_path
        restore_option[&#34;ResourcePool&#34;] = &#34;/&#34;

        # populate restore disk and datastore
        vm_disks = []
        disk_list, disk_info_dict = self.disk_level_browse(
            &#34;\\\\&#34; + vm_ids[vm_to_restore], copy_precedence=copy_precedence)

        for disk, data in disk_info_dict.items():
            ds = &#34;&#34;
            if &#34;datastore&#34; in restore_option:
                ds = restore_option[&#34;datastore&#34;]
            if restore_option[
                &#34;in_place&#34;] or &#34;datastore&#34; not in restore_option or not restore_option.get(
                    &#39;datastore&#39;):
                if &#34;datastore&#34; in data[&#34;advanced_data&#34;][&#34;browseMetaData&#34;][&#34;virtualServerMetaData&#34;]:
                    restore_option[&#34;datastore&#34;] = data[&#34;advanced_data&#34;][&#34;browseMetaData&#34;][
                        &#34;virtualServerMetaData&#34;][&#34;datastore&#34;]
                    ds = restore_option[&#34;datastore&#34;]
                elif &#34;esxHost&#34; in vs_metadata and &#34;is_aws_proxy&#34; in restore_option:
                    if restore_option.get(&#34;availability_zone&#34;) is not None:
                        ds = restore_option.get(&#34;availability_zone&#34;)
                    else:
                        ds = vs_metadata[&#34;esxHost&#34;]
            new_name_prefix = restore_option.get(&#34;disk_name_prefix&#34;)
            new_name = data[&#34;name&#34;] if new_name_prefix is None \
                else new_name_prefix + &#34;_&#34; + data[&#34;name&#34;]
            if self._instance_object.instance_name == HypervisorType.GOOGLE_CLOUD.value.lower():
                new_name = &#34;&#34;
                if data[&#34;advanced_data&#34;][&#34;browseMetaData&#34;][&#34;virtualServerMetaData&#34;].get(&#39;replicaZones&#39;, False):
                    replicaZones = restore_option.get(&#34;replicaZones&#34;)
            if restore_option[&#39;destination_instance&#39;].lower() in [HypervisorType.VIRTUAL_CENTER.value.lower(),
                                                                  HypervisorType.AZURE_V2.value.lower()]:
                _disk_dict = self._disk_dict_pattern(data[&#39;snap_display_name&#39;], ds, new_name)
            else:
                _disk_dict = self._disk_dict_pattern(disk.split(&#39;\\&#39;)[-1], ds, new_name)
            if &#39;is_aws_proxy&#39; in restore_option and not restore_option[&#39;is_aws_proxy&#39;]:
                _disk_dict[&#39;Datastore&#39;] = restore_option[&#34;datastore&#34;]
            vm_disks.append(_disk_dict)
        if not vm_disks:
            raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)
        restore_option[&#34;disks&#34;] = vm_disks

        # prepare nics info json
        if &#34;nics&#34; not in restore_option or \
                self._instance_object.instance_name == HypervisorType.GOOGLE_CLOUD.value.lower():
            nics_list = self._json_nics_advancedRestoreOptions(vm_to_restore, restore_option)
            restore_option[&#34;nics&#34;] = nics_list
            if restore_option.get(&#39;source_ip&#39;) and restore_option.get(&#39;destination_ip&#39;):
                vm_ip = self._json_vmip_advanced_restore_options(restore_option)
                restore_option[&#34;vm_ip_address_options&#34;] = vm_ip
            if restore_option[&#34;in_place&#34;]:
                if &#34;hyper&#34; in restore_option[&#34;destination_instance&#34;].lower():
                    restore_option[&#34;client_name&#34;] = vs_metadata[&#39;esxHost&#39;]
                    restore_option[&#34;esx_server&#34;] = vs_metadata[&#39;esxHost&#39;]
                elif &#39;Red&#39; in restore_option[&#34;destination_instance&#34;]:
                    restore_option[&#34;esxHost&#34;] = vs_metadata[&#39;clusterName&#39;]
                    restore_option[&#34;cluster&#34;] = vs_metadata[&#39;clusterName&#39;]
                    vs_metadata[&#34;esxHost&#34;] = vs_metadata[&#39;clusterName&#39;]

        #adding tag to restored vm
        if restore_option.get(&#39;vmTags&#39;):
            vm_tags = restore_option.get(&#39;vmTags&#39;)
            if isinstance(vm_tags, str) or isinstance(vm_tags, dict):
                vm_tags = [vm_tags]
            restore_option[&#34;vmTags&#34;] = vm_tags

        # populate VM Specific values
        self._set_restore_inputs(
            restore_option,
            disks=vm_disks,
            esx_host=restore_option.get(&#39;esx_host&#39;) or vs_metadata[&#39;esxHost&#39;],
            instanceSize=restore_option.get(&#39;instanceSize&#39;, instanceSize),
            new_name=restore_option.get(&#39;new_name&#39;, &#34;del&#34; + vm_to_restore)
        )

        temp_dict = self._json_restore_advancedRestoreOptions(restore_option)
        self._advanced_restore_option_list.append(temp_dict)

    def set_advanced_attach_disk_restore_options(self, vm_to_restore, restore_option):
        &#34;&#34;&#34;
        set the advanced restore options for all vm in restore
        :param

        vm_to_restore : Name of the VM where disks will be restored
        restore_option: restore options that need to be set for advanced restore option

            datastore                   - Datastore where the disks needs to be restored

            disks   (list of dict)      - list with dict for each disk in VM
                                            eg: [{
                                                    name:&#34;disk1.vmdk&#34;
                                                    datastore:&#34;local&#34;
                                                }
                                                {
                                                    name:&#34;disk2.vmdk&#34;
                                                    datastore:&#34;local1&#34;
                                                }
                                            ]
            guid                        - GUID of the VM needs to be restored
            new_name                    - New name for the VM to be restored
            esx_host                    - esx_host or client name where it need to be restored
            name                        - name of the VM to be restored
        &#34;&#34;&#34;

        # Set the new name for the restored VM.
        # If new_name is not given, it restores the VM with same name
        # with suffix Delete.
        vm_names, vm_ids = self._get_vm_ids_and_names_dict_from_browse()
        _ = self.vm_files_browse()
        # populate restore source item
        restore_option[&#39;name&#39;] = vm_to_restore
        restore_option[&#39;guid&#39;] = vm_ids[vm_to_restore]
        restore_option[&#34;FolderPath&#34;] = &#39;&#39;
        restore_option[&#34;ResourcePool&#34;] = &#34;/&#34;

        # populate restore disk and datastore
        vm_disks = []
        disk_list, disk_info_dict = self.disk_level_browse(
            &#34;\\\\&#34; + vm_ids[vm_to_restore])

        for disk, data in disk_info_dict.items():
            ds = &#34;&#34;
            if &#34;datastore&#34; in restore_option:
                ds = restore_option[&#34;datastore&#34;]
            new_name_prefix = restore_option.get(&#34;disk_name_prefix&#34;)
            if self._instance_object.instance_name != &#39;openstack&#39;:
                new_name = data[&#34;snap_display_name&#34;].replace(&#34;/&#34;, &#34;_&#34;).replace(&#34; &#34;, &#34;_&#34;)
                new_name = &#34;del_&#34; + new_name if new_name_prefix is None \
                    else new_name_prefix + &#34;_&#34; + new_name
            else:
                new_name = data[&#34;name&#34;]
            _disk_dict = self._disk_dict_pattern(data[&#39;snap_display_name&#39;], ds, new_name)
            vm_disks.append(_disk_dict)
        if not vm_disks:
            raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)
        restore_option[&#34;disks&#34;] = vm_disks

        # populate VM Specific values
        self._set_restore_inputs(
            restore_option,
            disks=vm_disks,
            esx_host=restore_option.get(&#39;esx&#39;),
            new_name=restore_option.get(&#39;newName&#39;, vm_to_restore),
            new_guid=restore_option.get(&#39;newGUID&#39;, restore_option.get(&#39;guid&#39;)),
            datastore=restore_option.get(&#39;datastore&#39;))

        temp_dict = self._json_restore_advancedRestoreOptions(restore_option)
        self._advanced_restore_option_list.append(temp_dict)

    @staticmethod
    def _find_security_groups(xml_str):
        &#34;&#34;&#34;
        sets the security group json from the input xml
        Args:
             xml_str            (str)  --  xml from which we want to retrieve security group info
        Returns:
             security_group    (dict)  -- security group dict
        &#34;&#34;&#34;
        match1 = re.search(r&#39;secGroupId=\&#34;(\S*)\&#34;&#39;, xml_str)
        match2 = re.search(r&#39;secGroupName=\&#34;(\S*)\&#34;&#39;, xml_str)
        security_group = [
            {
                &#34;groupId&#34;: match1.group(1),
                &#34;groupName&#34;: match2.group(1)
            }
        ]
        return security_group

    @staticmethod
    def _find_keypair_list(xml_str):
        &#34;&#34;&#34;
        sets the keypair list json from the input xml
        Args:
             xml_str            (str)  --  xml from which we want to retrieve keypair list info
        Returns:
             keypair_list       (dict) -- keypair list dict
        &#34;&#34;&#34;
        match1 = re.search(r&#39;keyPairId=\&#34;(\S*)\&#34;&#39;, xml_str)
        match2 = re.search(r&#39;keyPairName=\&#34;(\S*)\&#34;&#39;, xml_str)
        keypair_list = [
            {
                &#34;keyId&#34;: match1.group(1),
                &#34;keyName&#34;: match2.group(1)
            }
        ]
        return keypair_list

    def amazon_defaults(self, vm_to_restore, restore_option):
        &#34;&#34;&#34;
               set all the VMconversion changes need to be performed
               specfic to Amazon
               Args:
                   vm_to_restore  (str)  :  content of destination subclient object

                   restore_option (dict) :  dictionary with all VM restore options

        &#34;&#34;&#34;

        browse_result = self.vm_files_browse()
        # vs metadata from browse result
        _metadata = browse_result[1][(&#39;\\&#39; + vm_to_restore)]
        if (&#39;browseMetaData&#39; not in _metadata[&#39;advanced_data&#39;]) or \
                (&#39;virtualServerMetaData&#39; not in _metadata[&#39;advanced_data&#39;][&#39;browseMetaData&#39;]) or \
                (&#39;nics&#39; not in _metadata[&#39;advanced_data&#39;][&#39;browseMetaData&#39;][&#39;virtualServerMetaData&#39;]):
            browse_result = self.vm_files_browse(operation=&#39;find&#39;)
            _metadata = browse_result[1][(&#39;\\&#39; + vm_to_restore)]
        vs_metadata = _metadata[&#34;advanced_data&#34;][&#34;browseMetaData&#34;][&#34;virtualServerMetaData&#34;]

        restore_option[&#39;resourcePoolPath&#39;] = vs_metadata[&#39;resourcePoolPath&#39;]
        restore_option[&#39;datacenter&#39;] = vs_metadata.get(&#39;dataCenter&#39;, &#39;&#39;)
        restore_option[&#39;terminationProtected&#39;] = vs_metadata.get(&#39;terminationProtected&#39;, &#39;&#39;)
        restore_option[&#39;iamRole&#39;] = vs_metadata.get(&#39;role&#39;, &#39;&#39;)
        _security_groups = self._find_security_groups(vs_metadata[&#39;networkSecurityGroups&#39;])
        restore_option[&#39;securityGroups&#39;] = _security_groups
        _keypair_list = self._find_keypair_list(vs_metadata[&#39;loginKeyPairs&#39;])
        restore_option[&#39;keyPairList&#39;] = _keypair_list
        restore_option[&#39;esx_host&#39;] = vs_metadata.get(&#39;esxHost&#39;, &#39;&#39;)
        restore_option[&#39;datastore&#39;] = vs_metadata.get(&#39;datastore&#39;, &#39;&#39;)

        nics_list = self._json_nics_advancedRestoreOptions(vm_to_restore, restore_option)
        restore_option[&#34;nics&#34;] = nics_list

        return restore_option

    def _prepare_filelevel_restore_json(self, _file_restore_option):
        &#34;&#34;&#34;
        prepares the  file level restore json from getters
        &#34;&#34;&#34;

        if _file_restore_option is None:
            _file_restore_option = {}

        # set the setters
        self._backupset_object._instance_object._restore_association = self._subClientEntity
        request_json = self._restore_json(restore_option=_file_restore_option)
        self._json_restore_virtualServerRstOption(_file_restore_option)

        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;virtualServerRstOption&#34;] = self._virtualserver_option_restore_json

        if _file_restore_option.get(&#39;agentless&#39;):
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
                &#34;restoreOptions&#34;][&#34;virtualServerRstOption&#34;][
                &#34;fileLevelVMRestoreOption&#34;] = \
                self._json_restore_virtualServerRstOption_filelevelrestoreoption(_file_restore_option)
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
                &#34;restoreOptions&#34;][&#34;virtualServerRstOption&#34;][&#34;fileLevelVMRestoreOption&#34;][
                &#34;guestUserPassword&#34;] = self._json_restore_guest_password(_file_restore_option)

        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;volumeRstOption&#34;] = self._json_restore_volumeRstOption(_file_restore_option)

        return request_json

    def _prepare_disk_restore_json(self, _disk_restore_option=None):
        &#34;&#34;&#34;
        Prepare disk retsore Json with all getters

        Args:
            _disk_restore_option - dictionary with all disk restore options

            value:
                preserve_level              -  set the preserve level in restore
                unconditional_overwrite     - unconditionally overwrite the disk
                                                    in the restore path

                destination_path            - path where the disk needs to be restored
                client_name                 - client where the disk needs to be restored

                destination_vendor          - vendor id of the Hypervisor
                destination_disktype        - type of disk needs to be restored like VHDX,VHD,VMDK
                paths                 - GUID of VM from which disk needs to be restored
                                                eg:\\5F9FA60C-0A89-4BD9-9D02-C5ACB42745EA

                copy_precedence_applicable  - True if needs copy_precedence to be honored else
                                                        False

                copy_precedence            - the copy id from which browse and
                                                                restore needs to be performed

        returns:
            request_json        -complete json for performing disk Restore options

        &#34;&#34;&#34;

        if _disk_restore_option is None:
            _disk_restore_option = {}

        # set the setters
        self._backupset_object._instance_object._restore_association = self._subClientEntity
        request_json = self._restore_json(restore_option=_disk_restore_option)
        self._json_restore_virtualServerRstOption(_disk_restore_option)
        self._json_restore_diskLevelVMRestoreOption(_disk_restore_option)

        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][
            &#34;options&#34;][&#34;restoreOptions&#34;][&#34;virtualServerRstOption&#34;] = self._virtualserver_option_restore_json

        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;volumeRstOption&#34;] = self._json_restore_volumeRstOption(_disk_restore_option)

        return request_json

    def _prepare_attach_disk_restore_json(self, _disk_restore_option=None):
        &#34;&#34;&#34;
        Prepare attach disk retsore Json with all getters

        Args:
            _disk_restore_option - dictionary with all attach disk restore options

            value:
                destination_path            - path where the disk needs to be restored

                client_name                 - client where the disk needs to be restored

                destination_vendor          - vendor id of the Hypervisor

                paths                 - GUID of VM from which disk needs to be restored
                                                eg:\\5F9FA60C-0A89-4BD9-9D02-C5ACB42745EA

                copy_precedence_applicable  - True if needs copy_precedence to be honored else
                                                        False

                copy_precedence            - the copy id from which browse and
                                                                restore needs to be performed

        returns:
            request_json        -complete json for performing disk Restore options

        &#34;&#34;&#34;

        if _disk_restore_option is None:
            _disk_restore_option = {}

        # set the setters
        self._backupset_object._instance_object._restore_association = self._subClientEntity
        request_json = self._restore_json(restore_option=_disk_restore_option)
        self._set_restore_defaults(_disk_restore_option)
        self._json_restore_virtualServerRstOption(_disk_restore_option)
        self._json_vcenter_instance(_disk_restore_option)
        self._json_restore_attach_diskLevelVMRestoreOption(_disk_restore_option)
        self.set_advanced_attach_disk_restore_options(_disk_restore_option[&#39;vm_to_restore&#39;], _disk_restore_option)
        self._virtualserver_option_restore_json[&#34;diskLevelVMRestoreOption&#34;][
            &#34;advancedRestoreOptions&#34;] = self._advanced_restore_option_list
        self._advanced_restore_option_list = []
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;virtualServerRstOption&#34;] = self._virtualserver_option_restore_json
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;volumeRstOption&#34;] = self._json_restore_volumeRstOption(_disk_restore_option)
        if _disk_restore_option.get(&#39;new_instance&#39;):
            request_json = self._update_attach_disk_restore_new_instance(request_json, _disk_restore_option)
        return request_json

    @staticmethod
    def _update_attach_disk_restore_new_instance(json_to_be_edited, _disk_restore_option):
        &#34;&#34;&#34;
        Updates teh Json for attach disk restore as a new instance

        Args:
            json_to_be_edited               (dict): Request json to be edited

            _disk_restore_option:           (dict): Attach dsik restore options

        Returns:
            json_to_be_edited               (dict): Dictionary after its edited

        &#34;&#34;&#34;
        json_to_be_edited[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][
            &#39;virtualServerRstOption&#39;][&#39;diskLevelVMRestoreOption&#39;][&#39;powerOnVmAfterRestore&#39;] = True
        adv_options = json_to_be_edited[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][
            &#39;virtualServerRstOption&#39;][&#39;diskLevelVMRestoreOption&#39;][&#39;advancedRestoreOptions&#39;][0]
        del adv_options[&#39;newGuid&#39;]
        del adv_options[&#39;nics&#39;][0][&#39;destinationNetwork&#39;]
        _nic2 = adv_options[&#39;nics&#39;][0].copy()
        adv_options[&#39;nics&#39;].append(_nic2)
        adv_options[&#39;nics&#39;][1][&#39;networkName&#39;] = &#39;New Network Interface&#39;
        _region = adv_options[&#39;esxHost&#39;]
        for disks in adv_options[&#39;disks&#39;]:
            disks[&#39;availabilityZone&#39;] = _region
        adv_options[&#39;guestOperatingSystemId&#39;] = _disk_restore_option.get(&#39;os_id&#39;, 0)
        return json_to_be_edited

    def _prepare_fullvm_restore_json(self, restore_option=None):
        &#34;&#34;&#34;
        Prepare Full VM restore Json with all getters

        Args:
            restore_option - dictionary with all VM restore options

        value:
            preserve_level              - set the preserve level in restore

            unconditional_overwrite     - unconditionally overwrite the disk
                                          in the restore path

            destination_path            - path where the disk needs to be
                                          restored

            client_name                 - client where the disk needs to be
                                          restored

            destination_vendor          - vendor id of the Hypervisor

            destination_disktype        - type of disk needs to be restored
                                          like VHDX,VHD,VMDK

            source_item                 - GUID of VM from which disk needs to
                                          be restored
                                          eg:
                                          \\5F9FA60C-0A89-4BD9-9D02-C5ACB42745EA

            copy_precedence_applicable  - True if needs copy_precedence to
                                          be honoured else False

            copy_precedence            - the copy id from which browse and
                                          restore needs to be performed

            power_on                    - power on the VM after restore

            add_to_failover             - Register the VM to Failover Cluster

            datastore                   - Datastore where the VM needs to be
                                          restored

            disks   (list of dict)      - list with dict for each disk in VM
                                            eg: [{
                                                    name:&#34;disk1.vmdk&#34;
                                                    datastore:&#34;local&#34;
                                                }
                                                {
                                                    name:&#34;disk2.vmdk&#34;
                                                    datastore:&#34;local1&#34;
                                                }
                                            ]
            guid                        - GUID of the VM needs to be restored
            new_name                    - New name for the VM to be restored
            esx_host                    - esx_host or client name where it need
                                          to be restored
            name                        - name of the VM to be restored

        returns:
              request_json        -complete json for perfomring Full VM Restore
                                   options

        &#34;&#34;&#34;

        if restore_option is None:
            restore_option = {}
        restore_option[&#39;paths&#39;] = []

        if &#34;destination_vendor&#34; not in restore_option:
            restore_option[&#34;destination_vendor&#34;] = \
                self._backupset_object._instance_object._vendor_id

        if restore_option[&#39;copy_precedence&#39;]:
            restore_option[&#39;copy_precedence_applicable&#39;] = True

        # set all the restore defaults
        self._set_restore_defaults(restore_option)

        # set the setters
        self._backupset_object._instance_object._restore_association = self._subClientEntity
        self._json_restore_virtualServerRstOption(restore_option)
        self._json_restore_diskLevelVMRestoreOption(restore_option)
        self._json_vcenter_instance(restore_option)

        for _each_vm_to_restore in restore_option[&#39;vm_to_restore&#39;]:
            if not restore_option[&#34;in_place&#34;]:
                if &#39;disk_type&#39; in restore_option and restore_option[&#39;disk_type&#39;]:
                    restore_option[&#39;restoreAsManagedVM&#39;] = restore_option[&#39;disk_type&#39;][
                        _each_vm_to_restore]
                if (&#34;restore_new_name&#34; in restore_option and
                        restore_option[&#34;restore_new_name&#34;] is not None):
                    if len(restore_option[&#39;vm_to_restore&#39;]) == 1:
                        restore_option[&#34;new_name&#34;] = restore_option[&#34;restore_new_name&#34;]
                    else:
                        restore_option[&#34;new_name&#34;] = restore_option[
                                                         &#34;restore_new_name&#34;] + _each_vm_to_restore
                else:
                    restore_option[&#34;new_name&#34;] = &#34;del&#34; + _each_vm_to_restore
            else:
                restore_option[&#34;new_name&#34;] = _each_vm_to_restore
            self.set_advanced_vm_restore_options(_each_vm_to_restore, restore_option)

        # prepare json
        request_json = self._restore_json(restore_option=restore_option)
        self._virtualserver_option_restore_json[&#34;diskLevelVMRestoreOption&#34;][
            &#34;advancedRestoreOptions&#34;] = self._advanced_restore_option_list
        self._advanced_restore_option_list = []
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;virtualServerRstOption&#34;] = self._virtualserver_option_restore_json
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;volumeRstOption&#34;] = self._json_restore_volumeRstOption(
            restore_option)
        if restore_option.get(&#39;v2_details&#39;) and len(restore_option.get(&#39;vm_to_restore&#39;, &#39;&#39;)) &lt;= 1:
            request_json = self._full_vm_restore_update_json_for_v2(request_json, restore_option.get(&#39;v2_details&#39;))

        return request_json

    @staticmethod
    def _full_vm_restore_update_json_for_v2(json_to_be_edited, v2_details):
        &#34;&#34;&#34;
        Update the final request JSON to match wth the v2 vm
        Args:
            json_to_be_edited               (dict): Final restore JSON for the restore without v2 subclient details

            v2_details                      (dict): v2 vm subclient details
                                   eg: {
                                            &#39;clientName&#39;: &#39;vm_client1&#39;,
                                            &#39;instanceName&#39;: &#39;VMInstance&#39;,
                                            &#39;displayName&#39;: &#39;vm_client1&#39;,
                                            &#39;backupsetId&#39;: 12,
                                            &#39;instanceId&#39;: 2,
                                            &#39;subclientId&#39;: 123,
                                            &#39;clientId&#39;: 1234,
                                            &#39;appName&#39;: &#39;Virtual Server&#39;,
                                            &#39;backupsetName&#39;: &#39;defaultBackupSet&#39;,
                                            &#39;applicationId&#39;: 106,
                                            &#39;subclientName&#39;: &#39;default&#39;
                                        }

        Returns:
            json_to_be_edited        -complete json for performing Full VM Restore
                                        options with v2 subclient details

        &#34;&#34;&#34;
        json_to_be_edited[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;clientName&#39;] = v2_details.get(&#39;clientName&#39;)
        json_to_be_edited[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;clientId&#39;] = v2_details.get(&#39;clientId&#39;)
        json_to_be_edited[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;instanceName&#39;] = v2_details.get(&#39;instanceName&#39;)
        json_to_be_edited[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;instanceId&#39;] = v2_details.get(&#39;instanceId&#39;)
        json_to_be_edited[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;displayName&#39;] = v2_details.get(&#39;displayName&#39;)
        json_to_be_edited[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;backupsetName&#39;] = v2_details.get(&#39;backupsetName&#39;)
        json_to_be_edited[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;backupsetId&#39;] = v2_details.get(&#39;backupsetId&#39;)
        json_to_be_edited[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;subclientName&#39;] = v2_details.get(&#39;subclientName&#39;)
        json_to_be_edited[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;subclientId&#39;] = v2_details.get(&#39;subclientId&#39;)
        json_to_be_edited[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;browseOption&#39;][&#39;backupset&#39;][
            &#39;clientName&#39;] = v2_details.get(&#39;clientName&#39;)
        del json_to_be_edited[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;subclientGUID&#39;]
        return json_to_be_edited

    def backup(self,
               backup_level=&#34;Incremental&#34;,
               incremental_backup=False,
               incremental_level=&#39;BEFORE_SYNTH&#39;,
               collect_metadata=False,
               advanced_options=None,
               schedule_pattern=None):
        &#34;&#34;&#34;Runs a backup job for the subclient of the level specified.

            Args:
                backup_level            (str)   --  level of backup the user wish to run
                                                    Full / Incremental / Differential /
                                                    Synthetic_full

                incremental_backup      (bool)  --  run incremental backup
                                                    only applicable in case of Synthetic_full backup

                incremental_level       (str)   --  run incremental backup before/after synthetic full
                                                    BEFORE_SYNTH / AFTER_SYNTH
                                                    only applicable in case of Synthetic_full backup

                collect_metadata        (bool)  --  Collect Meta data for the backup

                advanced_options       (dict)  --  advanced backup options to be included while
                                                    making the request
                    options:
                        create_backup_copy_immediately  --  Run Backup copy just after snap backup
                        backup_copy_type                --  Backup Copy level using storage policy
                                                            or subclient rule

                schedule_pattern (dict) -- scheduling options to be included for the task

                        Please refer schedules.schedulePattern.createSchedule()
                                                                    doc for the types of Jsons

            Returns:
                object - instance of the Job class for this backup job if its an immediate Job

                         instance of the Schedule class for the backup job if its a scheduled Job

            Raises:
                SDKException:
                    if backup level specified is not correct

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;

        backup_level = backup_level.lower()
        if backup_level not in [&#39;full&#39;, &#39;incremental&#39;,
                                &#39;differential&#39;, &#39;synthetic_full&#39;]:
            raise SDKException(&#39;Subclient&#39;, &#39;103&#39;)

        if advanced_options or schedule_pattern:
            request_json = self._backup_json(
                backup_level=backup_level,
                incremental_backup=incremental_backup,
                incremental_level=incremental_level,
                advanced_options=advanced_options,
                schedule_pattern=schedule_pattern
            )

            backup_service = self._commcell_object._services[&#39;CREATE_TASK&#39;]

            flag, response = self._commcell_object._cvpysdk_object.make_request(
                &#39;POST&#39;, backup_service, request_json
            )

            return self._process_backup_response(flag, response)

        else:
            return super(VirtualServerSubclient, self).backup(backup_level=backup_level,
                                                              incremental_backup=incremental_backup,
                                                              incremental_level=incremental_level,
                                                              collect_metadata=collect_metadata)

    def _advanced_backup_options(self, options):
        &#34;&#34;&#34;Generates the advanced backup options dict

            Args:
                options         (dict)  --  advanced backup options that are to be included
                                            in the request
                    create_backup_copy_immediately  --  Run Backup copy just after snap backup
                    backup_copy_type                --  Backup Copy level using storage policy
                                                        or subclient rule

            Returns:
            (dict)                      --  generated advanced options dict
        &#34;&#34;&#34;
        final_dict = super(VirtualServerSubclient, self)._advanced_backup_options(options)

        if &#39;create_backup_copy_immediately&#39; in options:
            final_dict[&#39;dataOpt&#39;] = {
                &#39;createBackupCopyImmediately&#39;: options.get(&#39;create_backup_copy_immediately&#39;, False),
                &#39;backupCopyType&#39;: options.get(&#39;backup_copy_type&#39;, &#39;USING_STORAGE_POLICY_RULE&#39;)
            }

        return final_dict

    def _prepare_blr_json(self, restore_option):

        if &#34;destination_vendor&#34; not in restore_option:
            restore_option[&#34;destination_vendor&#34;] = self._backupset_object._instance_object._vendor_id

        # set all the restore defaults
        self._set_restore_defaults(restore_option)

        # Restore Options
        self._backupset_object._instance_object._restore_association = self._subClientEntity

        self._json_restore_virtualServerRstOption(restore_option)

        # Virtual Server RST option
        self._allocation_policy_json(restore_option)
        self._json_restore_diskLevelVMRestoreOption(restore_option)
        self._json_vcenter_instance(restore_option)
        _vm_browse_path_nodes_json = list()
        # Disk Level  VM Restore Options
        self._json_restore_default_restore_settings(restore_option)
        new_name = restore_option[&#34;new_name&#34;]
        for _each_vm_to_restore in restore_option[&#39;vm_to_restore&#39;]:
            if restore_option[&#34;prefix&#34;] == 1:
                restore_option[&#34;new_name&#34;] = &#34;{}{}&#34;.format(new_name, _each_vm_to_restore)
            else:
                restore_option[&#34;new_name&#34;] = &#34;{}{}&#34;.format(_each_vm_to_restore, new_name)
            self._set_advanced_vm_restore_options_blr(_each_vm_to_restore, restore_option)
            _vm_browse_path_nodes_json.append(self._vm_browse_path_nodes())
        self._virtualserver_option_restore_json[&#34;diskLevelVMRestoreOption&#34;][&#34;advancedRestoreOptions&#34;] = self._advanced_restore_option_list[0]

        # prepare json
        request_json = self._restore_json(restore_option=restore_option)
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;vmBrowsePathNodes&#34;] = _vm_browse_path_nodes_json
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;virtualServerRstOption&#34;] = self._virtualserver_option_restore_json
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;volumeRstOption&#34;] = self._json_restore_volumeRstOption(
            restore_option)
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;subTask&#34;][&#34;operationType&#34;] = 1007
        request_json[&#34;taskInfo&#34;][&#34;associations&#34;][0][&#34;_type_&#34;] = 6

        self._advanced_restore_option_list = list()
        request_json[&#34;TMMsg_TaskInfo&#34;] = request_json[&#34;taskInfo&#34;]
        del request_json[&#34;taskInfo&#34;]
        return request_json

    def _vm_browse_path_nodes(self):
        return {
            &#34;browsePath&#34;: self._advanced_restore_option_list[-1][&#34;name&#34;],
            &#34;vmGUID&#34;: self._advanced_restore_option_list[-1][&#34;guid&#34;],
            &#34;esxHost&#34;: self._advanced_restore_option_list[-1][&#34;esxHost&#34;],
            &#34;datastore&#34;: self._advanced_restore_option_list[-1][&#34;Datastore&#34;],
            &#34;resourcePoolPath&#34;: self._advanced_restore_option_list[-1][&#34;resourcePoolPath&#34;],
            &#34;vmDataStore&#34;: self._advanced_restore_option_list[-1][&#34;Datastore&#34;],
            &#34;vmEsxHost&#34;: self._advanced_restore_option_list[-1][&#34;esxHost&#34;],
            &#34;vmeResourcePoolPath&#34;: self._advanced_restore_option_list[-1][&#34;resourcePoolPath&#34;],
            &#34;isMetadataAvaiable&#34;: &#34;0&#34;
        }

    def _json_restore_default_restore_settings(self, restore_option):
        &#34;&#34;&#34;setter for  the default restore settings in block level json&#34;&#34;&#34;

        if not isinstance(restore_option, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        self._virtualserver_option_restore_json[&#34;diskLevelVMRestoreOption&#34;][&#34;defaultRestoreSettings&#34;] = {
            &#34;esxHost&#34;: restore_option[&#34;esx_host&#34;],
            &#34;Datastore&#34;: restore_option.get(&#34;datastore&#34;, &#34;&#34;),
            &#34;resourcePoolPath&#34;: restore_option.get(&#34;resource_pool&#34;, &#34;/&#34;),
            &#34;blrRecoveryOpts&#34;: self._json_restore_blrRecoveryOpts(restore_option)
        }

    def _allocation_policy_json(self, restore_option):
        self._virtualserver_option_restore_json[&#34;allocationPolicy&#34;] = {
            &#34;flags&#34;: &#34;&#34;,
            &#34;instanceEntity&#34;: {
                &#34;flags&#34;: &#34;&#34;
            },
            &#34;policyType&#34;: &#34;0&#34;,
            &#34;region&#34;: {
                &#34;flags&#34;: &#34;&#34;
            },
            &#34;vmAllocPolicyId&#34;: restore_option[&#34;target_id&#34;],
            &#34;vmAllocPolicyName&#34;: restore_option[&#34;target_name&#34;]
        }

    def _prepare_blr_xml(self, restore_option):
        request_json = self._prepare_blr_json(restore_option)

        xml_string = xmltodict.unparse(request_json)
        plans = Plans(self._commcell_object)

        return (
            &#34;&#34;&#34;&lt;?xml version=&#34;1.0&#34; encoding=&#34;UTF-8&#34;?&gt;&lt;EVGui_SetVMBlockLevelReplicationReq subclientId=&#34;{5}&#34; opType=&#34;3&#34;&gt;
            &lt;blockLevelReplicationTaskXML&gt;&lt;![CDATA[{0}]]&gt;&lt;/blockLevelReplicationTaskXML&gt;
            &lt;subClientProperties&gt;
            &lt;subClientEntity clientId=&#34;{1}&#34; applicationId=&#34;106&#34; instanceId=&#34;{2}&#34; backupsetId=&#34;{3}&#34;/&gt;
            &lt;planEntity planId=&#34;{4}&#34;/&gt;
            &lt;/subClientProperties&gt;
            &lt;/EVGui_SetVMBlockLevelReplicationReq&gt;
        &#34;&#34;&#34;.format(
                xml_string,
                self._client_object.client_id,
                self._instance_object.instance_id,
                self._backupset_object.backupset_id,
                plans.all_plans[restore_option[&#34;plan_name&#34;].lower()],
                self._subclient_id))

    def _set_advanced_vm_restore_options_blr(self, vm_to_restore, restore_option):
        &#34;&#34;&#34;set the advanced restore options for all vm in restore

        vm_to_restore : Name of the VM to restore

        restore_option: restore options that need to be set for advanced restore option

        &#34;&#34;&#34;

        vm_names, vm_ids = self._get_vm_ids_and_names_dict()

        # populate restore source item
        restore_option[&#39;name&#39;] = vm_to_restore
        restore_option[&#39;guid&#39;] = vm_ids[vm_to_restore]
        restore_option[&#39;paths&#39;].append(&#34;\\&#34; + vm_ids[vm_to_restore])
        restore_option[&#34;resourcePoolPath&#34;] = &#34;/&#34;

        restore_option[&#34;nics&#34;] = {
            &#34;sourceNetwork&#34;: restore_option[&#34;source_network&#34;],
            &#34;destinationNetwork&#34;: restore_option[&#34;destination_network&#34;]
        }
        temp_dict = self._json_restore_advancedRestoreOptions(restore_option)
        self._advanced_restore_option_list.append(temp_dict)

    def _prepare_preview_json(self):
        &#34;&#34;&#34;Prepares the JSON for previewing subclient contents

        Returns:
            JSON - for previewing subclient contents

        &#34;&#34;&#34;
        return(
            {
                &#34;appId&#34;: {
                    &#34;subclientId&#34;: int(self.subclient_id),
                    &#34;clientId&#34;: int(self._client_object.client_id),
                    &#34;instanceId&#34;: int(self._instance_object.instance_id),
                    &#34;backupsetId&#34;: int(self._backupset_object.backupset_id),
                    &#34;apptypeId&#34;: int(self._agent_object.agent_id)
                },
                &#34;filterEntity&#34;: self._vmFilter,
                &#34;contentEntity&#34;: self._vmContent
            }
        )

    def _parse_preview_vms(self, subclient_vm_list):
        &#34;&#34;&#34;Parses the vm list from the preview vm response

        Returns:
            _vm_list        (list)  - List of the vms as the subclient content
        &#34;&#34;&#34;
        _vm_list = []
        for vm in subclient_vm_list:
            _vm_list.append(vm[&#39;name&#39;])
        return _vm_list

    def preview_content(self):
        &#34;&#34;&#34;
        Preview the subclient and get the content

        Returns:
            list       - List of the vms as the subclient content

        &#34;&#34;&#34;
        preview = self._commcell_object._services[&#39;PREVIEW&#39;]
        preview_json = self._prepare_preview_json()

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, preview, preview_json
        )
        if flag and &#39;scList&#39; in response.json():
            return self._parse_preview_vms(response.json()[&#39;scList&#39;])
        else:
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                self._update_response_(
                    response.text))</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.subclients.vssubclient.VirtualServerSubclient"><code class="flex name class">
<span>class <span class="ident">VirtualServerSubclient</span></span>
<span>(</span><span>backupset_object, subclient_name, subclient_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Derived class from Subclient Base class, representing a virtual server subclient,
and to perform operations on that subclient.</p>
<p>Initialize the Instance object for the given Virtual Server instance.</p>
<h2 id="args">Args</h2>
<p>backupset_object
(object)
&ndash;
instance of the backupset class</p>
<p>subclient_name
(str)
&ndash;
subclient name</p>
<p>subclient_id
(int)
&ndash;
subclient id</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/vssubclient.py#L168-L3130" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class VirtualServerSubclient(Subclient):
    &#34;&#34;&#34;Derived class from Subclient Base class, representing a virtual server subclient,
        and to perform operations on that subclient.&#34;&#34;&#34;

    def __new__(cls, backupset_object, subclient_name, subclient_id=None):
        &#34;&#34;&#34;Decides which instance object needs to be created&#34;&#34;&#34;

        instance_name = VsInstanceType.VSINSTANCE_TYPE[backupset_object._instance_object._vsinstancetype]

        try:
            subclient_module = import_module(&#34;cvpysdk.subclients.virtualserver.{}&#34;.format(instance_name))
        except ImportError:
            subclient_module = import_module(&#34;cvpysdk.subclients.virtualserver.null&#34;)

        classes = getmembers(subclient_module, lambda m: isclass(m) and not isabstract(m))

        for name, _class in classes:
            if issubclass(_class, VirtualServerSubclient) and _class.__module__.rsplit(&#34;.&#34;, 1)[-1] == instance_name:
                return object.__new__(_class)

    def __init__(self, backupset_object, subclient_name, subclient_id=None):
        &#34;&#34;&#34;Initialize the Instance object for the given Virtual Server instance.

            Args:
                backupset_object    (object)    --  instance of the backupset class

                subclient_name      (str)       --  subclient name

                subclient_id        (int)       --  subclient id

        &#34;&#34;&#34;
        super(VirtualServerSubclient, self).__init__(
            backupset_object, subclient_name, subclient_id
        )

        self.content_types = {
            &#39;1&#39;: &#39;Host&#39;,
            &#39;2&#39;: &#39;Resource Pool&#39;,
            &#39;4&#39;: &#39;Datacenter&#39;,
            &#39;9&#39;: &#39;Virtual Machine&#39;,
            &#39;16&#39;: &#39;UnprotectedVMs&#39;,
            &#39;17&#39;: &#39;Root&#39;,
            &#39;34&#39;: &#39;Tag&#39;,
            &#39;35&#39;: &#39;TagCategory&#39;
        }

        self.filter_types = {
            &#39;1&#39;: &#39;Datastore&#39;,
            &#39;2&#39;: &#39;Virtual Disk Name/Pattern&#39;,
            &#39;3&#39;: &#39;Virtual Device Node&#39;,
            &#39;4&#39;: &#39;Container&#39;,
            &#39;5&#39;: &#39;Disk Label&#39;,
            &#39;6&#39;: &#39;Disk Type&#39;,
            &#39;9&#39;: &#39;Disk Tag Name/Value&#39;,
            &#39;10&#39;:&#39;Repository&#39;
        }

        self._disk_option = {
            &#39;original&#39;: 0,
            &#39;thicklazyzero&#39;: 1,
            &#39;thin&#39;: 2,
            &#39;thickeagerzero&#39;: 3
        }

        self._transport_mode = {
            &#39;auto&#39;: 0,
            &#39;san&#39;: 1,
            &#39;hotadd&#39;: 2,
            &#39;nbd&#39;: 5,
            &#39;nbdssl&#39;: 4
        }

        self._vm_names_browse = []
        self._vm_ids_browse = {}
        self._advanced_restore_option_list = []
        self._live_sync = None

    class disk_pattern(Enum):
        &#34;&#34;&#34;
        stores the disk pattern of all hypervisors
        &#34;&#34;&#34;
        name = &#34;name&#34;
        datastore = &#34;Datastore&#34;
        new_name = &#34;newName&#34;

    @property
    def content(self):
        &#34;&#34;&#34;Gets the appropriate content from the Subclient relevant to the user.

            Returns:
                list - list of content associated with the subclient

        &#34;&#34;&#34;
        content = []
        subclient_content = self._vmContent

        if &#39;children&#39; in subclient_content:
            children = subclient_content[&#39;children&#39;]
            content = self._get_content_list(children)
        return content

    @property
    def subclient_proxy(self):
        &#34;&#34;&#34;
            Gets the List of proxies at the Subclient

            Returns:
                    list         (list) :    Proxies at the subclient
        &#34;&#34;&#34;
        return self._get_subclient_proxies()

    @property
    def instance_proxy(self):
        &#34;&#34;&#34;
        Gets the proxy at instance level

        Returns:
                string          (string) :      Proxy at instane
        &#34;&#34;&#34;
        return self._proxyClient.get(&#39;clientName&#39;, None)

    @property
    def vm_filter(self):
        &#34;&#34;&#34;Gets the appropriate filter from the Subclient relevant to the user.

            Returns:
                list - list of filter associated with the subclient
        &#34;&#34;&#34;
        vm_filter = []
        if self._vmFilter:
            subclient_filter = self._vmFilter
            if &#39;children&#39; in subclient_filter:
                children = subclient_filter[&#39;children&#39;]
                vm_filter = self._get_content_list(children)
        return vm_filter

    @property
    def vm_diskfilter(self):
        &#34;&#34;&#34;Gets the appropriate Diskfilter from the Subclient relevant to the user.

            Returns:
                list - list of Diskfilter associated with the subclient

        &#34;&#34;&#34;
        vm_diskfilter = []
        if self._vmDiskFilter is not None:
            subclient_diskfilter = self._vmDiskFilter

            if &#39;filters&#39; in subclient_diskfilter:
                filters = subclient_diskfilter[&#39;filters&#39;]

                for child in filters:
                    filter_type_id = str(child[&#39;filterType&#39;])
                    filter_type = self.filter_types[str(child[&#39;filterType&#39;])]
                    vm_id = child[&#39;vmGuid&#39;] if &#39;vmGuid&#39; in child else None
                    filter_name = child[&#39;filter&#39;]
                    value = child[&#39;value&#39;]

                    temp_dict = {
                        &#39;filter&#39;: filter_name,
                        &#39;filterType&#39;: filter_type,
                        &#39;vmGuid&#39;: vm_id,
                        &#39;filterTypeId&#39;: filter_type_id,
                        &#39;value&#39;:value
                    }

                    vm_diskfilter.append(temp_dict)
        else:
            vm_diskfilter = self._vmDiskFilter

        if len(vm_diskfilter) == 0:
            vm_diskfilter = None
        return vm_diskfilter

    @property
    def metadata(self):
        &#34;&#34;&#34;
            Get if collect files/metadata value for given subclient.
            Returns status as True/False (string)
            Default: False for subclient which doesnt have the property
        &#34;&#34;&#34;
        collectdetails = r&#39;collectFileDetails&#39;
        if collectdetails in self._vsaSubclientProp:
            vsasubclient_collect_details = self._vsaSubclientProp[collectdetails]
        else:
            vsasubclient_collect_details = False
        return vsasubclient_collect_details

    @content.setter
    def content(self, subclient_content):
        &#34;&#34;&#34;Creates the list of content JSON to pass to the API to add/update
           content of a Virtual Server Subclient.

            Args:
                subclient_content (list)  --  list of the content to add to the
                subclient list should contain name and type
                (like VSAObjects.VMName, VSAObjects.DATASTORE )
                example:[
                            {
                            &#39;type&#39; : VSAObjects.VMNotes,
                            &#39;display_name&#39; : &#39;removed&#39;,
                            }
                        ]

                for Advance user:
                        where we need to have multiple constraints for a single
                        rule.
                        list should contain minimum 2 parameters (name, type,
                        true/False for equalsOrNotEquals) for a single
                        constraint
                        for power on/off, we need to specify one more
                        parameter i.e., true -on, false -off(as state variable)
                        example:
                        subclient_content = [{&#39;allOrAnyChildren&#39;: True, &#39;content&#39;: [
                            {&#39;equal_value&#39;: True, &#39;allOrAnyChildren&#39;: True, &#39;display_name&#39;: &#39;*abc*&#39;, &#39;type&#39;: &#39;VMName&#39;},
                            {&#39;equal_value&#39;: False, &#39;allOrAnyChildren&#39;: True, &#39;display_name&#39;: &#39;xyz&#39;, &#39;type&#39;: &#39;VMName&#39;}]},
                                      {&#39;allOrAnyChildren&#39;: False, &#39;content&#39;: [
                                          {&#39;equal_value&#39;: True, &#39;allOrAnyChildren&#39;: True, &#39;display_name&#39;: &#39;*12*&#39;,
                                           &#39;type&#39;: &#39;VMName&#39;},
                                          {&#39;equal_value&#39;: True, &#39;allOrAnyChildren&#39;: True, &#39;display_name&#39;: &#39;*34*&#39;,
                                           &#39;type&#39;: &#39;VMName&#39;}]}]
                        subclient_content = [
                                                [
                                                    {
                                                    &#39;type&#39; : VSAObjects.VMName,
                                                    &#39;display_name&#39; : &#39;VM*&#39;
                                                    }
                                                ],
                                                [
                                                    {
                                                      &#39;type&#39; : VSAObjects.VMNotes,
                                                      &#39;display_name&#39; : &#39;removed&#39;,
                                                    },
                                                    {
                                                      &#39;type&#39; : VSAObjects.VMPowerState,
                                                      &#39;state&#39;: &#39;false&#39;,
                                                    }
                                                ]
                                            ]

            Returns:
                list - list of the appropriate JSON for an agent to send to the
                       POST Subclient API
        &#34;&#34;&#34;
        content = []
        try:
            for entity in subclient_content:
                virtual_server_dict = dict()
                if not isinstance(entity, dict):
                    entity = {&#39;content&#39;: entity}
                elif &#39;content&#39; not in entity:
                    entity = {&#39;content&#39;: entity}
                virtual_server_dict[&#39;allOrAnyChildren&#39;] = entity.get(&#39;allOrAnyChildren&#39;, True)
                virtual_server_dict[&#39;equalsOrNotEquals&#39;] = entity.get(&#39;equalsOrNotEquals&#39;, True)
                virtual_server_dict[&#39;children&#39;] = []

                def add_childrens(item, multiple_rule=False):
                    &#34;&#34;&#34;
                    add contents in the hierarchy
                    Args:
                        item                (dict)  :   content&#39;s current item to be added
                        multiple_rule       (bool)  :   If multiple rule present or not

                    &#34;&#34;&#34;
                    temp = {
                        &#39;allOrAnyChildren&#39;: item.get(&#39;allOrAnyChildren&#39;, True),
                        &#39;equalsOrNotEquals&#39;: item.get(&#39;equal_value&#39;, True),
                        &#39;name&#39;: item.get(&#39;name&#39;, item.get(&#39;id&#39;, &#39;&#39;)),
                        &#39;displayName&#39;: item.get(&#39;display_name&#39;, &#39;&#39;),
                        &#39;path&#39;: &#39;&#39;,
                        &#39;type&#39;: item[&#39;type&#39;] if (
                                isinstance(item[&#39;type&#39;], int) or isinstance(item[&#39;type&#39;], str)) else
                        item[&#39;type&#39;].value   
                    }
                    if item[&#39;type&#39;] == VSAObjects.VMNotes:
                        temp[&#39;value&#39;] = item[&#39;display_name&#39;]
                        temp[&#39;displayName&#39;] = item[&#39;display_name&#39;]
                        temp[&#39;name&#39;] = &#34;Notes&#34;
                    if (item[&#39;type&#39;] ==
                            VSAObjects.VMPowerState and
                            item[&#39;state&#39;] == &#39;true&#39;):
                        temp[&#39;name&#39;] = &#34;PoweredState&#34;
                        temp[&#39;value&#39;] = &#34;1&#34;
                        temp[&#39;displayName&#39;] = &#34;Powered On&#34;
                    if (item[&#39;type&#39;] ==
                            VSAObjects.VMPowerState and
                            item[&#39;state&#39;] == &#39;false&#39;):
                        temp[&#39;name&#39;] = &#34;PoweredState&#34;
                        temp[&#39;value&#39;] = &#34;0&#34;
                        temp[&#39;displayName&#39;] = &#34;Powered Off&#34;
                    if item[&#39;type&#39;] == VSAObjects.VMCustomAttribute.name:
                        temp[&#39;value&#39;] = item.get(&#39;value&#39;, &#39;&#39;)
                    if multiple_rule:
                        virtual_server_dict.get(&#39;children&#39;).append(temp)
                    else:
                        content.append(temp)
                if not isinstance(entity, list):
                    entity = [entity]
                if len(entity[0][&#39;content&#39;]) == 1 or isinstance(entity[0][&#39;content&#39;], dict):
                    if isinstance(entity[0][&#39;content&#39;], list):
                        add_childrens(entity[0][&#39;content&#39;][0])
                    else:
                        add_childrens(entity[0][&#39;content&#39;])
                else:
                    for items in entity:
                        for item in items[&#39;content&#39;]:
                            add_childrens(item, True)
                        content.append(virtual_server_dict)
        except KeyError as err:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;{} not given in content&#39;.format(err))

        vs_subclient_content = {
            &#34;children&#34;: content
        }

        self._set_subclient_properties(&#34;_vmContent&#34;, vs_subclient_content)

    @vm_filter.setter
    def vm_filter(self, subclient_filter):
        &#34;&#34;&#34;Creates the list of Filter JSON to pass to the API to update the
           VM_filter of a Virtual Server Subclient. i.e. it works in overwrite
           mode

            Args:
                subclient_filter (list)  --  list of the filter to add to the
                                             subclient

            Returns:
                list - list of the appropriate JSON for an agent to send to the
                       POST Subclient API
        &#34;&#34;&#34;
        vm_filter = []
        try:
            for entity in subclient_filter:
                virtual_server_dict = dict()
                if not isinstance(entity, dict):
                    entity = {&#39;content&#39;: entity}
                elif &#39;content&#39; not in entity:
                    entity = {&#39;content&#39;: entity}
                virtual_server_dict[&#39;allOrAnyChildren&#39;] = entity.get(&#39;allOrAnyChildren&#39;, True)
                virtual_server_dict[&#39;equalsOrNotEquals&#39;] = entity.get(&#39;equalsOrNotEquals&#39;, True)
                virtual_server_dict[&#39;children&#39;] = []

                def add_childrens(item, multiple_rule=False):
                    &#34;&#34;&#34;
                    add filters in the hierarchy
                    Args:
                        item                (dict)  :   content&#39;s filters item to be added
                        multiple_rule       (bool)  :   If multiple rule present or not

                    &#34;&#34;&#34;
                    temp = {
                        &#39;allOrAnyChildren&#39;: item.get(&#39;allOrAnyChildren&#39;, True),
                        &#39;equalsOrNotEquals&#39;: item.get(&#39;equal_value&#39;, True),
                        &#39;name&#39;: item.get(&#39;name&#39;, item.get(&#39;id&#39;, &#39;&#39;)),
                        &#39;displayName&#39;: item.get(&#39;display_name&#39;, &#39;&#39;),
                        &#39;path&#39;: &#39;&#39;,
                        &#39;type&#39;: item[&#39;type&#39;] if (
                                isinstance(item[&#39;type&#39;], int) or isinstance(item[&#39;type&#39;], str)) else
                        item[&#39;type&#39;].value   
                    }
                    if item[&#39;type&#39;] == VSAObjects.VMNotes:
                        temp[&#39;value&#39;] = item[&#39;display_name&#39;]
                        temp[&#39;displayName&#39;] = item[&#39;display_name&#39;]
                        temp[&#39;name&#39;] = &#34;Notes&#34;
                    if (item[&#39;type&#39;] ==
                            VSAObjects.VMPowerState and
                            item[&#39;state&#39;] == &#39;true&#39;):
                        temp[&#39;name&#39;] = &#34;PoweredState&#34;
                        temp[&#39;value&#39;] = &#34;1&#34;
                        temp[&#39;displayName&#39;] = &#34;Powered On&#34;
                    if (item[&#39;type&#39;] ==
                            VSAObjects.VMPowerState and
                            item[&#39;state&#39;] == &#39;false&#39;):
                        temp[&#39;name&#39;] = &#34;PoweredState&#34;
                        temp[&#39;value&#39;] = &#34;0&#34;
                        temp[&#39;displayName&#39;] = &#34;Powered Off&#34;
                    if item[&#39;type&#39;] == VSAObjects.VMCustomAttribute.name:
                        temp[&#39;value&#39;] = item.get(&#39;value&#39;, &#39;&#39;)
                    if multiple_rule:
                        virtual_server_dict.get(&#39;children&#39;).append(temp)
                    else:
                        vm_filter.append(temp)
                if not isinstance(entity, list):
                    entity = [entity]
                if len(entity[0][&#39;content&#39;]) == 1 or isinstance(entity[0][&#39;content&#39;], dict):
                    if isinstance(entity[0][&#39;content&#39;], list):
                        add_childrens(entity[0][&#39;content&#39;][0])
                    else:
                        add_childrens(entity[0][&#39;content&#39;])
                else:
                    for items in entity:
                        for item in items[&#39;content&#39;]:
                            add_childrens(item, True)
                        vm_filter.append(virtual_server_dict)            

        except KeyError as err:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;,
                               &#39;{} not given in content&#39;.format(err))

        vs_filter_content = {
            &#34;children&#34;: vm_filter
        }
        self._set_subclient_properties(&#34;_vmFilter&#34;, vs_filter_content)

    @vm_diskfilter.setter
    def vm_diskfilter(self, subclient_diskfilter):
        &#34;&#34;&#34;Creates the list of Disk Filter JSON to pass to the API to update
           the Disk_filter of a Virtual Server Subclient. i.e. it works in
           overwrite mode

            Args:
                subclient_diskfilter (list)  --  list of the Disk filter to add
                                                 to the subclient

            Returns:
                list - list of the appropriate JSON for an agent to send to
                       the POST Subclient API
        &#34;&#34;&#34;
        vm_diskfilter = []

        try:
            for temp_dict in subclient_diskfilter:
                if temp_dict.get(&#39;filterTypeId&#39;):
                    filter_type_id = temp_dict[&#39;filterTypeId&#39;]
                else:
                    filter_type_id = \
                        list(filter(lambda x: self.filter_types[x].lower() == temp_dict[&#39;filtertype&#39;].lower(),
                                    self.filter_types))[
                            0]

                virtual_server_dict = {
                    &#39;filter&#39;: temp_dict[&#39;filter&#39;],
                    &#39;filterType&#39;: filter_type_id,
                    &#39;vmGuid&#39;: temp_dict.get(&#39;vmGuid&#39;)
                }

                vm_diskfilter.append(virtual_server_dict)

        except KeyError as err:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;,
                               &#39;{} not given in content&#39;.format(err))

        vs_diskfilter_content = {
            &#34;filters&#34;: vm_diskfilter
        }
        self._set_subclient_properties(&#34;_vmDiskFilter&#34;, vs_diskfilter_content)

    @property
    def live_sync(self):
        &#34;&#34;&#34;Returns the instance of the VSALiveSync class&#34;&#34;&#34;
        if not self._live_sync:
            from .virtualserver.livesync.vsa_live_sync import VsaLiveSync
            self._live_sync = VsaLiveSync(self)

        return self._live_sync

    @property
    def index_server(self):
        &#34;&#34;&#34;Returns the index server client set for the subclient. None if no Index Server is set&#34;&#34;&#34;

        if &#39;indexSettings&#39; not in self._commonProperties:
            return None

        index_settings = self._commonProperties[&#39;indexSettings&#39;]
        index_server = None

        if (&#39;currentIndexServer&#39; in index_settings and
                &#39;clientName&#39; in index_settings[&#39;currentIndexServer&#39;]):
            index_server = index_settings[&#39;currentIndexServer&#39;][&#39;clientName&#39;]

        if index_server is None:
            return None

        return self._commcell_object.clients.get(index_server)

    @index_server.setter
    def index_server(self, value):
        &#34;&#34;&#34;Sets the index server client for the backupset

            Args:
                value   (object)    --  The index server client object to set

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;

        if not isinstance(value, Client):
            raise SDKException(&#39;Subclient&#39;, &#39;121&#39;)

        index_server_name = value.client_name

        self._set_subclient_properties(
            &#34;_commonProperties[&#39;indexSettings&#39;][&#39;currentIndexServer&#39;][&#39;clientName&#39;]&#34;,
            index_server_name)

    @property
    def quiesce_file_system(self):
        &#34;&#34;&#34;
            Gets the quiesce value set for the vsa subclient

        Returns:
            (Boolean)    True/False
        &#34;&#34;&#34;
        return self._vsaSubclientProp.get(&#39;quiesceGuestFileSystemAndApplications&#39;)

    @quiesce_file_system.setter
    def quiesce_file_system(self, value):
        &#34;&#34;&#34;
        Sets the quiesce value for the vsa subclient

        Args:
            value   (Boolean)   True/False

        &#34;&#34;&#34;
        update_properties = self.properties
        update_properties[&#39;vsaSubclientProp&#39;][&#39;quiesceGuestFileSystemAndApplications&#39;] = value
        self.update_properties(update_properties)

    @property
    def snapshot_storage_type(self):
        &#34;&#34;&#34;
            Gets the snapshot storage type set for the vsa subclient

        Returns:
            (Boolean)    True/False
        &#34;&#34;&#34;
        return self._vsaSubclientProp.get(&#39;snapshotStorageType&#39;)

    def _get_disk_provisioning_value(self, provisioningType):
        &#34;&#34;&#34;
         Returns the provisioning code for the selected type

        Args:
                provisioningType  (String) - Disk provisioning type

        return: (int) - diskProvisionValue

        &#34;&#34;&#34;
        # Defaults to &#34;original&#34;
        disk_provision_value = 0
        provisioningType = provisioningType.replace(&#34; &#34;, &#34;&#34;).lower()
        if provisioningType in self._disk_option:
            disk_provision_value = self._disk_option[provisioningType]
        return disk_provision_value

    @metadata.setter
    def metadata(self, value=True):
        &#34;&#34;&#34;
        Set given value of collectFileDetails/metadata (True/false) on the subclient

        Args:
                value   (str)    True/False

        &#34;&#34;&#34;
        collectdetails = r&#39;collectFileDetails&#39;
        if collectdetails in self._vsaSubclientProp:
            self._set_subclient_properties(&#34;_vsaSubclientProp[&#39;collectFileDetails&#39;]&#34;, value)

    @property
    def cbtvalue(self):
        &#34;&#34;&#34;
        Get CBT value for given subclient.

        Returns:
            (Boolean)    True/False

        &#34;&#34;&#34;
        return self._subclient_properties.get(&#39;vsaSubclientProp&#39;, {}).get(&#34;useChangedTrackingOnVM&#34;, False)


    @cbtvalue.setter
    def cbtvalue(self, value):
        &#34;&#34;&#34;
        Set CBT value for given subclient

        Args:
            value   (Boolean)   True/False

        &#34;&#34;&#34;
        update_properties = self.properties
        update_properties[&#34;vsaSubclientProp&#34;][&#39;useChangedTrackingOnVM&#39;] = value
        self.update_properties(update_properties)

    def update_properties(self, properties_dict):
        &#34;&#34;&#34;
        child method to add any specific attributes for vsa
        Args:
            properties_dict         (dict):     dict of all propterties of subclient
        &#34;&#34;&#34;
        properties_dict.update({
            &#34;vmFilterOperationType&#34;: &#34;OVERWRITE&#34;,
            &#34;vmContentOperationType&#34;: &#34;OVERWRITE&#34;,
            &#34;vmDiskFilterOperationType&#34;: &#34;OVERWRITE&#34;
        })
        super().update_properties(properties_dict)

    def _get_content_list(self, children):
        &#34;&#34;&#34;
        Gets the content in list format
        Args:
            children                            (list):     Content if the subclient

        Returns:
            content_list                        (list):     Content of the subclient
        &#34;&#34;&#34;

        content_list = []
        for child in children:
            path = child[&#39;path&#39;] if &#39;path&#39; in child else &#39;&#39;
            allOrAnyChildren = child[&#39;allOrAnyChildren&#39;] if &#39;allOrAnyChildren&#39; in child else None
            equalsOrNotEquals = child[&#39;equalsOrNotEquals&#39;] if &#39;equalsOrNotEquals&#39; in child else None
            _temp_list = []
            _temp_dict = {}
            if &#39;children&#39; in child:
                nested_children = child[&#39;children&#39;]
                for each_condition in nested_children:
                    display_name = each_condition[&#39;displayName&#39;]
                    content_type = VSAObjects(each_condition[&#39;type&#39;]).name if isinstance(each_condition[&#39;type&#39;],
                                                                                         int) else each_condition[
                        &#39;type&#39;]
                    vm_id = &#39;&#39; if each_condition.get(&#39;name&#39;, &#39;&#39;) in display_name else each_condition.get(&#39;name&#39;, &#39;&#39;)
                    temp_dict = {
                        &#39;equal_value&#39;: each_condition.get(&#39;equalsOrNotEquals&#39;, True),
                        &#39;allOrAnyChildren&#39;: each_condition.get(&#39;allOrAnyChildren&#39;, True),
                        &#39;display_name&#39;: display_name,
                        &#39;type&#39;: content_type
                    }
                    if content_type != &#39;VMCustomAttribute&#39;:
                        temp_dict.update({&#39;id&#39;: vm_id, &#39;path&#39;: path})
                    else:
                        temp_dict.update({&#39;name&#39;: vm_id, &#39;value&#39;: each_condition[&#39;value&#39;]})
                    _temp_list.append(temp_dict)
                _temp_dict[&#39;allOrAnyChildren&#39;] = allOrAnyChildren
                _temp_dict[&#39;equalsOrNotEquals&#39;] = equalsOrNotEquals
                _temp_dict[&#39;content&#39;] = _temp_list
                content_list.append(_temp_dict)
            else:
                display_name = child[&#39;displayName&#39;]
                content_type = VSAObjects(child[&#39;type&#39;]).name if isinstance(child[&#39;type&#39;], int) else child[&#39;type&#39;]
                vm_id = child.get(&#39;name&#39;, &#39;&#39;)
                temp_dict = {
                        &#39;equal_value&#39;: child[&#39;equalsOrNotEquals&#39;],
                        &#39;allOrAnyChildren&#39;: child.get(&#39;allOrAnyChildren&#39;, True),
                        &#39;display_name&#39;: display_name,
                        &#39;type&#39;: content_type
                    }
                if content_type != &#39;VMCustomAttribute&#39;:
                    temp_dict.update({&#39;id&#39;: vm_id, &#39;path&#39;: path})
                else:
                    temp_dict.update({&#39;name&#39;: vm_id, &#39;value&#39;: child[&#39;value&#39;]})
                content_list.append(temp_dict)
        return content_list

    def _get_subclient_properties(self):
        &#34;&#34;&#34;Gets the subclient  related properties of Virtual server subclient.

        &#34;&#34;&#34;

        self._vmDiskFilter = None
        self._vmFilter = None
        super(VirtualServerSubclient, self)._get_subclient_properties()

        if &#39;vmContent&#39; in self._subclient_properties:
            self._vmContent = self._subclient_properties[&#39;vmContent&#39;]
        if &#39;vmDiskFilter&#39; in self._subclient_properties:
            self._vmDiskFilter = self._subclient_properties[&#39;vmDiskFilter&#39;]
        if &#39;vmFilter&#39; in self._subclient_properties:
            self._vmFilter = self._subclient_properties[&#39;vmFilter&#39;]
        if &#39;vmBackupInfo&#39; in self._subclient_properties:
            self._vmBackupInfo = self._subclient_properties[&#39;vmBackupInfo&#39;]
        if &#39;vsaSubclientProp&#39; in self._subclient_properties:
            self._vsaSubclientProp = self._subclient_properties[&#39;vsaSubclientProp&#39;]

    def _get_subclient_content_(self):
        &#34;&#34;&#34;
        Returns the subclient content from property. Base class Abstract method
        implementation

        return:
            VM content  (dict)  -- Dictionary of VM Content with all details

        &#34;&#34;&#34;
        return self.content

    def _get_subclient_properties_json(self):
        &#34;&#34;&#34;get the all subclient related properties of this subclient.

           Returns:
                dict - all subclient properties put inside a dict

        &#34;&#34;&#34;
        subclient_json = {
            &#34;subClientProperties&#34;:
                {
                    &#34;vmContent&#34;: self._vmContent,
                    &#34;proxyClient&#34;: self._proxyClient,
                    &#34;subClientEntity&#34;: self._subClientEntity,
                    &#34;vmDiskFilter&#34;: self._vmDiskFilter,
                    &#34;vmFilter&#34;: self._vmFilter,
                    &#34;vmBackupInfo&#34;: self._vmBackupInfo,
                    &#34;vsaSubclientProp&#34;: self._vsaSubclientProp,
                    # &#34;content&#34;: self._content,
                    &#34;commonProperties&#34;: self._commonProperties,
                    &#34;vmContentOperationType&#34;: 1,
                    &#34;vmDiskFilterOperationType&#34;: 1,
                    &#34;vmFilterOperationType&#34;: 1
                }
        }
        return subclient_json

    def _disk_dict_pattern(self, name, datastore, new_name=None):
        &#34;&#34;&#34;
        set the disk dictionary of the hypervisor

        Args:
                name            (str)       --  name of the disk

                datastore       (str)       --  datastore where the disk has to be restored

                new_name        (str)       --  new name of the disk

            Returns:

                disk dictionary(dict)       -- Dictionary with key name, new name , datastore
                                                and corresponding
        &#34;&#34;&#34;

        if not new_name and not self._instance_object.instance_name == HypervisorType.GOOGLE_CLOUD.value.lower():
            new_name = name
        temp_disk_dict = {}
        temp_disk_dict[self.disk_pattern.name.value] = name
        temp_disk_dict[self.disk_pattern.datastore.value] = datastore
        temp_disk_dict[self.disk_pattern.new_name.value] = new_name
        return temp_disk_dict

    def _json_vcenter_instance(self, value):
        &#34;&#34;&#34; Setter for vcenter_instance JSON &#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        self._virtualserver_option_restore_json[&#34;vCenterInstance&#34;] = {
            &#34;clientName&#34;: value.get(&#34;destination_client_name&#34;, &#34;&#34;),
            &#34;instanceName&#34;: value.get(&#34;destination_instance&#34;, &#34;&#34;),
            &#34;appName&#34;: value.get(&#34;appName&#34;, &#34;Virtual Server&#34;)
        }

        if value.get(&#34;destination_instance_id&#34;) and value.get(&#34;destination_client_id&#34;):
            self._virtualserver_option_restore_json[&#34;vCenterInstance&#34;].update(
                {&#34;instanceId&#34;: value.get(&#34;destination_instance_id&#34;, 0),
                 &#34;clientId&#34;: value.get(&#34;destination_client_id&#34;, 0)}
            )

    def _json_restore_virtualServerRstOption(self, value):
        &#34;&#34;&#34;
            setter for  the Virtual server restore  option in restore json
        &#34;&#34;&#34;
        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        self._virtualserver_option_restore_json = {
            &#34;isDiskBrowse&#34;: value.get(&#34;disk_browse&#34;, True),
            &#34;isFileBrowse&#34;: value.get(&#34;file_browse&#34;, False),
            &#34;isVolumeBrowse&#34;: False,
            &#34;isVirtualLab&#34;: value.get(&#34;virtual_lab&#34;, False),
            &#34;esxServer&#34;: value.get(&#34;esx_server&#34;, &#34;&#34;),
            &#34;isAttachToNewVM&#34;: value.get(&#34;attach_to_new_vm&#34;, False),
            &#34;viewType&#34;: &#34;DEFAULT&#34;,
            &#34;isBlockLevelReplication&#34;: value.get(&#34;block_level&#34;, False)
        }

        if value.get(&#39;run_security_scan&#39;):
            self._virtualserver_option_restore_json[&#39;securityScanOptions&#39;] = {
                &#34;runSecurityScan&#34;: value.get(&#34;run_security_scan&#34;, False)
            }

        if value.get(&#39;replication_guid&#39;):
            self._virtualserver_option_restore_json[&#39;replicationGuid&#39;] = value[&#39;replication_guid&#39;]

    def _json_restore_virtualServerRstOption_filelevelrestoreoption(self, value):
        &#34;&#34;&#34;
            setter for  the File level restore option for agent less restore option in restore json
        &#34;&#34;&#34;
        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        return {
            &#34;serverName&#34;: value.get(&#34;server_name&#34;, &#39;&#39;),
            &#34;vmGuid&#34;: value.get(&#34;vm_guid&#34;, &#39;&#39;),
            &#34;vmName&#34;: value.get(&#34;vm_name&#34;, &#39;&#39;)
        }

    def _json_restore_guest_password(self, value):
        &#34;&#34;&#34;
            setter for vm credentials for agentless restore option in restore json
        &#34;&#34;&#34;
        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        return {
            &#34;userName&#34;: value.get(&#34;user_name&#34;, &#39;&#39;),
            &#34;password&#34;: value.get(&#34;password&#34;, &#39;&#39;)
        }

    def _json_nics_advancedRestoreOptions(self, vm_to_restore, value):
        &#34;&#34;&#34;
            Setter for nics list for advanced restore option json
        &#34;&#34;&#34;

        nics_dict_from_browse = self.get_nics_from_browse(copy_precedence=value.get(&#39;copy_precedence&#39;, 0))
        nics_list = []
        vm_nics_list = nics_dict_from_browse[vm_to_restore]
        for network_card_dict in vm_nics_list:
            if self._instance_object.instance_name == HypervisorType.GOOGLE_CLOUD.value.lower():
                current_project = network_card_dict.get(&#39;subnetId&#39;).split(&#39;/&#39;)[6]
                if value.get(&#39;project_id&#39;) is not None:
                    network_card_dict[&#39;subnetId&#39;] = value.get(&#39;subnetwork_nic&#39;)
                    network_card_dict[&#39;sourceNetwork&#39;] = value.get(&#39;networks_nic&#39;)
                    network_card_dict[&#39;publicIPaddress&#39;] = value.get(&#39;publicIPaddress&#39;)
                    network_card_dict[&#39;privateIPaddress&#39;] = value.get(&#39;privateIPaddress&#39;)

            _destnetwork = value.get(&#34;destination_network&#34;,
                                     value.get(&#39;network&#39;,
                                               network_card_dict[&#39;name&#39;]))

            nics = {
                &#34;subnetId&#34;: network_card_dict.get(&#39;subnetId&#39;, &#34;&#34;),
                &#34;sourceNetwork&#34;: network_card_dict[&#39;name&#39;],
                &#34;sourceNetworkId&#34;: network_card_dict.get(&#39;sourceNetwork&#39;, &#34;&#34;),
                &#34;name&#34;: (network_card_dict.get(&#39;sourceNetwork&#39;,
                                               &#34;&#34;) + _destnetwork) if self._instance_object.instance_name ==
                                                                      HypervisorType.GOOGLE_CLOUD.value.lower() and _destnetwork else
                network_card_dict[&#39;label&#39;],
                &#34;publicIPaddress&#34;: network_card_dict.get(&#34;publicIPaddress&#34;,&#34;&#34;),
                &#34;privateIPaddress&#34;: network_card_dict.get(&#34;privateIPaddress&#34;,&#34;&#34;),
                &#34;networkName&#34;: _destnetwork if _destnetwork else &#39;&#39;,
                &#34;destinationNetwork&#34;: _destnetwork if _destnetwork else network_card_dict[&#39;name&#39;]
            }

            # setting nics for azureRM instance
            if value.get(&#39;destination_instance&#39;).lower() == HypervisorType.AZURE_V2.value.lower():
                if value.get(&#39;subnet_id&#39;):
                    nics[&#34;subnetId&#34;] = value.get(&#39;subnet_id&#39;)
                    nics[&#34;networkName&#34;] = value.get(&#39;subnet_id&#39;).split(&#39;/&#39;)[0]
                    nics[&#34;networkDisplayName&#34;] = nics[&#34;networkName&#34;] + &#39;\\&#39; + value.get(&#39;subnet_id&#39;).split(&#39;/&#39;)[-1]
                elif &#34;networkDisplayName&#34; in value and &#39;networkrsg&#39; in value and &#39;destsubid&#39; in value:
                    nics[&#34;networkDisplayName&#34;] = value[&#34;networkDisplayName&#34;]
                    nics[&#34;networkName&#34;] = value[&#34;networkDisplayName&#34;].split(&#39;\\&#39;)[0]
                    modify_nics = value.get(&#39;subnetId&#39;, nics[&#39;subnetId&#39;]).split(&#39;/&#39;)
                    modify_nics[8] = nics[&#34;networkName&#34;]
                    modify_nics[4] = value[&#39;networkrsg&#39;]
                    modify_nics[2] = value[&#39;destsubid&#39;]
                    modify_nics[10] = value[&#34;networkDisplayName&#34;].split(&#39;\\&#39;)[1]
                    final_nics = &#34;&#34;
                    for each_info in modify_nics[1:]:
                        final_nics = final_nics + &#39;/&#39; + each_info
                    nics[&#34;subnetId&#34;] = final_nics
                    name = &#39;&#39;
                    for each_info in modify_nics[1:9]:
                        name = name + &#39;/&#39; + each_info
                    nics[&#34;name&#34;] = name

            nics_list.append(nics)

        return nics_list

    def _json_vmip_advanced_restore_options(self, value):
        &#34;&#34;&#34;
            Setting IP for destination vm
        &#34;&#34;&#34;
        vmip = []
        _asterisk = &#34;*.*.*.*&#34;
        vm_ip = {
            &#34;sourceIP&#34;: value.get(&#34;source_ip&#34;),
            &#34;sourceSubnet&#34;: value[&#34;source_subnet&#34;] if value.get(&#34;source_subnet&#34;) else _asterisk,
            &#34;sourceGateway&#34;: value[&#34;source_gateway&#34;] if value.get(&#34;source_gateway&#34;) else _asterisk,
            &#34;destinationIP&#34;: value.get(&#34;destination_ip&#34;),
            &#34;destinationSubnet&#34;: value[&#34;destination_subnet&#34;] if value.get(&#34;destination_subnet&#34;) else _asterisk,
            &#34;destinationGateway&#34;: value[&#34;destination_gateway&#34;] if value.get(&#34;destination_gateway&#34;) else _asterisk,
            &#34;primaryDNS&#34;: value.get(&#34;primary_dns&#34;, &#34;&#34;),
            &#34;alternateDNS&#34;: value.get(&#34;alternate_dns&#34;, &#34;&#34;),
            &#34;primaryWins&#34;: value.get(&#34;primare_wins&#34;, &#34;&#34;),
            &#34;altenameWins&#34;: value.get(&#34;alternate_wins&#34;, &#34;&#34;),
            &#34;useDhcp&#34;: False
        }
        vmip.append(vm_ip)
        return vmip

    def _json_restore_diskLevelVMRestoreOption(self, value):
        &#34;&#34;&#34;setter for  the disk Level VM Restore Option    in restore json&#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)
        vcenter_userpwd = &#39;&#39;
        if &#39;vmware&#39; in self._instance_object.instance_name:
            vcenter_userpwd = self._instance_object._user_name

        json_disklevel_option_restore = {
            &#34;esxServerName&#34;: value.get(&#34;esx_server&#34;, &#34;&#34;),
            &#34;vmFolderName&#34;: value.get(&#34;vm_folder&#34;, &#34;&#34;),
            &#34;dataCenterName&#34;: value.get(&#34;data_center&#34;, &#34;&#34;),
            &#34;hostOrCluster&#34;: value.get(&#34;host_cluster&#34;, &#34;&#34;),
            &#34;diskOption&#34;: value.get(&#34;disk_option&#34;, 0),
            &#34;vmName&#34;: &#34;&#34;,
            &#34;transportMode&#34;: value.get(&#34;transport_mode&#34;, 0),
            &#34;passUnconditionalOverride&#34;: value.get(&#34;unconditional_overwrite&#34;, False),
            &#34;powerOnVmAfterRestore&#34;: value.get(&#34;power_on&#34;, False),
            &#34;registerWithFailoverCluster&#34;: value.get(&#34;add_to_failover&#34;, False),
            &#34;userPassword&#34;: {&#34;userName&#34;: vcenter_userpwd or &#34;admin&#34;},
            &#34;redirectWritesToDatastore&#34;: value.get(&#34;redirectWritesToDatastore&#34;) or &#34;&#34;,
            &#34;delayMigrationMinutes&#34;: value.get(&#34;delayMigrationMinutes&#34;) or 0
        }
        if value[&#39;in_place&#39;]:
            json_disklevel_option_restore[&#34;dataStore&#34;] = {}
        if value.get(&#39;distribute_vm_workload&#39;):
            json_disklevel_option_restore[&#34;maxNumOfVMPerJob&#34;] = value[&#39;distribute_vm_workload&#39;]

        self._virtualserver_option_restore_json[&#34;diskLevelVMRestoreOption&#34;] = json_disklevel_option_restore

    def _json_restore_attach_diskLevelVMRestoreOption(self, value):
        &#34;&#34;&#34;setter for the attach disk Level VM Restore Option in restore json&#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        json_disklevel_option_restore = {
            &#34;esxServerName&#34;: value.get(&#34;esxHost&#34;, &#34;&#34;),
            &#34;diskOption&#34;: value.get(&#34;disk_option&#34;, 0),
            &#34;passUnconditionalOverride&#34;: value.get(&#34;unconditional_overwrite&#34;, False),
            &#34;powerOnVmAfterRestore&#34;: value.get(&#34;power_on&#34;, False),
            &#34;transportMode&#34;: value.get(&#34;transport_mode&#34;, 0),
            &#34;userPassword&#34;: {&#34;userName&#34;: value.get(&#34;userName&#34;,&#34;&#34;),&#34;password&#34;: value.get(&#34;password&#34;,&#34;&#34;)}
        }
        self._virtualserver_option_restore_json[&#34;diskLevelVMRestoreOption&#34;] = json_disklevel_option_restore

    def _json_restore_advancedRestoreOptions(self, value):
        &#34;&#34;&#34;setter for the Virtual server restore  option in restore json&#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        self._advanced_option_restore_json = {
            &#34;disks&#34;: value.get(&#34;disks&#34;, []),
            &#34;guid&#34;: value.get(&#34;guid&#34;, &#34;&#34;),
            &#34;newGuid&#34;: value.get(&#34;new_guid&#34;, &#34;&#34;),
            &#34;newName&#34;: value.get(&#34;new_name&#34;, &#34;&#34;),
            &#34;esxHost&#34;: value.get(&#34;esx_host&#34;, &#34;&#34;),
            &#34;projectId&#34;: value.get(&#34;project_id&#34;, &#34;&#34;),
            &#34;cluster&#34;: value.get(&#34;cluster&#34;, &#34;&#34;),
            &#34;name&#34;: value.get(&#34;name&#34;, &#34;&#34;),
            &#34;nics&#34;: value.get(&#34;nics&#34;, []),
            &#34;vmTags&#34;:  value.get(&#39;vmTags&#39;) or [],
            &#34;vmIPAddressOptions&#34;: value.get(&#34;vm_ip_address_options&#34;, []),
            &#34;FolderPath&#34;: value.get(&#34;FolderPath&#34;, &#34;&#34;),
            &#34;resourcePoolPath&#34;: value.get(&#34;ResourcePool&#34;, &#34;&#34;),
            &#34;restoreVMTags&#34;: False if value.get(&#39;vmTags&#39;) else True,
            &#34;volumeType&#34;: value.get(&#34;volumeType&#34;, &#34;Auto&#34;),
            &#34;vmCustomMetadata&#34;: value.get(&#34;vmCustomMetadata&#34;,[])
        }

        value_dict = {
            &#34;createPublicIP&#34;: [&#34;createPublicIP&#34;, [&#34;createPublicIP&#34;, &#34;&#34;]],
            &#34;restoreAsManagedVM&#34;: [&#34;restoreAsManagedVM&#34;, [&#34;restoreAsManagedVM&#34;, &#34;&#34;]],
            &#34;destination_os_name&#34;: [&#34;osName&#34;, [&#34;destination_os_name&#34;, &#34;AUTO&#34;]],
            &#34;resourcePoolPath&#34;: [&#34;resourcePoolPath&#34;, [&#34;resourcePoolPath&#34;, &#34;&#34;]],
            &#34;datacenter&#34;: [&#34;datacenter&#34;, [&#34;datacenter&#34;, &#34;&#34;]],
            &#34;terminationProtected&#34;: [&#34;terminationProtected&#34;, [&#34;terminationProtected&#34;, False]],
            &#34;securityGroups&#34;: [&#34;securityGroups&#34;, [&#34;securityGroups&#34;, &#34;&#34;]],
            &#34;keyPairList&#34;: [&#34;keyPairList&#34;, [&#34;keyPairList&#34;, &#34;&#34;]]
        }

        for key in value_dict:
            if key in value:
                inner_key = value_dict[key][0]
                val1, val2 = value_dict[key][1][0], value_dict[key][1][1]
                self._advanced_option_restore_json[inner_key] = value.get(val1, val2)

        if &#34;vmSize&#34; in value:
            val1, val2 = (&#34;instanceSize&#34;, &#34;&#34;) if not value[&#34;vmSize&#34;] else (&#34;vmSize&#34;, &#34;vmSize&#34;)
            self._advanced_option_restore_json[&#34;vmSize&#34;] = value.get(val1, val2)
        if &#34;ami&#34; in value and value[&#34;ami&#34;] is not None:
            self._advanced_option_restore_json[&#34;templateId&#34;] = value[&#34;ami&#34;][&#34;templateId&#34;]
            if value.get(&#39;ami&#39;, {}).get(&#39;templateName&#39;):
                self._advanced_option_restore_json[&#34;templateName&#34;] = value[&#34;ami&#34;][&#34;templateName&#34;]
        if &#34;iamRole&#34; in value and value[&#34;iamRole&#34;] is not None:
            self._advanced_option_restore_json[&#34;roleInfo&#34;] = {
                &#34;name&#34;: value[&#34;iamRole&#34;]
            }
        if value.get(&#34;serviceAccount&#34;, {}).get(&#34;email&#34;):
            self._advanced_option_restore_json[&#34;roleInfo&#34;] = {
                &#34;email&#34;: value.get(&#34;serviceAccount&#34;).get(&#34;email&#34;),
                &#34;name&#34;: value.get(&#34;serviceAccount&#34;).get(&#34;displayName&#34;),
                &#34;id&#34;: value.get(&#34;serviceAccount&#34;).get(&#34;uniqueId&#34;)
            }
        if self._instance_object.instance_name == &#39;openstack&#39;:
            if &#34;securityGroups&#34; in value and value[&#34;securityGroups&#34;] is not None:
                self._advanced_option_restore_json[&#34;securityGroups&#34;] = [{&#34;groupName&#34;: value[&#34;securityGroups&#34;]}]
        if &#34;destComputerName&#34; in value and value[&#34;destComputerName&#34;] is not None:
            self._advanced_option_restore_json[&#34;destComputerName&#34;] = value[&#34;destComputerName&#34;]
        if &#34;destComputerUserName&#34; in value and value[&#34;destComputerUserName&#34;] is not None:
            self._advanced_option_restore_json[&#34;destComputerUserName&#34;] = value[&#34;destComputerUserName&#34;]
        if &#34;instanceAdminPassword&#34; in value and value[&#34;instanceAdminPassword&#34;] is not None:
            self._advanced_option_restore_json[&#34;instanceAdminPassword&#34;] = value[&#34;instanceAdminPassword&#34;]

        if self.disk_pattern.datastore.value == &#34;DestinationPath&#34;:
            self._advanced_option_restore_json[&#34;DestinationPath&#34;] = value.get(&#34;datastore&#34;, &#34;&#34;)

        else:
            self._advanced_option_restore_json[&#34;Datastore&#34;] = value.get(&#34;datastore&#34;, &#34;&#34;)

        if value.get(&#39;block_level&#39;):
            self._advanced_option_restore_json[&#34;blrRecoveryOpts&#34;] = \
                self._json_restore_blrRecoveryOpts(value)

        temp_dict = copy.deepcopy(self._advanced_option_restore_json)
        return temp_dict

    def _json_restore_blrRecoveryOpts(self, value):
        &#34;&#34;&#34; setter for blr recovery options in block level json&#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        return {
            &#34;recoveryType&#34;: value.get(&#34;recovery_type&#34;, 1),
            &#34;granularV2&#34;: {
                &#34;ccrpInterval&#34;: value.get(&#34;ccrp_interval&#34;, 300),
                &#34;acrpInterval&#34;: value.get(&#34;acrp_interval&#34;, 0),
                &#34;maxRpInterval&#34;: value.get(&#34;max_RpInterval&#34;, 21600),
                &#34;rpMergeDelay&#34;: value.get(&#34;rp_merge_delay&#34;, 172800),
                &#34;rpRetention&#34;: value.get(&#34;rp_retention&#34;, 604800),
                &#34;maxRpStoreOfflineTime&#34;: value.get(&#34;max_RpStore_OfflineTime&#34;, 0),
                &#34;useOffPeakSchedule&#34;: value.get(&#34;use_OffPeak_Schedule&#34;, 0),
                &#34;rpStoreId&#34;: value.get(&#34;rpstore_id&#34;, &#34;&#34;),
                &#34;rpStoreName&#34;: value.get(&#34;rpstore_name&#34;, &#34;&#34;)
            }
        }

    def _json_restore_volumeRstOption(self, value):
        &#34;&#34;&#34;setter for  the Volume restore option for in restore json&#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        return{
            &#34;destinationVendor&#34;: value.get(&#34;destination_vendor&#34;, 0),
            &#34;volumeLeveRestore&#34;: False,
            &#34;volumeLevelRestoreType&#34;: value.get(&#34;volume_level_restore&#34;, 0),
            &#34;destinationDiskType&#34;: value.get(&#34;destination_disktype&#34;, 0)
        }

    def _get_vm_ids_and_names_dict(self):
        &#34;&#34;&#34;Parses through the subclient content and creates 2 dictionaries.

            Returns:
                dict    -   dictionary consisting of VM ID as Key and VM
                            Display Name as value

                dict    -   dictionary consisting of VM Display Name as Key and
                            VM ID as value
        &#34;&#34;&#34;
        vm_ids = {}
        vm_names = {}

        def _assign_vm_name_id(contents, _vm_ids, _vm_names):
            for _content in contents:
                if _content.get(&#39;content&#39;):
                    _vm_ids, _vm_names = _assign_vm_name_id(_content[&#39;content&#39;], _vm_ids, _vm_names)
                    continue
                if _content[&#39;type&#39;].lower() in (&#39;vm&#39;, &#39;virtual machine&#39;):
                    _vm_ids[_content[&#39;id&#39;]] = _content[&#39;display_name&#39;]
                    _vm_names[_content[&#39;display_name&#39;]] = _content[&#39;id&#39;]
                else:
                    _vm_ids = {}
                    _vm_names = {}
                    break
            return _vm_ids, _vm_names
        return _assign_vm_name_id(self.content, vm_ids, vm_names)

    def _get_vm_ids_and_names_dict_from_browse(self):
        &#34;&#34;&#34;Parses through the Browse content and get the VMs Backed up

            returns :
                vm_names    (list)  -- returns list of VMs backed up
                vm_ids      (dict)  -- returns id list of VMs backed up
        &#34;&#34;&#34;

        _vm_ids, _vm_names = self._get_vm_ids_and_names_dict()
        if not self._vm_names_browse:
            paths, paths_dict = self.browse()
            if not _vm_names:
                for key, val in paths_dict.items():
                    _vm_names[val[&#39;name&#39;]] = val[&#39;snap_display_name&#39;]
            for _each_path in paths_dict:
                _vm_id = _each_path.split(&#34;\\&#34;)[1]
                self._vm_names_browse.append(_vm_id)
                self._vm_ids_browse[_vm_id] = _vm_names[_vm_id]

        return self._vm_names_browse, self._vm_ids_browse

    def _parse_vm_path(self, vm_names, vm_path):
        &#34;&#34;&#34;Parses the path provided by user, and replaces the VM Display Name
           with the VM ID.

            Returns:
                str     -   string of path to run browse for
        &#34;&#34;&#34;
        if vm_path not in [&#39;\\&#39;, &#39;&#39;]:
            if not vm_path.startswith(&#39;\\&#39;):
                vm_path = &#39;\\&#39; + vm_path

            vm_path_list = vm_path.split(&#39;\\&#39;)

            for vm_name in vm_names:
                if vm_name in vm_path_list[1]:
                    vm_path = vm_path.replace(vm_path_list[1], vm_names[vm_name])
                    break

        return vm_path

    def _process_vsa_browse_response(self, vm_ids, browse_content):
        &#34;&#34;&#34;Processes the Browse response and replaces the VM ID with their
        display name before returning to user.

            Args:
                vm_ids          (dict)      --  dictionary with VM ID as Key
                                                and VM Name as value

                browse_content  (tuple)     --  browse response received from
                                                server

            Returns:
                list - list of all folders or files with their full
                       paths inside the input path

                dict - path along with the details like name, file/folder,
                       size, modification time
        &#34;&#34;&#34;
        for index, path in enumerate(browse_content[0]):
            if vm_ids:
                for vm_id in vm_ids:
                    if vm_id in path:
                        browse_content[0][index] = path.replace(vm_id, vm_ids[vm_id])

        temp_dict = {}

        for path in browse_content[1]:
            if vm_ids:
                for vm_id in vm_ids:
                    if vm_id in path:
                        temp_dict[path.replace(vm_id, vm_ids[vm_id])] = browse_content[1][path]

        return browse_content[0], temp_dict

    def _process_restore_request(self, vm_names, restore_content):
        &#34;&#34;&#34;Processes the Restore Request and replaces the VM display name with
           their ID before passing to the API.

            Args:
                vm_names            (dict)      --  dictionary with VM Name as
                                                    Key, VM ID as value

                restore_content     (tuple)    --  content to restore specified
                                                   by user

            Returns:
                list - list of all folders or files with their full paths
                       inside the input path
        &#34;&#34;&#34;
        for index, path in enumerate(restore_content):
            if vm_names:
                for vm_name in vm_names:
                    if vm_name in path:
                        restore_content[index] = path.replace(vm_name, vm_names[vm_name])

        return restore_content

    def browse(self, vm_path=&#39;\\&#39;,
               show_deleted_files=False,
               vm_disk_browse=False,
               vm_files_browse=False,
               operation=&#39;browse&#39;,
               copy_precedence=0,
               **kwargs
               ):
        &#34;&#34;&#34;Gets the content of the backup for this subclient at the path
           specified.

            Args:
                vm_path             (str)   --  vm path to get the contents of
                                                default: &#39;\\&#39;;
                                                returns the root of the Backup
                                                content

                show_deleted_files  (bool)  --  include deleted files in the
                                                content or not default: False

                vm_disk_browse      (bool)  --  browse virtual machine files
                                                e.g.; .vmdk files, etc.
                                                only applicable when browsing
                                                content inside a guest virtual
                                                machine
                                                default: False

                vm_files_browse      (bool)  -- browse files and folders
                                                default: True

                operation            (str)   -- Type of operation, browser of find

                copy_precedence      (int)   -- The copy precedence to do the operation from

            Kwargs(optional)

                live_browse           (bool)   -- set to True to get live browse content
                                                    even though file indexing is enabled

            Returns:
                list - list of all folders or files with their full paths
                       inside the input path

                dict - path along with the details like name, file/folder,
                       size, modification time

            Raises:
                SDKException:
                    if failed to browse content

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        vm_ids, vm_names = self._get_vm_ids_and_names_dict()

        if operation == &#39;find&#39;:
            # Return all VMs browse content for find operation
            vm_path_list = []
            browse_content_dict = {}
            if not vm_names:
                _vm_ids, vm_names = self._get_vm_ids_and_names_dict_from_browse()
            vm_paths = [&#39;\\&#39; + vm_id for vm_id in vm_names.values()]
            for vm_path in vm_paths:
                vm_path = self._parse_vm_path(vm_names, vm_path)
                browse_content = super(VirtualServerSubclient, self).browse(
                    show_deleted_files, vm_disk_browse, True, path=vm_path,
                    vs_file_browse=vm_files_browse, operation=operation,
                    copy_precedence=copy_precedence
                )
                vm_path_list += browse_content[0]
                browse_content_dict.update(browse_content[1])
            browse_content = (vm_path_list, browse_content_dict)

        else:
            vm_path = self._parse_vm_path(vm_names, vm_path)
            browse_content = super(VirtualServerSubclient, self).browse(
                show_deleted_files, vm_disk_browse, True, path=vm_path,
                vs_file_browse=vm_files_browse, operation=operation, **kwargs
            )

        if not vm_ids:
            for key, val in browse_content[1].items():
                vm_ids[val[&#39;snap_display_name&#39;]] = val[&#39;name&#39;]
        return self._process_vsa_browse_response(vm_ids, browse_content)

    def parse_nics_xml(self, input_xml):
        &#34;&#34;&#34;
            Gets the content of the backup for this subclient at the path
            specified.

            Args:
                input_xml : --   nics info xml per vm to parse the nics name
                                 and label

            Returns:
                nic_list:   --    list of all Nics for a VM

            Raise:
                SDKException:
                    if input parameter is not proper


        &#34;&#34;&#34;
        if not isinstance(input_xml, str):
            raise SDKException(&#34;Subclient&#34;, &#34;101&#34;)

        root = ET.fromstring(input_xml)

        nic_list = []

        for nic in root.findall(&#39;nic&#39;):
            name = nic.get(&#39;name&#39;)
            label = nic.get(&#39;label&#39;)
            subnet = nic.get(&#39;subnet&#39;)
            networkDisplayName = nic.get(&#39;networkDisplayName&#39;, &#34;&#34;)
            sourceNetwork = nic.get(&#39;id&#39;,&#34;&#34;)

            nic_info = {
                &#39;name&#39;: name,
                &#39;label&#39;: label,
                &#39;subnetId&#39;: subnet,
                &#39;networkDisplayName&#39;: networkDisplayName,
                &#39;sourceNetwork&#39;: sourceNetwork
            }
            nic_list.append(nic_info)

        return nic_list

    def get_nics_from_browse(self, copy_precedence=0):
        &#34;&#34;&#34;
            Browses the vm to get the nics info xml, gets the nics info using
            the parse_nics_xml method and prepares the dict for nics json

            Args:
                copy_precedence     (int)   --  The copy precedence to do browse from

            Returns:
                dict:   --   dict with key as vm_name and the value as the
                             nics info for that vm

        &#34;&#34;&#34;

        path, path_dict = self.browse(vm_disk_browse=True, copy_precedence=copy_precedence)

        nics_dict = {}
        nics = &#34;&#34;

        # Added for v2.1
        for vmpath in path:
            result = path_dict[vmpath]
            if (&#39;browseMetaData&#39; not in result[&#39;advanced_data&#39;]) or \
                    (&#39;virtualServerMetaData&#39; not in result[&#39;advanced_data&#39;][&#39;browseMetaData&#39;]) or \
                    (&#39;nics&#39; not in result[&#39;advanced_data&#39;][&#39;browseMetaData&#39;][&#39;virtualServerMetaData&#39;]):
                path, path_dict = self.browse(vm_disk_browse=True, operation=&#39;find&#39;, copy_precedence=copy_precedence)
        for vmpath in path:
            result = path_dict[vmpath]
            name = &#34;&#34;
            if &#39;name&#39; in result:
                name = result[&#39;name&#39;]
            if &#39;advanced_data&#39; in result:
                advanced_data = result[&#39;advanced_data&#39;]

                if &#39;browseMetaData&#39; in advanced_data:
                    browse_meta_data = advanced_data[&#39;browseMetaData&#39;]

                    if &#39;virtualServerMetaData&#39; in browse_meta_data:
                        virtual_server_metadata = browse_meta_data[&#39;virtualServerMetaData&#39;]

                        if &#39;nics&#39; in virtual_server_metadata:
                            nics = virtual_server_metadata[&#39;nics&#39;]

            nics_dict[name] = self.parse_nics_xml(nics)

        return nics_dict

    def browse_in_time(
            self,
            vm_path=&#39;\\&#39;,
            show_deleted_files=False,
            restore_index=True,
            vm_disk_browse=False,
            from_date=0,
            to_date=0,
            copy_precedence=0,
            vm_files_browse=False,
            media_agent=&#34;&#34;):
        &#34;&#34;&#34;Gets the content of the backup for this subclient
                at the path specified in the time range specified.

                Args:
                    vm_path             (str)   --  folder path to get the
                                                    contents of
                                                    default: &#39;\\&#39;
                                                    returns the root of the
                                                    Backup content

                    show_deleted_files  (bool)  --  include deleted files in
                                                    the content or not
                                                    default: False

                    restore_index       (bool)  --  restore index if it is not
                                                    cached  default: True

                    vm_disk_browse      (bool)  --  browse the VM disks or not
                                                    default: False

                    from_date           (int)   --  date to get the contents
                                                    after
                                                    format: dd/MM/YYYY
                                                    gets contents from
                                                    01/01/1970 if not specified
                                                    default: 0

                    to_date             (int)  --   date to get the contents
                                                    before
                                                    format: dd/MM/YYYY
                                                    gets contents till current
                                                    day if not specified
                                                    default: 0

                    copy_precedence     (int)   --  copy precedence to be used
                                                    for browsing

                    media_agent         (str)   --  Browse MA via with Browse has to happen.
                                                    It can be MA different than Storage Policy MA

                Returns:
                    list - list of all folders or files with their full paths
                           inside the input path

                    dict - path along with the details like name, file/folder,
                           size, modification time

                Raises:
                    SDKException:
                        if from date value is incorrect

                        if to date value is incorrect

                        if to date is less than from date

                        if failed to browse content

                        if response is empty

                        if response is not success
            &#34;&#34;&#34;
        vm_ids, vm_names = self._get_vm_ids_and_names_dict()
        vm_path = self._parse_vm_path(vm_names, vm_path)

        browse_content = super(VirtualServerSubclient, self).browse(
            show_deleted=show_deleted_files, restore_index=restore_index,
            vm_disk_browse=vm_disk_browse,
            from_time=from_date, to_time=to_date, copy_precedence=copy_precedence,
            path=vm_path, vs_file_browse=vm_files_browse, media_agent=media_agent)
        if not vm_ids:
            for key, val in browse_content[1].items():
                vm_ids[val[&#39;snap_display_name&#39;]] = val[&#39;name&#39;]
        return self._process_vsa_browse_response(vm_ids, browse_content)

    def disk_level_browse(self, vm_path=&#39;\\&#39;,
                          show_deleted_files=False,
                          restore_index=True,
                          from_date=0,
                          to_date=0,
                          copy_precedence=0):
        &#34;&#34;&#34;Browses the Disks of a Virtual Machine.

            Args:
                vm_path             (str)   --  vm path to get the contents of
                    default: &#39;\\&#39;; returns the root of the Backup content

                show_deleted_files  (bool)  --  include deleted files in the
                                                content or not default: False

                restore_index  (bool)  --       Restore index or not.
                                                default: True

                from_date           (int)   --  date to get the contents after
                                                format: dd/MM/YYYY
                                                gets contents from 01/01/1970
                                                if not specified
                                                default: 0

                to_date             (int)  --  date to get the contents before
                                               format: dd/MM/YYYY
                                               gets contents till current day
                                               if not specified
                                               default: 0

                copy_precedence     (int)   --  copy precedence to be used
                                                    for browsing

            Returns:
                list - list of all folders or files with their full paths
                       inside the input path

                dict - path along with the details like name, file/folder,
                       size, modification time

            Raises:
                SDKException:
                    if failed to browse content

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        browse_content = self.browse_in_time(
            vm_path, show_deleted_files, restore_index, True, from_date, to_date, copy_precedence
        )

        paths_list = []
        for path in browse_content[0]:
            if any(path.lower().endswith(Ext) for Ext in self.diskExtension):
                paths_list.append(path)

            elif os.path.splitext(path)[1] == &#34;&#34; and &#34;none&#34; in self.diskExtension:
                paths_list.append(path)

        paths_dict = {}

        for path in browse_content[1]:
            if any(path.lower().endswith(Ext) for Ext in self.diskExtension):
                paths_dict[path] = browse_content[1][path]
            elif os.path.splitext(path)[1] == &#34;&#34; and &#34;none&#34; in self.diskExtension:
                # assuming it as Fusion compute kind of hypervisors
                paths_dict[path] = browse_content[1][path]

        if paths_list and paths_dict:
            return paths_list, paths_dict
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;113&#39;)

    def guest_files_browse(
            self,
            vm_path=&#39;\\&#39;,
            show_deleted_files=False,
            restore_index=True,
            from_date=0,
            to_date=0,
            copy_precedence=0,
            media_agent=&#34;&#34;):
        &#34;&#34;&#34;Browses the Files and Folders inside a Virtual Machine in the time
           range specified.

            Args:
                vm_path             (str)   --  folder path to get the contents
                                                of
                                                default: &#39;\\&#39;;
                                                returns the root of the Backup
                                                content

                show_deleted_files  (bool)  --  include deleted files in the
                                                content or not default: False

                restore_index       (bool)  --  restore index if it is not cached
                                                default: True

                from_date           (int)   --  date to get the contents after
                                                format: dd/MM/YYYY

                                                gets contents from 01/01/1970
                                                if not specified
                                                default: 0

                to_date             (int)  --  date to get the contents before
                                               format: dd/MM/YYYY

                                               gets contents till current day
                                               if not specified
                                               default: 0

                copy_precedence     (int)   --  copy precedence to be used
                                                    for browsing

                media_agent         (str)   --  Browse MA via with Browse has to happen.
                                                It can be MA different than Storage Policy MA

            Returns:
                list - list of all folders or files with their full paths
                       inside the input path

                dict - path along with the details like name, file/folder,
                       size, modification time

            Raises:
                SDKException:
                    if from date value is incorrect

                    if to date value is incorrect

                    if to date is less than from date

                    if failed to browse content

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        return self.browse_in_time(
            vm_path, show_deleted_files, restore_index, False, from_date, to_date, copy_precedence,
            vm_files_browse=True, media_agent=media_agent)

    def _check_folder_in_browse(
            self,
            _vm_id,
            _folder_to_restore,
            from_date,
            to_date,
            copy_precedence,
            media_agent):
        &#34;&#34;&#34;
        Check if the particular folder is present in browse of the subclient
        in particular VM

        args:
            _vm_id      (str)     -- VM id from which folder has to be restored

            _folder_to_restore (str)     -- folder path which has to be restored

            from_date           (int)   --  date to get the contents after
                                                format: dd/MM/YYYY

                                                gets contents from 01/01/1970
                                                if not specified
                                                default: 0

            to_date             (int)  --  date to get the contents before
                                               format: dd/MM/YYYY

                                               gets contents till current day
                                               if not specified
                                               default: 0

            copy_precedence     (int)   --  copy precedence to be used
                                                    for browsing

            media_agent         (str)   --  Browse MA via with Browse has to hapeen .
                                                    It can be MA different than Storage Policy MA

        exception:
            raise exception
                if folder is not present in browse
        &#34;&#34;&#34;

        source_item = None

        _folder_to_restore = _folder_to_restore.replace(&#34;:&#34;, &#34;&#34;)
        _restore_folder_name = _folder_to_restore.split(&#34;\\&#34;)[-1]
        _folder_to_restore = _folder_to_restore.replace(&#34;\\&#34; + _restore_folder_name, &#34;&#34;)
        _source_path = &#39;\\&#39;.join([_vm_id, _folder_to_restore])

        _browse_files, _browse_files_dict = self.guest_files_browse(
            _source_path, from_date=from_date, to_date=to_date,
            copy_precedence=copy_precedence, media_agent=media_agent)

        for _path in _browse_files_dict:
            _browse_folder_name = _path.split(&#34;\\&#34;)[-1]
            if _browse_folder_name == _restore_folder_name:
                source_item = &#39;\\&#39;.join([_source_path, _restore_folder_name])
                source_item = &#39;\\&#39; + source_item
                break

        if source_item is None:
            raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, &#39;Browse failure: Folder not found in browse&#39;)

        return source_item

    def guest_file_restore(self, *args, **kwargs):
        &#34;&#34;&#34;perform Guest file restore of the provided path

        Args:
            options     (dict)  --  dictionary of guest file restores options

        &#34;&#34;&#34;
        if args and isinstance(args[0], dict):
            options = args[0]
        else:
            options = kwargs
        vm_name = options.get(&#39;vm_name&#39;, None)
        folder_to_restore = options.get(&#39;folder_to_restore&#39;, None)
        destination_client = options.get(&#39;destination_client&#39;, None)
        destination_path = options.get(&#39;destination_path&#39;, None)
        copy_precedence = options.get(&#39;copy_precedence&#39;, 0)
        preserve_level = options.get(&#39;preserve_level&#39;, 1)
        unconditional_overwrite = options.get(&#39;unconditional_overwrite&#39;, False)
        restore_ACL = options.get(&#39;restore_ACL&#39;, True)
        from_date = options.get(&#39;from_date&#39;, 0)
        to_date = options.get(&#39;to_date&#39;, 0)
        show_deleted_files = options.get(&#39;show_deleted_files&#39;, False)
        fbr_ma = options.get(&#39;fbr_ma&#39;, None)
        browse_ma = options.get(&#39;browse_ma&#39;, &#34;&#34;)
        agentless = options.get(&#39;agentless&#39;, &#34;&#34;)
        in_place = options.get(&#39;in_place&#39;, False)

        _vm_names, _vm_ids = self._get_vm_ids_and_names_dict_from_browse()
        _file_restore_option = {}
        _verify_path = options.get(&#39;verify_path&#39;, True)

        # check if inputs are correct
        if not(isinstance(destination_path, str) and
               (isinstance(vm_name, str))):
            raise SDKException(&#39;Subclient&#39;, &#39;105&#39;)

        if vm_name not in _vm_names:
            raise SDKException(&#39;Subclient&#39;, &#39;111&#39;)

        # check if client name is correct
        if destination_client is None:
            destination_client = self._backupset_object._instance_object.co_ordinator

        if fbr_ma:
            _file_restore_option[&#34;proxy_client&#34;] = fbr_ma

        _file_restore_option[&#34;client&#34;] = destination_client
        _file_restore_option[&#34;destination_path&#34;] = destination_path

        # process the folder to restore for browse
        if isinstance(folder_to_restore, list):
            _folder_to_restore_list = folder_to_restore

        elif isinstance(folder_to_restore, str):
            _folder_to_restore_list = []
            _folder_to_restore_list.append(folder_to_restore)
        else:
            raise SDKException(&#39;Subclient&#39;, &#39;105&#39;)

        _file_restore_option[&#34;paths&#34;] = []
        for _each_folder in _folder_to_restore_list:
            # check_folder_in_browse modifies path (removes colon) and verifies in browse results.
            # The modified path does not work for windows VM when file indexing is enabled
            # Set `verify_path` to False to skip this verification and use the restore path as is

            if _verify_path:
                _restore_item_path = self._check_folder_in_browse(
                    _vm_ids[vm_name],
                    &#34;%s&#34; % _each_folder,
                    from_date,
                    to_date,
                    copy_precedence,
                    media_agent=browse_ma
                )
            else:
                # Converting native path to VM path
                # C:\folder1 =&gt; \&lt;vm_guid&gt;\C:\folder1
                # /folder1/folder2 =&gt; \&lt;vm_guid&gt;\folder1\folder2

                _item_path = _each_folder.replace(&#39;/&#39;, &#39;\\&#39;)
                _item_path = _item_path[1:] if _item_path[0] == &#39;\\&#39; else _item_path
                _restore_item_path = &#39;\\&#39;.join([&#39;&#39;, _vm_ids[vm_name], _item_path])

            _file_restore_option[&#34;paths&#34;].append(_restore_item_path)

        # set the browse options
        _file_restore_option[&#34;disk_browse&#34;] = False
        _file_restore_option[&#34;file_browse&#34;] = True
        _file_restore_option[&#34;from_time&#34;] = from_date
        _file_restore_option[&#34;to_time&#34;] = to_date

        # set the common file level restore options
        _file_restore_option[&#34;striplevel_type&#34;] = &#34;PRESERVE_LEVEL&#34;
        _file_restore_option[&#34;preserve_level&#34;] = preserve_level
        _file_restore_option[&#34;unconditional_overwrite&#34;] = unconditional_overwrite
        _file_restore_option[&#34;restore_ACL&#34;] = restore_ACL
        _file_restore_option[&#34;in_place&#34;] = in_place

        # set the browse option
        _file_restore_option[&#34;copy_precedence_applicable&#34;] = True
        _file_restore_option[&#34;copy_precedence&#34;] = copy_precedence
        _file_restore_option[&#34;media_agent&#34;] = browse_ma

        # set agentless options
        if agentless:
            _file_restore_option[&#34;server_name&#34;] = agentless[&#39;vserver&#39;]
            _file_restore_option[&#34;vm_guid&#34;] = agentless[&#39;vm_guid&#39;]
            _file_restore_option[&#34;vm_name&#34;] = agentless[&#39;vm_name&#39;]
            _file_restore_option[&#34;user_name&#34;] = agentless[&#39;vm_user&#39;]
            _file_restore_option[&#34;password&#34;] = agentless[&#39;vm_pass&#39;]
            _file_restore_option[&#34;agentless&#34;] = True

        # prepare and execute the Json
        request_json = self._prepare_filelevel_restore_json(_file_restore_option)
        return self._process_restore_response(request_json)

    def vm_files_browse(self, vm_path=&#39;\\&#39;, show_deleted_files=False, operation=&#39;browse&#39;, copy_precedence=0):
        &#34;&#34;&#34;Browses the Files and Folders of a Virtual Machine.

            Args:
                vm_path             (str)   --  vm path to get the contents of
                                                default: &#39;\\&#39;;
                                                returns the root of the Backup
                                                content

                show_deleted_files  (bool)  --  include deleted files in the
                                                content or not
                                                default: False

                operation           (str)   --  The type of operation to perform (browse/find)

                copy_precedence     (int)   --  The copy precedence to do browse from

            Returns:
                list - list of all folders or files with their full paths
                       inside the input path

                dict - path along with the details like name, file/folder,
                       size, modification time

            Raises:
                SDKException:
                    if failed to browse content

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        return self.browse(vm_path, show_deleted_files, True, operation=operation, copy_precedence=copy_precedence)

    def vm_files_browse_in_time(
            self,
            vm_path=&#39;\\&#39;,
            show_deleted_files=False,
            restore_index=True,
            from_date=0,
            to_date=0):
        &#34;&#34;&#34;Browses the Files and Folders of a Virtual Machine in the time range
           specified.

            Args:
                vm_path             (str)   --  folder path to get the contents
                                                default: &#39;\\&#39;;
                                                returns the root of the Backup
                                                content

                show_deleted_files  (bool)  --  include deleted files in the
                                                content or not
                                                default: False

                restore_index       (bool)  --  restore index if it is not
                                                cached
                                                default: True

                from_date           (int)   --  date to get the contents after
                                                format: dd/MM/YYYY
                                                gets contents from 01/01/1970
                                                if not specified
                                                default: 0

                to_date             (int)  --   date to get the contents before
                                                format: dd/MM/YYYY
                                                gets contents till current day
                                                if not specified
                                                default: 0

            Returns:
                list - list of all folders or files with their full paths
                       inside the input path

                dict - path along with the details like name, file/folder,
                       size, modification time

            Raises:
                SDKException:
                    if from date value is incorrect

                    if to date value is incorrect

                    if to date is less than from date

                    if failed to browse content

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        return self.browse_in_time(
            vm_path, show_deleted_files, restore_index, True, from_date, to_date
        )

    def reinitialize_vm_names_browse(self):
        self._vm_names_browse = []
        self._get_vm_ids_and_names_dict_from_browse()

    def _get_disk_extension(self, disk_list):
        &#34;&#34;&#34;
        get the Extension of all disk in the list

        Args:
            disk_list   (LIST)  -- get the disk List

        return:
            extn_list   (LIST)  --  returns the Extension List of the disk list
        &#34;&#34;&#34;

        _extn_list = []
        for each_disk in disk_list:
            _disk_name, _extn_name = os.path.splitext(each_disk)
            _extn_list.append(_extn_name)

        _extn_list = list(set(_extn_list))

        if len(_extn_list) &gt; 1:
            return _extn_list
        else:
            return _extn_list[0]

    def _get_conversion_disk_Type(self, _src_disk_extn, _dest_disk_extn):
        &#34;&#34;&#34;
        return volume restore type and destination disk Type

        Args:
            src_disk_extn   (str)   --  source disk extension of the disk
            dest_disk_extn  (str)   --  Extension to which disk is converted

        return
            _vol_restore_type   (str)   -- value of Volume restore type
                                           parameter of the XML
            _dest_disk_type     (str)   -- value of destination Disk Type
                                           parameter of the XML
        &#34;&#34;&#34;

        disk_conversion = {
            &#34;vhdx&#34;: {
                &#34;vhd&#34;: (&#34;VIRTUAL_HARD_DISKS&#34;, &#34;VHD_DYNAMIC&#34;),
                &#34;vmdk&#34;: (&#34;VMDK_FILES&#34;, &#34;VMDK_VCB4&#34;)
            },
            &#34;vmdk&#34;: {
                &#34;vhd&#34;: (&#34;VIRTUAL_HARD_DISKS&#34;, &#34;VHD_DYNAMIC&#34;),
                &#34;vhdx&#34;: (&#34;VIRTUAL_HARD_DISKS&#34;, &#34;VHDX_DYNAMIC&#34;)
            }
        }
        _src_disk_extn = _src_disk_extn.lower().strip(&#34;.&#34;)
        _dest_disk_extn = _dest_disk_extn.lower().strip(&#34;.&#34;)

        return disk_conversion[_src_disk_extn][_dest_disk_extn]

    def _set_vm_to_restore(self, vm_to_restore=None, restore_option=None):
        &#34;&#34;&#34;
        check whether the VMs provided for restore is backued up else assume
                            Vm_to_restore with default

        Args:
            vm_to_restore   (list)      -- list of Vm to restore

            restore_option  (dict)      -- dict with all restore options

        return:
            vm_to_restore   (list)      -- Final list of Vm need to be restored

        &#34;&#34;&#34;
        if restore_option is None:
            restore_option = {}

        if not self._vm_names_browse:
            self._get_vm_ids_and_names_dict_from_browse()

        # set vms to restore
        if not vm_to_restore:
            vm_to_restore = restore_option.get(&#34;vm_to_restore&#34;, self._vm_ids_browse)
            _temp_res_list = vm_to_restore

        else:
            _temp_res_list = []
            if not isinstance(vm_to_restore, list):
                vm_to_restore = [vm_to_restore]
            for each_vm in vm_to_restore:
                _temp_res_list.append(each_vm)

        vm_to_restore = list(set(self._vm_names_browse) &amp; set(_temp_res_list))

        if not vm_to_restore:
            raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)

        return vm_to_restore

    def _set_restore_inputs(self, restore_option, **kwargs):
        &#34;&#34;&#34;
        set all the advanced properties of the subclient restore

        Args:
            restore_option  (dict)  -- restore option dictionary where advanced
                                            properties to be appended

            **kwargs                --  Keyword arguments with key as property name
                                            and its value
        &#34;&#34;&#34;
        for key in kwargs:
            if key not in restore_option or restore_option[key] is None:
                restore_option[key] = kwargs[key]

    def _get_subclient_proxies(self):
        &#34;&#34;&#34;
        Get the list of all the proxies on a selected subclient

        Returns:
            associated_proxies   (List)  --  returns the proxies list
        &#34;&#34;&#34;
        associated_proxies = []
        try:
            available_subclient_proxies = self._vsaSubclientProp[&#34;proxies&#34;][&#34;memberServers&#34;]
            if len(available_subclient_proxies) &gt; 0:
                for client in available_subclient_proxies:
                    if &#39;clientName&#39; in client[&#39;client&#39;]:
                        associated_proxies.append(client[&#34;client&#34;][&#34;clientName&#34;])
                    elif &#39;clientGroupName&#39; in client[&#39;client&#39;]:
                        client_group = self._commcell_object.client_groups.get(client[&#34;client&#34;][&#34;clientGroupName&#34;])
                        associated_proxies.extend(client_group.associated_clients)
        except KeyError:
            pass
        return list(dict.fromkeys(associated_proxies))

    def _set_restore_defaults(self, restore_option):
        &#34;&#34;&#34;
        :param restore_option:  dict with all restore input values
        &#34;&#34;&#34;

        if ((&#34;vcenter_client&#34; not in restore_option) or (
                restore_option[&#34;vcenter_client&#34;] is None)):
            instance_dict = self._backupset_object._instance_object._properties[&#39;instance&#39;]
            restore_option[&#34;destination_client_name&#34;] = instance_dict[&#34;clientName&#34;]
            restore_option[&#34;destination_instance&#34;] = instance_dict[&#34;instanceName&#34;]
            instance = self._backupset_object._instance_object

        else:
            client = self._commcell_object.clients.get(restore_option[&#34;vcenter_client&#34;])
            restore_option[&#34;destination_client_name&#34;] = restore_option[&#34;vcenter_client&#34;]
            restore_option[&#34;destination_client_id&#34;] = int(client.client_id)
            agent = client.agents.get(&#39;Virtual Server&#39;)
            instancekeys = next(iter(agent.instances._instances))
            instance = agent.instances.get(instancekeys)
            restore_option[&#34;destination_instance&#34;] = instance.instance_name
            restore_option[&#34;destination_instance_id&#34;] = int(instance.instance_id)

        if ((&#34;esx_server&#34; not in restore_option) or
                (restore_option[&#34;esx_server&#34;] is None)):
            restore_option[&#34;esx_server&#34;] = instance.server_host_name[0]

        if ((&#34;client_name&#34; not in restore_option) or
                (restore_option[&#34;client_name&#34;] is None)):
            subclient_proxy_list = self._get_subclient_proxies()

            if len(subclient_proxy_list) &gt; 0:
                restore_option[&#34;client&#34;] = subclient_proxy_list[0]
            else:
                restore_option[&#34;client&#34;] = instance.co_ordinator
        else:
            restore_option[&#34;client&#34;] = restore_option[&#34;client_name&#34;]

    def _set_vm_conversion_defaults(self, vcenter_client, restore_option):
        &#34;&#34;&#34;
        set all the VMconversion changews need to be performed
        Args:
            vcenter_client: Client Name to which it has to be converted

            restore_option: dictinoary where parameter needs to be set

        Returns:
            subclient :     (obj)   : object for the subclient class of virtual client
            raise exception:
             if client does not exist

        &#34;&#34;&#34;

        client = self._commcell_object.clients.get(vcenter_client)
        agent = client.agents.get(&#39;Virtual Server&#39;)
        instancekeys = next(iter(agent.instances._instances))
        instance = agent.instances.get(instancekeys)
        backupsetkeys = next(iter(instance.backupsets._backupsets))
        backupset = instance.backupsets.get(backupsetkeys)
        sckeys = next(iter(backupset.subclients._subclients))
        subclient = backupset.subclients.get(sckeys)

        # populating all defaults
        esx_server = instance.server_host_name[0]
        self.disk_pattern = subclient.disk_pattern
        restore_option[&#34;destination_vendor&#34;] = instance._vendor_id
        restore_option[&#34;backupset_client_name&#34;] = client.client_name

        if not subclient:
            raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)

        return subclient

    def set_advanced_vm_restore_options(self, vm_to_restore, restore_option):
        &#34;&#34;&#34;
        set the advanced restore options for all vm in restore
        :param

        vm_to_restore : Name of the VM to restore
        restore_option: restore options that need to be set for advanced restore option

            power_on                    - power on the VM after restore
            add_to_failover             - Register the VM to Failover Cluster
            datastore                   - Datastore where the VM needs to be restored

            disks   (list of dict)      - list with dict for each disk in VM
                                            eg: [{
                                                    name:&#34;disk1.vmdk&#34;
                                                    datastore:&#34;local&#34;
                                                }
                                                {
                                                    name:&#34;disk2.vmdk&#34;
                                                    datastore:&#34;local1&#34;
                                                }
                                            ]
            guid                        - GUID of the VM needs to be restored
            new_name                    - New name for the VM to be restored
            esx_host                    - esx_host or client name where it need to be restored
            name                        - name of the VM to be restored
        &#34;&#34;&#34;

        # Set the new name for the restored VM.
        # If new_name is not given, it restores the VM with same name
        # with suffix Delete.
        vm_names, vm_ids = self._get_vm_ids_and_names_dict_from_browse()
        copy_precedence = restore_option.get(&#39;copy_precedence&#39;, 0)
        browse_result = self.vm_files_browse(copy_precedence=copy_precedence)

        # vs metadata from browse result
        _metadata = browse_result[1][(&#39;\\&#39; + vm_to_restore)]

        if (&#39;browseMetaData&#39; not in _metadata[&#39;advanced_data&#39;]) or \
                (&#39;virtualServerMetaData&#39; not in _metadata[&#39;advanced_data&#39;][&#39;browseMetaData&#39;]) or \
                (&#39;nics&#39; not in _metadata[&#39;advanced_data&#39;][&#39;browseMetaData&#39;][&#39;virtualServerMetaData&#39;]):
            browse_result = self.vm_files_browse(operation=&#39;find&#39;, copy_precedence=copy_precedence)
            _metadata = browse_result[1][(&#39;\\&#39; + vm_to_restore)]

        vs_metadata = _metadata[&#34;advanced_data&#34;][&#34;browseMetaData&#34;][&#34;virtualServerMetaData&#34;]
        if restore_option[&#39;in_place&#39;]:
            folder_path = vs_metadata.get(&#34;inventoryPath&#34;, &#39;&#39;)
            instanceSize = vs_metadata.get(&#34;instanceSize&#34;, &#39;&#39;)
        else:
            folder_path = restore_option[&#39;folder_path&#39;] if restore_option.get(&#39;folder_path&#39;) else &#39;&#39;
            instanceSize = &#39;&#39;

        if &#39;resourcePoolPath&#39; in restore_option and restore_option[&#39;resourcePoolPath&#39;] is None:
            restore_option[&#39;resourcePoolPath&#39;] = vs_metadata[&#39;resourcePoolPath&#39;]
        if &#39;datacenter&#39; in restore_option and restore_option[&#39;datacenter&#39;] is None:
            restore_option[&#39;datacenter&#39;] = vs_metadata.get(&#39;dataCenter&#39;, &#39;&#39;)
        if (&#39;terminationProtected&#39; in restore_option and
                restore_option[&#39;terminationProtected&#39;] is None):
            restore_option[&#39;terminationProtected&#39;] = vs_metadata.get(&#39;terminationProtected&#39;, &#39;&#39;)
        if &#39;iamRole&#39; in restore_option and restore_option[&#39;iamRole&#39;] is None:
            restore_option[&#39;iamRole&#39;] = vs_metadata.get(&#39;role&#39;, &#39;&#39;)
        if &#39;securityGroups&#39; in restore_option and restore_option[&#39;securityGroups&#39;] is None:
            _security_groups = self._find_security_groups(vs_metadata[&#39;networkSecurityGroups&#39;])
            restore_option[&#39;securityGroups&#39;] = _security_groups
        if &#39;keyPairList&#39; in restore_option and restore_option[&#39;keyPairList&#39;] is None:
            _keypair_list = self._find_keypair_list(vs_metadata[&#39;loginKeyPairs&#39;])
            restore_option[&#39;keyPairList&#39;] = _keypair_list

        # populate restore source item
        restore_option[&#39;paths&#39;].append(&#34;\\&#34; + vm_ids[vm_to_restore])
        restore_option[&#39;name&#39;] = vm_to_restore
        restore_option[&#39;guid&#39;] = vm_ids[vm_to_restore]
        restore_option[&#34;FolderPath&#34;] = folder_path
        restore_option[&#34;ResourcePool&#34;] = &#34;/&#34;

        # populate restore disk and datastore
        vm_disks = []
        disk_list, disk_info_dict = self.disk_level_browse(
            &#34;\\\\&#34; + vm_ids[vm_to_restore], copy_precedence=copy_precedence)

        for disk, data in disk_info_dict.items():
            ds = &#34;&#34;
            if &#34;datastore&#34; in restore_option:
                ds = restore_option[&#34;datastore&#34;]
            if restore_option[
                &#34;in_place&#34;] or &#34;datastore&#34; not in restore_option or not restore_option.get(
                    &#39;datastore&#39;):
                if &#34;datastore&#34; in data[&#34;advanced_data&#34;][&#34;browseMetaData&#34;][&#34;virtualServerMetaData&#34;]:
                    restore_option[&#34;datastore&#34;] = data[&#34;advanced_data&#34;][&#34;browseMetaData&#34;][
                        &#34;virtualServerMetaData&#34;][&#34;datastore&#34;]
                    ds = restore_option[&#34;datastore&#34;]
                elif &#34;esxHost&#34; in vs_metadata and &#34;is_aws_proxy&#34; in restore_option:
                    if restore_option.get(&#34;availability_zone&#34;) is not None:
                        ds = restore_option.get(&#34;availability_zone&#34;)
                    else:
                        ds = vs_metadata[&#34;esxHost&#34;]
            new_name_prefix = restore_option.get(&#34;disk_name_prefix&#34;)
            new_name = data[&#34;name&#34;] if new_name_prefix is None \
                else new_name_prefix + &#34;_&#34; + data[&#34;name&#34;]
            if self._instance_object.instance_name == HypervisorType.GOOGLE_CLOUD.value.lower():
                new_name = &#34;&#34;
                if data[&#34;advanced_data&#34;][&#34;browseMetaData&#34;][&#34;virtualServerMetaData&#34;].get(&#39;replicaZones&#39;, False):
                    replicaZones = restore_option.get(&#34;replicaZones&#34;)
            if restore_option[&#39;destination_instance&#39;].lower() in [HypervisorType.VIRTUAL_CENTER.value.lower(),
                                                                  HypervisorType.AZURE_V2.value.lower()]:
                _disk_dict = self._disk_dict_pattern(data[&#39;snap_display_name&#39;], ds, new_name)
            else:
                _disk_dict = self._disk_dict_pattern(disk.split(&#39;\\&#39;)[-1], ds, new_name)
            if &#39;is_aws_proxy&#39; in restore_option and not restore_option[&#39;is_aws_proxy&#39;]:
                _disk_dict[&#39;Datastore&#39;] = restore_option[&#34;datastore&#34;]
            vm_disks.append(_disk_dict)
        if not vm_disks:
            raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)
        restore_option[&#34;disks&#34;] = vm_disks

        # prepare nics info json
        if &#34;nics&#34; not in restore_option or \
                self._instance_object.instance_name == HypervisorType.GOOGLE_CLOUD.value.lower():
            nics_list = self._json_nics_advancedRestoreOptions(vm_to_restore, restore_option)
            restore_option[&#34;nics&#34;] = nics_list
            if restore_option.get(&#39;source_ip&#39;) and restore_option.get(&#39;destination_ip&#39;):
                vm_ip = self._json_vmip_advanced_restore_options(restore_option)
                restore_option[&#34;vm_ip_address_options&#34;] = vm_ip
            if restore_option[&#34;in_place&#34;]:
                if &#34;hyper&#34; in restore_option[&#34;destination_instance&#34;].lower():
                    restore_option[&#34;client_name&#34;] = vs_metadata[&#39;esxHost&#39;]
                    restore_option[&#34;esx_server&#34;] = vs_metadata[&#39;esxHost&#39;]
                elif &#39;Red&#39; in restore_option[&#34;destination_instance&#34;]:
                    restore_option[&#34;esxHost&#34;] = vs_metadata[&#39;clusterName&#39;]
                    restore_option[&#34;cluster&#34;] = vs_metadata[&#39;clusterName&#39;]
                    vs_metadata[&#34;esxHost&#34;] = vs_metadata[&#39;clusterName&#39;]

        #adding tag to restored vm
        if restore_option.get(&#39;vmTags&#39;):
            vm_tags = restore_option.get(&#39;vmTags&#39;)
            if isinstance(vm_tags, str) or isinstance(vm_tags, dict):
                vm_tags = [vm_tags]
            restore_option[&#34;vmTags&#34;] = vm_tags

        # populate VM Specific values
        self._set_restore_inputs(
            restore_option,
            disks=vm_disks,
            esx_host=restore_option.get(&#39;esx_host&#39;) or vs_metadata[&#39;esxHost&#39;],
            instanceSize=restore_option.get(&#39;instanceSize&#39;, instanceSize),
            new_name=restore_option.get(&#39;new_name&#39;, &#34;del&#34; + vm_to_restore)
        )

        temp_dict = self._json_restore_advancedRestoreOptions(restore_option)
        self._advanced_restore_option_list.append(temp_dict)

    def set_advanced_attach_disk_restore_options(self, vm_to_restore, restore_option):
        &#34;&#34;&#34;
        set the advanced restore options for all vm in restore
        :param

        vm_to_restore : Name of the VM where disks will be restored
        restore_option: restore options that need to be set for advanced restore option

            datastore                   - Datastore where the disks needs to be restored

            disks   (list of dict)      - list with dict for each disk in VM
                                            eg: [{
                                                    name:&#34;disk1.vmdk&#34;
                                                    datastore:&#34;local&#34;
                                                }
                                                {
                                                    name:&#34;disk2.vmdk&#34;
                                                    datastore:&#34;local1&#34;
                                                }
                                            ]
            guid                        - GUID of the VM needs to be restored
            new_name                    - New name for the VM to be restored
            esx_host                    - esx_host or client name where it need to be restored
            name                        - name of the VM to be restored
        &#34;&#34;&#34;

        # Set the new name for the restored VM.
        # If new_name is not given, it restores the VM with same name
        # with suffix Delete.
        vm_names, vm_ids = self._get_vm_ids_and_names_dict_from_browse()
        _ = self.vm_files_browse()
        # populate restore source item
        restore_option[&#39;name&#39;] = vm_to_restore
        restore_option[&#39;guid&#39;] = vm_ids[vm_to_restore]
        restore_option[&#34;FolderPath&#34;] = &#39;&#39;
        restore_option[&#34;ResourcePool&#34;] = &#34;/&#34;

        # populate restore disk and datastore
        vm_disks = []
        disk_list, disk_info_dict = self.disk_level_browse(
            &#34;\\\\&#34; + vm_ids[vm_to_restore])

        for disk, data in disk_info_dict.items():
            ds = &#34;&#34;
            if &#34;datastore&#34; in restore_option:
                ds = restore_option[&#34;datastore&#34;]
            new_name_prefix = restore_option.get(&#34;disk_name_prefix&#34;)
            if self._instance_object.instance_name != &#39;openstack&#39;:
                new_name = data[&#34;snap_display_name&#34;].replace(&#34;/&#34;, &#34;_&#34;).replace(&#34; &#34;, &#34;_&#34;)
                new_name = &#34;del_&#34; + new_name if new_name_prefix is None \
                    else new_name_prefix + &#34;_&#34; + new_name
            else:
                new_name = data[&#34;name&#34;]
            _disk_dict = self._disk_dict_pattern(data[&#39;snap_display_name&#39;], ds, new_name)
            vm_disks.append(_disk_dict)
        if not vm_disks:
            raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)
        restore_option[&#34;disks&#34;] = vm_disks

        # populate VM Specific values
        self._set_restore_inputs(
            restore_option,
            disks=vm_disks,
            esx_host=restore_option.get(&#39;esx&#39;),
            new_name=restore_option.get(&#39;newName&#39;, vm_to_restore),
            new_guid=restore_option.get(&#39;newGUID&#39;, restore_option.get(&#39;guid&#39;)),
            datastore=restore_option.get(&#39;datastore&#39;))

        temp_dict = self._json_restore_advancedRestoreOptions(restore_option)
        self._advanced_restore_option_list.append(temp_dict)

    @staticmethod
    def _find_security_groups(xml_str):
        &#34;&#34;&#34;
        sets the security group json from the input xml
        Args:
             xml_str            (str)  --  xml from which we want to retrieve security group info
        Returns:
             security_group    (dict)  -- security group dict
        &#34;&#34;&#34;
        match1 = re.search(r&#39;secGroupId=\&#34;(\S*)\&#34;&#39;, xml_str)
        match2 = re.search(r&#39;secGroupName=\&#34;(\S*)\&#34;&#39;, xml_str)
        security_group = [
            {
                &#34;groupId&#34;: match1.group(1),
                &#34;groupName&#34;: match2.group(1)
            }
        ]
        return security_group

    @staticmethod
    def _find_keypair_list(xml_str):
        &#34;&#34;&#34;
        sets the keypair list json from the input xml
        Args:
             xml_str            (str)  --  xml from which we want to retrieve keypair list info
        Returns:
             keypair_list       (dict) -- keypair list dict
        &#34;&#34;&#34;
        match1 = re.search(r&#39;keyPairId=\&#34;(\S*)\&#34;&#39;, xml_str)
        match2 = re.search(r&#39;keyPairName=\&#34;(\S*)\&#34;&#39;, xml_str)
        keypair_list = [
            {
                &#34;keyId&#34;: match1.group(1),
                &#34;keyName&#34;: match2.group(1)
            }
        ]
        return keypair_list

    def amazon_defaults(self, vm_to_restore, restore_option):
        &#34;&#34;&#34;
               set all the VMconversion changes need to be performed
               specfic to Amazon
               Args:
                   vm_to_restore  (str)  :  content of destination subclient object

                   restore_option (dict) :  dictionary with all VM restore options

        &#34;&#34;&#34;

        browse_result = self.vm_files_browse()
        # vs metadata from browse result
        _metadata = browse_result[1][(&#39;\\&#39; + vm_to_restore)]
        if (&#39;browseMetaData&#39; not in _metadata[&#39;advanced_data&#39;]) or \
                (&#39;virtualServerMetaData&#39; not in _metadata[&#39;advanced_data&#39;][&#39;browseMetaData&#39;]) or \
                (&#39;nics&#39; not in _metadata[&#39;advanced_data&#39;][&#39;browseMetaData&#39;][&#39;virtualServerMetaData&#39;]):
            browse_result = self.vm_files_browse(operation=&#39;find&#39;)
            _metadata = browse_result[1][(&#39;\\&#39; + vm_to_restore)]
        vs_metadata = _metadata[&#34;advanced_data&#34;][&#34;browseMetaData&#34;][&#34;virtualServerMetaData&#34;]

        restore_option[&#39;resourcePoolPath&#39;] = vs_metadata[&#39;resourcePoolPath&#39;]
        restore_option[&#39;datacenter&#39;] = vs_metadata.get(&#39;dataCenter&#39;, &#39;&#39;)
        restore_option[&#39;terminationProtected&#39;] = vs_metadata.get(&#39;terminationProtected&#39;, &#39;&#39;)
        restore_option[&#39;iamRole&#39;] = vs_metadata.get(&#39;role&#39;, &#39;&#39;)
        _security_groups = self._find_security_groups(vs_metadata[&#39;networkSecurityGroups&#39;])
        restore_option[&#39;securityGroups&#39;] = _security_groups
        _keypair_list = self._find_keypair_list(vs_metadata[&#39;loginKeyPairs&#39;])
        restore_option[&#39;keyPairList&#39;] = _keypair_list
        restore_option[&#39;esx_host&#39;] = vs_metadata.get(&#39;esxHost&#39;, &#39;&#39;)
        restore_option[&#39;datastore&#39;] = vs_metadata.get(&#39;datastore&#39;, &#39;&#39;)

        nics_list = self._json_nics_advancedRestoreOptions(vm_to_restore, restore_option)
        restore_option[&#34;nics&#34;] = nics_list

        return restore_option

    def _prepare_filelevel_restore_json(self, _file_restore_option):
        &#34;&#34;&#34;
        prepares the  file level restore json from getters
        &#34;&#34;&#34;

        if _file_restore_option is None:
            _file_restore_option = {}

        # set the setters
        self._backupset_object._instance_object._restore_association = self._subClientEntity
        request_json = self._restore_json(restore_option=_file_restore_option)
        self._json_restore_virtualServerRstOption(_file_restore_option)

        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;virtualServerRstOption&#34;] = self._virtualserver_option_restore_json

        if _file_restore_option.get(&#39;agentless&#39;):
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
                &#34;restoreOptions&#34;][&#34;virtualServerRstOption&#34;][
                &#34;fileLevelVMRestoreOption&#34;] = \
                self._json_restore_virtualServerRstOption_filelevelrestoreoption(_file_restore_option)
            request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
                &#34;restoreOptions&#34;][&#34;virtualServerRstOption&#34;][&#34;fileLevelVMRestoreOption&#34;][
                &#34;guestUserPassword&#34;] = self._json_restore_guest_password(_file_restore_option)

        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;volumeRstOption&#34;] = self._json_restore_volumeRstOption(_file_restore_option)

        return request_json

    def _prepare_disk_restore_json(self, _disk_restore_option=None):
        &#34;&#34;&#34;
        Prepare disk retsore Json with all getters

        Args:
            _disk_restore_option - dictionary with all disk restore options

            value:
                preserve_level              -  set the preserve level in restore
                unconditional_overwrite     - unconditionally overwrite the disk
                                                    in the restore path

                destination_path            - path where the disk needs to be restored
                client_name                 - client where the disk needs to be restored

                destination_vendor          - vendor id of the Hypervisor
                destination_disktype        - type of disk needs to be restored like VHDX,VHD,VMDK
                paths                 - GUID of VM from which disk needs to be restored
                                                eg:\\5F9FA60C-0A89-4BD9-9D02-C5ACB42745EA

                copy_precedence_applicable  - True if needs copy_precedence to be honored else
                                                        False

                copy_precedence            - the copy id from which browse and
                                                                restore needs to be performed

        returns:
            request_json        -complete json for performing disk Restore options

        &#34;&#34;&#34;

        if _disk_restore_option is None:
            _disk_restore_option = {}

        # set the setters
        self._backupset_object._instance_object._restore_association = self._subClientEntity
        request_json = self._restore_json(restore_option=_disk_restore_option)
        self._json_restore_virtualServerRstOption(_disk_restore_option)
        self._json_restore_diskLevelVMRestoreOption(_disk_restore_option)

        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][
            &#34;options&#34;][&#34;restoreOptions&#34;][&#34;virtualServerRstOption&#34;] = self._virtualserver_option_restore_json

        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;volumeRstOption&#34;] = self._json_restore_volumeRstOption(_disk_restore_option)

        return request_json

    def _prepare_attach_disk_restore_json(self, _disk_restore_option=None):
        &#34;&#34;&#34;
        Prepare attach disk retsore Json with all getters

        Args:
            _disk_restore_option - dictionary with all attach disk restore options

            value:
                destination_path            - path where the disk needs to be restored

                client_name                 - client where the disk needs to be restored

                destination_vendor          - vendor id of the Hypervisor

                paths                 - GUID of VM from which disk needs to be restored
                                                eg:\\5F9FA60C-0A89-4BD9-9D02-C5ACB42745EA

                copy_precedence_applicable  - True if needs copy_precedence to be honored else
                                                        False

                copy_precedence            - the copy id from which browse and
                                                                restore needs to be performed

        returns:
            request_json        -complete json for performing disk Restore options

        &#34;&#34;&#34;

        if _disk_restore_option is None:
            _disk_restore_option = {}

        # set the setters
        self._backupset_object._instance_object._restore_association = self._subClientEntity
        request_json = self._restore_json(restore_option=_disk_restore_option)
        self._set_restore_defaults(_disk_restore_option)
        self._json_restore_virtualServerRstOption(_disk_restore_option)
        self._json_vcenter_instance(_disk_restore_option)
        self._json_restore_attach_diskLevelVMRestoreOption(_disk_restore_option)
        self.set_advanced_attach_disk_restore_options(_disk_restore_option[&#39;vm_to_restore&#39;], _disk_restore_option)
        self._virtualserver_option_restore_json[&#34;diskLevelVMRestoreOption&#34;][
            &#34;advancedRestoreOptions&#34;] = self._advanced_restore_option_list
        self._advanced_restore_option_list = []
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;virtualServerRstOption&#34;] = self._virtualserver_option_restore_json
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;volumeRstOption&#34;] = self._json_restore_volumeRstOption(_disk_restore_option)
        if _disk_restore_option.get(&#39;new_instance&#39;):
            request_json = self._update_attach_disk_restore_new_instance(request_json, _disk_restore_option)
        return request_json

    @staticmethod
    def _update_attach_disk_restore_new_instance(json_to_be_edited, _disk_restore_option):
        &#34;&#34;&#34;
        Updates teh Json for attach disk restore as a new instance

        Args:
            json_to_be_edited               (dict): Request json to be edited

            _disk_restore_option:           (dict): Attach dsik restore options

        Returns:
            json_to_be_edited               (dict): Dictionary after its edited

        &#34;&#34;&#34;
        json_to_be_edited[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][
            &#39;virtualServerRstOption&#39;][&#39;diskLevelVMRestoreOption&#39;][&#39;powerOnVmAfterRestore&#39;] = True
        adv_options = json_to_be_edited[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][
            &#39;virtualServerRstOption&#39;][&#39;diskLevelVMRestoreOption&#39;][&#39;advancedRestoreOptions&#39;][0]
        del adv_options[&#39;newGuid&#39;]
        del adv_options[&#39;nics&#39;][0][&#39;destinationNetwork&#39;]
        _nic2 = adv_options[&#39;nics&#39;][0].copy()
        adv_options[&#39;nics&#39;].append(_nic2)
        adv_options[&#39;nics&#39;][1][&#39;networkName&#39;] = &#39;New Network Interface&#39;
        _region = adv_options[&#39;esxHost&#39;]
        for disks in adv_options[&#39;disks&#39;]:
            disks[&#39;availabilityZone&#39;] = _region
        adv_options[&#39;guestOperatingSystemId&#39;] = _disk_restore_option.get(&#39;os_id&#39;, 0)
        return json_to_be_edited

    def _prepare_fullvm_restore_json(self, restore_option=None):
        &#34;&#34;&#34;
        Prepare Full VM restore Json with all getters

        Args:
            restore_option - dictionary with all VM restore options

        value:
            preserve_level              - set the preserve level in restore

            unconditional_overwrite     - unconditionally overwrite the disk
                                          in the restore path

            destination_path            - path where the disk needs to be
                                          restored

            client_name                 - client where the disk needs to be
                                          restored

            destination_vendor          - vendor id of the Hypervisor

            destination_disktype        - type of disk needs to be restored
                                          like VHDX,VHD,VMDK

            source_item                 - GUID of VM from which disk needs to
                                          be restored
                                          eg:
                                          \\5F9FA60C-0A89-4BD9-9D02-C5ACB42745EA

            copy_precedence_applicable  - True if needs copy_precedence to
                                          be honoured else False

            copy_precedence            - the copy id from which browse and
                                          restore needs to be performed

            power_on                    - power on the VM after restore

            add_to_failover             - Register the VM to Failover Cluster

            datastore                   - Datastore where the VM needs to be
                                          restored

            disks   (list of dict)      - list with dict for each disk in VM
                                            eg: [{
                                                    name:&#34;disk1.vmdk&#34;
                                                    datastore:&#34;local&#34;
                                                }
                                                {
                                                    name:&#34;disk2.vmdk&#34;
                                                    datastore:&#34;local1&#34;
                                                }
                                            ]
            guid                        - GUID of the VM needs to be restored
            new_name                    - New name for the VM to be restored
            esx_host                    - esx_host or client name where it need
                                          to be restored
            name                        - name of the VM to be restored

        returns:
              request_json        -complete json for perfomring Full VM Restore
                                   options

        &#34;&#34;&#34;

        if restore_option is None:
            restore_option = {}
        restore_option[&#39;paths&#39;] = []

        if &#34;destination_vendor&#34; not in restore_option:
            restore_option[&#34;destination_vendor&#34;] = \
                self._backupset_object._instance_object._vendor_id

        if restore_option[&#39;copy_precedence&#39;]:
            restore_option[&#39;copy_precedence_applicable&#39;] = True

        # set all the restore defaults
        self._set_restore_defaults(restore_option)

        # set the setters
        self._backupset_object._instance_object._restore_association = self._subClientEntity
        self._json_restore_virtualServerRstOption(restore_option)
        self._json_restore_diskLevelVMRestoreOption(restore_option)
        self._json_vcenter_instance(restore_option)

        for _each_vm_to_restore in restore_option[&#39;vm_to_restore&#39;]:
            if not restore_option[&#34;in_place&#34;]:
                if &#39;disk_type&#39; in restore_option and restore_option[&#39;disk_type&#39;]:
                    restore_option[&#39;restoreAsManagedVM&#39;] = restore_option[&#39;disk_type&#39;][
                        _each_vm_to_restore]
                if (&#34;restore_new_name&#34; in restore_option and
                        restore_option[&#34;restore_new_name&#34;] is not None):
                    if len(restore_option[&#39;vm_to_restore&#39;]) == 1:
                        restore_option[&#34;new_name&#34;] = restore_option[&#34;restore_new_name&#34;]
                    else:
                        restore_option[&#34;new_name&#34;] = restore_option[
                                                         &#34;restore_new_name&#34;] + _each_vm_to_restore
                else:
                    restore_option[&#34;new_name&#34;] = &#34;del&#34; + _each_vm_to_restore
            else:
                restore_option[&#34;new_name&#34;] = _each_vm_to_restore
            self.set_advanced_vm_restore_options(_each_vm_to_restore, restore_option)

        # prepare json
        request_json = self._restore_json(restore_option=restore_option)
        self._virtualserver_option_restore_json[&#34;diskLevelVMRestoreOption&#34;][
            &#34;advancedRestoreOptions&#34;] = self._advanced_restore_option_list
        self._advanced_restore_option_list = []
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;virtualServerRstOption&#34;] = self._virtualserver_option_restore_json
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;volumeRstOption&#34;] = self._json_restore_volumeRstOption(
            restore_option)
        if restore_option.get(&#39;v2_details&#39;) and len(restore_option.get(&#39;vm_to_restore&#39;, &#39;&#39;)) &lt;= 1:
            request_json = self._full_vm_restore_update_json_for_v2(request_json, restore_option.get(&#39;v2_details&#39;))

        return request_json

    @staticmethod
    def _full_vm_restore_update_json_for_v2(json_to_be_edited, v2_details):
        &#34;&#34;&#34;
        Update the final request JSON to match wth the v2 vm
        Args:
            json_to_be_edited               (dict): Final restore JSON for the restore without v2 subclient details

            v2_details                      (dict): v2 vm subclient details
                                   eg: {
                                            &#39;clientName&#39;: &#39;vm_client1&#39;,
                                            &#39;instanceName&#39;: &#39;VMInstance&#39;,
                                            &#39;displayName&#39;: &#39;vm_client1&#39;,
                                            &#39;backupsetId&#39;: 12,
                                            &#39;instanceId&#39;: 2,
                                            &#39;subclientId&#39;: 123,
                                            &#39;clientId&#39;: 1234,
                                            &#39;appName&#39;: &#39;Virtual Server&#39;,
                                            &#39;backupsetName&#39;: &#39;defaultBackupSet&#39;,
                                            &#39;applicationId&#39;: 106,
                                            &#39;subclientName&#39;: &#39;default&#39;
                                        }

        Returns:
            json_to_be_edited        -complete json for performing Full VM Restore
                                        options with v2 subclient details

        &#34;&#34;&#34;
        json_to_be_edited[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;clientName&#39;] = v2_details.get(&#39;clientName&#39;)
        json_to_be_edited[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;clientId&#39;] = v2_details.get(&#39;clientId&#39;)
        json_to_be_edited[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;instanceName&#39;] = v2_details.get(&#39;instanceName&#39;)
        json_to_be_edited[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;instanceId&#39;] = v2_details.get(&#39;instanceId&#39;)
        json_to_be_edited[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;displayName&#39;] = v2_details.get(&#39;displayName&#39;)
        json_to_be_edited[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;backupsetName&#39;] = v2_details.get(&#39;backupsetName&#39;)
        json_to_be_edited[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;backupsetId&#39;] = v2_details.get(&#39;backupsetId&#39;)
        json_to_be_edited[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;subclientName&#39;] = v2_details.get(&#39;subclientName&#39;)
        json_to_be_edited[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;subclientId&#39;] = v2_details.get(&#39;subclientId&#39;)
        json_to_be_edited[&#39;taskInfo&#39;][&#39;subTasks&#39;][0][&#39;options&#39;][&#39;restoreOptions&#39;][&#39;browseOption&#39;][&#39;backupset&#39;][
            &#39;clientName&#39;] = v2_details.get(&#39;clientName&#39;)
        del json_to_be_edited[&#39;taskInfo&#39;][&#39;associations&#39;][0][&#39;subclientGUID&#39;]
        return json_to_be_edited

    def backup(self,
               backup_level=&#34;Incremental&#34;,
               incremental_backup=False,
               incremental_level=&#39;BEFORE_SYNTH&#39;,
               collect_metadata=False,
               advanced_options=None,
               schedule_pattern=None):
        &#34;&#34;&#34;Runs a backup job for the subclient of the level specified.

            Args:
                backup_level            (str)   --  level of backup the user wish to run
                                                    Full / Incremental / Differential /
                                                    Synthetic_full

                incremental_backup      (bool)  --  run incremental backup
                                                    only applicable in case of Synthetic_full backup

                incremental_level       (str)   --  run incremental backup before/after synthetic full
                                                    BEFORE_SYNTH / AFTER_SYNTH
                                                    only applicable in case of Synthetic_full backup

                collect_metadata        (bool)  --  Collect Meta data for the backup

                advanced_options       (dict)  --  advanced backup options to be included while
                                                    making the request
                    options:
                        create_backup_copy_immediately  --  Run Backup copy just after snap backup
                        backup_copy_type                --  Backup Copy level using storage policy
                                                            or subclient rule

                schedule_pattern (dict) -- scheduling options to be included for the task

                        Please refer schedules.schedulePattern.createSchedule()
                                                                    doc for the types of Jsons

            Returns:
                object - instance of the Job class for this backup job if its an immediate Job

                         instance of the Schedule class for the backup job if its a scheduled Job

            Raises:
                SDKException:
                    if backup level specified is not correct

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;

        backup_level = backup_level.lower()
        if backup_level not in [&#39;full&#39;, &#39;incremental&#39;,
                                &#39;differential&#39;, &#39;synthetic_full&#39;]:
            raise SDKException(&#39;Subclient&#39;, &#39;103&#39;)

        if advanced_options or schedule_pattern:
            request_json = self._backup_json(
                backup_level=backup_level,
                incremental_backup=incremental_backup,
                incremental_level=incremental_level,
                advanced_options=advanced_options,
                schedule_pattern=schedule_pattern
            )

            backup_service = self._commcell_object._services[&#39;CREATE_TASK&#39;]

            flag, response = self._commcell_object._cvpysdk_object.make_request(
                &#39;POST&#39;, backup_service, request_json
            )

            return self._process_backup_response(flag, response)

        else:
            return super(VirtualServerSubclient, self).backup(backup_level=backup_level,
                                                              incremental_backup=incremental_backup,
                                                              incremental_level=incremental_level,
                                                              collect_metadata=collect_metadata)

    def _advanced_backup_options(self, options):
        &#34;&#34;&#34;Generates the advanced backup options dict

            Args:
                options         (dict)  --  advanced backup options that are to be included
                                            in the request
                    create_backup_copy_immediately  --  Run Backup copy just after snap backup
                    backup_copy_type                --  Backup Copy level using storage policy
                                                        or subclient rule

            Returns:
            (dict)                      --  generated advanced options dict
        &#34;&#34;&#34;
        final_dict = super(VirtualServerSubclient, self)._advanced_backup_options(options)

        if &#39;create_backup_copy_immediately&#39; in options:
            final_dict[&#39;dataOpt&#39;] = {
                &#39;createBackupCopyImmediately&#39;: options.get(&#39;create_backup_copy_immediately&#39;, False),
                &#39;backupCopyType&#39;: options.get(&#39;backup_copy_type&#39;, &#39;USING_STORAGE_POLICY_RULE&#39;)
            }

        return final_dict

    def _prepare_blr_json(self, restore_option):

        if &#34;destination_vendor&#34; not in restore_option:
            restore_option[&#34;destination_vendor&#34;] = self._backupset_object._instance_object._vendor_id

        # set all the restore defaults
        self._set_restore_defaults(restore_option)

        # Restore Options
        self._backupset_object._instance_object._restore_association = self._subClientEntity

        self._json_restore_virtualServerRstOption(restore_option)

        # Virtual Server RST option
        self._allocation_policy_json(restore_option)
        self._json_restore_diskLevelVMRestoreOption(restore_option)
        self._json_vcenter_instance(restore_option)
        _vm_browse_path_nodes_json = list()
        # Disk Level  VM Restore Options
        self._json_restore_default_restore_settings(restore_option)
        new_name = restore_option[&#34;new_name&#34;]
        for _each_vm_to_restore in restore_option[&#39;vm_to_restore&#39;]:
            if restore_option[&#34;prefix&#34;] == 1:
                restore_option[&#34;new_name&#34;] = &#34;{}{}&#34;.format(new_name, _each_vm_to_restore)
            else:
                restore_option[&#34;new_name&#34;] = &#34;{}{}&#34;.format(_each_vm_to_restore, new_name)
            self._set_advanced_vm_restore_options_blr(_each_vm_to_restore, restore_option)
            _vm_browse_path_nodes_json.append(self._vm_browse_path_nodes())
        self._virtualserver_option_restore_json[&#34;diskLevelVMRestoreOption&#34;][&#34;advancedRestoreOptions&#34;] = self._advanced_restore_option_list[0]

        # prepare json
        request_json = self._restore_json(restore_option=restore_option)
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;vmBrowsePathNodes&#34;] = _vm_browse_path_nodes_json
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;virtualServerRstOption&#34;] = self._virtualserver_option_restore_json
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;volumeRstOption&#34;] = self._json_restore_volumeRstOption(
            restore_option)
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;subTask&#34;][&#34;operationType&#34;] = 1007
        request_json[&#34;taskInfo&#34;][&#34;associations&#34;][0][&#34;_type_&#34;] = 6

        self._advanced_restore_option_list = list()
        request_json[&#34;TMMsg_TaskInfo&#34;] = request_json[&#34;taskInfo&#34;]
        del request_json[&#34;taskInfo&#34;]
        return request_json

    def _vm_browse_path_nodes(self):
        return {
            &#34;browsePath&#34;: self._advanced_restore_option_list[-1][&#34;name&#34;],
            &#34;vmGUID&#34;: self._advanced_restore_option_list[-1][&#34;guid&#34;],
            &#34;esxHost&#34;: self._advanced_restore_option_list[-1][&#34;esxHost&#34;],
            &#34;datastore&#34;: self._advanced_restore_option_list[-1][&#34;Datastore&#34;],
            &#34;resourcePoolPath&#34;: self._advanced_restore_option_list[-1][&#34;resourcePoolPath&#34;],
            &#34;vmDataStore&#34;: self._advanced_restore_option_list[-1][&#34;Datastore&#34;],
            &#34;vmEsxHost&#34;: self._advanced_restore_option_list[-1][&#34;esxHost&#34;],
            &#34;vmeResourcePoolPath&#34;: self._advanced_restore_option_list[-1][&#34;resourcePoolPath&#34;],
            &#34;isMetadataAvaiable&#34;: &#34;0&#34;
        }

    def _json_restore_default_restore_settings(self, restore_option):
        &#34;&#34;&#34;setter for  the default restore settings in block level json&#34;&#34;&#34;

        if not isinstance(restore_option, dict):
            raise SDKException(&#39;Subclient&#39;, &#39;101&#39;)

        self._virtualserver_option_restore_json[&#34;diskLevelVMRestoreOption&#34;][&#34;defaultRestoreSettings&#34;] = {
            &#34;esxHost&#34;: restore_option[&#34;esx_host&#34;],
            &#34;Datastore&#34;: restore_option.get(&#34;datastore&#34;, &#34;&#34;),
            &#34;resourcePoolPath&#34;: restore_option.get(&#34;resource_pool&#34;, &#34;/&#34;),
            &#34;blrRecoveryOpts&#34;: self._json_restore_blrRecoveryOpts(restore_option)
        }

    def _allocation_policy_json(self, restore_option):
        self._virtualserver_option_restore_json[&#34;allocationPolicy&#34;] = {
            &#34;flags&#34;: &#34;&#34;,
            &#34;instanceEntity&#34;: {
                &#34;flags&#34;: &#34;&#34;
            },
            &#34;policyType&#34;: &#34;0&#34;,
            &#34;region&#34;: {
                &#34;flags&#34;: &#34;&#34;
            },
            &#34;vmAllocPolicyId&#34;: restore_option[&#34;target_id&#34;],
            &#34;vmAllocPolicyName&#34;: restore_option[&#34;target_name&#34;]
        }

    def _prepare_blr_xml(self, restore_option):
        request_json = self._prepare_blr_json(restore_option)

        xml_string = xmltodict.unparse(request_json)
        plans = Plans(self._commcell_object)

        return (
            &#34;&#34;&#34;&lt;?xml version=&#34;1.0&#34; encoding=&#34;UTF-8&#34;?&gt;&lt;EVGui_SetVMBlockLevelReplicationReq subclientId=&#34;{5}&#34; opType=&#34;3&#34;&gt;
            &lt;blockLevelReplicationTaskXML&gt;&lt;![CDATA[{0}]]&gt;&lt;/blockLevelReplicationTaskXML&gt;
            &lt;subClientProperties&gt;
            &lt;subClientEntity clientId=&#34;{1}&#34; applicationId=&#34;106&#34; instanceId=&#34;{2}&#34; backupsetId=&#34;{3}&#34;/&gt;
            &lt;planEntity planId=&#34;{4}&#34;/&gt;
            &lt;/subClientProperties&gt;
            &lt;/EVGui_SetVMBlockLevelReplicationReq&gt;
        &#34;&#34;&#34;.format(
                xml_string,
                self._client_object.client_id,
                self._instance_object.instance_id,
                self._backupset_object.backupset_id,
                plans.all_plans[restore_option[&#34;plan_name&#34;].lower()],
                self._subclient_id))

    def _set_advanced_vm_restore_options_blr(self, vm_to_restore, restore_option):
        &#34;&#34;&#34;set the advanced restore options for all vm in restore

        vm_to_restore : Name of the VM to restore

        restore_option: restore options that need to be set for advanced restore option

        &#34;&#34;&#34;

        vm_names, vm_ids = self._get_vm_ids_and_names_dict()

        # populate restore source item
        restore_option[&#39;name&#39;] = vm_to_restore
        restore_option[&#39;guid&#39;] = vm_ids[vm_to_restore]
        restore_option[&#39;paths&#39;].append(&#34;\\&#34; + vm_ids[vm_to_restore])
        restore_option[&#34;resourcePoolPath&#34;] = &#34;/&#34;

        restore_option[&#34;nics&#34;] = {
            &#34;sourceNetwork&#34;: restore_option[&#34;source_network&#34;],
            &#34;destinationNetwork&#34;: restore_option[&#34;destination_network&#34;]
        }
        temp_dict = self._json_restore_advancedRestoreOptions(restore_option)
        self._advanced_restore_option_list.append(temp_dict)

    def _prepare_preview_json(self):
        &#34;&#34;&#34;Prepares the JSON for previewing subclient contents

        Returns:
            JSON - for previewing subclient contents

        &#34;&#34;&#34;
        return(
            {
                &#34;appId&#34;: {
                    &#34;subclientId&#34;: int(self.subclient_id),
                    &#34;clientId&#34;: int(self._client_object.client_id),
                    &#34;instanceId&#34;: int(self._instance_object.instance_id),
                    &#34;backupsetId&#34;: int(self._backupset_object.backupset_id),
                    &#34;apptypeId&#34;: int(self._agent_object.agent_id)
                },
                &#34;filterEntity&#34;: self._vmFilter,
                &#34;contentEntity&#34;: self._vmContent
            }
        )

    def _parse_preview_vms(self, subclient_vm_list):
        &#34;&#34;&#34;Parses the vm list from the preview vm response

        Returns:
            _vm_list        (list)  - List of the vms as the subclient content
        &#34;&#34;&#34;
        _vm_list = []
        for vm in subclient_vm_list:
            _vm_list.append(vm[&#39;name&#39;])
        return _vm_list

    def preview_content(self):
        &#34;&#34;&#34;
        Preview the subclient and get the content

        Returns:
            list       - List of the vms as the subclient content

        &#34;&#34;&#34;
        preview = self._commcell_object._services[&#39;PREVIEW&#39;]
        preview_json = self._prepare_preview_json()

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, preview, preview_json
        )
        if flag and &#39;scList&#39; in response.json():
            return self._parse_preview_vms(response.json()[&#39;scList&#39;])
        else:
            raise SDKException(
                &#39;Subclient&#39;,
                &#39;102&#39;,
                self._update_response_(
                    response.text))</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.subclient.Subclient" href="../subclient.html#cvpysdk.subclient.Subclient">Subclient</a></li>
</ul>
<h3>Subclasses</h3>
<ul class="hlist">
<li><a title="cvpysdk.subclients.virtualserver.alibaba_cloud.AlibabaCloudVirtualServerSubclient" href="virtualserver/alibaba_cloud.html#cvpysdk.subclients.virtualserver.alibaba_cloud.AlibabaCloudVirtualServerSubclient">AlibabaCloudVirtualServerSubclient</a></li>
<li><a title="cvpysdk.subclients.virtualserver.amazon_web_services.AmazonVirtualServerSubclient" href="virtualserver/amazon_web_services.html#cvpysdk.subclients.virtualserver.amazon_web_services.AmazonVirtualServerSubclient">AmazonVirtualServerSubclient</a></li>
<li><a title="cvpysdk.subclients.virtualserver.azure.AzureSubclient" href="virtualserver/azure.html#cvpysdk.subclients.virtualserver.azure.AzureSubclient">AzureSubclient</a></li>
<li><a title="cvpysdk.subclients.virtualserver.azure_resource_manager.AzureRMSubclient" href="virtualserver/azure_resource_manager.html#cvpysdk.subclients.virtualserver.azure_resource_manager.AzureRMSubclient">AzureRMSubclient</a></li>
<li><a title="cvpysdk.subclients.virtualserver.azure_stack.AzureStackSubclient" href="virtualserver/azure_stack.html#cvpysdk.subclients.virtualserver.azure_stack.AzureStackSubclient">AzureStackSubclient</a></li>
<li><a title="cvpysdk.subclients.virtualserver.fusioncompute.FusionComputeVirtualServerSubclient" href="virtualserver/fusioncompute.html#cvpysdk.subclients.virtualserver.fusioncompute.FusionComputeVirtualServerSubclient">FusionComputeVirtualServerSubclient</a></li>
<li><a title="cvpysdk.subclients.virtualserver.google_cloud_platform.GooglecloudVirtualServerSubclient" href="virtualserver/google_cloud_platform.html#cvpysdk.subclients.virtualserver.google_cloud_platform.GooglecloudVirtualServerSubclient">GooglecloudVirtualServerSubclient</a></li>
<li><a title="cvpysdk.subclients.virtualserver.hyperv.HyperVVirtualServerSubclient" href="virtualserver/hyperv.html#cvpysdk.subclients.virtualserver.hyperv.HyperVVirtualServerSubclient">HyperVVirtualServerSubclient</a></li>
<li><a title="cvpysdk.subclients.virtualserver.kubernetes.KubernetesVirtualServerSubclient" href="virtualserver/kubernetes.html#cvpysdk.subclients.virtualserver.kubernetes.KubernetesVirtualServerSubclient">KubernetesVirtualServerSubclient</a></li>
<li><a title="cvpysdk.subclients.virtualserver.null.NullSubclient" href="virtualserver/null.html#cvpysdk.subclients.virtualserver.null.NullSubclient">NullSubclient</a></li>
<li><a title="cvpysdk.subclients.virtualserver.nutanix_ahv.nutanixsubclient" href="virtualserver/nutanix_ahv.html#cvpysdk.subclients.virtualserver.nutanix_ahv.nutanixsubclient">nutanixsubclient</a></li>
<li><a title="cvpysdk.subclients.virtualserver.openstack.OpenStackVirtualServerSubclient" href="virtualserver/openstack.html#cvpysdk.subclients.virtualserver.openstack.OpenStackVirtualServerSubclient">OpenStackVirtualServerSubclient</a></li>
<li><a title="cvpysdk.subclients.virtualserver.oracle_cloud.OracleCloudVirtualServerSubclient" href="virtualserver/oracle_cloud.html#cvpysdk.subclients.virtualserver.oracle_cloud.OracleCloudVirtualServerSubclient">OracleCloudVirtualServerSubclient</a></li>
<li><a title="cvpysdk.subclients.virtualserver.oracle_cloud_infrastructure.OCIVirtualServerSubclient" href="virtualserver/oracle_cloud_infrastructure.html#cvpysdk.subclients.virtualserver.oracle_cloud_infrastructure.OCIVirtualServerSubclient">OCIVirtualServerSubclient</a></li>
<li><a title="cvpysdk.subclients.virtualserver.oraclevm.OracleVMVirtualServerSubclient" href="virtualserver/oraclevm.html#cvpysdk.subclients.virtualserver.oraclevm.OracleVMVirtualServerSubclient">OracleVMVirtualServerSubclient</a></li>
<li><a title="cvpysdk.subclients.virtualserver.proxmox_ve.ProxmoxSubclient" href="virtualserver/proxmox_ve.html#cvpysdk.subclients.virtualserver.proxmox_ve.ProxmoxSubclient">ProxmoxSubclient</a></li>
<li><a title="cvpysdk.subclients.virtualserver.red_hat_openshift.OpenshiftSubclient" href="virtualserver/red_hat_openshift.html#cvpysdk.subclients.virtualserver.red_hat_openshift.OpenshiftSubclient">OpenshiftSubclient</a></li>
<li><a title="cvpysdk.subclients.virtualserver.red_hat_virtualization.RhevVirtualServerSubclient" href="virtualserver/red_hat_virtualization.html#cvpysdk.subclients.virtualserver.red_hat_virtualization.RhevVirtualServerSubclient">RhevVirtualServerSubclient</a></li>
<li><a title="cvpysdk.subclients.virtualserver.vcloud_director.VcloudVirtualServerSubclient" href="virtualserver/vcloud_director.html#cvpysdk.subclients.virtualserver.vcloud_director.VcloudVirtualServerSubclient">VcloudVirtualServerSubclient</a></li>
<li><a title="cvpysdk.subclients.virtualserver.vmware.VMWareVirtualServerSubclient" href="virtualserver/vmware.html#cvpysdk.subclients.virtualserver.vmware.VMWareVirtualServerSubclient">VMWareVirtualServerSubclient</a></li>
<li><a title="cvpysdk.subclients.virtualserver.xen.Xen" href="virtualserver/xen.html#cvpysdk.subclients.virtualserver.xen.Xen">Xen</a></li>
</ul>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.subclients.vssubclient.VirtualServerSubclient.disk_pattern"><code class="name">var <span class="ident">disk_pattern</span></code></dt>
<dd>
<div class="desc"><p>stores the disk pattern of all hypervisors</p></div>
</dd>
</dl>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.subclients.vssubclient.VirtualServerSubclient.cbtvalue"><code class="name">var <span class="ident">cbtvalue</span></code></dt>
<dd>
<div class="desc"><p>Get CBT value for given subclient.</p>
<h2 id="returns">Returns</h2>
<p>(Boolean)
True/False</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/vssubclient.py#L731-L740" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def cbtvalue(self):
    &#34;&#34;&#34;
    Get CBT value for given subclient.

    Returns:
        (Boolean)    True/False

    &#34;&#34;&#34;
    return self._subclient_properties.get(&#39;vsaSubclientProp&#39;, {}).get(&#34;useChangedTrackingOnVM&#34;, False)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.vssubclient.VirtualServerSubclient.content"><code class="name">var <span class="ident">content</span></code></dt>
<dd>
<div class="desc"><p>Gets the appropriate content from the Subclient relevant to the user.</p>
<h2 id="returns">Returns</h2>
<p>list - list of content associated with the subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/vssubclient.py#L253-L267" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def content(self):
    &#34;&#34;&#34;Gets the appropriate content from the Subclient relevant to the user.

        Returns:
            list - list of content associated with the subclient

    &#34;&#34;&#34;
    content = []
    subclient_content = self._vmContent

    if &#39;children&#39; in subclient_content:
        children = subclient_content[&#39;children&#39;]
        content = self._get_content_list(children)
    return content</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.vssubclient.VirtualServerSubclient.index_server"><code class="name">var <span class="ident">index_server</span></code></dt>
<dd>
<div class="desc"><p>Returns the index server client set for the subclient. None if no Index Server is set</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/vssubclient.py#L625-L642" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def index_server(self):
    &#34;&#34;&#34;Returns the index server client set for the subclient. None if no Index Server is set&#34;&#34;&#34;

    if &#39;indexSettings&#39; not in self._commonProperties:
        return None

    index_settings = self._commonProperties[&#39;indexSettings&#39;]
    index_server = None

    if (&#39;currentIndexServer&#39; in index_settings and
            &#39;clientName&#39; in index_settings[&#39;currentIndexServer&#39;]):
        index_server = index_settings[&#39;currentIndexServer&#39;][&#39;clientName&#39;]

    if index_server is None:
        return None

    return self._commcell_object.clients.get(index_server)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.vssubclient.VirtualServerSubclient.instance_proxy"><code class="name">var <span class="ident">instance_proxy</span></code></dt>
<dd>
<div class="desc"><p>Gets the proxy at instance level</p>
<h2 id="returns">Returns</h2>
<p>string
(string) :
Proxy at instane</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/vssubclient.py#L279-L287" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def instance_proxy(self):
    &#34;&#34;&#34;
    Gets the proxy at instance level

    Returns:
            string          (string) :      Proxy at instane
    &#34;&#34;&#34;
    return self._proxyClient.get(&#39;clientName&#39;, None)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.vssubclient.VirtualServerSubclient.live_sync"><code class="name">var <span class="ident">live_sync</span></code></dt>
<dd>
<div class="desc"><p>Returns the instance of the VSALiveSync class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/vssubclient.py#L616-L623" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def live_sync(self):
    &#34;&#34;&#34;Returns the instance of the VSALiveSync class&#34;&#34;&#34;
    if not self._live_sync:
        from .virtualserver.livesync.vsa_live_sync import VsaLiveSync
        self._live_sync = VsaLiveSync(self)

    return self._live_sync</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.vssubclient.VirtualServerSubclient.metadata"><code class="name">var <span class="ident">metadata</span></code></dt>
<dd>
<div class="desc"><p>Get if collect files/metadata value for given subclient.
Returns status as True/False (string)
Default: False for subclient which doesnt have the property</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/vssubclient.py#L342-L354" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def metadata(self):
    &#34;&#34;&#34;
        Get if collect files/metadata value for given subclient.
        Returns status as True/False (string)
        Default: False for subclient which doesnt have the property
    &#34;&#34;&#34;
    collectdetails = r&#39;collectFileDetails&#39;
    if collectdetails in self._vsaSubclientProp:
        vsasubclient_collect_details = self._vsaSubclientProp[collectdetails]
    else:
        vsasubclient_collect_details = False
    return vsasubclient_collect_details</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.vssubclient.VirtualServerSubclient.quiesce_file_system"><code class="name">var <span class="ident">quiesce_file_system</span></code></dt>
<dd>
<div class="desc"><p>Gets the quiesce value set for the vsa subclient</p>
<h2 id="returns">Returns</h2>
<p>(Boolean)
True/False</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/vssubclient.py#L668-L676" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def quiesce_file_system(self):
    &#34;&#34;&#34;
        Gets the quiesce value set for the vsa subclient

    Returns:
        (Boolean)    True/False
    &#34;&#34;&#34;
    return self._vsaSubclientProp.get(&#39;quiesceGuestFileSystemAndApplications&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.vssubclient.VirtualServerSubclient.snapshot_storage_type"><code class="name">var <span class="ident">snapshot_storage_type</span></code></dt>
<dd>
<div class="desc"><p>Gets the snapshot storage type set for the vsa subclient</p>
<h2 id="returns">Returns</h2>
<p>(Boolean)
True/False</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/vssubclient.py#L691-L699" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def snapshot_storage_type(self):
    &#34;&#34;&#34;
        Gets the snapshot storage type set for the vsa subclient

    Returns:
        (Boolean)    True/False
    &#34;&#34;&#34;
    return self._vsaSubclientProp.get(&#39;snapshotStorageType&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.vssubclient.VirtualServerSubclient.subclient_proxy"><code class="name">var <span class="ident">subclient_proxy</span></code></dt>
<dd>
<div class="desc"><p>Gets the List of proxies at the Subclient</p>
<h2 id="returns">Returns</h2>
<p>list
(list) :
Proxies at the subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/vssubclient.py#L269-L277" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def subclient_proxy(self):
    &#34;&#34;&#34;
        Gets the List of proxies at the Subclient

        Returns:
                list         (list) :    Proxies at the subclient
    &#34;&#34;&#34;
    return self._get_subclient_proxies()</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.vssubclient.VirtualServerSubclient.vm_diskfilter"><code class="name">var <span class="ident">vm_diskfilter</span></code></dt>
<dd>
<div class="desc"><p>Gets the appropriate Diskfilter from the Subclient relevant to the user.</p>
<h2 id="returns">Returns</h2>
<p>list - list of Diskfilter associated with the subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/vssubclient.py#L304-L340" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def vm_diskfilter(self):
    &#34;&#34;&#34;Gets the appropriate Diskfilter from the Subclient relevant to the user.

        Returns:
            list - list of Diskfilter associated with the subclient

    &#34;&#34;&#34;
    vm_diskfilter = []
    if self._vmDiskFilter is not None:
        subclient_diskfilter = self._vmDiskFilter

        if &#39;filters&#39; in subclient_diskfilter:
            filters = subclient_diskfilter[&#39;filters&#39;]

            for child in filters:
                filter_type_id = str(child[&#39;filterType&#39;])
                filter_type = self.filter_types[str(child[&#39;filterType&#39;])]
                vm_id = child[&#39;vmGuid&#39;] if &#39;vmGuid&#39; in child else None
                filter_name = child[&#39;filter&#39;]
                value = child[&#39;value&#39;]

                temp_dict = {
                    &#39;filter&#39;: filter_name,
                    &#39;filterType&#39;: filter_type,
                    &#39;vmGuid&#39;: vm_id,
                    &#39;filterTypeId&#39;: filter_type_id,
                    &#39;value&#39;:value
                }

                vm_diskfilter.append(temp_dict)
    else:
        vm_diskfilter = self._vmDiskFilter

    if len(vm_diskfilter) == 0:
        vm_diskfilter = None
    return vm_diskfilter</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.vssubclient.VirtualServerSubclient.vm_filter"><code class="name">var <span class="ident">vm_filter</span></code></dt>
<dd>
<div class="desc"><p>Gets the appropriate filter from the Subclient relevant to the user.</p>
<h2 id="returns">Returns</h2>
<p>list - list of filter associated with the subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/vssubclient.py#L289-L302" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def vm_filter(self):
    &#34;&#34;&#34;Gets the appropriate filter from the Subclient relevant to the user.

        Returns:
            list - list of filter associated with the subclient
    &#34;&#34;&#34;
    vm_filter = []
    if self._vmFilter:
        subclient_filter = self._vmFilter
        if &#39;children&#39; in subclient_filter:
            children = subclient_filter[&#39;children&#39;]
            vm_filter = self._get_content_list(children)
    return vm_filter</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.subclients.vssubclient.VirtualServerSubclient.amazon_defaults"><code class="name flex">
<span>def <span class="ident">amazon_defaults</span></span>(<span>self, vm_to_restore, restore_option)</span>
</code></dt>
<dd>
<div class="desc"><p>set all the VMconversion changes need to be performed
specfic to Amazon</p>
<h2 id="args">Args</h2>
<p>vm_to_restore
(str)
:
content of destination subclient object</p>
<p>restore_option (dict) :
dictionary with all VM restore options</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/vssubclient.py#L2495-L2530" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def amazon_defaults(self, vm_to_restore, restore_option):
    &#34;&#34;&#34;
           set all the VMconversion changes need to be performed
           specfic to Amazon
           Args:
               vm_to_restore  (str)  :  content of destination subclient object

               restore_option (dict) :  dictionary with all VM restore options

    &#34;&#34;&#34;

    browse_result = self.vm_files_browse()
    # vs metadata from browse result
    _metadata = browse_result[1][(&#39;\\&#39; + vm_to_restore)]
    if (&#39;browseMetaData&#39; not in _metadata[&#39;advanced_data&#39;]) or \
            (&#39;virtualServerMetaData&#39; not in _metadata[&#39;advanced_data&#39;][&#39;browseMetaData&#39;]) or \
            (&#39;nics&#39; not in _metadata[&#39;advanced_data&#39;][&#39;browseMetaData&#39;][&#39;virtualServerMetaData&#39;]):
        browse_result = self.vm_files_browse(operation=&#39;find&#39;)
        _metadata = browse_result[1][(&#39;\\&#39; + vm_to_restore)]
    vs_metadata = _metadata[&#34;advanced_data&#34;][&#34;browseMetaData&#34;][&#34;virtualServerMetaData&#34;]

    restore_option[&#39;resourcePoolPath&#39;] = vs_metadata[&#39;resourcePoolPath&#39;]
    restore_option[&#39;datacenter&#39;] = vs_metadata.get(&#39;dataCenter&#39;, &#39;&#39;)
    restore_option[&#39;terminationProtected&#39;] = vs_metadata.get(&#39;terminationProtected&#39;, &#39;&#39;)
    restore_option[&#39;iamRole&#39;] = vs_metadata.get(&#39;role&#39;, &#39;&#39;)
    _security_groups = self._find_security_groups(vs_metadata[&#39;networkSecurityGroups&#39;])
    restore_option[&#39;securityGroups&#39;] = _security_groups
    _keypair_list = self._find_keypair_list(vs_metadata[&#39;loginKeyPairs&#39;])
    restore_option[&#39;keyPairList&#39;] = _keypair_list
    restore_option[&#39;esx_host&#39;] = vs_metadata.get(&#39;esxHost&#39;, &#39;&#39;)
    restore_option[&#39;datastore&#39;] = vs_metadata.get(&#39;datastore&#39;, &#39;&#39;)

    nics_list = self._json_nics_advancedRestoreOptions(vm_to_restore, restore_option)
    restore_option[&#34;nics&#34;] = nics_list

    return restore_option</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.vssubclient.VirtualServerSubclient.backup"><code class="name flex">
<span>def <span class="ident">backup</span></span>(<span>self, backup_level='Incremental', incremental_backup=False, incremental_level='BEFORE_SYNTH', collect_metadata=False, advanced_options=None, schedule_pattern=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Runs a backup job for the subclient of the level specified.</p>
<h2 id="args">Args</h2>
<p>backup_level
(str)
&ndash;
level of backup the user wish to run
Full / Incremental / Differential /
Synthetic_full</p>
<p>incremental_backup
(bool)
&ndash;
run incremental backup
only applicable in case of Synthetic_full backup</p>
<p>incremental_level
(str)
&ndash;
run incremental backup before/after synthetic full
BEFORE_SYNTH / AFTER_SYNTH
only applicable in case of Synthetic_full backup</p>
<p>collect_metadata
(bool)
&ndash;
Collect Meta data for the backup</p>
<p>advanced_options
(dict)
&ndash;
advanced backup options to be included while
making the request
options:
create_backup_copy_immediately
&ndash;
Run Backup copy just after snap backup
backup_copy_type
&ndash;
Backup Copy level using storage policy
or subclient rule</p>
<p>schedule_pattern (dict) &ndash; scheduling options to be included for the task</p>
<pre><code>    Please refer schedules.schedulePattern.createSchedule()
                                                doc for the types of Jsons
</code></pre>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this backup job if its an immediate Job</p>
<pre><code>     instance of the Schedule class for the backup job if its a scheduled Job
</code></pre>
<h2 id="raises">Raises</h2>
<p>SDKException:
if backup level specified is not correct</p>
<pre><code>if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/vssubclient.py#L2847-L2922" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def backup(self,
           backup_level=&#34;Incremental&#34;,
           incremental_backup=False,
           incremental_level=&#39;BEFORE_SYNTH&#39;,
           collect_metadata=False,
           advanced_options=None,
           schedule_pattern=None):
    &#34;&#34;&#34;Runs a backup job for the subclient of the level specified.

        Args:
            backup_level            (str)   --  level of backup the user wish to run
                                                Full / Incremental / Differential /
                                                Synthetic_full

            incremental_backup      (bool)  --  run incremental backup
                                                only applicable in case of Synthetic_full backup

            incremental_level       (str)   --  run incremental backup before/after synthetic full
                                                BEFORE_SYNTH / AFTER_SYNTH
                                                only applicable in case of Synthetic_full backup

            collect_metadata        (bool)  --  Collect Meta data for the backup

            advanced_options       (dict)  --  advanced backup options to be included while
                                                making the request
                options:
                    create_backup_copy_immediately  --  Run Backup copy just after snap backup
                    backup_copy_type                --  Backup Copy level using storage policy
                                                        or subclient rule

            schedule_pattern (dict) -- scheduling options to be included for the task

                    Please refer schedules.schedulePattern.createSchedule()
                                                                doc for the types of Jsons

        Returns:
            object - instance of the Job class for this backup job if its an immediate Job

                     instance of the Schedule class for the backup job if its a scheduled Job

        Raises:
            SDKException:
                if backup level specified is not correct

                if response is empty

                if response is not success
    &#34;&#34;&#34;

    backup_level = backup_level.lower()
    if backup_level not in [&#39;full&#39;, &#39;incremental&#39;,
                            &#39;differential&#39;, &#39;synthetic_full&#39;]:
        raise SDKException(&#39;Subclient&#39;, &#39;103&#39;)

    if advanced_options or schedule_pattern:
        request_json = self._backup_json(
            backup_level=backup_level,
            incremental_backup=incremental_backup,
            incremental_level=incremental_level,
            advanced_options=advanced_options,
            schedule_pattern=schedule_pattern
        )

        backup_service = self._commcell_object._services[&#39;CREATE_TASK&#39;]

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, backup_service, request_json
        )

        return self._process_backup_response(flag, response)

    else:
        return super(VirtualServerSubclient, self).backup(backup_level=backup_level,
                                                          incremental_backup=incremental_backup,
                                                          incremental_level=incremental_level,
                                                          collect_metadata=collect_metadata)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.vssubclient.VirtualServerSubclient.browse"><code class="name flex">
<span>def <span class="ident">browse</span></span>(<span>self, vm_path='\\', show_deleted_files=False, vm_disk_browse=False, vm_files_browse=False, operation='browse', copy_precedence=0, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Gets the content of the backup for this subclient at the path
specified.</p>
<p>Args:
vm_path
(str)
&ndash;
vm path to get the contents of
default: '';
returns the root of the Backup
content</p>
<pre><code> show_deleted_files  (bool)  --  include deleted files in the
                                 content or not default: False

 vm_disk_browse      (bool)  --  browse virtual machine files
                                 e.g.; .vmdk files, etc.
                                 only applicable when browsing
                                 content inside a guest virtual
                                 machine
                                 default: False

 vm_files_browse      (bool)  -- browse files and folders
                                 default: True

 operation            (str)   -- Type of operation, browser of find

 copy_precedence      (int)   -- The copy precedence to do the operation from
</code></pre>
<p>Kwargs(optional)</p>
<pre><code> live_browse           (bool)   -- set to True to get live browse content
                                     even though file indexing is enabled
</code></pre>
<p>Returns:
list - list of all folders or files with their full paths
inside the input path</p>
<pre><code> dict - path along with the details like name, file/folder,
        size, modification time
</code></pre>
<p>Raises:
SDKException:
if failed to browse content</p>
<pre><code>     if response is empty

     if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/vssubclient.py#L1350-L1434" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def browse(self, vm_path=&#39;\\&#39;,
           show_deleted_files=False,
           vm_disk_browse=False,
           vm_files_browse=False,
           operation=&#39;browse&#39;,
           copy_precedence=0,
           **kwargs
           ):
    &#34;&#34;&#34;Gets the content of the backup for this subclient at the path
       specified.

        Args:
            vm_path             (str)   --  vm path to get the contents of
                                            default: &#39;\\&#39;;
                                            returns the root of the Backup
                                            content

            show_deleted_files  (bool)  --  include deleted files in the
                                            content or not default: False

            vm_disk_browse      (bool)  --  browse virtual machine files
                                            e.g.; .vmdk files, etc.
                                            only applicable when browsing
                                            content inside a guest virtual
                                            machine
                                            default: False

            vm_files_browse      (bool)  -- browse files and folders
                                            default: True

            operation            (str)   -- Type of operation, browser of find

            copy_precedence      (int)   -- The copy precedence to do the operation from

        Kwargs(optional)

            live_browse           (bool)   -- set to True to get live browse content
                                                even though file indexing is enabled

        Returns:
            list - list of all folders or files with their full paths
                   inside the input path

            dict - path along with the details like name, file/folder,
                   size, modification time

        Raises:
            SDKException:
                if failed to browse content

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    vm_ids, vm_names = self._get_vm_ids_and_names_dict()

    if operation == &#39;find&#39;:
        # Return all VMs browse content for find operation
        vm_path_list = []
        browse_content_dict = {}
        if not vm_names:
            _vm_ids, vm_names = self._get_vm_ids_and_names_dict_from_browse()
        vm_paths = [&#39;\\&#39; + vm_id for vm_id in vm_names.values()]
        for vm_path in vm_paths:
            vm_path = self._parse_vm_path(vm_names, vm_path)
            browse_content = super(VirtualServerSubclient, self).browse(
                show_deleted_files, vm_disk_browse, True, path=vm_path,
                vs_file_browse=vm_files_browse, operation=operation,
                copy_precedence=copy_precedence
            )
            vm_path_list += browse_content[0]
            browse_content_dict.update(browse_content[1])
        browse_content = (vm_path_list, browse_content_dict)

    else:
        vm_path = self._parse_vm_path(vm_names, vm_path)
        browse_content = super(VirtualServerSubclient, self).browse(
            show_deleted_files, vm_disk_browse, True, path=vm_path,
            vs_file_browse=vm_files_browse, operation=operation, **kwargs
        )

    if not vm_ids:
        for key, val in browse_content[1].items():
            vm_ids[val[&#39;snap_display_name&#39;]] = val[&#39;name&#39;]
    return self._process_vsa_browse_response(vm_ids, browse_content)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.vssubclient.VirtualServerSubclient.browse_in_time"><code class="name flex">
<span>def <span class="ident">browse_in_time</span></span>(<span>self, vm_path='\\', show_deleted_files=False, restore_index=True, vm_disk_browse=False, from_date=0, to_date=0, copy_precedence=0, vm_files_browse=False, media_agent='')</span>
</code></dt>
<dd>
<div class="desc"><p>Gets the content of the backup for this subclient
at the path specified in the time range specified.</p>
<h2 id="args">Args</h2>
<p>vm_path
(str)
&ndash;
folder path to get the
contents of
default: ''
returns the root of the
Backup content</p>
<p>show_deleted_files
(bool)
&ndash;
include deleted files in
the content or not
default: False</p>
<p>restore_index
(bool)
&ndash;
restore index if it is not
cached
default: True</p>
<p>vm_disk_browse
(bool)
&ndash;
browse the VM disks or not
default: False</p>
<p>from_date
(int)
&ndash;
date to get the contents
after
format: dd/MM/YYYY
gets contents from
01/01/1970 if not specified
default: 0</p>
<p>to_date
(int)
&ndash;
date to get the contents
before
format: dd/MM/YYYY
gets contents till current
day if not specified
default: 0</p>
<p>copy_precedence
(int)
&ndash;
copy precedence to be used
for browsing</p>
<p>media_agent
(str)
&ndash;
Browse MA via with Browse has to happen.
It can be MA different than Storage Policy MA</p>
<h2 id="returns">Returns</h2>
<p>list - list of all folders or files with their full paths
inside the input path</p>
<p>dict - path along with the details like name, file/folder,
size, modification time</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if from date value is incorrect</p>
<pre><code>if to date value is incorrect

if to date is less than from date

if failed to browse content

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/vssubclient.py#L1526-L1609" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def browse_in_time(
        self,
        vm_path=&#39;\\&#39;,
        show_deleted_files=False,
        restore_index=True,
        vm_disk_browse=False,
        from_date=0,
        to_date=0,
        copy_precedence=0,
        vm_files_browse=False,
        media_agent=&#34;&#34;):
    &#34;&#34;&#34;Gets the content of the backup for this subclient
            at the path specified in the time range specified.

            Args:
                vm_path             (str)   --  folder path to get the
                                                contents of
                                                default: &#39;\\&#39;
                                                returns the root of the
                                                Backup content

                show_deleted_files  (bool)  --  include deleted files in
                                                the content or not
                                                default: False

                restore_index       (bool)  --  restore index if it is not
                                                cached  default: True

                vm_disk_browse      (bool)  --  browse the VM disks or not
                                                default: False

                from_date           (int)   --  date to get the contents
                                                after
                                                format: dd/MM/YYYY
                                                gets contents from
                                                01/01/1970 if not specified
                                                default: 0

                to_date             (int)  --   date to get the contents
                                                before
                                                format: dd/MM/YYYY
                                                gets contents till current
                                                day if not specified
                                                default: 0

                copy_precedence     (int)   --  copy precedence to be used
                                                for browsing

                media_agent         (str)   --  Browse MA via with Browse has to happen.
                                                It can be MA different than Storage Policy MA

            Returns:
                list - list of all folders or files with their full paths
                       inside the input path

                dict - path along with the details like name, file/folder,
                       size, modification time

            Raises:
                SDKException:
                    if from date value is incorrect

                    if to date value is incorrect

                    if to date is less than from date

                    if failed to browse content

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
    vm_ids, vm_names = self._get_vm_ids_and_names_dict()
    vm_path = self._parse_vm_path(vm_names, vm_path)

    browse_content = super(VirtualServerSubclient, self).browse(
        show_deleted=show_deleted_files, restore_index=restore_index,
        vm_disk_browse=vm_disk_browse,
        from_time=from_date, to_time=to_date, copy_precedence=copy_precedence,
        path=vm_path, vs_file_browse=vm_files_browse, media_agent=media_agent)
    if not vm_ids:
        for key, val in browse_content[1].items():
            vm_ids[val[&#39;snap_display_name&#39;]] = val[&#39;name&#39;]
    return self._process_vsa_browse_response(vm_ids, browse_content)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.vssubclient.VirtualServerSubclient.disk_level_browse"><code class="name flex">
<span>def <span class="ident">disk_level_browse</span></span>(<span>self, vm_path='\\', show_deleted_files=False, restore_index=True, from_date=0, to_date=0, copy_precedence=0)</span>
</code></dt>
<dd>
<div class="desc"><p>Browses the Disks of a Virtual Machine.</p>
<h2 id="args">Args</h2>
<p>vm_path
(str)
&ndash;
vm path to get the contents of
default: ''; returns the root of the Backup content</p>
<p>show_deleted_files
(bool)
&ndash;
include deleted files in the
content or not default: False</p>
<p>restore_index
(bool)
&ndash;
Restore index or not.
default: True</p>
<p>from_date
(int)
&ndash;
date to get the contents after
format: dd/MM/YYYY
gets contents from 01/01/1970
if not specified
default: 0</p>
<p>to_date
(int)
&ndash;
date to get the contents before
format: dd/MM/YYYY
gets contents till current day
if not specified
default: 0</p>
<p>copy_precedence
(int)
&ndash;
copy precedence to be used
for browsing</p>
<h2 id="returns">Returns</h2>
<p>list - list of all folders or files with their full paths
inside the input path</p>
<p>dict - path along with the details like name, file/folder,
size, modification time</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to browse content</p>
<pre><code>if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/vssubclient.py#L1611-L1683" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def disk_level_browse(self, vm_path=&#39;\\&#39;,
                      show_deleted_files=False,
                      restore_index=True,
                      from_date=0,
                      to_date=0,
                      copy_precedence=0):
    &#34;&#34;&#34;Browses the Disks of a Virtual Machine.

        Args:
            vm_path             (str)   --  vm path to get the contents of
                default: &#39;\\&#39;; returns the root of the Backup content

            show_deleted_files  (bool)  --  include deleted files in the
                                            content or not default: False

            restore_index  (bool)  --       Restore index or not.
                                            default: True

            from_date           (int)   --  date to get the contents after
                                            format: dd/MM/YYYY
                                            gets contents from 01/01/1970
                                            if not specified
                                            default: 0

            to_date             (int)  --  date to get the contents before
                                           format: dd/MM/YYYY
                                           gets contents till current day
                                           if not specified
                                           default: 0

            copy_precedence     (int)   --  copy precedence to be used
                                                for browsing

        Returns:
            list - list of all folders or files with their full paths
                   inside the input path

            dict - path along with the details like name, file/folder,
                   size, modification time

        Raises:
            SDKException:
                if failed to browse content

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    browse_content = self.browse_in_time(
        vm_path, show_deleted_files, restore_index, True, from_date, to_date, copy_precedence
    )

    paths_list = []
    for path in browse_content[0]:
        if any(path.lower().endswith(Ext) for Ext in self.diskExtension):
            paths_list.append(path)

        elif os.path.splitext(path)[1] == &#34;&#34; and &#34;none&#34; in self.diskExtension:
            paths_list.append(path)

    paths_dict = {}

    for path in browse_content[1]:
        if any(path.lower().endswith(Ext) for Ext in self.diskExtension):
            paths_dict[path] = browse_content[1][path]
        elif os.path.splitext(path)[1] == &#34;&#34; and &#34;none&#34; in self.diskExtension:
            # assuming it as Fusion compute kind of hypervisors
            paths_dict[path] = browse_content[1][path]

    if paths_list and paths_dict:
        return paths_list, paths_dict
    else:
        raise SDKException(&#39;Subclient&#39;, &#39;113&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.vssubclient.VirtualServerSubclient.get_nics_from_browse"><code class="name flex">
<span>def <span class="ident">get_nics_from_browse</span></span>(<span>self, copy_precedence=0)</span>
</code></dt>
<dd>
<div class="desc"><p>Browses the vm to get the nics info xml, gets the nics info using
the parse_nics_xml method and prepares the dict for nics json</p>
<h2 id="args">Args</h2>
<p>copy_precedence
(int)
&ndash;
The copy precedence to do browse from</p>
<h2 id="returns">Returns</h2>
<dl>
<dt><code>dict</code></dt>
<dd>&ndash;
dict with key as vm_name and the value as the
nics info for that vm</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/vssubclient.py#L1479-L1524" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_nics_from_browse(self, copy_precedence=0):
    &#34;&#34;&#34;
        Browses the vm to get the nics info xml, gets the nics info using
        the parse_nics_xml method and prepares the dict for nics json

        Args:
            copy_precedence     (int)   --  The copy precedence to do browse from

        Returns:
            dict:   --   dict with key as vm_name and the value as the
                         nics info for that vm

    &#34;&#34;&#34;

    path, path_dict = self.browse(vm_disk_browse=True, copy_precedence=copy_precedence)

    nics_dict = {}
    nics = &#34;&#34;

    # Added for v2.1
    for vmpath in path:
        result = path_dict[vmpath]
        if (&#39;browseMetaData&#39; not in result[&#39;advanced_data&#39;]) or \
                (&#39;virtualServerMetaData&#39; not in result[&#39;advanced_data&#39;][&#39;browseMetaData&#39;]) or \
                (&#39;nics&#39; not in result[&#39;advanced_data&#39;][&#39;browseMetaData&#39;][&#39;virtualServerMetaData&#39;]):
            path, path_dict = self.browse(vm_disk_browse=True, operation=&#39;find&#39;, copy_precedence=copy_precedence)
    for vmpath in path:
        result = path_dict[vmpath]
        name = &#34;&#34;
        if &#39;name&#39; in result:
            name = result[&#39;name&#39;]
        if &#39;advanced_data&#39; in result:
            advanced_data = result[&#39;advanced_data&#39;]

            if &#39;browseMetaData&#39; in advanced_data:
                browse_meta_data = advanced_data[&#39;browseMetaData&#39;]

                if &#39;virtualServerMetaData&#39; in browse_meta_data:
                    virtual_server_metadata = browse_meta_data[&#39;virtualServerMetaData&#39;]

                    if &#39;nics&#39; in virtual_server_metadata:
                        nics = virtual_server_metadata[&#39;nics&#39;]

        nics_dict[name] = self.parse_nics_xml(nics)

    return nics_dict</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.vssubclient.VirtualServerSubclient.guest_file_restore"><code class="name flex">
<span>def <span class="ident">guest_file_restore</span></span>(<span>self, *args, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>perform Guest file restore of the provided path</p>
<h2 id="args">Args</h2>
<p>options
(dict)
&ndash;
dictionary of guest file restores options</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/vssubclient.py#L1820-L1934" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def guest_file_restore(self, *args, **kwargs):
    &#34;&#34;&#34;perform Guest file restore of the provided path

    Args:
        options     (dict)  --  dictionary of guest file restores options

    &#34;&#34;&#34;
    if args and isinstance(args[0], dict):
        options = args[0]
    else:
        options = kwargs
    vm_name = options.get(&#39;vm_name&#39;, None)
    folder_to_restore = options.get(&#39;folder_to_restore&#39;, None)
    destination_client = options.get(&#39;destination_client&#39;, None)
    destination_path = options.get(&#39;destination_path&#39;, None)
    copy_precedence = options.get(&#39;copy_precedence&#39;, 0)
    preserve_level = options.get(&#39;preserve_level&#39;, 1)
    unconditional_overwrite = options.get(&#39;unconditional_overwrite&#39;, False)
    restore_ACL = options.get(&#39;restore_ACL&#39;, True)
    from_date = options.get(&#39;from_date&#39;, 0)
    to_date = options.get(&#39;to_date&#39;, 0)
    show_deleted_files = options.get(&#39;show_deleted_files&#39;, False)
    fbr_ma = options.get(&#39;fbr_ma&#39;, None)
    browse_ma = options.get(&#39;browse_ma&#39;, &#34;&#34;)
    agentless = options.get(&#39;agentless&#39;, &#34;&#34;)
    in_place = options.get(&#39;in_place&#39;, False)

    _vm_names, _vm_ids = self._get_vm_ids_and_names_dict_from_browse()
    _file_restore_option = {}
    _verify_path = options.get(&#39;verify_path&#39;, True)

    # check if inputs are correct
    if not(isinstance(destination_path, str) and
           (isinstance(vm_name, str))):
        raise SDKException(&#39;Subclient&#39;, &#39;105&#39;)

    if vm_name not in _vm_names:
        raise SDKException(&#39;Subclient&#39;, &#39;111&#39;)

    # check if client name is correct
    if destination_client is None:
        destination_client = self._backupset_object._instance_object.co_ordinator

    if fbr_ma:
        _file_restore_option[&#34;proxy_client&#34;] = fbr_ma

    _file_restore_option[&#34;client&#34;] = destination_client
    _file_restore_option[&#34;destination_path&#34;] = destination_path

    # process the folder to restore for browse
    if isinstance(folder_to_restore, list):
        _folder_to_restore_list = folder_to_restore

    elif isinstance(folder_to_restore, str):
        _folder_to_restore_list = []
        _folder_to_restore_list.append(folder_to_restore)
    else:
        raise SDKException(&#39;Subclient&#39;, &#39;105&#39;)

    _file_restore_option[&#34;paths&#34;] = []
    for _each_folder in _folder_to_restore_list:
        # check_folder_in_browse modifies path (removes colon) and verifies in browse results.
        # The modified path does not work for windows VM when file indexing is enabled
        # Set `verify_path` to False to skip this verification and use the restore path as is

        if _verify_path:
            _restore_item_path = self._check_folder_in_browse(
                _vm_ids[vm_name],
                &#34;%s&#34; % _each_folder,
                from_date,
                to_date,
                copy_precedence,
                media_agent=browse_ma
            )
        else:
            # Converting native path to VM path
            # C:\folder1 =&gt; \&lt;vm_guid&gt;\C:\folder1
            # /folder1/folder2 =&gt; \&lt;vm_guid&gt;\folder1\folder2

            _item_path = _each_folder.replace(&#39;/&#39;, &#39;\\&#39;)
            _item_path = _item_path[1:] if _item_path[0] == &#39;\\&#39; else _item_path
            _restore_item_path = &#39;\\&#39;.join([&#39;&#39;, _vm_ids[vm_name], _item_path])

        _file_restore_option[&#34;paths&#34;].append(_restore_item_path)

    # set the browse options
    _file_restore_option[&#34;disk_browse&#34;] = False
    _file_restore_option[&#34;file_browse&#34;] = True
    _file_restore_option[&#34;from_time&#34;] = from_date
    _file_restore_option[&#34;to_time&#34;] = to_date

    # set the common file level restore options
    _file_restore_option[&#34;striplevel_type&#34;] = &#34;PRESERVE_LEVEL&#34;
    _file_restore_option[&#34;preserve_level&#34;] = preserve_level
    _file_restore_option[&#34;unconditional_overwrite&#34;] = unconditional_overwrite
    _file_restore_option[&#34;restore_ACL&#34;] = restore_ACL
    _file_restore_option[&#34;in_place&#34;] = in_place

    # set the browse option
    _file_restore_option[&#34;copy_precedence_applicable&#34;] = True
    _file_restore_option[&#34;copy_precedence&#34;] = copy_precedence
    _file_restore_option[&#34;media_agent&#34;] = browse_ma

    # set agentless options
    if agentless:
        _file_restore_option[&#34;server_name&#34;] = agentless[&#39;vserver&#39;]
        _file_restore_option[&#34;vm_guid&#34;] = agentless[&#39;vm_guid&#39;]
        _file_restore_option[&#34;vm_name&#34;] = agentless[&#39;vm_name&#39;]
        _file_restore_option[&#34;user_name&#34;] = agentless[&#39;vm_user&#39;]
        _file_restore_option[&#34;password&#34;] = agentless[&#39;vm_pass&#39;]
        _file_restore_option[&#34;agentless&#34;] = True

    # prepare and execute the Json
    request_json = self._prepare_filelevel_restore_json(_file_restore_option)
    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.vssubclient.VirtualServerSubclient.guest_files_browse"><code class="name flex">
<span>def <span class="ident">guest_files_browse</span></span>(<span>self, vm_path='\\', show_deleted_files=False, restore_index=True, from_date=0, to_date=0, copy_precedence=0, media_agent='')</span>
</code></dt>
<dd>
<div class="desc"><p>Browses the Files and Folders inside a Virtual Machine in the time
range specified.</p>
<p>Args:
vm_path
(str)
&ndash;
folder path to get the contents
of
default: '';
returns the root of the Backup
content</p>
<pre><code> show_deleted_files  (bool)  --  include deleted files in the
                                 content or not default: False

 restore_index       (bool)  --  restore index if it is not cached
                                 default: True

 from_date           (int)   --  date to get the contents after
                                 format: dd/MM/YYYY

                                 gets contents from 01/01/1970
                                 if not specified
                                 default: 0

 to_date             (int)  --  date to get the contents before
                                format: dd/MM/YYYY

                                gets contents till current day
                                if not specified
                                default: 0

 copy_precedence     (int)   --  copy precedence to be used
                                     for browsing

 media_agent         (str)   --  Browse MA via with Browse has to happen.
                                 It can be MA different than Storage Policy MA
</code></pre>
<p>Returns:
list - list of all folders or files with their full paths
inside the input path</p>
<pre><code> dict - path along with the details like name, file/folder,
        size, modification time
</code></pre>
<p>Raises:
SDKException:
if from date value is incorrect</p>
<pre><code>     if to date value is incorrect

     if to date is less than from date

     if failed to browse content

     if response is empty

     if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/vssubclient.py#L1685-L1753" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def guest_files_browse(
        self,
        vm_path=&#39;\\&#39;,
        show_deleted_files=False,
        restore_index=True,
        from_date=0,
        to_date=0,
        copy_precedence=0,
        media_agent=&#34;&#34;):
    &#34;&#34;&#34;Browses the Files and Folders inside a Virtual Machine in the time
       range specified.

        Args:
            vm_path             (str)   --  folder path to get the contents
                                            of
                                            default: &#39;\\&#39;;
                                            returns the root of the Backup
                                            content

            show_deleted_files  (bool)  --  include deleted files in the
                                            content or not default: False

            restore_index       (bool)  --  restore index if it is not cached
                                            default: True

            from_date           (int)   --  date to get the contents after
                                            format: dd/MM/YYYY

                                            gets contents from 01/01/1970
                                            if not specified
                                            default: 0

            to_date             (int)  --  date to get the contents before
                                           format: dd/MM/YYYY

                                           gets contents till current day
                                           if not specified
                                           default: 0

            copy_precedence     (int)   --  copy precedence to be used
                                                for browsing

            media_agent         (str)   --  Browse MA via with Browse has to happen.
                                            It can be MA different than Storage Policy MA

        Returns:
            list - list of all folders or files with their full paths
                   inside the input path

            dict - path along with the details like name, file/folder,
                   size, modification time

        Raises:
            SDKException:
                if from date value is incorrect

                if to date value is incorrect

                if to date is less than from date

                if failed to browse content

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    return self.browse_in_time(
        vm_path, show_deleted_files, restore_index, False, from_date, to_date, copy_precedence,
        vm_files_browse=True, media_agent=media_agent)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.vssubclient.VirtualServerSubclient.parse_nics_xml"><code class="name flex">
<span>def <span class="ident">parse_nics_xml</span></span>(<span>self, input_xml)</span>
</code></dt>
<dd>
<div class="desc"><p>Gets the content of the backup for this subclient at the path
specified.</p>
<h2 id="args">Args</h2>
<p>input_xml : &ndash;
nics info xml per vm to parse the nics name
and label</p>
<h2 id="returns">Returns</h2>
<dl>
<dt><code>nic_list</code></dt>
<dd>&ndash;
list of all Nics for a VM</dd>
</dl>
<h2 id="raise">Raise</h2>
<p>SDKException:
if input parameter is not proper</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/vssubclient.py#L1436-L1477" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def parse_nics_xml(self, input_xml):
    &#34;&#34;&#34;
        Gets the content of the backup for this subclient at the path
        specified.

        Args:
            input_xml : --   nics info xml per vm to parse the nics name
                             and label

        Returns:
            nic_list:   --    list of all Nics for a VM

        Raise:
            SDKException:
                if input parameter is not proper


    &#34;&#34;&#34;
    if not isinstance(input_xml, str):
        raise SDKException(&#34;Subclient&#34;, &#34;101&#34;)

    root = ET.fromstring(input_xml)

    nic_list = []

    for nic in root.findall(&#39;nic&#39;):
        name = nic.get(&#39;name&#39;)
        label = nic.get(&#39;label&#39;)
        subnet = nic.get(&#39;subnet&#39;)
        networkDisplayName = nic.get(&#39;networkDisplayName&#39;, &#34;&#34;)
        sourceNetwork = nic.get(&#39;id&#39;,&#34;&#34;)

        nic_info = {
            &#39;name&#39;: name,
            &#39;label&#39;: label,
            &#39;subnetId&#39;: subnet,
            &#39;networkDisplayName&#39;: networkDisplayName,
            &#39;sourceNetwork&#39;: sourceNetwork
        }
        nic_list.append(nic_info)

    return nic_list</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.vssubclient.VirtualServerSubclient.preview_content"><code class="name flex">
<span>def <span class="ident">preview_content</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Preview the subclient and get the content</p>
<h2 id="returns">Returns</h2>
<p>list
- List of the vms as the subclient content</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/vssubclient.py#L3109-L3130" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def preview_content(self):
    &#34;&#34;&#34;
    Preview the subclient and get the content

    Returns:
        list       - List of the vms as the subclient content

    &#34;&#34;&#34;
    preview = self._commcell_object._services[&#39;PREVIEW&#39;]
    preview_json = self._prepare_preview_json()

    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, preview, preview_json
    )
    if flag and &#39;scList&#39; in response.json():
        return self._parse_preview_vms(response.json()[&#39;scList&#39;])
    else:
        raise SDKException(
            &#39;Subclient&#39;,
            &#39;102&#39;,
            self._update_response_(
                response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.vssubclient.VirtualServerSubclient.reinitialize_vm_names_browse"><code class="name flex">
<span>def <span class="ident">reinitialize_vm_names_browse</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/vssubclient.py#L2031-L2033" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def reinitialize_vm_names_browse(self):
    self._vm_names_browse = []
    self._get_vm_ids_and_names_dict_from_browse()</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.vssubclient.VirtualServerSubclient.set_advanced_attach_disk_restore_options"><code class="name flex">
<span>def <span class="ident">set_advanced_attach_disk_restore_options</span></span>(<span>self, vm_to_restore, restore_option)</span>
</code></dt>
<dd>
<div class="desc"><p>set the advanced restore options for all vm in restore
:param</p>
<p>vm_to_restore : Name of the VM where disks will be restored
restore_option: restore options that need to be set for advanced restore option</p>
<pre><code>datastore                   - Datastore where the disks needs to be restored

disks   (list of dict)      - list with dict for each disk in VM
                                eg: [{
                                        name:"disk1.vmdk"
                                        datastore:"local"
                                    }
                                    {
                                        name:"disk2.vmdk"
                                        datastore:"local1"
                                    }
                                ]
guid                        - GUID of the VM needs to be restored
new_name                    - New name for the VM to be restored
esx_host                    - esx_host or client name where it need to be restored
name                        - name of the VM to be restored
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/vssubclient.py#L2386-L2455" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_advanced_attach_disk_restore_options(self, vm_to_restore, restore_option):
    &#34;&#34;&#34;
    set the advanced restore options for all vm in restore
    :param

    vm_to_restore : Name of the VM where disks will be restored
    restore_option: restore options that need to be set for advanced restore option

        datastore                   - Datastore where the disks needs to be restored

        disks   (list of dict)      - list with dict for each disk in VM
                                        eg: [{
                                                name:&#34;disk1.vmdk&#34;
                                                datastore:&#34;local&#34;
                                            }
                                            {
                                                name:&#34;disk2.vmdk&#34;
                                                datastore:&#34;local1&#34;
                                            }
                                        ]
        guid                        - GUID of the VM needs to be restored
        new_name                    - New name for the VM to be restored
        esx_host                    - esx_host or client name where it need to be restored
        name                        - name of the VM to be restored
    &#34;&#34;&#34;

    # Set the new name for the restored VM.
    # If new_name is not given, it restores the VM with same name
    # with suffix Delete.
    vm_names, vm_ids = self._get_vm_ids_and_names_dict_from_browse()
    _ = self.vm_files_browse()
    # populate restore source item
    restore_option[&#39;name&#39;] = vm_to_restore
    restore_option[&#39;guid&#39;] = vm_ids[vm_to_restore]
    restore_option[&#34;FolderPath&#34;] = &#39;&#39;
    restore_option[&#34;ResourcePool&#34;] = &#34;/&#34;

    # populate restore disk and datastore
    vm_disks = []
    disk_list, disk_info_dict = self.disk_level_browse(
        &#34;\\\\&#34; + vm_ids[vm_to_restore])

    for disk, data in disk_info_dict.items():
        ds = &#34;&#34;
        if &#34;datastore&#34; in restore_option:
            ds = restore_option[&#34;datastore&#34;]
        new_name_prefix = restore_option.get(&#34;disk_name_prefix&#34;)
        if self._instance_object.instance_name != &#39;openstack&#39;:
            new_name = data[&#34;snap_display_name&#34;].replace(&#34;/&#34;, &#34;_&#34;).replace(&#34; &#34;, &#34;_&#34;)
            new_name = &#34;del_&#34; + new_name if new_name_prefix is None \
                else new_name_prefix + &#34;_&#34; + new_name
        else:
            new_name = data[&#34;name&#34;]
        _disk_dict = self._disk_dict_pattern(data[&#39;snap_display_name&#39;], ds, new_name)
        vm_disks.append(_disk_dict)
    if not vm_disks:
        raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)
    restore_option[&#34;disks&#34;] = vm_disks

    # populate VM Specific values
    self._set_restore_inputs(
        restore_option,
        disks=vm_disks,
        esx_host=restore_option.get(&#39;esx&#39;),
        new_name=restore_option.get(&#39;newName&#39;, vm_to_restore),
        new_guid=restore_option.get(&#39;newGUID&#39;, restore_option.get(&#39;guid&#39;)),
        datastore=restore_option.get(&#39;datastore&#39;))

    temp_dict = self._json_restore_advancedRestoreOptions(restore_option)
    self._advanced_restore_option_list.append(temp_dict)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.vssubclient.VirtualServerSubclient.set_advanced_vm_restore_options"><code class="name flex">
<span>def <span class="ident">set_advanced_vm_restore_options</span></span>(<span>self, vm_to_restore, restore_option)</span>
</code></dt>
<dd>
<div class="desc"><p>set the advanced restore options for all vm in restore
:param</p>
<p>vm_to_restore : Name of the VM to restore
restore_option: restore options that need to be set for advanced restore option</p>
<pre><code>power_on                    - power on the VM after restore
add_to_failover             - Register the VM to Failover Cluster
datastore                   - Datastore where the VM needs to be restored

disks   (list of dict)      - list with dict for each disk in VM
                                eg: [{
                                        name:"disk1.vmdk"
                                        datastore:"local"
                                    }
                                    {
                                        name:"disk2.vmdk"
                                        datastore:"local1"
                                    }
                                ]
guid                        - GUID of the VM needs to be restored
new_name                    - New name for the VM to be restored
esx_host                    - esx_host or client name where it need to be restored
name                        - name of the VM to be restored
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/vssubclient.py#L2235-L2384" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_advanced_vm_restore_options(self, vm_to_restore, restore_option):
    &#34;&#34;&#34;
    set the advanced restore options for all vm in restore
    :param

    vm_to_restore : Name of the VM to restore
    restore_option: restore options that need to be set for advanced restore option

        power_on                    - power on the VM after restore
        add_to_failover             - Register the VM to Failover Cluster
        datastore                   - Datastore where the VM needs to be restored

        disks   (list of dict)      - list with dict for each disk in VM
                                        eg: [{
                                                name:&#34;disk1.vmdk&#34;
                                                datastore:&#34;local&#34;
                                            }
                                            {
                                                name:&#34;disk2.vmdk&#34;
                                                datastore:&#34;local1&#34;
                                            }
                                        ]
        guid                        - GUID of the VM needs to be restored
        new_name                    - New name for the VM to be restored
        esx_host                    - esx_host or client name where it need to be restored
        name                        - name of the VM to be restored
    &#34;&#34;&#34;

    # Set the new name for the restored VM.
    # If new_name is not given, it restores the VM with same name
    # with suffix Delete.
    vm_names, vm_ids = self._get_vm_ids_and_names_dict_from_browse()
    copy_precedence = restore_option.get(&#39;copy_precedence&#39;, 0)
    browse_result = self.vm_files_browse(copy_precedence=copy_precedence)

    # vs metadata from browse result
    _metadata = browse_result[1][(&#39;\\&#39; + vm_to_restore)]

    if (&#39;browseMetaData&#39; not in _metadata[&#39;advanced_data&#39;]) or \
            (&#39;virtualServerMetaData&#39; not in _metadata[&#39;advanced_data&#39;][&#39;browseMetaData&#39;]) or \
            (&#39;nics&#39; not in _metadata[&#39;advanced_data&#39;][&#39;browseMetaData&#39;][&#39;virtualServerMetaData&#39;]):
        browse_result = self.vm_files_browse(operation=&#39;find&#39;, copy_precedence=copy_precedence)
        _metadata = browse_result[1][(&#39;\\&#39; + vm_to_restore)]

    vs_metadata = _metadata[&#34;advanced_data&#34;][&#34;browseMetaData&#34;][&#34;virtualServerMetaData&#34;]
    if restore_option[&#39;in_place&#39;]:
        folder_path = vs_metadata.get(&#34;inventoryPath&#34;, &#39;&#39;)
        instanceSize = vs_metadata.get(&#34;instanceSize&#34;, &#39;&#39;)
    else:
        folder_path = restore_option[&#39;folder_path&#39;] if restore_option.get(&#39;folder_path&#39;) else &#39;&#39;
        instanceSize = &#39;&#39;

    if &#39;resourcePoolPath&#39; in restore_option and restore_option[&#39;resourcePoolPath&#39;] is None:
        restore_option[&#39;resourcePoolPath&#39;] = vs_metadata[&#39;resourcePoolPath&#39;]
    if &#39;datacenter&#39; in restore_option and restore_option[&#39;datacenter&#39;] is None:
        restore_option[&#39;datacenter&#39;] = vs_metadata.get(&#39;dataCenter&#39;, &#39;&#39;)
    if (&#39;terminationProtected&#39; in restore_option and
            restore_option[&#39;terminationProtected&#39;] is None):
        restore_option[&#39;terminationProtected&#39;] = vs_metadata.get(&#39;terminationProtected&#39;, &#39;&#39;)
    if &#39;iamRole&#39; in restore_option and restore_option[&#39;iamRole&#39;] is None:
        restore_option[&#39;iamRole&#39;] = vs_metadata.get(&#39;role&#39;, &#39;&#39;)
    if &#39;securityGroups&#39; in restore_option and restore_option[&#39;securityGroups&#39;] is None:
        _security_groups = self._find_security_groups(vs_metadata[&#39;networkSecurityGroups&#39;])
        restore_option[&#39;securityGroups&#39;] = _security_groups
    if &#39;keyPairList&#39; in restore_option and restore_option[&#39;keyPairList&#39;] is None:
        _keypair_list = self._find_keypair_list(vs_metadata[&#39;loginKeyPairs&#39;])
        restore_option[&#39;keyPairList&#39;] = _keypair_list

    # populate restore source item
    restore_option[&#39;paths&#39;].append(&#34;\\&#34; + vm_ids[vm_to_restore])
    restore_option[&#39;name&#39;] = vm_to_restore
    restore_option[&#39;guid&#39;] = vm_ids[vm_to_restore]
    restore_option[&#34;FolderPath&#34;] = folder_path
    restore_option[&#34;ResourcePool&#34;] = &#34;/&#34;

    # populate restore disk and datastore
    vm_disks = []
    disk_list, disk_info_dict = self.disk_level_browse(
        &#34;\\\\&#34; + vm_ids[vm_to_restore], copy_precedence=copy_precedence)

    for disk, data in disk_info_dict.items():
        ds = &#34;&#34;
        if &#34;datastore&#34; in restore_option:
            ds = restore_option[&#34;datastore&#34;]
        if restore_option[
            &#34;in_place&#34;] or &#34;datastore&#34; not in restore_option or not restore_option.get(
                &#39;datastore&#39;):
            if &#34;datastore&#34; in data[&#34;advanced_data&#34;][&#34;browseMetaData&#34;][&#34;virtualServerMetaData&#34;]:
                restore_option[&#34;datastore&#34;] = data[&#34;advanced_data&#34;][&#34;browseMetaData&#34;][
                    &#34;virtualServerMetaData&#34;][&#34;datastore&#34;]
                ds = restore_option[&#34;datastore&#34;]
            elif &#34;esxHost&#34; in vs_metadata and &#34;is_aws_proxy&#34; in restore_option:
                if restore_option.get(&#34;availability_zone&#34;) is not None:
                    ds = restore_option.get(&#34;availability_zone&#34;)
                else:
                    ds = vs_metadata[&#34;esxHost&#34;]
        new_name_prefix = restore_option.get(&#34;disk_name_prefix&#34;)
        new_name = data[&#34;name&#34;] if new_name_prefix is None \
            else new_name_prefix + &#34;_&#34; + data[&#34;name&#34;]
        if self._instance_object.instance_name == HypervisorType.GOOGLE_CLOUD.value.lower():
            new_name = &#34;&#34;
            if data[&#34;advanced_data&#34;][&#34;browseMetaData&#34;][&#34;virtualServerMetaData&#34;].get(&#39;replicaZones&#39;, False):
                replicaZones = restore_option.get(&#34;replicaZones&#34;)
        if restore_option[&#39;destination_instance&#39;].lower() in [HypervisorType.VIRTUAL_CENTER.value.lower(),
                                                              HypervisorType.AZURE_V2.value.lower()]:
            _disk_dict = self._disk_dict_pattern(data[&#39;snap_display_name&#39;], ds, new_name)
        else:
            _disk_dict = self._disk_dict_pattern(disk.split(&#39;\\&#39;)[-1], ds, new_name)
        if &#39;is_aws_proxy&#39; in restore_option and not restore_option[&#39;is_aws_proxy&#39;]:
            _disk_dict[&#39;Datastore&#39;] = restore_option[&#34;datastore&#34;]
        vm_disks.append(_disk_dict)
    if not vm_disks:
        raise SDKException(&#39;Subclient&#39;, &#39;104&#39;)
    restore_option[&#34;disks&#34;] = vm_disks

    # prepare nics info json
    if &#34;nics&#34; not in restore_option or \
            self._instance_object.instance_name == HypervisorType.GOOGLE_CLOUD.value.lower():
        nics_list = self._json_nics_advancedRestoreOptions(vm_to_restore, restore_option)
        restore_option[&#34;nics&#34;] = nics_list
        if restore_option.get(&#39;source_ip&#39;) and restore_option.get(&#39;destination_ip&#39;):
            vm_ip = self._json_vmip_advanced_restore_options(restore_option)
            restore_option[&#34;vm_ip_address_options&#34;] = vm_ip
        if restore_option[&#34;in_place&#34;]:
            if &#34;hyper&#34; in restore_option[&#34;destination_instance&#34;].lower():
                restore_option[&#34;client_name&#34;] = vs_metadata[&#39;esxHost&#39;]
                restore_option[&#34;esx_server&#34;] = vs_metadata[&#39;esxHost&#39;]
            elif &#39;Red&#39; in restore_option[&#34;destination_instance&#34;]:
                restore_option[&#34;esxHost&#34;] = vs_metadata[&#39;clusterName&#39;]
                restore_option[&#34;cluster&#34;] = vs_metadata[&#39;clusterName&#39;]
                vs_metadata[&#34;esxHost&#34;] = vs_metadata[&#39;clusterName&#39;]

    #adding tag to restored vm
    if restore_option.get(&#39;vmTags&#39;):
        vm_tags = restore_option.get(&#39;vmTags&#39;)
        if isinstance(vm_tags, str) or isinstance(vm_tags, dict):
            vm_tags = [vm_tags]
        restore_option[&#34;vmTags&#34;] = vm_tags

    # populate VM Specific values
    self._set_restore_inputs(
        restore_option,
        disks=vm_disks,
        esx_host=restore_option.get(&#39;esx_host&#39;) or vs_metadata[&#39;esxHost&#39;],
        instanceSize=restore_option.get(&#39;instanceSize&#39;, instanceSize),
        new_name=restore_option.get(&#39;new_name&#39;, &#34;del&#34; + vm_to_restore)
    )

    temp_dict = self._json_restore_advancedRestoreOptions(restore_option)
    self._advanced_restore_option_list.append(temp_dict)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.vssubclient.VirtualServerSubclient.update_properties"><code class="name flex">
<span>def <span class="ident">update_properties</span></span>(<span>self, properties_dict)</span>
</code></dt>
<dd>
<div class="desc"><p>child method to add any specific attributes for vsa</p>
<h2 id="args">Args</h2>
<p>properties_dict
(dict):
dict of all propterties of subclient</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/vssubclient.py#L756-L767" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def update_properties(self, properties_dict):
    &#34;&#34;&#34;
    child method to add any specific attributes for vsa
    Args:
        properties_dict         (dict):     dict of all propterties of subclient
    &#34;&#34;&#34;
    properties_dict.update({
        &#34;vmFilterOperationType&#34;: &#34;OVERWRITE&#34;,
        &#34;vmContentOperationType&#34;: &#34;OVERWRITE&#34;,
        &#34;vmDiskFilterOperationType&#34;: &#34;OVERWRITE&#34;
    })
    super().update_properties(properties_dict)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.vssubclient.VirtualServerSubclient.vm_files_browse"><code class="name flex">
<span>def <span class="ident">vm_files_browse</span></span>(<span>self, vm_path='\\', show_deleted_files=False, operation='browse', copy_precedence=0)</span>
</code></dt>
<dd>
<div class="desc"><p>Browses the Files and Folders of a Virtual Machine.</p>
<h2 id="args">Args</h2>
<p>vm_path
(str)
&ndash;
vm path to get the contents of
default: '';
returns the root of the Backup
content</p>
<p>show_deleted_files
(bool)
&ndash;
include deleted files in the
content or not
default: False</p>
<p>operation
(str)
&ndash;
The type of operation to perform (browse/find)</p>
<p>copy_precedence
(int)
&ndash;
The copy precedence to do browse from</p>
<h2 id="returns">Returns</h2>
<p>list - list of all folders or files with their full paths
inside the input path</p>
<p>dict - path along with the details like name, file/folder,
size, modification time</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to browse content</p>
<pre><code>if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/vssubclient.py#L1936-L1968" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def vm_files_browse(self, vm_path=&#39;\\&#39;, show_deleted_files=False, operation=&#39;browse&#39;, copy_precedence=0):
    &#34;&#34;&#34;Browses the Files and Folders of a Virtual Machine.

        Args:
            vm_path             (str)   --  vm path to get the contents of
                                            default: &#39;\\&#39;;
                                            returns the root of the Backup
                                            content

            show_deleted_files  (bool)  --  include deleted files in the
                                            content or not
                                            default: False

            operation           (str)   --  The type of operation to perform (browse/find)

            copy_precedence     (int)   --  The copy precedence to do browse from

        Returns:
            list - list of all folders or files with their full paths
                   inside the input path

            dict - path along with the details like name, file/folder,
                   size, modification time

        Raises:
            SDKException:
                if failed to browse content

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    return self.browse(vm_path, show_deleted_files, True, operation=operation, copy_precedence=copy_precedence)</code></pre>
</details>
</dd>
<dt id="cvpysdk.subclients.vssubclient.VirtualServerSubclient.vm_files_browse_in_time"><code class="name flex">
<span>def <span class="ident">vm_files_browse_in_time</span></span>(<span>self, vm_path='\\', show_deleted_files=False, restore_index=True, from_date=0, to_date=0)</span>
</code></dt>
<dd>
<div class="desc"><p>Browses the Files and Folders of a Virtual Machine in the time range
specified.</p>
<p>Args:
vm_path
(str)
&ndash;
folder path to get the contents
default: '';
returns the root of the Backup
content</p>
<pre><code> show_deleted_files  (bool)  --  include deleted files in the
                                 content or not
                                 default: False

 restore_index       (bool)  --  restore index if it is not
                                 cached
                                 default: True

 from_date           (int)   --  date to get the contents after
                                 format: dd/MM/YYYY
                                 gets contents from 01/01/1970
                                 if not specified
                                 default: 0

 to_date             (int)  --   date to get the contents before
                                 format: dd/MM/YYYY
                                 gets contents till current day
                                 if not specified
                                 default: 0
</code></pre>
<p>Returns:
list - list of all folders or files with their full paths
inside the input path</p>
<pre><code> dict - path along with the details like name, file/folder,
        size, modification time
</code></pre>
<p>Raises:
SDKException:
if from date value is incorrect</p>
<pre><code>     if to date value is incorrect

     if to date is less than from date

     if failed to browse content

     if response is empty

     if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/vssubclient.py#L1970-L2029" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def vm_files_browse_in_time(
        self,
        vm_path=&#39;\\&#39;,
        show_deleted_files=False,
        restore_index=True,
        from_date=0,
        to_date=0):
    &#34;&#34;&#34;Browses the Files and Folders of a Virtual Machine in the time range
       specified.

        Args:
            vm_path             (str)   --  folder path to get the contents
                                            default: &#39;\\&#39;;
                                            returns the root of the Backup
                                            content

            show_deleted_files  (bool)  --  include deleted files in the
                                            content or not
                                            default: False

            restore_index       (bool)  --  restore index if it is not
                                            cached
                                            default: True

            from_date           (int)   --  date to get the contents after
                                            format: dd/MM/YYYY
                                            gets contents from 01/01/1970
                                            if not specified
                                            default: 0

            to_date             (int)  --   date to get the contents before
                                            format: dd/MM/YYYY
                                            gets contents till current day
                                            if not specified
                                            default: 0

        Returns:
            list - list of all folders or files with their full paths
                   inside the input path

            dict - path along with the details like name, file/folder,
                   size, modification time

        Raises:
            SDKException:
                if from date value is incorrect

                if to date value is incorrect

                if to date is less than from date

                if failed to browse content

                if response is empty

                if response is not success
    &#34;&#34;&#34;
    return self.browse_in_time(
        vm_path, show_deleted_files, restore_index, True, from_date, to_date
    )</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.subclient.Subclient" href="../subclient.html#cvpysdk.subclient.Subclient">Subclient</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.subclient.Subclient.allow_multiple_readers" href="../subclient.html#cvpysdk.subclient.Subclient.allow_multiple_readers">allow_multiple_readers</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.data_readers" href="../subclient.html#cvpysdk.subclient.Subclient.data_readers">data_readers</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.deduplication_options" href="../subclient.html#cvpysdk.subclient.Subclient.deduplication_options">deduplication_options</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.description" href="../subclient.html#cvpysdk.subclient.Subclient.description">description</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.disable_backup" href="../subclient.html#cvpysdk.subclient.Subclient.disable_backup">disable_backup</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.disable_intelli_snap" href="../subclient.html#cvpysdk.subclient.Subclient.disable_intelli_snap">disable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.display_name" href="../subclient.html#cvpysdk.subclient.Subclient.display_name">display_name</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_backup" href="../subclient.html#cvpysdk.subclient.Subclient.enable_backup">enable_backup</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_backup_at_time" href="../subclient.html#cvpysdk.subclient.Subclient.enable_backup_at_time">enable_backup_at_time</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_intelli_snap" href="../subclient.html#cvpysdk.subclient.Subclient.enable_intelli_snap">enable_intelli_snap</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_trueup" href="../subclient.html#cvpysdk.subclient.Subclient.enable_trueup">enable_trueup</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.enable_trueup_days" href="../subclient.html#cvpysdk.subclient.Subclient.enable_trueup_days">enable_trueup_days</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.encryption_flag" href="../subclient.html#cvpysdk.subclient.Subclient.encryption_flag">encryption_flag</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.exclude_from_sla" href="../subclient.html#cvpysdk.subclient.Subclient.exclude_from_sla">exclude_from_sla</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.find" href="../subclient.html#cvpysdk.subclient.Subclient.find">find</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.find_latest_job" href="../subclient.html#cvpysdk.subclient.Subclient.find_latest_job">find_latest_job</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.get_ma_associated_storagepolicy" href="../subclient.html#cvpysdk.subclient.Subclient.get_ma_associated_storagepolicy">get_ma_associated_storagepolicy</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_backup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_backup_enabled">is_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_blocklevel_backup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_blocklevel_backup_enabled">is_blocklevel_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_default_subclient" href="../subclient.html#cvpysdk.subclient.Subclient.is_default_subclient">is_default_subclient</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_intelli_snap_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_intelli_snap_enabled">is_intelli_snap_enabled</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_on_demand_subclient" href="../subclient.html#cvpysdk.subclient.Subclient.is_on_demand_subclient">is_on_demand_subclient</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.is_trueup_enabled" href="../subclient.html#cvpysdk.subclient.Subclient.is_trueup_enabled">is_trueup_enabled</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.last_backup_time" href="../subclient.html#cvpysdk.subclient.Subclient.last_backup_time">last_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.list_media" href="../subclient.html#cvpysdk.subclient.Subclient.list_media">list_media</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.name" href="../subclient.html#cvpysdk.subclient.Subclient.name">name</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.network_agent" href="../subclient.html#cvpysdk.subclient.Subclient.network_agent">network_agent</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.next_backup_time" href="../subclient.html#cvpysdk.subclient.Subclient.next_backup_time">next_backup_time</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.plan" href="../subclient.html#cvpysdk.subclient.Subclient.plan">plan</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.properties" href="../subclient.html#cvpysdk.subclient.Subclient.properties">properties</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.read_buffer_size" href="../subclient.html#cvpysdk.subclient.Subclient.read_buffer_size">read_buffer_size</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.refresh" href="../subclient.html#cvpysdk.subclient.Subclient.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.restore_in_place" href="../subclient.html#cvpysdk.subclient.Subclient.restore_in_place">restore_in_place</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.restore_out_of_place" href="../subclient.html#cvpysdk.subclient.Subclient.restore_out_of_place">restore_out_of_place</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.run_content_indexing" href="../subclient.html#cvpysdk.subclient.Subclient.run_content_indexing">run_content_indexing</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.set_backup_nodes" href="../subclient.html#cvpysdk.subclient.Subclient.set_backup_nodes">set_backup_nodes</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.set_proxy_for_snap" href="../subclient.html#cvpysdk.subclient.Subclient.set_proxy_for_snap">set_proxy_for_snap</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.snapshot_engine_name" href="../subclient.html#cvpysdk.subclient.Subclient.snapshot_engine_name">snapshot_engine_name</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.software_compression" href="../subclient.html#cvpysdk.subclient.Subclient.software_compression">software_compression</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.storage_ma" href="../subclient.html#cvpysdk.subclient.Subclient.storage_ma">storage_ma</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.storage_ma_id" href="../subclient.html#cvpysdk.subclient.Subclient.storage_ma_id">storage_ma_id</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.storage_policy" href="../subclient.html#cvpysdk.subclient.Subclient.storage_policy">storage_policy</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.subclient_guid" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_guid">subclient_guid</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.subclient_id" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_id">subclient_id</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.subclient_name" href="../subclient.html#cvpysdk.subclient.Subclient.subclient_name">subclient_name</a></code></li>
<li><code><a title="cvpysdk.subclient.Subclient.unset_proxy_for_snap" href="../subclient.html#cvpysdk.subclient.Subclient.unset_proxy_for_snap">unset_proxy_for_snap</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.subclients" href="index.html">cvpysdk.subclients</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient" href="#cvpysdk.subclients.vssubclient.VirtualServerSubclient">VirtualServerSubclient</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.amazon_defaults" href="#cvpysdk.subclients.vssubclient.VirtualServerSubclient.amazon_defaults">amazon_defaults</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.backup" href="#cvpysdk.subclients.vssubclient.VirtualServerSubclient.backup">backup</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.browse" href="#cvpysdk.subclients.vssubclient.VirtualServerSubclient.browse">browse</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.browse_in_time" href="#cvpysdk.subclients.vssubclient.VirtualServerSubclient.browse_in_time">browse_in_time</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.cbtvalue" href="#cvpysdk.subclients.vssubclient.VirtualServerSubclient.cbtvalue">cbtvalue</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.content" href="#cvpysdk.subclients.vssubclient.VirtualServerSubclient.content">content</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.disk_level_browse" href="#cvpysdk.subclients.vssubclient.VirtualServerSubclient.disk_level_browse">disk_level_browse</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.disk_pattern" href="#cvpysdk.subclients.vssubclient.VirtualServerSubclient.disk_pattern">disk_pattern</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.get_nics_from_browse" href="#cvpysdk.subclients.vssubclient.VirtualServerSubclient.get_nics_from_browse">get_nics_from_browse</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.guest_file_restore" href="#cvpysdk.subclients.vssubclient.VirtualServerSubclient.guest_file_restore">guest_file_restore</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.guest_files_browse" href="#cvpysdk.subclients.vssubclient.VirtualServerSubclient.guest_files_browse">guest_files_browse</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.index_server" href="#cvpysdk.subclients.vssubclient.VirtualServerSubclient.index_server">index_server</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.instance_proxy" href="#cvpysdk.subclients.vssubclient.VirtualServerSubclient.instance_proxy">instance_proxy</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.live_sync" href="#cvpysdk.subclients.vssubclient.VirtualServerSubclient.live_sync">live_sync</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.metadata" href="#cvpysdk.subclients.vssubclient.VirtualServerSubclient.metadata">metadata</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.parse_nics_xml" href="#cvpysdk.subclients.vssubclient.VirtualServerSubclient.parse_nics_xml">parse_nics_xml</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.preview_content" href="#cvpysdk.subclients.vssubclient.VirtualServerSubclient.preview_content">preview_content</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.quiesce_file_system" href="#cvpysdk.subclients.vssubclient.VirtualServerSubclient.quiesce_file_system">quiesce_file_system</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.reinitialize_vm_names_browse" href="#cvpysdk.subclients.vssubclient.VirtualServerSubclient.reinitialize_vm_names_browse">reinitialize_vm_names_browse</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.set_advanced_attach_disk_restore_options" href="#cvpysdk.subclients.vssubclient.VirtualServerSubclient.set_advanced_attach_disk_restore_options">set_advanced_attach_disk_restore_options</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.set_advanced_vm_restore_options" href="#cvpysdk.subclients.vssubclient.VirtualServerSubclient.set_advanced_vm_restore_options">set_advanced_vm_restore_options</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.snapshot_storage_type" href="#cvpysdk.subclients.vssubclient.VirtualServerSubclient.snapshot_storage_type">snapshot_storage_type</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.subclient_proxy" href="#cvpysdk.subclients.vssubclient.VirtualServerSubclient.subclient_proxy">subclient_proxy</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.update_properties" href="#cvpysdk.subclients.vssubclient.VirtualServerSubclient.update_properties">update_properties</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.vm_diskfilter" href="#cvpysdk.subclients.vssubclient.VirtualServerSubclient.vm_diskfilter">vm_diskfilter</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.vm_files_browse" href="#cvpysdk.subclients.vssubclient.VirtualServerSubclient.vm_files_browse">vm_files_browse</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.vm_files_browse_in_time" href="#cvpysdk.subclients.vssubclient.VirtualServerSubclient.vm_files_browse_in_time">vm_files_browse_in_time</a></code></li>
<li><code><a title="cvpysdk.subclients.vssubclient.VirtualServerSubclient.vm_filter" href="#cvpysdk.subclients.vssubclient.VirtualServerSubclient.vm_filter">vm_filter</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>