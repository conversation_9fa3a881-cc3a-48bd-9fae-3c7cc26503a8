<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.cvpysdk API documentation</title>
<meta name="description" content="Helper file for session operations …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.cvpysdk</code></h1>
</header>
<section id="section-intro">
<p>Helper file for session operations.</p>
<p>This file is used to perform Authentication for the user on the Commcell.</p>
<pre><code>#.  Check if the web server and service is valid and running

#.  Perform Login operation to the Commcell using the credentials provided by the user

#.  Store the Authtoken received after Login REST API call to use for the entire session

#.  Renew Authtoken if credentials were given by the user during Commcell object
    initialization, and the current token has expired

#.  Logout the current user from the Commcell, and disconnect the API session

#.  Common method to be used in the entire SDK to perform REST API call on the Web Server
</code></pre>
<h2 id="cvpysdk">Cvpysdk</h2>
<p><strong>init</strong>(commcell_object)
&ndash;
initialise object of the CVPySDK class and bind to the commcell</p>
<p>_is_valid_service()
&ndash;
checks if the service is valid and running or not</p>
<p>_login()
&ndash;
sign in the user to the commcell with the credentials provided</p>
<p>_renew_login_token()
&ndash;
renews the Authtoken for the currently logged in user</p>
<p>_logout()
&ndash;
sign out the current logged in user from the commcell,
and ends the session</p>
<p>_request()
&ndash;
executes the request on the server and return the Response</p>
<p>who_am_i()
&ndash;
Fetches the username of the user to whom authtoken is mapped</p>
<p>make_request()
&ndash;
run the http request specified on the URL/WebService provided,
and return the flag specifying success/fail, and response</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cvpysdk.py#L1-L493" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

# pylint: disable=R1705

&#34;&#34;&#34;Helper file for session operations.

This file is used to perform Authentication for the user on the Commcell.

    #.  Check if the web server and service is valid and running

    #.  Perform Login operation to the Commcell using the credentials provided by the user

    #.  Store the Authtoken received after Login REST API call to use for the entire session

    #.  Renew Authtoken if credentials were given by the user during Commcell object
        initialization, and the current token has expired

    #.  Logout the current user from the Commcell, and disconnect the API session

    #.  Common method to be used in the entire SDK to perform REST API call on the Web Server


CVPySDK:

    __init__(commcell_object)   --  initialise object of the CVPySDK class and bind to the commcell

    _is_valid_service()         --  checks if the service is valid and running or not

    _login()                    --  sign in the user to the commcell with the credentials provided

    _renew_login_token()        --  renews the Authtoken for the currently logged in user

    _logout()                   --  sign out the current logged in user from the commcell,
    and ends the session

    _request()                  --  executes the request on the server and return the Response

    who_am_i()                  --  Fetches the username of the user to whom authtoken is mapped

    make_request()              --  run the http request specified on the URL/WebService provided,
    and return the flag specifying success/fail, and response

&#34;&#34;&#34;

from __future__ import absolute_import
from __future__ import unicode_literals

from xml.parsers.expat import ExpatError

import requests
import xmltodict
import urllib3

try:
    # Python 2 import
    import httplib
except ImportError:
    # Python 3 import
    import http.client as httplib

from .exception import SDKException


class CVPySDK(object):
    &#34;&#34;&#34;Helper class for login, and logout operations.

        Also contains common method for running all HTTP requests.
    &#34;&#34;&#34;

    def __init__(self, commcell_object, certificate_path=None, verify_ssl=True):
        &#34;&#34;&#34;Initialize the CVPySDK object for running various operations.

            Args:
                commcell_object     (object)    --  instance of the Commcell class


                certificate_path     (str)   --  path of the CA_BUNDLE or directory with
                certificates of trusted CAs (including trusted self-signed certificates)

                    default: None

                verify_ssl           (str)   --  verify ssl while making requests
                    default: True

            Returns:
                object  -   instance of the CVPySDK class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._certificate_path = certificate_path
        self._verify_ssl = verify_ssl
        self._response_headers = {}

        if not self._verify_ssl:
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

    def _is_valid_service(self):
        &#34;&#34;&#34;Checks if the service url is a valid url or not.

            Returns:
                True    -   if the service url is valid

                False   -   if the service url is invalid

            Raises:
                requests Connection Error:
                    requests.exceptions.ConnectionError

                requests Timeout Error:
                    requests.exceptions.Timeout

        &#34;&#34;&#34;
        try:
            response = self._request(
                method=&#39;GET&#39;,
                url=self._commcell_object._web_service,
                timeout=10
            )

            # Valid service if the status code is 200 and response is True
            return response.status_code == httplib.OK and response.ok
        except (requests.exceptions.ConnectionError, requests.exceptions.Timeout) as error:
            raise error

    def _login(self):
        &#34;&#34;&#34;Posts a login request to the server.

            Returns:
                str     -   Authtoken received from the WebServer upon successfull login

            Raises:
                SDKException:
                    if login failed

                    if response is empty

                    if response is not success

                requests Connection Error:
                    requests.exceptions.ConnectionError

        &#34;&#34;&#34;
        try:
            if isinstance(self._commcell_object._password, dict):
                raise SDKException(&#39;CVPySDK&#39;, &#39;104&#39;)


            if self._commcell_object.is_service_commcell:
                json_login_request = {
                    &#34;mode&#34;: 4,
                    &#34;clientType&#34;: 30,
                    &#34;autoLogin&#34;: {
                        &#34;autoLoginType&#34;: 5,
                        &#34;encryptedMessage&#34;: self._commcell_object.master_saml_token
                    }
                }

            else:
                json_login_request = {
                    &#34;mode&#34;: 4,
                    &#34;username&#34;: self._commcell_object._user,
                    &#34;password&#34;: self._commcell_object._password,
                    &#34;deviceId&#34;: self._commcell_object.device_id,
                    &#34;clientType&#34;: 30,
                }

            flag, response = self.make_request(
                &#39;POST&#39;, self._commcell_object._services[&#39;LOGIN&#39;], json_login_request
            )

            if flag:
                if response.json():
                    if &#34;userName&#34; in response.json() and &#34;token&#34; in response.json():
                        return response.json()[&#39;token&#39;]
                    else:
                        error_message = response.json()[&#39;errList&#39;][0][&#39;errLogMessage&#39;]
                        err_msg = &#39;Error: &#34;{0}&#34;&#39;.format(error_message)
                        if response.json().get(&#39;isAccountLocked&#39;, False) and response.json().get(&#39;remainingLockTime&#39;,0) &gt; 0:
                            locktime = response.json().get(&#39;remainingLockTime&#39;,0)
                            lock_hours = locktime // 3600
                            rem_secs = locktime % 3600
                            lock_mins = rem_secs // 60
                            err_msg = &#39;Error: &#34;User account is locked for {0} hour(s) {1} minute(s).&#34;&#39;.format(lock_hours,lock_mins)
                        raise SDKException(&#39;CVPySDK&#39;, &#39;101&#39;, err_msg)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                if response is not None:
                    response_json = response.json()
                    if (&#34;errorMessage&#34; in response_json and &#34;Access denied&#34; in response_json[&#34;errorMessage&#34;] and
                        &#34;errorCode&#34; in response_json and response_json[&#34;errorCode&#34;] == 5):
                        raise SDKException(&#39;Response&#39;, &#39;101&#39;, &#34;Commcell was reachable &#34;
                            &#34;but there may be a problem with the SSL certificate. &#34;
                            &#34;You can try providing the certificate file using the certificate_path parameter. &#34;
                            &#34;Alternatively, you can ignore this check by setting verify_ssl=False.&#34;)
                response_string = self._commcell_object._update_response_(response.text)
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        except requests.exceptions.ConnectionError as con_err:
            raise con_err

    def _renew_login_token(self, attempts):
        &#34;&#34;&#34;Posts a Renew Login-Token request to the server.

            Args:
                attempts    (int)   --  number of attempts made with the same request
                
            Returns:
                str     -   new token received from the WebServer

            Raises:
                SDKException:
                    if token renew failed

                    if response is empty

                    if response is not success

                requests Connection Error:
                    requests.exceptions.ConnectionError

        &#34;&#34;&#34;
        try:
            if self._commcell_object._is_saml_login and not self._commcell_object.is_service_commcell:
                raise SDKException(&#39;CVPySDK&#39;, &#39;106&#39;)

            token_renew_request = {
                &#34;sessionId&#34;: self._commcell_object._headers[&#39;Authtoken&#39;],
                &#34;deviceId&#34;: self._commcell_object.device_id
            }

            flag, response = self.make_request(
                &#39;POST&#39;, self._commcell_object._services[&#39;RENEW_LOGIN_TOKEN&#39;], token_renew_request, attempts
            )

            if flag:
                if response.json():
                    if &#34;token&#34; in response.json():
                        return response.json()[&#39;token&#39;]
                    else:
                        error_message = response.json()[&#39;error&#39;][&#39;errLogMessage&#39;]
                        err_msg = &#39;Error: &#34;{0}&#34;&#39;.format(error_message)
                        raise SDKException(&#39;CVPySDK&#39;, &#39;101&#39;, err_msg)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                response_string = self._commcell_object._update_response_(response.text)
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        except requests.exceptions.ConnectionError as con_err:
            raise con_err

    def _logout(self):
        &#34;&#34;&#34;Posts a logout request to the server.

            Returns:
                str     -   response string from server upon logout success

        &#34;&#34;&#34;
        flag, response = self.make_request(&#39;POST&#39;, self._commcell_object._services[&#39;LOGOUT&#39;])

        if flag:
            self._commcell_object._headers[&#39;Authtoken&#39;] = None

            if response.status_code == httplib.OK:
                return response.text
            else:
                return &#39;Failed to logout the user&#39;
        else:
            return &#39;User already logged out&#39;

    def _request(self, **kwargs):
        &#34;&#34;&#34;Executes the request on the Server with the given parameters.

            If the certificate path is given and the Web Service starts with **https**,
            it adds the **verify** parameter to the request, and passes the certificate path as
            its value.

            Args:
                **kwargs    --  dict of keyword arguments, same as accepted by the

                    **requests.request** method

            Returns:
                object  -   **requests.Response** class instance, as received from calling the
                **requests.request** method

        &#34;&#34;&#34;
        if self._certificate_path and self._commcell_object._web_service.startswith(&#39;https&#39;):
            return requests.request(verify=self._certificate_path, **kwargs)
        else:
            return requests.request(verify=self._verify_ssl, **kwargs)

    def who_am_i(self, authtoken=None):
        &#34;&#34;&#34;Get the username of the user, to whom the Authtoken belongs to.

            Args:
                authtoken   (str)   --  QSDK or SAML authentication token

            Returns:
                str     -   username of the user respective to the token

            Raises:
                SDKException:
                    if no user mapping found

        &#34;&#34;&#34;
        temp_headers = self._commcell_object._headers.copy()

        if authtoken:
            temp_headers[&#39;Authtoken&#39;] = authtoken

        flag, response = self.make_request(
            &#39;POST&#39;, self._commcell_object._services[&#39;WHO_AM_I&#39;], headers=temp_headers
        )

        if flag:
            if &#39;application/json&#39; in response.headers[&#39;Content-Type&#39;]:
                if response.json().get(&#39;errorCode&#39;, 0) == 0:
                    return response.json()[&#39;user&#39;][&#39;userName&#39;]
                else:
                    raise SDKException(&#39;CVPySDK&#39;, &#39;107&#39;)
            else:
                user_dict = xmltodict.parse(response.content)

                if &#39;CvEntities_ProcessingInstructionInfo&#39; in user_dict:
                    return user_dict[&#39;CvEntities_ProcessingInstructionInfo&#39;][&#39;user&#39;][&#39;@userName&#39;]
                else:
                    raise SDKException(&#39;CVPySDK&#39;, &#39;107&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def make_request(
            self,
            method,
            url,
            payload=None,
            attempts=0,
            headers=None,
            stream=False,
            files=None,
            **kwargs):
        &#34;&#34;&#34;Makes the request of the type specified in the argument &#39;method&#39;.

            Args:
                method      (str)           --  HTTP operation to perform

                    e.g.:

                    -   GET

                    -   POST

                    -   PUT

                    -   DELETE

                url         (str)           --  the web url or service to run the HTTP request on


                payload     (dict / str)    --  data to be passed along with the request

                    default: None


                attempts    (int)           --  number of attempts made with the same request

                    default: 0


                headers     (dict)          --  dict of request headers for the request

                        if not specified we use default headers

                    default: None


                stream      (bool)          --  boolean specifying whether the request should get
                data via stream or normal get

                    default: False


                files       (dict)          --  file to upload in the form of

                        {
                            &#39;file&#39;: open(&#39;report.txt&#39;, &#39;rb&#39;)
                        }

                    default: None

                Kwargs supported values:

                    remove_processing_info  (bool)      --  removes the processing instruction info from response.json()

            Returns:
                tuple:
                    (True, response)    -   in case of success

                    (False, response)   -   in case of failure

            Raises:
                SDKException:
                    if the method passed is incorrect / not supported

                    if the number of attempts exceed 3

                requests Connection Error:
                    requests.exceptions.ConnectionError

        &#34;&#34;&#34;
        try:
            if headers is None:
                headers = self._commcell_object._headers.copy()

            if method == &#39;POST&#39;:
                if isinstance(payload, (dict, list)):
                    if files is not None:
                        response = self._request(method=method, url=url, files=files, data=payload)
                    else:
                        response = self._request(
                            method=method, url=url, headers=headers, json=payload, stream=stream
                        )
                else:
                    try:
                        # call encode on the payload in case the characters in the payload
                        # are not encoded, and to encode the string payload to bytes
                        payload = payload.encode()
                    except AttributeError:
                        # pass silently if payload is alredy encoded in bytes
                        pass

                    if &#39;Content-type&#39; in headers and headers[&#39;Content-type&#39;] not in [
                            &#39;application/x-www-form-urlencoded&#39;]:
                        try:
                            if payload is not None:
                                xmltodict.parse(payload)
                            headers[&#39;Content-type&#39;] = &#39;application/xml&#39;
                        except ExpatError:
                            headers[&#39;Content-type&#39;] = &#39;text/plain&#39;

                    response = self._request(
                        method=method, url=url, headers=headers, data=payload, stream=stream
                    )
            elif method == &#39;GET&#39;:
                response = self._request(method=method, url=url, headers=headers, stream=stream)
            elif method == &#39;PUT&#39;:
                response = self._request(method=method, url=url, headers=headers, json=payload)
            elif method == &#39;DELETE&#39;:
                response = self._request(method=method, url=url, headers=headers)
            else:
                raise SDKException(&#39;CVPySDK&#39;, &#39;102&#39;, &#39;HTTP method {} not supported&#39;.format(method))

            # Processinginfo removal from response. It is under try catch to handle different response cases
            # (Eg:DownloadStream API will return file stream in response)
            self._response_headers = response.headers
            try:
                if kwargs.get(&#39;remove_processing_info&#39;, True) and &#39;processinginstructioninfo&#39; in response.json():
                    del response.json()[&#39;processinginstructioninfo&#39;]
            except Exception as e:
                pass

            if response.status_code == httplib.UNAUTHORIZED and headers.get(&#39;Authtoken&#39;) is not None:
                if headers[&#39;Authtoken&#39;].startswith(&#39;Bearer &#39;):
                    raise SDKException(&#39;CVPySDK&#39;, &#39;106&#39;)
                if attempts &lt; 3:
                    self._commcell_object._headers[&#39;Authtoken&#39;] = self._renew_login_token(attempts + 1)
                    return self.make_request(method, url, payload, attempts + 1)
                else:
                    # Raise max attempts exception, if attempts exceeds 3
                    raise SDKException(&#39;CVPySDK&#39;, &#39;103&#39;)

            if (response.status_code == httplib.OK or response.status_code == httplib.CREATED) and response.ok:
                return (True, response)
            else:
                return (False, response)
        except requests.exceptions.ConnectionError as con_err:
            raise con_err</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.cvpysdk.CVPySDK"><code class="flex name class">
<span>class <span class="ident">CVPySDK</span></span>
<span>(</span><span>commcell_object, certificate_path=None, verify_ssl=True)</span>
</code></dt>
<dd>
<div class="desc"><p>Helper class for login, and logout operations.</p>
<p>Also contains common method for running all HTTP requests.</p>
<p>Initialize the CVPySDK object for running various operations.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the Commcell class</p>
<p>certificate_path
(str)
&ndash;
path of the CA_BUNDLE or directory with
certificates of trusted CAs (including trusted self-signed certificates)</p>
<pre><code>default: None
</code></pre>
<p>verify_ssl
(str)
&ndash;
verify ssl while making requests
default: True</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the CVPySDK class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cvpysdk.py#L80-L493" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class CVPySDK(object):
    &#34;&#34;&#34;Helper class for login, and logout operations.

        Also contains common method for running all HTTP requests.
    &#34;&#34;&#34;

    def __init__(self, commcell_object, certificate_path=None, verify_ssl=True):
        &#34;&#34;&#34;Initialize the CVPySDK object for running various operations.

            Args:
                commcell_object     (object)    --  instance of the Commcell class


                certificate_path     (str)   --  path of the CA_BUNDLE or directory with
                certificates of trusted CAs (including trusted self-signed certificates)

                    default: None

                verify_ssl           (str)   --  verify ssl while making requests
                    default: True

            Returns:
                object  -   instance of the CVPySDK class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._certificate_path = certificate_path
        self._verify_ssl = verify_ssl
        self._response_headers = {}

        if not self._verify_ssl:
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

    def _is_valid_service(self):
        &#34;&#34;&#34;Checks if the service url is a valid url or not.

            Returns:
                True    -   if the service url is valid

                False   -   if the service url is invalid

            Raises:
                requests Connection Error:
                    requests.exceptions.ConnectionError

                requests Timeout Error:
                    requests.exceptions.Timeout

        &#34;&#34;&#34;
        try:
            response = self._request(
                method=&#39;GET&#39;,
                url=self._commcell_object._web_service,
                timeout=10
            )

            # Valid service if the status code is 200 and response is True
            return response.status_code == httplib.OK and response.ok
        except (requests.exceptions.ConnectionError, requests.exceptions.Timeout) as error:
            raise error

    def _login(self):
        &#34;&#34;&#34;Posts a login request to the server.

            Returns:
                str     -   Authtoken received from the WebServer upon successfull login

            Raises:
                SDKException:
                    if login failed

                    if response is empty

                    if response is not success

                requests Connection Error:
                    requests.exceptions.ConnectionError

        &#34;&#34;&#34;
        try:
            if isinstance(self._commcell_object._password, dict):
                raise SDKException(&#39;CVPySDK&#39;, &#39;104&#39;)


            if self._commcell_object.is_service_commcell:
                json_login_request = {
                    &#34;mode&#34;: 4,
                    &#34;clientType&#34;: 30,
                    &#34;autoLogin&#34;: {
                        &#34;autoLoginType&#34;: 5,
                        &#34;encryptedMessage&#34;: self._commcell_object.master_saml_token
                    }
                }

            else:
                json_login_request = {
                    &#34;mode&#34;: 4,
                    &#34;username&#34;: self._commcell_object._user,
                    &#34;password&#34;: self._commcell_object._password,
                    &#34;deviceId&#34;: self._commcell_object.device_id,
                    &#34;clientType&#34;: 30,
                }

            flag, response = self.make_request(
                &#39;POST&#39;, self._commcell_object._services[&#39;LOGIN&#39;], json_login_request
            )

            if flag:
                if response.json():
                    if &#34;userName&#34; in response.json() and &#34;token&#34; in response.json():
                        return response.json()[&#39;token&#39;]
                    else:
                        error_message = response.json()[&#39;errList&#39;][0][&#39;errLogMessage&#39;]
                        err_msg = &#39;Error: &#34;{0}&#34;&#39;.format(error_message)
                        if response.json().get(&#39;isAccountLocked&#39;, False) and response.json().get(&#39;remainingLockTime&#39;,0) &gt; 0:
                            locktime = response.json().get(&#39;remainingLockTime&#39;,0)
                            lock_hours = locktime // 3600
                            rem_secs = locktime % 3600
                            lock_mins = rem_secs // 60
                            err_msg = &#39;Error: &#34;User account is locked for {0} hour(s) {1} minute(s).&#34;&#39;.format(lock_hours,lock_mins)
                        raise SDKException(&#39;CVPySDK&#39;, &#39;101&#39;, err_msg)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                if response is not None:
                    response_json = response.json()
                    if (&#34;errorMessage&#34; in response_json and &#34;Access denied&#34; in response_json[&#34;errorMessage&#34;] and
                        &#34;errorCode&#34; in response_json and response_json[&#34;errorCode&#34;] == 5):
                        raise SDKException(&#39;Response&#39;, &#39;101&#39;, &#34;Commcell was reachable &#34;
                            &#34;but there may be a problem with the SSL certificate. &#34;
                            &#34;You can try providing the certificate file using the certificate_path parameter. &#34;
                            &#34;Alternatively, you can ignore this check by setting verify_ssl=False.&#34;)
                response_string = self._commcell_object._update_response_(response.text)
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        except requests.exceptions.ConnectionError as con_err:
            raise con_err

    def _renew_login_token(self, attempts):
        &#34;&#34;&#34;Posts a Renew Login-Token request to the server.

            Args:
                attempts    (int)   --  number of attempts made with the same request
                
            Returns:
                str     -   new token received from the WebServer

            Raises:
                SDKException:
                    if token renew failed

                    if response is empty

                    if response is not success

                requests Connection Error:
                    requests.exceptions.ConnectionError

        &#34;&#34;&#34;
        try:
            if self._commcell_object._is_saml_login and not self._commcell_object.is_service_commcell:
                raise SDKException(&#39;CVPySDK&#39;, &#39;106&#39;)

            token_renew_request = {
                &#34;sessionId&#34;: self._commcell_object._headers[&#39;Authtoken&#39;],
                &#34;deviceId&#34;: self._commcell_object.device_id
            }

            flag, response = self.make_request(
                &#39;POST&#39;, self._commcell_object._services[&#39;RENEW_LOGIN_TOKEN&#39;], token_renew_request, attempts
            )

            if flag:
                if response.json():
                    if &#34;token&#34; in response.json():
                        return response.json()[&#39;token&#39;]
                    else:
                        error_message = response.json()[&#39;error&#39;][&#39;errLogMessage&#39;]
                        err_msg = &#39;Error: &#34;{0}&#34;&#39;.format(error_message)
                        raise SDKException(&#39;CVPySDK&#39;, &#39;101&#39;, err_msg)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                response_string = self._commcell_object._update_response_(response.text)
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        except requests.exceptions.ConnectionError as con_err:
            raise con_err

    def _logout(self):
        &#34;&#34;&#34;Posts a logout request to the server.

            Returns:
                str     -   response string from server upon logout success

        &#34;&#34;&#34;
        flag, response = self.make_request(&#39;POST&#39;, self._commcell_object._services[&#39;LOGOUT&#39;])

        if flag:
            self._commcell_object._headers[&#39;Authtoken&#39;] = None

            if response.status_code == httplib.OK:
                return response.text
            else:
                return &#39;Failed to logout the user&#39;
        else:
            return &#39;User already logged out&#39;

    def _request(self, **kwargs):
        &#34;&#34;&#34;Executes the request on the Server with the given parameters.

            If the certificate path is given and the Web Service starts with **https**,
            it adds the **verify** parameter to the request, and passes the certificate path as
            its value.

            Args:
                **kwargs    --  dict of keyword arguments, same as accepted by the

                    **requests.request** method

            Returns:
                object  -   **requests.Response** class instance, as received from calling the
                **requests.request** method

        &#34;&#34;&#34;
        if self._certificate_path and self._commcell_object._web_service.startswith(&#39;https&#39;):
            return requests.request(verify=self._certificate_path, **kwargs)
        else:
            return requests.request(verify=self._verify_ssl, **kwargs)

    def who_am_i(self, authtoken=None):
        &#34;&#34;&#34;Get the username of the user, to whom the Authtoken belongs to.

            Args:
                authtoken   (str)   --  QSDK or SAML authentication token

            Returns:
                str     -   username of the user respective to the token

            Raises:
                SDKException:
                    if no user mapping found

        &#34;&#34;&#34;
        temp_headers = self._commcell_object._headers.copy()

        if authtoken:
            temp_headers[&#39;Authtoken&#39;] = authtoken

        flag, response = self.make_request(
            &#39;POST&#39;, self._commcell_object._services[&#39;WHO_AM_I&#39;], headers=temp_headers
        )

        if flag:
            if &#39;application/json&#39; in response.headers[&#39;Content-Type&#39;]:
                if response.json().get(&#39;errorCode&#39;, 0) == 0:
                    return response.json()[&#39;user&#39;][&#39;userName&#39;]
                else:
                    raise SDKException(&#39;CVPySDK&#39;, &#39;107&#39;)
            else:
                user_dict = xmltodict.parse(response.content)

                if &#39;CvEntities_ProcessingInstructionInfo&#39; in user_dict:
                    return user_dict[&#39;CvEntities_ProcessingInstructionInfo&#39;][&#39;user&#39;][&#39;@userName&#39;]
                else:
                    raise SDKException(&#39;CVPySDK&#39;, &#39;107&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def make_request(
            self,
            method,
            url,
            payload=None,
            attempts=0,
            headers=None,
            stream=False,
            files=None,
            **kwargs):
        &#34;&#34;&#34;Makes the request of the type specified in the argument &#39;method&#39;.

            Args:
                method      (str)           --  HTTP operation to perform

                    e.g.:

                    -   GET

                    -   POST

                    -   PUT

                    -   DELETE

                url         (str)           --  the web url or service to run the HTTP request on


                payload     (dict / str)    --  data to be passed along with the request

                    default: None


                attempts    (int)           --  number of attempts made with the same request

                    default: 0


                headers     (dict)          --  dict of request headers for the request

                        if not specified we use default headers

                    default: None


                stream      (bool)          --  boolean specifying whether the request should get
                data via stream or normal get

                    default: False


                files       (dict)          --  file to upload in the form of

                        {
                            &#39;file&#39;: open(&#39;report.txt&#39;, &#39;rb&#39;)
                        }

                    default: None

                Kwargs supported values:

                    remove_processing_info  (bool)      --  removes the processing instruction info from response.json()

            Returns:
                tuple:
                    (True, response)    -   in case of success

                    (False, response)   -   in case of failure

            Raises:
                SDKException:
                    if the method passed is incorrect / not supported

                    if the number of attempts exceed 3

                requests Connection Error:
                    requests.exceptions.ConnectionError

        &#34;&#34;&#34;
        try:
            if headers is None:
                headers = self._commcell_object._headers.copy()

            if method == &#39;POST&#39;:
                if isinstance(payload, (dict, list)):
                    if files is not None:
                        response = self._request(method=method, url=url, files=files, data=payload)
                    else:
                        response = self._request(
                            method=method, url=url, headers=headers, json=payload, stream=stream
                        )
                else:
                    try:
                        # call encode on the payload in case the characters in the payload
                        # are not encoded, and to encode the string payload to bytes
                        payload = payload.encode()
                    except AttributeError:
                        # pass silently if payload is alredy encoded in bytes
                        pass

                    if &#39;Content-type&#39; in headers and headers[&#39;Content-type&#39;] not in [
                            &#39;application/x-www-form-urlencoded&#39;]:
                        try:
                            if payload is not None:
                                xmltodict.parse(payload)
                            headers[&#39;Content-type&#39;] = &#39;application/xml&#39;
                        except ExpatError:
                            headers[&#39;Content-type&#39;] = &#39;text/plain&#39;

                    response = self._request(
                        method=method, url=url, headers=headers, data=payload, stream=stream
                    )
            elif method == &#39;GET&#39;:
                response = self._request(method=method, url=url, headers=headers, stream=stream)
            elif method == &#39;PUT&#39;:
                response = self._request(method=method, url=url, headers=headers, json=payload)
            elif method == &#39;DELETE&#39;:
                response = self._request(method=method, url=url, headers=headers)
            else:
                raise SDKException(&#39;CVPySDK&#39;, &#39;102&#39;, &#39;HTTP method {} not supported&#39;.format(method))

            # Processinginfo removal from response. It is under try catch to handle different response cases
            # (Eg:DownloadStream API will return file stream in response)
            self._response_headers = response.headers
            try:
                if kwargs.get(&#39;remove_processing_info&#39;, True) and &#39;processinginstructioninfo&#39; in response.json():
                    del response.json()[&#39;processinginstructioninfo&#39;]
            except Exception as e:
                pass

            if response.status_code == httplib.UNAUTHORIZED and headers.get(&#39;Authtoken&#39;) is not None:
                if headers[&#39;Authtoken&#39;].startswith(&#39;Bearer &#39;):
                    raise SDKException(&#39;CVPySDK&#39;, &#39;106&#39;)
                if attempts &lt; 3:
                    self._commcell_object._headers[&#39;Authtoken&#39;] = self._renew_login_token(attempts + 1)
                    return self.make_request(method, url, payload, attempts + 1)
                else:
                    # Raise max attempts exception, if attempts exceeds 3
                    raise SDKException(&#39;CVPySDK&#39;, &#39;103&#39;)

            if (response.status_code == httplib.OK or response.status_code == httplib.CREATED) and response.ok:
                return (True, response)
            else:
                return (False, response)
        except requests.exceptions.ConnectionError as con_err:
            raise con_err</code></pre>
</details>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.cvpysdk.CVPySDK.make_request"><code class="name flex">
<span>def <span class="ident">make_request</span></span>(<span>self, method, url, payload=None, attempts=0, headers=None, stream=False, files=None, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Makes the request of the type specified in the argument 'method'.</p>
<h2 id="args">Args</h2>
<p>method
(str)
&ndash;
HTTP operation to perform</p>
<pre><code>e.g.:

-   GET

-   POST

-   PUT

-   DELETE
</code></pre>
<p>url
(str)
&ndash;
the web url or service to run the HTTP request on</p>
<p>payload
(dict / str)
&ndash;
data to be passed along with the request</p>
<pre><code>default: None
</code></pre>
<p>attempts
(int)
&ndash;
number of attempts made with the same request</p>
<pre><code>default: 0
</code></pre>
<p>headers
(dict)
&ndash;
dict of request headers for the request</p>
<pre><code>    if not specified we use default headers

default: None
</code></pre>
<p>stream
(bool)
&ndash;
boolean specifying whether the request should get
data via stream or normal get</p>
<pre><code>default: False
</code></pre>
<p>files
(dict)
&ndash;
file to upload in the form of</p>
<pre><code>    {
        'file': open('report.txt', 'rb')
    }

default: None
</code></pre>
<p>Kwargs supported values:</p>
<pre><code>remove_processing_info  (bool)      --  removes the processing instruction info from response.json()
</code></pre>
<h2 id="returns">Returns</h2>
<p>tuple:
(True, response)
-
in case of success</p>
<pre><code>(False, response)   -   in case of failure
</code></pre>
<h2 id="raises">Raises</h2>
<p>SDKException:
if the method passed is incorrect / not supported</p>
<pre><code>if the number of attempts exceed 3
</code></pre>
<p>requests Connection Error:
requests.exceptions.ConnectionError</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cvpysdk.py#L348-L493" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def make_request(
        self,
        method,
        url,
        payload=None,
        attempts=0,
        headers=None,
        stream=False,
        files=None,
        **kwargs):
    &#34;&#34;&#34;Makes the request of the type specified in the argument &#39;method&#39;.

        Args:
            method      (str)           --  HTTP operation to perform

                e.g.:

                -   GET

                -   POST

                -   PUT

                -   DELETE

            url         (str)           --  the web url or service to run the HTTP request on


            payload     (dict / str)    --  data to be passed along with the request

                default: None


            attempts    (int)           --  number of attempts made with the same request

                default: 0


            headers     (dict)          --  dict of request headers for the request

                    if not specified we use default headers

                default: None


            stream      (bool)          --  boolean specifying whether the request should get
            data via stream or normal get

                default: False


            files       (dict)          --  file to upload in the form of

                    {
                        &#39;file&#39;: open(&#39;report.txt&#39;, &#39;rb&#39;)
                    }

                default: None

            Kwargs supported values:

                remove_processing_info  (bool)      --  removes the processing instruction info from response.json()

        Returns:
            tuple:
                (True, response)    -   in case of success

                (False, response)   -   in case of failure

        Raises:
            SDKException:
                if the method passed is incorrect / not supported

                if the number of attempts exceed 3

            requests Connection Error:
                requests.exceptions.ConnectionError

    &#34;&#34;&#34;
    try:
        if headers is None:
            headers = self._commcell_object._headers.copy()

        if method == &#39;POST&#39;:
            if isinstance(payload, (dict, list)):
                if files is not None:
                    response = self._request(method=method, url=url, files=files, data=payload)
                else:
                    response = self._request(
                        method=method, url=url, headers=headers, json=payload, stream=stream
                    )
            else:
                try:
                    # call encode on the payload in case the characters in the payload
                    # are not encoded, and to encode the string payload to bytes
                    payload = payload.encode()
                except AttributeError:
                    # pass silently if payload is alredy encoded in bytes
                    pass

                if &#39;Content-type&#39; in headers and headers[&#39;Content-type&#39;] not in [
                        &#39;application/x-www-form-urlencoded&#39;]:
                    try:
                        if payload is not None:
                            xmltodict.parse(payload)
                        headers[&#39;Content-type&#39;] = &#39;application/xml&#39;
                    except ExpatError:
                        headers[&#39;Content-type&#39;] = &#39;text/plain&#39;

                response = self._request(
                    method=method, url=url, headers=headers, data=payload, stream=stream
                )
        elif method == &#39;GET&#39;:
            response = self._request(method=method, url=url, headers=headers, stream=stream)
        elif method == &#39;PUT&#39;:
            response = self._request(method=method, url=url, headers=headers, json=payload)
        elif method == &#39;DELETE&#39;:
            response = self._request(method=method, url=url, headers=headers)
        else:
            raise SDKException(&#39;CVPySDK&#39;, &#39;102&#39;, &#39;HTTP method {} not supported&#39;.format(method))

        # Processinginfo removal from response. It is under try catch to handle different response cases
        # (Eg:DownloadStream API will return file stream in response)
        self._response_headers = response.headers
        try:
            if kwargs.get(&#39;remove_processing_info&#39;, True) and &#39;processinginstructioninfo&#39; in response.json():
                del response.json()[&#39;processinginstructioninfo&#39;]
        except Exception as e:
            pass

        if response.status_code == httplib.UNAUTHORIZED and headers.get(&#39;Authtoken&#39;) is not None:
            if headers[&#39;Authtoken&#39;].startswith(&#39;Bearer &#39;):
                raise SDKException(&#39;CVPySDK&#39;, &#39;106&#39;)
            if attempts &lt; 3:
                self._commcell_object._headers[&#39;Authtoken&#39;] = self._renew_login_token(attempts + 1)
                return self.make_request(method, url, payload, attempts + 1)
            else:
                # Raise max attempts exception, if attempts exceeds 3
                raise SDKException(&#39;CVPySDK&#39;, &#39;103&#39;)

        if (response.status_code == httplib.OK or response.status_code == httplib.CREATED) and response.ok:
            return (True, response)
        else:
            return (False, response)
    except requests.exceptions.ConnectionError as con_err:
        raise con_err</code></pre>
</details>
</dd>
<dt id="cvpysdk.cvpysdk.CVPySDK.who_am_i"><code class="name flex">
<span>def <span class="ident">who_am_i</span></span>(<span>self, authtoken=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Get the username of the user, to whom the Authtoken belongs to.</p>
<h2 id="args">Args</h2>
<p>authtoken
(str)
&ndash;
QSDK or SAML authentication token</p>
<h2 id="returns">Returns</h2>
<p>str
-
username of the user respective to the token</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if no user mapping found</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cvpysdk.py#L308-L346" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def who_am_i(self, authtoken=None):
    &#34;&#34;&#34;Get the username of the user, to whom the Authtoken belongs to.

        Args:
            authtoken   (str)   --  QSDK or SAML authentication token

        Returns:
            str     -   username of the user respective to the token

        Raises:
            SDKException:
                if no user mapping found

    &#34;&#34;&#34;
    temp_headers = self._commcell_object._headers.copy()

    if authtoken:
        temp_headers[&#39;Authtoken&#39;] = authtoken

    flag, response = self.make_request(
        &#39;POST&#39;, self._commcell_object._services[&#39;WHO_AM_I&#39;], headers=temp_headers
    )

    if flag:
        if &#39;application/json&#39; in response.headers[&#39;Content-Type&#39;]:
            if response.json().get(&#39;errorCode&#39;, 0) == 0:
                return response.json()[&#39;user&#39;][&#39;userName&#39;]
            else:
                raise SDKException(&#39;CVPySDK&#39;, &#39;107&#39;)
        else:
            user_dict = xmltodict.parse(response.content)

            if &#39;CvEntities_ProcessingInstructionInfo&#39; in user_dict:
                return user_dict[&#39;CvEntities_ProcessingInstructionInfo&#39;][&#39;user&#39;][&#39;@userName&#39;]
            else:
                raise SDKException(&#39;CVPySDK&#39;, &#39;107&#39;)
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.cvpysdk.CVPySDK" href="#cvpysdk.cvpysdk.CVPySDK">CVPySDK</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.cvpysdk.CVPySDK.make_request" href="#cvpysdk.cvpysdk.CVPySDK.make_request">make_request</a></code></li>
<li><code><a title="cvpysdk.cvpysdk.CVPySDK.who_am_i" href="#cvpysdk.cvpysdk.CVPySDK.who_am_i">who_am_i</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>