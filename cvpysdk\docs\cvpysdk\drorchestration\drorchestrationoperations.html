<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.drorchestration.drorchestrationoperations API documentation</title>
<meta name="description" content="Main file for performing DR Orchestration specific operations …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.drorchestration.drorchestrationoperations</code></h1>
</header>
<section id="section-intro">
<p>Main file for performing DR Orchestration specific operations.</p>
<p>DROrchestrationOperations:
Class for representing all the DR orchestration operations from failover groups
or replication monitor</p>
<h2 id="drorchestrationoperations">Drorchestrationoperations</h2>
<p><strong>init</strong>(commcell_object)
&ndash; Initialise object of DROrchestrationOperations</p>
<p><strong>repr</strong>()
&ndash; Return the DROrchestrationOperations</p>
<p>testboot()
&ndash; Call testboot operation</p>
<p>planned_failover()
&ndash; Call Planned failvoer operation</p>
<p>unplanned_failover()
&ndash; Call Unplanned Failover operation</p>
<p>failback()
&ndash; Call failback operation</p>
<p>undo_failover()
&ndash; Call UndoFailover operation</p>
<p>revert_failover()
&ndash; Call RevertFailover operation</p>
<p>point_in_time_failover()
&ndash; Call PointInTimeFailover operation</p>
<p>reverse_replication()
&ndash; Schedule and call ReverseReplication operation</p>
<p>schedule_reverse_replication()
&ndash; Schedule ReverseReplication</p>
<p>force_reverse_replication()
&ndash; Call ReverseReplication operation</p>
<p>validate_dr_orchestration_job(jobId)
&ndash; Validate DR orchestration job Id</p>
<p>get_snapshot_list(guid)
&ndash; Retrieves snapshot lists from the specified guid</p>
<h5 id="internal-methods">internal methods</h5>
<p>_construct_dr_orchestration_operation_json()
&ndash; Construct dr orchestration operation json</p>
<p>_construct_reverse_replication_json()
&ndash; Construct reverse replication json</p>
<p>_call_dr_orchestration_task
(dr_orchestration_json)
&ndash; Call DR orchestration task</p>
<p>_call_reverse_replication_task
(dr_orchestration_json)
&ndash; Call reverse replication</p>
<p>_get_dr_orchestration_job_stats
(jobId, replicationId)
&ndash; Gets DR orchetration job phase types and states</p>
<h5 id="properties">properties</h5>
<p>_json_task()
&ndash; Returns task json</p>
<p>_json_dr_orchestration_subtasks()
&ndash; Returns DR orchestration subtasks json</p>
<p>_json_dr_orchestration()
&ndash; Returns DR orchestration json</p>
<p>_dr_group_id()
&ndash; Returns DR group Id</p>
<p>dr_orchestration_options()
&ndash; DR orchestration options</p>
<p>dr_orchestration_job_phase()
&ndash; DR orchestration job phase type</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/drorchestrationoperations.py#L1-L888" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Main file for performing DR Orchestration specific operations.


DROrchestrationOperations:  Class for representing all the DR orchestration operations from failover groups
                            or replication monitor


DROrchestrationOperations:
    __init__(commcell_object)                       -- Initialise object of DROrchestrationOperations

    __repr__()                                      -- Return the DROrchestrationOperations

    testboot()                                      -- Call testboot operation

    planned_failover()                              -- Call Planned failvoer operation

    unplanned_failover()                            -- Call Unplanned Failover operation

    failback()                                      -- Call failback operation

    undo_failover()                                 -- Call UndoFailover operation

    revert_failover()                               -- Call RevertFailover operation

    point_in_time_failover()                        -- Call PointInTimeFailover operation

    reverse_replication()                           -- Schedule and call ReverseReplication operation

    schedule_reverse_replication()                  -- Schedule ReverseReplication

    force_reverse_replication()                     -- Call ReverseReplication operation

    validate_dr_orchestration_job(jobId)            -- Validate DR orchestration job Id

    get_snapshot_list(guid)                         -- Retrieves snapshot lists from the specified guid


    ##### internal methods #####
    _construct_dr_orchestration_operation_json()    -- Construct dr orchestration operation json

    _construct_reverse_replication_json()           -- Construct reverse replication json

    _call_dr_orchestration_task
    (dr_orchestration_json)                         -- Call DR orchestration task

    _call_reverse_replication_task
    (dr_orchestration_json)                         -- Call reverse replication

    _get_dr_orchestration_job_stats
    (jobId, replicationId)                          -- Gets DR orchetration job phase types and states


    ##### properties #####
    _json_task()                                    -- Returns task json

    _json_dr_orchestration_subtasks()               -- Returns DR orchestration subtasks json

    _json_dr_orchestration()                        -- Returns DR orchestration json

    _dr_group_id()                                  -- Returns DR group Id

    dr_orchestration_options()                      -- DR orchestration options

    dr_orchestration_job_phase()                    -- DR orchestration job phase type


&#34;&#34;&#34;

from __future__ import absolute_import
from __future__ import unicode_literals

from ..exception import SDKException

from .dr_orchestration_job_phase import DRJobPhaseToText, DRJobPhases


class DROrchestrationOperations(object):
    &#34;&#34;&#34;Class for invoking DROrchestration operations in the commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the DROrchestrationOperations.

            Args:
                commcell_object (object)  --  instance of the Commcell class

            Returns:
                object - instance of the DROrchestrationOperations class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._services = commcell_object._services

        ####### init REST API URLS #########
        self._CREATE_TASK = self._commcell_object._services[&#39;CREATE_TASK&#39;]
        self._REVERSE_REPLICATION_TASK = self._commcell_object._services[
            &#39;REVERSE_REPLICATION_TASK&#39;]

        #### init variables ######
        self._dr_orchestration_option = None
        self._dr_orchestration_job_phase = None
        self._dr_group_Id = 0

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        return &#39;DROrchestrationOperations instance for Commcell&#39;

    @property
    def dr_orchestration_job_phase(self):
        &#34;&#34;&#34;
        Args:

        Returns: DR orchestration job phase dict

        Raises:
        &#34;&#34;&#34;
        if not self._dr_orchestration_job_phase:
            _dr_orchestration_job_phase = DRJobPhases

            self._dr_orchestration_job_phase = _dr_orchestration_job_phase
        return self._dr_orchestration_job_phase

    @property
    def dr_orchestration_options(self):
        &#34;&#34;&#34;Getter dr orchestration options json&#34;&#34;&#34;
        return self._dr_orchestration_option

    @dr_orchestration_options.setter
    def dr_orchestration_options(self, value):
        &#34;&#34;&#34;Setter dr orchestration options json&#34;&#34;&#34;
        self._dr_orchestration_option = value

    @property
    def dr_group_id(self):
        &#34;&#34;&#34;Getter DR group Id&#34;&#34;&#34;
        return int(self.dr_orchestration_options.get(&#34;failoverGroupId&#34;, 0))

    @property
    def _json_task(self):
        &#34;&#34;&#34;Getter for the task information in JSON&#34;&#34;&#34;

        _taks_option_json = {
            &#34;ownerId&#34;: 1,
            &#34;taskType&#34;: 1,
            &#34;ownerName&#34;: &#34;admin&#34;,
            &#34;sequenceNumber&#34;: 0,
            &#34;initiatedFrom&#34;: 2,
            &#34;taskFlags&#34;: {
                &#34;disabled&#34;: False
            }
        }

        return _taks_option_json

    @property
    def _json_dr_orchestration_subtasks(self):
        &#34;&#34;&#34;Getter for the subtask in DR orchestraion JSON . It is read only attribute&#34;&#34;&#34;

        _backup_subtask = {
            &#34;subTaskType&#34;: 1,
            &#34;operationType&#34;: 4046
        }

        return _backup_subtask

    @property
    def _json_dr_orchestration(self):
        &#34;&#34;&#34;Getter for the DRorchestration task in failover group JSON . It is read only attribute&#34;&#34;&#34;

        if not isinstance(self.dr_orchestration_options, dict):
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        initiatedFrom = self.dr_orchestration_options.get(
            &#34;initiatedfromMonitor&#34;, False)

        _dr_orchestration_json = {
            &#34;operationType&#34;: int(self.dr_orchestration_options.get(&#34;DROrchestrationType&#34;)),
            &#34;initiatedfromMonitor&#34;: initiatedFrom,
            &#34;advancedOptions&#34;: {
                &#34;skipDisableNetworkAdapter&#34;: self.dr_orchestration_options.get(&#34;skipDisableNetworkAdapter&#34;, False)
            }
        }

        # if initiated from monitor set To True
        if initiatedFrom:

            _dr_orchestration_json.update({
                    &#34;replicationInfo&#34;: {
                        &#34;replicationId&#34;: self.dr_orchestration_options.get(&#34;replicationIds&#34;, [0])
                    }
            })

        else:
            _dr_orchestration_json.update({
                &#34;vApp&#34;: {
                    &#34;vAppId&#34;: int(self.dr_orchestration_options.get(&#34;failoverGroupId&#34;)),
                    &#34;vAppName&#34;: self.dr_orchestration_options.get(&#34;failoverGroupName&#34;)
                }
            })

        return _dr_orchestration_json

    def testboot(self):
        &#34;&#34;&#34;Performs testboot failover operation.

            Args:

            Returns:
                (JobId, TaskId) - JobId and taskId of the Testboot job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        if not isinstance(self.dr_orchestration_options, dict):
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        self.dr_orchestration_options[&#34;DROrchestrationType&#34;] = &#34;7&#34;
        dr_orchestration_json = self._construct_dr_orchestration_operation_json()

        if not dr_orchestration_json:
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        return self._call_dr_orchestration_task(dr_orchestration_json)

    def planned_failover(self):
        &#34;&#34;&#34;Performs Planned failover operation.

            Args:

            Returns:
                (JobId, TaskId) - JobId and taskId of the Planned Failover job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        if not isinstance(self.dr_orchestration_options, dict):
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        self.dr_orchestration_options[&#34;DROrchestrationType&#34;] = &#34;1&#34;
        dr_orchestration_json = self._construct_dr_orchestration_operation_json()

        if not dr_orchestration_json:
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        return self._call_dr_orchestration_task(dr_orchestration_json)

    def unplanned_failover(self):
        &#34;&#34;&#34;Performs UnPlanned failover operation.

            Args:

            Returns:
                (JobId, TaskId) - JobId and taskId of the Unplanned Failover job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        if not isinstance(self.dr_orchestration_options, dict):
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        self.dr_orchestration_options[&#34;DROrchestrationType&#34;] = &#34;3&#34;
        dr_orchestration_json = self._construct_dr_orchestration_operation_json()

        if not dr_orchestration_json:
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        return self._call_dr_orchestration_task(dr_orchestration_json)

    def failback(self):
        &#34;&#34;&#34;Performs Failback operation.

            Args:

            Returns:
                (JobId, TaskId) - JobId and taskId of the failback job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        if not isinstance(self.dr_orchestration_options, dict):
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        self.dr_orchestration_options[&#34;DROrchestrationType&#34;] = &#34;2&#34;
        dr_orchestration_json = self._construct_dr_orchestration_operation_json()

        if not dr_orchestration_json:
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        return self._call_dr_orchestration_task(dr_orchestration_json)

    def undo_failover(self):
        &#34;&#34;&#34;Performs Undo Failover operation.

            Args:

            Returns:
                (JobId, TaskId) - JobId and taskId of the failback job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        if not isinstance(self.dr_orchestration_options, dict):
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        self.dr_orchestration_options[&#34;DROrchestrationType&#34;] = &#34;6&#34;
        dr_orchestration_json = self._construct_dr_orchestration_operation_json()

        if not dr_orchestration_json:
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        return self._call_dr_orchestration_task(dr_orchestration_json)

    def reverse_replication(self):
        &#34;&#34;&#34;Schedules and calls Reverse Replication

            Args:

            Returns:
                (JobId, TaskId) - JobId and taskId of the reverse replication job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        self.schedule_reverse_replication()
        return self.force_reverse_replication()

    def schedule_reverse_replication(self):
        &#34;&#34;&#34;Schedules Reverse Replication.

            Args:

            Returns:
                (TaskId) - TaskId of the scheduling reverse replication job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        if not isinstance(self.dr_orchestration_options, dict):
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        self.dr_orchestration_options[&#34;DROrchestrationType&#34;] = &#34;9&#34;
        dr_orchestration_json = self._json_dr_orchestration

        if not dr_orchestration_json:
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        return self._call_reverse_replication_task(dr_orchestration_json)

    def force_reverse_replication(self):
        &#34;&#34;&#34;Performs one reverse replication operation.

            Args:

            Returns:
                (JobId, TaskId) - JobId and taskId of the reverse replication job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        if not isinstance(self.dr_orchestration_options, dict):
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        self.dr_orchestration_options[&#34;DROrchestrationType&#34;] = &#34;9&#34;
        dr_orchestration_json = self._construct_dr_orchestration_operation_json()

        # We need to have this option in the request because &#34;powerOnVM&#34; is True by default.
        # If it is not set, both src and dst VMs will be booted up.
        dr_orchestration_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;adminOpts&#34;][&#34;drOrchestrationOption&#34;][&#34;advancedOptions&#34;].update(
                {&#34;powerOnVM&#34;: False})

        if not dr_orchestration_json:
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        return self._call_dr_orchestration_task(dr_orchestration_json)

    def revert_failover(self):
        &#34;&#34;&#34;Performs Revert Failover operation.

            Args:

            Returns:
                (JobId, TaskId) - JobId and taskId of the failback job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        if not isinstance(self.dr_orchestration_options, dict):
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        self.dr_orchestration_options[&#34;DROrchestrationType&#34;] = &#34;4&#34;
        dr_orchestration_json = self._construct_dr_orchestration_operation_json()

        if not dr_orchestration_json:
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        return self._call_dr_orchestration_task(dr_orchestration_json)

    def point_in_time_failover(self, timestamp, replication_id):
        &#34;&#34;&#34;Performs Point in time Failover operation.

            Args:

            Returns:
                (JobId, TaskId) - JobId and taskId of the failback job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        if not isinstance(self.dr_orchestration_options, dict):
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        self.dr_orchestration_options[&#34;DROrchestrationType&#34;] = &#34;8&#34;
        dr_orchestration_json = self._construct_dr_orchestration_operation_json()

        if not dr_orchestration_json:
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        dr_orchestration_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;adminOpts&#34;][&#34;drOrchestrationOption&#34;][&#34;replicationInfo&#34;][
                &#34;configOption&#34;] = [
                    {&#34;pointInTime&#34;: int(timestamp),
                     &#34;replicationId&#34;: int(replication_id)}]

        return self._call_dr_orchestration_task(dr_orchestration_json)

    def validate_dr_orchestration_job(self, jobId):
        &#34;&#34;&#34; Validates DR orchestration job of jobId
            Args:
                JobId: Job Id of the DR orchestration job

            Returns:
                bool - boolean that represents whether the DR orchestration job finished successfully or not

            Raises:
                SDKException:
                    if proper inputs are not provided
                    If failover phase failed at any stage
        &#34;&#34;&#34;

        if not isinstance(jobId, str):
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        _replicationIds = self.dr_orchestration_options.get(
            &#34;replicationIds&#34;, [])
        if not _replicationIds:
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        # iterate over replication Ids
        for replicationId in iter(_replicationIds):

            dr_orchestration_job_stats_json = self._get_dr_orchestration_job_stats(
                str(jobId), str(replicationId))

            if dr_orchestration_job_stats_json and isinstance(
                    dr_orchestration_job_stats_json, dict):

                if &#34;phase&#34; in dr_orchestration_job_stats_json:

                    for phase in dr_orchestration_job_stats_json[&#34;phase&#34;]:

                        phase_type = DRJobPhaseToText(self.dr_orchestration_job_phase(phase[&#34;phase&#34;])).value
                        phase_state = phase[&#34;status&#34;]

                        if phase_state == 1:
                            o_str = &#39;Failed to complete phase: [&#39; + str(
                                phase_type) + &#39;] status: [&#39; + str(phase_state) + &#39;]&#39;
                            raise SDKException(
                                &#39;DROrchestrationOperations&#39;, &#39;102&#39;, o_str)

                else:

                    o_str = &#39;Failed to finish any phases in DR orchestration Job {0} \n&#39;.format(
                        jobId)
                    raise SDKException(
                        &#39;DROrchestrationOperations&#39;, &#39;102&#39;, o_str)

        return True

    def get_snapshot_list(self, guid: str, instance_id: int, timestamp_filter: bool = True):
        &#34;&#34;&#34; Gets snapshot list

            Args:
                guid (str): GUID of the spcified VM

                timestamp_filter (bool): whether to only return snapshots with timestamps

            Returns:
                list of dict: list of snapshot information
        &#34;&#34;&#34;
        vm_browse_req = &#34;{0}VMBrowse/{1}?instanceId={2}&amp;snapshots=true&#34;.format(
            self._commcell_object._web_service, guid, instance_id)
        (flag, response) = self._commcell_object._cvpysdk_object.make_request(
            method=&#39;GET&#39;, url=vm_browse_req)

        # only fetches snapshot info from the response
        if flag:
            if response.json():
                snapshots = response.json()[&#34;scList&#34;][0][&#34;snapshots&#34;]
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        if timestamp_filter:
            # we only need snapshots with timestamps
            snapshots = list(
                filter(lambda s: &#34;__GX_Recovery_Point_&#34; in s[&#34;name&#34;], snapshots))

            # adds integer timestamps to the list
            for s in snapshots:
                timestamp = s[&#34;name&#34;].split(&#34;_&#34;)[-1]
                s[&#34;timestamp&#34;] = int(timestamp)

        return snapshots


#################### private functions #####################

    def _get_dr_orchestration_operation_string(self, orchestration_type):
        &#34;&#34;&#34;Getter for dr orchestration operation type&#34;&#34;&#34;

        _orchestration_string = &#34;&#34;
        if orchestration_type == 1:
            _orchestration_string = &#34;Planned Failover&#34;

        elif orchestration_type == 2:
            _orchestration_string = &#34;Failback&#34;

        elif orchestration_type == 3:
            _orchestration_string = &#34;UnPlanned Failover&#34;

        elif orchestration_type == 4:
            _orchestration_string = &#34;Revert Failover&#34;

        elif orchestration_type == 6:
            _orchestration_string = &#34;Undo Failover&#34;

        elif orchestration_type == 7:
            _orchestration_string = &#34;TestBoot&#34;

        elif orchestration_type == 8:
            _orchestration_string = &#34;Point in Time Failover&#34;

        elif orchestration_type == 9:
            _orchestration_string = &#34;Reverse Replication&#34;

        return _orchestration_string

    def _construct_dr_orchestration_operation_json(self):
        &#34;&#34;&#34;
            Constructs DR orchestration operation json to invoke DR orchestration operation in the commcell

            Args:

            Returns: DR orchestration json dict

            Raises:
                SDKException:
                    if response is empty
                    if response is not success
        &#34;&#34;&#34;
        dr_orchestration_json = {
            &#34;taskInfo&#34;: {
                &#34;task&#34;: self._json_task,
                &#34;subTasks&#34;: [
                    {
                        &#34;subTaskOperation&#34;: 1,
                        &#34;subTask&#34;: self._json_dr_orchestration_subtasks,
                        &#34;options&#34;: {
                            &#34;adminOpts&#34;: {
                                &#34;drOrchestrationOption&#34;: self._json_dr_orchestration
                            }
                        }
                    }
                ]
            }
        }

        return dr_orchestration_json

    def _construct_reverse_replication_json(self):
        &#34;&#34;&#34;
            Constructs reverse replication json to invoke reverse replication task in the commcell

            Args:

            Returns: Reverse replication task dict

            Raises:
                SDKException:
                    if response is empty
                    if response is not success
        &#34;&#34;&#34;
        dr_orchestration_json = {

            &#34;drOrchestrationOption&#34;: self._json_dr_orchestration

        }

        return dr_orchestration_json

    def _call_dr_orchestration_task(self, dr_orchestration_json):
        &#34;&#34;&#34;
            Invokes DR orchestration operation task

            Args: dr orchestration json

                    taskInfo&#39;:  {
                    &#39;task&#39;:  {
                        &#39;taskFlags&#39;:  {
                            &#39;disabled&#39;:  0
                        },
                         &#39;taskType&#39;:  1,
                         &#39;ownerId&#39;:  1,
                         &#39;initiatedFrom&#39;:  2,
                         &#39;sequenceNumber&#39;:  0,
                         &#39;ownerName&#39;:  &#39;admin&#39;
                    },
                     &#39;subTasks&#39;:  {
                        &#39;subTask&#39;:  {
                            &#39;subTaskType&#39;:  1,
                             &#39;operationType&#39;:  4046
                        },
                         &#39;options&#39;:  {
                            &#39;adminOpts&#39;:  {
                                &#39;drOrchestrationOption&#39;:  {
                                    &#39;replicationInfo&#39;:  {
                                        &#39;replicationId&#39;:  [
                                            {
                                                &#39;val&#39;:  3
                                            },
                                             {
                                                &#39;val&#39;:  4
                                            }
                                        ]
                                    },
                                     &#39;vApp&#39;:  {
                                        &#39;vAppId&#39;:  26,
                                         &#39;vAppName&#39;:  &#39;fg1-automation1x21&#39;
                                    },
                                     &#39;advancedOptions&#39;:  {
                                        &#39;skipDisableNetworkAdapter&#39;:  0
                                    },
                                     &#39;operationType&#39;:  7,
                                     &#39;initiatedfromMonitor&#39;:  0
                                }
                            }
                        },
                         &#39;subTaskOperation&#39;:  1
                    }
                }
            }


            Returns:
                (JobId, TaskId) - JobId and taskId of the DR orchestration job triggered

            Raises:
                SDKException:
                    if response is empty
                    if response is not success
        &#34;&#34;&#34;
        if not isinstance(dr_orchestration_json, dict):
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        # passing the built json to start failover
        (flag, response) = self._commcell_object._cvpysdk_object.make_request(
            method=&#39;POST&#39;, url=self._CREATE_TASK, payload=dr_orchestration_json)

        if flag:
            if response.json():
                if &#39;error&#39; in response.json():
                    error_message = response.json()[&#39;error&#39;][&#39;errorMessage&#39;]
                    o_str = &#39;Failed to start {0} job \nError: &#34;{1}&#34;&#39;.format(
                        self._get_dr_orchestration_operation_string(self.dr_orchestration_options.get(&#34;DROrchestrationType&#34;)), error_message)

                    raise SDKException(
                        &#39;DROrchestrationOperations&#39;, &#39;102&#39;, o_str)
                else:
                    # return object of corresponding Virtual Machine Policy
                    # here
                    task_id = response.json()[&#39;taskId&#39;]
                    job_id = response.json()[&#39;jobIds&#39;][0]
                    return (job_id, task_id)

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _call_reverse_replication_task(self, dr_orchestration_json):
        &#34;&#34;&#34;
            Create reverse replication task

            Args: DR orchestration json

                drOrchestrationOption&#39;:  {
                    &#39;replicationInfo&#39;:  {
                        &#39;replicationId&#39;:  [
                            {
                                &#39;val&#39;:  3
                            },
                             {
                                &#39;val&#39;:  4
                            }
                        ]
                    },

                     &#39;advancedOptions&#39;:  {
                        &#39;skipDisableNetworkAdapter&#39;:  0
                    },
                     &#39;operationType&#39;:  9,
                     &#39;initiatedfromMonitor&#39;:  1
            }


            Returns:
                (TaskId) - taskId of the created reverse replication schedule

            Raises:
                SDKException:
                    if response is empty
                    if response is not success
        &#34;&#34;&#34;

        if not isinstance(dr_orchestration_json, dict):
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        # passing the built json to start DR orchestration
        (flag, response) = self._commcell_object._cvpysdk_object.make_request(
            method=&#39;PUT&#39;, url=self._REVERSE_REPLICATION_TASK, payload=dr_orchestration_json)

        if flag:
            if response.json():
                if &#39;error&#39; in response.json():
                    error_message = response.json()[&#39;error&#39;][&#39;errorMessage&#39;]
                    o_str = &#39;Failed to start {0} job \nError: &#34;{1}&#34;&#39;.format(
                        self._get_dr_orchestration_operation_string(self.dr_orchestration_options.get(&#34;DROrchestrationType&#34;)), error_message)

                    raise SDKException(
                        &#39;DROrchestrationOperations&#39;, &#39;102&#39;, o_str)
                else:
                    # return object of corresponding Virtual Machine Policy
                    # here
                    task_id = response.json()[&#39;taskId&#39;]
                    return (task_id)

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _get_dr_orchestration_job_stats(self, jobId, replicationId):
        &#34;&#34;&#34; Gets DR orchestration job stats json
            Args:
                JobId: Job Id of the DR orchestration job
                replicationId: replication Id of the DR orchestration

            Returns:
                dict - DR orchestration job phases
                {
                            &#34;jobId&#34;: 4240,
                            &#34;replicationId&#34;: 7,
                            &#34;phase&#34;: [
                                {
                                    &#34;phase&#34;: 15,
                                    &#34;status&#34;: 0,
                                    &#34;startTime&#34;: {
                                        &#34;_type_&#34;: 55,
                                        &#34;time&#34;: 1516293332
                                    },
                                    &#34;endTime&#34;: {
                                        &#34;_type_&#34;: 55,
                                        &#34;time&#34;: 1516293335
                                    },
                                    &#34;entity&#34;: {
                                        &#34;clientName&#34;: &#34;failovervm1&#34;
                                    }
                                },
                                {
                                    &#34;phase&#34;: 17,
                                    &#34;status&#34;: 0,
                                    &#34;startTime&#34;: {
                                        &#34;_type_&#34;: 55,
                                        &#34;time&#34;: 1516293335
                                    },
                                    &#34;endTime&#34;: {
                                        &#34;_type_&#34;: 55,
                                        &#34;time&#34;: 1516293340
                                    },
                                    &#34;entity&#34;: {
                                        &#34;clientName&#34;: &#34;failovervm1&#34;
                                    }
                                },
                                {
                                    &#34;phase&#34;: 20,
                                    &#34;status&#34;: 0,
                                    &#34;startTime&#34;: {
                                        &#34;_type_&#34;: 55,
                                        &#34;time&#34;: 1516293340
                                    },
                                    &#34;endTime&#34;: {
                                        &#34;_type_&#34;: 55,
                                        &#34;time&#34;: 1516293345
                                    },
                                    &#34;entity&#34;: {
                                        &#34;clientName&#34;: &#34;failovervm1&#34;
                                    }
                                }
            }

            Raises:
                SDKException:
                    if proper inputs are not provided
                    If DR orchestration phase failed at any stage
        &#34;&#34;&#34;

        if not isinstance(jobId, str) and not isinstance(
                replicationId, str):
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        _DR_JOB_STATS = (self._services[&#39;FAILOVER_GROUP_JOB_STATS&#39;]
                         if self._commcell_object.commserv_version &gt; 30
                         else self._services[&#39;DR_GROUP_JOB_STATS&#39;]) % (jobId, self.dr_group_id, replicationId)

        # passing the built json to get DR orchestration job phases
        (flag, response) = self._commcell_object._cvpysdk_object.make_request(
            method=&#39;GET&#39;, url=_DR_JOB_STATS)

        if flag:
            if response.json():
                if &#39;error&#39; in response.json():
                    error_message = response.json()[&#39;error&#39;][&#39;errorMessage&#39;]
                    o_str = &#39;Failed to validate DR orchestration job {0} \nError: &#34;{1}&#34;&#39;.format(jobId,
                                                                                                error_message)

                    raise SDKException(
                        &#39;DROrchestrationOperations&#39;, &#39;102&#39;, o_str)
                else:

                    if &#39;job&#39; in response.json():

                        # return DR orchestration job phases
                        return (response.json()[&#34;job&#34;][0])

                    else:
                        raise SDKException(
                            &#39;DROrchestrationOperations&#39;, &#39;102&#39;, &#39;Failed to start DR orchestration job&#39;)

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations"><code class="flex name class">
<span>class <span class="ident">DROrchestrationOperations</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for invoking DROrchestration operations in the commcell.</p>
<p>Initialize object of the DROrchestrationOperations.</p>
<h2 id="args">Args</h2>
<p>commcell_object (object)
&ndash;
instance of the Commcell class</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the DROrchestrationOperations class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/drorchestrationoperations.py#L95-L888" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class DROrchestrationOperations(object):
    &#34;&#34;&#34;Class for invoking DROrchestration operations in the commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the DROrchestrationOperations.

            Args:
                commcell_object (object)  --  instance of the Commcell class

            Returns:
                object - instance of the DROrchestrationOperations class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._services = commcell_object._services

        ####### init REST API URLS #########
        self._CREATE_TASK = self._commcell_object._services[&#39;CREATE_TASK&#39;]
        self._REVERSE_REPLICATION_TASK = self._commcell_object._services[
            &#39;REVERSE_REPLICATION_TASK&#39;]

        #### init variables ######
        self._dr_orchestration_option = None
        self._dr_orchestration_job_phase = None
        self._dr_group_Id = 0

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        return &#39;DROrchestrationOperations instance for Commcell&#39;

    @property
    def dr_orchestration_job_phase(self):
        &#34;&#34;&#34;
        Args:

        Returns: DR orchestration job phase dict

        Raises:
        &#34;&#34;&#34;
        if not self._dr_orchestration_job_phase:
            _dr_orchestration_job_phase = DRJobPhases

            self._dr_orchestration_job_phase = _dr_orchestration_job_phase
        return self._dr_orchestration_job_phase

    @property
    def dr_orchestration_options(self):
        &#34;&#34;&#34;Getter dr orchestration options json&#34;&#34;&#34;
        return self._dr_orchestration_option

    @dr_orchestration_options.setter
    def dr_orchestration_options(self, value):
        &#34;&#34;&#34;Setter dr orchestration options json&#34;&#34;&#34;
        self._dr_orchestration_option = value

    @property
    def dr_group_id(self):
        &#34;&#34;&#34;Getter DR group Id&#34;&#34;&#34;
        return int(self.dr_orchestration_options.get(&#34;failoverGroupId&#34;, 0))

    @property
    def _json_task(self):
        &#34;&#34;&#34;Getter for the task information in JSON&#34;&#34;&#34;

        _taks_option_json = {
            &#34;ownerId&#34;: 1,
            &#34;taskType&#34;: 1,
            &#34;ownerName&#34;: &#34;admin&#34;,
            &#34;sequenceNumber&#34;: 0,
            &#34;initiatedFrom&#34;: 2,
            &#34;taskFlags&#34;: {
                &#34;disabled&#34;: False
            }
        }

        return _taks_option_json

    @property
    def _json_dr_orchestration_subtasks(self):
        &#34;&#34;&#34;Getter for the subtask in DR orchestraion JSON . It is read only attribute&#34;&#34;&#34;

        _backup_subtask = {
            &#34;subTaskType&#34;: 1,
            &#34;operationType&#34;: 4046
        }

        return _backup_subtask

    @property
    def _json_dr_orchestration(self):
        &#34;&#34;&#34;Getter for the DRorchestration task in failover group JSON . It is read only attribute&#34;&#34;&#34;

        if not isinstance(self.dr_orchestration_options, dict):
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        initiatedFrom = self.dr_orchestration_options.get(
            &#34;initiatedfromMonitor&#34;, False)

        _dr_orchestration_json = {
            &#34;operationType&#34;: int(self.dr_orchestration_options.get(&#34;DROrchestrationType&#34;)),
            &#34;initiatedfromMonitor&#34;: initiatedFrom,
            &#34;advancedOptions&#34;: {
                &#34;skipDisableNetworkAdapter&#34;: self.dr_orchestration_options.get(&#34;skipDisableNetworkAdapter&#34;, False)
            }
        }

        # if initiated from monitor set To True
        if initiatedFrom:

            _dr_orchestration_json.update({
                    &#34;replicationInfo&#34;: {
                        &#34;replicationId&#34;: self.dr_orchestration_options.get(&#34;replicationIds&#34;, [0])
                    }
            })

        else:
            _dr_orchestration_json.update({
                &#34;vApp&#34;: {
                    &#34;vAppId&#34;: int(self.dr_orchestration_options.get(&#34;failoverGroupId&#34;)),
                    &#34;vAppName&#34;: self.dr_orchestration_options.get(&#34;failoverGroupName&#34;)
                }
            })

        return _dr_orchestration_json

    def testboot(self):
        &#34;&#34;&#34;Performs testboot failover operation.

            Args:

            Returns:
                (JobId, TaskId) - JobId and taskId of the Testboot job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        if not isinstance(self.dr_orchestration_options, dict):
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        self.dr_orchestration_options[&#34;DROrchestrationType&#34;] = &#34;7&#34;
        dr_orchestration_json = self._construct_dr_orchestration_operation_json()

        if not dr_orchestration_json:
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        return self._call_dr_orchestration_task(dr_orchestration_json)

    def planned_failover(self):
        &#34;&#34;&#34;Performs Planned failover operation.

            Args:

            Returns:
                (JobId, TaskId) - JobId and taskId of the Planned Failover job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        if not isinstance(self.dr_orchestration_options, dict):
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        self.dr_orchestration_options[&#34;DROrchestrationType&#34;] = &#34;1&#34;
        dr_orchestration_json = self._construct_dr_orchestration_operation_json()

        if not dr_orchestration_json:
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        return self._call_dr_orchestration_task(dr_orchestration_json)

    def unplanned_failover(self):
        &#34;&#34;&#34;Performs UnPlanned failover operation.

            Args:

            Returns:
                (JobId, TaskId) - JobId and taskId of the Unplanned Failover job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        if not isinstance(self.dr_orchestration_options, dict):
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        self.dr_orchestration_options[&#34;DROrchestrationType&#34;] = &#34;3&#34;
        dr_orchestration_json = self._construct_dr_orchestration_operation_json()

        if not dr_orchestration_json:
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        return self._call_dr_orchestration_task(dr_orchestration_json)

    def failback(self):
        &#34;&#34;&#34;Performs Failback operation.

            Args:

            Returns:
                (JobId, TaskId) - JobId and taskId of the failback job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        if not isinstance(self.dr_orchestration_options, dict):
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        self.dr_orchestration_options[&#34;DROrchestrationType&#34;] = &#34;2&#34;
        dr_orchestration_json = self._construct_dr_orchestration_operation_json()

        if not dr_orchestration_json:
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        return self._call_dr_orchestration_task(dr_orchestration_json)

    def undo_failover(self):
        &#34;&#34;&#34;Performs Undo Failover operation.

            Args:

            Returns:
                (JobId, TaskId) - JobId and taskId of the failback job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        if not isinstance(self.dr_orchestration_options, dict):
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        self.dr_orchestration_options[&#34;DROrchestrationType&#34;] = &#34;6&#34;
        dr_orchestration_json = self._construct_dr_orchestration_operation_json()

        if not dr_orchestration_json:
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        return self._call_dr_orchestration_task(dr_orchestration_json)

    def reverse_replication(self):
        &#34;&#34;&#34;Schedules and calls Reverse Replication

            Args:

            Returns:
                (JobId, TaskId) - JobId and taskId of the reverse replication job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        self.schedule_reverse_replication()
        return self.force_reverse_replication()

    def schedule_reverse_replication(self):
        &#34;&#34;&#34;Schedules Reverse Replication.

            Args:

            Returns:
                (TaskId) - TaskId of the scheduling reverse replication job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        if not isinstance(self.dr_orchestration_options, dict):
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        self.dr_orchestration_options[&#34;DROrchestrationType&#34;] = &#34;9&#34;
        dr_orchestration_json = self._json_dr_orchestration

        if not dr_orchestration_json:
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        return self._call_reverse_replication_task(dr_orchestration_json)

    def force_reverse_replication(self):
        &#34;&#34;&#34;Performs one reverse replication operation.

            Args:

            Returns:
                (JobId, TaskId) - JobId and taskId of the reverse replication job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        if not isinstance(self.dr_orchestration_options, dict):
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        self.dr_orchestration_options[&#34;DROrchestrationType&#34;] = &#34;9&#34;
        dr_orchestration_json = self._construct_dr_orchestration_operation_json()

        # We need to have this option in the request because &#34;powerOnVM&#34; is True by default.
        # If it is not set, both src and dst VMs will be booted up.
        dr_orchestration_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;adminOpts&#34;][&#34;drOrchestrationOption&#34;][&#34;advancedOptions&#34;].update(
                {&#34;powerOnVM&#34;: False})

        if not dr_orchestration_json:
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        return self._call_dr_orchestration_task(dr_orchestration_json)

    def revert_failover(self):
        &#34;&#34;&#34;Performs Revert Failover operation.

            Args:

            Returns:
                (JobId, TaskId) - JobId and taskId of the failback job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        if not isinstance(self.dr_orchestration_options, dict):
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        self.dr_orchestration_options[&#34;DROrchestrationType&#34;] = &#34;4&#34;
        dr_orchestration_json = self._construct_dr_orchestration_operation_json()

        if not dr_orchestration_json:
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        return self._call_dr_orchestration_task(dr_orchestration_json)

    def point_in_time_failover(self, timestamp, replication_id):
        &#34;&#34;&#34;Performs Point in time Failover operation.

            Args:

            Returns:
                (JobId, TaskId) - JobId and taskId of the failback job triggered

            Raises:
                SDKException:
                    if proper inputs are not provided
        &#34;&#34;&#34;
        if not isinstance(self.dr_orchestration_options, dict):
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        self.dr_orchestration_options[&#34;DROrchestrationType&#34;] = &#34;8&#34;
        dr_orchestration_json = self._construct_dr_orchestration_operation_json()

        if not dr_orchestration_json:
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        dr_orchestration_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;adminOpts&#34;][&#34;drOrchestrationOption&#34;][&#34;replicationInfo&#34;][
                &#34;configOption&#34;] = [
                    {&#34;pointInTime&#34;: int(timestamp),
                     &#34;replicationId&#34;: int(replication_id)}]

        return self._call_dr_orchestration_task(dr_orchestration_json)

    def validate_dr_orchestration_job(self, jobId):
        &#34;&#34;&#34; Validates DR orchestration job of jobId
            Args:
                JobId: Job Id of the DR orchestration job

            Returns:
                bool - boolean that represents whether the DR orchestration job finished successfully or not

            Raises:
                SDKException:
                    if proper inputs are not provided
                    If failover phase failed at any stage
        &#34;&#34;&#34;

        if not isinstance(jobId, str):
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        _replicationIds = self.dr_orchestration_options.get(
            &#34;replicationIds&#34;, [])
        if not _replicationIds:
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        # iterate over replication Ids
        for replicationId in iter(_replicationIds):

            dr_orchestration_job_stats_json = self._get_dr_orchestration_job_stats(
                str(jobId), str(replicationId))

            if dr_orchestration_job_stats_json and isinstance(
                    dr_orchestration_job_stats_json, dict):

                if &#34;phase&#34; in dr_orchestration_job_stats_json:

                    for phase in dr_orchestration_job_stats_json[&#34;phase&#34;]:

                        phase_type = DRJobPhaseToText(self.dr_orchestration_job_phase(phase[&#34;phase&#34;])).value
                        phase_state = phase[&#34;status&#34;]

                        if phase_state == 1:
                            o_str = &#39;Failed to complete phase: [&#39; + str(
                                phase_type) + &#39;] status: [&#39; + str(phase_state) + &#39;]&#39;
                            raise SDKException(
                                &#39;DROrchestrationOperations&#39;, &#39;102&#39;, o_str)

                else:

                    o_str = &#39;Failed to finish any phases in DR orchestration Job {0} \n&#39;.format(
                        jobId)
                    raise SDKException(
                        &#39;DROrchestrationOperations&#39;, &#39;102&#39;, o_str)

        return True

    def get_snapshot_list(self, guid: str, instance_id: int, timestamp_filter: bool = True):
        &#34;&#34;&#34; Gets snapshot list

            Args:
                guid (str): GUID of the spcified VM

                timestamp_filter (bool): whether to only return snapshots with timestamps

            Returns:
                list of dict: list of snapshot information
        &#34;&#34;&#34;
        vm_browse_req = &#34;{0}VMBrowse/{1}?instanceId={2}&amp;snapshots=true&#34;.format(
            self._commcell_object._web_service, guid, instance_id)
        (flag, response) = self._commcell_object._cvpysdk_object.make_request(
            method=&#39;GET&#39;, url=vm_browse_req)

        # only fetches snapshot info from the response
        if flag:
            if response.json():
                snapshots = response.json()[&#34;scList&#34;][0][&#34;snapshots&#34;]
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        if timestamp_filter:
            # we only need snapshots with timestamps
            snapshots = list(
                filter(lambda s: &#34;__GX_Recovery_Point_&#34; in s[&#34;name&#34;], snapshots))

            # adds integer timestamps to the list
            for s in snapshots:
                timestamp = s[&#34;name&#34;].split(&#34;_&#34;)[-1]
                s[&#34;timestamp&#34;] = int(timestamp)

        return snapshots


#################### private functions #####################

    def _get_dr_orchestration_operation_string(self, orchestration_type):
        &#34;&#34;&#34;Getter for dr orchestration operation type&#34;&#34;&#34;

        _orchestration_string = &#34;&#34;
        if orchestration_type == 1:
            _orchestration_string = &#34;Planned Failover&#34;

        elif orchestration_type == 2:
            _orchestration_string = &#34;Failback&#34;

        elif orchestration_type == 3:
            _orchestration_string = &#34;UnPlanned Failover&#34;

        elif orchestration_type == 4:
            _orchestration_string = &#34;Revert Failover&#34;

        elif orchestration_type == 6:
            _orchestration_string = &#34;Undo Failover&#34;

        elif orchestration_type == 7:
            _orchestration_string = &#34;TestBoot&#34;

        elif orchestration_type == 8:
            _orchestration_string = &#34;Point in Time Failover&#34;

        elif orchestration_type == 9:
            _orchestration_string = &#34;Reverse Replication&#34;

        return _orchestration_string

    def _construct_dr_orchestration_operation_json(self):
        &#34;&#34;&#34;
            Constructs DR orchestration operation json to invoke DR orchestration operation in the commcell

            Args:

            Returns: DR orchestration json dict

            Raises:
                SDKException:
                    if response is empty
                    if response is not success
        &#34;&#34;&#34;
        dr_orchestration_json = {
            &#34;taskInfo&#34;: {
                &#34;task&#34;: self._json_task,
                &#34;subTasks&#34;: [
                    {
                        &#34;subTaskOperation&#34;: 1,
                        &#34;subTask&#34;: self._json_dr_orchestration_subtasks,
                        &#34;options&#34;: {
                            &#34;adminOpts&#34;: {
                                &#34;drOrchestrationOption&#34;: self._json_dr_orchestration
                            }
                        }
                    }
                ]
            }
        }

        return dr_orchestration_json

    def _construct_reverse_replication_json(self):
        &#34;&#34;&#34;
            Constructs reverse replication json to invoke reverse replication task in the commcell

            Args:

            Returns: Reverse replication task dict

            Raises:
                SDKException:
                    if response is empty
                    if response is not success
        &#34;&#34;&#34;
        dr_orchestration_json = {

            &#34;drOrchestrationOption&#34;: self._json_dr_orchestration

        }

        return dr_orchestration_json

    def _call_dr_orchestration_task(self, dr_orchestration_json):
        &#34;&#34;&#34;
            Invokes DR orchestration operation task

            Args: dr orchestration json

                    taskInfo&#39;:  {
                    &#39;task&#39;:  {
                        &#39;taskFlags&#39;:  {
                            &#39;disabled&#39;:  0
                        },
                         &#39;taskType&#39;:  1,
                         &#39;ownerId&#39;:  1,
                         &#39;initiatedFrom&#39;:  2,
                         &#39;sequenceNumber&#39;:  0,
                         &#39;ownerName&#39;:  &#39;admin&#39;
                    },
                     &#39;subTasks&#39;:  {
                        &#39;subTask&#39;:  {
                            &#39;subTaskType&#39;:  1,
                             &#39;operationType&#39;:  4046
                        },
                         &#39;options&#39;:  {
                            &#39;adminOpts&#39;:  {
                                &#39;drOrchestrationOption&#39;:  {
                                    &#39;replicationInfo&#39;:  {
                                        &#39;replicationId&#39;:  [
                                            {
                                                &#39;val&#39;:  3
                                            },
                                             {
                                                &#39;val&#39;:  4
                                            }
                                        ]
                                    },
                                     &#39;vApp&#39;:  {
                                        &#39;vAppId&#39;:  26,
                                         &#39;vAppName&#39;:  &#39;fg1-automation1x21&#39;
                                    },
                                     &#39;advancedOptions&#39;:  {
                                        &#39;skipDisableNetworkAdapter&#39;:  0
                                    },
                                     &#39;operationType&#39;:  7,
                                     &#39;initiatedfromMonitor&#39;:  0
                                }
                            }
                        },
                         &#39;subTaskOperation&#39;:  1
                    }
                }
            }


            Returns:
                (JobId, TaskId) - JobId and taskId of the DR orchestration job triggered

            Raises:
                SDKException:
                    if response is empty
                    if response is not success
        &#34;&#34;&#34;
        if not isinstance(dr_orchestration_json, dict):
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        # passing the built json to start failover
        (flag, response) = self._commcell_object._cvpysdk_object.make_request(
            method=&#39;POST&#39;, url=self._CREATE_TASK, payload=dr_orchestration_json)

        if flag:
            if response.json():
                if &#39;error&#39; in response.json():
                    error_message = response.json()[&#39;error&#39;][&#39;errorMessage&#39;]
                    o_str = &#39;Failed to start {0} job \nError: &#34;{1}&#34;&#39;.format(
                        self._get_dr_orchestration_operation_string(self.dr_orchestration_options.get(&#34;DROrchestrationType&#34;)), error_message)

                    raise SDKException(
                        &#39;DROrchestrationOperations&#39;, &#39;102&#39;, o_str)
                else:
                    # return object of corresponding Virtual Machine Policy
                    # here
                    task_id = response.json()[&#39;taskId&#39;]
                    job_id = response.json()[&#39;jobIds&#39;][0]
                    return (job_id, task_id)

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _call_reverse_replication_task(self, dr_orchestration_json):
        &#34;&#34;&#34;
            Create reverse replication task

            Args: DR orchestration json

                drOrchestrationOption&#39;:  {
                    &#39;replicationInfo&#39;:  {
                        &#39;replicationId&#39;:  [
                            {
                                &#39;val&#39;:  3
                            },
                             {
                                &#39;val&#39;:  4
                            }
                        ]
                    },

                     &#39;advancedOptions&#39;:  {
                        &#39;skipDisableNetworkAdapter&#39;:  0
                    },
                     &#39;operationType&#39;:  9,
                     &#39;initiatedfromMonitor&#39;:  1
            }


            Returns:
                (TaskId) - taskId of the created reverse replication schedule

            Raises:
                SDKException:
                    if response is empty
                    if response is not success
        &#34;&#34;&#34;

        if not isinstance(dr_orchestration_json, dict):
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        # passing the built json to start DR orchestration
        (flag, response) = self._commcell_object._cvpysdk_object.make_request(
            method=&#39;PUT&#39;, url=self._REVERSE_REPLICATION_TASK, payload=dr_orchestration_json)

        if flag:
            if response.json():
                if &#39;error&#39; in response.json():
                    error_message = response.json()[&#39;error&#39;][&#39;errorMessage&#39;]
                    o_str = &#39;Failed to start {0} job \nError: &#34;{1}&#34;&#39;.format(
                        self._get_dr_orchestration_operation_string(self.dr_orchestration_options.get(&#34;DROrchestrationType&#34;)), error_message)

                    raise SDKException(
                        &#39;DROrchestrationOperations&#39;, &#39;102&#39;, o_str)
                else:
                    # return object of corresponding Virtual Machine Policy
                    # here
                    task_id = response.json()[&#39;taskId&#39;]
                    return (task_id)

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _get_dr_orchestration_job_stats(self, jobId, replicationId):
        &#34;&#34;&#34; Gets DR orchestration job stats json
            Args:
                JobId: Job Id of the DR orchestration job
                replicationId: replication Id of the DR orchestration

            Returns:
                dict - DR orchestration job phases
                {
                            &#34;jobId&#34;: 4240,
                            &#34;replicationId&#34;: 7,
                            &#34;phase&#34;: [
                                {
                                    &#34;phase&#34;: 15,
                                    &#34;status&#34;: 0,
                                    &#34;startTime&#34;: {
                                        &#34;_type_&#34;: 55,
                                        &#34;time&#34;: 1516293332
                                    },
                                    &#34;endTime&#34;: {
                                        &#34;_type_&#34;: 55,
                                        &#34;time&#34;: 1516293335
                                    },
                                    &#34;entity&#34;: {
                                        &#34;clientName&#34;: &#34;failovervm1&#34;
                                    }
                                },
                                {
                                    &#34;phase&#34;: 17,
                                    &#34;status&#34;: 0,
                                    &#34;startTime&#34;: {
                                        &#34;_type_&#34;: 55,
                                        &#34;time&#34;: 1516293335
                                    },
                                    &#34;endTime&#34;: {
                                        &#34;_type_&#34;: 55,
                                        &#34;time&#34;: 1516293340
                                    },
                                    &#34;entity&#34;: {
                                        &#34;clientName&#34;: &#34;failovervm1&#34;
                                    }
                                },
                                {
                                    &#34;phase&#34;: 20,
                                    &#34;status&#34;: 0,
                                    &#34;startTime&#34;: {
                                        &#34;_type_&#34;: 55,
                                        &#34;time&#34;: 1516293340
                                    },
                                    &#34;endTime&#34;: {
                                        &#34;_type_&#34;: 55,
                                        &#34;time&#34;: 1516293345
                                    },
                                    &#34;entity&#34;: {
                                        &#34;clientName&#34;: &#34;failovervm1&#34;
                                    }
                                }
            }

            Raises:
                SDKException:
                    if proper inputs are not provided
                    If DR orchestration phase failed at any stage
        &#34;&#34;&#34;

        if not isinstance(jobId, str) and not isinstance(
                replicationId, str):
            raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

        _DR_JOB_STATS = (self._services[&#39;FAILOVER_GROUP_JOB_STATS&#39;]
                         if self._commcell_object.commserv_version &gt; 30
                         else self._services[&#39;DR_GROUP_JOB_STATS&#39;]) % (jobId, self.dr_group_id, replicationId)

        # passing the built json to get DR orchestration job phases
        (flag, response) = self._commcell_object._cvpysdk_object.make_request(
            method=&#39;GET&#39;, url=_DR_JOB_STATS)

        if flag:
            if response.json():
                if &#39;error&#39; in response.json():
                    error_message = response.json()[&#39;error&#39;][&#39;errorMessage&#39;]
                    o_str = &#39;Failed to validate DR orchestration job {0} \nError: &#34;{1}&#34;&#39;.format(jobId,
                                                                                                error_message)

                    raise SDKException(
                        &#39;DROrchestrationOperations&#39;, &#39;102&#39;, o_str)
                else:

                    if &#39;job&#39; in response.json():

                        # return DR orchestration job phases
                        return (response.json()[&#34;job&#34;][0])

                    else:
                        raise SDKException(
                            &#39;DROrchestrationOperations&#39;, &#39;102&#39;, &#39;Failed to start DR orchestration job&#39;)

            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.dr_group_id"><code class="name">var <span class="ident">dr_group_id</span></code></dt>
<dd>
<div class="desc"><p>Getter DR group Id</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/drorchestrationoperations.py#L149-L152" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def dr_group_id(self):
    &#34;&#34;&#34;Getter DR group Id&#34;&#34;&#34;
    return int(self.dr_orchestration_options.get(&#34;failoverGroupId&#34;, 0))</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.dr_orchestration_job_phase"><code class="name">var <span class="ident">dr_orchestration_job_phase</span></code></dt>
<dd>
<div class="desc"><p>Args:</p>
<p>Returns: DR orchestration job phase dict</p>
<p>Raises:</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/drorchestrationoperations.py#L124-L137" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def dr_orchestration_job_phase(self):
    &#34;&#34;&#34;
    Args:

    Returns: DR orchestration job phase dict

    Raises:
    &#34;&#34;&#34;
    if not self._dr_orchestration_job_phase:
        _dr_orchestration_job_phase = DRJobPhases

        self._dr_orchestration_job_phase = _dr_orchestration_job_phase
    return self._dr_orchestration_job_phase</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.dr_orchestration_options"><code class="name">var <span class="ident">dr_orchestration_options</span></code></dt>
<dd>
<div class="desc"><p>Getter dr orchestration options json</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/drorchestrationoperations.py#L139-L142" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def dr_orchestration_options(self):
    &#34;&#34;&#34;Getter dr orchestration options json&#34;&#34;&#34;
    return self._dr_orchestration_option</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.failback"><code class="name flex">
<span>def <span class="ident">failback</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Performs Failback operation.</p>
<p>Args:</p>
<h2 id="returns">Returns</h2>
<p>(JobId, TaskId) - JobId and taskId of the failback job triggered</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if proper inputs are not provided</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/drorchestrationoperations.py#L288-L309" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def failback(self):
    &#34;&#34;&#34;Performs Failback operation.

        Args:

        Returns:
            (JobId, TaskId) - JobId and taskId of the failback job triggered

        Raises:
            SDKException:
                if proper inputs are not provided
    &#34;&#34;&#34;
    if not isinstance(self.dr_orchestration_options, dict):
        raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

    self.dr_orchestration_options[&#34;DROrchestrationType&#34;] = &#34;2&#34;
    dr_orchestration_json = self._construct_dr_orchestration_operation_json()

    if not dr_orchestration_json:
        raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

    return self._call_dr_orchestration_task(dr_orchestration_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.force_reverse_replication"><code class="name flex">
<span>def <span class="ident">force_reverse_replication</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Performs one reverse replication operation.</p>
<p>Args:</p>
<h2 id="returns">Returns</h2>
<p>(JobId, TaskId) - JobId and taskId of the reverse replication job triggered</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if proper inputs are not provided</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/drorchestrationoperations.py#L372-L399" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def force_reverse_replication(self):
    &#34;&#34;&#34;Performs one reverse replication operation.

        Args:

        Returns:
            (JobId, TaskId) - JobId and taskId of the reverse replication job triggered

        Raises:
            SDKException:
                if proper inputs are not provided
    &#34;&#34;&#34;
    if not isinstance(self.dr_orchestration_options, dict):
        raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

    self.dr_orchestration_options[&#34;DROrchestrationType&#34;] = &#34;9&#34;
    dr_orchestration_json = self._construct_dr_orchestration_operation_json()

    # We need to have this option in the request because &#34;powerOnVM&#34; is True by default.
    # If it is not set, both src and dst VMs will be booted up.
    dr_orchestration_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
        &#34;adminOpts&#34;][&#34;drOrchestrationOption&#34;][&#34;advancedOptions&#34;].update(
            {&#34;powerOnVM&#34;: False})

    if not dr_orchestration_json:
        raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

    return self._call_dr_orchestration_task(dr_orchestration_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.get_snapshot_list"><code class="name flex">
<span>def <span class="ident">get_snapshot_list</span></span>(<span>self, guid: str, instance_id: int, timestamp_filter: bool = True)</span>
</code></dt>
<dd>
<div class="desc"><p>Gets snapshot list</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>guid</code></strong> :&ensp;<code>str</code></dt>
<dd>GUID of the spcified VM</dd>
<dt><strong><code>timestamp_filter</code></strong> :&ensp;<code>bool</code></dt>
<dd>whether to only return snapshots with timestamps</dd>
</dl>
<h2 id="returns">Returns</h2>
<dl>
<dt><code>list</code> of <code>dict</code></dt>
<dd>list of snapshot information</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/drorchestrationoperations.py#L506-L543" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_snapshot_list(self, guid: str, instance_id: int, timestamp_filter: bool = True):
    &#34;&#34;&#34; Gets snapshot list

        Args:
            guid (str): GUID of the spcified VM

            timestamp_filter (bool): whether to only return snapshots with timestamps

        Returns:
            list of dict: list of snapshot information
    &#34;&#34;&#34;
    vm_browse_req = &#34;{0}VMBrowse/{1}?instanceId={2}&amp;snapshots=true&#34;.format(
        self._commcell_object._web_service, guid, instance_id)
    (flag, response) = self._commcell_object._cvpysdk_object.make_request(
        method=&#39;GET&#39;, url=vm_browse_req)

    # only fetches snapshot info from the response
    if flag:
        if response.json():
            snapshots = response.json()[&#34;scList&#34;][0][&#34;snapshots&#34;]
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._commcell_object._update_response_(
            response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    if timestamp_filter:
        # we only need snapshots with timestamps
        snapshots = list(
            filter(lambda s: &#34;__GX_Recovery_Point_&#34; in s[&#34;name&#34;], snapshots))

        # adds integer timestamps to the list
        for s in snapshots:
            timestamp = s[&#34;name&#34;].split(&#34;_&#34;)[-1]
            s[&#34;timestamp&#34;] = int(timestamp)

    return snapshots</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.planned_failover"><code class="name flex">
<span>def <span class="ident">planned_failover</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Performs Planned failover operation.</p>
<p>Args:</p>
<h2 id="returns">Returns</h2>
<p>(JobId, TaskId) - JobId and taskId of the Planned Failover job triggered</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if proper inputs are not provided</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/drorchestrationoperations.py#L242-L263" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def planned_failover(self):
    &#34;&#34;&#34;Performs Planned failover operation.

        Args:

        Returns:
            (JobId, TaskId) - JobId and taskId of the Planned Failover job triggered

        Raises:
            SDKException:
                if proper inputs are not provided
    &#34;&#34;&#34;
    if not isinstance(self.dr_orchestration_options, dict):
        raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

    self.dr_orchestration_options[&#34;DROrchestrationType&#34;] = &#34;1&#34;
    dr_orchestration_json = self._construct_dr_orchestration_operation_json()

    if not dr_orchestration_json:
        raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

    return self._call_dr_orchestration_task(dr_orchestration_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.point_in_time_failover"><code class="name flex">
<span>def <span class="ident">point_in_time_failover</span></span>(<span>self, timestamp, replication_id)</span>
</code></dt>
<dd>
<div class="desc"><p>Performs Point in time Failover operation.</p>
<p>Args:</p>
<h2 id="returns">Returns</h2>
<p>(JobId, TaskId) - JobId and taskId of the failback job triggered</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if proper inputs are not provided</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/drorchestrationoperations.py#L424-L451" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def point_in_time_failover(self, timestamp, replication_id):
    &#34;&#34;&#34;Performs Point in time Failover operation.

        Args:

        Returns:
            (JobId, TaskId) - JobId and taskId of the failback job triggered

        Raises:
            SDKException:
                if proper inputs are not provided
    &#34;&#34;&#34;
    if not isinstance(self.dr_orchestration_options, dict):
        raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

    self.dr_orchestration_options[&#34;DROrchestrationType&#34;] = &#34;8&#34;
    dr_orchestration_json = self._construct_dr_orchestration_operation_json()

    if not dr_orchestration_json:
        raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

    dr_orchestration_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
        &#34;adminOpts&#34;][&#34;drOrchestrationOption&#34;][&#34;replicationInfo&#34;][
            &#34;configOption&#34;] = [
                {&#34;pointInTime&#34;: int(timestamp),
                 &#34;replicationId&#34;: int(replication_id)}]

    return self._call_dr_orchestration_task(dr_orchestration_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.reverse_replication"><code class="name flex">
<span>def <span class="ident">reverse_replication</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Schedules and calls Reverse Replication</p>
<p>Args:</p>
<h2 id="returns">Returns</h2>
<p>(JobId, TaskId) - JobId and taskId of the reverse replication job triggered</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if proper inputs are not provided</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/drorchestrationoperations.py#L334-L347" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def reverse_replication(self):
    &#34;&#34;&#34;Schedules and calls Reverse Replication

        Args:

        Returns:
            (JobId, TaskId) - JobId and taskId of the reverse replication job triggered

        Raises:
            SDKException:
                if proper inputs are not provided
    &#34;&#34;&#34;
    self.schedule_reverse_replication()
    return self.force_reverse_replication()</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.revert_failover"><code class="name flex">
<span>def <span class="ident">revert_failover</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Performs Revert Failover operation.</p>
<p>Args:</p>
<h2 id="returns">Returns</h2>
<p>(JobId, TaskId) - JobId and taskId of the failback job triggered</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if proper inputs are not provided</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/drorchestrationoperations.py#L401-L422" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def revert_failover(self):
    &#34;&#34;&#34;Performs Revert Failover operation.

        Args:

        Returns:
            (JobId, TaskId) - JobId and taskId of the failback job triggered

        Raises:
            SDKException:
                if proper inputs are not provided
    &#34;&#34;&#34;
    if not isinstance(self.dr_orchestration_options, dict):
        raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

    self.dr_orchestration_options[&#34;DROrchestrationType&#34;] = &#34;4&#34;
    dr_orchestration_json = self._construct_dr_orchestration_operation_json()

    if not dr_orchestration_json:
        raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

    return self._call_dr_orchestration_task(dr_orchestration_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.schedule_reverse_replication"><code class="name flex">
<span>def <span class="ident">schedule_reverse_replication</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Schedules Reverse Replication.</p>
<p>Args:</p>
<h2 id="returns">Returns</h2>
<p>(TaskId) - TaskId of the scheduling reverse replication job triggered</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if proper inputs are not provided</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/drorchestrationoperations.py#L349-L370" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def schedule_reverse_replication(self):
    &#34;&#34;&#34;Schedules Reverse Replication.

        Args:

        Returns:
            (TaskId) - TaskId of the scheduling reverse replication job triggered

        Raises:
            SDKException:
                if proper inputs are not provided
    &#34;&#34;&#34;
    if not isinstance(self.dr_orchestration_options, dict):
        raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

    self.dr_orchestration_options[&#34;DROrchestrationType&#34;] = &#34;9&#34;
    dr_orchestration_json = self._json_dr_orchestration

    if not dr_orchestration_json:
        raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

    return self._call_reverse_replication_task(dr_orchestration_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.testboot"><code class="name flex">
<span>def <span class="ident">testboot</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Performs testboot failover operation.</p>
<p>Args:</p>
<h2 id="returns">Returns</h2>
<p>(JobId, TaskId) - JobId and taskId of the Testboot job triggered</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if proper inputs are not provided</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/drorchestrationoperations.py#L219-L240" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def testboot(self):
    &#34;&#34;&#34;Performs testboot failover operation.

        Args:

        Returns:
            (JobId, TaskId) - JobId and taskId of the Testboot job triggered

        Raises:
            SDKException:
                if proper inputs are not provided
    &#34;&#34;&#34;
    if not isinstance(self.dr_orchestration_options, dict):
        raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

    self.dr_orchestration_options[&#34;DROrchestrationType&#34;] = &#34;7&#34;
    dr_orchestration_json = self._construct_dr_orchestration_operation_json()

    if not dr_orchestration_json:
        raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

    return self._call_dr_orchestration_task(dr_orchestration_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.undo_failover"><code class="name flex">
<span>def <span class="ident">undo_failover</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Performs Undo Failover operation.</p>
<p>Args:</p>
<h2 id="returns">Returns</h2>
<p>(JobId, TaskId) - JobId and taskId of the failback job triggered</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if proper inputs are not provided</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/drorchestrationoperations.py#L311-L332" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def undo_failover(self):
    &#34;&#34;&#34;Performs Undo Failover operation.

        Args:

        Returns:
            (JobId, TaskId) - JobId and taskId of the failback job triggered

        Raises:
            SDKException:
                if proper inputs are not provided
    &#34;&#34;&#34;
    if not isinstance(self.dr_orchestration_options, dict):
        raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

    self.dr_orchestration_options[&#34;DROrchestrationType&#34;] = &#34;6&#34;
    dr_orchestration_json = self._construct_dr_orchestration_operation_json()

    if not dr_orchestration_json:
        raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

    return self._call_dr_orchestration_task(dr_orchestration_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.unplanned_failover"><code class="name flex">
<span>def <span class="ident">unplanned_failover</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Performs UnPlanned failover operation.</p>
<p>Args:</p>
<h2 id="returns">Returns</h2>
<p>(JobId, TaskId) - JobId and taskId of the Unplanned Failover job triggered</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if proper inputs are not provided</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/drorchestrationoperations.py#L265-L286" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def unplanned_failover(self):
    &#34;&#34;&#34;Performs UnPlanned failover operation.

        Args:

        Returns:
            (JobId, TaskId) - JobId and taskId of the Unplanned Failover job triggered

        Raises:
            SDKException:
                if proper inputs are not provided
    &#34;&#34;&#34;
    if not isinstance(self.dr_orchestration_options, dict):
        raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

    self.dr_orchestration_options[&#34;DROrchestrationType&#34;] = &#34;3&#34;
    dr_orchestration_json = self._construct_dr_orchestration_operation_json()

    if not dr_orchestration_json:
        raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

    return self._call_dr_orchestration_task(dr_orchestration_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.validate_dr_orchestration_job"><code class="name flex">
<span>def <span class="ident">validate_dr_orchestration_job</span></span>(<span>self, jobId)</span>
</code></dt>
<dd>
<div class="desc"><p>Validates DR orchestration job of jobId</p>
<h2 id="args">Args</h2>
<dl>
<dt><strong><code>JobId</code></strong></dt>
<dd>Job Id of the DR orchestration job</dd>
</dl>
<h2 id="returns">Returns</h2>
<p>bool - boolean that represents whether the DR orchestration job finished successfully or not</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if proper inputs are not provided
If failover phase failed at any stage</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/drorchestration/drorchestrationoperations.py#L453-L504" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def validate_dr_orchestration_job(self, jobId):
    &#34;&#34;&#34; Validates DR orchestration job of jobId
        Args:
            JobId: Job Id of the DR orchestration job

        Returns:
            bool - boolean that represents whether the DR orchestration job finished successfully or not

        Raises:
            SDKException:
                if proper inputs are not provided
                If failover phase failed at any stage
    &#34;&#34;&#34;

    if not isinstance(jobId, str):
        raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

    _replicationIds = self.dr_orchestration_options.get(
        &#34;replicationIds&#34;, [])
    if not _replicationIds:
        raise SDKException(&#39;DROrchestrationOperations&#39;, &#39;101&#39;)

    # iterate over replication Ids
    for replicationId in iter(_replicationIds):

        dr_orchestration_job_stats_json = self._get_dr_orchestration_job_stats(
            str(jobId), str(replicationId))

        if dr_orchestration_job_stats_json and isinstance(
                dr_orchestration_job_stats_json, dict):

            if &#34;phase&#34; in dr_orchestration_job_stats_json:

                for phase in dr_orchestration_job_stats_json[&#34;phase&#34;]:

                    phase_type = DRJobPhaseToText(self.dr_orchestration_job_phase(phase[&#34;phase&#34;])).value
                    phase_state = phase[&#34;status&#34;]

                    if phase_state == 1:
                        o_str = &#39;Failed to complete phase: [&#39; + str(
                            phase_type) + &#39;] status: [&#39; + str(phase_state) + &#39;]&#39;
                        raise SDKException(
                            &#39;DROrchestrationOperations&#39;, &#39;102&#39;, o_str)

            else:

                o_str = &#39;Failed to finish any phases in DR orchestration Job {0} \n&#39;.format(
                    jobId)
                raise SDKException(
                    &#39;DROrchestrationOperations&#39;, &#39;102&#39;, o_str)

    return True</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.drorchestration" href="index.html">cvpysdk.drorchestration</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations" href="#cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations">DROrchestrationOperations</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.dr_group_id" href="#cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.dr_group_id">dr_group_id</a></code></li>
<li><code><a title="cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.dr_orchestration_job_phase" href="#cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.dr_orchestration_job_phase">dr_orchestration_job_phase</a></code></li>
<li><code><a title="cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.dr_orchestration_options" href="#cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.dr_orchestration_options">dr_orchestration_options</a></code></li>
<li><code><a title="cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.failback" href="#cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.failback">failback</a></code></li>
<li><code><a title="cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.force_reverse_replication" href="#cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.force_reverse_replication">force_reverse_replication</a></code></li>
<li><code><a title="cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.get_snapshot_list" href="#cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.get_snapshot_list">get_snapshot_list</a></code></li>
<li><code><a title="cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.planned_failover" href="#cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.planned_failover">planned_failover</a></code></li>
<li><code><a title="cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.point_in_time_failover" href="#cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.point_in_time_failover">point_in_time_failover</a></code></li>
<li><code><a title="cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.reverse_replication" href="#cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.reverse_replication">reverse_replication</a></code></li>
<li><code><a title="cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.revert_failover" href="#cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.revert_failover">revert_failover</a></code></li>
<li><code><a title="cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.schedule_reverse_replication" href="#cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.schedule_reverse_replication">schedule_reverse_replication</a></code></li>
<li><code><a title="cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.testboot" href="#cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.testboot">testboot</a></code></li>
<li><code><a title="cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.undo_failover" href="#cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.undo_failover">undo_failover</a></code></li>
<li><code><a title="cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.unplanned_failover" href="#cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.unplanned_failover">unplanned_failover</a></code></li>
<li><code><a title="cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.validate_dr_orchestration_job" href="#cvpysdk.drorchestration.drorchestrationoperations.DROrchestrationOperations.validate_dr_orchestration_job">validate_dr_orchestration_job</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>