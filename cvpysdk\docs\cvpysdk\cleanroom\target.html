<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.cleanroom.target API documentation</title>
<meta name="description" content="Main file for performing Cleanroom Target specific operations …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.cleanroom.target</code></h1>
</header>
<section id="section-intro">
<p>Main file for performing Cleanroom Target specific operations.</p>
<p>CleanroomTargets and CleanroomTarget are 2 classes defined in this file.</p>
<p>CleanroomTargets:
Class for representing all the cleanroom targets</p>
<p>CleanroomTarget:
Class for a single cleanroom target selected, and to perform operations on that cleanroom target</p>
<p>cleanroomTargets:
<strong>init</strong>()
&ndash;
initialize object of CleanroomTargets class</p>
<pre><code>__str__()                   --  returns all the Cleanroom Targets

_get_cleanroom_targets()     -- Gets all the cleanroom targets

has_cleanroom_target()       -- Checks if a target is present in the commcell.

get()                        --  returns the cleanroom target class object of the input target name

refresh()                   --  refresh the targets present in the client
</code></pre>
<h2 id="cleanroomtargets-attributes">Cleanroomtargets Attributes</h2>
<pre><code>**all_targets**             --  returns the dictionary consisting of all the targets that are
present in the commcell and their information such as id and name
</code></pre>
<h2 id="cleanroomtarget">Cleanroomtarget</h2>
<p><strong>init</strong>()
&ndash;
initialize object of CleanroomTarget with the specified cleanroom target name</p>
<p>_get_cleanroom_target_id()
&ndash;
method to get the cleanroom target id</p>
<p>_get_cleanroom_target_properties()
&ndash;
get the properties of this recovery target</p>
<p>_delete_cleanroom_target()
&ndash; Deletes the cleanroom target</p>
<p>delete()
&ndash; Deletes the cleanroom target</p>
<p>refresh()
&ndash;
refresh the object properties</p>
<h2 id="cleanroomtarget-attributes">Cleanroomtarget Attributes</h2>
<pre><code>**cleanroom_target_id**      -- Returns the id of the cleanroom target
**cleanroom_target_name**    -- Returns the name of the cleanroom Target
**destination_hypervisor**  -- Returns the name of destination hypervisor
**vm_prefix**               -- Returns the prefix of the vm name
**destination_host**        -- Returns the destination host
**storage_account**          -- Returns the storage_account host
**policy_type**             -- Returns the policy type
**application_type**          -- Returns the application type
**restore_as_managed_vm**   -- Returns the restore_as_managed_vm
**region**                  -- Returns the region
**expiration_time**         -- Returns the _expiration_time
**vm_suffix**               -- Returns the vm_suffix
**vm_prefix**               -- Returns the vm_prefix
**access_node**             -- Returns the access_node
**access_node_client_group** -- Returns the access_node_client_group
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/target.py#L1-L467" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Main file for performing Cleanroom Target specific operations.

CleanroomTargets and CleanroomTarget are 2 classes defined in this file.

CleanroomTargets:     Class for representing all the cleanroom targets

CleanroomTarget:      Class for a single cleanroom target selected, and to perform operations on that cleanroom target


cleanroomTargets:
    __init__()                   --  initialize object of CleanroomTargets class

    __str__()                   --  returns all the Cleanroom Targets

    _get_cleanroom_targets()     -- Gets all the cleanroom targets

    has_cleanroom_target()       -- Checks if a target is present in the commcell.

    get()                        --  returns the cleanroom target class object of the input target name

    refresh()                   --  refresh the targets present in the client

cleanroomTargets Attributes
--------------------------

    **all_targets**             --  returns the dictionary consisting of all the targets that are
    present in the commcell and their information such as id and name

CleanroomTarget:
    __init__()                   --   initialize object of CleanroomTarget with the specified cleanroom target name

    _get_cleanroom_target_id()   --   method to get the cleanroom target id

    _get_cleanroom_target_properties()  --   get the properties of this recovery target

    _delete_cleanroom_target()    -- Deletes the cleanroom target

    delete()                     -- Deletes the cleanroom target

    refresh()                   --   refresh the object properties

CleanroomTarget Attributes
--------------------------

    **cleanroom_target_id**      -- Returns the id of the cleanroom target
    **cleanroom_target_name**    -- Returns the name of the cleanroom Target
    **destination_hypervisor**  -- Returns the name of destination hypervisor
    **vm_prefix**               -- Returns the prefix of the vm name
    **destination_host**        -- Returns the destination host
    **storage_account**          -- Returns the storage_account host
    **policy_type**             -- Returns the policy type
    **application_type**          -- Returns the application type
    **restore_as_managed_vm**   -- Returns the restore_as_managed_vm
    **region**                  -- Returns the region
    **expiration_time**         -- Returns the _expiration_time
    **vm_suffix**               -- Returns the vm_suffix
    **vm_prefix**               -- Returns the vm_prefix
    **access_node**             -- Returns the access_node
    **access_node_client_group** -- Returns the access_node_client_group

&#34;&#34;&#34;
from __future__ import absolute_import
from __future__ import unicode_literals

from cvpysdk.exception import SDKException


class CleanroomTargets:

    &#34;&#34;&#34;Class for representing all the Cleanroom targets&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the CleanroomTargets class.

            Args:
                commcell_object (object)  --  instance of the Commcell class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object

        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_
        self._RECOVERY_TARGETS_API = self._services[&#39;GET_ALL_RECOVERY_TARGETS&#39;]

        self._cleanroom_targets = None
        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all targets .

            Returns:
                str     -   string of all the targets

        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\n\n&#39;.format(&#39;S. No.&#39;, &#39;CleanroomTargets&#39;)

        for index, cleanroom_target in enumerate(self._cleanroom_targets):
            sub_str = &#39;{:^5}\t{:20}\n&#39;.format(
                index + 1,
                cleanroom_target
            )
            representation_string += sub_str

        return representation_string.strip()

    def _get_cleanroom_targets(self):
        &#34;&#34;&#34;Gets all the cleanroom targets.

            Returns:
                dict - consists of all targets in the client
                    {
                         &#34;target1_name&#34;: target1_id,
                         &#34;target2_name&#34;: target2_id
                    }

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._RECOVERY_TARGETS_API)
        if flag:
            if response.json() and &#39;recoveryTargets&#39; in response.json():
                cleanroom_target_dict = {}
                for cleanroomTarget in response.json()[&#39;recoveryTargets&#39;]:
                    if cleanroomTarget[&#39;applicationType&#39;] == &#34;CLEAN_ROOM&#34;:
                        temp_name = cleanroomTarget[&#39;name&#39;].lower()
                        cleanroom_target_dict[temp_name] = str(cleanroomTarget[&#39;id&#39;])

                return cleanroom_target_dict
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    @property
    def all_targets(self):
        &#34;&#34;&#34;Returns dict of all the targets.

         Returns dict    -   consists of all targets

                {
                    &#34;target1_name&#34;: target1_id,

                    &#34;target2_name&#34;: target2_id
                }

        &#34;&#34;&#34;
        return self._cleanroom_targets

    def has_cleanroom_target(self, target_name):
        &#34;&#34;&#34;Checks if a target is present in the commcell.

            Args:
                target_name (str)  --  name of the target

            Returns:
                bool - boolean output whether the target is present in commcell or not

            Raises:
                SDKException:
                    if type of the target name argument is not string

        &#34;&#34;&#34;
        if not isinstance(target_name, str):
            raise SDKException(&#39;Target&#39;, &#39;101&#39;)

        return self._cleanroom_targets and target_name.lower() in self._cleanroom_targets

    def get(self, cleanroom_target_name):
        &#34;&#34;&#34;Returns a target object.

            Args:
                cleanroom_target_name (str)  --  name of the target

            Returns:
                object - instance of the target class for the given target name

            Raises:
                SDKException:
                    if type of the target name argument is not string

                    if no target exists with the given name

        &#34;&#34;&#34;
        if not isinstance(cleanroom_target_name, str):
            raise SDKException(&#39;Target&#39;, &#39;101&#39;)
        else:
            cleanroom_target_name = cleanroom_target_name.lower()

            if self.has_cleanroom_target(cleanroom_target_name):
                return CleanroomTarget(
                    self._commcell_object, cleanroom_target_name, self.all_targets[cleanroom_target_name])

            raise SDKException(&#39;RecoveryTarget&#39;, &#39;102&#39;, &#39;No target exists with name: {0}&#39;.format(cleanroom_target_name))

    def refresh(self):
        &#34;&#34;&#34;Refresh the cleanroom targets&#34;&#34;&#34;
        self._cleanroom_targets = self._get_cleanroom_targets()


class CleanroomTarget:
    &#34;&#34;&#34;Class for performing target operations&#34;&#34;&#34;

    def __init__(self, commcell_object, cleanroom_target_name, cleanroom_target_id=None):
        &#34;&#34;&#34;Initialize the instance of the CleanroomTarget class.

            Args:
                commcell_object   (object)    --  instance of the Commcell class

                cleanroom_target_name      (str)       --  name of the target

                cleanroom_target_id        (str)       --  id of the target

        &#34;&#34;&#34;
        self._commcell_object = commcell_object

        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_
        self._cleanroom_target_name = cleanroom_target_name.lower()

        if cleanroom_target_id:
            # Use the target id mentioned in the arguments
            self._cleanroom_target_id = str(cleanroom_target_id)
        else:
            # Get the target id if target id is not provided
            self._cleanroom_target_id = self._get_cleanroom_target_id()
        self._RECOVERY_TARGET_API = self._services[&#39;GET_RECOVERY_TARGET&#39;] % self._cleanroom_target_id

        self._cleanroom_target_properties = None

        self._policy_type = None
        self._application_type = None
        self._destination_hypervisor = None
        self._access_node = None
        self._access_node_client_group = None
        self._users = []
        self._user_groups = []
        self._vm_prefix = &#39;&#39;
        self._vm_suffix = &#39;&#39;
        self._expiration_time = None

        self._region = None
        self._availability_zone = None
        self._storage_account = None
        self._restore_as_managed_vm = None

        self.refresh()

    def _get_cleanroom_target_id(self):
        &#34;&#34;&#34;Gets the target id associated with this target.

            Returns:
                str - id associated with this target

        &#34;&#34;&#34;
        target = CleanroomTargets(self._commcell_object)
        return target.all_targets[self._cleanroom_target_name]

    def _delete_cleanroom_target(self):
        &#34;&#34;&#34;Deletes the cleanroom target

            Raises:
                SDKException:
                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;DELETE&#39;, self._RECOVERY_TARGET_API)
        if flag:
            return flag
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _set_policy_type(self, policy_type):
        &#34;&#34;&#34;Sets the policy type&#34;&#34;&#34;
        if policy_type == &#34;AMAZON&#34;:
            self._policy_type = 1
        elif policy_type == &#34;MICROSOFT&#34;:
            self._policy_type = 2
        elif policy_type == &#34;AZURE_RESOURCE_MANAGER&#34;:
            self._policy_type = 7
        elif policy_type in [&#34;VMW_BACKUP_LABTEMPLATE&#34;, &#34;VMW_LIVEMOUNT&#34;]:
            self._policy_type = 13
        else:
            self._policy_type = -1

    def _get_cleanroom_target_properties(self):
        &#34;&#34;&#34;Gets the target properties of this target.

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._RECOVERY_TARGET_API)

        if flag:
            if response.json() and &#39;entity&#39; in response.json():
                self._cleanroom_target_properties = response.json()
                self._application_type = self._cleanroom_target_properties[&#39;entity&#39;][&#39;applicationType&#39;]
                self._destination_hypervisor = self._cleanroom_target_properties[&#39;entity&#39;][&#39;destinationHypervisor&#39;][
                    &#39;name&#39;]
                self._vm_suffix = self._cleanroom_target_properties[&#34;vmDisplayName&#34;].get(&#34;suffix&#34;, &#34;&#34;)
                self._vm_prefix = self._cleanroom_target_properties[&#34;vmDisplayName&#34;].get(&#34;prefix&#34;, &#34;&#34;)
                self._access_node = self._cleanroom_target_properties[&#34;accessNode&#34;].get(&#34;type&#34;, &#34;&#34;)
                self._access_node_client_group = (self._cleanroom_target_properties.get(&#39;proxyClientGroupEntity&#39;, {})
                                                  .get(&#39;clientGroupName&#39;))
                self._users = self._cleanroom_target_properties.get(&#39;securityOptions&#39;, {}).get(&#39;users&#39;, [])
                self._user_groups = self._cleanroom_target_properties.get(&#39;securityOptions&#39;, {}).get(&#39;userGroups&#39;, [])
                policy_type = self._cleanroom_target_properties[&#34;entity&#34;].get(&#34;policyType&#34;, &#34;&#34;)
                self._set_policy_type(policy_type)

                if self._policy_type == 7:
                    self._region = (self._cleanroom_target_properties.get(&#39;cloudDestinationOptions&#39;, {})
                                    .get(&#39;region&#39;, {})
                                    .get(&#39;name&#39;))
                    self._availability_zone = (self._cleanroom_target_properties.get(&#39;cloudDestinationOptions&#39;, {})
                                               .get(&#39;availabilityZone&#39;))
                    self._storage_account = (self._cleanroom_target_properties.get(&#34;destinationOptions&#34;, {})
                                             .get(&#34;dataStore&#34;, &#34;&#34;))

                    self._vm_size = (self._cleanroom_target_properties.get(&#39;amazonPolicy&#39;, {})
                                     .get(&#39;vmInstanceTypes&#39;, [{}])[0]
                                     .get(&#39;vmInstanceTypeName&#39;, &#39;&#39;))
                    self._disk_type = (self._cleanroom_target_properties.get(&#39;cloudDestinationOptions&#39;, {})
                                       .get(&#39;volumeType&#39;))
                    self._virtual_network = (self._cleanroom_target_properties.get(&#39;networkOptions&#39;, {})
                                             .get(&#39;networkCard&#39;, {})
                                             .get(&#39;networkDisplayName&#39;))
                    self._security_group = (self._cleanroom_target_properties.get(&#39;securityOptions&#39;, {})
                                            .get(&#39;securityGroups&#39;, [{}])[0]
                                            .get(&#39;name&#39;, &#39;&#39;))
                    self._create_public_ip = (self._cleanroom_target_properties.get(&#39;cloudDestinationOptions&#39;, {})
                                              .get(&#39;publicIP&#39;))
                    self._restore_as_managed_vm = (self._cleanroom_target_properties.get(&#39;cloudDestinationOptions&#39;, {})
                                                   .get(&#39;restoreAsManagedVM&#39;))
                    expiry_hours = (self._cleanroom_target_properties.get(&#34;liveMountOptions&#34;, {})
                                    .get(&#34;expirationTime&#34;, {})
                                    .get(&#34;minutesRetainUntil&#34;, &#34;&#34;))
                    expiry_days = (self._cleanroom_target_properties.get(&#34;liveMountOptions&#34;, {})
                                   .get(&#34;expirationTime&#34;, {})
                                   .get(&#34;daysRetainUntil&#34;, &#34;&#34;))
                    if expiry_hours:
                        self._expiration_time = f&#39;{expiry_hours} hours&#39;
                    elif expiry_days:
                        self._expiration_time = f&#39;{expiry_days} days&#39;
                    self._test_virtual_network = (self._cleanroom_target_properties.get(&#39;networkOptions&#39;, {})
                                                  .get(&#39;cloudNetwork&#39;, {})
                                                  .get(&#39;label&#39;))
                    self._test_vm_size = (self._cleanroom_target_properties.get(&#39;amazonPolicy&#39;, {})
                                          .get(&#39;vmInstanceTypes&#39;, [{}])[0]
                                          .get(&#39;vmInstanceTypeName&#39;, &#39;&#39;))
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    @property
    def cleanroom_target_id(self):
        &#34;&#34;&#34;Returns: (str) the id of the cleanroom target&#34;&#34;&#34;
        return self._cleanroom_target_id

    @property
    def cleanroom_target_name(self):
        &#34;&#34;&#34;Returns: (str) the display name of the cleanroom target&#34;&#34;&#34;
        return self._cleanroom_target_name

    @property
    def policy_type(self):
        &#34;&#34;&#34;Returns: (str) the policy type ID
            1  - AWS
            2  - Microsoft Hyper-V
            7  - Azure
            13 - VMware
        &#34;&#34;&#34;
        return self._policy_type

    @property
    def application_type(self):
        &#34;&#34;&#34;Returns: (str) the name of the application type
            0 - Replication type
            1 - Regular type
        &#34;&#34;&#34;
        return self._application_type

    @property
    def destination_hypervisor(self):
        &#34;&#34;&#34;Returns: (str) the client name of destination hypervisor&#34;&#34;&#34;
        return self._destination_hypervisor

    @property
    def access_node(self):
        &#34;&#34;&#34;Returns: (str) the client name of the access node/proxy of the cleanroom target&#34;&#34;&#34;
        return self._access_node

    @property
    def access_node_client_group(self):
        &#34;&#34;&#34;Returns: (str) The client group name set on the access node field of cleanroom target&#34;&#34;&#34;
        return self._access_node_client_group

    @property
    def security_user_names(self):
        &#34;&#34;&#34;Returns: list&lt;str&gt; the names of the users who are used for ownership of the hypervisor and VMs&#34;&#34;&#34;
        return [user[&#39;userName&#39;] for user in self._users]

    @property
    def vm_prefix(self):
        &#34;&#34;&#34;Returns: (str) the prefix of the vm name to be prefixed to the destination VM&#34;&#34;&#34;
        return self._vm_prefix

    @property
    def vm_suffix(self):
        &#34;&#34;&#34;Returns: (str) the suffix of the vm name to be suffixed to the destination VM&#34;&#34;&#34;
        return self._vm_suffix

    @property
    def expiration_time(self):
        &#34;&#34;&#34;Returns: (str) VMware/Azure: the expiration time of the test boot VM/test failover VM
            eg: 4 hours or 3 days
        &#34;&#34;&#34;
        return self._expiration_time

    @property
    def storage_account(self):
        &#34;&#34;&#34;Returns: (str) Azure: the storage account name used to deploy the VM&#39;s storage&#34;&#34;&#34;
        return self._storage_account

    @property
    def region(self):
        &#34;&#34;&#34;Return: (str) Azure: the cleanroom target region for destination VM&#34;&#34;&#34;
        return self._region

    @property
    def restore_as_managed_vm(self):
        &#34;&#34;&#34;Returns: (bool) whether the destination VM will be a managed VM&#34;&#34;&#34;
        return self._restore_as_managed_vm

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the cleanroom Target.&#34;&#34;&#34;
        self._get_cleanroom_target_properties()

    def delete(self):
        &#34;&#34;&#34;Deletes the Cleanroom Target. Returns: (bool) whether the target is deleted or not.&#34;&#34;&#34;
        return self._delete_cleanroom_target()</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.cleanroom.target.CleanroomTarget"><code class="flex name class">
<span>class <span class="ident">CleanroomTarget</span></span>
<span>(</span><span>commcell_object, cleanroom_target_name, cleanroom_target_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for performing target operations</p>
<p>Initialize the instance of the CleanroomTarget class.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the Commcell class</p>
<p>cleanroom_target_name
(str)
&ndash;
name of the target</p>
<p>cleanroom_target_id
(str)
&ndash;
id of the target</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/target.py#L223-L467" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class CleanroomTarget:
    &#34;&#34;&#34;Class for performing target operations&#34;&#34;&#34;

    def __init__(self, commcell_object, cleanroom_target_name, cleanroom_target_id=None):
        &#34;&#34;&#34;Initialize the instance of the CleanroomTarget class.

            Args:
                commcell_object   (object)    --  instance of the Commcell class

                cleanroom_target_name      (str)       --  name of the target

                cleanroom_target_id        (str)       --  id of the target

        &#34;&#34;&#34;
        self._commcell_object = commcell_object

        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_
        self._cleanroom_target_name = cleanroom_target_name.lower()

        if cleanroom_target_id:
            # Use the target id mentioned in the arguments
            self._cleanroom_target_id = str(cleanroom_target_id)
        else:
            # Get the target id if target id is not provided
            self._cleanroom_target_id = self._get_cleanroom_target_id()
        self._RECOVERY_TARGET_API = self._services[&#39;GET_RECOVERY_TARGET&#39;] % self._cleanroom_target_id

        self._cleanroom_target_properties = None

        self._policy_type = None
        self._application_type = None
        self._destination_hypervisor = None
        self._access_node = None
        self._access_node_client_group = None
        self._users = []
        self._user_groups = []
        self._vm_prefix = &#39;&#39;
        self._vm_suffix = &#39;&#39;
        self._expiration_time = None

        self._region = None
        self._availability_zone = None
        self._storage_account = None
        self._restore_as_managed_vm = None

        self.refresh()

    def _get_cleanroom_target_id(self):
        &#34;&#34;&#34;Gets the target id associated with this target.

            Returns:
                str - id associated with this target

        &#34;&#34;&#34;
        target = CleanroomTargets(self._commcell_object)
        return target.all_targets[self._cleanroom_target_name]

    def _delete_cleanroom_target(self):
        &#34;&#34;&#34;Deletes the cleanroom target

            Raises:
                SDKException:
                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;DELETE&#39;, self._RECOVERY_TARGET_API)
        if flag:
            return flag
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _set_policy_type(self, policy_type):
        &#34;&#34;&#34;Sets the policy type&#34;&#34;&#34;
        if policy_type == &#34;AMAZON&#34;:
            self._policy_type = 1
        elif policy_type == &#34;MICROSOFT&#34;:
            self._policy_type = 2
        elif policy_type == &#34;AZURE_RESOURCE_MANAGER&#34;:
            self._policy_type = 7
        elif policy_type in [&#34;VMW_BACKUP_LABTEMPLATE&#34;, &#34;VMW_LIVEMOUNT&#34;]:
            self._policy_type = 13
        else:
            self._policy_type = -1

    def _get_cleanroom_target_properties(self):
        &#34;&#34;&#34;Gets the target properties of this target.

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._RECOVERY_TARGET_API)

        if flag:
            if response.json() and &#39;entity&#39; in response.json():
                self._cleanroom_target_properties = response.json()
                self._application_type = self._cleanroom_target_properties[&#39;entity&#39;][&#39;applicationType&#39;]
                self._destination_hypervisor = self._cleanroom_target_properties[&#39;entity&#39;][&#39;destinationHypervisor&#39;][
                    &#39;name&#39;]
                self._vm_suffix = self._cleanroom_target_properties[&#34;vmDisplayName&#34;].get(&#34;suffix&#34;, &#34;&#34;)
                self._vm_prefix = self._cleanroom_target_properties[&#34;vmDisplayName&#34;].get(&#34;prefix&#34;, &#34;&#34;)
                self._access_node = self._cleanroom_target_properties[&#34;accessNode&#34;].get(&#34;type&#34;, &#34;&#34;)
                self._access_node_client_group = (self._cleanroom_target_properties.get(&#39;proxyClientGroupEntity&#39;, {})
                                                  .get(&#39;clientGroupName&#39;))
                self._users = self._cleanroom_target_properties.get(&#39;securityOptions&#39;, {}).get(&#39;users&#39;, [])
                self._user_groups = self._cleanroom_target_properties.get(&#39;securityOptions&#39;, {}).get(&#39;userGroups&#39;, [])
                policy_type = self._cleanroom_target_properties[&#34;entity&#34;].get(&#34;policyType&#34;, &#34;&#34;)
                self._set_policy_type(policy_type)

                if self._policy_type == 7:
                    self._region = (self._cleanroom_target_properties.get(&#39;cloudDestinationOptions&#39;, {})
                                    .get(&#39;region&#39;, {})
                                    .get(&#39;name&#39;))
                    self._availability_zone = (self._cleanroom_target_properties.get(&#39;cloudDestinationOptions&#39;, {})
                                               .get(&#39;availabilityZone&#39;))
                    self._storage_account = (self._cleanroom_target_properties.get(&#34;destinationOptions&#34;, {})
                                             .get(&#34;dataStore&#34;, &#34;&#34;))

                    self._vm_size = (self._cleanroom_target_properties.get(&#39;amazonPolicy&#39;, {})
                                     .get(&#39;vmInstanceTypes&#39;, [{}])[0]
                                     .get(&#39;vmInstanceTypeName&#39;, &#39;&#39;))
                    self._disk_type = (self._cleanroom_target_properties.get(&#39;cloudDestinationOptions&#39;, {})
                                       .get(&#39;volumeType&#39;))
                    self._virtual_network = (self._cleanroom_target_properties.get(&#39;networkOptions&#39;, {})
                                             .get(&#39;networkCard&#39;, {})
                                             .get(&#39;networkDisplayName&#39;))
                    self._security_group = (self._cleanroom_target_properties.get(&#39;securityOptions&#39;, {})
                                            .get(&#39;securityGroups&#39;, [{}])[0]
                                            .get(&#39;name&#39;, &#39;&#39;))
                    self._create_public_ip = (self._cleanroom_target_properties.get(&#39;cloudDestinationOptions&#39;, {})
                                              .get(&#39;publicIP&#39;))
                    self._restore_as_managed_vm = (self._cleanroom_target_properties.get(&#39;cloudDestinationOptions&#39;, {})
                                                   .get(&#39;restoreAsManagedVM&#39;))
                    expiry_hours = (self._cleanroom_target_properties.get(&#34;liveMountOptions&#34;, {})
                                    .get(&#34;expirationTime&#34;, {})
                                    .get(&#34;minutesRetainUntil&#34;, &#34;&#34;))
                    expiry_days = (self._cleanroom_target_properties.get(&#34;liveMountOptions&#34;, {})
                                   .get(&#34;expirationTime&#34;, {})
                                   .get(&#34;daysRetainUntil&#34;, &#34;&#34;))
                    if expiry_hours:
                        self._expiration_time = f&#39;{expiry_hours} hours&#39;
                    elif expiry_days:
                        self._expiration_time = f&#39;{expiry_days} days&#39;
                    self._test_virtual_network = (self._cleanroom_target_properties.get(&#39;networkOptions&#39;, {})
                                                  .get(&#39;cloudNetwork&#39;, {})
                                                  .get(&#39;label&#39;))
                    self._test_vm_size = (self._cleanroom_target_properties.get(&#39;amazonPolicy&#39;, {})
                                          .get(&#39;vmInstanceTypes&#39;, [{}])[0]
                                          .get(&#39;vmInstanceTypeName&#39;, &#39;&#39;))
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    @property
    def cleanroom_target_id(self):
        &#34;&#34;&#34;Returns: (str) the id of the cleanroom target&#34;&#34;&#34;
        return self._cleanroom_target_id

    @property
    def cleanroom_target_name(self):
        &#34;&#34;&#34;Returns: (str) the display name of the cleanroom target&#34;&#34;&#34;
        return self._cleanroom_target_name

    @property
    def policy_type(self):
        &#34;&#34;&#34;Returns: (str) the policy type ID
            1  - AWS
            2  - Microsoft Hyper-V
            7  - Azure
            13 - VMware
        &#34;&#34;&#34;
        return self._policy_type

    @property
    def application_type(self):
        &#34;&#34;&#34;Returns: (str) the name of the application type
            0 - Replication type
            1 - Regular type
        &#34;&#34;&#34;
        return self._application_type

    @property
    def destination_hypervisor(self):
        &#34;&#34;&#34;Returns: (str) the client name of destination hypervisor&#34;&#34;&#34;
        return self._destination_hypervisor

    @property
    def access_node(self):
        &#34;&#34;&#34;Returns: (str) the client name of the access node/proxy of the cleanroom target&#34;&#34;&#34;
        return self._access_node

    @property
    def access_node_client_group(self):
        &#34;&#34;&#34;Returns: (str) The client group name set on the access node field of cleanroom target&#34;&#34;&#34;
        return self._access_node_client_group

    @property
    def security_user_names(self):
        &#34;&#34;&#34;Returns: list&lt;str&gt; the names of the users who are used for ownership of the hypervisor and VMs&#34;&#34;&#34;
        return [user[&#39;userName&#39;] for user in self._users]

    @property
    def vm_prefix(self):
        &#34;&#34;&#34;Returns: (str) the prefix of the vm name to be prefixed to the destination VM&#34;&#34;&#34;
        return self._vm_prefix

    @property
    def vm_suffix(self):
        &#34;&#34;&#34;Returns: (str) the suffix of the vm name to be suffixed to the destination VM&#34;&#34;&#34;
        return self._vm_suffix

    @property
    def expiration_time(self):
        &#34;&#34;&#34;Returns: (str) VMware/Azure: the expiration time of the test boot VM/test failover VM
            eg: 4 hours or 3 days
        &#34;&#34;&#34;
        return self._expiration_time

    @property
    def storage_account(self):
        &#34;&#34;&#34;Returns: (str) Azure: the storage account name used to deploy the VM&#39;s storage&#34;&#34;&#34;
        return self._storage_account

    @property
    def region(self):
        &#34;&#34;&#34;Return: (str) Azure: the cleanroom target region for destination VM&#34;&#34;&#34;
        return self._region

    @property
    def restore_as_managed_vm(self):
        &#34;&#34;&#34;Returns: (bool) whether the destination VM will be a managed VM&#34;&#34;&#34;
        return self._restore_as_managed_vm

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the cleanroom Target.&#34;&#34;&#34;
        self._get_cleanroom_target_properties()

    def delete(self):
        &#34;&#34;&#34;Deletes the Cleanroom Target. Returns: (bool) whether the target is deleted or not.&#34;&#34;&#34;
        return self._delete_cleanroom_target()</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.cleanroom.target.CleanroomTarget.access_node"><code class="name">var <span class="ident">access_node</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) the client name of the access node/proxy of the cleanroom target</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/target.py#L414-L417" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def access_node(self):
    &#34;&#34;&#34;Returns: (str) the client name of the access node/proxy of the cleanroom target&#34;&#34;&#34;
    return self._access_node</code></pre>
</details>
</dd>
<dt id="cvpysdk.cleanroom.target.CleanroomTarget.access_node_client_group"><code class="name">var <span class="ident">access_node_client_group</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) The client group name set on the access node field of cleanroom target</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/target.py#L419-L422" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def access_node_client_group(self):
    &#34;&#34;&#34;Returns: (str) The client group name set on the access node field of cleanroom target&#34;&#34;&#34;
    return self._access_node_client_group</code></pre>
</details>
</dd>
<dt id="cvpysdk.cleanroom.target.CleanroomTarget.application_type"><code class="name">var <span class="ident">application_type</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) the name of the application type
0 - Replication type
1 - Regular type</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/target.py#L401-L407" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def application_type(self):
    &#34;&#34;&#34;Returns: (str) the name of the application type
        0 - Replication type
        1 - Regular type
    &#34;&#34;&#34;
    return self._application_type</code></pre>
</details>
</dd>
<dt id="cvpysdk.cleanroom.target.CleanroomTarget.cleanroom_target_id"><code class="name">var <span class="ident">cleanroom_target_id</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) the id of the cleanroom target</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/target.py#L381-L384" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def cleanroom_target_id(self):
    &#34;&#34;&#34;Returns: (str) the id of the cleanroom target&#34;&#34;&#34;
    return self._cleanroom_target_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.cleanroom.target.CleanroomTarget.cleanroom_target_name"><code class="name">var <span class="ident">cleanroom_target_name</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) the display name of the cleanroom target</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/target.py#L386-L389" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def cleanroom_target_name(self):
    &#34;&#34;&#34;Returns: (str) the display name of the cleanroom target&#34;&#34;&#34;
    return self._cleanroom_target_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.cleanroom.target.CleanroomTarget.destination_hypervisor"><code class="name">var <span class="ident">destination_hypervisor</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) the client name of destination hypervisor</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/target.py#L409-L412" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def destination_hypervisor(self):
    &#34;&#34;&#34;Returns: (str) the client name of destination hypervisor&#34;&#34;&#34;
    return self._destination_hypervisor</code></pre>
</details>
</dd>
<dt id="cvpysdk.cleanroom.target.CleanroomTarget.expiration_time"><code class="name">var <span class="ident">expiration_time</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) VMware/Azure: the expiration time of the test boot VM/test failover VM
eg: 4 hours or 3 days</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/target.py#L439-L444" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def expiration_time(self):
    &#34;&#34;&#34;Returns: (str) VMware/Azure: the expiration time of the test boot VM/test failover VM
        eg: 4 hours or 3 days
    &#34;&#34;&#34;
    return self._expiration_time</code></pre>
</details>
</dd>
<dt id="cvpysdk.cleanroom.target.CleanroomTarget.policy_type"><code class="name">var <span class="ident">policy_type</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) the policy type ID
1
- AWS
2
- Microsoft Hyper-V
7
- Azure
13 - VMware</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/target.py#L391-L399" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def policy_type(self):
    &#34;&#34;&#34;Returns: (str) the policy type ID
        1  - AWS
        2  - Microsoft Hyper-V
        7  - Azure
        13 - VMware
    &#34;&#34;&#34;
    return self._policy_type</code></pre>
</details>
</dd>
<dt id="cvpysdk.cleanroom.target.CleanroomTarget.region"><code class="name">var <span class="ident">region</span></code></dt>
<dd>
<div class="desc"><p>Return: (str) Azure: the cleanroom target region for destination VM</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/target.py#L451-L454" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def region(self):
    &#34;&#34;&#34;Return: (str) Azure: the cleanroom target region for destination VM&#34;&#34;&#34;
    return self._region</code></pre>
</details>
</dd>
<dt id="cvpysdk.cleanroom.target.CleanroomTarget.restore_as_managed_vm"><code class="name">var <span class="ident">restore_as_managed_vm</span></code></dt>
<dd>
<div class="desc"><p>Returns: (bool) whether the destination VM will be a managed VM</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/target.py#L456-L459" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def restore_as_managed_vm(self):
    &#34;&#34;&#34;Returns: (bool) whether the destination VM will be a managed VM&#34;&#34;&#34;
    return self._restore_as_managed_vm</code></pre>
</details>
</dd>
<dt id="cvpysdk.cleanroom.target.CleanroomTarget.security_user_names"><code class="name">var <span class="ident">security_user_names</span></code></dt>
<dd>
<div class="desc"><p>Returns: list<str> the names of the users who are used for ownership of the hypervisor and VMs</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/target.py#L424-L427" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def security_user_names(self):
    &#34;&#34;&#34;Returns: list&lt;str&gt; the names of the users who are used for ownership of the hypervisor and VMs&#34;&#34;&#34;
    return [user[&#39;userName&#39;] for user in self._users]</code></pre>
</details>
</dd>
<dt id="cvpysdk.cleanroom.target.CleanroomTarget.storage_account"><code class="name">var <span class="ident">storage_account</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) Azure: the storage account name used to deploy the VM's storage</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/target.py#L446-L449" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def storage_account(self):
    &#34;&#34;&#34;Returns: (str) Azure: the storage account name used to deploy the VM&#39;s storage&#34;&#34;&#34;
    return self._storage_account</code></pre>
</details>
</dd>
<dt id="cvpysdk.cleanroom.target.CleanroomTarget.vm_prefix"><code class="name">var <span class="ident">vm_prefix</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) the prefix of the vm name to be prefixed to the destination VM</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/target.py#L429-L432" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def vm_prefix(self):
    &#34;&#34;&#34;Returns: (str) the prefix of the vm name to be prefixed to the destination VM&#34;&#34;&#34;
    return self._vm_prefix</code></pre>
</details>
</dd>
<dt id="cvpysdk.cleanroom.target.CleanroomTarget.vm_suffix"><code class="name">var <span class="ident">vm_suffix</span></code></dt>
<dd>
<div class="desc"><p>Returns: (str) the suffix of the vm name to be suffixed to the destination VM</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/target.py#L434-L437" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def vm_suffix(self):
    &#34;&#34;&#34;Returns: (str) the suffix of the vm name to be suffixed to the destination VM&#34;&#34;&#34;
    return self._vm_suffix</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.cleanroom.target.CleanroomTarget.delete"><code class="name flex">
<span>def <span class="ident">delete</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes the Cleanroom Target. Returns: (bool) whether the target is deleted or not.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/target.py#L465-L467" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete(self):
    &#34;&#34;&#34;Deletes the Cleanroom Target. Returns: (bool) whether the target is deleted or not.&#34;&#34;&#34;
    return self._delete_cleanroom_target()</code></pre>
</details>
</dd>
<dt id="cvpysdk.cleanroom.target.CleanroomTarget.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the properties of the cleanroom Target.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/target.py#L461-L463" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the properties of the cleanroom Target.&#34;&#34;&#34;
    self._get_cleanroom_target_properties()</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.cleanroom.target.CleanroomTargets"><code class="flex name class">
<span>class <span class="ident">CleanroomTargets</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing all the Cleanroom targets</p>
<p>Initialize object of the CleanroomTargets class.</p>
<h2 id="args">Args</h2>
<p>commcell_object (object)
&ndash;
instance of the Commcell class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/target.py#L86-L220" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class CleanroomTargets:

    &#34;&#34;&#34;Class for representing all the Cleanroom targets&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the CleanroomTargets class.

            Args:
                commcell_object (object)  --  instance of the Commcell class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object

        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_
        self._RECOVERY_TARGETS_API = self._services[&#39;GET_ALL_RECOVERY_TARGETS&#39;]

        self._cleanroom_targets = None
        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all targets .

            Returns:
                str     -   string of all the targets

        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\n\n&#39;.format(&#39;S. No.&#39;, &#39;CleanroomTargets&#39;)

        for index, cleanroom_target in enumerate(self._cleanroom_targets):
            sub_str = &#39;{:^5}\t{:20}\n&#39;.format(
                index + 1,
                cleanroom_target
            )
            representation_string += sub_str

        return representation_string.strip()

    def _get_cleanroom_targets(self):
        &#34;&#34;&#34;Gets all the cleanroom targets.

            Returns:
                dict - consists of all targets in the client
                    {
                         &#34;target1_name&#34;: target1_id,
                         &#34;target2_name&#34;: target2_id
                    }

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._RECOVERY_TARGETS_API)
        if flag:
            if response.json() and &#39;recoveryTargets&#39; in response.json():
                cleanroom_target_dict = {}
                for cleanroomTarget in response.json()[&#39;recoveryTargets&#39;]:
                    if cleanroomTarget[&#39;applicationType&#39;] == &#34;CLEAN_ROOM&#34;:
                        temp_name = cleanroomTarget[&#39;name&#39;].lower()
                        cleanroom_target_dict[temp_name] = str(cleanroomTarget[&#39;id&#39;])

                return cleanroom_target_dict
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    @property
    def all_targets(self):
        &#34;&#34;&#34;Returns dict of all the targets.

         Returns dict    -   consists of all targets

                {
                    &#34;target1_name&#34;: target1_id,

                    &#34;target2_name&#34;: target2_id
                }

        &#34;&#34;&#34;
        return self._cleanroom_targets

    def has_cleanroom_target(self, target_name):
        &#34;&#34;&#34;Checks if a target is present in the commcell.

            Args:
                target_name (str)  --  name of the target

            Returns:
                bool - boolean output whether the target is present in commcell or not

            Raises:
                SDKException:
                    if type of the target name argument is not string

        &#34;&#34;&#34;
        if not isinstance(target_name, str):
            raise SDKException(&#39;Target&#39;, &#39;101&#39;)

        return self._cleanroom_targets and target_name.lower() in self._cleanroom_targets

    def get(self, cleanroom_target_name):
        &#34;&#34;&#34;Returns a target object.

            Args:
                cleanroom_target_name (str)  --  name of the target

            Returns:
                object - instance of the target class for the given target name

            Raises:
                SDKException:
                    if type of the target name argument is not string

                    if no target exists with the given name

        &#34;&#34;&#34;
        if not isinstance(cleanroom_target_name, str):
            raise SDKException(&#39;Target&#39;, &#39;101&#39;)
        else:
            cleanroom_target_name = cleanroom_target_name.lower()

            if self.has_cleanroom_target(cleanroom_target_name):
                return CleanroomTarget(
                    self._commcell_object, cleanroom_target_name, self.all_targets[cleanroom_target_name])

            raise SDKException(&#39;RecoveryTarget&#39;, &#39;102&#39;, &#39;No target exists with name: {0}&#39;.format(cleanroom_target_name))

    def refresh(self):
        &#34;&#34;&#34;Refresh the cleanroom targets&#34;&#34;&#34;
        self._cleanroom_targets = self._get_cleanroom_targets()</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.cleanroom.target.CleanroomTargets.all_targets"><code class="name">var <span class="ident">all_targets</span></code></dt>
<dd>
<div class="desc"><p>Returns dict of all the targets.</p>
<p>Returns dict
-
consists of all targets</p>
<pre><code>   {
       "target1_name": target1_id,

       "target2_name": target2_id
   }
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/target.py#L157-L170" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def all_targets(self):
    &#34;&#34;&#34;Returns dict of all the targets.

     Returns dict    -   consists of all targets

            {
                &#34;target1_name&#34;: target1_id,

                &#34;target2_name&#34;: target2_id
            }

    &#34;&#34;&#34;
    return self._cleanroom_targets</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.cleanroom.target.CleanroomTargets.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, cleanroom_target_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a target object.</p>
<h2 id="args">Args</h2>
<p>cleanroom_target_name (str)
&ndash;
name of the target</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the target class for the given target name</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the target name argument is not string</p>
<pre><code>if no target exists with the given name
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/target.py#L191-L216" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, cleanroom_target_name):
    &#34;&#34;&#34;Returns a target object.

        Args:
            cleanroom_target_name (str)  --  name of the target

        Returns:
            object - instance of the target class for the given target name

        Raises:
            SDKException:
                if type of the target name argument is not string

                if no target exists with the given name

    &#34;&#34;&#34;
    if not isinstance(cleanroom_target_name, str):
        raise SDKException(&#39;Target&#39;, &#39;101&#39;)
    else:
        cleanroom_target_name = cleanroom_target_name.lower()

        if self.has_cleanroom_target(cleanroom_target_name):
            return CleanroomTarget(
                self._commcell_object, cleanroom_target_name, self.all_targets[cleanroom_target_name])

        raise SDKException(&#39;RecoveryTarget&#39;, &#39;102&#39;, &#39;No target exists with name: {0}&#39;.format(cleanroom_target_name))</code></pre>
</details>
</dd>
<dt id="cvpysdk.cleanroom.target.CleanroomTargets.has_cleanroom_target"><code class="name flex">
<span>def <span class="ident">has_cleanroom_target</span></span>(<span>self, target_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks if a target is present in the commcell.</p>
<h2 id="args">Args</h2>
<p>target_name (str)
&ndash;
name of the target</p>
<h2 id="returns">Returns</h2>
<p>bool - boolean output whether the target is present in commcell or not</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the target name argument is not string</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/target.py#L172-L189" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_cleanroom_target(self, target_name):
    &#34;&#34;&#34;Checks if a target is present in the commcell.

        Args:
            target_name (str)  --  name of the target

        Returns:
            bool - boolean output whether the target is present in commcell or not

        Raises:
            SDKException:
                if type of the target name argument is not string

    &#34;&#34;&#34;
    if not isinstance(target_name, str):
        raise SDKException(&#39;Target&#39;, &#39;101&#39;)

    return self._cleanroom_targets and target_name.lower() in self._cleanroom_targets</code></pre>
</details>
</dd>
<dt id="cvpysdk.cleanroom.target.CleanroomTargets.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the cleanroom targets</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/cleanroom/target.py#L218-L220" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the cleanroom targets&#34;&#34;&#34;
    self._cleanroom_targets = self._get_cleanroom_targets()</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#cleanroomtargets-attributes">cleanroomTargets Attributes</a></li>
<li><a href="#cleanroomtarget-attributes">CleanroomTarget Attributes</a></li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.cleanroom" href="index.html">cvpysdk.cleanroom</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.cleanroom.target.CleanroomTarget" href="#cvpysdk.cleanroom.target.CleanroomTarget">CleanroomTarget</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.cleanroom.target.CleanroomTarget.access_node" href="#cvpysdk.cleanroom.target.CleanroomTarget.access_node">access_node</a></code></li>
<li><code><a title="cvpysdk.cleanroom.target.CleanroomTarget.access_node_client_group" href="#cvpysdk.cleanroom.target.CleanroomTarget.access_node_client_group">access_node_client_group</a></code></li>
<li><code><a title="cvpysdk.cleanroom.target.CleanroomTarget.application_type" href="#cvpysdk.cleanroom.target.CleanroomTarget.application_type">application_type</a></code></li>
<li><code><a title="cvpysdk.cleanroom.target.CleanroomTarget.cleanroom_target_id" href="#cvpysdk.cleanroom.target.CleanroomTarget.cleanroom_target_id">cleanroom_target_id</a></code></li>
<li><code><a title="cvpysdk.cleanroom.target.CleanroomTarget.cleanroom_target_name" href="#cvpysdk.cleanroom.target.CleanroomTarget.cleanroom_target_name">cleanroom_target_name</a></code></li>
<li><code><a title="cvpysdk.cleanroom.target.CleanroomTarget.delete" href="#cvpysdk.cleanroom.target.CleanroomTarget.delete">delete</a></code></li>
<li><code><a title="cvpysdk.cleanroom.target.CleanroomTarget.destination_hypervisor" href="#cvpysdk.cleanroom.target.CleanroomTarget.destination_hypervisor">destination_hypervisor</a></code></li>
<li><code><a title="cvpysdk.cleanroom.target.CleanroomTarget.expiration_time" href="#cvpysdk.cleanroom.target.CleanroomTarget.expiration_time">expiration_time</a></code></li>
<li><code><a title="cvpysdk.cleanroom.target.CleanroomTarget.policy_type" href="#cvpysdk.cleanroom.target.CleanroomTarget.policy_type">policy_type</a></code></li>
<li><code><a title="cvpysdk.cleanroom.target.CleanroomTarget.refresh" href="#cvpysdk.cleanroom.target.CleanroomTarget.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.cleanroom.target.CleanroomTarget.region" href="#cvpysdk.cleanroom.target.CleanroomTarget.region">region</a></code></li>
<li><code><a title="cvpysdk.cleanroom.target.CleanroomTarget.restore_as_managed_vm" href="#cvpysdk.cleanroom.target.CleanroomTarget.restore_as_managed_vm">restore_as_managed_vm</a></code></li>
<li><code><a title="cvpysdk.cleanroom.target.CleanroomTarget.security_user_names" href="#cvpysdk.cleanroom.target.CleanroomTarget.security_user_names">security_user_names</a></code></li>
<li><code><a title="cvpysdk.cleanroom.target.CleanroomTarget.storage_account" href="#cvpysdk.cleanroom.target.CleanroomTarget.storage_account">storage_account</a></code></li>
<li><code><a title="cvpysdk.cleanroom.target.CleanroomTarget.vm_prefix" href="#cvpysdk.cleanroom.target.CleanroomTarget.vm_prefix">vm_prefix</a></code></li>
<li><code><a title="cvpysdk.cleanroom.target.CleanroomTarget.vm_suffix" href="#cvpysdk.cleanroom.target.CleanroomTarget.vm_suffix">vm_suffix</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.cleanroom.target.CleanroomTargets" href="#cvpysdk.cleanroom.target.CleanroomTargets">CleanroomTargets</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.cleanroom.target.CleanroomTargets.all_targets" href="#cvpysdk.cleanroom.target.CleanroomTargets.all_targets">all_targets</a></code></li>
<li><code><a title="cvpysdk.cleanroom.target.CleanroomTargets.get" href="#cvpysdk.cleanroom.target.CleanroomTargets.get">get</a></code></li>
<li><code><a title="cvpysdk.cleanroom.target.CleanroomTargets.has_cleanroom_target" href="#cvpysdk.cleanroom.target.CleanroomTargets.has_cleanroom_target">has_cleanroom_target</a></code></li>
<li><code><a title="cvpysdk.cleanroom.target.CleanroomTargets.refresh" href="#cvpysdk.cleanroom.target.CleanroomTargets.refresh">refresh</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>