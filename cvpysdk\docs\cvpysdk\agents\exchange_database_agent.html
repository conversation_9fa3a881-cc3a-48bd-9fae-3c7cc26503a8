<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.agents.exchange_database_agent API documentation</title>
<meta name="description" content="Module for doing operations on an Exchange Database Agent …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.agents.exchange_database_agent</code></h1>
</header>
<section id="section-intro">
<p>Module for doing operations on an Exchange Database Agent.</p>
<p>This module has operations that are applicable at the Agent level for Exchange Database.</p>
<h2 id="exchangedatabaseagent">Exchangedatabaseagent</h2>
<p><strong>init</strong>()
&ndash;
initialize object of Agent with the specified agent name
and id, and associated to the specified client</p>
<p>backup()
&ndash;
runs backup for all subclients present in the Agent</p>
<p>browse()
&ndash;
browse the backed up content of the agent</p>
<p>find()
&ndash;
searches the backed up content in the agent for the given file / folder</p>
<p>refresh()
&ndash;
refresh the properties of the object</p>
<h2 id="attributes">Attributes</h2>
<pre><code>**subclients**  --  returns the instance of the Subclients class, listing the subclients
associated to the Agent
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/agents/exchange_database_agent.py#L1-L219" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Module for doing operations on an Exchange Database Agent.

This module has operations that are applicable at the Agent level for Exchange Database.

ExchangeDatabaseAgent:
    __init__()      --  initialize object of Agent with the specified agent name
    and id, and associated to the specified client

    backup()        --  runs backup for all subclients present in the Agent

    browse()        --  browse the backed up content of the agent

    find()          --  searches the backed up content in the agent for the given file / folder

    refresh()       --  refresh the properties of the object


Attributes
----------

    **subclients**  --  returns the instance of the Subclients class, listing the subclients
    associated to the Agent


&#34;&#34;&#34;

from __future__ import unicode_literals

from ..agent import Agent
from ..subclient import Subclients


class ExchangeDatabaseAgent(Agent):
    &#34;&#34;&#34;Derived class from the Agent Base class,
        to perform operations specific to an Exchange Database Agent.&#34;&#34;&#34;

    def __init__(self, client_object, agent_name, agent_id=None):
        &#34;&#34;&#34;Initialize the instance of the Agent class.

            Args:
                client_object   (object)    --  instance of the Client class

                agent_name      (str)       --  name of the agent

                    (File System, Virtual Server, etc.)

                agent_id        (str)       --  id of the agent

                    default: None

            Returns:
                object  -   instance of the Agent class

        &#34;&#34;&#34;
        super(ExchangeDatabaseAgent, self).__init__(client_object, agent_name, agent_id)

        if self.instances.has_instance(&#39;defaultInstance&#39;):
            self._instance_object = self.instances.get(&#39;defaultInstance&#39;)
        else:
            self._instance_object = self.instances.get(
                sorted(self.instances.all_instances)[0]
            )

        if self._instance_object.backupsets.has_backupset(&#39;defaultBackupSet&#39;):
            self._backupset_object = self._instance_object.backupsets.get(&#39;defaultBackupSet&#39;)
        else:
            self._backupset_object = self._instance_object.backupsets.get(
                sorted(self._instance_object.backupsets.all_backupsets)[0]
            )

        self._subclients = None

    @property
    def subclients(self):
        &#34;&#34;&#34;Returns the instance of the Subclients class representing the list of Subclients
        installed / configured on the Client for the selected Agent.
        &#34;&#34;&#34;
        if self._subclients is None:
            self._subclients = Subclients(self)

        return self._subclients

    def backup(self):
        &#34;&#34;&#34;Runs Incremental backup job for all subclients belonging to the Exchange Database Agent.

            Runs Full Backup job for a subclient, if no job had been ran earlier for it.

            Returns:
                list    -   list consisting of the job objects for the backup jobs started for
                the subclients in the agent

        &#34;&#34;&#34;
        return self._backupset_object.backup()

    def browse(self, *args, **kwargs):
        &#34;&#34;&#34;Browses the content of the Exchange Database Agent.

            Args:
                Dictionary of browse options:
                    Example:

                        browse({
                            &#39;path&#39;: &#39;c:\\\\hello&#39;,

                            &#39;show_deleted&#39;: True,

                            &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,

                            &#39;to_time&#39;: &#39;2016-04-21 12:00:00&#39;
                        })

            Kwargs:
                Keyword argument of browse options:
                    Example:

                        browse(
                            path=&#39;c:\\hello&#39;,

                            show_deleted=True,

                            from_time=&#39;2014-04-20 12:00:00&#39;,

                            to_time=&#39;2016-04-21 12:00:00&#39;
                        )

            Returns:
                (list, dict)
                    list    -   List of only the file, folder paths from the browse response

                    dict    -   Dictionary of all the paths with additional metadata retrieved
                    from browse operation


            Refer `default_browse_options`_ for all the supported options.

            .. _default_browse_options: https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565

        &#34;&#34;&#34;
        return self._instance_object.browse(*args, **kwargs)

    def find(self, *args, **kwargs):
        &#34;&#34;&#34;Searches a file/folder in the backed up content of the agent,
            and returns all the files matching the filters given.

            Args:
                Dictionary of browse options:
                    Example:

                        find({
                            &#39;file_name&#39;: &#39;*.txt&#39;,

                            &#39;show_deleted&#39;: True,

                            &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,

                            &#39;to_time&#39;: &#39;2016-04-31 12:00:00&#39;
                        })

            Kwargs:
                Keyword argument of browse options:
                    Example:

                        find(
                            file_name=&#39;*.txt&#39;,

                            show_deleted=True,

                            &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,

                            to_time=&#39;2016-04-31 12:00:00&#39;
                        )

            Returns:
                (list, dict)
                    list    -   List of only the file, folder paths from the browse response

                    dict    -   Dictionary of all the paths with additional metadata retrieved
                    from browse operation


            Refer `default_browse_options`_ for all the supported options.

            Additional options supported:
                file_name       (str)   --  Find files with name

                file_size_gt    (int)   --  Find files with size greater than size

                file_size_lt    (int)   --  Find files with size lesser than size

                file_size_et    (int)   --  Find files with size equal to size

            .. _default_browse_options: https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565

        &#34;&#34;&#34;
        return self._instance_object.find(*args, **kwargs)

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the Agent.&#34;&#34;&#34;
        super(ExchangeDatabaseAgent, self).refresh()

        self._subclients = None</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.agents.exchange_database_agent.ExchangeDatabaseAgent"><code class="flex name class">
<span>class <span class="ident">ExchangeDatabaseAgent</span></span>
<span>(</span><span>client_object, agent_name, agent_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Derived class from the Agent Base class,
to perform operations specific to an Exchange Database Agent.</p>
<p>Initialize the instance of the Agent class.</p>
<h2 id="args">Args</h2>
<p>client_object
(object)
&ndash;
instance of the Client class</p>
<p>agent_name
(str)
&ndash;
name of the agent</p>
<pre><code>(File System, Virtual Server, etc.)
</code></pre>
<p>agent_id
(str)
&ndash;
id of the agent</p>
<pre><code>default: None
</code></pre>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the Agent class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/agents/exchange_database_agent.py#L51-L219" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class ExchangeDatabaseAgent(Agent):
    &#34;&#34;&#34;Derived class from the Agent Base class,
        to perform operations specific to an Exchange Database Agent.&#34;&#34;&#34;

    def __init__(self, client_object, agent_name, agent_id=None):
        &#34;&#34;&#34;Initialize the instance of the Agent class.

            Args:
                client_object   (object)    --  instance of the Client class

                agent_name      (str)       --  name of the agent

                    (File System, Virtual Server, etc.)

                agent_id        (str)       --  id of the agent

                    default: None

            Returns:
                object  -   instance of the Agent class

        &#34;&#34;&#34;
        super(ExchangeDatabaseAgent, self).__init__(client_object, agent_name, agent_id)

        if self.instances.has_instance(&#39;defaultInstance&#39;):
            self._instance_object = self.instances.get(&#39;defaultInstance&#39;)
        else:
            self._instance_object = self.instances.get(
                sorted(self.instances.all_instances)[0]
            )

        if self._instance_object.backupsets.has_backupset(&#39;defaultBackupSet&#39;):
            self._backupset_object = self._instance_object.backupsets.get(&#39;defaultBackupSet&#39;)
        else:
            self._backupset_object = self._instance_object.backupsets.get(
                sorted(self._instance_object.backupsets.all_backupsets)[0]
            )

        self._subclients = None

    @property
    def subclients(self):
        &#34;&#34;&#34;Returns the instance of the Subclients class representing the list of Subclients
        installed / configured on the Client for the selected Agent.
        &#34;&#34;&#34;
        if self._subclients is None:
            self._subclients = Subclients(self)

        return self._subclients

    def backup(self):
        &#34;&#34;&#34;Runs Incremental backup job for all subclients belonging to the Exchange Database Agent.

            Runs Full Backup job for a subclient, if no job had been ran earlier for it.

            Returns:
                list    -   list consisting of the job objects for the backup jobs started for
                the subclients in the agent

        &#34;&#34;&#34;
        return self._backupset_object.backup()

    def browse(self, *args, **kwargs):
        &#34;&#34;&#34;Browses the content of the Exchange Database Agent.

            Args:
                Dictionary of browse options:
                    Example:

                        browse({
                            &#39;path&#39;: &#39;c:\\\\hello&#39;,

                            &#39;show_deleted&#39;: True,

                            &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,

                            &#39;to_time&#39;: &#39;2016-04-21 12:00:00&#39;
                        })

            Kwargs:
                Keyword argument of browse options:
                    Example:

                        browse(
                            path=&#39;c:\\hello&#39;,

                            show_deleted=True,

                            from_time=&#39;2014-04-20 12:00:00&#39;,

                            to_time=&#39;2016-04-21 12:00:00&#39;
                        )

            Returns:
                (list, dict)
                    list    -   List of only the file, folder paths from the browse response

                    dict    -   Dictionary of all the paths with additional metadata retrieved
                    from browse operation


            Refer `default_browse_options`_ for all the supported options.

            .. _default_browse_options: https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565

        &#34;&#34;&#34;
        return self._instance_object.browse(*args, **kwargs)

    def find(self, *args, **kwargs):
        &#34;&#34;&#34;Searches a file/folder in the backed up content of the agent,
            and returns all the files matching the filters given.

            Args:
                Dictionary of browse options:
                    Example:

                        find({
                            &#39;file_name&#39;: &#39;*.txt&#39;,

                            &#39;show_deleted&#39;: True,

                            &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,

                            &#39;to_time&#39;: &#39;2016-04-31 12:00:00&#39;
                        })

            Kwargs:
                Keyword argument of browse options:
                    Example:

                        find(
                            file_name=&#39;*.txt&#39;,

                            show_deleted=True,

                            &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,

                            to_time=&#39;2016-04-31 12:00:00&#39;
                        )

            Returns:
                (list, dict)
                    list    -   List of only the file, folder paths from the browse response

                    dict    -   Dictionary of all the paths with additional metadata retrieved
                    from browse operation


            Refer `default_browse_options`_ for all the supported options.

            Additional options supported:
                file_name       (str)   --  Find files with name

                file_size_gt    (int)   --  Find files with size greater than size

                file_size_lt    (int)   --  Find files with size lesser than size

                file_size_et    (int)   --  Find files with size equal to size

            .. _default_browse_options: https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565

        &#34;&#34;&#34;
        return self._instance_object.find(*args, **kwargs)

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the Agent.&#34;&#34;&#34;
        super(ExchangeDatabaseAgent, self).refresh()

        self._subclients = None</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.agent.Agent" href="../agent.html#cvpysdk.agent.Agent">Agent</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.agents.exchange_database_agent.ExchangeDatabaseAgent.subclients"><code class="name">var <span class="ident">subclients</span></code></dt>
<dd>
<div class="desc"><p>Returns the instance of the Subclients class representing the list of Subclients
installed / configured on the Client for the selected Agent.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/agents/exchange_database_agent.py#L91-L99" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def subclients(self):
    &#34;&#34;&#34;Returns the instance of the Subclients class representing the list of Subclients
    installed / configured on the Client for the selected Agent.
    &#34;&#34;&#34;
    if self._subclients is None:
        self._subclients = Subclients(self)

    return self._subclients</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.agents.exchange_database_agent.ExchangeDatabaseAgent.backup"><code class="name flex">
<span>def <span class="ident">backup</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Runs Incremental backup job for all subclients belonging to the Exchange Database Agent.</p>
<p>Runs Full Backup job for a subclient, if no job had been ran earlier for it.</p>
<h2 id="returns">Returns</h2>
<p>list
-
list consisting of the job objects for the backup jobs started for
the subclients in the agent</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/agents/exchange_database_agent.py#L101-L111" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def backup(self):
    &#34;&#34;&#34;Runs Incremental backup job for all subclients belonging to the Exchange Database Agent.

        Runs Full Backup job for a subclient, if no job had been ran earlier for it.

        Returns:
            list    -   list consisting of the job objects for the backup jobs started for
            the subclients in the agent

    &#34;&#34;&#34;
    return self._backupset_object.backup()</code></pre>
</details>
</dd>
<dt id="cvpysdk.agents.exchange_database_agent.ExchangeDatabaseAgent.browse"><code class="name flex">
<span>def <span class="ident">browse</span></span>(<span>self, *args, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Browses the content of the Exchange Database Agent.</p>
<h2 id="args">Args</h2>
<p>Dictionary of browse options:
Example:</p>
<pre><code>    browse({
        'path': 'c:\\hello',

        'show_deleted': True,

        'from_time': '2014-04-20 12:00:00',

        'to_time': '2016-04-21 12:00:00'
    })
</code></pre>
<h2 id="kwargs">Kwargs</h2>
<p>Keyword argument of browse options:
Example:</p>
<pre><code>    browse(
        path='c:\hello',

        show_deleted=True,

        from_time='2014-04-20 12:00:00',

        to_time='2016-04-21 12:00:00'
    )
</code></pre>
<h2 id="returns">Returns</h2>
<p>(list, dict)
list
-
List of only the file, folder paths from the browse response</p>
<pre><code>dict    -   Dictionary of all the paths with additional metadata retrieved
from browse operation
</code></pre>
<p>Refer <code>default_browse_options</code>_ for all the supported options.</p>
<p>.. _default_browse_options: <a href="https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565">https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565</a></p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/agents/exchange_database_agent.py#L113-L157" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def browse(self, *args, **kwargs):
    &#34;&#34;&#34;Browses the content of the Exchange Database Agent.

        Args:
            Dictionary of browse options:
                Example:

                    browse({
                        &#39;path&#39;: &#39;c:\\\\hello&#39;,

                        &#39;show_deleted&#39;: True,

                        &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,

                        &#39;to_time&#39;: &#39;2016-04-21 12:00:00&#39;
                    })

        Kwargs:
            Keyword argument of browse options:
                Example:

                    browse(
                        path=&#39;c:\\hello&#39;,

                        show_deleted=True,

                        from_time=&#39;2014-04-20 12:00:00&#39;,

                        to_time=&#39;2016-04-21 12:00:00&#39;
                    )

        Returns:
            (list, dict)
                list    -   List of only the file, folder paths from the browse response

                dict    -   Dictionary of all the paths with additional metadata retrieved
                from browse operation


        Refer `default_browse_options`_ for all the supported options.

        .. _default_browse_options: https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565

    &#34;&#34;&#34;
    return self._instance_object.browse(*args, **kwargs)</code></pre>
</details>
</dd>
<dt id="cvpysdk.agents.exchange_database_agent.ExchangeDatabaseAgent.find"><code class="name flex">
<span>def <span class="ident">find</span></span>(<span>self, *args, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Searches a file/folder in the backed up content of the agent,
and returns all the files matching the filters given.</p>
<h2 id="args">Args</h2>
<p>Dictionary of browse options:
Example:</p>
<pre><code>    find({
        'file_name': '*.txt',

        'show_deleted': True,

        'from_time': '2014-04-20 12:00:00',

        'to_time': '2016-04-31 12:00:00'
    })
</code></pre>
<h2 id="kwargs">Kwargs</h2>
<p>Keyword argument of browse options:
Example:</p>
<pre><code>    find(
        file_name='*.txt',

        show_deleted=True,

        'from_time': '2014-04-20 12:00:00',

        to_time='2016-04-31 12:00:00'
    )
</code></pre>
<h2 id="returns">Returns</h2>
<p>(list, dict)
list
-
List of only the file, folder paths from the browse response</p>
<pre><code>dict    -   Dictionary of all the paths with additional metadata retrieved
from browse operation
</code></pre>
<p>Refer <code>default_browse_options</code>_ for all the supported options.</p>
<p>Additional options supported:
file_name
(str)
&ndash;
Find files with name</p>
<pre><code>file_size_gt    (int)   --  Find files with size greater than size

file_size_lt    (int)   --  Find files with size lesser than size

file_size_et    (int)   --  Find files with size equal to size
</code></pre>
<p>.. _default_browse_options: <a href="https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565">https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565</a></p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/agents/exchange_database_agent.py#L159-L213" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def find(self, *args, **kwargs):
    &#34;&#34;&#34;Searches a file/folder in the backed up content of the agent,
        and returns all the files matching the filters given.

        Args:
            Dictionary of browse options:
                Example:

                    find({
                        &#39;file_name&#39;: &#39;*.txt&#39;,

                        &#39;show_deleted&#39;: True,

                        &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,

                        &#39;to_time&#39;: &#39;2016-04-31 12:00:00&#39;
                    })

        Kwargs:
            Keyword argument of browse options:
                Example:

                    find(
                        file_name=&#39;*.txt&#39;,

                        show_deleted=True,

                        &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,

                        to_time=&#39;2016-04-31 12:00:00&#39;
                    )

        Returns:
            (list, dict)
                list    -   List of only the file, folder paths from the browse response

                dict    -   Dictionary of all the paths with additional metadata retrieved
                from browse operation


        Refer `default_browse_options`_ for all the supported options.

        Additional options supported:
            file_name       (str)   --  Find files with name

            file_size_gt    (int)   --  Find files with size greater than size

            file_size_lt    (int)   --  Find files with size lesser than size

            file_size_et    (int)   --  Find files with size equal to size

        .. _default_browse_options: https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565

    &#34;&#34;&#34;
    return self._instance_object.find(*args, **kwargs)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.agent.Agent" href="../agent.html#cvpysdk.agent.Agent">Agent</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.agent.Agent.agent_id" href="../agent.html#cvpysdk.agent.Agent.agent_id">agent_id</a></code></li>
<li><code><a title="cvpysdk.agent.Agent.agent_name" href="../agent.html#cvpysdk.agent.Agent.agent_name">agent_name</a></code></li>
<li><code><a title="cvpysdk.agent.Agent.backupsets" href="../agent.html#cvpysdk.agent.Agent.backupsets">backupsets</a></code></li>
<li><code><a title="cvpysdk.agent.Agent.description" href="../agent.html#cvpysdk.agent.Agent.description">description</a></code></li>
<li><code><a title="cvpysdk.agent.Agent.disable_backup" href="../agent.html#cvpysdk.agent.Agent.disable_backup">disable_backup</a></code></li>
<li><code><a title="cvpysdk.agent.Agent.disable_restore" href="../agent.html#cvpysdk.agent.Agent.disable_restore">disable_restore</a></code></li>
<li><code><a title="cvpysdk.agent.Agent.enable_backup" href="../agent.html#cvpysdk.agent.Agent.enable_backup">enable_backup</a></code></li>
<li><code><a title="cvpysdk.agent.Agent.enable_backup_at_time" href="../agent.html#cvpysdk.agent.Agent.enable_backup_at_time">enable_backup_at_time</a></code></li>
<li><code><a title="cvpysdk.agent.Agent.enable_ews_support_for_exchange_on_prem" href="../agent.html#cvpysdk.agent.Agent.enable_ews_support_for_exchange_on_prem">enable_ews_support_for_exchange_on_prem</a></code></li>
<li><code><a title="cvpysdk.agent.Agent.enable_restore" href="../agent.html#cvpysdk.agent.Agent.enable_restore">enable_restore</a></code></li>
<li><code><a title="cvpysdk.agent.Agent.enable_restore_at_time" href="../agent.html#cvpysdk.agent.Agent.enable_restore_at_time">enable_restore_at_time</a></code></li>
<li><code><a title="cvpysdk.agent.Agent.instances" href="../agent.html#cvpysdk.agent.Agent.instances">instances</a></code></li>
<li><code><a title="cvpysdk.agent.Agent.is_backup_enabled" href="../agent.html#cvpysdk.agent.Agent.is_backup_enabled">is_backup_enabled</a></code></li>
<li><code><a title="cvpysdk.agent.Agent.is_restore_enabled" href="../agent.html#cvpysdk.agent.Agent.is_restore_enabled">is_restore_enabled</a></code></li>
<li><code><a title="cvpysdk.agent.Agent.name" href="../agent.html#cvpysdk.agent.Agent.name">name</a></code></li>
<li><code><a title="cvpysdk.agent.Agent.properties" href="../agent.html#cvpysdk.agent.Agent.properties">properties</a></code></li>
<li><code><a title="cvpysdk.agent.Agent.refresh" href="../agent.html#cvpysdk.agent.Agent.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.agent.Agent.schedules" href="../agent.html#cvpysdk.agent.Agent.schedules">schedules</a></code></li>
<li><code><a title="cvpysdk.agent.Agent.update_properties" href="../agent.html#cvpysdk.agent.Agent.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#attributes">Attributes</a></li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.agents" href="index.html">cvpysdk.agents</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.agents.exchange_database_agent.ExchangeDatabaseAgent" href="#cvpysdk.agents.exchange_database_agent.ExchangeDatabaseAgent">ExchangeDatabaseAgent</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.agents.exchange_database_agent.ExchangeDatabaseAgent.backup" href="#cvpysdk.agents.exchange_database_agent.ExchangeDatabaseAgent.backup">backup</a></code></li>
<li><code><a title="cvpysdk.agents.exchange_database_agent.ExchangeDatabaseAgent.browse" href="#cvpysdk.agents.exchange_database_agent.ExchangeDatabaseAgent.browse">browse</a></code></li>
<li><code><a title="cvpysdk.agents.exchange_database_agent.ExchangeDatabaseAgent.find" href="#cvpysdk.agents.exchange_database_agent.ExchangeDatabaseAgent.find">find</a></code></li>
<li><code><a title="cvpysdk.agents.exchange_database_agent.ExchangeDatabaseAgent.subclients" href="#cvpysdk.agents.exchange_database_agent.ExchangeDatabaseAgent.subclients">subclients</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>