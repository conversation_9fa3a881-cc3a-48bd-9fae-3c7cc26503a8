<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.instances.sybaseinstance API documentation</title>
<meta name="description" content="File for operating on a Sybase Instance …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.instances.sybaseinstance</code></h1>
</header>
<section id="section-intro">
<p>File for operating on a Sybase Instance.</p>
<p>SybaseInstance is the only class defined in this file.
SybaseInstance: Derived class from Instance Base class, representing an
sybase instance, and to perform operations on that instance</p>
<h2 id="sybaseinstance">Sybaseinstance</h2>
<p><strong>init</strong>()
&ndash; initialise object of Sybase
Instance associated with
the specified agent</p>
<p>_get_sybase_restore_json()
&ndash; Private Method to construct restore JSON for
individual database restore</p>
<p>_get_sybase_full_restore_json() &ndash; Private Method to construct
restore JSON for fullserver restore</p>
<p>_get_single_database_json()
&ndash; Private Method to construct
restore JSON for individual database restore</p>
<p>_get_server_content()
&ndash; Private Method to construct restore JSON for
individual database when we have rename device options</p>
<p>_restore_common_options_json()
&ndash; setter for common options property in restore</p>
<p>_restore_destination_json()
&ndash; setter for destination options property in restore</p>
<p>_restore_sybase_option_json()
&ndash; setter for Sybase restore option property in restore</p>
<p>_primary_node_properties()
&ndash;
Returns Primary Node Properties of sybase hadr instance</p>
<p>sybase_home()
&ndash; returns string of sybase_home Property of Sybase instance</p>
<p>sybase_instance_name()
&ndash; returns sybase instance name without any case change</p>
<p>is_discovery_enabled()
&ndash; returns bool value of autodiscovery option
at given sybase instance level</p>
<p>localadmin_user()
&ndash; returns string of localadmin_user of given sybase instance</p>
<p>sa_user()
&ndash; returns string of sybase sa_user of given sybase instance</p>
<p>version()
&ndash; returns string of given sybase server version</p>
<p>backup_server()
&ndash; returns string of backup_server for given sybase instance</p>
<p>sybase_ocs()
&ndash; returns string of sybase_ocs for given sybase instance</p>
<p>sybase_ase()
&ndash; returns string of sybase_ase for given sybase
instance</p>
<p>sybase_blocksize()
&ndash; returns integer of block
size for given sybase instance</p>
<p>sybase_configfile()
&ndash; returns string of sybase_configfile
for given sybase instance</p>
<p>sybase_sharedmemory_directory() &ndash; returns string of sybase_memory_directory
for given sybase instance</p>
<p>restore_sybase_server()
&ndash; Performs full sybase server restore</p>
<p>restore_database()
&ndash; Performs individual databases restore</p>
<p>restore_to_disk()
&ndash; Perform restore to disk [Application free restore] for sybase</p>
<p>get_node_properties()
&ndash;
Returns properties of all sybase hadr nodes or for given node id</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sybaseinstance.py#L1-L1122" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;
File for operating on a Sybase Instance.

SybaseInstance is the only class defined in this file.
SybaseInstance: Derived class from Instance Base class, representing an
                            sybase instance, and to perform operations on that instance

SybaseInstance:

    __init__()                      -- initialise object of Sybase  Instance associated with
                                       the specified agent

    _get_sybase_restore_json()      -- Private Method to construct restore JSON for
                                       individual database restore

    _get_sybase_full_restore_json() -- Private Method to construct
                                       restore JSON for fullserver restore

    _get_single_database_json()     -- Private Method to construct
                                       restore JSON for individual database restore

    _get_server_content()           -- Private Method to construct restore JSON for
                                       individual database when we have rename device options


    _restore_common_options_json()  -- setter for common options property in restore

    _restore_destination_json()     -- setter for destination options property in restore

    _restore_sybase_option_json()   -- setter for Sybase restore option property in restore

    _primary_node_properties()      --  Returns Primary Node Properties of sybase hadr instance

    sybase_home()                   -- returns string of sybase_home Property of Sybase instance

    sybase_instance_name()          -- returns sybase instance name without any case change

    is_discovery_enabled()          -- returns bool value of autodiscovery option
                                       at given sybase instance level

    localadmin_user()               -- returns string of localadmin_user of given sybase instance

    sa_user()                       -- returns string of sybase sa_user of given sybase instance

    version()                       -- returns string of given sybase server version

    backup_server()                 -- returns string of backup_server for given sybase instance

    sybase_ocs()                    -- returns string of sybase_ocs for given sybase instance

    sybase_ase()                    -- returns string of sybase_ase for given sybase  instance

    sybase_blocksize()              -- returns integer of block
                                       size for given sybase instance

    sybase_configfile()             -- returns string of sybase_configfile
                                       for given sybase instance

    sybase_sharedmemory_directory() -- returns string of sybase_memory_directory
                                       for given sybase instance

    restore_sybase_server()         -- Performs full sybase server restore

    restore_database()              -- Performs individual databases restore

    restore_to_disk()               -- Perform restore to disk [Application free restore] for sybase

    get_node_properties()           --  Returns properties of all sybase hadr nodes or for given node id

&#34;&#34;&#34;

from __future__ import unicode_literals
import datetime
from .dbinstance import DatabaseInstance
from ..exception import SDKException


class SybaseInstance(DatabaseInstance):
    &#34;&#34;&#34;
    Class to represent a standalone Sybase Instance
    &#34;&#34;&#34;

    def __init__(self, agent_object, instance_name, instance_id=None):
        &#34;&#34;&#34;
        Initializes the object of Sybase Instance class

            Args:
                agent_object    (object) --     instance of the Agent class

                instance_name   (str)    --     name of the instance

                instance_id     (str)    --     id of the instance
                                                default None

            Returns :
                (object) - instance of the Sybase Instance class

        &#34;&#34;&#34;
        self._sybase_restore_json = None
        self._commonoption_restore_json = None
        self._destination_restore_json = None
        super(SybaseInstance, self).__init__(
            agent_object, instance_name, instance_id)
        self._is_hadr = len(self._properties.get(&#39;sybaseClusterInstance&#39;, {})) &gt; 0
        self._instanceprop = {}  # instance variable to hold instance properties
        self._nodes = self.get_node_properties()
        self._primarynodeprop = self._primary_node_properties()

    @property
    def sybase_home(self):
        &#34;&#34;&#34;
        Returns sybase home

            Returns:
                (str) - string representing sybase home

        &#34;&#34;&#34;
        if self.is_hadr:
            return self._primarynodeprop.get(&#39;sybaseProps&#39;, {}).get(&#39;sybaseHome&#39;)
        return self._properties.get(&#39;sybaseInstance&#39;, {}).get(&#39;sybaseHome&#39;)

    @property
    def sybase_instance_name(self):
        &#34;&#34;&#34;
        Returns sybase instance name with actual case without any conversion

            Returns:
                (str) - string representing sybase instance name

        &#34;&#34;&#34;
        if self.is_hadr:
            return self._primarynodeprop.get(&#39;sybaseProps&#39;, {}).get(&#39;backupServer&#39;)[:-3]
        return self._properties.get(&#39;instance&#39;, {}).get(&#39;instanceName&#39;)

    @property
    def is_discovery_enabled(self):
        &#34;&#34;&#34;
        Returns autodiscovery enable status

            Returns:
                bool - boolean value beased on autodiscovery enable status.

                    True  - returns True if autodiscovery is enabled

                    False - returns False if autodiscosvery is not enabled

        &#34;&#34;&#34;
        if self.is_hadr:
            return self._primarynodeprop.get(&#39;sybaseProps&#39;, {}).get(&#39;enableAutoDiscovery&#39;)
        return self._properties.get(&#39;sybaseInstance&#39;, {}).get(&#39;enableAutoDiscovery&#39;)

    @property
    def localadmin_user(self):
        &#34;&#34;&#34;
        Returns for local admin user

            Returns:
                (str) - string representing local admin user

        &#34;&#34;&#34;
        if self.is_hadr:
            return self._primarynodeprop.get(&#39;sybaseProps&#39;, {}).get(&#39;localAdministrator&#39;, {}).get(&#39;userName&#39;)
        return self._properties.get(&#39;sybaseInstance&#39;, {}).get(&#39;localAdministrator&#39;, {}).get(&#39;userName&#39;)

    @property
    def sa_user(self):
        &#34;&#34;&#34;
        Returns for sa username

            Returns:
                (str) - string representing sa username

        &#34;&#34;&#34;
        if self.is_hadr:
            return self._primarynodeprop.get(&#39;sybaseProps&#39;, {}).get(&#39;saUser&#39;, {}).get(&#39;userName&#39;)
        return self._properties.get(&#39;sybaseInstance&#39;, {}).get(&#39;saUser&#39;, {}).get(&#39;userName&#39;)

    @property
    def version(self):
        &#34;&#34;&#34;
        Returns for sybase version

            Returns:
                (str) - string representing sybase version

        &#34;&#34;&#34;
        return self._properties.get(&#39;version&#39;)

    @property
    def backup_server(self):
        &#34;&#34;&#34;
        Returns for backup server

            Returns:
                (str) - string representing backup server

        &#34;&#34;&#34;
        if self.is_hadr:
            return self._primarynodeprop.get(&#39;sybaseProps&#39;, {}).get(&#39;backupServer&#39;)
        return self._properties.get(&#39;sybaseInstance&#39;, {}).get(&#39;backupServer&#39;)

    @property
    def sybase_ocs(self):
        &#34;&#34;&#34;
        Returns for sybase ocs

            Returns:
                (str) - string representing sybase OCS

        &#34;&#34;&#34;
        if self.is_hadr:
            return self._primarynodeprop.get(&#39;sybaseProps&#39;, {}).get(&#39;sybaseOCS&#39;)
        return self._properties.get(&#39;sybaseInstance&#39;, {}).get(&#39;sybaseOCS&#39;)

    @property
    def sybase_ase(self):
        &#34;&#34;&#34;
        Returns for sybase ase

            Returns:
                (str) - string representing sybase ASE

        &#34;&#34;&#34;
        if self.is_hadr:
            return self._primarynodeprop.get(&#39;sybaseProps&#39;, {}).get(&#39;sybaseASE&#39;)
        return self._properties.get(&#39;sybaseInstance&#39;, {}).get(&#39;sybaseASE&#39;)

    @property
    def sybase_blocksize(self):
        &#34;&#34;&#34;
        Returns for sybase blocksize

            Returns:
                (int) - integer representing block size value

        &#34;&#34;&#34;
        return self._properties.get(&#39;sybaseInstance&#39;, {}).get(&#39;sybaseBlockSize&#39;)

    @property
    def sybase_configfile(self):
        &#34;&#34;&#34;
        Returns for sybase configfile

            Returns:
                (str) - string representing sybase config file

        &#34;&#34;&#34;
        if self.is_hadr:
            return self._primarynodeprop.get(&#39;sybaseProps&#39;, {}).get(&#39;configFile&#39;)
        return self._properties.get(&#39;sybaseInstance&#39;, {}).get(&#39;configFile&#39;)

    @property
    def sybase_sharedmemory_directory(self):
        &#34;&#34;&#34;
        Returns for sybase shared memory directory

            Returns:
                (str)  -    string representing sybase
                            sybase shared memory directory

        &#34;&#34;&#34;
        if self.is_hadr:
            return self._primarynodeprop.get(&#39;sybaseProps&#39;, {}).get(&#39;sharedMemoryDirectory&#39;)
        return self._properties.get(&#39;sybaseInstance&#39;, {}).get(&#39;sharedMemoryDirectory&#39;)

    @property
    def is_hadr(self):
        &#34;&#34;&#34;
        Returns if current instance is Sybase HADR

            Returns:
                (bool) - true if instance is HADR
        &#34;&#34;&#34;
        return self._is_hadr

    @property
    def hadr_primarynode_id(self):
        &#34;&#34;&#34;
        Returns Primary node ID if instance is HADR

            Returns:
                (str) - Sybase Hadr Primary Node id
        &#34;&#34;&#34;
        if self.is_hadr:
            return self._properties.get(&#39;sybaseClusterInstance&#39;, {}).get(&#39;primaryNodeId&#39;)
        return None

    @property
    def client_name(self):
        &#34;&#34;&#34;
        Returns client name of this instance

            Returns:
                (str) - client name as registered in the commcell

        &#34;&#34;&#34;
        return self._properties.get(&#39;instance&#39;, {}).get(&#39;clientName&#39;)

    def _primary_node_properties(self):
        &#34;&#34;&#34;
        Returns Primary Node Properties

            Returns:
                (dict) - Primary Node Properties
        &#34;&#34;&#34;
        if self.is_hadr:
            nodes = self._properties.get(&#39;sybaseClusterInstance&#39;).get(&#39;nodes&#39;)
            for node in nodes:
                if node.get(&#39;physicalClient&#39;, {}).get(&#39;clientId&#39;) == self.hadr_primarynode_id:
                    return node

    def get_node_properties(self, clientId=None):
        &#34;&#34;&#34;
        Returns Nodes Properties

        Args:
            clientId(str) - Returns Node properties of given ClientId

        Returns:
            (dict) - Node properties of given Client Id if given,
                    otherwise all the node properties
        &#34;&#34;&#34;
        if self.is_hadr:
            nodes = self._properties.get(&#39;sybaseClusterInstance&#39;).get(&#39;nodes&#39;)
            if clientId:
                for node in nodes:
                    if node.get(&#39;physicalClient&#39;, {}).get(&#39;clientId&#39;) == int(clientId):
                        return node
            return nodes

    def _restore_common_options_json(self, value):
        &#34;&#34;&#34;
        Setter for the Common options in restore JSON

            Args:
                value   (dict)  --  dict of common options
                                    for restore json

        &#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        self._commonoption_restore_json = {
            &#34;indexFreeRestore&#34;: value.get(&#34;index_free_restore&#34;, False),
            &#34;restoreToDisk&#34;: value.get(&#34;restore_to_disk&#34;, False),
            &#34;sybaseCreateDevices&#34;: value.get(&#34;sybase_create_device&#34;, False)
        }

    def _restore_destination_json(self, value):
        &#34;&#34;&#34;
        Setter for the Sybase Destination options in restore JSON

            Args:
                    value   (dict)  --  dict of values for destination option

        &#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        self._destination_restore_json = {
            &#34;destinationInstance&#34;: {
                &#34;clientName&#34;: value.get(&#34;destination_client&#34;, &#34;&#34;),
                &#34;instanceName&#34;: value.get(&#34;destination_instance_name&#34;, &#34;&#34;),
                &#34;appName&#34;: &#34;Sybase&#34;
            },
            &#34;destClient&#34;: {
                &#34;clientName&#34;: value.get(&#34;destination_client&#34;, &#34;&#34;)
            },
            &#34;destPath&#34;: [value.get(&#34;destination_path&#34;, &#34;&#34;)]
        }

    def _restore_sybase_option_json(self, value):
        &#34;&#34;&#34;
        Setter for the sybase restore option in restore JSON

            Args:
                value   (dict)  --  dict of values for sybase option

        &#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        if value.get(&#34;to_time&#34;) is None:
            time_dict = {}
        else:
            time_dict = {
                &#34;timeValue&#34;: value.get(&#34;to_time&#34;, &#34;&#34;)
            }
        self._sybase_restore_json = {
            &#34;sybaseRecoverType&#34;: &#34;STATE_RECOVER&#34;,
            &#34;pointofTime&#34;: value.get(&#34;point_in_time&#34;, &#34;&#34;),
            &#34;destinationServer&#34;: {
                &#34;name&#34;: value.get(&#34;destination_instance_name&#34;, &#34;&#34;)
            },
            &#34;pointInTime&#34;: time_dict,
            &#34;instanceRestore&#34;: value.get(&#34;instance_restore&#34;, &#34;&#34;),
            &#34;renameDatabases&#34;: value.get(&#34;rename_databases&#34;, &#34;&#34;),
            &#34;restoreType&#34;: &#34;POINT_IN_TIME&#34;,
            &#34;sybaseDatabase&#34;: value.get(&#34;syb_db&#34;, &#34;&#34;)
        }

    def _restore_json(self, **kwargs):
        &#34;&#34;&#34;
        Returns the JSON request to pass to the API as per the options selected by the user.

                Args:
                    kwargs   (list)  --  list of options need to be set for restore

                Returns:
                   (dict) - JSON request to pass to the API

        &#34;&#34;&#34;
        restore_json = super(SybaseInstance, self)._restore_json(**kwargs)
        restore_option = {}
        if kwargs.get(&#34;restore_option&#34;):
            restore_option = kwargs[&#34;restore_option&#34;]
            for key in kwargs:
                if not key == &#34;restore_option&#34;:
                    restore_option[key] = kwargs[key]
        else:
            restore_option.update(kwargs)

        self._restore_sybase_option_json(restore_option)
        restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;sybaseRstOption&#34;] = self._sybase_restore_json
        return restore_json

    def _get_sybase_restore_base_json(self,
                                      destination_client,
                                      destination_instance_name,
                                      point_in_time=False,
                                      instance_restore=False,
                                      timevalue=None,
                                      sybase_create_device=False,
                                      rename_databases=False,
                                      copy_precedence=0):
        &#34;&#34;&#34;
        Returns basic sybase restore JSON based on given combination of parameters

            Args:
                destination_client_name    (str)    -- sybase destination client for restore

                destination_instance_name  (str)    -- sybase destination instance for restore

                point_in_time              (bool)   -- determines point_in_time based restore or not
                                                       default : False

                instance_restore           (bool)   -- determines if its single database or
                                                       complete sybase server restore
                                                       default : False

                timevalue                  (str)    -- for point_in_time based restore
                                                       format: YYYY-MM-DD HH:MM:SS
                                                       default : None

                sybase_create_device       (bool)   -- determines whether to createdevice for
                                                       sybase database restore
                                                       default : False

                rename_databases           (bool)   -- determines whether renamedatabase option
                                                       chosen for given database restore
                                                       default : False

                copy_precedence            (int)    -- copy precedence value of storage policy
                                                       default: 0


            Return:
                (dict)  -    returns base sybase restore json

        &#34;&#34;&#34;
        copy_precedence_applicable = False
        if copy_precedence is not None:
            copy_precedence_applicable = True
        if instance_restore is not True:
            point_in_time = True
            if timevalue is None:
                current_time = datetime.datetime.utcnow()
                current_time = current_time.strftime(&#34;%Y-%m-%d %H:%M:%S&#34;)
                timevalue = current_time
        else:
            if (timevalue is None) and (point_in_time is True):
                current_time = datetime.datetime.utcnow()
                current_time = current_time.strftime(&#34;%Y-%m-%d %H:%M:%S&#34;)
                timevalue = current_time
        syb_db = []
        basic_sybase_options = self._restore_json(
            destination_client=destination_client,
            destination_instance_name=destination_instance_name,
            point_in_time=point_in_time,
            instance_restore=instance_restore,
            to_time=timevalue,
            from_time=None,
            sybase_create_device=sybase_create_device,
            rename_databases=rename_databases,
            copy_precedence=copy_precedence,
            copy_precedence_applicable=copy_precedence_applicable,
            syb_db=syb_db)

        return basic_sybase_options

    def _db_option(self,
                   database_list,
                   rename_databases,
                   sybase_create_device,
                   device_options):
        &#34;&#34;&#34;
        Constructs  database option for
        each databases in database list

            Args:
                database_list           (list)          --  list of databases opted for restore

                sybase_create_device    (bool)          --  determines whether to createdevice
                                                            for sybase database restore

                rename_databases        (bool)          --  determines whether renamedatabase option
                                                            chosen for given database restore

                device_options          (dict(dict))    --  list of dict for each database with
                                                            device rename and database
                                                            rename options

            Returns:
                (dict)    -     list of db_options to
                                be added to restore_json

        &#34;&#34;&#34;
        db_options = []
        for dbname in database_list:
            if device_options is None:
                db_json = self._get_single_database_json(dbname=dbname)
            else:
                if dbname in device_options.keys():
                    dev_opt = device_options[dbname]
                    db_json = self._get_single_database_json(
                        dbname, dev_opt, rename_databases, sybase_create_device)
                else:
                    db_json = self._get_single_database_json(dbname=dbname)
            db_options.append(db_json)
        return db_options

    def _get_sybase_restore_json(self,
                                 destination_client,
                                 destination_instance_name,
                                 database_list,
                                 timevalue=None,
                                 sybase_create_device=False,
                                 rename_databases=False,
                                 device_options=None,
                                 copy_precedence=0):
        &#34;&#34;&#34;
        Constructs sybase restore JSON for individual Database restore

            Args:
                destination_client_name         (str)           --  sybase destination client
                                                                    for restore

                destination_instance_name       (str)           --  sybase destination instance
                                                                    for restore

                database_list                   (list)          --  list of databases for restore

                timevalue                       (str)           --  for point_in_time based restore
                                                                    format: YYYY-MM-DD HH:MM:SS
                                                                    default : None

                sybase_create_device            (bool)          --  determines whether to
                                                                    createdevice
                                                                    for sybase database restore
                                                                    default : False

                rename_databases                (bool)          --  determines whether
                                                                    renamedatabase option
                                                                    enabled or not
                                                                    default : False

                device_options                  (dict(dict))    --  dict of dict for each
                                                                    database with device
                                                                    and database rename options
                                                                    default : None

                copy_precedence                 (int)           --  copy precedence of
                                                                    storage policy
                                                                    default: 0

            Returns:
                    (dict)    -   return restore JSON for individual Sybase database restored

        &#34;&#34;&#34;
        instance_restore = False
        point_in_time = True
        # Check to perform renamedatabase/create device
        if (sybase_create_device is False) and (rename_databases is False):
            device_options = None

        restore_json = self._get_sybase_restore_base_json(
            destination_client,
            destination_instance_name,
            point_in_time,
            instance_restore,
            timevalue,
            sybase_create_device,
            rename_databases,
            copy_precedence
        )

        db_options = self._db_option(
            database_list, rename_databases, sybase_create_device, device_options
        )
        restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;sybaseRstOption&#34;][&#34;sybaseDatabase&#34;] = db_options
        return restore_json

    def _get_sybase_full_restore_json(self,
                                      destination_client,
                                      destination_instance_name,
                                      point_in_time=False,
                                      timevalue=None,
                                      sybase_create_device=True,
                                      rename_databases=False,
                                      device_options=None,
                                      copy_precedence=0):
        &#34;&#34;&#34;
        Creates JSON for Full server restore

            Args:
                destination_client_name         (str)           --  sybase destination client
                                                                    for restore

                destination_instance_name       (str)           --  sybase destination instance
                                                                    for restore

                point_in_time                   (bool)          --  determines point_in_time
                                                                    restore or not
                                                                    default : False

                timevalue                       (str)           --  for point_in_time based restore
                                                                    format: YYYY-MM-DD HH:MM:SS
                                                                    default : None

                sybase_create_device            (bool)          --  determines whether to
                                                                    createdevice
                                                                    for sybase database restore
                                                                    default : True

                rename_databases                (bool)          --  determines whether
                                                                    renamedatabase option
                                                                    enabled or not
                                                                    default : False

                device_options                  (dict(dict))    --  dict of dict for each
                                                                    database with device
                                                                    and database
                                                                    rename options
                                                                    default : None

                copy_precedence                 (int)           --  copy precedence of
                                                                    storage policy
                                                                    default: 0

            Returns:
                    (dict) -   return restore JSON for Full sybase server restore

        &#34;&#34;&#34;

        instance_restore = True
        restore_json = self._get_sybase_restore_base_json(
            destination_client,
            destination_instance_name,
            point_in_time,
            instance_restore,
            timevalue,
            sybase_create_device,
            rename_databases,
            copy_precedence)
        db_options = []
        dblist = self._get_server_content()
        device_options_keys = []
        if device_options is not None:
            for key in device_options.keys():
                device_options_keys.append(str(key))

        if not dblist:
            raise SDKException(
                &#39;Instance&#39;, &#39;102&#39;, &#39;Database contents of Sybase server is empty&#39;
            )

        database_list = dblist
        db_options = self._db_option(
            database_list, rename_databases, sybase_create_device, device_options)
        restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;sybaseRstOption&#34;][&#34;sybaseDatabase&#34;] = db_options
        return restore_json

    def _get_single_database_json(self,
                                  dbname,
                                  device_options=None,
                                  rename_databases=False,
                                  sybase_create_device=False):
        &#34;&#34;&#34;
        Constructs database JSON for individual
        Sybase databases with create device and rename database parameters

            Args:
                dbname                  (str)              --   Database name opted for restore

                device_options          (dict(dict))       --   dict of dict for given database
                                                                with device and database
                                                                rename option


                sybase_create_device    (bool)             --   determines whether to createdevice
                                                                for sybase database restore

                rename_databases        (bool)             --   determines whether renamedatabase
                                                                option chosen for given
                                                                database restore
            Returns:
                (dict)    -     Individual Database option with restore[
                                for rename and create device]

        &#34;&#34;&#34;
        sybase_db_details = None
        databasechain = &#34;0:0:{0}:0&#34;.format(dbname)
        dbchain_list = []
        dbchain_list.append(databasechain)
        subclientid = &#34;0&#34;
        dbid = &#34;0&#34;
        datadevid = &#34;0&#34;
        logdevid = &#34;0&#34;
        size = &#34;0&#34;
        device = []
        sybase_db_details = {
            &#34;databaseId&#34;: {&#34;name&#34;: dbname},
            &#34;associatedSubClientId&#34;: 0,
            &#34;databaseChain&#34;: dbchain_list
        }
        if device_options is not None:
            if sybase_create_device:
                for key1, value1 in device_options.items():
                    if device_options[key1] is None:
                        if key1 == &#34;newdatabasename&#34;:
                            device_options[key1] = None
                        else:
                            device_options[key1] = &#34;0&#34;
            else:
                for key1, value1 in device_options.items():
                    if key1 == &#34;newdatabasename&#34;:
                        continue
                    else:
                        device_options[key1] = &#34;0&#34;

            datadevicename = device_options[&#34;datadevicename&#34;]
            newdatadevicename = device_options[&#34;newdatadevicename&#34;]
            newdatadevicepath = device_options[&#34;newdatadevicepath&#34;]
            logdevicename = device_options[&#34;logdevicename&#34;]
            newlogdevicename = device_options[&#34;newlogdevicename&#34;]
            newlogdevicepath = device_options[&#34;newlogdevicepath&#34;]
            if rename_databases:
                if device_options[&#34;newdatabasename&#34;] is None:
                    newdatabasename = dbname
                else:
                    newdatabasename = device_options[&#34;newdatabasename&#34;]
            else:
                device_options[&#34;newdatabasename&#34;] = dbname

            # Check to find given device is system device

            system_databases = [&#39;master&#39;, &#39;model&#39;, &#39;sybsystemprocs&#39;,
                                &#39;sybsystemdb&#39;, &#39;tempdb&#39;, &#39;sybmgmtdb&#39;, &#39;dbccdb&#39;, &#39;sybsecurity&#39;]
            if dbname in system_databases:
                newdatadevicename = &#34;0&#34;
                newlogdevicename = &#34;0&#34;
                newdatabasename = dbname
            data_device = &#34;{0}:{1}:{2}:{3}:{4}:{5}:{6}:{7}&#34;.format(subclientid,
                                                                   dbid,
                                                                   datadevid,
                                                                   datadevicename,
                                                                   newdatadevicename,
                                                                   newdatadevicepath,
                                                                   size,
                                                                   newdatabasename)

            log_device = &#34;{0}:{1}:{2}:{3}:{4}:{5}:{6}:{7}&#34;.format(subclientid,
                                                                  dbid,
                                                                  logdevid,
                                                                  logdevicename,
                                                                  newlogdevicename,
                                                                  newlogdevicepath,
                                                                  size,
                                                                  newdatabasename)

            device.append(data_device)
            device.append(log_device)
            sybase_db_details = {
                &#34;databaseId&#34;: {&#34;name&#34;: dbname},
                &#34;associatedSubClientId&#34;: 0,
                &#34;databaseChain&#34;: dbchain_list,
                &#34;devices&#34;: device
            }

        return sybase_db_details

    def _get_server_content(self):
        &#34;&#34;&#34;
        To get all databases for the speicfied Sybase Server instance

        Returns:
            (list)      -       list of databases available as server content

        &#34;&#34;&#34;
        subclient_dict = self.subclients._get_subclients()
        subclient_list = []
        db_list = []
        for key in subclient_dict.keys():
            subclient_list.append(str(key))
        for sub in subclient_list:
            sub_obj = self.subclients.get(sub)
            content = sub_obj.content
            for eachdb in content:
                db_list.append(eachdb)
        db_list = set(db_list)
        return db_list

    def restore_sybase_server(self,
                              destination_client=None,
                              destination_instance_name=None,
                              point_in_time=False,
                              timevalue=None,
                              rename_databases=False,
                              device_options=None,
                              copy_precedence=0):
        &#34;&#34;&#34;
        Performs Full sybase server restore

            Args:
                destination_client_name     (str)               --  sybase destination client
                                                                    for restore
                                                                    default : None

                destination_instance_name   (str)               --  sybase destination instance
                                                                    for restore
                                                                    default : None

                point_in_time               (bool)              --  determines point_in_time
                                                                    restore or not
                                                                    default:False

                timevalue                   (str)               --  for point_in_time based restore
                                                                    format: YYYY-MM-DD HH:MM:SS
                                                                    default : None


                rename_databases            (bool)              --  determines whether
                                                                    renamedatabase option chosen
                                                                    default:False


                device_options              (dict(dict))        --  dict of dict for each database
                                                                    with device and database
                                                                    rename options
                                                                    default : None

                copy_precedence             (int)               --  copy precedence of storage
                                                                    policy
                                                                    default: 0

            Note:

            Also This is dict  of dict having sourcedatabasenamead
            Key and set of another dict options
            as value corresponding to that source Database.

            Also if you wouldn&#39;t want to pass value for particular option , mark it none

            Dict format : &#34;sourceDBname&#34;:&#34;dict options&#34;

                    Example: device_options = {}
                        &#34;db1&#34;:
                            {
                                        &#34;datadevicename&#34;:&#34;testdata&#34;,

                                        &#34;newdatadevicename&#34;:&#34;testdatanew&#34;,

                                        &#34;newdatadevicepath&#34;:&#34;/opt/sap/data/testdatanew.dat&#34;,

                                        &#34;logdevicename&#34;:&#34;testlog&#34;,

                                        &#34;newlogdevicename&#34;:&#34;testlognew&#34;,

                                        &#34;newlogdevicepath&#34;:&#34;/opt/sap/data/testlognew.dat&#34;,

                                        &#34;newdatabasename&#34;: &#34;db1new&#34;

                            },

                        &#34;model&#34;:
                            {
                                        &#34;datadevicename&#34;:None,

                                        &#34;newdatadevicename&#34;:None,

                                        &#34;newdatadevicepath&#34;:None,

                                        &#34;logdevicename&#34;:None,

                                        &#34;newlogdevicename&#34;:None,

                                        &#34;newlogdevicepath&#34;:None,

                                        &#34;newdatabasename&#34;: &#34;modelnew&#34;

                            }

                    }

            Note : Devices corresponding to System database cannot be renamed

            Returns:
                (object)    -     Job containing restore details

        &#34;&#34;&#34;

        if destination_client is None:
            destination_client = self.client_name

        if destination_instance_name is None:
            destination_instance_name = self.instance_name

        sybase_create_device = True
        request_json = self._get_sybase_full_restore_json(
            destination_client,
            destination_instance_name,
            point_in_time,
            timevalue,
            sybase_create_device,
            rename_databases,
            device_options,
            copy_precedence)

        return self._process_restore_response(request_json)

    def restore_database(self,
                         destination_client=None,
                         destination_instance_name=None,
                         database_list=None,
                         timevalue=None,
                         sybase_create_device=False,
                         rename_databases=False,
                         device_options=None,
                         copy_precedence=0):
        &#34;&#34;&#34;
        Performs individual database restores

            Args:

                destination_client_name     (str)               --  destination client for restore
                                                                    default : None

                destination_instance_name   (str)               --  destination instance
                                                                    for restore
                                                                    default : None

                database_list               (list)              --  list of databases for restore

                timevalue                   (str)               --  for point_in_time based restore
                                                                    format: YYYY-MM-DD HH:MM:SS
                                                                    default : None

                sybase_create_device        (bool)              --  determines whether to create
                                                                    device for database restore
                                                                    default:False

                rename_databases            (bool)              --  determines whether
                                                                    renamedatabase option chosen
                                                                    default:False

                device_options              (dict(dict))        --  dict of dict for each database
                                                                    with device and database
                                                                    rename options
                                                                    default : None

                copy_precedence             (int)               --  copy precedence of storage
                                                                    policy
                                                                    default: 0
            Note :

            Also This is dict  of dict having sourcedatabasename
            as Key and set of another dict options
            as value corresponding to that source Database.

            Also if you wouldn&#39;t want to pass
            value for particular option , mark it none


                    Example: device_options = {
                        &#34;db1&#34;:
                            {
                                        &#34;datadevicename&#34;:&#34;testdata&#34;,

                                        &#34;newdatadevicename&#34;:&#34;testdatanew&#34;,

                                        &#34;newdatadevicepath&#34;:&#34;/opt/sap/data/testdatanew.dat&#34;,

                                        &#34;logdevicename&#34;:&#34;testlog&#34;,

                                        &#34;newlogdevicename&#34;:&#34;testlognew&#34;,

                                        &#34;newlogdevicepath&#34;:&#34;/opt/sap/data/testlognew.dat&#34;,

                                        &#34;newdatabasename&#34;: &#34;db1new&#34;
                            },
                        &#34;db2&#34;:
                        {
                                        &#34;datadevicename&#34;:None,

                                        &#34;newdatadevicename&#34;:None,

                                        &#34;newdatadevicepath&#34;:&#34;/opt/sap/data/testdatanew.dat&#34;,

                                        &#34;logdevicename&#34;:&#34;testlog&#34;,

                                        &#34;newlogdevicename&#34;:&#34;testlognew&#34;,

                                        &#34;newlogdevicepath&#34;:&#34;/opt/sap/data/testlognew.dat&#34;,

                                        &#34;newdatabasename&#34;: None
                        }

                    }


            Returns:
                (object)    -     Job containing restore details

            Raises:
                SDKException
                    if databaselist is empty

        &#34;&#34;&#34;
        if destination_client is None:
            destination_client = self.client_name

        if destination_instance_name is None:
            destination_instance_name = self.instance_name

        if database_list is None:
            raise SDKException(&#39;Instance&#39;, r&#39;102&#39;,
                               &#39;Restore Database List cannot be empty&#39;)

        request_json = self._get_sybase_restore_json(
            destination_client,
            destination_instance_name,
            database_list,
            timevalue,
            sybase_create_device,
            rename_databases,
            device_options,
            copy_precedence)

        return self._process_restore_response(request_json)

    def restore_to_disk(self,
                        destination_client,
                        destination_path,
                        backup_job_ids,
                        user_name,
                        password):
        &#34;&#34;&#34;
        Perform restore to disk [Application free restore] for sybase

            Args:
                destination_client          (str)   --  destination client name

                destination_path:           (str)   --  destination path

                backup_job_ids              (list)  --  list of backup job IDs
                                                        to be used for disk restore

                user_name                   (str)   --  impersonation user name to
                                                        restore to destination client

                password                    (str)   --  impersonation user password

            Returns:
                (object)    -     Job containing restore details

            Raises:
                SDKException
                    if backup_job_ids not given as list of items

        &#34;&#34;&#34;
        if not isinstance(backup_job_ids, list):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        request_json = self._get_restore_to_disk_json(destination_client,
                                                      destination_path,
                                                      backup_job_ids,
                                                      user_name,
                                                      password)
        del request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;sybaseRstOption&#34;]
        return self._process_restore_response(request_json)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.instances.sybaseinstance.SybaseInstance"><code class="flex name class">
<span>class <span class="ident">SybaseInstance</span></span>
<span>(</span><span>agent_object, instance_name, instance_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class to represent a standalone Sybase Instance</p>
<p>Initializes the object of Sybase Instance class</p>
<pre><code>Args:
    agent_object    (object) --     instance of the Agent class

    instance_name   (str)    --     name of the instance

    instance_id     (str)    --     id of the instance
                                    default None

Returns :
    (object) - instance of the Sybase Instance class
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sybaseinstance.py#L96-L1122" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class SybaseInstance(DatabaseInstance):
    &#34;&#34;&#34;
    Class to represent a standalone Sybase Instance
    &#34;&#34;&#34;

    def __init__(self, agent_object, instance_name, instance_id=None):
        &#34;&#34;&#34;
        Initializes the object of Sybase Instance class

            Args:
                agent_object    (object) --     instance of the Agent class

                instance_name   (str)    --     name of the instance

                instance_id     (str)    --     id of the instance
                                                default None

            Returns :
                (object) - instance of the Sybase Instance class

        &#34;&#34;&#34;
        self._sybase_restore_json = None
        self._commonoption_restore_json = None
        self._destination_restore_json = None
        super(SybaseInstance, self).__init__(
            agent_object, instance_name, instance_id)
        self._is_hadr = len(self._properties.get(&#39;sybaseClusterInstance&#39;, {})) &gt; 0
        self._instanceprop = {}  # instance variable to hold instance properties
        self._nodes = self.get_node_properties()
        self._primarynodeprop = self._primary_node_properties()

    @property
    def sybase_home(self):
        &#34;&#34;&#34;
        Returns sybase home

            Returns:
                (str) - string representing sybase home

        &#34;&#34;&#34;
        if self.is_hadr:
            return self._primarynodeprop.get(&#39;sybaseProps&#39;, {}).get(&#39;sybaseHome&#39;)
        return self._properties.get(&#39;sybaseInstance&#39;, {}).get(&#39;sybaseHome&#39;)

    @property
    def sybase_instance_name(self):
        &#34;&#34;&#34;
        Returns sybase instance name with actual case without any conversion

            Returns:
                (str) - string representing sybase instance name

        &#34;&#34;&#34;
        if self.is_hadr:
            return self._primarynodeprop.get(&#39;sybaseProps&#39;, {}).get(&#39;backupServer&#39;)[:-3]
        return self._properties.get(&#39;instance&#39;, {}).get(&#39;instanceName&#39;)

    @property
    def is_discovery_enabled(self):
        &#34;&#34;&#34;
        Returns autodiscovery enable status

            Returns:
                bool - boolean value beased on autodiscovery enable status.

                    True  - returns True if autodiscovery is enabled

                    False - returns False if autodiscosvery is not enabled

        &#34;&#34;&#34;
        if self.is_hadr:
            return self._primarynodeprop.get(&#39;sybaseProps&#39;, {}).get(&#39;enableAutoDiscovery&#39;)
        return self._properties.get(&#39;sybaseInstance&#39;, {}).get(&#39;enableAutoDiscovery&#39;)

    @property
    def localadmin_user(self):
        &#34;&#34;&#34;
        Returns for local admin user

            Returns:
                (str) - string representing local admin user

        &#34;&#34;&#34;
        if self.is_hadr:
            return self._primarynodeprop.get(&#39;sybaseProps&#39;, {}).get(&#39;localAdministrator&#39;, {}).get(&#39;userName&#39;)
        return self._properties.get(&#39;sybaseInstance&#39;, {}).get(&#39;localAdministrator&#39;, {}).get(&#39;userName&#39;)

    @property
    def sa_user(self):
        &#34;&#34;&#34;
        Returns for sa username

            Returns:
                (str) - string representing sa username

        &#34;&#34;&#34;
        if self.is_hadr:
            return self._primarynodeprop.get(&#39;sybaseProps&#39;, {}).get(&#39;saUser&#39;, {}).get(&#39;userName&#39;)
        return self._properties.get(&#39;sybaseInstance&#39;, {}).get(&#39;saUser&#39;, {}).get(&#39;userName&#39;)

    @property
    def version(self):
        &#34;&#34;&#34;
        Returns for sybase version

            Returns:
                (str) - string representing sybase version

        &#34;&#34;&#34;
        return self._properties.get(&#39;version&#39;)

    @property
    def backup_server(self):
        &#34;&#34;&#34;
        Returns for backup server

            Returns:
                (str) - string representing backup server

        &#34;&#34;&#34;
        if self.is_hadr:
            return self._primarynodeprop.get(&#39;sybaseProps&#39;, {}).get(&#39;backupServer&#39;)
        return self._properties.get(&#39;sybaseInstance&#39;, {}).get(&#39;backupServer&#39;)

    @property
    def sybase_ocs(self):
        &#34;&#34;&#34;
        Returns for sybase ocs

            Returns:
                (str) - string representing sybase OCS

        &#34;&#34;&#34;
        if self.is_hadr:
            return self._primarynodeprop.get(&#39;sybaseProps&#39;, {}).get(&#39;sybaseOCS&#39;)
        return self._properties.get(&#39;sybaseInstance&#39;, {}).get(&#39;sybaseOCS&#39;)

    @property
    def sybase_ase(self):
        &#34;&#34;&#34;
        Returns for sybase ase

            Returns:
                (str) - string representing sybase ASE

        &#34;&#34;&#34;
        if self.is_hadr:
            return self._primarynodeprop.get(&#39;sybaseProps&#39;, {}).get(&#39;sybaseASE&#39;)
        return self._properties.get(&#39;sybaseInstance&#39;, {}).get(&#39;sybaseASE&#39;)

    @property
    def sybase_blocksize(self):
        &#34;&#34;&#34;
        Returns for sybase blocksize

            Returns:
                (int) - integer representing block size value

        &#34;&#34;&#34;
        return self._properties.get(&#39;sybaseInstance&#39;, {}).get(&#39;sybaseBlockSize&#39;)

    @property
    def sybase_configfile(self):
        &#34;&#34;&#34;
        Returns for sybase configfile

            Returns:
                (str) - string representing sybase config file

        &#34;&#34;&#34;
        if self.is_hadr:
            return self._primarynodeprop.get(&#39;sybaseProps&#39;, {}).get(&#39;configFile&#39;)
        return self._properties.get(&#39;sybaseInstance&#39;, {}).get(&#39;configFile&#39;)

    @property
    def sybase_sharedmemory_directory(self):
        &#34;&#34;&#34;
        Returns for sybase shared memory directory

            Returns:
                (str)  -    string representing sybase
                            sybase shared memory directory

        &#34;&#34;&#34;
        if self.is_hadr:
            return self._primarynodeprop.get(&#39;sybaseProps&#39;, {}).get(&#39;sharedMemoryDirectory&#39;)
        return self._properties.get(&#39;sybaseInstance&#39;, {}).get(&#39;sharedMemoryDirectory&#39;)

    @property
    def is_hadr(self):
        &#34;&#34;&#34;
        Returns if current instance is Sybase HADR

            Returns:
                (bool) - true if instance is HADR
        &#34;&#34;&#34;
        return self._is_hadr

    @property
    def hadr_primarynode_id(self):
        &#34;&#34;&#34;
        Returns Primary node ID if instance is HADR

            Returns:
                (str) - Sybase Hadr Primary Node id
        &#34;&#34;&#34;
        if self.is_hadr:
            return self._properties.get(&#39;sybaseClusterInstance&#39;, {}).get(&#39;primaryNodeId&#39;)
        return None

    @property
    def client_name(self):
        &#34;&#34;&#34;
        Returns client name of this instance

            Returns:
                (str) - client name as registered in the commcell

        &#34;&#34;&#34;
        return self._properties.get(&#39;instance&#39;, {}).get(&#39;clientName&#39;)

    def _primary_node_properties(self):
        &#34;&#34;&#34;
        Returns Primary Node Properties

            Returns:
                (dict) - Primary Node Properties
        &#34;&#34;&#34;
        if self.is_hadr:
            nodes = self._properties.get(&#39;sybaseClusterInstance&#39;).get(&#39;nodes&#39;)
            for node in nodes:
                if node.get(&#39;physicalClient&#39;, {}).get(&#39;clientId&#39;) == self.hadr_primarynode_id:
                    return node

    def get_node_properties(self, clientId=None):
        &#34;&#34;&#34;
        Returns Nodes Properties

        Args:
            clientId(str) - Returns Node properties of given ClientId

        Returns:
            (dict) - Node properties of given Client Id if given,
                    otherwise all the node properties
        &#34;&#34;&#34;
        if self.is_hadr:
            nodes = self._properties.get(&#39;sybaseClusterInstance&#39;).get(&#39;nodes&#39;)
            if clientId:
                for node in nodes:
                    if node.get(&#39;physicalClient&#39;, {}).get(&#39;clientId&#39;) == int(clientId):
                        return node
            return nodes

    def _restore_common_options_json(self, value):
        &#34;&#34;&#34;
        Setter for the Common options in restore JSON

            Args:
                value   (dict)  --  dict of common options
                                    for restore json

        &#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        self._commonoption_restore_json = {
            &#34;indexFreeRestore&#34;: value.get(&#34;index_free_restore&#34;, False),
            &#34;restoreToDisk&#34;: value.get(&#34;restore_to_disk&#34;, False),
            &#34;sybaseCreateDevices&#34;: value.get(&#34;sybase_create_device&#34;, False)
        }

    def _restore_destination_json(self, value):
        &#34;&#34;&#34;
        Setter for the Sybase Destination options in restore JSON

            Args:
                    value   (dict)  --  dict of values for destination option

        &#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        self._destination_restore_json = {
            &#34;destinationInstance&#34;: {
                &#34;clientName&#34;: value.get(&#34;destination_client&#34;, &#34;&#34;),
                &#34;instanceName&#34;: value.get(&#34;destination_instance_name&#34;, &#34;&#34;),
                &#34;appName&#34;: &#34;Sybase&#34;
            },
            &#34;destClient&#34;: {
                &#34;clientName&#34;: value.get(&#34;destination_client&#34;, &#34;&#34;)
            },
            &#34;destPath&#34;: [value.get(&#34;destination_path&#34;, &#34;&#34;)]
        }

    def _restore_sybase_option_json(self, value):
        &#34;&#34;&#34;
        Setter for the sybase restore option in restore JSON

            Args:
                value   (dict)  --  dict of values for sybase option

        &#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        if value.get(&#34;to_time&#34;) is None:
            time_dict = {}
        else:
            time_dict = {
                &#34;timeValue&#34;: value.get(&#34;to_time&#34;, &#34;&#34;)
            }
        self._sybase_restore_json = {
            &#34;sybaseRecoverType&#34;: &#34;STATE_RECOVER&#34;,
            &#34;pointofTime&#34;: value.get(&#34;point_in_time&#34;, &#34;&#34;),
            &#34;destinationServer&#34;: {
                &#34;name&#34;: value.get(&#34;destination_instance_name&#34;, &#34;&#34;)
            },
            &#34;pointInTime&#34;: time_dict,
            &#34;instanceRestore&#34;: value.get(&#34;instance_restore&#34;, &#34;&#34;),
            &#34;renameDatabases&#34;: value.get(&#34;rename_databases&#34;, &#34;&#34;),
            &#34;restoreType&#34;: &#34;POINT_IN_TIME&#34;,
            &#34;sybaseDatabase&#34;: value.get(&#34;syb_db&#34;, &#34;&#34;)
        }

    def _restore_json(self, **kwargs):
        &#34;&#34;&#34;
        Returns the JSON request to pass to the API as per the options selected by the user.

                Args:
                    kwargs   (list)  --  list of options need to be set for restore

                Returns:
                   (dict) - JSON request to pass to the API

        &#34;&#34;&#34;
        restore_json = super(SybaseInstance, self)._restore_json(**kwargs)
        restore_option = {}
        if kwargs.get(&#34;restore_option&#34;):
            restore_option = kwargs[&#34;restore_option&#34;]
            for key in kwargs:
                if not key == &#34;restore_option&#34;:
                    restore_option[key] = kwargs[key]
        else:
            restore_option.update(kwargs)

        self._restore_sybase_option_json(restore_option)
        restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;sybaseRstOption&#34;] = self._sybase_restore_json
        return restore_json

    def _get_sybase_restore_base_json(self,
                                      destination_client,
                                      destination_instance_name,
                                      point_in_time=False,
                                      instance_restore=False,
                                      timevalue=None,
                                      sybase_create_device=False,
                                      rename_databases=False,
                                      copy_precedence=0):
        &#34;&#34;&#34;
        Returns basic sybase restore JSON based on given combination of parameters

            Args:
                destination_client_name    (str)    -- sybase destination client for restore

                destination_instance_name  (str)    -- sybase destination instance for restore

                point_in_time              (bool)   -- determines point_in_time based restore or not
                                                       default : False

                instance_restore           (bool)   -- determines if its single database or
                                                       complete sybase server restore
                                                       default : False

                timevalue                  (str)    -- for point_in_time based restore
                                                       format: YYYY-MM-DD HH:MM:SS
                                                       default : None

                sybase_create_device       (bool)   -- determines whether to createdevice for
                                                       sybase database restore
                                                       default : False

                rename_databases           (bool)   -- determines whether renamedatabase option
                                                       chosen for given database restore
                                                       default : False

                copy_precedence            (int)    -- copy precedence value of storage policy
                                                       default: 0


            Return:
                (dict)  -    returns base sybase restore json

        &#34;&#34;&#34;
        copy_precedence_applicable = False
        if copy_precedence is not None:
            copy_precedence_applicable = True
        if instance_restore is not True:
            point_in_time = True
            if timevalue is None:
                current_time = datetime.datetime.utcnow()
                current_time = current_time.strftime(&#34;%Y-%m-%d %H:%M:%S&#34;)
                timevalue = current_time
        else:
            if (timevalue is None) and (point_in_time is True):
                current_time = datetime.datetime.utcnow()
                current_time = current_time.strftime(&#34;%Y-%m-%d %H:%M:%S&#34;)
                timevalue = current_time
        syb_db = []
        basic_sybase_options = self._restore_json(
            destination_client=destination_client,
            destination_instance_name=destination_instance_name,
            point_in_time=point_in_time,
            instance_restore=instance_restore,
            to_time=timevalue,
            from_time=None,
            sybase_create_device=sybase_create_device,
            rename_databases=rename_databases,
            copy_precedence=copy_precedence,
            copy_precedence_applicable=copy_precedence_applicable,
            syb_db=syb_db)

        return basic_sybase_options

    def _db_option(self,
                   database_list,
                   rename_databases,
                   sybase_create_device,
                   device_options):
        &#34;&#34;&#34;
        Constructs  database option for
        each databases in database list

            Args:
                database_list           (list)          --  list of databases opted for restore

                sybase_create_device    (bool)          --  determines whether to createdevice
                                                            for sybase database restore

                rename_databases        (bool)          --  determines whether renamedatabase option
                                                            chosen for given database restore

                device_options          (dict(dict))    --  list of dict for each database with
                                                            device rename and database
                                                            rename options

            Returns:
                (dict)    -     list of db_options to
                                be added to restore_json

        &#34;&#34;&#34;
        db_options = []
        for dbname in database_list:
            if device_options is None:
                db_json = self._get_single_database_json(dbname=dbname)
            else:
                if dbname in device_options.keys():
                    dev_opt = device_options[dbname]
                    db_json = self._get_single_database_json(
                        dbname, dev_opt, rename_databases, sybase_create_device)
                else:
                    db_json = self._get_single_database_json(dbname=dbname)
            db_options.append(db_json)
        return db_options

    def _get_sybase_restore_json(self,
                                 destination_client,
                                 destination_instance_name,
                                 database_list,
                                 timevalue=None,
                                 sybase_create_device=False,
                                 rename_databases=False,
                                 device_options=None,
                                 copy_precedence=0):
        &#34;&#34;&#34;
        Constructs sybase restore JSON for individual Database restore

            Args:
                destination_client_name         (str)           --  sybase destination client
                                                                    for restore

                destination_instance_name       (str)           --  sybase destination instance
                                                                    for restore

                database_list                   (list)          --  list of databases for restore

                timevalue                       (str)           --  for point_in_time based restore
                                                                    format: YYYY-MM-DD HH:MM:SS
                                                                    default : None

                sybase_create_device            (bool)          --  determines whether to
                                                                    createdevice
                                                                    for sybase database restore
                                                                    default : False

                rename_databases                (bool)          --  determines whether
                                                                    renamedatabase option
                                                                    enabled or not
                                                                    default : False

                device_options                  (dict(dict))    --  dict of dict for each
                                                                    database with device
                                                                    and database rename options
                                                                    default : None

                copy_precedence                 (int)           --  copy precedence of
                                                                    storage policy
                                                                    default: 0

            Returns:
                    (dict)    -   return restore JSON for individual Sybase database restored

        &#34;&#34;&#34;
        instance_restore = False
        point_in_time = True
        # Check to perform renamedatabase/create device
        if (sybase_create_device is False) and (rename_databases is False):
            device_options = None

        restore_json = self._get_sybase_restore_base_json(
            destination_client,
            destination_instance_name,
            point_in_time,
            instance_restore,
            timevalue,
            sybase_create_device,
            rename_databases,
            copy_precedence
        )

        db_options = self._db_option(
            database_list, rename_databases, sybase_create_device, device_options
        )
        restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;sybaseRstOption&#34;][&#34;sybaseDatabase&#34;] = db_options
        return restore_json

    def _get_sybase_full_restore_json(self,
                                      destination_client,
                                      destination_instance_name,
                                      point_in_time=False,
                                      timevalue=None,
                                      sybase_create_device=True,
                                      rename_databases=False,
                                      device_options=None,
                                      copy_precedence=0):
        &#34;&#34;&#34;
        Creates JSON for Full server restore

            Args:
                destination_client_name         (str)           --  sybase destination client
                                                                    for restore

                destination_instance_name       (str)           --  sybase destination instance
                                                                    for restore

                point_in_time                   (bool)          --  determines point_in_time
                                                                    restore or not
                                                                    default : False

                timevalue                       (str)           --  for point_in_time based restore
                                                                    format: YYYY-MM-DD HH:MM:SS
                                                                    default : None

                sybase_create_device            (bool)          --  determines whether to
                                                                    createdevice
                                                                    for sybase database restore
                                                                    default : True

                rename_databases                (bool)          --  determines whether
                                                                    renamedatabase option
                                                                    enabled or not
                                                                    default : False

                device_options                  (dict(dict))    --  dict of dict for each
                                                                    database with device
                                                                    and database
                                                                    rename options
                                                                    default : None

                copy_precedence                 (int)           --  copy precedence of
                                                                    storage policy
                                                                    default: 0

            Returns:
                    (dict) -   return restore JSON for Full sybase server restore

        &#34;&#34;&#34;

        instance_restore = True
        restore_json = self._get_sybase_restore_base_json(
            destination_client,
            destination_instance_name,
            point_in_time,
            instance_restore,
            timevalue,
            sybase_create_device,
            rename_databases,
            copy_precedence)
        db_options = []
        dblist = self._get_server_content()
        device_options_keys = []
        if device_options is not None:
            for key in device_options.keys():
                device_options_keys.append(str(key))

        if not dblist:
            raise SDKException(
                &#39;Instance&#39;, &#39;102&#39;, &#39;Database contents of Sybase server is empty&#39;
            )

        database_list = dblist
        db_options = self._db_option(
            database_list, rename_databases, sybase_create_device, device_options)
        restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;sybaseRstOption&#34;][&#34;sybaseDatabase&#34;] = db_options
        return restore_json

    def _get_single_database_json(self,
                                  dbname,
                                  device_options=None,
                                  rename_databases=False,
                                  sybase_create_device=False):
        &#34;&#34;&#34;
        Constructs database JSON for individual
        Sybase databases with create device and rename database parameters

            Args:
                dbname                  (str)              --   Database name opted for restore

                device_options          (dict(dict))       --   dict of dict for given database
                                                                with device and database
                                                                rename option


                sybase_create_device    (bool)             --   determines whether to createdevice
                                                                for sybase database restore

                rename_databases        (bool)             --   determines whether renamedatabase
                                                                option chosen for given
                                                                database restore
            Returns:
                (dict)    -     Individual Database option with restore[
                                for rename and create device]

        &#34;&#34;&#34;
        sybase_db_details = None
        databasechain = &#34;0:0:{0}:0&#34;.format(dbname)
        dbchain_list = []
        dbchain_list.append(databasechain)
        subclientid = &#34;0&#34;
        dbid = &#34;0&#34;
        datadevid = &#34;0&#34;
        logdevid = &#34;0&#34;
        size = &#34;0&#34;
        device = []
        sybase_db_details = {
            &#34;databaseId&#34;: {&#34;name&#34;: dbname},
            &#34;associatedSubClientId&#34;: 0,
            &#34;databaseChain&#34;: dbchain_list
        }
        if device_options is not None:
            if sybase_create_device:
                for key1, value1 in device_options.items():
                    if device_options[key1] is None:
                        if key1 == &#34;newdatabasename&#34;:
                            device_options[key1] = None
                        else:
                            device_options[key1] = &#34;0&#34;
            else:
                for key1, value1 in device_options.items():
                    if key1 == &#34;newdatabasename&#34;:
                        continue
                    else:
                        device_options[key1] = &#34;0&#34;

            datadevicename = device_options[&#34;datadevicename&#34;]
            newdatadevicename = device_options[&#34;newdatadevicename&#34;]
            newdatadevicepath = device_options[&#34;newdatadevicepath&#34;]
            logdevicename = device_options[&#34;logdevicename&#34;]
            newlogdevicename = device_options[&#34;newlogdevicename&#34;]
            newlogdevicepath = device_options[&#34;newlogdevicepath&#34;]
            if rename_databases:
                if device_options[&#34;newdatabasename&#34;] is None:
                    newdatabasename = dbname
                else:
                    newdatabasename = device_options[&#34;newdatabasename&#34;]
            else:
                device_options[&#34;newdatabasename&#34;] = dbname

            # Check to find given device is system device

            system_databases = [&#39;master&#39;, &#39;model&#39;, &#39;sybsystemprocs&#39;,
                                &#39;sybsystemdb&#39;, &#39;tempdb&#39;, &#39;sybmgmtdb&#39;, &#39;dbccdb&#39;, &#39;sybsecurity&#39;]
            if dbname in system_databases:
                newdatadevicename = &#34;0&#34;
                newlogdevicename = &#34;0&#34;
                newdatabasename = dbname
            data_device = &#34;{0}:{1}:{2}:{3}:{4}:{5}:{6}:{7}&#34;.format(subclientid,
                                                                   dbid,
                                                                   datadevid,
                                                                   datadevicename,
                                                                   newdatadevicename,
                                                                   newdatadevicepath,
                                                                   size,
                                                                   newdatabasename)

            log_device = &#34;{0}:{1}:{2}:{3}:{4}:{5}:{6}:{7}&#34;.format(subclientid,
                                                                  dbid,
                                                                  logdevid,
                                                                  logdevicename,
                                                                  newlogdevicename,
                                                                  newlogdevicepath,
                                                                  size,
                                                                  newdatabasename)

            device.append(data_device)
            device.append(log_device)
            sybase_db_details = {
                &#34;databaseId&#34;: {&#34;name&#34;: dbname},
                &#34;associatedSubClientId&#34;: 0,
                &#34;databaseChain&#34;: dbchain_list,
                &#34;devices&#34;: device
            }

        return sybase_db_details

    def _get_server_content(self):
        &#34;&#34;&#34;
        To get all databases for the speicfied Sybase Server instance

        Returns:
            (list)      -       list of databases available as server content

        &#34;&#34;&#34;
        subclient_dict = self.subclients._get_subclients()
        subclient_list = []
        db_list = []
        for key in subclient_dict.keys():
            subclient_list.append(str(key))
        for sub in subclient_list:
            sub_obj = self.subclients.get(sub)
            content = sub_obj.content
            for eachdb in content:
                db_list.append(eachdb)
        db_list = set(db_list)
        return db_list

    def restore_sybase_server(self,
                              destination_client=None,
                              destination_instance_name=None,
                              point_in_time=False,
                              timevalue=None,
                              rename_databases=False,
                              device_options=None,
                              copy_precedence=0):
        &#34;&#34;&#34;
        Performs Full sybase server restore

            Args:
                destination_client_name     (str)               --  sybase destination client
                                                                    for restore
                                                                    default : None

                destination_instance_name   (str)               --  sybase destination instance
                                                                    for restore
                                                                    default : None

                point_in_time               (bool)              --  determines point_in_time
                                                                    restore or not
                                                                    default:False

                timevalue                   (str)               --  for point_in_time based restore
                                                                    format: YYYY-MM-DD HH:MM:SS
                                                                    default : None


                rename_databases            (bool)              --  determines whether
                                                                    renamedatabase option chosen
                                                                    default:False


                device_options              (dict(dict))        --  dict of dict for each database
                                                                    with device and database
                                                                    rename options
                                                                    default : None

                copy_precedence             (int)               --  copy precedence of storage
                                                                    policy
                                                                    default: 0

            Note:

            Also This is dict  of dict having sourcedatabasenamead
            Key and set of another dict options
            as value corresponding to that source Database.

            Also if you wouldn&#39;t want to pass value for particular option , mark it none

            Dict format : &#34;sourceDBname&#34;:&#34;dict options&#34;

                    Example: device_options = {}
                        &#34;db1&#34;:
                            {
                                        &#34;datadevicename&#34;:&#34;testdata&#34;,

                                        &#34;newdatadevicename&#34;:&#34;testdatanew&#34;,

                                        &#34;newdatadevicepath&#34;:&#34;/opt/sap/data/testdatanew.dat&#34;,

                                        &#34;logdevicename&#34;:&#34;testlog&#34;,

                                        &#34;newlogdevicename&#34;:&#34;testlognew&#34;,

                                        &#34;newlogdevicepath&#34;:&#34;/opt/sap/data/testlognew.dat&#34;,

                                        &#34;newdatabasename&#34;: &#34;db1new&#34;

                            },

                        &#34;model&#34;:
                            {
                                        &#34;datadevicename&#34;:None,

                                        &#34;newdatadevicename&#34;:None,

                                        &#34;newdatadevicepath&#34;:None,

                                        &#34;logdevicename&#34;:None,

                                        &#34;newlogdevicename&#34;:None,

                                        &#34;newlogdevicepath&#34;:None,

                                        &#34;newdatabasename&#34;: &#34;modelnew&#34;

                            }

                    }

            Note : Devices corresponding to System database cannot be renamed

            Returns:
                (object)    -     Job containing restore details

        &#34;&#34;&#34;

        if destination_client is None:
            destination_client = self.client_name

        if destination_instance_name is None:
            destination_instance_name = self.instance_name

        sybase_create_device = True
        request_json = self._get_sybase_full_restore_json(
            destination_client,
            destination_instance_name,
            point_in_time,
            timevalue,
            sybase_create_device,
            rename_databases,
            device_options,
            copy_precedence)

        return self._process_restore_response(request_json)

    def restore_database(self,
                         destination_client=None,
                         destination_instance_name=None,
                         database_list=None,
                         timevalue=None,
                         sybase_create_device=False,
                         rename_databases=False,
                         device_options=None,
                         copy_precedence=0):
        &#34;&#34;&#34;
        Performs individual database restores

            Args:

                destination_client_name     (str)               --  destination client for restore
                                                                    default : None

                destination_instance_name   (str)               --  destination instance
                                                                    for restore
                                                                    default : None

                database_list               (list)              --  list of databases for restore

                timevalue                   (str)               --  for point_in_time based restore
                                                                    format: YYYY-MM-DD HH:MM:SS
                                                                    default : None

                sybase_create_device        (bool)              --  determines whether to create
                                                                    device for database restore
                                                                    default:False

                rename_databases            (bool)              --  determines whether
                                                                    renamedatabase option chosen
                                                                    default:False

                device_options              (dict(dict))        --  dict of dict for each database
                                                                    with device and database
                                                                    rename options
                                                                    default : None

                copy_precedence             (int)               --  copy precedence of storage
                                                                    policy
                                                                    default: 0
            Note :

            Also This is dict  of dict having sourcedatabasename
            as Key and set of another dict options
            as value corresponding to that source Database.

            Also if you wouldn&#39;t want to pass
            value for particular option , mark it none


                    Example: device_options = {
                        &#34;db1&#34;:
                            {
                                        &#34;datadevicename&#34;:&#34;testdata&#34;,

                                        &#34;newdatadevicename&#34;:&#34;testdatanew&#34;,

                                        &#34;newdatadevicepath&#34;:&#34;/opt/sap/data/testdatanew.dat&#34;,

                                        &#34;logdevicename&#34;:&#34;testlog&#34;,

                                        &#34;newlogdevicename&#34;:&#34;testlognew&#34;,

                                        &#34;newlogdevicepath&#34;:&#34;/opt/sap/data/testlognew.dat&#34;,

                                        &#34;newdatabasename&#34;: &#34;db1new&#34;
                            },
                        &#34;db2&#34;:
                        {
                                        &#34;datadevicename&#34;:None,

                                        &#34;newdatadevicename&#34;:None,

                                        &#34;newdatadevicepath&#34;:&#34;/opt/sap/data/testdatanew.dat&#34;,

                                        &#34;logdevicename&#34;:&#34;testlog&#34;,

                                        &#34;newlogdevicename&#34;:&#34;testlognew&#34;,

                                        &#34;newlogdevicepath&#34;:&#34;/opt/sap/data/testlognew.dat&#34;,

                                        &#34;newdatabasename&#34;: None
                        }

                    }


            Returns:
                (object)    -     Job containing restore details

            Raises:
                SDKException
                    if databaselist is empty

        &#34;&#34;&#34;
        if destination_client is None:
            destination_client = self.client_name

        if destination_instance_name is None:
            destination_instance_name = self.instance_name

        if database_list is None:
            raise SDKException(&#39;Instance&#39;, r&#39;102&#39;,
                               &#39;Restore Database List cannot be empty&#39;)

        request_json = self._get_sybase_restore_json(
            destination_client,
            destination_instance_name,
            database_list,
            timevalue,
            sybase_create_device,
            rename_databases,
            device_options,
            copy_precedence)

        return self._process_restore_response(request_json)

    def restore_to_disk(self,
                        destination_client,
                        destination_path,
                        backup_job_ids,
                        user_name,
                        password):
        &#34;&#34;&#34;
        Perform restore to disk [Application free restore] for sybase

            Args:
                destination_client          (str)   --  destination client name

                destination_path:           (str)   --  destination path

                backup_job_ids              (list)  --  list of backup job IDs
                                                        to be used for disk restore

                user_name                   (str)   --  impersonation user name to
                                                        restore to destination client

                password                    (str)   --  impersonation user password

            Returns:
                (object)    -     Job containing restore details

            Raises:
                SDKException
                    if backup_job_ids not given as list of items

        &#34;&#34;&#34;
        if not isinstance(backup_job_ids, list):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
        request_json = self._get_restore_to_disk_json(destination_client,
                                                      destination_path,
                                                      backup_job_ids,
                                                      user_name,
                                                      password)
        del request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;sybaseRstOption&#34;]
        return self._process_restore_response(request_json)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.instances.dbinstance.DatabaseInstance" href="dbinstance.html#cvpysdk.instances.dbinstance.DatabaseInstance">DatabaseInstance</a></li>
<li><a title="cvpysdk.instance.Instance" href="../instance.html#cvpysdk.instance.Instance">Instance</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.instances.sybaseinstance.SybaseInstance.backup_server"><code class="name">var <span class="ident">backup_server</span></code></dt>
<dd>
<div class="desc"><p>Returns for backup server</p>
<pre><code>Returns:
    (str) - string representing backup server
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sybaseinstance.py#L207-L218" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def backup_server(self):
    &#34;&#34;&#34;
    Returns for backup server

        Returns:
            (str) - string representing backup server

    &#34;&#34;&#34;
    if self.is_hadr:
        return self._primarynodeprop.get(&#39;sybaseProps&#39;, {}).get(&#39;backupServer&#39;)
    return self._properties.get(&#39;sybaseInstance&#39;, {}).get(&#39;backupServer&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.sybaseinstance.SybaseInstance.client_name"><code class="name">var <span class="ident">client_name</span></code></dt>
<dd>
<div class="desc"><p>Returns client name of this instance</p>
<pre><code>Returns:
    (str) - client name as registered in the commcell
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sybaseinstance.py#L306-L315" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def client_name(self):
    &#34;&#34;&#34;
    Returns client name of this instance

        Returns:
            (str) - client name as registered in the commcell

    &#34;&#34;&#34;
    return self._properties.get(&#39;instance&#39;, {}).get(&#39;clientName&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.sybaseinstance.SybaseInstance.hadr_primarynode_id"><code class="name">var <span class="ident">hadr_primarynode_id</span></code></dt>
<dd>
<div class="desc"><p>Returns Primary node ID if instance is HADR</p>
<pre><code>Returns:
    (str) - Sybase Hadr Primary Node id
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sybaseinstance.py#L294-L304" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def hadr_primarynode_id(self):
    &#34;&#34;&#34;
    Returns Primary node ID if instance is HADR

        Returns:
            (str) - Sybase Hadr Primary Node id
    &#34;&#34;&#34;
    if self.is_hadr:
        return self._properties.get(&#39;sybaseClusterInstance&#39;, {}).get(&#39;primaryNodeId&#39;)
    return None</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.sybaseinstance.SybaseInstance.is_discovery_enabled"><code class="name">var <span class="ident">is_discovery_enabled</span></code></dt>
<dd>
<div class="desc"><p>Returns autodiscovery enable status</p>
<pre><code>Returns:
    bool - boolean value beased on autodiscovery enable status.

        True  - returns True if autodiscovery is enabled

        False - returns False if autodiscosvery is not enabled
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sybaseinstance.py#L153-L168" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_discovery_enabled(self):
    &#34;&#34;&#34;
    Returns autodiscovery enable status

        Returns:
            bool - boolean value beased on autodiscovery enable status.

                True  - returns True if autodiscovery is enabled

                False - returns False if autodiscosvery is not enabled

    &#34;&#34;&#34;
    if self.is_hadr:
        return self._primarynodeprop.get(&#39;sybaseProps&#39;, {}).get(&#39;enableAutoDiscovery&#39;)
    return self._properties.get(&#39;sybaseInstance&#39;, {}).get(&#39;enableAutoDiscovery&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.sybaseinstance.SybaseInstance.is_hadr"><code class="name">var <span class="ident">is_hadr</span></code></dt>
<dd>
<div class="desc"><p>Returns if current instance is Sybase HADR</p>
<pre><code>Returns:
    (bool) - true if instance is HADR
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sybaseinstance.py#L284-L292" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_hadr(self):
    &#34;&#34;&#34;
    Returns if current instance is Sybase HADR

        Returns:
            (bool) - true if instance is HADR
    &#34;&#34;&#34;
    return self._is_hadr</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.sybaseinstance.SybaseInstance.localadmin_user"><code class="name">var <span class="ident">localadmin_user</span></code></dt>
<dd>
<div class="desc"><p>Returns for local admin user</p>
<pre><code>Returns:
    (str) - string representing local admin user
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sybaseinstance.py#L170-L181" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def localadmin_user(self):
    &#34;&#34;&#34;
    Returns for local admin user

        Returns:
            (str) - string representing local admin user

    &#34;&#34;&#34;
    if self.is_hadr:
        return self._primarynodeprop.get(&#39;sybaseProps&#39;, {}).get(&#39;localAdministrator&#39;, {}).get(&#39;userName&#39;)
    return self._properties.get(&#39;sybaseInstance&#39;, {}).get(&#39;localAdministrator&#39;, {}).get(&#39;userName&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.sybaseinstance.SybaseInstance.sa_user"><code class="name">var <span class="ident">sa_user</span></code></dt>
<dd>
<div class="desc"><p>Returns for sa username</p>
<pre><code>Returns:
    (str) - string representing sa username
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sybaseinstance.py#L183-L194" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def sa_user(self):
    &#34;&#34;&#34;
    Returns for sa username

        Returns:
            (str) - string representing sa username

    &#34;&#34;&#34;
    if self.is_hadr:
        return self._primarynodeprop.get(&#39;sybaseProps&#39;, {}).get(&#39;saUser&#39;, {}).get(&#39;userName&#39;)
    return self._properties.get(&#39;sybaseInstance&#39;, {}).get(&#39;saUser&#39;, {}).get(&#39;userName&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.sybaseinstance.SybaseInstance.sybase_ase"><code class="name">var <span class="ident">sybase_ase</span></code></dt>
<dd>
<div class="desc"><p>Returns for sybase ase</p>
<pre><code>Returns:
    (str) - string representing sybase ASE
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sybaseinstance.py#L233-L244" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def sybase_ase(self):
    &#34;&#34;&#34;
    Returns for sybase ase

        Returns:
            (str) - string representing sybase ASE

    &#34;&#34;&#34;
    if self.is_hadr:
        return self._primarynodeprop.get(&#39;sybaseProps&#39;, {}).get(&#39;sybaseASE&#39;)
    return self._properties.get(&#39;sybaseInstance&#39;, {}).get(&#39;sybaseASE&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.sybaseinstance.SybaseInstance.sybase_blocksize"><code class="name">var <span class="ident">sybase_blocksize</span></code></dt>
<dd>
<div class="desc"><p>Returns for sybase blocksize</p>
<pre><code>Returns:
    (int) - integer representing block size value
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sybaseinstance.py#L246-L255" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def sybase_blocksize(self):
    &#34;&#34;&#34;
    Returns for sybase blocksize

        Returns:
            (int) - integer representing block size value

    &#34;&#34;&#34;
    return self._properties.get(&#39;sybaseInstance&#39;, {}).get(&#39;sybaseBlockSize&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.sybaseinstance.SybaseInstance.sybase_configfile"><code class="name">var <span class="ident">sybase_configfile</span></code></dt>
<dd>
<div class="desc"><p>Returns for sybase configfile</p>
<pre><code>Returns:
    (str) - string representing sybase config file
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sybaseinstance.py#L257-L268" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def sybase_configfile(self):
    &#34;&#34;&#34;
    Returns for sybase configfile

        Returns:
            (str) - string representing sybase config file

    &#34;&#34;&#34;
    if self.is_hadr:
        return self._primarynodeprop.get(&#39;sybaseProps&#39;, {}).get(&#39;configFile&#39;)
    return self._properties.get(&#39;sybaseInstance&#39;, {}).get(&#39;configFile&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.sybaseinstance.SybaseInstance.sybase_home"><code class="name">var <span class="ident">sybase_home</span></code></dt>
<dd>
<div class="desc"><p>Returns sybase home</p>
<pre><code>Returns:
    (str) - string representing sybase home
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sybaseinstance.py#L127-L138" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def sybase_home(self):
    &#34;&#34;&#34;
    Returns sybase home

        Returns:
            (str) - string representing sybase home

    &#34;&#34;&#34;
    if self.is_hadr:
        return self._primarynodeprop.get(&#39;sybaseProps&#39;, {}).get(&#39;sybaseHome&#39;)
    return self._properties.get(&#39;sybaseInstance&#39;, {}).get(&#39;sybaseHome&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.sybaseinstance.SybaseInstance.sybase_instance_name"><code class="name">var <span class="ident">sybase_instance_name</span></code></dt>
<dd>
<div class="desc"><p>Returns sybase instance name with actual case without any conversion</p>
<pre><code>Returns:
    (str) - string representing sybase instance name
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sybaseinstance.py#L140-L151" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def sybase_instance_name(self):
    &#34;&#34;&#34;
    Returns sybase instance name with actual case without any conversion

        Returns:
            (str) - string representing sybase instance name

    &#34;&#34;&#34;
    if self.is_hadr:
        return self._primarynodeprop.get(&#39;sybaseProps&#39;, {}).get(&#39;backupServer&#39;)[:-3]
    return self._properties.get(&#39;instance&#39;, {}).get(&#39;instanceName&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.sybaseinstance.SybaseInstance.sybase_ocs"><code class="name">var <span class="ident">sybase_ocs</span></code></dt>
<dd>
<div class="desc"><p>Returns for sybase ocs</p>
<pre><code>Returns:
    (str) - string representing sybase OCS
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sybaseinstance.py#L220-L231" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def sybase_ocs(self):
    &#34;&#34;&#34;
    Returns for sybase ocs

        Returns:
            (str) - string representing sybase OCS

    &#34;&#34;&#34;
    if self.is_hadr:
        return self._primarynodeprop.get(&#39;sybaseProps&#39;, {}).get(&#39;sybaseOCS&#39;)
    return self._properties.get(&#39;sybaseInstance&#39;, {}).get(&#39;sybaseOCS&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.sybaseinstance.SybaseInstance.sybase_sharedmemory_directory"><code class="name">var <span class="ident">sybase_sharedmemory_directory</span></code></dt>
<dd>
<div class="desc"><p>Returns for sybase shared memory directory</p>
<pre><code>Returns:
    (str)  -    string representing sybase
                sybase shared memory directory
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sybaseinstance.py#L270-L282" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def sybase_sharedmemory_directory(self):
    &#34;&#34;&#34;
    Returns for sybase shared memory directory

        Returns:
            (str)  -    string representing sybase
                        sybase shared memory directory

    &#34;&#34;&#34;
    if self.is_hadr:
        return self._primarynodeprop.get(&#39;sybaseProps&#39;, {}).get(&#39;sharedMemoryDirectory&#39;)
    return self._properties.get(&#39;sybaseInstance&#39;, {}).get(&#39;sharedMemoryDirectory&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.sybaseinstance.SybaseInstance.version"><code class="name">var <span class="ident">version</span></code></dt>
<dd>
<div class="desc"><p>Returns for sybase version</p>
<pre><code>Returns:
    (str) - string representing sybase version
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sybaseinstance.py#L196-L205" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def version(self):
    &#34;&#34;&#34;
    Returns for sybase version

        Returns:
            (str) - string representing sybase version

    &#34;&#34;&#34;
    return self._properties.get(&#39;version&#39;)</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.instances.sybaseinstance.SybaseInstance.get_node_properties"><code class="name flex">
<span>def <span class="ident">get_node_properties</span></span>(<span>self, clientId=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns Nodes Properties</p>
<h2 id="args">Args</h2>
<p>clientId(str) - Returns Node properties of given ClientId</p>
<h2 id="returns">Returns</h2>
<p>(dict) - Node properties of given Client Id if given,
otherwise all the node properties</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sybaseinstance.py#L330-L347" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_node_properties(self, clientId=None):
    &#34;&#34;&#34;
    Returns Nodes Properties

    Args:
        clientId(str) - Returns Node properties of given ClientId

    Returns:
        (dict) - Node properties of given Client Id if given,
                otherwise all the node properties
    &#34;&#34;&#34;
    if self.is_hadr:
        nodes = self._properties.get(&#39;sybaseClusterInstance&#39;).get(&#39;nodes&#39;)
        if clientId:
            for node in nodes:
                if node.get(&#39;physicalClient&#39;, {}).get(&#39;clientId&#39;) == int(clientId):
                    return node
        return nodes</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.sybaseinstance.SybaseInstance.restore_database"><code class="name flex">
<span>def <span class="ident">restore_database</span></span>(<span>self, destination_client=None, destination_instance_name=None, database_list=None, timevalue=None, sybase_create_device=False, rename_databases=False, device_options=None, copy_precedence=0)</span>
</code></dt>
<dd>
<div class="desc"><p>Performs individual database restores</p>
<pre><code>Args:

    destination_client_name     (str)               --  destination client for restore
                                                        default : None

    destination_instance_name   (str)               --  destination instance
                                                        for restore
                                                        default : None

    database_list               (list)              --  list of databases for restore

    timevalue                   (str)               --  for point_in_time based restore
                                                        format: YYYY-MM-DD HH:MM:SS
                                                        default : None

    sybase_create_device        (bool)              --  determines whether to create
                                                        device for database restore
                                                        default:False

    rename_databases            (bool)              --  determines whether
                                                        renamedatabase option chosen
                                                        default:False

    device_options              (dict(dict))        --  dict of dict for each database
                                                        with device and database
                                                        rename options
                                                        default : None

    copy_precedence             (int)               --  copy precedence of storage
                                                        policy
                                                        default: 0
Note :

Also This is dict  of dict having sourcedatabasename
as Key and set of another dict options
as value corresponding to that source Database.

Also if you wouldn't want to pass
value for particular option , mark it none


        Example: device_options = {
            "db1":
                {
                            "datadevicename":"testdata",

                            "newdatadevicename":"testdatanew",

                            "newdatadevicepath":"/opt/sap/data/testdatanew.dat",

                            "logdevicename":"testlog",

                            "newlogdevicename":"testlognew",

                            "newlogdevicepath":"/opt/sap/data/testlognew.dat",

                            "newdatabasename": "db1new"
                },
            "db2":
            {
                            "datadevicename":None,

                            "newdatadevicename":None,

                            "newdatadevicepath":"/opt/sap/data/testdatanew.dat",

                            "logdevicename":"testlog",

                            "newlogdevicename":"testlognew",

                            "newlogdevicepath":"/opt/sap/data/testlognew.dat",

                            "newdatabasename": None
            }

        }


Returns:
    (object)    -     Job containing restore details

Raises:
    SDKException
        if databaselist is empty
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sybaseinstance.py#L964-L1082" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_database(self,
                     destination_client=None,
                     destination_instance_name=None,
                     database_list=None,
                     timevalue=None,
                     sybase_create_device=False,
                     rename_databases=False,
                     device_options=None,
                     copy_precedence=0):
    &#34;&#34;&#34;
    Performs individual database restores

        Args:

            destination_client_name     (str)               --  destination client for restore
                                                                default : None

            destination_instance_name   (str)               --  destination instance
                                                                for restore
                                                                default : None

            database_list               (list)              --  list of databases for restore

            timevalue                   (str)               --  for point_in_time based restore
                                                                format: YYYY-MM-DD HH:MM:SS
                                                                default : None

            sybase_create_device        (bool)              --  determines whether to create
                                                                device for database restore
                                                                default:False

            rename_databases            (bool)              --  determines whether
                                                                renamedatabase option chosen
                                                                default:False

            device_options              (dict(dict))        --  dict of dict for each database
                                                                with device and database
                                                                rename options
                                                                default : None

            copy_precedence             (int)               --  copy precedence of storage
                                                                policy
                                                                default: 0
        Note :

        Also This is dict  of dict having sourcedatabasename
        as Key and set of another dict options
        as value corresponding to that source Database.

        Also if you wouldn&#39;t want to pass
        value for particular option , mark it none


                Example: device_options = {
                    &#34;db1&#34;:
                        {
                                    &#34;datadevicename&#34;:&#34;testdata&#34;,

                                    &#34;newdatadevicename&#34;:&#34;testdatanew&#34;,

                                    &#34;newdatadevicepath&#34;:&#34;/opt/sap/data/testdatanew.dat&#34;,

                                    &#34;logdevicename&#34;:&#34;testlog&#34;,

                                    &#34;newlogdevicename&#34;:&#34;testlognew&#34;,

                                    &#34;newlogdevicepath&#34;:&#34;/opt/sap/data/testlognew.dat&#34;,

                                    &#34;newdatabasename&#34;: &#34;db1new&#34;
                        },
                    &#34;db2&#34;:
                    {
                                    &#34;datadevicename&#34;:None,

                                    &#34;newdatadevicename&#34;:None,

                                    &#34;newdatadevicepath&#34;:&#34;/opt/sap/data/testdatanew.dat&#34;,

                                    &#34;logdevicename&#34;:&#34;testlog&#34;,

                                    &#34;newlogdevicename&#34;:&#34;testlognew&#34;,

                                    &#34;newlogdevicepath&#34;:&#34;/opt/sap/data/testlognew.dat&#34;,

                                    &#34;newdatabasename&#34;: None
                    }

                }


        Returns:
            (object)    -     Job containing restore details

        Raises:
            SDKException
                if databaselist is empty

    &#34;&#34;&#34;
    if destination_client is None:
        destination_client = self.client_name

    if destination_instance_name is None:
        destination_instance_name = self.instance_name

    if database_list is None:
        raise SDKException(&#39;Instance&#39;, r&#39;102&#39;,
                           &#39;Restore Database List cannot be empty&#39;)

    request_json = self._get_sybase_restore_json(
        destination_client,
        destination_instance_name,
        database_list,
        timevalue,
        sybase_create_device,
        rename_databases,
        device_options,
        copy_precedence)

    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.sybaseinstance.SybaseInstance.restore_sybase_server"><code class="name flex">
<span>def <span class="ident">restore_sybase_server</span></span>(<span>self, destination_client=None, destination_instance_name=None, point_in_time=False, timevalue=None, rename_databases=False, device_options=None, copy_precedence=0)</span>
</code></dt>
<dd>
<div class="desc"><p>Performs Full sybase server restore</p>
<pre><code>Args:
    destination_client_name     (str)               --  sybase destination client
                                                        for restore
                                                        default : None

    destination_instance_name   (str)               --  sybase destination instance
                                                        for restore
                                                        default : None

    point_in_time               (bool)              --  determines point_in_time
                                                        restore or not
                                                        default:False

    timevalue                   (str)               --  for point_in_time based restore
                                                        format: YYYY-MM-DD HH:MM:SS
                                                        default : None


    rename_databases            (bool)              --  determines whether
                                                        renamedatabase option chosen
                                                        default:False


    device_options              (dict(dict))        --  dict of dict for each database
                                                        with device and database
                                                        rename options
                                                        default : None

    copy_precedence             (int)               --  copy precedence of storage
                                                        policy
                                                        default: 0

Note:

Also This is dict  of dict having sourcedatabasenamead
Key and set of another dict options
as value corresponding to that source Database.

Also if you wouldn't want to pass value for particular option , mark it none

Dict format : "sourceDBname":"dict options"

        Example: device_options = {}
            "db1":
                {
                            "datadevicename":"testdata",

                            "newdatadevicename":"testdatanew",

                            "newdatadevicepath":"/opt/sap/data/testdatanew.dat",

                            "logdevicename":"testlog",

                            "newlogdevicename":"testlognew",

                            "newlogdevicepath":"/opt/sap/data/testlognew.dat",

                            "newdatabasename": "db1new"

                },

            "model":
                {
                            "datadevicename":None,

                            "newdatadevicename":None,

                            "newdatadevicepath":None,

                            "logdevicename":None,

                            "newlogdevicename":None,

                            "newlogdevicepath":None,

                            "newdatabasename": "modelnew"

                }

        }

Note : Devices corresponding to System database cannot be renamed

Returns:
    (object)    -     Job containing restore details
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sybaseinstance.py#L846-L962" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_sybase_server(self,
                          destination_client=None,
                          destination_instance_name=None,
                          point_in_time=False,
                          timevalue=None,
                          rename_databases=False,
                          device_options=None,
                          copy_precedence=0):
    &#34;&#34;&#34;
    Performs Full sybase server restore

        Args:
            destination_client_name     (str)               --  sybase destination client
                                                                for restore
                                                                default : None

            destination_instance_name   (str)               --  sybase destination instance
                                                                for restore
                                                                default : None

            point_in_time               (bool)              --  determines point_in_time
                                                                restore or not
                                                                default:False

            timevalue                   (str)               --  for point_in_time based restore
                                                                format: YYYY-MM-DD HH:MM:SS
                                                                default : None


            rename_databases            (bool)              --  determines whether
                                                                renamedatabase option chosen
                                                                default:False


            device_options              (dict(dict))        --  dict of dict for each database
                                                                with device and database
                                                                rename options
                                                                default : None

            copy_precedence             (int)               --  copy precedence of storage
                                                                policy
                                                                default: 0

        Note:

        Also This is dict  of dict having sourcedatabasenamead
        Key and set of another dict options
        as value corresponding to that source Database.

        Also if you wouldn&#39;t want to pass value for particular option , mark it none

        Dict format : &#34;sourceDBname&#34;:&#34;dict options&#34;

                Example: device_options = {}
                    &#34;db1&#34;:
                        {
                                    &#34;datadevicename&#34;:&#34;testdata&#34;,

                                    &#34;newdatadevicename&#34;:&#34;testdatanew&#34;,

                                    &#34;newdatadevicepath&#34;:&#34;/opt/sap/data/testdatanew.dat&#34;,

                                    &#34;logdevicename&#34;:&#34;testlog&#34;,

                                    &#34;newlogdevicename&#34;:&#34;testlognew&#34;,

                                    &#34;newlogdevicepath&#34;:&#34;/opt/sap/data/testlognew.dat&#34;,

                                    &#34;newdatabasename&#34;: &#34;db1new&#34;

                        },

                    &#34;model&#34;:
                        {
                                    &#34;datadevicename&#34;:None,

                                    &#34;newdatadevicename&#34;:None,

                                    &#34;newdatadevicepath&#34;:None,

                                    &#34;logdevicename&#34;:None,

                                    &#34;newlogdevicename&#34;:None,

                                    &#34;newlogdevicepath&#34;:None,

                                    &#34;newdatabasename&#34;: &#34;modelnew&#34;

                        }

                }

        Note : Devices corresponding to System database cannot be renamed

        Returns:
            (object)    -     Job containing restore details

    &#34;&#34;&#34;

    if destination_client is None:
        destination_client = self.client_name

    if destination_instance_name is None:
        destination_instance_name = self.instance_name

    sybase_create_device = True
    request_json = self._get_sybase_full_restore_json(
        destination_client,
        destination_instance_name,
        point_in_time,
        timevalue,
        sybase_create_device,
        rename_databases,
        device_options,
        copy_precedence)

    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.sybaseinstance.SybaseInstance.restore_to_disk"><code class="name flex">
<span>def <span class="ident">restore_to_disk</span></span>(<span>self, destination_client, destination_path, backup_job_ids, user_name, password)</span>
</code></dt>
<dd>
<div class="desc"><p>Perform restore to disk [Application free restore] for sybase</p>
<pre><code>Args:
    destination_client          (str)   --  destination client name

    destination_path:           (str)   --  destination path

    backup_job_ids              (list)  --  list of backup job IDs
                                            to be used for disk restore

    user_name                   (str)   --  impersonation user name to
                                            restore to destination client

    password                    (str)   --  impersonation user password

Returns:
    (object)    -     Job containing restore details

Raises:
    SDKException
        if backup_job_ids not given as list of items
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/sybaseinstance.py#L1084-L1122" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_to_disk(self,
                    destination_client,
                    destination_path,
                    backup_job_ids,
                    user_name,
                    password):
    &#34;&#34;&#34;
    Perform restore to disk [Application free restore] for sybase

        Args:
            destination_client          (str)   --  destination client name

            destination_path:           (str)   --  destination path

            backup_job_ids              (list)  --  list of backup job IDs
                                                    to be used for disk restore

            user_name                   (str)   --  impersonation user name to
                                                    restore to destination client

            password                    (str)   --  impersonation user password

        Returns:
            (object)    -     Job containing restore details

        Raises:
            SDKException
                if backup_job_ids not given as list of items

    &#34;&#34;&#34;
    if not isinstance(backup_job_ids, list):
        raise SDKException(&#39;Instance&#39;, &#39;101&#39;)
    request_json = self._get_restore_to_disk_json(destination_client,
                                                  destination_path,
                                                  backup_job_ids,
                                                  user_name,
                                                  password)
    del request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][&#34;sybaseRstOption&#34;]
    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.instances.dbinstance.DatabaseInstance" href="dbinstance.html#cvpysdk.instances.dbinstance.DatabaseInstance">DatabaseInstance</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.instances.dbinstance.DatabaseInstance.backupsets" href="../instance.html#cvpysdk.instance.Instance.backupsets">backupsets</a></code></li>
<li><code><a title="cvpysdk.instances.dbinstance.DatabaseInstance.browse" href="../instance.html#cvpysdk.instance.Instance.browse">browse</a></code></li>
<li><code><a title="cvpysdk.instances.dbinstance.DatabaseInstance.credentials" href="../instance.html#cvpysdk.instance.Instance.credentials">credentials</a></code></li>
<li><code><a title="cvpysdk.instances.dbinstance.DatabaseInstance.find" href="../instance.html#cvpysdk.instance.Instance.find">find</a></code></li>
<li><code><a title="cvpysdk.instances.dbinstance.DatabaseInstance.instance_id" href="../instance.html#cvpysdk.instance.Instance.instance_id">instance_id</a></code></li>
<li><code><a title="cvpysdk.instances.dbinstance.DatabaseInstance.instance_name" href="../instance.html#cvpysdk.instance.Instance.instance_name">instance_name</a></code></li>
<li><code><a title="cvpysdk.instances.dbinstance.DatabaseInstance.name" href="../instance.html#cvpysdk.instance.Instance.name">name</a></code></li>
<li><code><a title="cvpysdk.instances.dbinstance.DatabaseInstance.properties" href="../instance.html#cvpysdk.instance.Instance.properties">properties</a></code></li>
<li><code><a title="cvpysdk.instances.dbinstance.DatabaseInstance.refresh" href="../instance.html#cvpysdk.instance.Instance.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.instances.dbinstance.DatabaseInstance.subclients" href="../instance.html#cvpysdk.instance.Instance.subclients">subclients</a></code></li>
<li><code><a title="cvpysdk.instances.dbinstance.DatabaseInstance.update_properties" href="../instance.html#cvpysdk.instance.Instance.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.instances" href="index.html">cvpysdk.instances</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.instances.sybaseinstance.SybaseInstance" href="#cvpysdk.instances.sybaseinstance.SybaseInstance">SybaseInstance</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.instances.sybaseinstance.SybaseInstance.backup_server" href="#cvpysdk.instances.sybaseinstance.SybaseInstance.backup_server">backup_server</a></code></li>
<li><code><a title="cvpysdk.instances.sybaseinstance.SybaseInstance.client_name" href="#cvpysdk.instances.sybaseinstance.SybaseInstance.client_name">client_name</a></code></li>
<li><code><a title="cvpysdk.instances.sybaseinstance.SybaseInstance.get_node_properties" href="#cvpysdk.instances.sybaseinstance.SybaseInstance.get_node_properties">get_node_properties</a></code></li>
<li><code><a title="cvpysdk.instances.sybaseinstance.SybaseInstance.hadr_primarynode_id" href="#cvpysdk.instances.sybaseinstance.SybaseInstance.hadr_primarynode_id">hadr_primarynode_id</a></code></li>
<li><code><a title="cvpysdk.instances.sybaseinstance.SybaseInstance.is_discovery_enabled" href="#cvpysdk.instances.sybaseinstance.SybaseInstance.is_discovery_enabled">is_discovery_enabled</a></code></li>
<li><code><a title="cvpysdk.instances.sybaseinstance.SybaseInstance.is_hadr" href="#cvpysdk.instances.sybaseinstance.SybaseInstance.is_hadr">is_hadr</a></code></li>
<li><code><a title="cvpysdk.instances.sybaseinstance.SybaseInstance.localadmin_user" href="#cvpysdk.instances.sybaseinstance.SybaseInstance.localadmin_user">localadmin_user</a></code></li>
<li><code><a title="cvpysdk.instances.sybaseinstance.SybaseInstance.restore_database" href="#cvpysdk.instances.sybaseinstance.SybaseInstance.restore_database">restore_database</a></code></li>
<li><code><a title="cvpysdk.instances.sybaseinstance.SybaseInstance.restore_sybase_server" href="#cvpysdk.instances.sybaseinstance.SybaseInstance.restore_sybase_server">restore_sybase_server</a></code></li>
<li><code><a title="cvpysdk.instances.sybaseinstance.SybaseInstance.restore_to_disk" href="#cvpysdk.instances.sybaseinstance.SybaseInstance.restore_to_disk">restore_to_disk</a></code></li>
<li><code><a title="cvpysdk.instances.sybaseinstance.SybaseInstance.sa_user" href="#cvpysdk.instances.sybaseinstance.SybaseInstance.sa_user">sa_user</a></code></li>
<li><code><a title="cvpysdk.instances.sybaseinstance.SybaseInstance.sybase_ase" href="#cvpysdk.instances.sybaseinstance.SybaseInstance.sybase_ase">sybase_ase</a></code></li>
<li><code><a title="cvpysdk.instances.sybaseinstance.SybaseInstance.sybase_blocksize" href="#cvpysdk.instances.sybaseinstance.SybaseInstance.sybase_blocksize">sybase_blocksize</a></code></li>
<li><code><a title="cvpysdk.instances.sybaseinstance.SybaseInstance.sybase_configfile" href="#cvpysdk.instances.sybaseinstance.SybaseInstance.sybase_configfile">sybase_configfile</a></code></li>
<li><code><a title="cvpysdk.instances.sybaseinstance.SybaseInstance.sybase_home" href="#cvpysdk.instances.sybaseinstance.SybaseInstance.sybase_home">sybase_home</a></code></li>
<li><code><a title="cvpysdk.instances.sybaseinstance.SybaseInstance.sybase_instance_name" href="#cvpysdk.instances.sybaseinstance.SybaseInstance.sybase_instance_name">sybase_instance_name</a></code></li>
<li><code><a title="cvpysdk.instances.sybaseinstance.SybaseInstance.sybase_ocs" href="#cvpysdk.instances.sybaseinstance.SybaseInstance.sybase_ocs">sybase_ocs</a></code></li>
<li><code><a title="cvpysdk.instances.sybaseinstance.SybaseInstance.sybase_sharedmemory_directory" href="#cvpysdk.instances.sybaseinstance.SybaseInstance.sybase_sharedmemory_directory">sybase_sharedmemory_directory</a></code></li>
<li><code><a title="cvpysdk.instances.sybaseinstance.SybaseInstance.version" href="#cvpysdk.instances.sybaseinstance.SybaseInstance.version">version</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>