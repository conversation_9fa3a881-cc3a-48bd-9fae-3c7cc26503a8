<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.security.usergroup API documentation</title>
<meta name="description" content="Main file for performing user group operations …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.security.usergroup</code></h1>
</header>
<section id="section-intro">
<p>Main file for performing user group operations.</p>
<p>UserGroups and UserGroup are the classes defined in this file.</p>
<p>UserGroups: Class for representing all the user groups associated with a commcell</p>
<p>UserGroup:
Class for representing a single User Group of the commcell</p>
<h2 id="usergroups">Usergroups</h2>
<p><strong>init</strong>(commcell_object)
&ndash;
Initialise instance of the UserGroups
associated with the specified commcell</p>
<p><strong>str</strong>()
&ndash;
Returns all the user groups associated with
the commcell</p>
<p><strong>repr</strong>()
&ndash;
Returns the string for the instance of the
UserGroups class</p>
<p>_get_usergroups()
&ndash;
Gets all the usergroups associated with the
commcell specified</p>
<p>_get_fl_parameters()
&ndash;
Returns the fl parameters to be passed in the mongodb caching api call</p>
<p>_get_sort_parameters()
&ndash;
Returns the sort parameters to be passed in the mongodb caching api call</p>
<p>get_user_groups_cache()
&ndash;
Gets all the user groups present in CommcellEntityCache DB.</p>
<p>all_user_groups_cache()
&ndash;
Returns dict of all the user groups and their info present in CommcellEntityCache
in mongoDB</p>
<p>has_user_group()
&ndash;
Checks if a user group exists with the given
name or not</p>
<p>get(user_group_name)
&ndash;
Returns the instance of the UserGroup class,
for the the input user group name</p>
<p>add()
&ndash;
Adds local/external user group on this
commserver</p>
<p>delete(user_group_name)
&ndash;
Deletes the user group from the commcell</p>
<p>refresh()
&ndash;
Refresh the user groups associated with the
commcell</p>
<p>all_user_groups()
&ndash;
Returns all the usergroups present in the commcell</p>
<p>non_system_usergroups()
&ndash;
Returns all usergroups excluding system created ones</p>
<p>all_usergroups_prop()
&ndash;
Returns complete GET API response</p>
<h2 id="usergroup">Usergroup</h2>
<p><strong>init</strong>(commcell_object,
usergroup_name,
usergroup_id=None)
&ndash;
initialise instance of the UserGroup for the
commcell</p>
<p><strong>repr</strong>()
&ndash;
return the usergroup name, the instance is
associated with</p>
<p>_get_usergroup_id()
&ndash;
method to get the usergroup id, if not
specified in <strong>init</strong></p>
<p>_get_usergroup_properties()
&ndash;
get the properties of this usergroup</p>
<p>_has_usergroup()
&ndash;
checks list of users present on the commcell</p>
<p>refresh()
&ndash;
refresh the properties of the user group</p>
<p>status()
&ndash;
sets status for users (enable or disable)</p>
<p>update_security_associations()
&ndash;
updates 3-way security associations on usergroup</p>
<p>update_usergroup_members()
&ndash;
DELETE, OVERWRITE users with this usergroup</p>
<p>_send_request()
&ndash;
forms complete joson request for usergroup</p>
<p>_update_usergroup_props()
&ndash;
Updates the properties of this usergroup</p>
<p>_v4_update_usergroup_props()
&ndash;
Uses V4 API to update properties of a usergroup</p>
<p>users()
&ndash;
returns users who are members of this usergroup</p>
<p>usergroups()
&ndash;
returns external usergroups who are members of this
usergroup</p>
<p>user_group_id()
&ndash;
returns group id of this user group</p>
<p>user_group_name()
&ndash;
returns user group name of this group</p>
<p>description()
&ndash;
returns the description set for this user group</p>
<p>email()
&ndash;
returns the email of this user group</p>
<p>company_name()
&ndash;
returns the company name of this user group</p>
<p>company_id()
&ndash;
returns the company id of this user group</p>
<p>associations()
&ndash;
Returns security associations present on the usergroup</p>
<p>is_tfa_enabled()
&ndash;
Returns status of tfa</p>
<p>enable_tfa()
&ndash;
Enables tfa for this user group</p>
<p>disable_tfa()
&ndash;
Disables tfa for this user group</p>
<p>update_navigation_preferences
&ndash;
Updates user group navigation preferences</p>
<p>allow_multiple_company_members
&ndash;
Sets/Gets the value for allowing multiple members for a company</p>
<p>available_users_for_group
&ndash;
Returns dict of users compatible for adding to this group</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/usergroup.py#L1-L1281" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Main file for performing user group operations.

UserGroups and UserGroup are the classes defined in this file.

UserGroups: Class for representing all the user groups associated with a commcell

UserGroup:  Class for representing a single User Group of the commcell

UserGroups:
    __init__(commcell_object)       --  Initialise instance of the UserGroups
                                        associated with the specified commcell

    __str__()                       --  Returns all the user groups associated with
                                        the commcell

    __repr__()                      --  Returns the string for the instance of the
                                        UserGroups class

    _get_usergroups()               --  Gets all the usergroups associated with the
                                        commcell specified

    _get_fl_parameters()            --  Returns the fl parameters to be passed in the mongodb caching api call

    _get_sort_parameters()          --  Returns the sort parameters to be passed in the mongodb caching api call

    get_user_groups_cache()         --  Gets all the user groups present in CommcellEntityCache DB.

    all_user_groups_cache()         --  Returns dict of all the user groups and their info present in CommcellEntityCache
                                        in mongoDB

    has_user_group()                --  Checks if a user group exists with the given
                                        name or not

    get(user_group_name)            --  Returns the instance of the UserGroup class,
                                        for the the input user group name

    add()                           --  Adds local/external user group on this
                                        commserver

    delete(user_group_name)         --  Deletes the user group from the commcell

    refresh()                       --  Refresh the user groups associated with the
                                        commcell

    all_user_groups()               --  Returns all the usergroups present in the commcell

    non_system_usergroups()         --  Returns all usergroups excluding system created ones

    all_usergroups_prop()           --   Returns complete GET API response

UserGroup:
    __init__(commcell_object,
             usergroup_name,
             usergroup_id=None)     --  initialise instance of the UserGroup for the
                                        commcell

    __repr__()                      --  return the usergroup name, the instance is
                                        associated with

    _get_usergroup_id()             --  method to get the usergroup id, if not
                                        specified in __init__

    _get_usergroup_properties()     --  get the properties of this usergroup

    _has_usergroup()                --  checks list of users present on the commcell

    refresh()                       --  refresh the properties of the user group

    status()                        --  sets status for users (enable or disable)

    update_security_associations()  --  updates 3-way security associations on usergroup

    update_usergroup_members()      --  DELETE, OVERWRITE users with this usergroup

    _send_request()                 --  forms complete joson request for usergroup

    _update_usergroup_props()       --  Updates the properties of this usergroup

    _v4_update_usergroup_props()    --  Uses V4 API to update properties of a usergroup

    users()                         --  returns users who are members of this usergroup

    usergroups()                    --  returns external usergroups who are members of this
                                        usergroup

    user_group_id()                 --  returns group id of this user group

    user_group_name()               --  returns user group name of this group

    description()                   --  returns the description set for this user group

    email()                         --  returns the email of this user group

    company_name()                  --  returns the company name of this user group

    company_id()                    --  returns the company id of this user group

    associations()                  --  Returns security associations present on the usergroup

    is_tfa_enabled()                --  Returns status of tfa

    enable_tfa()                    --  Enables tfa for this user group

    disable_tfa()                   --  Disables tfa for this user group

    update_navigation_preferences   --  Updates user group navigation preferences

    allow_multiple_company_members  --  Sets/Gets the value for allowing multiple members for a company

    available_users_for_group       --  Returns dict of users compatible for adding to this group

&#34;&#34;&#34;

from __future__ import absolute_import
from __future__ import unicode_literals
from .security_association import SecurityAssociation

from ..exception import SDKException


class UserGroups(object):
    &#34;&#34;&#34;Class for getting all the usergroups associated with a commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the UserGroups class.

            Args:
                commcell_object (object)  --  instance of the Commcell class

            Returns:
                object - instance of the UserGroups class

        &#34;&#34;&#34;
        self._all_usergroups_prop = None
        self._commcell_object = commcell_object
        self._user_group = self._commcell_object._services[&#39;USERGROUPS&#39;]
        self._user_groups_cache = None
        self._user_groups = None
        self.filter_query_count = 0
        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all usergroups of the Commcell.

            Returns:
                str - string of all the usergroups for a commcell
        &#34;&#34;&#34;
        representation_string = &#34;{:^5}\t{:^50}\n\n&#34;.format(&#39;S. No.&#39;, &#39;User Group&#39;)

        for index, user_group in enumerate(self._user_groups):
            sub_str = &#39;{:^5}\t{:30}\n&#39;.format(index + 1, user_group)
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the UserGroups class.&#34;&#34;&#34;
        return &#34;UserGroups class instance for Commcell&#34;

    def _get_user_groups(self, system_created=True, full_response: bool = False):
        &#34;&#34;&#34;Gets all the user groups associated with the commcell

            Args:
                system_created  (bool) --   flag to include system created user groups
                full_response   (bool) --  flag to return complete response

            Returns:
                dict - consists of all user group in the commcell
                    {
                         &#34;user_group1_name&#34;: user_group1_id,
                         &#34;user_group2_name&#34;: user_group2_id
                    }

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        request_url = f&#39;{self._user_group % str(system_created).lower()}&amp;level=10&#39;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, request_url
        )

        if flag:
            if response.json() and &#39;userGroups&#39; in response.json():
                if full_response:
                    return response.json()
                self._all_usergroups_prop = response.json()[&#39;userGroups&#39;]
                user_groups_dict = {}

                for temp in self._all_usergroups_prop:
                    temp_name = temp[&#39;userGroupEntity&#39;][&#39;userGroupName&#39;].lower()
                    temp_id = str(temp[&#39;userGroupEntity&#39;][&#39;userGroupId&#39;]).lower()
                    user_groups_dict[temp_name] = temp_id

                return user_groups_dict
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _get_fl_parameters(self, fl: list = None) -&gt; str:
        &#34;&#34;&#34;
        Returns the fl parameters to be passed in the mongodb caching api call

        Args:
            fl    (list)  --   list of columns to be passed in API request

        Returns:
            fl_parameters(str) -- fl parameter string
        &#34;&#34;&#34;
        self.valid_columns = {
            &#39;groupName&#39;: &#39;userGroups.userGroupEntity.userGroupName&#39;,
            &#39;groupId&#39;: &#39;userGroups.userGroupEntity.userGroupId&#39;,
            &#39;description&#39;: &#39;userGroups.description&#39;,
            &#39;status&#39;: &#39;userGroups.enabled&#39;,
            &#39;company&#39;: &#39;companyName&#39;
        }
        default_columns = &#39;userGroups.userGroupEntity.userGroupName&#39;

        if fl:
            if all(col in self.valid_columns for col in fl):
                fl_parameters = f&#34;&amp;fl={default_columns},{&#39;,&#39;.join(self.valid_columns[column] for column in fl)}&#34;
            else:
                raise SDKException(&#39;UserGroup&#39;, &#39;102&#39;, &#39;Invalid column name passed&#39;)
        else:
            fl_parameters = f&#34;&amp;fl={default_columns},{&#39;,&#39;.join(column for column in self.valid_columns.values())}&#34;

        return fl_parameters

    def _get_sort_parameters(self, sort: list = None) -&gt; str:
        &#34;&#34;&#34;
        Returns the sort parameters to be passed in the mongodb caching api call

        Args:
            sort  (list)  --   contains the name of the column on which sorting will be performed and type of sort
                                valid sor type -- 1 for ascending and -1 for descending
                                e.g. sort = [&#39;connectName&#39;,&#39;1&#39;]

        Returns:
            sort_parameters(str) -- sort parameter string
        &#34;&#34;&#34;
        sort_type = str(sort[1])
        col = sort[0]
        if col in self.valid_columns.keys() and sort_type in [&#39;1&#39;, &#39;-1&#39;]:
            sort_parameter = &#39;&amp;sort=&#39; + self.valid_columns[col] + &#39;:&#39; + sort_type
        else:
            raise SDKException(&#39;UserGroup&#39;, &#39;102&#39;, &#39;Invalid column name passed&#39;)
        return sort_parameter

    def _get_fq_parameters(self, fq: list = None) -&gt; str:
        &#34;&#34;&#34;
        Returns the fq parameters based on the fq list passed
        Args:
             fq     (list) --   contains the columnName, condition and value
                    e.g. fq = [[&#39;groupName&#39;,&#39;contains&#39;, test&#39;][&#39;status&#39;,&#39;eq&#39;,&#39;Enabled&#39;]]

        Returns:
            fq_parameters(str) -- fq parameter string
        &#34;&#34;&#34;
        conditions = {&#34;contains&#34;, &#34;notContain&#34;, &#34;eq&#34;, &#34;neq&#34;}
        params = []

        for column, condition, *value in fq or []:
            if column not in self.valid_columns:
                raise SDKException(&#39;UserGroup&#39;, &#39;102&#39;, &#39;Invalid column name passed&#39;)

            if condition in conditions:
                params.append(f&#34;&amp;fq={self.valid_columns[column]}:{condition.lower()}:{value[0]}&#34;)
            elif condition == &#34;isEmpty&#34; and not value:
                params.append(f&#34;&amp;fq={self.valid_columns[column]}:in:null,&#34;)
            else:
                raise SDKException(&#39;UserGroup&#39;, &#39;102&#39;, &#39;Invalid condition passed&#39;)

        return &#34;&#34;.join(params)

    def get_user_groups_cache(self, hard: bool = False, **kwargs) -&gt; dict:
        &#34;&#34;&#34;
        Gets all the user groups present in CommcellEntityCache DB.

        Args:
            hard  (bool)          --   Flag to perform hard refresh on user groups cache.
            **kwargs (dict):
                fl (list)         --   List of columns to return in response (default: None).
                sort (list)       --   Contains the name of the column on which sorting will be performed and type of sort.
                                            Valid sort type: 1 for ascending and -1 for descending
                                            e.g. sort = [&#39;columnName&#39;, &#39;1&#39;] (default: None).
                limit (list)      --   Contains the start and limit parameter value.
                                            Default [&#39;0&#39;, &#39;100&#39;].
                search (str)      --   Contains the string to search in the commcell entity cache (default: None).
                fq (list)         --   Contains the columnName, condition and value.
                                            e.g. fq = [[&#39;groupName&#39;, &#39;contains&#39;, &#39;test&#39;],
                                            [&#39;status&#39;, &#39;eq&#39;, &#39;Enabled&#39;]] (default: None).

        Returns:
            dict: Dictionary of all the properties present in response.
        &#34;&#34;&#34;
        # computing params
        fl_parameters = self._get_fl_parameters(kwargs.get(&#39;fl&#39;, None))
        fq_parameters = self._get_fq_parameters(kwargs.get(&#39;fq&#39;, None))
        limit = kwargs.get(&#39;limit&#39;, None)
        limit_parameters = f&#39;start={limit[0]}&amp;limit={limit[1]}&#39; if limit else &#39;&#39;
        hard_refresh = &#39;&amp;hardRefresh=true&#39; if hard else &#39;&#39;
        sort_parameters = self._get_sort_parameters(kwargs.get(&#39;sort&#39;, None)) if kwargs.get(&#39;sort&#39;, None) else &#39;&#39;

        # Search operation can only be performed on limited columns, so filtering out the columns on which search works
        searchable_columns = [&#34;groupName&#34;,&#34;description&#34;,&#34;company&#34;]
        search_parameter = (f&#39;&amp;search={&#34;,&#34;.join(self.valid_columns[col] for col in searchable_columns)}:contains:&#39;
                            f&#39;{kwargs.get(&#34;search&#34;, None)}&#39;) if kwargs.get(&#39;search&#39;, None) else &#39;&#39;

        params = [
            limit_parameters,
            sort_parameters,
            fl_parameters,
            hard_refresh,
            search_parameter,
            fq_parameters
        ]
        request_url = f&#34;{self._commcell_object._services[&#39;USERGROUPS&#39;]}?&#34; + &#34;&#34;.join(params)
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#34;GET&#34;, request_url)
        if not flag:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        user_groups_cache = {}
        if response.json() and &#39;userGroups&#39; in response.json():
            self.filter_query_count = response.json().get(&#39;filterQueryCount&#39;,0)
            for group in response.json()[&#39;userGroups&#39;]:
                name = group.get(&#39;userGroupEntity&#39;, {}).get(&#39;userGroupName&#39;)
                user_groups_config = {
                    &#39;groupName&#39;:name,
                    &#39;groupId&#39;: group.get(&#39;userGroupEntity&#39;, {}).get(&#39;userGroupId&#39;),
                    &#39;description&#39;: group.get(&#39;description&#39;, &#39;&#39;),
                    &#39;status&#39;: group.get(&#39;enabled&#39;),
                    &#39;company&#39;: group.get(&#39;userGroupEntity&#39;, {}).get(&#39;entityInfo&#39;, {}).get(&#39;companyName&#39;)
                }
                if self._commcell_object.is_global_scope():
                    user_groups_config[&#39;commcell&#39;] = group.get(&#39;userGroupEntity&#39;, {}).get(&#39;entityInfo&#39;, {}).get(&#39;multiCommcellName&#39;,&#39;&#39;)

                    # Handle duplicate names for different commcells
                    unique_name = name
                    i = 1
                    while unique_name in user_groups_cache:
                        existing_user = user_groups_cache[unique_name]
                        if existing_user.get(&#39;commcell&#39;) != user_groups_config.get(&#39;commcell&#39;):
                            unique_name = f&#34;{name}__{i}&#34;
                            i += 1
                        else:
                            break
                    user_groups_cache[unique_name] = user_groups_config
                else:
                    user_groups_cache[name] = user_groups_config

            return user_groups_cache
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

    @property
    def all_user_groups_cache(self) -&gt; dict:
        &#34;&#34;&#34;Returns dict of all the user groups and their info present in CommcellEntityCache in mongoDB

            dict - consists of all user groups of the in the CommcellEntityCache
                    {
                         &#34;userGroup1_name&#34;: {
                                &#39;id&#39;: userGroup1_id ,
                                &#39;description&#39;: userGroup1_description,
                                &#39;status&#39;: userGroup1_status,
                                &#39;company&#39;: userGroup1_company
                                },
                         &#34;userGroup2_name&#34;: {
                                &#39;id&#39;: userGroup2_id ,
                                &#39;description&#39;: userGroup2_description,
                                &#39;status&#39;: userGroup2_status,
                                &#39;company&#39;: userGroup2_company
                                }
                    }
        &#34;&#34;&#34;
        if not self._user_groups_cache:
            self._user_groups_cache = self.get_user_groups_cache()
        return self._user_groups_cache

    def has_user_group(self, user_group_name):
        &#34;&#34;&#34;Checks if a user group exists in the commcell with the input user group name.

            Args:
                user_group_name (str)  --  name of the user group

            Returns:
                bool - boolean output whether the user group exists in the commcell
                       or not

            Raises:
                SDKException:
                    if type of the user group name argument is not string
        &#34;&#34;&#34;
        if not isinstance(user_group_name, str):
            raise SDKException(&#39;UserGroup&#39;, &#39;101&#39;)

        return self._user_groups and user_group_name.lower() in self._user_groups

    def get(self, user_group_name):
        &#34;&#34;&#34;Returns a user group object of the specified user group name.

            Args:
                user_group_name (str)  --  name of the user group

            Returns:
                object - instance of the UserGroup class for the given user group name

            Raises:
                SDKException:
                    if type of the user group name argument is not string

                    if no user group exists with the given name
        &#34;&#34;&#34;
        if not isinstance(user_group_name, str):
            raise SDKException(&#39;UserGroup&#39;, &#39;101&#39;)
        else:
            user_group_name = user_group_name.lower()

            if self.has_user_group(user_group_name):
                return UserGroup(self._commcell_object, user_group_name, self._user_groups[
                    user_group_name])

            raise SDKException(
                &#39;UserGroup&#39;, &#39;102&#39;, &#39;No user group exists with name: {0}&#39;.format(
                    user_group_name)
            )

    def add(self,
            usergroup_name,
            domain=None,
            users_list=None,
            entity_dictionary=None,
            external_usergroup=None,
            local_usergroup=None):
        &#34;&#34;&#34;Adds local/external user group on this commcell based domain parameter provided

            Args:
                usergroup_name (str)        --  name of the user group

                domain  (str)               --  name of the domain to which user group
                                                belongs to

                users_list      (list)                  --  list which contains users who will be
                                                members of this group

                entity_dictionary(dict)     --  combination of entity_type, entity
                                                names and role
                e.g.: security_dict={
                                &#39;assoc1&#39;:
                                    {
                                        &#39;entity_type&#39;:[&#39;entity_name&#39;],
                                        &#39;entity_type&#39;:[&#39;entity_name&#39;, &#39;entity_name&#39;],
                                        &#39;_type_&#39;:[&#39;entity_type1&#39;, &#39;entity_type2&#39;]
                                        &#39;role&#39;: [&#39;role1&#39;]
                                    },
                                &#39;assoc2&#39;:
                                    {
                                        &#39;mediaAgentName&#39;: [&#39;networktestcs&#39;, &#39;standbycs&#39;],
                                        &#39;clientName&#39;: [&#39;Linux1&#39;],
                                        &#39;role&#39;: [&#39;New1&#39;]
                                        }
                                    },
                                &#39;assoc3&#39;:
                                    {
                                        &#39;_type_&#39;: [&#39;CLIENT_ENTITY&#39;, &#39;STORAGE_POLICIES_ENTITY&#39;],
                                        &#39;role&#39;: [&#39;Alert Owner&#39;]
                                        }
                                    },
                entity_type         --      key for the entity present in dictionary
                                            on which user will have access
                entity_name         --      Value of the key
                role                --      key for role name you specify
                e.g:   e.g.: {&#34;clientName&#34;:&#34;Linux1&#34;}
                Entity Types are:   clientName, mediaAgentName, libraryName, userName,
                                    userGroupName, storagePolicyName, clientGroupName,
                                    schedulePolicyName, locationName, providerDomainName,
                                    alertName, workflowName, policyName, roleName

                entity_name = &#34;Linux1&#34;, &#34;ClientMachine1&#34;

                external_usergroup(list)    --  list of domain user group which could
                                                be added as members to this group

                local_usergroup (list)      --  list of commcell usergroup which could
                                                be added as members to this group

            Returns:
                (object)    -   UserGroup class instance for the specified user group name

            Raises:
                SDKException:

                    if usergroup with specified name already exists

                    if failed to add usergroup to commcell
        &#34;&#34;&#34;
        if domain:
            group_name = &#34;{0}\\{1}&#34;.format(domain, usergroup_name)
        else:
            group_name = usergroup_name

        if self.has_user_group(group_name):
            raise SDKException(
                &#39;User&#39;, &#39;102&#39;, &#34;UserGroup {0} already exists on this commcell.&#34;.format
                (group_name))

        local_usergroup_json = []
        if local_usergroup:
            local_usergroup_json = [{&#34;userGroupName&#34;: local_group}
                                    for local_group in local_usergroup]

        security_json = {}
        if entity_dictionary:
            security_request = SecurityAssociation._security_association_json(
                entity_dictionary=entity_dictionary)
            security_json = {
                &#34;associationsOperationType&#34;: &#34;ADD&#34;,
                &#34;associations&#34;: security_request
            }
        user_json = []
        if users_list:
            user_json = [{&#34;userName&#34;: uname} for uname in users_list]

        external_usergroup_json = []
        if external_usergroup:
            external_usergroup_json = [{&#34;userGroupName&#34;: external_group}
                                       for external_group in external_usergroup]

        usergrop_request = {
            &#34;groups&#34;: [
                {
                    &#34;userGroupEntity&#34;: {
                        &#34;userGroupName&#34;: group_name
                    },
                    &#34;securityAssociations&#34;: security_json,
                    &#34;users&#34;: user_json,
                    &#34;localUserGroups&#34;: local_usergroup_json,
                    &#34;associatedExternalUserGroups&#34;: external_usergroup_json
                }
            ]
        }

        usergroup_req = self._commcell_object._services[&#39;USERGROUPS&#39;]

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, usergroup_req, usergrop_request
        )
        if flag:
            if response.json():
                if &#39;response&#39; in response.json():
                    response_json = response.json()[&#39;response&#39;][0]
                    error_code = response_json[&#39;errorCode&#39;]
                    error_message = response_json.get(&#39;errorString&#39;, &#39;&#39;)
                    if not error_code == 0:
                        raise SDKException(&#39;Response&#39;, &#39;101&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        self.refresh()
        return self.get(group_name)

    def delete(self, user_group, new_user=None, new_usergroup=None):
        &#34;&#34;&#34;Deletes the specified user from the existing commcell users

            Args:
                user_group          (str)   --  name of the usergroup which has to be deleted

                new_user            (str)   --  name of the target user, whom the ownership
                                                of entities should be transferred

                new_usergroup       (str)   --  name of the user group, whom the ownership
                                                of entities should be transferred

            Note: either user or usergroup  should be provided for ownership
                transfer not both.

            Raises:
                SDKException:

                    if usergroup doesn&#39;t exist

                    if new user and new usergroup any of these is passed and these doesn&#39;t
                    exist on commcell

                    if both user and usergroup is passed for ownership transfer

                    if both user and usergroup is not passed for ownership transfer

                    if response is not success

        &#34;&#34;&#34;
        if not self.has_user_group(user_group):
            raise SDKException(
                &#39;UserGroup&#39;, &#39;102&#39;, &#34;UserGroup {0} doesn&#39;t exists on this commcell.&#34;.format(
                    user_group)
            )
        if new_user and new_usergroup:
            raise SDKException(
                &#39;User&#39;, &#39;102&#39;, &#34;{0} and {1} both can not be set as owner!! &#34;
                               &#34;please send either new_user or new_usergroup&#34;.format(new_user,
                                                                                     new_usergroup))
        else:
            if new_user:
                if not self._commcell_object.users.has_user(new_user):
                    raise SDKException(
                        &#39;User&#39;, &#39;102&#39;, &#34;User {0} doesn&#39;t exists on this commcell.&#34;.format(
                            new_user)
                    )
                new_user_id = self._commcell_object.users._users[new_user.lower()]
                new_group_id = 0
            else:
                if new_usergroup:
                    if not self.has_user_group(new_usergroup):
                        raise SDKException(
                            &#39;UserGroup&#39;, &#39;102&#39;, &#34;UserGroup {0} doesn&#39;t exists &#34;
                                                &#34;on this commcell.&#34;.format(new_usergroup)
                        )
                else:
                    raise SDKException(
                        &#39;User&#39;, &#39;102&#39;,
                        &#34;Ownership transfer is mondatory!! Please provide new owner information&#34;
                    )
                new_group_id = self._commcell_object.user_groups.get(new_usergroup).user_group_id
                new_user_id = 0

        delete_usergroup = self._commcell_object._services[&#39;DELETE_USERGROUP&#39;] % (
            self._user_groups[user_group.lower()], new_user_id, new_group_id)
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;DELETE&#39;, delete_usergroup
        )
        if flag:
            if response.json():
                if &#39;response&#39; in response.json():
                    response_json = response.json()[&#39;response&#39;][0]
                    error_code = response_json[&#39;errorCode&#39;]
                    error_message = response_json[&#39;errorString&#39;]
                    if not error_code == 0:
                        raise SDKException(&#39;Response&#39;, &#39;101&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        self._user_groups = self._get_user_groups()

    def refresh(self, **kwargs):
        &#34;&#34;&#34;
        Refresh the list of user groups on this commcell.

            Args:
                **kwargs (dict):
                    mongodb (bool)  -- Flag to fetch user groups cache from MongoDB (default: False).
                    hard (bool)     -- Flag to hard refresh MongoDB cache for this entity (default: False).
        &#34;&#34;&#34;
        mongodb = kwargs.get(&#39;mongodb&#39;, False)
        hard = kwargs.get(&#39;hard&#39;, False)

        self._user_groups = self._get_user_groups()
        if mongodb:
            self._user_groups_cache = self.get_user_groups_cache(hard=hard)

    @property
    def all_user_groups(self):
        &#34;&#34;&#34;Returns dict of all the user groups associated with this commcell

        dict - consists of all user group in the commcell
                 {
                   &#34;user_group1_name&#34;: user_group1_id,
                   &#34;user_group2_name&#34;: user_group2_id
                  }

        &#34;&#34;&#34;
        return self._user_groups

    def non_system_usergroups(self):
        &#34;&#34;&#34;Returns dict of all the user groups associated with this commcell

        dict - consists of all user group in the commcell
                 {
                   &#34;user_group1_name&#34;: user_group1_id,
                   &#34;user_group2_name&#34;: user_group2_id
                  }

        &#34;&#34;&#34;
        return self._get_user_groups(system_created=False)

    @property
    def all_usergroups_prop(self)-&gt;list[dict]:
        &#34;&#34;&#34;
        Returns complete GET API response
        &#34;&#34;&#34;
        self._all_usergroups_prop = self._get_user_groups(full_response=True).get(&#34;userGroups&#34;,[])
        return self._all_usergroups_prop


class UserGroup(object):
    &#34;&#34;&#34;Class for performing operations for a specific User Group.&#34;&#34;&#34;

    def __init__(self, commcell_object, user_group_name, user_group_id=None):
        &#34;&#34;&#34;Initialise the UserGroup class instance.

            Args:
                commcell_object     (object)  --  instance of the Commcell class

                user_group_name     (str)     --  name of the user group

                user_group_id       (str)     --  id of the user group
                    default: None

            Returns:
                object - instance of the UserGroup class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._user_group_name = user_group_name.lower()

        if user_group_id:
            self._user_group_id = str(user_group_id)
        else:
            self._user_group_id = self._get_usergroup_id()

        self._usergroup = self._commcell_object._services[&#39;USERGROUP&#39;] % (self.user_group_id)

        self._description = None
        self._properties = None
        self._email = None
        self._users = []
        self._usergroups = []
        self._usergroup_status = None
        self._company_id = None
        self._company_name = None
        self._allow_multiple_company_members = False
        self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        representation_string = &#39;User Group instance for UserGroup: &#34;{0}&#34;&#39;

        return representation_string.format(
            self.user_group_name
        )

    def _get_usergroup_id(self):
        &#34;&#34;&#34;Gets the user group id associated with this user group.

            Returns:
                str - id associated with this user group
        &#34;&#34;&#34;
        user_groups = UserGroups(self._commcell_object)
        return user_groups.get(self.user_group_name).user_group_id

    def _get_usergroup_properties(self):
        &#34;&#34;&#34;Gets the user group properties of this user group.

            Returns:
                dict - dictionary consisting of the properties of this user group

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;GET&#39;, self._usergroup)

        if flag:
            if response.json() and &#39;userGroups&#39; in response.json():
                self._properties = response.json()[&#39;userGroups&#39;][0]

                if &#39;description&#39; in self._properties:
                    self._description = self._properties[&#39;description&#39;]

                if &#39;enabled&#39; in self._properties:
                    self._usergroup_status = self._properties[&#39;enabled&#39;]

                self._allow_multiple_company_members = self._properties.get(&#39;allowMultiCompanyMembers&#39;, False)

                if &#39;email&#39; in self._properties:
                    self._email = self._properties[&#39;email&#39;]

                self._company_id = self._properties.get(&#39;groupSecurity&#39;, {}).get(&#39;tagWithCompany&#39;, {}).get(&#39;providerId&#39;)
                self._company_name = self._properties.get(&#39;groupSecurity&#39;, {}).get(&#39;tagWithCompany&#39;, {}).get(&#39;providerDomainName&#39;)

                security_properties = self._properties.get(&#39;securityAssociations&#39;, {}).get(
                    &#39;associations&#39;, {})
                self._security_associations = SecurityAssociation.fetch_security_association(
                    security_dict=security_properties)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _has_usergroup(self, usergroup_list):
        &#34;&#34;&#34;checks whether these users are present on this commcell

            Args:
            usergroup_list (list)   --   list of local_usergroup or external user group

            Raises:
                SDKException:
                    if user is not found on this commcell
        &#34;&#34;&#34;
        if usergroup_list is not None:
            for usergroup in usergroup_list:
                if not self._commcell_object.user_groups.has_user_group(usergroup):
                    raise SDKException(
                        &#39;UserGroup&#39;, &#39;102&#39;, &#34;UserGroup {0} doesn&#39;texists on this commcell.&#34;.format(
                            usergroup))

    @property
    def name(self):
        &#34;&#34;&#34;Returns the UserGroup display name&#34;&#34;&#34;
        return self._properties[&#39;userGroupEntity&#39;][&#39;userGroupName&#39;]

    @property
    def user_group_id(self):
        &#34;&#34;&#34;Treats the usergroup id as a read-only attribute.&#34;&#34;&#34;
        return self._user_group_id

    @property
    def user_group_name(self):
        &#34;&#34;&#34;Treats the usergroup name as a read-only attribute.&#34;&#34;&#34;
        return self._user_group_name

    @property
    def description(self):
        &#34;&#34;&#34;Treats the usergroup description as a read-only attribute.&#34;&#34;&#34;
        return self._description

    @property
    def email(self):
        &#34;&#34;&#34;Treats the usergroup email as a read-only attribute.&#34;&#34;&#34;
        return self._email

    @property
    def company_id(self):
        &#34;&#34;&#34;Treats the usergroup company id as a read-only attribute.&#34;&#34;&#34;
        return self._company_id

    @property
    def company_name(self):
        &#34;&#34;&#34;
        Returns:
            str  -  company name to which user group belongs to.
            str  -  empty string, if usergroup belongs to Commcell
        &#34;&#34;&#34;
        return self._company_name

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the UserGroup.&#34;&#34;&#34;
        self._get_usergroup_properties()

    @property
    def status(self):
        &#34;&#34;&#34;Returns the status of user group on this commcell&#34;&#34;&#34;
        return self._usergroup_status

    @status.setter
    def status(self, value):
        &#34;&#34;&#34;Sets the status for this commcell user group&#34;&#34;&#34;

        request_json = {
            &#34;groups&#34;: [{
                &#34;enabled&#34;: value
            }]
        }
        self._update_usergroup_props(request_json)

    @property
    def allow_multiple_company_members(self):
        &#34;&#34;&#34;
        Returns the status of user group on this commcell
        Returns:
            Bool    -   True for allowing multiple company members
                        False otherwise
        &#34;&#34;&#34;
        return self._allow_multiple_company_members

    @allow_multiple_company_members.setter
    def allow_multiple_company_members(self, flag=True):
        &#34;&#34;&#34;
        Allows Multiple Company Members to be part of this commcell user group
        Args:
            flag(bool)      -   True if multiple company members to be allowed,
                                False otherwise
        &#34;&#34;&#34;
        if not isinstance(flag, bool):
            raise SDKException(&#39;UserGroup&#39;, &#39;101&#39;)
        request_json = {&#34;allowMultipleCompanyMembers&#34;: flag}
        self._v4_update_usergroup_props(request_json)

    @property
    def users(self):
        &#34;&#34;&#34;Returns the list of associated users with this usergroup&#34;&#34;&#34;
        users = []
        if &#39;users&#39; in self._properties:
            for user in self._properties[&#39;users&#39;]:
                users.append(user[&#39;userName&#39;])

        return users

    @property
    def usergroups(self):
        &#34;&#34;&#34;Returns the list of associated external usergroups with this usergroup&#34;&#34;&#34;
        user_groups = []
        if &#39;externalUserGroups&#39; in self._properties:
            for user_group in self._properties[&#39;externalUserGroups&#39;]:
                user_groups.append(user_group[&#39;externalGroupName&#39;])

        return user_groups

    @property
    def associations(self):
        &#34;&#34;&#34;Returns security associations present on th usergroup&#34;&#34;&#34;
        return self._security_associations

    @property
    def is_tfa_enabled(self):
        &#34;&#34;&#34;Returns two factor authentication status (True/False)&#34;&#34;&#34;
        return self._properties.get(&#39;enableTwoFactorAuthentication&#39;) == 1

    def enable_tfa(self):
        &#34;&#34;&#34;
        enables two factor authentication on this group

            Note: tfa will not get enabled for this user group if global tfa is disabled

        Returns:
             None
        &#34;&#34;&#34;
        request_json = {
            &#34;groups&#34;: [{
                &#34;enableTwoFactorAuthentication&#34;: 1
            }]
        }
        self._update_usergroup_props(request_json)

    def disable_tfa(self):
        &#34;&#34;&#34;
        disables two factor authentication for this group

        Returns:
            None
        &#34;&#34;&#34;
        request_json = {
            &#34;groups&#34;: [{
                &#34;enableTwoFactorAuthentication&#34;: 0
            }]
        }
        self._update_usergroup_props(request_json)

    def update_security_associations(self, entity_dictionary, request_type):
        &#34;&#34;&#34;handles three way associations (role-usergroup-entities)

            Args:
                entity_dictionary   (dict)      --  combination of entity_type, entity names
                                                    and role
                e.g.: security_dict={
                                &#39;assoc1&#39;:
                                    {
                                        &#39;entity_type&#39;:[&#39;entity_name&#39;],
                                        &#39;entity_type&#39;:[&#39;entity_name&#39;, &#39;entity_name&#39;],
                                        &#39;role&#39;: [&#39;role1&#39;]
                                    },
                                &#39;assoc2&#39;:
                                    {
                                        &#39;mediaAgentName&#39;: [&#39;networktestcs&#39;, &#39;standbycs&#39;],
                                        &#39;clientName&#39;: [&#39;Linux1&#39;],
                                        &#39;role&#39;: [&#39;New1&#39;]
                                        }
                                    }

                entity_type         --      key for the entity present in dictionary
                                            on which user will have access

                entity_name         --      Value of the key

                role                --      key for role name you specify

                e.g.: {&#34;clientName&#34;:&#34;Linux1&#34;}
                Entity Types are:   clientName, mediaAgentName, libraryName, userName,
                                    userGroupName, storagePolicyName, clientGroupName,
                                    schedulePolicyName, locationName, providerDomainName,
                                    alertName, workflowName, policyName, roleName

                entity_name:        client name for entity_type &#39;clientName&#39;
                                    Media agent name for entitytype &#39;mediaAgentName&#39;
                                    similar for other entity_types

                request_type        --      decides whether to UPDATE, DELETE or
                                            OVERWRITE user security association.

            Raises:
                SDKException:

                    if failed update user properties

        &#34;&#34;&#34;
        security_request = {}
        if entity_dictionary:
            security_request = SecurityAssociation._security_association_json(entity_dictionary)

        self._send_request(request_type, association_blob=security_request)

    def update_usergroup_members(
            self,
            request_type,
            users_list=None,
            external_usergroups=None,
            local_usergroups=None):
        &#34;&#34;&#34;updates users and usergroups to local usergroup members tab
            Args:
                request_type (str)              --      decides whether to UPDATE, DELETE or
                                                        OVERWRITE user security association

                users_list  (list)              --      comlete list of local users and
                                                        externalusers
                e.g : users_list = [r&#39;Red\\RedUser2&#39;, r&#39;Red\\RedUser12&#39;, r&#39;mirje-pc\\A&#39;,
                                    r&#39;mirje-pc\\B&#39;, r&#39;John&#39;, r&#39;Prasad&#39;, r&#39;Mahesh&#39;]
                where:
                RedUser2, RedUser12 are belongs to AD &#39;Red&#39;
                A, B are belongs to AD &#39;mirje-pc&#39;
                John, Prasad, Mahesh are local users

                external_usergroups (list)      --      complete list of external usergroup only

                e.g : external_usergroups_list = [&#39;Red\\RedGroup2&#39;, &#39;mirje-pc\\XYZ&#39;]
                where:
                RedGroup2 is external user group present in AD &#39;Red&#39;
                XYZ is external user group present in AD &#39;mirje-pc&#39;

                local_usergroups (list) --  complete list of local user groups
                                            (Not required when updating external
                                            usergroup properties)
                e.g : local_usergroups=[&#39;usergroup1&#39;, &#39;usegrouop2&#39;]


            Raises:
                SDKException:

                    if failed update local usergroup properties
        &#34;&#34;&#34;
        if users_list is not None:
            for user in users_list:
                if not self._commcell_object.users.has_user(user):
                    raise SDKException(
                        &#39;User&#39;, &#39;102&#39;, &#34;User {0} doesn&#39;t exists on this commcell.&#34;.format(user))
            userlist_json = [{&#34;userName&#34;: xuser} for xuser in users_list]
        else:
            userlist_json = []

        if external_usergroups is not None:
            self._has_usergroup(external_usergroups)
            usergroup_json = [{&#34;userGroupName&#34;: name} for name in external_usergroups]
        else:
            usergroup_json = []

        if local_usergroups is not None:
            self._has_usergroup(local_usergroups)
            local_groups_json = [{&#34;userGroupName&#34;: user_name} for user_name in local_usergroups]
        else:
            local_groups_json = []

        self._send_request(request_type=request_type, users_blob=userlist_json,
                           external_group_blob=usergroup_json, local_group_blob=local_groups_json)

    def update_navigation_preferences(self, include_navigation_list):
        &#34;&#34;&#34;Updates the user group&#39;s include navigation preferences with the the list provided

            Args:
                include_navigation_list   (list)    --  list of navigation items to be seen in command center

            Raises:
                SDKException:

                    if failed update user properties

        &#34;&#34;&#34;
        request_json = {
            &#39;groups&#39;: [
                {
                    &#39;additionalSettings&#39;: [
                        {
                            &#39;deleted&#39;: 0,
                            &#39;relativepath&#39;: &#39;CommServDB.AdminConsole&#39;,
                            &#39;keyName&#39;: &#39;includeNavItems&#39;,
                            &#39;type&#39;: &#39;MULTISTRING&#39;,
                            &#39;value&#39;: &#39;,&#39;.join(include_navigation_list),
                            &#39;enabled&#39;: 1
                        }
                    ]
                }
            ]
        }
        self._update_usergroup_props(request_json)

    def _send_request(self, request_type, association_blob=None, users_blob=None,
                      external_group_blob=None, local_group_blob=None):
        &#34;&#34;&#34;forms complete json request for user groups

            Args:
                request_type        (str)   --  decides whether to UPDATE, DELETE or
                                                OVERWRITE user security association

                association_blob    (dict)  --  security association blob generated from
                                                static method _security_association_json
                                                present in SecurityAssociation

                users_blob          (dict)  --  comlete json blob of local users and
                                                externalusers

                external_group_blob (dict)  --  complete json blob of external
                                                usergroup only

                local_group_blob    (list)  --  complete json blob of local
                                                usergroup only

            Raises:
                SDKException:

                    if failed update local usergroup properties

                    if response received is empty

                    if response is not success
        &#34;&#34;&#34;
        update_usergroup_request = {
            &#34;NONE&#34;: 0,
            &#34;OVERWRITE&#34;: 1,
            &#34;UPDATE&#34;: 2,
            &#34;DELETE&#34;: 3,
        }
        if local_group_blob is None:
            local_group_blob = []

        if association_blob:
            security_association_request = {
                &#34;associationsOperationType&#34;: update_usergroup_request[request_type.upper()],
                &#34;associations&#34;: association_blob
            }
        else:
            security_association_request = {}

        if users_blob is None:
            users_blob = []

        group_json = {
            &#34;localUserGroupsOperationType&#34;: update_usergroup_request[request_type.upper()],
            &#34;usersOperationType&#34;: update_usergroup_request[request_type.upper()],
            &#34;externalUserGroupsOperationType&#34;: update_usergroup_request[request_type.upper()],
            &#34;securityAssociations&#34;: security_association_request,
            &#34;localUserGroups&#34;: local_group_blob,
            &#34;users&#34;: users_blob
        }
        if external_group_blob is not None:
            group_json.update({&#34;associatedExternalUserGroups&#34;: external_group_blob})

        request_json = {
            &#34;groups&#34;: [group_json]
        }

        self._update_usergroup_props(request_json)

    def _v4_update_usergroup_props(self, properties_dict):
        &#34;&#34;&#34;Updates the properties of this usergroup

            Args:
                properties_dict (dict)  --  user property dict which is to be updated

            Raises:
                SDKException:
                    if arguments passed are of incorrect types
                    if failed to update user group properties
                    if response is empty
                    if response is not success
        &#34;&#34;&#34;
        if not isinstance(properties_dict, dict):
            raise SDKException(&#39;UserGroup&#39;, &#39;101&#39;)
        usergroup_request = self._commcell_object._services[&#39;USERGROUP_V4&#39;] % (self._user_group_id)

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;PUT&#39;, usergroup_request, properties_dict
        )
        if flag:
            if response.json():
                error_code = response.json().get(&#39;errorCode&#39;, 0)
                if error_code != 0:
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))
                self.refresh()
                return
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    def _update_usergroup_props(self, properties_dict):
        &#34;&#34;&#34;Updates the properties of this usergroup

            Args:
                properties_dict (dict)  --  user property dict which is to be updated

            Raises:
                SDKException:
                    if failed update usergroup properties

                    if response is not success
        &#34;&#34;&#34;
        usergroup_request = self._commcell_object._services[&#39;USERGROUP&#39;] % (self._user_group_id)

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, usergroup_request, properties_dict
        )
        if flag:
            if response.json():
                if &#39;response&#39; in response.json():
                    response_json = response.json()[&#39;response&#39;][0]
                    error_code = response_json[&#39;errorCode&#39;]
                    error_message = response_json[&#39;errorString&#39;]
                    if not error_code == 0:
                        raise SDKException(&#39;Response&#39;, &#39;101&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        self.refresh()

    def available_users_for_group(self):
        &#34;&#34;&#34;Returns the dict of all the users on the commcell, that can be added to this group

        dict of all the users available for adding to group
                   {
                        &#34;user1&#34;: {
                            &#34;id&#34;: ...,
                            &#34;name&#34;: ...,
                            &#34;GUID&#34;: ...,
                            ..
                        }
                   }
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._commcell_object._services[&#39;V4_USERS&#39;] + f&#39;&amp;groupContext={self.user_group_id}&#39;
        )

        if flag:
            if response.json():
                users_dict = {}
                for user in response.json().get(&#39;users&#39;, []):
                    users_dict[user.get(&#39;name&#39;)] = user
                return users_dict
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.security.usergroup.UserGroup"><code class="flex name class">
<span>class <span class="ident">UserGroup</span></span>
<span>(</span><span>commcell_object, user_group_name, user_group_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for performing operations for a specific User Group.</p>
<p>Initialise the UserGroup class instance.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the Commcell class</p>
<p>user_group_name
(str)
&ndash;
name of the user group</p>
<p>user_group_id
(str)
&ndash;
id of the user group
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the UserGroup class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/usergroup.py#L722-L1281" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class UserGroup(object):
    &#34;&#34;&#34;Class for performing operations for a specific User Group.&#34;&#34;&#34;

    def __init__(self, commcell_object, user_group_name, user_group_id=None):
        &#34;&#34;&#34;Initialise the UserGroup class instance.

            Args:
                commcell_object     (object)  --  instance of the Commcell class

                user_group_name     (str)     --  name of the user group

                user_group_id       (str)     --  id of the user group
                    default: None

            Returns:
                object - instance of the UserGroup class
        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._user_group_name = user_group_name.lower()

        if user_group_id:
            self._user_group_id = str(user_group_id)
        else:
            self._user_group_id = self._get_usergroup_id()

        self._usergroup = self._commcell_object._services[&#39;USERGROUP&#39;] % (self.user_group_id)

        self._description = None
        self._properties = None
        self._email = None
        self._users = []
        self._usergroups = []
        self._usergroup_status = None
        self._company_id = None
        self._company_name = None
        self._allow_multiple_company_members = False
        self.refresh()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        representation_string = &#39;User Group instance for UserGroup: &#34;{0}&#34;&#39;

        return representation_string.format(
            self.user_group_name
        )

    def _get_usergroup_id(self):
        &#34;&#34;&#34;Gets the user group id associated with this user group.

            Returns:
                str - id associated with this user group
        &#34;&#34;&#34;
        user_groups = UserGroups(self._commcell_object)
        return user_groups.get(self.user_group_name).user_group_id

    def _get_usergroup_properties(self):
        &#34;&#34;&#34;Gets the user group properties of this user group.

            Returns:
                dict - dictionary consisting of the properties of this user group

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;GET&#39;, self._usergroup)

        if flag:
            if response.json() and &#39;userGroups&#39; in response.json():
                self._properties = response.json()[&#39;userGroups&#39;][0]

                if &#39;description&#39; in self._properties:
                    self._description = self._properties[&#39;description&#39;]

                if &#39;enabled&#39; in self._properties:
                    self._usergroup_status = self._properties[&#39;enabled&#39;]

                self._allow_multiple_company_members = self._properties.get(&#39;allowMultiCompanyMembers&#39;, False)

                if &#39;email&#39; in self._properties:
                    self._email = self._properties[&#39;email&#39;]

                self._company_id = self._properties.get(&#39;groupSecurity&#39;, {}).get(&#39;tagWithCompany&#39;, {}).get(&#39;providerId&#39;)
                self._company_name = self._properties.get(&#39;groupSecurity&#39;, {}).get(&#39;tagWithCompany&#39;, {}).get(&#39;providerDomainName&#39;)

                security_properties = self._properties.get(&#39;securityAssociations&#39;, {}).get(
                    &#39;associations&#39;, {})
                self._security_associations = SecurityAssociation.fetch_security_association(
                    security_dict=security_properties)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _has_usergroup(self, usergroup_list):
        &#34;&#34;&#34;checks whether these users are present on this commcell

            Args:
            usergroup_list (list)   --   list of local_usergroup or external user group

            Raises:
                SDKException:
                    if user is not found on this commcell
        &#34;&#34;&#34;
        if usergroup_list is not None:
            for usergroup in usergroup_list:
                if not self._commcell_object.user_groups.has_user_group(usergroup):
                    raise SDKException(
                        &#39;UserGroup&#39;, &#39;102&#39;, &#34;UserGroup {0} doesn&#39;texists on this commcell.&#34;.format(
                            usergroup))

    @property
    def name(self):
        &#34;&#34;&#34;Returns the UserGroup display name&#34;&#34;&#34;
        return self._properties[&#39;userGroupEntity&#39;][&#39;userGroupName&#39;]

    @property
    def user_group_id(self):
        &#34;&#34;&#34;Treats the usergroup id as a read-only attribute.&#34;&#34;&#34;
        return self._user_group_id

    @property
    def user_group_name(self):
        &#34;&#34;&#34;Treats the usergroup name as a read-only attribute.&#34;&#34;&#34;
        return self._user_group_name

    @property
    def description(self):
        &#34;&#34;&#34;Treats the usergroup description as a read-only attribute.&#34;&#34;&#34;
        return self._description

    @property
    def email(self):
        &#34;&#34;&#34;Treats the usergroup email as a read-only attribute.&#34;&#34;&#34;
        return self._email

    @property
    def company_id(self):
        &#34;&#34;&#34;Treats the usergroup company id as a read-only attribute.&#34;&#34;&#34;
        return self._company_id

    @property
    def company_name(self):
        &#34;&#34;&#34;
        Returns:
            str  -  company name to which user group belongs to.
            str  -  empty string, if usergroup belongs to Commcell
        &#34;&#34;&#34;
        return self._company_name

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the UserGroup.&#34;&#34;&#34;
        self._get_usergroup_properties()

    @property
    def status(self):
        &#34;&#34;&#34;Returns the status of user group on this commcell&#34;&#34;&#34;
        return self._usergroup_status

    @status.setter
    def status(self, value):
        &#34;&#34;&#34;Sets the status for this commcell user group&#34;&#34;&#34;

        request_json = {
            &#34;groups&#34;: [{
                &#34;enabled&#34;: value
            }]
        }
        self._update_usergroup_props(request_json)

    @property
    def allow_multiple_company_members(self):
        &#34;&#34;&#34;
        Returns the status of user group on this commcell
        Returns:
            Bool    -   True for allowing multiple company members
                        False otherwise
        &#34;&#34;&#34;
        return self._allow_multiple_company_members

    @allow_multiple_company_members.setter
    def allow_multiple_company_members(self, flag=True):
        &#34;&#34;&#34;
        Allows Multiple Company Members to be part of this commcell user group
        Args:
            flag(bool)      -   True if multiple company members to be allowed,
                                False otherwise
        &#34;&#34;&#34;
        if not isinstance(flag, bool):
            raise SDKException(&#39;UserGroup&#39;, &#39;101&#39;)
        request_json = {&#34;allowMultipleCompanyMembers&#34;: flag}
        self._v4_update_usergroup_props(request_json)

    @property
    def users(self):
        &#34;&#34;&#34;Returns the list of associated users with this usergroup&#34;&#34;&#34;
        users = []
        if &#39;users&#39; in self._properties:
            for user in self._properties[&#39;users&#39;]:
                users.append(user[&#39;userName&#39;])

        return users

    @property
    def usergroups(self):
        &#34;&#34;&#34;Returns the list of associated external usergroups with this usergroup&#34;&#34;&#34;
        user_groups = []
        if &#39;externalUserGroups&#39; in self._properties:
            for user_group in self._properties[&#39;externalUserGroups&#39;]:
                user_groups.append(user_group[&#39;externalGroupName&#39;])

        return user_groups

    @property
    def associations(self):
        &#34;&#34;&#34;Returns security associations present on th usergroup&#34;&#34;&#34;
        return self._security_associations

    @property
    def is_tfa_enabled(self):
        &#34;&#34;&#34;Returns two factor authentication status (True/False)&#34;&#34;&#34;
        return self._properties.get(&#39;enableTwoFactorAuthentication&#39;) == 1

    def enable_tfa(self):
        &#34;&#34;&#34;
        enables two factor authentication on this group

            Note: tfa will not get enabled for this user group if global tfa is disabled

        Returns:
             None
        &#34;&#34;&#34;
        request_json = {
            &#34;groups&#34;: [{
                &#34;enableTwoFactorAuthentication&#34;: 1
            }]
        }
        self._update_usergroup_props(request_json)

    def disable_tfa(self):
        &#34;&#34;&#34;
        disables two factor authentication for this group

        Returns:
            None
        &#34;&#34;&#34;
        request_json = {
            &#34;groups&#34;: [{
                &#34;enableTwoFactorAuthentication&#34;: 0
            }]
        }
        self._update_usergroup_props(request_json)

    def update_security_associations(self, entity_dictionary, request_type):
        &#34;&#34;&#34;handles three way associations (role-usergroup-entities)

            Args:
                entity_dictionary   (dict)      --  combination of entity_type, entity names
                                                    and role
                e.g.: security_dict={
                                &#39;assoc1&#39;:
                                    {
                                        &#39;entity_type&#39;:[&#39;entity_name&#39;],
                                        &#39;entity_type&#39;:[&#39;entity_name&#39;, &#39;entity_name&#39;],
                                        &#39;role&#39;: [&#39;role1&#39;]
                                    },
                                &#39;assoc2&#39;:
                                    {
                                        &#39;mediaAgentName&#39;: [&#39;networktestcs&#39;, &#39;standbycs&#39;],
                                        &#39;clientName&#39;: [&#39;Linux1&#39;],
                                        &#39;role&#39;: [&#39;New1&#39;]
                                        }
                                    }

                entity_type         --      key for the entity present in dictionary
                                            on which user will have access

                entity_name         --      Value of the key

                role                --      key for role name you specify

                e.g.: {&#34;clientName&#34;:&#34;Linux1&#34;}
                Entity Types are:   clientName, mediaAgentName, libraryName, userName,
                                    userGroupName, storagePolicyName, clientGroupName,
                                    schedulePolicyName, locationName, providerDomainName,
                                    alertName, workflowName, policyName, roleName

                entity_name:        client name for entity_type &#39;clientName&#39;
                                    Media agent name for entitytype &#39;mediaAgentName&#39;
                                    similar for other entity_types

                request_type        --      decides whether to UPDATE, DELETE or
                                            OVERWRITE user security association.

            Raises:
                SDKException:

                    if failed update user properties

        &#34;&#34;&#34;
        security_request = {}
        if entity_dictionary:
            security_request = SecurityAssociation._security_association_json(entity_dictionary)

        self._send_request(request_type, association_blob=security_request)

    def update_usergroup_members(
            self,
            request_type,
            users_list=None,
            external_usergroups=None,
            local_usergroups=None):
        &#34;&#34;&#34;updates users and usergroups to local usergroup members tab
            Args:
                request_type (str)              --      decides whether to UPDATE, DELETE or
                                                        OVERWRITE user security association

                users_list  (list)              --      comlete list of local users and
                                                        externalusers
                e.g : users_list = [r&#39;Red\\RedUser2&#39;, r&#39;Red\\RedUser12&#39;, r&#39;mirje-pc\\A&#39;,
                                    r&#39;mirje-pc\\B&#39;, r&#39;John&#39;, r&#39;Prasad&#39;, r&#39;Mahesh&#39;]
                where:
                RedUser2, RedUser12 are belongs to AD &#39;Red&#39;
                A, B are belongs to AD &#39;mirje-pc&#39;
                John, Prasad, Mahesh are local users

                external_usergroups (list)      --      complete list of external usergroup only

                e.g : external_usergroups_list = [&#39;Red\\RedGroup2&#39;, &#39;mirje-pc\\XYZ&#39;]
                where:
                RedGroup2 is external user group present in AD &#39;Red&#39;
                XYZ is external user group present in AD &#39;mirje-pc&#39;

                local_usergroups (list) --  complete list of local user groups
                                            (Not required when updating external
                                            usergroup properties)
                e.g : local_usergroups=[&#39;usergroup1&#39;, &#39;usegrouop2&#39;]


            Raises:
                SDKException:

                    if failed update local usergroup properties
        &#34;&#34;&#34;
        if users_list is not None:
            for user in users_list:
                if not self._commcell_object.users.has_user(user):
                    raise SDKException(
                        &#39;User&#39;, &#39;102&#39;, &#34;User {0} doesn&#39;t exists on this commcell.&#34;.format(user))
            userlist_json = [{&#34;userName&#34;: xuser} for xuser in users_list]
        else:
            userlist_json = []

        if external_usergroups is not None:
            self._has_usergroup(external_usergroups)
            usergroup_json = [{&#34;userGroupName&#34;: name} for name in external_usergroups]
        else:
            usergroup_json = []

        if local_usergroups is not None:
            self._has_usergroup(local_usergroups)
            local_groups_json = [{&#34;userGroupName&#34;: user_name} for user_name in local_usergroups]
        else:
            local_groups_json = []

        self._send_request(request_type=request_type, users_blob=userlist_json,
                           external_group_blob=usergroup_json, local_group_blob=local_groups_json)

    def update_navigation_preferences(self, include_navigation_list):
        &#34;&#34;&#34;Updates the user group&#39;s include navigation preferences with the the list provided

            Args:
                include_navigation_list   (list)    --  list of navigation items to be seen in command center

            Raises:
                SDKException:

                    if failed update user properties

        &#34;&#34;&#34;
        request_json = {
            &#39;groups&#39;: [
                {
                    &#39;additionalSettings&#39;: [
                        {
                            &#39;deleted&#39;: 0,
                            &#39;relativepath&#39;: &#39;CommServDB.AdminConsole&#39;,
                            &#39;keyName&#39;: &#39;includeNavItems&#39;,
                            &#39;type&#39;: &#39;MULTISTRING&#39;,
                            &#39;value&#39;: &#39;,&#39;.join(include_navigation_list),
                            &#39;enabled&#39;: 1
                        }
                    ]
                }
            ]
        }
        self._update_usergroup_props(request_json)

    def _send_request(self, request_type, association_blob=None, users_blob=None,
                      external_group_blob=None, local_group_blob=None):
        &#34;&#34;&#34;forms complete json request for user groups

            Args:
                request_type        (str)   --  decides whether to UPDATE, DELETE or
                                                OVERWRITE user security association

                association_blob    (dict)  --  security association blob generated from
                                                static method _security_association_json
                                                present in SecurityAssociation

                users_blob          (dict)  --  comlete json blob of local users and
                                                externalusers

                external_group_blob (dict)  --  complete json blob of external
                                                usergroup only

                local_group_blob    (list)  --  complete json blob of local
                                                usergroup only

            Raises:
                SDKException:

                    if failed update local usergroup properties

                    if response received is empty

                    if response is not success
        &#34;&#34;&#34;
        update_usergroup_request = {
            &#34;NONE&#34;: 0,
            &#34;OVERWRITE&#34;: 1,
            &#34;UPDATE&#34;: 2,
            &#34;DELETE&#34;: 3,
        }
        if local_group_blob is None:
            local_group_blob = []

        if association_blob:
            security_association_request = {
                &#34;associationsOperationType&#34;: update_usergroup_request[request_type.upper()],
                &#34;associations&#34;: association_blob
            }
        else:
            security_association_request = {}

        if users_blob is None:
            users_blob = []

        group_json = {
            &#34;localUserGroupsOperationType&#34;: update_usergroup_request[request_type.upper()],
            &#34;usersOperationType&#34;: update_usergroup_request[request_type.upper()],
            &#34;externalUserGroupsOperationType&#34;: update_usergroup_request[request_type.upper()],
            &#34;securityAssociations&#34;: security_association_request,
            &#34;localUserGroups&#34;: local_group_blob,
            &#34;users&#34;: users_blob
        }
        if external_group_blob is not None:
            group_json.update({&#34;associatedExternalUserGroups&#34;: external_group_blob})

        request_json = {
            &#34;groups&#34;: [group_json]
        }

        self._update_usergroup_props(request_json)

    def _v4_update_usergroup_props(self, properties_dict):
        &#34;&#34;&#34;Updates the properties of this usergroup

            Args:
                properties_dict (dict)  --  user property dict which is to be updated

            Raises:
                SDKException:
                    if arguments passed are of incorrect types
                    if failed to update user group properties
                    if response is empty
                    if response is not success
        &#34;&#34;&#34;
        if not isinstance(properties_dict, dict):
            raise SDKException(&#39;UserGroup&#39;, &#39;101&#39;)
        usergroup_request = self._commcell_object._services[&#39;USERGROUP_V4&#39;] % (self._user_group_id)

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;PUT&#39;, usergroup_request, properties_dict
        )
        if flag:
            if response.json():
                error_code = response.json().get(&#39;errorCode&#39;, 0)
                if error_code != 0:
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))
                self.refresh()
                return
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    def _update_usergroup_props(self, properties_dict):
        &#34;&#34;&#34;Updates the properties of this usergroup

            Args:
                properties_dict (dict)  --  user property dict which is to be updated

            Raises:
                SDKException:
                    if failed update usergroup properties

                    if response is not success
        &#34;&#34;&#34;
        usergroup_request = self._commcell_object._services[&#39;USERGROUP&#39;] % (self._user_group_id)

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, usergroup_request, properties_dict
        )
        if flag:
            if response.json():
                if &#39;response&#39; in response.json():
                    response_json = response.json()[&#39;response&#39;][0]
                    error_code = response_json[&#39;errorCode&#39;]
                    error_message = response_json[&#39;errorString&#39;]
                    if not error_code == 0:
                        raise SDKException(&#39;Response&#39;, &#39;101&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(
                response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        self.refresh()

    def available_users_for_group(self):
        &#34;&#34;&#34;Returns the dict of all the users on the commcell, that can be added to this group

        dict of all the users available for adding to group
                   {
                        &#34;user1&#34;: {
                            &#34;id&#34;: ...,
                            &#34;name&#34;: ...,
                            &#34;GUID&#34;: ...,
                            ..
                        }
                   }
        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._commcell_object._services[&#39;V4_USERS&#39;] + f&#39;&amp;groupContext={self.user_group_id}&#39;
        )

        if flag:
            if response.json():
                users_dict = {}
                for user in response.json().get(&#39;users&#39;, []):
                    users_dict[user.get(&#39;name&#39;)] = user
                return users_dict
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.security.usergroup.UserGroup.allow_multiple_company_members"><code class="name">var <span class="ident">allow_multiple_company_members</span></code></dt>
<dd>
<div class="desc"><p>Returns the status of user group on this commcell</p>
<h2 id="returns">Returns</h2>
<p>Bool
-
True for allowing multiple company members
False otherwise</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/usergroup.py#L895-L903" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def allow_multiple_company_members(self):
    &#34;&#34;&#34;
    Returns the status of user group on this commcell
    Returns:
        Bool    -   True for allowing multiple company members
                    False otherwise
    &#34;&#34;&#34;
    return self._allow_multiple_company_members</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.usergroup.UserGroup.associations"><code class="name">var <span class="ident">associations</span></code></dt>
<dd>
<div class="desc"><p>Returns security associations present on th usergroup</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/usergroup.py#L938-L941" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def associations(self):
    &#34;&#34;&#34;Returns security associations present on th usergroup&#34;&#34;&#34;
    return self._security_associations</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.usergroup.UserGroup.company_id"><code class="name">var <span class="ident">company_id</span></code></dt>
<dd>
<div class="desc"><p>Treats the usergroup company id as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/usergroup.py#L861-L864" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def company_id(self):
    &#34;&#34;&#34;Treats the usergroup company id as a read-only attribute.&#34;&#34;&#34;
    return self._company_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.usergroup.UserGroup.company_name"><code class="name">var <span class="ident">company_name</span></code></dt>
<dd>
<div class="desc"><h2 id="returns">Returns</h2>
<p>str
-
company name to which user group belongs to.
str
-
empty string, if usergroup belongs to Commcell</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/usergroup.py#L866-L873" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def company_name(self):
    &#34;&#34;&#34;
    Returns:
        str  -  company name to which user group belongs to.
        str  -  empty string, if usergroup belongs to Commcell
    &#34;&#34;&#34;
    return self._company_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.usergroup.UserGroup.description"><code class="name">var <span class="ident">description</span></code></dt>
<dd>
<div class="desc"><p>Treats the usergroup description as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/usergroup.py#L851-L854" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def description(self):
    &#34;&#34;&#34;Treats the usergroup description as a read-only attribute.&#34;&#34;&#34;
    return self._description</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.usergroup.UserGroup.email"><code class="name">var <span class="ident">email</span></code></dt>
<dd>
<div class="desc"><p>Treats the usergroup email as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/usergroup.py#L856-L859" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def email(self):
    &#34;&#34;&#34;Treats the usergroup email as a read-only attribute.&#34;&#34;&#34;
    return self._email</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.usergroup.UserGroup.is_tfa_enabled"><code class="name">var <span class="ident">is_tfa_enabled</span></code></dt>
<dd>
<div class="desc"><p>Returns two factor authentication status (True/False)</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/usergroup.py#L943-L946" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_tfa_enabled(self):
    &#34;&#34;&#34;Returns two factor authentication status (True/False)&#34;&#34;&#34;
    return self._properties.get(&#39;enableTwoFactorAuthentication&#39;) == 1</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.usergroup.UserGroup.name"><code class="name">var <span class="ident">name</span></code></dt>
<dd>
<div class="desc"><p>Returns the UserGroup display name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/usergroup.py#L836-L839" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def name(self):
    &#34;&#34;&#34;Returns the UserGroup display name&#34;&#34;&#34;
    return self._properties[&#39;userGroupEntity&#39;][&#39;userGroupName&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.usergroup.UserGroup.status"><code class="name">var <span class="ident">status</span></code></dt>
<dd>
<div class="desc"><p>Returns the status of user group on this commcell</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/usergroup.py#L879-L882" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def status(self):
    &#34;&#34;&#34;Returns the status of user group on this commcell&#34;&#34;&#34;
    return self._usergroup_status</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.usergroup.UserGroup.user_group_id"><code class="name">var <span class="ident">user_group_id</span></code></dt>
<dd>
<div class="desc"><p>Treats the usergroup id as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/usergroup.py#L841-L844" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def user_group_id(self):
    &#34;&#34;&#34;Treats the usergroup id as a read-only attribute.&#34;&#34;&#34;
    return self._user_group_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.usergroup.UserGroup.user_group_name"><code class="name">var <span class="ident">user_group_name</span></code></dt>
<dd>
<div class="desc"><p>Treats the usergroup name as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/usergroup.py#L846-L849" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def user_group_name(self):
    &#34;&#34;&#34;Treats the usergroup name as a read-only attribute.&#34;&#34;&#34;
    return self._user_group_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.usergroup.UserGroup.usergroups"><code class="name">var <span class="ident">usergroups</span></code></dt>
<dd>
<div class="desc"><p>Returns the list of associated external usergroups with this usergroup</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/usergroup.py#L928-L936" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def usergroups(self):
    &#34;&#34;&#34;Returns the list of associated external usergroups with this usergroup&#34;&#34;&#34;
    user_groups = []
    if &#39;externalUserGroups&#39; in self._properties:
        for user_group in self._properties[&#39;externalUserGroups&#39;]:
            user_groups.append(user_group[&#39;externalGroupName&#39;])

    return user_groups</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.usergroup.UserGroup.users"><code class="name">var <span class="ident">users</span></code></dt>
<dd>
<div class="desc"><p>Returns the list of associated users with this usergroup</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/usergroup.py#L918-L926" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def users(self):
    &#34;&#34;&#34;Returns the list of associated users with this usergroup&#34;&#34;&#34;
    users = []
    if &#39;users&#39; in self._properties:
        for user in self._properties[&#39;users&#39;]:
            users.append(user[&#39;userName&#39;])

    return users</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.security.usergroup.UserGroup.available_users_for_group"><code class="name flex">
<span>def <span class="ident">available_users_for_group</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the dict of all the users on the commcell, that can be added to this group</p>
<p>dict of all the users available for adding to group
{
"user1": {
"id": &hellip;,
"name": &hellip;,
"GUID": &hellip;,
..
}
}</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/usergroup.py#L1254-L1281" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def available_users_for_group(self):
    &#34;&#34;&#34;Returns the dict of all the users on the commcell, that can be added to this group

    dict of all the users available for adding to group
               {
                    &#34;user1&#34;: {
                        &#34;id&#34;: ...,
                        &#34;name&#34;: ...,
                        &#34;GUID&#34;: ...,
                        ..
                    }
               }
    &#34;&#34;&#34;
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;GET&#39;, self._commcell_object._services[&#39;V4_USERS&#39;] + f&#39;&amp;groupContext={self.user_group_id}&#39;
    )

    if flag:
        if response.json():
            users_dict = {}
            for user in response.json().get(&#39;users&#39;, []):
                users_dict[user.get(&#39;name&#39;)] = user
            return users_dict
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.usergroup.UserGroup.disable_tfa"><code class="name flex">
<span>def <span class="ident">disable_tfa</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>disables two factor authentication for this group</p>
<h2 id="returns">Returns</h2>
<p>None</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/usergroup.py#L964-L976" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def disable_tfa(self):
    &#34;&#34;&#34;
    disables two factor authentication for this group

    Returns:
        None
    &#34;&#34;&#34;
    request_json = {
        &#34;groups&#34;: [{
            &#34;enableTwoFactorAuthentication&#34;: 0
        }]
    }
    self._update_usergroup_props(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.usergroup.UserGroup.enable_tfa"><code class="name flex">
<span>def <span class="ident">enable_tfa</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>enables two factor authentication on this group</p>
<pre><code>Note: tfa will not get enabled for this user group if global tfa is disabled
</code></pre>
<h2 id="returns">Returns</h2>
<p>None</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/usergroup.py#L948-L962" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def enable_tfa(self):
    &#34;&#34;&#34;
    enables two factor authentication on this group

        Note: tfa will not get enabled for this user group if global tfa is disabled

    Returns:
         None
    &#34;&#34;&#34;
    request_json = {
        &#34;groups&#34;: [{
            &#34;enableTwoFactorAuthentication&#34;: 1
        }]
    }
    self._update_usergroup_props(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.usergroup.UserGroup.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the properties of the UserGroup.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/usergroup.py#L875-L877" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the properties of the UserGroup.&#34;&#34;&#34;
    self._get_usergroup_properties()</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.usergroup.UserGroup.update_navigation_preferences"><code class="name flex">
<span>def <span class="ident">update_navigation_preferences</span></span>(<span>self, include_navigation_list)</span>
</code></dt>
<dd>
<div class="desc"><p>Updates the user group's include navigation preferences with the the list provided</p>
<h2 id="args">Args</h2>
<p>include_navigation_list
(list)
&ndash;
list of navigation items to be seen in command center</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if failed update user properties
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/usergroup.py#L1093-L1121" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def update_navigation_preferences(self, include_navigation_list):
    &#34;&#34;&#34;Updates the user group&#39;s include navigation preferences with the the list provided

        Args:
            include_navigation_list   (list)    --  list of navigation items to be seen in command center

        Raises:
            SDKException:

                if failed update user properties

    &#34;&#34;&#34;
    request_json = {
        &#39;groups&#39;: [
            {
                &#39;additionalSettings&#39;: [
                    {
                        &#39;deleted&#39;: 0,
                        &#39;relativepath&#39;: &#39;CommServDB.AdminConsole&#39;,
                        &#39;keyName&#39;: &#39;includeNavItems&#39;,
                        &#39;type&#39;: &#39;MULTISTRING&#39;,
                        &#39;value&#39;: &#39;,&#39;.join(include_navigation_list),
                        &#39;enabled&#39;: 1
                    }
                ]
            }
        ]
    }
    self._update_usergroup_props(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.usergroup.UserGroup.update_security_associations"><code class="name flex">
<span>def <span class="ident">update_security_associations</span></span>(<span>self, entity_dictionary, request_type)</span>
</code></dt>
<dd>
<div class="desc"><p>handles three way associations (role-usergroup-entities)</p>
<h2 id="args">Args</h2>
<p>entity_dictionary
(dict)
&ndash;
combination of entity_type, entity names
and role
e.g.: security_dict={
'assoc1':
{
'entity_type':['entity_name'],
'entity_type':['entity_name', 'entity_name'],
'role': ['role1']
},
'assoc2':
{
'mediaAgentName': ['networktestcs', 'standbycs'],
'clientName': ['Linux1'],
'role': ['New1']
}
}</p>
<p>entity_type
&ndash;
key for the entity present in dictionary
on which user will have access</p>
<p>entity_name
&ndash;
Value of the key</p>
<p>role
&ndash;
key for role name you specify</p>
<p>e.g.: {"clientName":"Linux1"}
Entity Types are:
clientName, mediaAgentName, libraryName, userName,
userGroupName, storagePolicyName, clientGroupName,
schedulePolicyName, locationName, providerDomainName,
alertName, workflowName, policyName, roleName</p>
<dl>
<dt><strong><code>entity_name</code></strong></dt>
<dd>
<p>client name for entity_type 'clientName'
Media agent name for entitytype 'mediaAgentName'
similar for other entity_types</p>
</dd>
</dl>
<p>request_type
&ndash;
decides whether to UPDATE, DELETE or
OVERWRITE user security association.</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if failed update user properties
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/usergroup.py#L978-L1029" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def update_security_associations(self, entity_dictionary, request_type):
    &#34;&#34;&#34;handles three way associations (role-usergroup-entities)

        Args:
            entity_dictionary   (dict)      --  combination of entity_type, entity names
                                                and role
            e.g.: security_dict={
                            &#39;assoc1&#39;:
                                {
                                    &#39;entity_type&#39;:[&#39;entity_name&#39;],
                                    &#39;entity_type&#39;:[&#39;entity_name&#39;, &#39;entity_name&#39;],
                                    &#39;role&#39;: [&#39;role1&#39;]
                                },
                            &#39;assoc2&#39;:
                                {
                                    &#39;mediaAgentName&#39;: [&#39;networktestcs&#39;, &#39;standbycs&#39;],
                                    &#39;clientName&#39;: [&#39;Linux1&#39;],
                                    &#39;role&#39;: [&#39;New1&#39;]
                                    }
                                }

            entity_type         --      key for the entity present in dictionary
                                        on which user will have access

            entity_name         --      Value of the key

            role                --      key for role name you specify

            e.g.: {&#34;clientName&#34;:&#34;Linux1&#34;}
            Entity Types are:   clientName, mediaAgentName, libraryName, userName,
                                userGroupName, storagePolicyName, clientGroupName,
                                schedulePolicyName, locationName, providerDomainName,
                                alertName, workflowName, policyName, roleName

            entity_name:        client name for entity_type &#39;clientName&#39;
                                Media agent name for entitytype &#39;mediaAgentName&#39;
                                similar for other entity_types

            request_type        --      decides whether to UPDATE, DELETE or
                                        OVERWRITE user security association.

        Raises:
            SDKException:

                if failed update user properties

    &#34;&#34;&#34;
    security_request = {}
    if entity_dictionary:
        security_request = SecurityAssociation._security_association_json(entity_dictionary)

    self._send_request(request_type, association_blob=security_request)</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.usergroup.UserGroup.update_usergroup_members"><code class="name flex">
<span>def <span class="ident">update_usergroup_members</span></span>(<span>self, request_type, users_list=None, external_usergroups=None, local_usergroups=None)</span>
</code></dt>
<dd>
<div class="desc"><p>updates users and usergroups to local usergroup members tab</p>
<h2 id="args">Args</h2>
<p>request_type (str)
&ndash;
decides whether to UPDATE, DELETE or
OVERWRITE user security association</p>
<p>users_list
(list)
&ndash;
comlete list of local users and
externalusers
e.g : users_list = [r'Red\RedUser2', r'Red\RedUser12', r'mirje-pc\A',
r'mirje-pc\B', r'John', r'Prasad', r'Mahesh']
where:
RedUser2, RedUser12 are belongs to AD 'Red'
A, B are belongs to AD 'mirje-pc'
John, Prasad, Mahesh are local users</p>
<p>external_usergroups (list)
&ndash;
complete list of external usergroup only</p>
<p>e.g : external_usergroups_list = ['Red\RedGroup2', 'mirje-pc\XYZ']
where:
RedGroup2 is external user group present in AD 'Red'
XYZ is external user group present in AD 'mirje-pc'</p>
<p>local_usergroups (list) &ndash;
complete list of local user groups
(Not required when updating external
usergroup properties)
e.g : local_usergroups=['usergroup1', 'usegrouop2']</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if failed update local usergroup properties
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/usergroup.py#L1031-L1091" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def update_usergroup_members(
        self,
        request_type,
        users_list=None,
        external_usergroups=None,
        local_usergroups=None):
    &#34;&#34;&#34;updates users and usergroups to local usergroup members tab
        Args:
            request_type (str)              --      decides whether to UPDATE, DELETE or
                                                    OVERWRITE user security association

            users_list  (list)              --      comlete list of local users and
                                                    externalusers
            e.g : users_list = [r&#39;Red\\RedUser2&#39;, r&#39;Red\\RedUser12&#39;, r&#39;mirje-pc\\A&#39;,
                                r&#39;mirje-pc\\B&#39;, r&#39;John&#39;, r&#39;Prasad&#39;, r&#39;Mahesh&#39;]
            where:
            RedUser2, RedUser12 are belongs to AD &#39;Red&#39;
            A, B are belongs to AD &#39;mirje-pc&#39;
            John, Prasad, Mahesh are local users

            external_usergroups (list)      --      complete list of external usergroup only

            e.g : external_usergroups_list = [&#39;Red\\RedGroup2&#39;, &#39;mirje-pc\\XYZ&#39;]
            where:
            RedGroup2 is external user group present in AD &#39;Red&#39;
            XYZ is external user group present in AD &#39;mirje-pc&#39;

            local_usergroups (list) --  complete list of local user groups
                                        (Not required when updating external
                                        usergroup properties)
            e.g : local_usergroups=[&#39;usergroup1&#39;, &#39;usegrouop2&#39;]


        Raises:
            SDKException:

                if failed update local usergroup properties
    &#34;&#34;&#34;
    if users_list is not None:
        for user in users_list:
            if not self._commcell_object.users.has_user(user):
                raise SDKException(
                    &#39;User&#39;, &#39;102&#39;, &#34;User {0} doesn&#39;t exists on this commcell.&#34;.format(user))
        userlist_json = [{&#34;userName&#34;: xuser} for xuser in users_list]
    else:
        userlist_json = []

    if external_usergroups is not None:
        self._has_usergroup(external_usergroups)
        usergroup_json = [{&#34;userGroupName&#34;: name} for name in external_usergroups]
    else:
        usergroup_json = []

    if local_usergroups is not None:
        self._has_usergroup(local_usergroups)
        local_groups_json = [{&#34;userGroupName&#34;: user_name} for user_name in local_usergroups]
    else:
        local_groups_json = []

    self._send_request(request_type=request_type, users_blob=userlist_json,
                       external_group_blob=usergroup_json, local_group_blob=local_groups_json)</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.security.usergroup.UserGroups"><code class="flex name class">
<span>class <span class="ident">UserGroups</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for getting all the usergroups associated with a commcell.</p>
<p>Initialize object of the UserGroups class.</p>
<h2 id="args">Args</h2>
<p>commcell_object (object)
&ndash;
instance of the Commcell class</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the UserGroups class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/usergroup.py#L139-L719" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class UserGroups(object):
    &#34;&#34;&#34;Class for getting all the usergroups associated with a commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the UserGroups class.

            Args:
                commcell_object (object)  --  instance of the Commcell class

            Returns:
                object - instance of the UserGroups class

        &#34;&#34;&#34;
        self._all_usergroups_prop = None
        self._commcell_object = commcell_object
        self._user_group = self._commcell_object._services[&#39;USERGROUPS&#39;]
        self._user_groups_cache = None
        self._user_groups = None
        self.filter_query_count = 0
        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all usergroups of the Commcell.

            Returns:
                str - string of all the usergroups for a commcell
        &#34;&#34;&#34;
        representation_string = &#34;{:^5}\t{:^50}\n\n&#34;.format(&#39;S. No.&#39;, &#39;User Group&#39;)

        for index, user_group in enumerate(self._user_groups):
            sub_str = &#39;{:^5}\t{:30}\n&#39;.format(index + 1, user_group)
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the UserGroups class.&#34;&#34;&#34;
        return &#34;UserGroups class instance for Commcell&#34;

    def _get_user_groups(self, system_created=True, full_response: bool = False):
        &#34;&#34;&#34;Gets all the user groups associated with the commcell

            Args:
                system_created  (bool) --   flag to include system created user groups
                full_response   (bool) --  flag to return complete response

            Returns:
                dict - consists of all user group in the commcell
                    {
                         &#34;user_group1_name&#34;: user_group1_id,
                         &#34;user_group2_name&#34;: user_group2_id
                    }

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        request_url = f&#39;{self._user_group % str(system_created).lower()}&amp;level=10&#39;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, request_url
        )

        if flag:
            if response.json() and &#39;userGroups&#39; in response.json():
                if full_response:
                    return response.json()
                self._all_usergroups_prop = response.json()[&#39;userGroups&#39;]
                user_groups_dict = {}

                for temp in self._all_usergroups_prop:
                    temp_name = temp[&#39;userGroupEntity&#39;][&#39;userGroupName&#39;].lower()
                    temp_id = str(temp[&#39;userGroupEntity&#39;][&#39;userGroupId&#39;]).lower()
                    user_groups_dict[temp_name] = temp_id

                return user_groups_dict
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def _get_fl_parameters(self, fl: list = None) -&gt; str:
        &#34;&#34;&#34;
        Returns the fl parameters to be passed in the mongodb caching api call

        Args:
            fl    (list)  --   list of columns to be passed in API request

        Returns:
            fl_parameters(str) -- fl parameter string
        &#34;&#34;&#34;
        self.valid_columns = {
            &#39;groupName&#39;: &#39;userGroups.userGroupEntity.userGroupName&#39;,
            &#39;groupId&#39;: &#39;userGroups.userGroupEntity.userGroupId&#39;,
            &#39;description&#39;: &#39;userGroups.description&#39;,
            &#39;status&#39;: &#39;userGroups.enabled&#39;,
            &#39;company&#39;: &#39;companyName&#39;
        }
        default_columns = &#39;userGroups.userGroupEntity.userGroupName&#39;

        if fl:
            if all(col in self.valid_columns for col in fl):
                fl_parameters = f&#34;&amp;fl={default_columns},{&#39;,&#39;.join(self.valid_columns[column] for column in fl)}&#34;
            else:
                raise SDKException(&#39;UserGroup&#39;, &#39;102&#39;, &#39;Invalid column name passed&#39;)
        else:
            fl_parameters = f&#34;&amp;fl={default_columns},{&#39;,&#39;.join(column for column in self.valid_columns.values())}&#34;

        return fl_parameters

    def _get_sort_parameters(self, sort: list = None) -&gt; str:
        &#34;&#34;&#34;
        Returns the sort parameters to be passed in the mongodb caching api call

        Args:
            sort  (list)  --   contains the name of the column on which sorting will be performed and type of sort
                                valid sor type -- 1 for ascending and -1 for descending
                                e.g. sort = [&#39;connectName&#39;,&#39;1&#39;]

        Returns:
            sort_parameters(str) -- sort parameter string
        &#34;&#34;&#34;
        sort_type = str(sort[1])
        col = sort[0]
        if col in self.valid_columns.keys() and sort_type in [&#39;1&#39;, &#39;-1&#39;]:
            sort_parameter = &#39;&amp;sort=&#39; + self.valid_columns[col] + &#39;:&#39; + sort_type
        else:
            raise SDKException(&#39;UserGroup&#39;, &#39;102&#39;, &#39;Invalid column name passed&#39;)
        return sort_parameter

    def _get_fq_parameters(self, fq: list = None) -&gt; str:
        &#34;&#34;&#34;
        Returns the fq parameters based on the fq list passed
        Args:
             fq     (list) --   contains the columnName, condition and value
                    e.g. fq = [[&#39;groupName&#39;,&#39;contains&#39;, test&#39;][&#39;status&#39;,&#39;eq&#39;,&#39;Enabled&#39;]]

        Returns:
            fq_parameters(str) -- fq parameter string
        &#34;&#34;&#34;
        conditions = {&#34;contains&#34;, &#34;notContain&#34;, &#34;eq&#34;, &#34;neq&#34;}
        params = []

        for column, condition, *value in fq or []:
            if column not in self.valid_columns:
                raise SDKException(&#39;UserGroup&#39;, &#39;102&#39;, &#39;Invalid column name passed&#39;)

            if condition in conditions:
                params.append(f&#34;&amp;fq={self.valid_columns[column]}:{condition.lower()}:{value[0]}&#34;)
            elif condition == &#34;isEmpty&#34; and not value:
                params.append(f&#34;&amp;fq={self.valid_columns[column]}:in:null,&#34;)
            else:
                raise SDKException(&#39;UserGroup&#39;, &#39;102&#39;, &#39;Invalid condition passed&#39;)

        return &#34;&#34;.join(params)

    def get_user_groups_cache(self, hard: bool = False, **kwargs) -&gt; dict:
        &#34;&#34;&#34;
        Gets all the user groups present in CommcellEntityCache DB.

        Args:
            hard  (bool)          --   Flag to perform hard refresh on user groups cache.
            **kwargs (dict):
                fl (list)         --   List of columns to return in response (default: None).
                sort (list)       --   Contains the name of the column on which sorting will be performed and type of sort.
                                            Valid sort type: 1 for ascending and -1 for descending
                                            e.g. sort = [&#39;columnName&#39;, &#39;1&#39;] (default: None).
                limit (list)      --   Contains the start and limit parameter value.
                                            Default [&#39;0&#39;, &#39;100&#39;].
                search (str)      --   Contains the string to search in the commcell entity cache (default: None).
                fq (list)         --   Contains the columnName, condition and value.
                                            e.g. fq = [[&#39;groupName&#39;, &#39;contains&#39;, &#39;test&#39;],
                                            [&#39;status&#39;, &#39;eq&#39;, &#39;Enabled&#39;]] (default: None).

        Returns:
            dict: Dictionary of all the properties present in response.
        &#34;&#34;&#34;
        # computing params
        fl_parameters = self._get_fl_parameters(kwargs.get(&#39;fl&#39;, None))
        fq_parameters = self._get_fq_parameters(kwargs.get(&#39;fq&#39;, None))
        limit = kwargs.get(&#39;limit&#39;, None)
        limit_parameters = f&#39;start={limit[0]}&amp;limit={limit[1]}&#39; if limit else &#39;&#39;
        hard_refresh = &#39;&amp;hardRefresh=true&#39; if hard else &#39;&#39;
        sort_parameters = self._get_sort_parameters(kwargs.get(&#39;sort&#39;, None)) if kwargs.get(&#39;sort&#39;, None) else &#39;&#39;

        # Search operation can only be performed on limited columns, so filtering out the columns on which search works
        searchable_columns = [&#34;groupName&#34;,&#34;description&#34;,&#34;company&#34;]
        search_parameter = (f&#39;&amp;search={&#34;,&#34;.join(self.valid_columns[col] for col in searchable_columns)}:contains:&#39;
                            f&#39;{kwargs.get(&#34;search&#34;, None)}&#39;) if kwargs.get(&#39;search&#39;, None) else &#39;&#39;

        params = [
            limit_parameters,
            sort_parameters,
            fl_parameters,
            hard_refresh,
            search_parameter,
            fq_parameters
        ]
        request_url = f&#34;{self._commcell_object._services[&#39;USERGROUPS&#39;]}?&#34; + &#34;&#34;.join(params)
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#34;GET&#34;, request_url)
        if not flag:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        user_groups_cache = {}
        if response.json() and &#39;userGroups&#39; in response.json():
            self.filter_query_count = response.json().get(&#39;filterQueryCount&#39;,0)
            for group in response.json()[&#39;userGroups&#39;]:
                name = group.get(&#39;userGroupEntity&#39;, {}).get(&#39;userGroupName&#39;)
                user_groups_config = {
                    &#39;groupName&#39;:name,
                    &#39;groupId&#39;: group.get(&#39;userGroupEntity&#39;, {}).get(&#39;userGroupId&#39;),
                    &#39;description&#39;: group.get(&#39;description&#39;, &#39;&#39;),
                    &#39;status&#39;: group.get(&#39;enabled&#39;),
                    &#39;company&#39;: group.get(&#39;userGroupEntity&#39;, {}).get(&#39;entityInfo&#39;, {}).get(&#39;companyName&#39;)
                }
                if self._commcell_object.is_global_scope():
                    user_groups_config[&#39;commcell&#39;] = group.get(&#39;userGroupEntity&#39;, {}).get(&#39;entityInfo&#39;, {}).get(&#39;multiCommcellName&#39;,&#39;&#39;)

                    # Handle duplicate names for different commcells
                    unique_name = name
                    i = 1
                    while unique_name in user_groups_cache:
                        existing_user = user_groups_cache[unique_name]
                        if existing_user.get(&#39;commcell&#39;) != user_groups_config.get(&#39;commcell&#39;):
                            unique_name = f&#34;{name}__{i}&#34;
                            i += 1
                        else:
                            break
                    user_groups_cache[unique_name] = user_groups_config
                else:
                    user_groups_cache[name] = user_groups_config

            return user_groups_cache
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)

    @property
    def all_user_groups_cache(self) -&gt; dict:
        &#34;&#34;&#34;Returns dict of all the user groups and their info present in CommcellEntityCache in mongoDB

            dict - consists of all user groups of the in the CommcellEntityCache
                    {
                         &#34;userGroup1_name&#34;: {
                                &#39;id&#39;: userGroup1_id ,
                                &#39;description&#39;: userGroup1_description,
                                &#39;status&#39;: userGroup1_status,
                                &#39;company&#39;: userGroup1_company
                                },
                         &#34;userGroup2_name&#34;: {
                                &#39;id&#39;: userGroup2_id ,
                                &#39;description&#39;: userGroup2_description,
                                &#39;status&#39;: userGroup2_status,
                                &#39;company&#39;: userGroup2_company
                                }
                    }
        &#34;&#34;&#34;
        if not self._user_groups_cache:
            self._user_groups_cache = self.get_user_groups_cache()
        return self._user_groups_cache

    def has_user_group(self, user_group_name):
        &#34;&#34;&#34;Checks if a user group exists in the commcell with the input user group name.

            Args:
                user_group_name (str)  --  name of the user group

            Returns:
                bool - boolean output whether the user group exists in the commcell
                       or not

            Raises:
                SDKException:
                    if type of the user group name argument is not string
        &#34;&#34;&#34;
        if not isinstance(user_group_name, str):
            raise SDKException(&#39;UserGroup&#39;, &#39;101&#39;)

        return self._user_groups and user_group_name.lower() in self._user_groups

    def get(self, user_group_name):
        &#34;&#34;&#34;Returns a user group object of the specified user group name.

            Args:
                user_group_name (str)  --  name of the user group

            Returns:
                object - instance of the UserGroup class for the given user group name

            Raises:
                SDKException:
                    if type of the user group name argument is not string

                    if no user group exists with the given name
        &#34;&#34;&#34;
        if not isinstance(user_group_name, str):
            raise SDKException(&#39;UserGroup&#39;, &#39;101&#39;)
        else:
            user_group_name = user_group_name.lower()

            if self.has_user_group(user_group_name):
                return UserGroup(self._commcell_object, user_group_name, self._user_groups[
                    user_group_name])

            raise SDKException(
                &#39;UserGroup&#39;, &#39;102&#39;, &#39;No user group exists with name: {0}&#39;.format(
                    user_group_name)
            )

    def add(self,
            usergroup_name,
            domain=None,
            users_list=None,
            entity_dictionary=None,
            external_usergroup=None,
            local_usergroup=None):
        &#34;&#34;&#34;Adds local/external user group on this commcell based domain parameter provided

            Args:
                usergroup_name (str)        --  name of the user group

                domain  (str)               --  name of the domain to which user group
                                                belongs to

                users_list      (list)                  --  list which contains users who will be
                                                members of this group

                entity_dictionary(dict)     --  combination of entity_type, entity
                                                names and role
                e.g.: security_dict={
                                &#39;assoc1&#39;:
                                    {
                                        &#39;entity_type&#39;:[&#39;entity_name&#39;],
                                        &#39;entity_type&#39;:[&#39;entity_name&#39;, &#39;entity_name&#39;],
                                        &#39;_type_&#39;:[&#39;entity_type1&#39;, &#39;entity_type2&#39;]
                                        &#39;role&#39;: [&#39;role1&#39;]
                                    },
                                &#39;assoc2&#39;:
                                    {
                                        &#39;mediaAgentName&#39;: [&#39;networktestcs&#39;, &#39;standbycs&#39;],
                                        &#39;clientName&#39;: [&#39;Linux1&#39;],
                                        &#39;role&#39;: [&#39;New1&#39;]
                                        }
                                    },
                                &#39;assoc3&#39;:
                                    {
                                        &#39;_type_&#39;: [&#39;CLIENT_ENTITY&#39;, &#39;STORAGE_POLICIES_ENTITY&#39;],
                                        &#39;role&#39;: [&#39;Alert Owner&#39;]
                                        }
                                    },
                entity_type         --      key for the entity present in dictionary
                                            on which user will have access
                entity_name         --      Value of the key
                role                --      key for role name you specify
                e.g:   e.g.: {&#34;clientName&#34;:&#34;Linux1&#34;}
                Entity Types are:   clientName, mediaAgentName, libraryName, userName,
                                    userGroupName, storagePolicyName, clientGroupName,
                                    schedulePolicyName, locationName, providerDomainName,
                                    alertName, workflowName, policyName, roleName

                entity_name = &#34;Linux1&#34;, &#34;ClientMachine1&#34;

                external_usergroup(list)    --  list of domain user group which could
                                                be added as members to this group

                local_usergroup (list)      --  list of commcell usergroup which could
                                                be added as members to this group

            Returns:
                (object)    -   UserGroup class instance for the specified user group name

            Raises:
                SDKException:

                    if usergroup with specified name already exists

                    if failed to add usergroup to commcell
        &#34;&#34;&#34;
        if domain:
            group_name = &#34;{0}\\{1}&#34;.format(domain, usergroup_name)
        else:
            group_name = usergroup_name

        if self.has_user_group(group_name):
            raise SDKException(
                &#39;User&#39;, &#39;102&#39;, &#34;UserGroup {0} already exists on this commcell.&#34;.format
                (group_name))

        local_usergroup_json = []
        if local_usergroup:
            local_usergroup_json = [{&#34;userGroupName&#34;: local_group}
                                    for local_group in local_usergroup]

        security_json = {}
        if entity_dictionary:
            security_request = SecurityAssociation._security_association_json(
                entity_dictionary=entity_dictionary)
            security_json = {
                &#34;associationsOperationType&#34;: &#34;ADD&#34;,
                &#34;associations&#34;: security_request
            }
        user_json = []
        if users_list:
            user_json = [{&#34;userName&#34;: uname} for uname in users_list]

        external_usergroup_json = []
        if external_usergroup:
            external_usergroup_json = [{&#34;userGroupName&#34;: external_group}
                                       for external_group in external_usergroup]

        usergrop_request = {
            &#34;groups&#34;: [
                {
                    &#34;userGroupEntity&#34;: {
                        &#34;userGroupName&#34;: group_name
                    },
                    &#34;securityAssociations&#34;: security_json,
                    &#34;users&#34;: user_json,
                    &#34;localUserGroups&#34;: local_usergroup_json,
                    &#34;associatedExternalUserGroups&#34;: external_usergroup_json
                }
            ]
        }

        usergroup_req = self._commcell_object._services[&#39;USERGROUPS&#39;]

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;POST&#39;, usergroup_req, usergrop_request
        )
        if flag:
            if response.json():
                if &#39;response&#39; in response.json():
                    response_json = response.json()[&#39;response&#39;][0]
                    error_code = response_json[&#39;errorCode&#39;]
                    error_message = response_json.get(&#39;errorString&#39;, &#39;&#39;)
                    if not error_code == 0:
                        raise SDKException(&#39;Response&#39;, &#39;101&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        self.refresh()
        return self.get(group_name)

    def delete(self, user_group, new_user=None, new_usergroup=None):
        &#34;&#34;&#34;Deletes the specified user from the existing commcell users

            Args:
                user_group          (str)   --  name of the usergroup which has to be deleted

                new_user            (str)   --  name of the target user, whom the ownership
                                                of entities should be transferred

                new_usergroup       (str)   --  name of the user group, whom the ownership
                                                of entities should be transferred

            Note: either user or usergroup  should be provided for ownership
                transfer not both.

            Raises:
                SDKException:

                    if usergroup doesn&#39;t exist

                    if new user and new usergroup any of these is passed and these doesn&#39;t
                    exist on commcell

                    if both user and usergroup is passed for ownership transfer

                    if both user and usergroup is not passed for ownership transfer

                    if response is not success

        &#34;&#34;&#34;
        if not self.has_user_group(user_group):
            raise SDKException(
                &#39;UserGroup&#39;, &#39;102&#39;, &#34;UserGroup {0} doesn&#39;t exists on this commcell.&#34;.format(
                    user_group)
            )
        if new_user and new_usergroup:
            raise SDKException(
                &#39;User&#39;, &#39;102&#39;, &#34;{0} and {1} both can not be set as owner!! &#34;
                               &#34;please send either new_user or new_usergroup&#34;.format(new_user,
                                                                                     new_usergroup))
        else:
            if new_user:
                if not self._commcell_object.users.has_user(new_user):
                    raise SDKException(
                        &#39;User&#39;, &#39;102&#39;, &#34;User {0} doesn&#39;t exists on this commcell.&#34;.format(
                            new_user)
                    )
                new_user_id = self._commcell_object.users._users[new_user.lower()]
                new_group_id = 0
            else:
                if new_usergroup:
                    if not self.has_user_group(new_usergroup):
                        raise SDKException(
                            &#39;UserGroup&#39;, &#39;102&#39;, &#34;UserGroup {0} doesn&#39;t exists &#34;
                                                &#34;on this commcell.&#34;.format(new_usergroup)
                        )
                else:
                    raise SDKException(
                        &#39;User&#39;, &#39;102&#39;,
                        &#34;Ownership transfer is mondatory!! Please provide new owner information&#34;
                    )
                new_group_id = self._commcell_object.user_groups.get(new_usergroup).user_group_id
                new_user_id = 0

        delete_usergroup = self._commcell_object._services[&#39;DELETE_USERGROUP&#39;] % (
            self._user_groups[user_group.lower()], new_user_id, new_group_id)
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;DELETE&#39;, delete_usergroup
        )
        if flag:
            if response.json():
                if &#39;response&#39; in response.json():
                    response_json = response.json()[&#39;response&#39;][0]
                    error_code = response_json[&#39;errorCode&#39;]
                    error_message = response_json[&#39;errorString&#39;]
                    if not error_code == 0:
                        raise SDKException(&#39;Response&#39;, &#39;101&#39;, error_message)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

        self._user_groups = self._get_user_groups()

    def refresh(self, **kwargs):
        &#34;&#34;&#34;
        Refresh the list of user groups on this commcell.

            Args:
                **kwargs (dict):
                    mongodb (bool)  -- Flag to fetch user groups cache from MongoDB (default: False).
                    hard (bool)     -- Flag to hard refresh MongoDB cache for this entity (default: False).
        &#34;&#34;&#34;
        mongodb = kwargs.get(&#39;mongodb&#39;, False)
        hard = kwargs.get(&#39;hard&#39;, False)

        self._user_groups = self._get_user_groups()
        if mongodb:
            self._user_groups_cache = self.get_user_groups_cache(hard=hard)

    @property
    def all_user_groups(self):
        &#34;&#34;&#34;Returns dict of all the user groups associated with this commcell

        dict - consists of all user group in the commcell
                 {
                   &#34;user_group1_name&#34;: user_group1_id,
                   &#34;user_group2_name&#34;: user_group2_id
                  }

        &#34;&#34;&#34;
        return self._user_groups

    def non_system_usergroups(self):
        &#34;&#34;&#34;Returns dict of all the user groups associated with this commcell

        dict - consists of all user group in the commcell
                 {
                   &#34;user_group1_name&#34;: user_group1_id,
                   &#34;user_group2_name&#34;: user_group2_id
                  }

        &#34;&#34;&#34;
        return self._get_user_groups(system_created=False)

    @property
    def all_usergroups_prop(self)-&gt;list[dict]:
        &#34;&#34;&#34;
        Returns complete GET API response
        &#34;&#34;&#34;
        self._all_usergroups_prop = self._get_user_groups(full_response=True).get(&#34;userGroups&#34;,[])
        return self._all_usergroups_prop</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.security.usergroup.UserGroups.all_user_groups"><code class="name">var <span class="ident">all_user_groups</span></code></dt>
<dd>
<div class="desc"><p>Returns dict of all the user groups associated with this commcell</p>
<p>dict - consists of all user group in the commcell
{
"user_group1_name": user_group1_id,
"user_group2_name": user_group2_id
}</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/usergroup.py#L688-L699" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def all_user_groups(self):
    &#34;&#34;&#34;Returns dict of all the user groups associated with this commcell

    dict - consists of all user group in the commcell
             {
               &#34;user_group1_name&#34;: user_group1_id,
               &#34;user_group2_name&#34;: user_group2_id
              }

    &#34;&#34;&#34;
    return self._user_groups</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.usergroup.UserGroups.all_user_groups_cache"><code class="name">var <span class="ident">all_user_groups_cache</span> : dict</code></dt>
<dd>
<div class="desc"><p>Returns dict of all the user groups and their info present in CommcellEntityCache in mongoDB</p>
<p>dict - consists of all user groups of the in the CommcellEntityCache
{
"userGroup1_name": {
'id': userGroup1_id ,
'description': userGroup1_description,
'status': userGroup1_status,
'company': userGroup1_company
},
"userGroup2_name": {
'id': userGroup2_id ,
'description': userGroup2_description,
'status': userGroup2_status,
'company': userGroup2_company
}
}</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/usergroup.py#L378-L400" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def all_user_groups_cache(self) -&gt; dict:
    &#34;&#34;&#34;Returns dict of all the user groups and their info present in CommcellEntityCache in mongoDB

        dict - consists of all user groups of the in the CommcellEntityCache
                {
                     &#34;userGroup1_name&#34;: {
                            &#39;id&#39;: userGroup1_id ,
                            &#39;description&#39;: userGroup1_description,
                            &#39;status&#39;: userGroup1_status,
                            &#39;company&#39;: userGroup1_company
                            },
                     &#34;userGroup2_name&#34;: {
                            &#39;id&#39;: userGroup2_id ,
                            &#39;description&#39;: userGroup2_description,
                            &#39;status&#39;: userGroup2_status,
                            &#39;company&#39;: userGroup2_company
                            }
                }
    &#34;&#34;&#34;
    if not self._user_groups_cache:
        self._user_groups_cache = self.get_user_groups_cache()
    return self._user_groups_cache</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.usergroup.UserGroups.all_usergroups_prop"><code class="name">var <span class="ident">all_usergroups_prop</span> : list[dict]</code></dt>
<dd>
<div class="desc"><p>Returns complete GET API response</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/usergroup.py#L713-L719" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def all_usergroups_prop(self)-&gt;list[dict]:
    &#34;&#34;&#34;
    Returns complete GET API response
    &#34;&#34;&#34;
    self._all_usergroups_prop = self._get_user_groups(full_response=True).get(&#34;userGroups&#34;,[])
    return self._all_usergroups_prop</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.security.usergroup.UserGroups.add"><code class="name flex">
<span>def <span class="ident">add</span></span>(<span>self, usergroup_name, domain=None, users_list=None, entity_dictionary=None, external_usergroup=None, local_usergroup=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds local/external user group on this commcell based domain parameter provided</p>
<h2 id="args">Args</h2>
<p>usergroup_name (str)
&ndash;
name of the user group</p>
<p>domain
(str)
&ndash;
name of the domain to which user group
belongs to</p>
<p>users_list
(list)
&ndash;
list which contains users who will be
members of this group</p>
<p>entity_dictionary(dict)
&ndash;
combination of entity_type, entity
names and role
e.g.: security_dict={
'assoc1':
{
'entity_type':['entity_name'],
'entity_type':['entity_name', 'entity_name'],
'<em>type</em>':['entity_type1', 'entity_type2']
'role': ['role1']
},
'assoc2':
{
'mediaAgentName': ['networktestcs', 'standbycs'],
'clientName': ['Linux1'],
'role': ['New1']
}
},
'assoc3':
{
'<em>type</em>': ['CLIENT_ENTITY', 'STORAGE_POLICIES_ENTITY'],
'role': ['Alert Owner']
}
},
entity_type
&ndash;
key for the entity present in dictionary
on which user will have access
entity_name
&ndash;
Value of the key
role
&ndash;
key for role name you specify
e.g:
e.g.: {"clientName":"Linux1"}
Entity Types are:
clientName, mediaAgentName, libraryName, userName,
userGroupName, storagePolicyName, clientGroupName,
schedulePolicyName, locationName, providerDomainName,
alertName, workflowName, policyName, roleName</p>
<p>entity_name = "Linux1", "ClientMachine1"</p>
<p>external_usergroup(list)
&ndash;
list of domain user group which could
be added as members to this group</p>
<p>local_usergroup (list)
&ndash;
list of commcell usergroup which could
be added as members to this group</p>
<h2 id="returns">Returns</h2>
<p>(object)
-
UserGroup class instance for the specified user group name</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if usergroup with specified name already exists

if failed to add usergroup to commcell
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/usergroup.py#L450-L585" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add(self,
        usergroup_name,
        domain=None,
        users_list=None,
        entity_dictionary=None,
        external_usergroup=None,
        local_usergroup=None):
    &#34;&#34;&#34;Adds local/external user group on this commcell based domain parameter provided

        Args:
            usergroup_name (str)        --  name of the user group

            domain  (str)               --  name of the domain to which user group
                                            belongs to

            users_list      (list)                  --  list which contains users who will be
                                            members of this group

            entity_dictionary(dict)     --  combination of entity_type, entity
                                            names and role
            e.g.: security_dict={
                            &#39;assoc1&#39;:
                                {
                                    &#39;entity_type&#39;:[&#39;entity_name&#39;],
                                    &#39;entity_type&#39;:[&#39;entity_name&#39;, &#39;entity_name&#39;],
                                    &#39;_type_&#39;:[&#39;entity_type1&#39;, &#39;entity_type2&#39;]
                                    &#39;role&#39;: [&#39;role1&#39;]
                                },
                            &#39;assoc2&#39;:
                                {
                                    &#39;mediaAgentName&#39;: [&#39;networktestcs&#39;, &#39;standbycs&#39;],
                                    &#39;clientName&#39;: [&#39;Linux1&#39;],
                                    &#39;role&#39;: [&#39;New1&#39;]
                                    }
                                },
                            &#39;assoc3&#39;:
                                {
                                    &#39;_type_&#39;: [&#39;CLIENT_ENTITY&#39;, &#39;STORAGE_POLICIES_ENTITY&#39;],
                                    &#39;role&#39;: [&#39;Alert Owner&#39;]
                                    }
                                },
            entity_type         --      key for the entity present in dictionary
                                        on which user will have access
            entity_name         --      Value of the key
            role                --      key for role name you specify
            e.g:   e.g.: {&#34;clientName&#34;:&#34;Linux1&#34;}
            Entity Types are:   clientName, mediaAgentName, libraryName, userName,
                                userGroupName, storagePolicyName, clientGroupName,
                                schedulePolicyName, locationName, providerDomainName,
                                alertName, workflowName, policyName, roleName

            entity_name = &#34;Linux1&#34;, &#34;ClientMachine1&#34;

            external_usergroup(list)    --  list of domain user group which could
                                            be added as members to this group

            local_usergroup (list)      --  list of commcell usergroup which could
                                            be added as members to this group

        Returns:
            (object)    -   UserGroup class instance for the specified user group name

        Raises:
            SDKException:

                if usergroup with specified name already exists

                if failed to add usergroup to commcell
    &#34;&#34;&#34;
    if domain:
        group_name = &#34;{0}\\{1}&#34;.format(domain, usergroup_name)
    else:
        group_name = usergroup_name

    if self.has_user_group(group_name):
        raise SDKException(
            &#39;User&#39;, &#39;102&#39;, &#34;UserGroup {0} already exists on this commcell.&#34;.format
            (group_name))

    local_usergroup_json = []
    if local_usergroup:
        local_usergroup_json = [{&#34;userGroupName&#34;: local_group}
                                for local_group in local_usergroup]

    security_json = {}
    if entity_dictionary:
        security_request = SecurityAssociation._security_association_json(
            entity_dictionary=entity_dictionary)
        security_json = {
            &#34;associationsOperationType&#34;: &#34;ADD&#34;,
            &#34;associations&#34;: security_request
        }
    user_json = []
    if users_list:
        user_json = [{&#34;userName&#34;: uname} for uname in users_list]

    external_usergroup_json = []
    if external_usergroup:
        external_usergroup_json = [{&#34;userGroupName&#34;: external_group}
                                   for external_group in external_usergroup]

    usergrop_request = {
        &#34;groups&#34;: [
            {
                &#34;userGroupEntity&#34;: {
                    &#34;userGroupName&#34;: group_name
                },
                &#34;securityAssociations&#34;: security_json,
                &#34;users&#34;: user_json,
                &#34;localUserGroups&#34;: local_usergroup_json,
                &#34;associatedExternalUserGroups&#34;: external_usergroup_json
            }
        ]
    }

    usergroup_req = self._commcell_object._services[&#39;USERGROUPS&#39;]

    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;POST&#39;, usergroup_req, usergrop_request
    )
    if flag:
        if response.json():
            if &#39;response&#39; in response.json():
                response_json = response.json()[&#39;response&#39;][0]
                error_code = response_json[&#39;errorCode&#39;]
                error_message = response_json.get(&#39;errorString&#39;, &#39;&#39;)
                if not error_code == 0:
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, error_message)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    self.refresh()
    return self.get(group_name)</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.usergroup.UserGroups.delete"><code class="name flex">
<span>def <span class="ident">delete</span></span>(<span>self, user_group, new_user=None, new_usergroup=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes the specified user from the existing commcell users</p>
<h2 id="args">Args</h2>
<p>user_group
(str)
&ndash;
name of the usergroup which has to be deleted</p>
<p>new_user
(str)
&ndash;
name of the target user, whom the ownership
of entities should be transferred</p>
<p>new_usergroup
(str)
&ndash;
name of the user group, whom the ownership
of entities should be transferred
Note: either user or usergroup
should be provided for ownership
transfer not both.</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>if usergroup doesn't exist

if new user and new usergroup any of these is passed and these doesn't
exist on commcell

if both user and usergroup is passed for ownership transfer

if both user and usergroup is not passed for ownership transfer

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/usergroup.py#L587-L670" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete(self, user_group, new_user=None, new_usergroup=None):
    &#34;&#34;&#34;Deletes the specified user from the existing commcell users

        Args:
            user_group          (str)   --  name of the usergroup which has to be deleted

            new_user            (str)   --  name of the target user, whom the ownership
                                            of entities should be transferred

            new_usergroup       (str)   --  name of the user group, whom the ownership
                                            of entities should be transferred

        Note: either user or usergroup  should be provided for ownership
            transfer not both.

        Raises:
            SDKException:

                if usergroup doesn&#39;t exist

                if new user and new usergroup any of these is passed and these doesn&#39;t
                exist on commcell

                if both user and usergroup is passed for ownership transfer

                if both user and usergroup is not passed for ownership transfer

                if response is not success

    &#34;&#34;&#34;
    if not self.has_user_group(user_group):
        raise SDKException(
            &#39;UserGroup&#39;, &#39;102&#39;, &#34;UserGroup {0} doesn&#39;t exists on this commcell.&#34;.format(
                user_group)
        )
    if new_user and new_usergroup:
        raise SDKException(
            &#39;User&#39;, &#39;102&#39;, &#34;{0} and {1} both can not be set as owner!! &#34;
                           &#34;please send either new_user or new_usergroup&#34;.format(new_user,
                                                                                 new_usergroup))
    else:
        if new_user:
            if not self._commcell_object.users.has_user(new_user):
                raise SDKException(
                    &#39;User&#39;, &#39;102&#39;, &#34;User {0} doesn&#39;t exists on this commcell.&#34;.format(
                        new_user)
                )
            new_user_id = self._commcell_object.users._users[new_user.lower()]
            new_group_id = 0
        else:
            if new_usergroup:
                if not self.has_user_group(new_usergroup):
                    raise SDKException(
                        &#39;UserGroup&#39;, &#39;102&#39;, &#34;UserGroup {0} doesn&#39;t exists &#34;
                                            &#34;on this commcell.&#34;.format(new_usergroup)
                    )
            else:
                raise SDKException(
                    &#39;User&#39;, &#39;102&#39;,
                    &#34;Ownership transfer is mondatory!! Please provide new owner information&#34;
                )
            new_group_id = self._commcell_object.user_groups.get(new_usergroup).user_group_id
            new_user_id = 0

    delete_usergroup = self._commcell_object._services[&#39;DELETE_USERGROUP&#39;] % (
        self._user_groups[user_group.lower()], new_user_id, new_group_id)
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;DELETE&#39;, delete_usergroup
    )
    if flag:
        if response.json():
            if &#39;response&#39; in response.json():
                response_json = response.json()[&#39;response&#39;][0]
                error_code = response_json[&#39;errorCode&#39;]
                error_message = response_json[&#39;errorString&#39;]
                if not error_code == 0:
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, error_message)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    self._user_groups = self._get_user_groups()</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.usergroup.UserGroups.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, user_group_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a user group object of the specified user group name.</p>
<h2 id="args">Args</h2>
<p>user_group_name (str)
&ndash;
name of the user group</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the UserGroup class for the given user group name</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the user group name argument is not string</p>
<pre><code>if no user group exists with the given name
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/usergroup.py#L421-L448" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, user_group_name):
    &#34;&#34;&#34;Returns a user group object of the specified user group name.

        Args:
            user_group_name (str)  --  name of the user group

        Returns:
            object - instance of the UserGroup class for the given user group name

        Raises:
            SDKException:
                if type of the user group name argument is not string

                if no user group exists with the given name
    &#34;&#34;&#34;
    if not isinstance(user_group_name, str):
        raise SDKException(&#39;UserGroup&#39;, &#39;101&#39;)
    else:
        user_group_name = user_group_name.lower()

        if self.has_user_group(user_group_name):
            return UserGroup(self._commcell_object, user_group_name, self._user_groups[
                user_group_name])

        raise SDKException(
            &#39;UserGroup&#39;, &#39;102&#39;, &#39;No user group exists with name: {0}&#39;.format(
                user_group_name)
        )</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.usergroup.UserGroups.get_user_groups_cache"><code class="name flex">
<span>def <span class="ident">get_user_groups_cache</span></span>(<span>self, hard: bool = False, **kwargs) ‑> dict</span>
</code></dt>
<dd>
<div class="desc"><p>Gets all the user groups present in CommcellEntityCache DB.</p>
<h2 id="args">Args</h2>
<p>hard
(bool)
&ndash;
Flag to perform hard refresh on user groups cache.
**kwargs (dict):
fl (list)
&ndash;
List of columns to return in response (default: None).
sort (list)
&ndash;
Contains the name of the column on which sorting will be performed and type of sort.
Valid sort type: 1 for ascending and -1 for descending
e.g. sort = ['columnName', '1'] (default: None).
limit (list)
&ndash;
Contains the start and limit parameter value.
Default ['0', '100'].
search (str)
&ndash;
Contains the string to search in the commcell entity cache (default: None).
fq (list)
&ndash;
Contains the columnName, condition and value.
e.g. fq = [['groupName', 'contains', 'test'],
['status', 'eq', 'Enabled']] (default: None).</p>
<h2 id="returns">Returns</h2>
<dl>
<dt><code>dict</code></dt>
<dd>Dictionary of all the properties present in response.</dd>
</dl></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/usergroup.py#L297-L376" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get_user_groups_cache(self, hard: bool = False, **kwargs) -&gt; dict:
    &#34;&#34;&#34;
    Gets all the user groups present in CommcellEntityCache DB.

    Args:
        hard  (bool)          --   Flag to perform hard refresh on user groups cache.
        **kwargs (dict):
            fl (list)         --   List of columns to return in response (default: None).
            sort (list)       --   Contains the name of the column on which sorting will be performed and type of sort.
                                        Valid sort type: 1 for ascending and -1 for descending
                                        e.g. sort = [&#39;columnName&#39;, &#39;1&#39;] (default: None).
            limit (list)      --   Contains the start and limit parameter value.
                                        Default [&#39;0&#39;, &#39;100&#39;].
            search (str)      --   Contains the string to search in the commcell entity cache (default: None).
            fq (list)         --   Contains the columnName, condition and value.
                                        e.g. fq = [[&#39;groupName&#39;, &#39;contains&#39;, &#39;test&#39;],
                                        [&#39;status&#39;, &#39;eq&#39;, &#39;Enabled&#39;]] (default: None).

    Returns:
        dict: Dictionary of all the properties present in response.
    &#34;&#34;&#34;
    # computing params
    fl_parameters = self._get_fl_parameters(kwargs.get(&#39;fl&#39;, None))
    fq_parameters = self._get_fq_parameters(kwargs.get(&#39;fq&#39;, None))
    limit = kwargs.get(&#39;limit&#39;, None)
    limit_parameters = f&#39;start={limit[0]}&amp;limit={limit[1]}&#39; if limit else &#39;&#39;
    hard_refresh = &#39;&amp;hardRefresh=true&#39; if hard else &#39;&#39;
    sort_parameters = self._get_sort_parameters(kwargs.get(&#39;sort&#39;, None)) if kwargs.get(&#39;sort&#39;, None) else &#39;&#39;

    # Search operation can only be performed on limited columns, so filtering out the columns on which search works
    searchable_columns = [&#34;groupName&#34;,&#34;description&#34;,&#34;company&#34;]
    search_parameter = (f&#39;&amp;search={&#34;,&#34;.join(self.valid_columns[col] for col in searchable_columns)}:contains:&#39;
                        f&#39;{kwargs.get(&#34;search&#34;, None)}&#39;) if kwargs.get(&#39;search&#39;, None) else &#39;&#39;

    params = [
        limit_parameters,
        sort_parameters,
        fl_parameters,
        hard_refresh,
        search_parameter,
        fq_parameters
    ]
    request_url = f&#34;{self._commcell_object._services[&#39;USERGROUPS&#39;]}?&#34; + &#34;&#34;.join(params)
    flag, response = self._commcell_object._cvpysdk_object.make_request(&#34;GET&#34;, request_url)
    if not flag:
        response_string = self._commcell_object._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    user_groups_cache = {}
    if response.json() and &#39;userGroups&#39; in response.json():
        self.filter_query_count = response.json().get(&#39;filterQueryCount&#39;,0)
        for group in response.json()[&#39;userGroups&#39;]:
            name = group.get(&#39;userGroupEntity&#39;, {}).get(&#39;userGroupName&#39;)
            user_groups_config = {
                &#39;groupName&#39;:name,
                &#39;groupId&#39;: group.get(&#39;userGroupEntity&#39;, {}).get(&#39;userGroupId&#39;),
                &#39;description&#39;: group.get(&#39;description&#39;, &#39;&#39;),
                &#39;status&#39;: group.get(&#39;enabled&#39;),
                &#39;company&#39;: group.get(&#39;userGroupEntity&#39;, {}).get(&#39;entityInfo&#39;, {}).get(&#39;companyName&#39;)
            }
            if self._commcell_object.is_global_scope():
                user_groups_config[&#39;commcell&#39;] = group.get(&#39;userGroupEntity&#39;, {}).get(&#39;entityInfo&#39;, {}).get(&#39;multiCommcellName&#39;,&#39;&#39;)

                # Handle duplicate names for different commcells
                unique_name = name
                i = 1
                while unique_name in user_groups_cache:
                    existing_user = user_groups_cache[unique_name]
                    if existing_user.get(&#39;commcell&#39;) != user_groups_config.get(&#39;commcell&#39;):
                        unique_name = f&#34;{name}__{i}&#34;
                        i += 1
                    else:
                        break
                user_groups_cache[unique_name] = user_groups_config
            else:
                user_groups_cache[name] = user_groups_config

        return user_groups_cache
    else:
        raise SDKException(&#39;Response&#39;, &#39;102&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.usergroup.UserGroups.has_user_group"><code class="name flex">
<span>def <span class="ident">has_user_group</span></span>(<span>self, user_group_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks if a user group exists in the commcell with the input user group name.</p>
<h2 id="args">Args</h2>
<p>user_group_name (str)
&ndash;
name of the user group</p>
<h2 id="returns">Returns</h2>
<p>bool - boolean output whether the user group exists in the commcell
or not</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the user group name argument is not string</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/usergroup.py#L402-L419" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_user_group(self, user_group_name):
    &#34;&#34;&#34;Checks if a user group exists in the commcell with the input user group name.

        Args:
            user_group_name (str)  --  name of the user group

        Returns:
            bool - boolean output whether the user group exists in the commcell
                   or not

        Raises:
            SDKException:
                if type of the user group name argument is not string
    &#34;&#34;&#34;
    if not isinstance(user_group_name, str):
        raise SDKException(&#39;UserGroup&#39;, &#39;101&#39;)

    return self._user_groups and user_group_name.lower() in self._user_groups</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.usergroup.UserGroups.non_system_usergroups"><code class="name flex">
<span>def <span class="ident">non_system_usergroups</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns dict of all the user groups associated with this commcell</p>
<p>dict - consists of all user group in the commcell
{
"user_group1_name": user_group1_id,
"user_group2_name": user_group2_id
}</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/usergroup.py#L701-L711" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def non_system_usergroups(self):
    &#34;&#34;&#34;Returns dict of all the user groups associated with this commcell

    dict - consists of all user group in the commcell
             {
               &#34;user_group1_name&#34;: user_group1_id,
               &#34;user_group2_name&#34;: user_group2_id
              }

    &#34;&#34;&#34;
    return self._get_user_groups(system_created=False)</code></pre>
</details>
</dd>
<dt id="cvpysdk.security.usergroup.UserGroups.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the list of user groups on this commcell.</p>
<pre><code>Args:
    **kwargs (dict):
        mongodb (bool)  -- Flag to fetch user groups cache from MongoDB (default: False).
        hard (bool)     -- Flag to hard refresh MongoDB cache for this entity (default: False).
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/security/usergroup.py#L672-L686" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self, **kwargs):
    &#34;&#34;&#34;
    Refresh the list of user groups on this commcell.

        Args:
            **kwargs (dict):
                mongodb (bool)  -- Flag to fetch user groups cache from MongoDB (default: False).
                hard (bool)     -- Flag to hard refresh MongoDB cache for this entity (default: False).
    &#34;&#34;&#34;
    mongodb = kwargs.get(&#39;mongodb&#39;, False)
    hard = kwargs.get(&#39;hard&#39;, False)

    self._user_groups = self._get_user_groups()
    if mongodb:
        self._user_groups_cache = self.get_user_groups_cache(hard=hard)</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.security" href="index.html">cvpysdk.security</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.security.usergroup.UserGroup" href="#cvpysdk.security.usergroup.UserGroup">UserGroup</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.security.usergroup.UserGroup.allow_multiple_company_members" href="#cvpysdk.security.usergroup.UserGroup.allow_multiple_company_members">allow_multiple_company_members</a></code></li>
<li><code><a title="cvpysdk.security.usergroup.UserGroup.associations" href="#cvpysdk.security.usergroup.UserGroup.associations">associations</a></code></li>
<li><code><a title="cvpysdk.security.usergroup.UserGroup.available_users_for_group" href="#cvpysdk.security.usergroup.UserGroup.available_users_for_group">available_users_for_group</a></code></li>
<li><code><a title="cvpysdk.security.usergroup.UserGroup.company_id" href="#cvpysdk.security.usergroup.UserGroup.company_id">company_id</a></code></li>
<li><code><a title="cvpysdk.security.usergroup.UserGroup.company_name" href="#cvpysdk.security.usergroup.UserGroup.company_name">company_name</a></code></li>
<li><code><a title="cvpysdk.security.usergroup.UserGroup.description" href="#cvpysdk.security.usergroup.UserGroup.description">description</a></code></li>
<li><code><a title="cvpysdk.security.usergroup.UserGroup.disable_tfa" href="#cvpysdk.security.usergroup.UserGroup.disable_tfa">disable_tfa</a></code></li>
<li><code><a title="cvpysdk.security.usergroup.UserGroup.email" href="#cvpysdk.security.usergroup.UserGroup.email">email</a></code></li>
<li><code><a title="cvpysdk.security.usergroup.UserGroup.enable_tfa" href="#cvpysdk.security.usergroup.UserGroup.enable_tfa">enable_tfa</a></code></li>
<li><code><a title="cvpysdk.security.usergroup.UserGroup.is_tfa_enabled" href="#cvpysdk.security.usergroup.UserGroup.is_tfa_enabled">is_tfa_enabled</a></code></li>
<li><code><a title="cvpysdk.security.usergroup.UserGroup.name" href="#cvpysdk.security.usergroup.UserGroup.name">name</a></code></li>
<li><code><a title="cvpysdk.security.usergroup.UserGroup.refresh" href="#cvpysdk.security.usergroup.UserGroup.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.security.usergroup.UserGroup.status" href="#cvpysdk.security.usergroup.UserGroup.status">status</a></code></li>
<li><code><a title="cvpysdk.security.usergroup.UserGroup.update_navigation_preferences" href="#cvpysdk.security.usergroup.UserGroup.update_navigation_preferences">update_navigation_preferences</a></code></li>
<li><code><a title="cvpysdk.security.usergroup.UserGroup.update_security_associations" href="#cvpysdk.security.usergroup.UserGroup.update_security_associations">update_security_associations</a></code></li>
<li><code><a title="cvpysdk.security.usergroup.UserGroup.update_usergroup_members" href="#cvpysdk.security.usergroup.UserGroup.update_usergroup_members">update_usergroup_members</a></code></li>
<li><code><a title="cvpysdk.security.usergroup.UserGroup.user_group_id" href="#cvpysdk.security.usergroup.UserGroup.user_group_id">user_group_id</a></code></li>
<li><code><a title="cvpysdk.security.usergroup.UserGroup.user_group_name" href="#cvpysdk.security.usergroup.UserGroup.user_group_name">user_group_name</a></code></li>
<li><code><a title="cvpysdk.security.usergroup.UserGroup.usergroups" href="#cvpysdk.security.usergroup.UserGroup.usergroups">usergroups</a></code></li>
<li><code><a title="cvpysdk.security.usergroup.UserGroup.users" href="#cvpysdk.security.usergroup.UserGroup.users">users</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.security.usergroup.UserGroups" href="#cvpysdk.security.usergroup.UserGroups">UserGroups</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.security.usergroup.UserGroups.add" href="#cvpysdk.security.usergroup.UserGroups.add">add</a></code></li>
<li><code><a title="cvpysdk.security.usergroup.UserGroups.all_user_groups" href="#cvpysdk.security.usergroup.UserGroups.all_user_groups">all_user_groups</a></code></li>
<li><code><a title="cvpysdk.security.usergroup.UserGroups.all_user_groups_cache" href="#cvpysdk.security.usergroup.UserGroups.all_user_groups_cache">all_user_groups_cache</a></code></li>
<li><code><a title="cvpysdk.security.usergroup.UserGroups.all_usergroups_prop" href="#cvpysdk.security.usergroup.UserGroups.all_usergroups_prop">all_usergroups_prop</a></code></li>
<li><code><a title="cvpysdk.security.usergroup.UserGroups.delete" href="#cvpysdk.security.usergroup.UserGroups.delete">delete</a></code></li>
<li><code><a title="cvpysdk.security.usergroup.UserGroups.get" href="#cvpysdk.security.usergroup.UserGroups.get">get</a></code></li>
<li><code><a title="cvpysdk.security.usergroup.UserGroups.get_user_groups_cache" href="#cvpysdk.security.usergroup.UserGroups.get_user_groups_cache">get_user_groups_cache</a></code></li>
<li><code><a title="cvpysdk.security.usergroup.UserGroups.has_user_group" href="#cvpysdk.security.usergroup.UserGroups.has_user_group">has_user_group</a></code></li>
<li><code><a title="cvpysdk.security.usergroup.UserGroups.non_system_usergroups" href="#cvpysdk.security.usergroup.UserGroups.non_system_usergroups">non_system_usergroups</a></code></li>
<li><code><a title="cvpysdk.security.usergroup.UserGroups.refresh" href="#cvpysdk.security.usergroup.UserGroups.refresh">refresh</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>