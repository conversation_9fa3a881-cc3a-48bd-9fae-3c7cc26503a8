<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.backupset API documentation</title>
<meta name="description" content="Main file for performing backup set operations …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.backupset</code></h1>
</header>
<section id="section-intro">
<p>Main file for performing backup set operations.</p>
<p>Backupsets and Backupset are 2 classes defined in this file.</p>
<p>Backupsets: Class for representing all the backup sets associated with a specific agent</p>
<p>Backupset:
Class for a single backup set selected for an agent,
and to perform operations on that backup set</p>
<h1 id="backupsets">Backupsets:</h1>
<pre><code>__init__(class_object)          -- initialise object of Backupsets class associated with
the specified agent/instance

__str__()                       -- returns all the backupsets associated with the agent

__repr__()                      -- returns the string for the instance of the Backupsets class

__len__()                       -- returns the number of backupsets associated with the Agent

__getitem__()                   -- returns the name of the backupset for the given backupset Id
or the details for the given backupset name

_get_backupsets()               -- gets all the backupsets associated with the agent specified

default_backup_set()            -- returns the name of the default backup set

all_backupsets()                -- returns the dict of all the backupsets for the Agent /
Instance of the selected Client

has_backupset(backupset_name)   -- checks if a backupset exists with the given name or not

_process_add_response()         -- to process the add backupset request using API call

add(backupset_name)             -- adds a new backupset to the agent of the specified client

add_archiveset(archiveset_name)   -- adds a new archiveset to the agent of the specified client

add_v1_sharepoint_client()      -- Adds a new Office 365 V1 Share Point Pseudo Client to the Commcell.

add_salesforce_backupset()      -- adds a new salesforce backupset

get(backupset_name)             -- returns the Backupset class object
of the input backup set name

delete(backupset_name)          -- removes the backupset from the agent of the specified client

refresh()                       -- refresh the backupsets associated with the agent
</code></pre>
<h1 id="backupset">Backupset:</h1>
<pre><code>__init__()                      -- initialise object of Backupset with the specified backupset
name and id, and associated to the specified instance

__getattr__()                   -- provides access to restore helper methods

__repr__()                      -- return the backupset name, the instance is associated with

_get_backupset_id()             -- method to get the backupset id, if not specified in __init__

_get_backupset_properties()     -- get the properties of this backupset

_run_backup()                   -- runs full backup for the specified subclient,
and appends the job object to the return list

_update()                       -- updates the properties of the backupset

_get_epoch_time()               -- gets the Epoch time given the input time is in format

                                        %Y-%m-%d %H:%M:%S

_set_defaults()                 -- recursively sets default values on a dictionary

_prepare_browse_options()       -- prepares the options for the Browse/find operation

_prepare_browse_json()          -- prepares the JSON object for the browse request

_process_browse_response()      -- retrieves the items from browse response

_process_update_request()       --  to process the request using API call

_do_browse()                    -- performs a browse operation with the given options

update_properties()             -- updates the backupset properties

set_default_backupset()         -- sets the backupset as the default backup set for the agent,
if not already default

backup()                        -- runs full backup for all subclients
associated with this backupset

browse()                        -- browse the content of the backupset

find()                          -- find content in the backupset

list_media()                    -- List media required to browse and restore backed up data from the backupset

refresh()                       -- refresh the properties of the backupset

delete_data()                   -- deletes items from the backupset and makes then unavailable
to browse and restore

backed_up_files_count()         -- Returns the count of the total number of files present in the backed up data
                                   of all the subclients of the given backupset.
</code></pre>
<h2 id="backupset-instance-attributes">Backupset Instance Attributes</h2>
<pre><code>**properties**                  -- returns the properties of backupset

**name**                        -- returns the name of the backupset

**guid**                        -- treats the backupset GUID as a property
of the Backupset class
</code></pre>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupset.py#L1-L2391" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Main file for performing backup set operations.

Backupsets and Backupset are 2 classes defined in this file.

Backupsets: Class for representing all the backup sets associated with a specific agent

Backupset:  Class for a single backup set selected for an agent,
and to perform operations on that backup set


Backupsets:
===========
    __init__(class_object)          -- initialise object of Backupsets class associated with
    the specified agent/instance

    __str__()                       -- returns all the backupsets associated with the agent

    __repr__()                      -- returns the string for the instance of the Backupsets class

    __len__()                       -- returns the number of backupsets associated with the Agent

    __getitem__()                   -- returns the name of the backupset for the given backupset Id
    or the details for the given backupset name

    _get_backupsets()               -- gets all the backupsets associated with the agent specified

    default_backup_set()            -- returns the name of the default backup set

    all_backupsets()                -- returns the dict of all the backupsets for the Agent /
    Instance of the selected Client

    has_backupset(backupset_name)   -- checks if a backupset exists with the given name or not

    _process_add_response()         -- to process the add backupset request using API call

    add(backupset_name)             -- adds a new backupset to the agent of the specified client
    
    add_archiveset(archiveset_name)   -- adds a new archiveset to the agent of the specified client

    add_v1_sharepoint_client()      -- Adds a new Office 365 V1 Share Point Pseudo Client to the Commcell.

    add_salesforce_backupset()      -- adds a new salesforce backupset

    get(backupset_name)             -- returns the Backupset class object
    of the input backup set name

    delete(backupset_name)          -- removes the backupset from the agent of the specified client

    refresh()                       -- refresh the backupsets associated with the agent


Backupset:
==========
    __init__()                      -- initialise object of Backupset with the specified backupset
    name and id, and associated to the specified instance

    __getattr__()                   -- provides access to restore helper methods

    __repr__()                      -- return the backupset name, the instance is associated with

    _get_backupset_id()             -- method to get the backupset id, if not specified in __init__

    _get_backupset_properties()     -- get the properties of this backupset

    _run_backup()                   -- runs full backup for the specified subclient,
    and appends the job object to the return list

    _update()                       -- updates the properties of the backupset

    _get_epoch_time()               -- gets the Epoch time given the input time is in format

                                            %Y-%m-%d %H:%M:%S

    _set_defaults()                 -- recursively sets default values on a dictionary

    _prepare_browse_options()       -- prepares the options for the Browse/find operation

    _prepare_browse_json()          -- prepares the JSON object for the browse request

    _process_browse_response()      -- retrieves the items from browse response

    _process_update_request()       --  to process the request using API call

    _do_browse()                    -- performs a browse operation with the given options

    update_properties()             -- updates the backupset properties

    set_default_backupset()         -- sets the backupset as the default backup set for the agent,
    if not already default

    backup()                        -- runs full backup for all subclients
    associated with this backupset

    browse()                        -- browse the content of the backupset

    find()                          -- find content in the backupset

    list_media()                    -- List media required to browse and restore backed up data from the backupset

    refresh()                       -- refresh the properties of the backupset

    delete_data()                   -- deletes items from the backupset and makes then unavailable
    to browse and restore

    backed_up_files_count()         -- Returns the count of the total number of files present in the backed up data
                                       of all the subclients of the given backupset.

Backupset instance Attributes
-----------------------------

    **properties**                  -- returns the properties of backupset

    **name**                        -- returns the name of the backupset

    **guid**                        -- treats the backupset GUID as a property
    of the Backupset class

&#34;&#34;&#34;

from __future__ import absolute_import
from __future__ import unicode_literals

import threading
import time
import copy

from base64 import b64encode

from .subclient import Subclients
from .schedules import Schedules
from .exception import SDKException


class Backupsets(object):
    &#34;&#34;&#34;Class for getting all the backupsets associated with a client.&#34;&#34;&#34;

    def __init__(self, class_object):
        &#34;&#34;&#34;Initialize object of the Backupsets class.

            Args:
                class_object    (object)    --  instance of the Agent / Instance class

            Returns:
                object  -   instance of the Backupsets class

            Raises:
                SDKException:
                    if class object is not an instance of the Agent / Instance class

        &#34;&#34;&#34;
        from .agent import Agent
        from .instance import Instance

        self._instance_object = None

        if isinstance(class_object, Agent):
            self._agent_object = class_object
        elif isinstance(class_object, Instance):
            self._instance_object = class_object
            self._agent_object = class_object._agent_object
        else:
            raise SDKException(&#39;Backupset&#39;, &#39;103&#39;)

        self._client_object = self._agent_object._client_object

        self._commcell_object = self._agent_object._commcell_object

        self._cvpysdk_object = self._commcell_object._cvpysdk_object
        self._services = self._commcell_object._services
        self._update_response_ = self._commcell_object._update_response_

        self._BACKUPSETS = self._services[&#39;GET_ALL_BACKUPSETS&#39;] % (self._client_object.client_id)
        if self._agent_object:
            self._BACKUPSETS += &#39;&amp;applicationId=&#39; + self._agent_object.agent_id

        if self._instance_object:
            self._BACKUPSETS += &#39;&amp;instanceId=&#39; + self._instance_object.instance_id

        if self._agent_object.agent_name in [&#39;cloud apps&#39;, &#39;sql server&#39;, &#39;sap hana&#39;]:
            self._BACKUPSETS += &#39;&amp;excludeHidden=0&#39;

        self._backupsets = None
        self._default_backup_set = None
        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all backupsets of the agent of a client.

            Returns:
                str - string of all the backupsets of an agent of a client
        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\t{:^20}\t{:^20}\t{:^20}\n\n&#39;.format(
            &#39;S. No.&#39;, &#39;Backupset&#39;, &#39;Instance&#39;, &#39;Agent&#39;, &#39;Client&#39;
        )

        for index, backupset in enumerate(self._backupsets):
            sub_str = &#39;{:^5}\t{:20}\t{:20}\t{:20}\t{:20}\n&#39;.format(
                index + 1,
                backupset.split(&#39;\\&#39;)[-1],
                self._backupsets[backupset][&#39;instance&#39;],
                self._agent_object.agent_name,
                self._client_object.client_name
            )
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the Backupsets class.&#34;&#34;&#34;
        return &#34;Backupsets class instance for Agent: &#39;{0}&#39;&#34;.format(self._agent_object.agent_name)

    def __len__(self):
        &#34;&#34;&#34;Returns the number of the backupsets for the selected Agent.&#34;&#34;&#34;
        return len(self.all_backupsets)

    def __getitem__(self, value):
        &#34;&#34;&#34;Returns the name of the backupset for the given backupset ID or
            the details of the backupset for given backupset Name.

            Args:
                value   (str / int)     --  Name or ID of the backupset

            Returns:
                str     -   name of the backupset, if the backupset id was given

                dict    -   dict of details of the backupset, if backupset name was given

            Raises:
                IndexError:
                    no backupset exists with the given Name / Id

        &#34;&#34;&#34;
        value = str(value)

        if value in self.all_backupsets:
            return self.all_backupsets[value]
        else:
            try:
                return list(
                    filter(lambda x: x[1][&#39;id&#39;] == value, self.all_backupsets.items())
                )[0][0]
            except IndexError:
                raise IndexError(&#39;No backupset exists with the given Name / Id&#39;)

    def _get_backupsets(self):
        &#34;&#34;&#34;Gets all the backupsets associated to the agent specified by agent_object.

            Returns:
                dict - consists of all backupsets of the agent
                    {
                         &#34;backupset1_name&#34;: {
                             &#34;id&#34;: backupset1_id,
                             &#34;instance&#34;: instance
                         },
                         &#34;backupset2_name&#34;: {
                             &#34;id&#34;: backupset2_id,
                             &#34;instance&#34;: instance
                         }
                    }

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._BACKUPSETS)

        if flag:
            if response.json() and &#39;backupsetProperties&#39; in response.json():
                return_dict = {}

                for dictionary in response.json()[&#39;backupsetProperties&#39;]:
                    agent = dictionary[&#39;backupSetEntity&#39;][&#39;appName&#39;].lower()
                    instance = dictionary[&#39;backupSetEntity&#39;][&#39;instanceName&#39;].lower()

                    if self._instance_object is not None:
                        if (self._instance_object.instance_name in instance and
                                self._agent_object.agent_name in agent):
                            temp_name = dictionary[&#39;backupSetEntity&#39;][&#39;backupsetName&#39;].lower()
                            temp_id = str(dictionary[&#39;backupSetEntity&#39;][&#39;backupsetId&#39;]).lower()
                            return_dict[temp_name] = {
                                &#34;id&#34;: temp_id,
                                &#34;instance&#34;: instance
                            }

                            if dictionary[&#39;commonBackupSet&#39;].get(&#39;isDefaultBackupSet&#39;):
                                self._default_backup_set = temp_name

                    elif self._agent_object.agent_name in agent:
                        temp_name = dictionary[&#39;backupSetEntity&#39;][&#39;backupsetName&#39;].lower()
                        temp_id = str(dictionary[&#39;backupSetEntity&#39;][&#39;backupsetId&#39;]).lower()

                        if len(self._agent_object.instances.all_instances) &gt; 1:
                            return_dict[&#34;{0}\\{1}&#34;.format(instance, temp_name)] = {
                                &#34;id&#34;: temp_id,
                                &#34;instance&#34;: instance
                            }

                            if dictionary[&#39;commonBackupSet&#39;].get(&#39;isDefaultBackupSet&#39;):
                                self._default_backup_set = &#34;{0}\\{1}&#34;.format(instance, temp_name)
                        else:
                            return_dict[temp_name] = {
                                &#34;id&#34;: temp_id,
                                &#34;instance&#34;: instance
                            }

                            if dictionary[&#39;commonBackupSet&#39;].get(&#39;isDefaultBackupSet&#39;):
                                self._default_backup_set = temp_name

                return return_dict
            else:
                return {}
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    @property
    def all_backupsets(self):
        &#34;&#34;&#34;Returns the dict of backupsets for the Agent / Instance of the selected Client

            dict - consists of all backupsets
                    {
                         &#34;backupset1_name&#34;: {
                             &#34;id&#34;: backupset1_id,
                             &#34;instance&#34;: instance
                         },
                         &#34;backupset2_name&#34;: {
                             &#34;id&#34;: backupset2_id,
                             &#34;instance&#34;: instance
                         }
                    }
        &#34;&#34;&#34;
        return self._backupsets

    def has_backupset(self, backupset_name):
        &#34;&#34;&#34;Checks if a backupset exists for the agent with the input backupset name.

            Args:
                backupset_name (str)  --  name of the backupset

            Returns:
                bool - boolean output whether the backupset exists for the agent or not

            Raises:
                SDKException:
                    if type of the backupset name argument is not string
        &#34;&#34;&#34;
        if not isinstance(backupset_name, str):
            raise SDKException(&#39;Backupset&#39;, &#39;101&#39;)

        return self._backupsets and backupset_name.lower() in self._backupsets

    def _process_add_response(self, backupset_name, request_json):
        &#34;&#34;&#34;Runs the Backupset Add API with the request JSON provided,
            and returns the contents after parsing the response.

            Args:
                backupset_name   (str)  --   backupset name
                request_json    (dict)  --  JSON request to run for the API

            Returns:
                (bool, str, str):
                    bool -  flag specifies whether success / failure

                    str  -  error code received in the response

                    str  -  error message received

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._services[&#39;ADD_BACKUPSET&#39;], request_json)

        if flag:
            if response.json():
                if &#39;response&#39; in response.json():
                    error_code = response.json()[&#39;response&#39;][0][&#39;errorCode&#39;]

                    if error_code != 0:
                        error_string = response.json()[&#39;response&#39;][0][&#39;errorString&#39;]
                        o_str = &#39;Failed to create backupset\nError: &#34;{0}&#34;&#39;.format(error_string)
                        raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, o_str)
                    else:
                        # initialize the backupsets again
                        # so the backupset object has all the backupsets
                        self.refresh()
                        return self.get(backupset_name)
                elif &#39;errorMessage&#39; in response.json():
                    error_string = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Failed to create backuspet\nError: &#34;{0}&#34;&#39;.format(error_string)
                    raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, o_str)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def add(self, backupset_name, on_demand_backupset=False, **kwargs):
        &#34;&#34;&#34;Adds a new backup set to the agent.

            Args:
                backupset_name      (str)   --  name of the new backupset to add

                on_demand_backupset (bool)  --  flag to specify whether the backupset to be added
                is a simple backupset or an on-demand backupset

                    default: False

                **kwargs    --  dict of keyword arguments as follows:

                    storage_policy  (str)   --  name of the storage policy to associate to the
                    backupset

                    plan_name       (str)   -- name of the plan to associate to the backupset

                    is_nas_turbo_backupset  (bool)    --  True for NAS based client.


            Returns:
                object - instance of the Backupset class, if created successfully

            Raises:
                SDKException:
                    if type of the backupset name argument is not string

                    if failed to create a backupset

                    if response is empty

                    if response is not success

                    if backupset with same name already exists
        &#34;&#34;&#34;
        if not (isinstance(backupset_name, str) and isinstance(on_demand_backupset, bool)):
            raise SDKException(&#39;Backupset&#39;, &#39;101&#39;)

        if self.has_backupset(backupset_name):
            raise SDKException(
                &#39;Backupset&#39;, &#39;102&#39;, &#39;Backupset &#34;{0}&#34; already exists.&#39;.format(backupset_name)
            )

        if self._instance_object is None:
            if self._agent_object.instances.has_instance(&#39;DefaultInstanceName&#39;):
                self._instance_object = self._agent_object.instances.get(&#39;DefaultInstanceName&#39;)
            else:
                self._instance_object = self._agent_object.instances.get(
                    sorted(self._agent_object.instances.all_instances)[0]
                )

        request_json = {
            &#34;association&#34;: {
                &#34;entity&#34;: [{
                    &#34;clientName&#34;: self._client_object.client_name,
                    &#34;appName&#34;: self._agent_object.agent_name,
                    &#34;instanceName&#34;: self._instance_object.instance_name,
                    &#34;backupsetName&#34;: backupset_name
                }]
            },
            &#34;backupSetInfo&#34;: {
                &#34;commonBackupSet&#34;: {
                    &#34;onDemandBackupset&#34;: on_demand_backupset
                }
            }
        }

        if kwargs.get(&#39;is_nas_turbo_type&#39;):
            request_json[&#34;backupSetInfo&#34;][&#34;commonBackupSet&#34;][&#34;isNasTurboBackupSet&#34;] = kwargs.get(&#39;is_nas_turbo_type&#39;,
                                                                                                 False)

        agent_settings = {
            &#39;db2&#39;: &#34;&#34;&#34;
request_json[&#39;backupSetInfo&#39;].update({
    &#39;db2BackupSet&#39;: {
        &#39;dB2DefaultIndexSP&#39;: {
            &#39;storagePolicyName&#39;: kwargs.get(&#39;storage_policy&#39;, &#39;&#39;)
        }
    }
})
            &#34;&#34;&#34;
        }

        exec(agent_settings.get(self._agent_object.agent_name, &#39;&#39;))

        if kwargs.get(&#39;plan_name&#39;):
            plan_entity_dict = {
                &#34;planName&#34;: kwargs.get(&#39;plan_name&#39;)
            }
            request_json[&#39;backupSetInfo&#39;][&#39;planEntity&#39;] = plan_entity_dict

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;ADD_BACKUPSET&#39;], request_json
        )

        if flag:
            if response.json():
                if &#39;response&#39; in response.json():
                    response_value = response.json()[&#39;response&#39;][0]
                    error_code = str(response_value[&#39;errorCode&#39;])
                    error_message = None

                    if &#39;errorString&#39; in response_value:
                        error_message = response_value[&#39;errorString&#39;]

                    if error_message:
                        o_str = &#39;Failed to create new backupset\nError: &#34;{0}&#34;&#39;.format(
                            error_message
                        )
                        raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, o_str)
                    else:
                        if error_code == &#39;0&#39;:
                            # initialize the backupsets again
                            # so the backupsets object has all the backupsets
                            self.refresh()

                            return self.get(backupset_name)

                        else:
                            o_str = (&#39;Failed to create new backupset with error code: &#34;{0}&#34;\n&#39;
                                     &#39;Please check the documentation for &#39;
                                     &#39;more details on the error&#39;).format(error_code)

                            raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, o_str)
                else:
                    error_code = response.json()[&#39;errorCode&#39;]
                    error_message = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Failed to create new backupset\nError: &#34;{0}&#34;&#39;.format(
                        error_message
                    )
                    raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def add_archiveset(self, archiveset_name, is_nas_turbo_backupset=False):
        &#34;&#34;&#34; 
        Adds a new archiveset to the agent. It is just a backupset but is mainly used for archive only items

        Args:
            archiveset_name     (str) -- name of new archiveset to add

            is_nas_turbo_backupset  (bool) -- True for NAS based client.
                default -   False
            
        Returns:
        object - instance of the Backupset class, if created successfully

        Raises:
            SDKException:
                if type of the archiveset name argument is not string

                if failed to create a archiveset

                if response is empty

                if response is not success

                if archiveset with same name already exists
                

        &#34;&#34;&#34;        
        if not (isinstance(archiveset_name, str)):
            raise SDKException(&#39;Backupset&#39;, &#39;101&#39;)
        else:
            archiveset_name = archiveset_name.lower()

        if self.has_backupset(archiveset_name):
            raise SDKException(&#39;archiveset_name&#39;, &#39;102&#39;, &#39;Archiveset &#34;{0}&#34; already exists.&#39;.format(archiveset_name))

        if self._agent_object.agent_id not in [&#39;29&#39;, &#39;33&#39;]:
            raise SDKException(&#39;Backupset&#39;, &#39;101&#39;, &#34;Archiveset is not applicable to this application type.&#34;)



        request_json = {
            &#34;backupSetInfo&#34;: {
                &#34;useContentFromPlan&#34;: False,
                &#34;planEntity&#34;: {},
                &#34;commonBackupSet&#34;: {
                    &#34;isNasTurboBackupSet&#34;: is_nas_turbo_backupset,
                    &#34;isArchivingEnabled&#34;: True,
                    &#34;isDefaultBackupSet&#34;: False
                },
                &#34;backupSetEntity&#34;: {
                    &#34;_type_&#34;: 6,
                    &#34;clientId&#34;: int(self._client_object.client_id),
                    &#34;backupsetName&#34;: archiveset_name,
                    &#34;applicationId&#34;: int(self._agent_object.agent_id)
                },
                &#34;subClientList&#34;: [
                    {
                        &#34;contentOperationType&#34;: 1,
                        &#34;fsSubClientProp&#34;: {
                            &#34;useGlobalFilters&#34;: 2,
                            &#34;forcedArchiving&#34;: True,
                            &#34;diskCleanupRules&#34;: {
                                &#34;enableArchivingWithRules&#34;: True,
                                &#34;diskCleanupFileTypes&#34;: {}
                            }
                        },
                        &#34;content&#34;: [
                            {
                                &#34;path&#34;: &#34;&#34;
                            }
                        ]
                    }
                ]
            }
        }
        
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;ADD_BACKUPSET&#39;], request_json
        )

        if flag:
            if response.json():
                if &#39;response&#39; in response.json():
                    response_value = response.json()[&#39;response&#39;][0]
                    error_code = str(response_value[&#39;errorCode&#39;])
                    error_message = None

                    if &#39;errorString&#39; in response_value:
                        error_message = response_value[&#39;errorString&#39;]

                    if error_message:
                        o_str = &#39;Failed to create new Archiveset\nError: &#34;{0}&#34;&#39;.format(
                            error_message
                        )
                        raise SDKException(&#39;Archiveset&#39;, &#39;102&#39;, o_str)
                    else:
                        if error_code == &#39;0&#39;:
                            # initialize the backupsets again
                            # so the backupsets object has all the backupsets
                            self.refresh()
                            return self.get(archiveset_name)
                        
                        else:
                            o_str = (&#39;Failed to create new Archiveset with error code: &#34;{0}&#34;\n&#39;
                                     &#39;Please check the documentation for &#39;
                                     &#39;more details on the error&#39;).format(error_code)

                            raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, o_str)
                else:
                    error_code = response.json()[&#39;errorCode&#39;]
                    error_message = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Failed to create new Archiveset\nError: &#34;{0}&#34;&#39;.format(
                        error_message
                    )
                    raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def add_v1_sharepoint_client(
            self,
            backupset_name,
            server_plan,
            client_name,
            **kwargs):
        &#34;&#34;&#34;
        for sharepoint v1 client creation is a backupset
        Adds a new Office 365 V1 Share Point Pseudo Client to the Commcell.

                Args:
                    backupset_name                 (str)   --  name of the new Sharepoint Pseudo Client

                    server_plan                 (str)   --  server_plan to associate with the client

                    client_name                 (str) -- the access node for which Pseudo Client will be created


                Kwargs :

                    tenant_url                  (str)   --  url of sharepoint tenant

                    azure_username              (str)   --  username of azure app

                    azure_secret                (str)   --  secret key of azure app

                    user_username        (str)   --  username of Sharepoint admin

                    user_password           (str)  -- password of Sharepoint admin

                    azure_app_id            (str)       --  azure app id for sharepoint online

                    azure_app_key_id        (str)       --  app key for sharepoint online

                    azure_directory_id    (str)   --  azure directory id for sharepoint online


                Returns:
                    object  -   instance of the Client class for this new client

                Raises:
                    SDKException:
                        if client with given name already exists

                        if index_server is not found

                        if server_plan is not found

                        if failed to add the client

                        if response is empty

                        if response is not success

        &#34;&#34;&#34;
        if self.has_backupset(backupset_name):
            raise SDKException(
                &#39;Backupset&#39;, &#39;102&#39;, &#39;Backupset &#34;{0}&#34; already exists.&#39;.format(backupset_name))
        if self._commcell_object.plans.has_plan(server_plan):
            server_plan_object = self._commcell_object.plans.get(server_plan)
            server_plan_dict = {
                &#34;planId&#34;: int(server_plan_object.plan_id)
            }
        else:
            raise SDKException(&#39;Backupset&#39;, &#39;102&#39;)
        backup_set = {
            &#34;_type_&#34;: 6,
            &#34;applicationId&#34;: 78,
            &#34;backupsetName&#34;: backupset_name,
            &#34;clientId&#34;: int(self._client_object.client_id)
        }
        request_json = {
            &#34;backupSetInfo&#34;: {
                &#34;planEntity&#34;: server_plan_dict,
                &#34;backupSetEntity&#34;: backup_set,
                &#34;sharepointBackupSet&#34;: {
                    &#34;sharepointBackupSetType&#34;: 4
                }
            }
        }
        tenant_url = kwargs.get(&#39;tenant_url&#39;)
        user_username = kwargs.get(&#39;user_username&#39;)
        is_modern_auth_enabled = kwargs.get(&#39;is_modern_auth_enabled&#39;,False)
        azure_secret = b64encode(kwargs.get(&#39;azure_secret&#39;).encode()).decode()
        azure_app_key_id = b64encode(kwargs.get(&#39;azure_app_key_id&#39;).encode()).decode()
        user_password = b64encode(kwargs.get(&#39;user_password&#39;).encode()).decode()
        request_json[&#34;backupSetInfo&#34;][&#34;sharepointBackupSet&#34;][
            &#34;spOffice365BackupSetProp&#34;] = {
            &#34;azureUserAccount&#34;: kwargs.get(&#39;azure_username&#39;),
            &#34;azureAccountKey&#34;: azure_secret,
            &#34;tenantUrlItem&#34;: tenant_url,
            &#34;isModernAuthEnabled&#34;: is_modern_auth_enabled,
            &#34;office365Credentials&#34;: {
                &#34;userName&#34;: user_username,
                &#34;password&#34;: user_password
            },
        }
        if is_modern_auth_enabled:
            request_json[&#34;backupSetInfo&#34;][&#34;sharepointBackupSet&#34;][
                &#34;spOffice365BackupSetProp&#34;][&#34;azureAppList&#34;] = {
                &#34;azureApps&#34;: [
                    {
                        &#34;azureAppId&#34;: kwargs.get(&#39;azure_app_id&#39;),
                        &#34;azureAppKeyValue&#34;: azure_app_key_id,
                        &#34;azureDirectoryId&#34;: kwargs.get(&#39;azure_directory_id&#39;)
                    }
                ]
            }

        self._process_add_response(backupset_name, request_json)

    def add_salesforce_backupset(
            self,
            salesforce_options,
            db_options=None, **kwargs):
        &#34;&#34;&#34;Adds a new Salesforce Backupset to the Commcell.

            Args:

                salesforce_options         (dict)       --  salesforce options
                                                            {
                                                                &#34;salesforce_user_name&#34;: &#39;salesforce login user&#39;,
                                                                &#34;salesforce_user_password&#34;: &#39;salesforce user password&#39;,
                                                                &#34;salesforce_user_token&#34;: &#39;salesforce user token&#39;
                                                            }

                db_options                 (dict)       --  database options to configure sync db
                                                            {
                                                                &#34;db_enabled&#34;: &#39;True or False&#39;,
                                                                &#34;db_type&#34;: &#39;SQLSERVER or POSTGRESQL&#39;,
                                                                &#34;db_host_name&#34;: &#39;database hostname&#39;,
                                                                &#34;db_instance&#34;: &#39;database instance name&#39;,
                                                                &#34;db_name&#34;: &#39;database name&#39;,
                                                                &#34;db_port&#34;: &#39;port of the database&#39;,
                                                                &#34;db_user_name&#34;: &#39;database user name&#39;,
                                                                &#34;db_user_password&#34;: &#39;database user password&#39;
                                                            }

                **kwargs                   (dict)       --     dict of keyword arguments as follows

                                                            download_cache_path     (str)   -- download cache path
                                                            mutual_auth_path        (str)   -- mutual auth cert path
                                                            storage_policy          (str)   -- storage policy
                                                            streams                 (int)   -- number of streams

            Returns:
                object  -   instance of the Backupset class for this new backupset

            Raises:
                SDKException:
                    if backupset with given name already exists

                    if failed to add the backupset

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;

        if db_options is None:
            db_options = {&#39;db_enabled&#39;: False}
        if self.has_backupset(salesforce_options.get(&#39;salesforce_user_name&#39;)):
            raise SDKException(&#39;Backupset&#39;, &#39;102&#39;,
                               &#39;Backupset &#34;{0}&#34; already exists.&#39;.format(salesforce_options.get(&#39;salesforce_user_name&#39;)))

        salesforce_password = b64encode(salesforce_options.get(&#39;salesforce_user_password&#39;).encode()).decode()
        salesforce_token = b64encode(salesforce_options.get(&#39;salesforce_user_token&#39;, &#39;&#39;).encode()).decode()
        db_user_password = &#34;&#34;
        if db_options.get(&#39;db_enabled&#39;, False):
            db_user_password = b64encode(db_options.get(&#39;db_user_password&#39;).encode()).decode()

        request_json = {
            &#34;backupSetInfo&#34;: {
                &#34;backupSetEntity&#34;: {
                    &#34;clientName&#34;: self._client_object.client_name,
                    &#34;instanceName&#34;: self._instance_object.instance_name,
                    &#34;backupsetName&#34;: salesforce_options.get(&#39;salesforce_user_name&#39;),
                    &#34;appName&#34;: self._agent_object.agent_name
                },
                &#34;cloudAppsBackupset&#34;: {
                    &#34;instanceType&#34;: 3,
                    &#34;salesforceBackupSet&#34;: {
                        &#34;enableREST&#34;: True,
                        &#34;downloadCachePath&#34;: kwargs.get(&#39;download_cache_path&#39;, &#39;/tmp&#39;),
                        &#34;mutualAuthPath&#34;: kwargs.get(&#39;mutual_auth_path&#39;, &#39;&#39;),
                        &#34;token&#34;: salesforce_token,
                        &#34;userPassword&#34;: {
                            &#34;userName&#34;: salesforce_options.get(&#39;salesforce_user_name&#39;),
                            &#34;password&#34;: salesforce_password,
                        },
                        &#34;syncDatabase&#34;: {
                            &#34;dbEnabled&#34;: db_options.get(&#39;db_enabled&#39;, False),
                            &#34;dbPort&#34;: db_options.get(&#39;db_port&#39;, &#39;1433&#39;),
                            &#34;dbInstance&#34;: db_options.get(&#39;db_instance&#39;, &#39;&#39;),
                            &#34;dbName&#34;: db_options.get(&#39;db_name&#39;, self._instance_object.instance_name),
                            &#34;dbType&#34;: db_options.get(&#39;db_type&#39;, &#34;SQLSERVER&#34;),
                            &#34;dbHost&#34;: db_options.get(&#39;db_host_name&#39;, &#39;&#39;),
                            &#34;dbUserPassword&#34;: {
                                &#34;userName&#34;: db_options.get(&#39;db_user_name&#39;, &#39;&#39;),
                                &#34;password&#34;: db_user_password,

                            },
                        },
                    },
                    &#34;generalCloudProperties&#34;: {
                        &#34;numberOfBackupStreams&#34;: kwargs.get(&#39;streams&#39;, 2),
                        &#34;storageDevice&#34;: {
                            &#34;dataBackupStoragePolicy&#34;: {
                                &#34;storagePolicyName&#34;: kwargs.get(&#39;storage_policy&#39;, &#39;&#39;)
                            },
                        },
                    },
                },
            },
        }

        self._process_add_response(salesforce_options.get(&#39;salesforce_user_name&#39;), request_json)

    def get(self, backupset_name):
        &#34;&#34;&#34;Returns a backupset object of the specified backupset name.

            Args:
                backupset_name (str)  --  name of the backupset

            Returns:
                object - instance of the Backupset class for the given backupset name

            Raises:
                SDKException:
                    if type of the backupset name argument is not string

                    if no backupset exists with the given name
        &#34;&#34;&#34;
        if not isinstance(backupset_name, str):
            raise SDKException(&#39;Backupset&#39;, &#39;101&#39;)
        else:
            backupset_name = backupset_name.lower()

            if self.has_backupset(backupset_name):
                if self._instance_object is None:
                    self._instance_object = self._agent_object.instances.get(
                        self._backupsets[backupset_name][&#39;instance&#39;]
                    )
                return Backupset(
                    self._instance_object,
                    backupset_name,
                    self._backupsets[backupset_name][&#34;id&#34;]
                )

            raise SDKException(
                &#39;Backupset&#39;, &#39;102&#39;, &#39;No backupset exists with name: &#34;{0}&#34;&#39;.format(backupset_name)
            )

    def delete(self, backupset_name):
        &#34;&#34;&#34;Deletes the backup set from the agent.

            Args:
                backupset_name (str)  --  name of the backupset

            Raises:
                SDKException:
                    if type of the backupset name argument is not string

                    if failed to delete the backupset

                    if response is empty

                    if response is not success

                    if no backupset exists with the given name
        &#34;&#34;&#34;
        if not isinstance(backupset_name, str):
            raise SDKException(&#39;Backupset&#39;, &#39;101&#39;)
        else:
            backupset_name = backupset_name.lower()

        if self.has_backupset(backupset_name):
            delete_backupset_service = self._services[&#39;BACKUPSET&#39;] % (
                self._backupsets[backupset_name][&#39;id&#39;]
            )

            flag, response = self._cvpysdk_object.make_request(&#39;DELETE&#39;, delete_backupset_service)

            if flag:
                if response.json():
                    if &#39;response&#39; in response.json():
                        response_value = response.json()[&#39;response&#39;][0]
                        error_code = str(response_value[&#39;errorCode&#39;])
                        error_message = None

                        if &#39;errorString&#39; in response_value:
                            error_message = response_value[&#39;errorString&#39;]

                        if error_message:
                            o_str = &#39;Failed to delete backupset\nError: &#34;{0}&#34;&#39;
                            raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, o_str.format(error_message))
                        else:
                            if error_code == &#39;0&#39;:
                                # initialize the backupsets again
                                # so the backupsets object has all the backupsets
                                self.refresh()
                            else:
                                o_str = (&#39;Failed to delete backupset with error code: &#34;{0}&#34;\n&#39;
                                         &#39;Please check the documentation for &#39;
                                         &#39;more details on the error&#39;).format(error_code)
                                raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, o_str)
                    else:
                        error_code = response.json()[&#39;errorCode&#39;]
                        error_message = response.json()[&#39;errorMessage&#39;]
                        o_str = &#39;Failed to delete backupset\nError: &#34;{0}&#34;&#39;.format(error_message)
                        raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, o_str)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
        else:
            raise SDKException(
                &#39;Backupset&#39;, &#39;102&#39;, &#39;No backupset exists with name: &#34;{0}&#34;&#39;.format(backupset_name)
            )

    def refresh(self):
        &#34;&#34;&#34;Refresh the backupsets associated with the Agent / Instance.&#34;&#34;&#34;
        self._backupsets = self._get_backupsets()

    @property
    def default_backup_set(self):
        &#34;&#34;&#34;Returns the name of the default backup set for the selected Client and Agent.&#34;&#34;&#34;
        return self._default_backup_set


class Backupset(object):
    &#34;&#34;&#34;Class for performing backupset operations for a specific backupset.&#34;&#34;&#34;

    def __new__(cls, instance_object, backupset_name, backupset_id=None):
        &#34;&#34;&#34;Class composition for CV backupsets&#34;&#34;&#34;
        from .backupsets.fsbackupset import FSBackupset
        from .backupsets.nasbackupset import NASBackupset
        from .backupsets.hanabackupset import HANABackupset
        from .backupsets.cabackupset import CloudAppsBackupset
        from .backupsets.postgresbackupset import PostgresBackupset
        from .backupsets.adbackupset import ADBackupset
        from .backupsets.db2backupset import DB2Backupset
        from .backupsets.vsbackupset import VSBackupset
        from .backupsets.aadbackupset import AzureAdBackupset
        from .backupsets.sharepointbackupset import SharepointBackupset

        _backupsets_dict = {
            &#39;file system&#39;: FSBackupset,
            &#39;nas&#39;: NASBackupset,        # SP11 or lower CS honors NAS as the Agent Name
            &#39;ndmp&#39;: NASBackupset,       # SP12 and above honors NDMP as the Agent Name
            &#39;sap hana&#39;: HANABackupset,
            &#39;cloud apps&#39;: CloudAppsBackupset,
            &#39;postgresql&#39;: PostgresBackupset,
            &#34;active directory&#34;: ADBackupset,
            &#39;db2&#39;: DB2Backupset,
            &#39;virtual server&#39;: VSBackupset,
            &#34;azure ad&#34;: AzureAdBackupset,
            &#39;sharepoint server&#39;: SharepointBackupset
        }

        if instance_object._agent_object.agent_name in _backupsets_dict.keys():
            _class = _backupsets_dict.get(instance_object._agent_object.agent_name, cls)
            if _class.__new__ == cls.__new__:
                return object.__new__(_class)
            return _class.__new__(_class, instance_object, backupset_name, backupset_id)
        else:
            return object.__new__(cls)

    def __init__(self, instance_object, backupset_name, backupset_id=None):
        &#34;&#34;&#34;Initialise the backupset object.

            Args:
                instance_object     (object)  --  instance of the Instance class

                backupset_name      (str)     --  name of the backupset

                backupset_id        (str)     --  id of the backupset
                    default: None

            Returns:
                object - instance of the Backupset class
        &#34;&#34;&#34;
        self._instance_object = instance_object
        self._agent_object = self._instance_object._agent_object
        self._client_object = self._agent_object._client_object

        self._commcell_object = self._agent_object._commcell_object

        self._cvpysdk_object = self._commcell_object._cvpysdk_object
        self._services = self._commcell_object._services
        self._update_response_ = self._commcell_object._update_response_

        # self._restore_methods = [&#39;_process_restore_response&#39;, &#39;_filter_paths&#39;, &#39;_restore_json&#39;]
        self._restore_methods = [
            &#39;_process_restore_response&#39;,
            &#39;_filter_paths&#39;,
            &#39;_process_search_response&#39;,
            &#39;_restore_json&#39;,
            &#39;_impersonation_json&#39;,
            &#39;_restore_browse_option_json&#39;,
            &#39;_restore_common_options_json&#39;,
            &#39;_restore_destination_json&#39;,
            &#39;_restore_fileoption_json&#39;,
            &#39;_json_restore_subtask&#39;
        ]

        self._restore_options_json = [
            &#39;_impersonation_json_&#39;,
            &#39;_browse_restore_json&#39;,
            &#39;_destination_restore_json&#39;,
            &#39;_commonoption_restore_json&#39;,
            &#39;_fileoption_restore_json&#39;,
        ]

        self._backupset_name = backupset_name.split(&#39;\\&#39;)[-1].lower()
        self._description = None

        if backupset_id:
            # Use the backupset id provided in the arguments
            self._backupset_id = str(backupset_id)
        else:
            # Get the id associated with this backupset
            self._backupset_id = self._get_backupset_id()

        self._BACKUPSET = self._services[&#39;BACKUPSET&#39;] % (self.backupset_id)
        self._BROWSE = self._services[&#39;BROWSE&#39;]
        self._RESTORE = self._services[&#39;RESTORE&#39;]

        self._is_default = False
        self._is_on_demand_backupset = False
        self._properties = None
        self._backupset_association = {}
        self._plan_name = None
        self._plan_obj = None

        self.subclients = None
        self.schedules = None
        self._hidden_subclient = None
        self.refresh()

        self._default_browse_options = {
            &#39;operation&#39;: &#39;browse&#39;,
            &#39;show_deleted&#39;: False,
            &#39;from_time&#39;: 0,  # value should either be the Epoch time or the Timestamp
            &#39;to_time&#39;: 0,  # value should either be the Epoch time or the Timestamp
            &#39;path&#39;: &#39;\\&#39;,
            &#39;copy_precedence&#39;: 0,
            &#39;media_agent&#39;: &#39;&#39;,
            &#39;page_size&#39;: 100000,
            &#39;skip_node&#39;: 0,
            &#39;restore_index&#39;: True,
            &#39;vm_disk_browse&#39;: False,
            &#39;filters&#39;: [],
            &#39;job_id&#39;: 0,
            &#39;commcell_id&#39;: self._commcell_object.commcell_id,
            &#39;include_aged_data&#39;: False,
            &#39;include_meta_data&#39;:False,
            &#39;include_hidden&#39;: False,
            &#39;include_running_jobs&#39;: False,
            &#39;compute_folder_size&#39;: False,
            &#39;vs_volume_browse&#39;: False,
            &#39;browse_view_name&#39;: &#39;VOLUMEVIEW&#39;,
            &#39;compare_backups_req&#39;: 0,
            &#39;comparison_job_id&#39;: 0,

            &#39;_subclient_id&#39;: 0,
            &#39;_raw_response&#39;: False,
            &#39;_custom_queries&#39;: False
        }

    def __getattr__(self, attribute):
        &#34;&#34;&#34;Returns the persistent attributes&#34;&#34;&#34;
        if attribute in self._restore_methods:
            return getattr(self._instance_object, attribute)
        elif attribute in self._restore_options_json:
            return getattr(self._instance_object, attribute)

        return super(Backupset, self).__getattribute__(attribute)

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        representation_string = (&#39;Backupset class instance for Backupset: &#34;{0}&#34; &#39;
                                 &#39;for Instance: &#34;{1}&#34; of Agent: &#34;{2}&#34;&#39;)
        return representation_string.format(
            self.backupset_name,
            self._instance_object.instance_name,
            self._agent_object.agent_name
        )

    def _get_backupset_id(self):
        &#34;&#34;&#34;Gets the backupset id associated with this backupset.

            Returns:
                str - id associated with this backupset
        &#34;&#34;&#34;
        backupsets = Backupsets(self._instance_object)
        return backupsets.get(self.backupset_name).backupset_id

    def _get_backupset_properties(self):
        &#34;&#34;&#34;Gets the properties of this backupset.

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._BACKUPSET)

        if flag:
            if response.json() and &#34;backupsetProperties&#34; in response.json():
                self._properties = response.json()[&#34;backupsetProperties&#34;][0]

                backupset_name = self._properties[&#34;backupSetEntity&#34;][&#34;backupsetName&#34;]
                self._backupset_name = backupset_name.lower()

                self._backupset_association = self._properties[&#39;backupSetEntity&#39;]

                self._is_default = bool(self._properties[&#34;commonBackupSet&#34;][&#34;isDefaultBackupSet&#34;])

                if &#39;commonBackupSet&#39; in self._properties:
                    if &#39;onDemandBackupset&#39; in self._properties[&#39;commonBackupSet&#39;]:
                        self._is_on_demand_backupset = bool(
                            self._properties[&#39;commonBackupSet&#39;][&#39;onDemandBackupset&#39;]
                        )

                if &#34;userDescription&#34; in self._properties[&#34;commonBackupSet&#34;]:
                    self._description = self._properties[&#34;commonBackupSet&#34;][&#34;userDescription&#34;]

                if &#34;planName&#34; in self._properties[&#34;planEntity&#34;]:
                    self._plan_name = self._properties[&#34;planEntity&#34;][&#34;planName&#34;]
                else:
                    self._plan_name = None
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _run_backup(self, subclient_name, return_list, **kwargs):
        &#34;&#34;&#34;Triggers backup job for the given subclient, and appends its Job object to the list.
            Backup job is started only when backup activity is enabled and storage policy is set for it.

            The SDKException class instance is appended to the list,
            if any exception is raised while running the backup job for the Subclient.

            Args:
                subclient_name (str)   --  name of the subclient to trigger the backup for

                return_list    (list)  --  list to append the job object to

            Kwargs:
                All arguments used by subclient.backup() can be used here. Commonly used arguments are

                backup_level   (str)   --  The type of backup to run

                advanced_options   (dict)  --  advanced backup options to be included while
                                    making the request

            Returns:
                None

        &#34;&#34;&#34;
        try:
            subclient = self.subclients.get(subclient_name)
            if subclient.is_backup_enabled and subclient.storage_policy is not None:
                job = subclient.backup(**kwargs)
                return_list.append(job)
                time.sleep(2)  # Staggering the next backup job to be started
        except SDKException as excp:
            return_list.append(excp)

    def _process_update_reponse(self, request_json):
        &#34;&#34;&#34;Runs the Backupset update API with the request JSON provided,
            and returns the contents after parsing the response.

            Args:
                request_json    (dict)  --  JSON request to run for the API

            Returns:
                (bool, str, str):
                    bool -  flag specifies whether success / failure

                    str  -  error code received in the response

                    str  -  error message received

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._BACKUPSET, request_json)

        self._get_backupset_properties()

        if flag:
            if response.json() and &#34;response&#34; in response.json():
                error_code = str(response.json()[&#34;response&#34;][0][&#34;errorCode&#34;])

                if error_code == &#34;0&#34;:
                    return True, &#34;0&#34;, &#34;&#34;
                else:
                    error_string = &#34;&#34;

                    if &#34;errorString&#34; in response.json()[&#34;response&#34;][0]:
                        error_string = response.json()[&#34;response&#34;][0][&#34;errorString&#34;]

                    if error_string:
                        return False, error_code, error_string
                    else:
                        return False, error_code, &#34;&#34;
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _update(self, backupset_name, backupset_description, default_backupset):
        &#34;&#34;&#34;Updates the properties of the backupset.

            Args:
                backupset_name        (str)   --  new name of the backupset

                backupset_description (str)   --  description of the backupset

                default_backupset     (bool)  --  default backupset property

            Returns:
                (bool, str, str):
                    bool -  flag specifies whether success / failure

                    str  -  error code received in the response

                    str  -  error message received

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;

        request_json = {
            &#34;association&#34;: {
                &#34;entity&#34;: [{
                    &#34;clientName&#34;: self._client_object.client_name,
                    &#34;appName&#34;: self._agent_object.agent_name,
                    &#34;instanceName&#34;: self._instance_object.instance_name,
                    &#34;backupsetName&#34;: self.backupset_name
                }]
            },
            &#34;backupsetProperties&#34;: {
                &#34;commonBackupSet&#34;: {
                    &#34;newBackupSetName&#34;: backupset_name,
                    &#34;isDefaultBackupSet&#34;: default_backupset
                }
            }
        }

        if backupset_description is not None:
            request_json[&#34;backupsetProperties&#34;][&#34;commonBackupSet&#34;][
                &#34;userDescription&#34;] = backupset_description

        return self._process_update_reponse(request_json)

    @staticmethod
    def _get_epoch_time(timestamp):
        &#34;&#34;&#34;Returns the Epoch time given the input time is in format %Y-%m-%d %H:%M:%S.

            Args:
                timestamp   (int / str)     --  value should either be the Epoch time or, the
                                                    Timestamp of the format %Y-%m-%d %H:%M:%S

            Returns:
                int - epoch time converted from the input timestamp

            Raises:
                SDKException:
                    if the input timestamp is not of correct format
        &#34;&#34;&#34;
        if str(timestamp) == &#39;0&#39;:
            return 0

        try:
            # return the timestamp value in int type
            return int(timestamp)
        except ValueError:
            # if not convertible to int, then convert the timestamp input to Epoch time
            try:
                return int(time.mktime(time.strptime(timestamp, &#34;%Y-%m-%d %H:%M:%S&#34;)))
            except Exception:
                raise SDKException(&#39;Subclient&#39;, &#39;106&#39;)

    def _set_defaults(self, final_dict, defaults_dict):
        &#34;&#34;&#34;Iterates over the defaults_dict, and adds the default value to the final_dict,
            for the key which is not present in the final dict.

            Recursively sets default values on the final_dict dictionary.

            Args:
                final_dict      (dict)  --  the dictionary to be set with defaults, and to be used
                                                to generate the Browse / Find JSON

                defaults_dict   (dict)  --  the dictionary with default values

            Returns:
                None
        &#34;&#34;&#34;
        for key in defaults_dict:
            if key not in final_dict:
                final_dict[key] = defaults_dict[key]

            if isinstance(defaults_dict[key], dict):
                self._set_defaults(final_dict[key], defaults_dict[key])

    def _prepare_browse_options(self, options):
        &#34;&#34;&#34;Prepares the options for the Browse/find operation.

            Args:
                options     (dict)  --  a dictionary of browse options

            Returns:
                dict - The browse options with all the default options set
        &#34;&#34;&#34;
        self._set_defaults(options, self._default_browse_options)
        return options

    def _prepare_browse_json(self, options):
        &#34;&#34;&#34;Prepares the JSON object for the browse request.

            Args:
                options     (dict)  --  the browse options dictionary

            Returns:
                dict - A JSON object for the browse response
        &#34;&#34;&#34;
        operation_types = {
            &#39;browse&#39;: 0,
            &#39;find&#39;: 1,
            &#39;all_versions&#39;: 2,
            &#39;list_media&#39;: 3,
            &#39;delete_data&#39;: 7
        }

        options[&#39;operation&#39;] = options[&#39;operation&#39;].lower()

        if options[&#39;operation&#39;] not in operation_types:
            options[&#39;operation&#39;] = &#39;find&#39;

        # add the browse mode value here, if it is different for an agent
        # if agent is not added in the dict, default value 2 will be used
        browse_mode = {
            &#39;virtual server&#39;: 4,
            &#39;cloud apps&#39;: 3,
            &#39;azure ad&#39;: 3
        }

        mode = 2
        paths = []

        if isinstance(options[&#39;path&#39;], str):
            paths.append(options[&#39;path&#39;])
        elif isinstance(options[&#39;path&#39;], list):
            paths = options[&#39;path&#39;]
        else:
            paths = [&#39;\\&#39;]

        if self._agent_object.agent_name in browse_mode:
            mode = browse_mode[self._agent_object.agent_name]

        request_json = {
            &#34;opType&#34;: operation_types[options[&#39;operation&#39;]],
            &#34;mode&#34;: {
                &#34;mode&#34;: mode
            },
            &#34;paths&#34;: [{&#34;path&#34;: path} for path in paths],
            &#34;options&#34;: {
                &#34;showDeletedFiles&#34;: options.get(&#39;show_deleted&#39;, False),
                &#34;restoreIndex&#34;: options[&#39;restore_index&#39;],
                &#34;vsDiskBrowse&#34;: options[&#39;vm_disk_browse&#39;],
                &#34;vsFileBrowse&#34;: options.get(&#39;vs_file_browse&#39;, False),
                &#34;includeMetadata&#34;: options.get(&#39;include_meta_data&#39;, False),
                &#34;hideUserHidden&#34;: options.get(&#39;hide_user_hidden&#39;, False)
            },
            &#34;entity&#34;: {
                &#34;clientName&#34;: self._client_object.client_name,
                &#34;clientId&#34;: int(self._client_object.client_id),
                &#34;applicationId&#34;: int(self._agent_object.agent_id),
                &#34;instanceId&#34;: int(self._instance_object.instance_id),
                &#34;backupsetId&#34;: int(self.backupset_id),
                &#34;subclientId&#34;: int(options[&#39;_subclient_id&#39;])
            },
            &#34;timeRange&#34;: {
                &#34;fromTime&#34;: self._get_epoch_time(options[&#39;from_time&#39;]),
                &#34;toTime&#34;: self._get_epoch_time(options[&#39;to_time&#39;])
            },
            &#34;advOptions&#34;: {
                &#34;copyPrecedence&#34;: int(options[&#39;copy_precedence&#39;])
            },
            &#34;ma&#34;: {
                &#34;clientName&#34;: options[&#39;media_agent&#39;]
            },
            &#34;queries&#34;: [{
                &#34;type&#34;: 0,
                &#34;queryId&#34;: &#34;dataQuery&#34;,
                &#34;dataParam&#34;: {
                    &#34;sortParam&#34;: {
                        &#34;ascending&#34;: False,
                        &#34;sortBy&#34;: [0]
                    },
                    &#34;paging&#34;: {
                        &#34;pageSize&#34;: int(options[&#39;page_size&#39;]),
                        &#34;skipNode&#34;: int(options[&#39;skip_node&#39;]),
                        &#34;firstNode&#34;: 0
                    }
                }
            }]
        }

        if options[&#39;filters&#39;]:
            # [(&#39;FileName&#39;, &#39;*.txt&#39;), (&#39;FileSize&#39;,&#39;GT&#39;,&#39;100&#39;)]
            request_json[&#39;queries&#39;][0][&#39;whereClause&#39;] = []

            for browse_filter in options[&#39;filters&#39;]:
                if browse_filter[0] in (&#39;FileName&#39;, &#39;FileSize&#39;):
                    temp_dict = {
                        &#39;connector&#39;: 0,
                        &#39;criteria&#39;: {
                            &#39;field&#39;: browse_filter[0],
                            &#39;values&#39;: [browse_filter[1]]
                        }
                    }

                    if browse_filter[0] == &#39;FileSize&#39;:
                        temp_dict[&#39;criteria&#39;][&#39;dataOperator&#39;] = browse_filter[2]

                    request_json[&#39;queries&#39;][0][&#39;whereClause&#39;].append(temp_dict)

        if options[&#39;job_id&#39;] != 0:
            request_json[&#39;advOptions&#39;][&#39;advConfig&#39;] = {
                &#39;browseAdvancedConfigBrowseByJob&#39;: {
                    &#39;commcellId&#39;: options[&#39;commcell_id&#39;],
                    &#39;jobId&#39;: options[&#39;job_id&#39;]
                }
            }

        if options[&#39;include_aged_data&#39;]:
            request_json[&#39;options&#39;][&#39;includeAgedData&#39;] = True

        if options[&#39;include_meta_data&#39;]:
            request_json[&#39;options&#39;][&#39;includeMetadata&#39;] = True

        if options[&#39;include_hidden&#39;]:
            request_json[&#39;options&#39;][&#39;includeHidden&#39;] = True

        if options[&#39;include_running_jobs&#39;]:
            request_json[&#39;options&#39;][&#39;includeRunningJobs&#39;] = True

        if options[&#39;compute_folder_size&#39;]:
            request_json[&#39;options&#39;][&#39;computeFolderSizeForFilteredBrowse&#39;] = True

        if options[&#39;vs_volume_browse&#39;]:
            request_json[&#39;mode&#39;][&#39;mode&#39;] = 3
            request_json[&#39;options&#39;][&#39;vsVolumeBrowse&#39;] = True
            request_json[&#39;advOptions&#39;][&#39;browseViewName&#39;] = options[&#39;browse_view_name&#39;]

        if options[&#39;operation&#39;] == &#39;list_media&#39;:
            request_json[&#39;options&#39;][&#39;doPrediction&#39;] = True

        if options[&#39;_custom_queries&#39;]:
            request_json[&#39;queries&#39;] = options[&#39;_custom_queries&#39;]

        if options.get(&#39;live_browse&#39;, False):
            request_json[&#39;options&#39;][&#39;liveBrowse&#39;] = True

        if options.get(&#39;compare_backups_req&#39;, 0) != 0:
            request_json[&#39;mode&#39;][&#39;mode&#39;] = 3
            request_json[&#39;advOptions&#39;][&#39;advConfig&#39;] = {
                &#39;compareBackupsReqType&#39;: int(options.get(&#39;compare_backups_req&#39;))
            }
            if options.get(&#39;comparison_job_id&#39;, 0) != 0:
                request_json[&#39;applicationInfo&#39;] = {
                    &#39;indexingInfo&#39;: {
                        &#39;jobId&#39;: int(options.get(&#39;comparison_job_id&#39;))
                    }
                }

        return request_json

    def _process_browse_all_versions_response(self, result_set, options):
        &#34;&#34;&#34;Retrieves the items from browse response.

        Args:
            result_set  (list of dict)  --  browse response dict obtained from server
            options     (dict)          --  The browse options dictionary
                                                                                        {
                                                                                                &#34;show_deleted&#34;: True,
                                                                                        }

        Returns:
            dict - Dictionary of the specified file with list of all the file versions and
                    additional metadata retrieved from browse

        Raises:
            SDKException:
                if failed to browse/search for content

                if response is empty

                if response is not success
        &#34;&#34;&#34;
        path = None
        versions_list = []
        show_deleted = options.get(&#39;show_deleted&#39;, False)

        for result in result_set:
            name = result[&#39;displayName&#39;]
            path = result[&#39;path&#39;]

            if &#39;modificationTime&#39; in result:
                mod_time = time.localtime(int(result[&#39;modificationTime&#39;]))
                mod_time = time.strftime(&#39;%d/%m/%Y %H:%M:%S&#39;, mod_time)
            else:
                mod_time = None
            
            if &#39;backupTime&#39; in result[&#39;advancedData&#39;] and int(result[&#39;advancedData&#39;][&#39;backupTime&#39;]) &gt; 0:
                bkp_time = time.localtime(int(result[&#39;advancedData&#39;][&#39;backupTime&#39;]))
                bkp_time = time.strftime(&#39;%d/%m/%Y %H:%M:%S&#39;, bkp_time)
            else:
                bkp_time = None

            if &#39;file&#39; in result[&#39;flags&#39;]:
                if result[&#39;flags&#39;][&#39;file&#39;] in (True, &#39;1&#39;):
                    file_or_folder = &#39;File&#39;
                else:
                    file_or_folder = &#39;Folder&#39;
            else:
                file_or_folder = &#39;Folder&#39;

            if &#39;size&#39; in result:
                size = result[&#39;size&#39;]
            else:
                size = None

            if &#39;version&#39; in result:
                version = result[&#39;version&#39;]
            else:
                version = None

            if show_deleted and &#39;deleted&#39; in result.get(&#39;flags&#39;):
                deleted = True if result[&#39;flags&#39;].get(&#39;deleted&#39;) in (True, &#39;1&#39;) else False
            else:
                deleted = None
                    
            paths_dict = {
                &#39;name&#39;: name,
                &#39;version&#39;: version,
                &#39;size&#39;: size,
                &#39;modified_time&#39;: mod_time,
                &#39;type&#39;: file_or_folder,
                &#39;backup_time&#39;: bkp_time,
                &#39;advanced_data&#39;: result[&#39;advancedData&#39;],
                &#39;deleted&#39;: deleted
            }

            versions_list.append(paths_dict)

        all_versions_dict = dict()
        all_versions_dict[path] = versions_list

        return all_versions_dict

    def _process_browse_response(self, flag, response, options):
        &#34;&#34;&#34;Retrieves the items from browse response.

        Args:
            flag        (bool)  --  boolean, whether the response was success or not

            response    (dict)  --  JSON response received for the request from the Server

            options     (dict)  --  The browse options dictionary

        Returns:
            list - List of only the file / folder paths from the browse response

            dict - Dictionary of all the paths with additional metadata retrieved from browse

        Raises:
            SDKException:
                if failed to browse/search for content

                if response is empty

                if response is not success
        &#34;&#34;&#34;

        operation_types = {
            &#34;browse&#34;: (&#39;110&#39;, &#39;Failed to browse for subclient backup content\nError: &#34;{0}&#34;&#39;),
            &#34;find&#34;: (&#39;111&#39;, &#39;Failed to Search\nError: &#34;{0}&#34;&#39;),
            &#34;all_versions&#34;: (
                &#39;112&#39;, &#39;Failed to browse all version for specified content\nError: &#34;{0}&#34;&#39;
            ),
            &#34;delete_data&#34;: (
                &#39;113&#39;, &#39;Failed to perform delete data operation for given content\nError: &#34;{0}&#34;&#39;
            ),
            &#34;list_media&#34;: (
                &#39;113&#39;, &#39;Failed to perform list media operation for given content\nError: &#34;{0}&#34;&#39;
            )
        }

        exception_code = operation_types[options[&#39;operation&#39;]][0]
        exception_message = operation_types[options[&#39;operation&#39;]][1]
        
        show_deleted = options.get(&#39;show_deleted&#39;, False)

        if flag:

            response_json = response.json()
            paths_dict = {}
            paths = []
            result_set = None
            browse_result = None

            # Send raw result as browse response for advanced use cases
            if options[&#39;_raw_response&#39;]:
                return [], response_json

            if response_json and &#39;browseResponses&#39; in response_json:
                _browse_responses = response_json[&#39;browseResponses&#39;]
                for browse_response in _browse_responses:
                    if &#34;browseResult&#34; in browse_response:
                        browse_result = browse_response[&#39;browseResult&#39;]
                        if &#39;dataResultSet&#39; in browse_result:
                            result_set = browse_result[&#39;dataResultSet&#39;]
                            break

                if not browse_result:
                    try:
                        message = response_json[&#39;browseResponses&#39;][0][&#39;messages&#39;][0]
                        error_message = message[&#39;errorMessage&#39;]

                        o_str = exception_message
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str.format(error_message))
                    except KeyError:
                        return [], {}

                if not result_set:
                    raise SDKException(&#39;Subclient&#39;, exception_code)

                if not isinstance(result_set, list):
                    result_set = [result_set]

                if &#39;all_versions&#39; in options[&#39;operation&#39;]:
                    return self._process_browse_all_versions_response(result_set, options)

                for result in result_set:
                    name = result.get(&#39;displayName&#39;)
                    snap_display_name = result.get(&#39;name&#39;)

                    if &#39;path&#39; in result:
                        path = result[&#39;path&#39;]
                    else:
                        path = &#39;\\&#39;.join([options[&#39;path&#39;], name])

                    if &#39;modificationTime&#39; in result and int(result[&#39;modificationTime&#39;]) &gt; 0:
                        mod_time = time.localtime(int(result[&#39;modificationTime&#39;]))
                        mod_time = time.strftime(&#39;%d/%m/%Y %H:%M:%S&#39;, mod_time)
                    else:
                        mod_time = None
                    
                    if &#39;backupTime&#39; in result[&#39;advancedData&#39;] and int(result[&#39;advancedData&#39;][&#39;backupTime&#39;]) &gt; 0:
                        bkp_time = time.localtime(int(result[&#39;advancedData&#39;][&#39;backupTime&#39;]))
                        bkp_time = time.strftime(&#39;%d/%m/%Y %H:%M:%S&#39;, bkp_time)
                    else:
                        bkp_time = None

                    if &#39;file&#39; in result[&#39;flags&#39;]:
                        if result[&#39;flags&#39;][&#39;file&#39;] in (True, &#39;1&#39;):
                            file_or_folder = &#39;File&#39;
                        else:
                            file_or_folder = &#39;Folder&#39;
                    else:
                        file_or_folder = &#39;Folder&#39;

                    if &#39;size&#39; in result:
                        size = result[&#39;size&#39;]
                    else:
                        size = None
                        
                    if show_deleted and &#39;deleted&#39; in result.get(&#39;flags&#39;):
                        deleted = True if result[&#39;flags&#39;].get(&#39;deleted&#39;) in (True, &#39;1&#39;) else False
                    else:
                        deleted = None

                    paths_dict[path] = {
                        &#39;name&#39;: name,
                        &#39;snap_display_name&#39;: snap_display_name,
                        &#39;size&#39;: size,
                        &#39;modified_time&#39;: mod_time,
                        &#39;type&#39;: file_or_folder,
                        &#39;backup_time&#39;: bkp_time,
                        &#39;advanced_data&#39;: result[&#39;advancedData&#39;],
                        &#39;deleted&#39;: deleted
                    }

                    paths.append(path)

                return paths, paths_dict
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _do_browse(self, options=None, retry=10):
        &#34;&#34;&#34;Performs a browse operation with the given options.

        Args:
            options     (dict)  --  dictionary of browse options

            retry       (int)   --  Number of times to retry for browse

        Returns:
            list - List of only the file, folder paths from the browse response

            dict - Dictionary of all the paths with additional metadata retrieved from browse
        &#34;&#34;&#34;
        if options is None:
            options = {}

        options = self._prepare_browse_options(options)
        request_json = self._prepare_browse_json(options)

        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._BROWSE, request_json)

        attempt = 1
        while attempt &lt;= retry:
            if response.json() == {}:
                time.sleep(120)
                flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._BROWSE, request_json)
            else:
                break
            attempt += 1
        return self._process_browse_response(flag, response, options)

    def update_properties(self, properties_dict):
        &#34;&#34;&#34;Updates the backupset properties

            Args:
                properties_dict (dict)  --  Backupset property dict which is to be updated

            Returns:
                None

            Raises:
                SDKException:
                    if failed to add

                    if response is empty

                    if response code is not as expected

        **Note** self.properties can be used to get a deep copy of all the properties, modify the properties which you
        need to change and use the update_properties method to set the properties

        &#34;&#34;&#34;
        request_json = {
            &#34;backupsetProperties&#34;: {},
            &#34;association&#34;: {
                &#34;entity&#34;: [
                    {
                        &#34;clientName&#34;: self._client_object.client_name,
                        &#34;backupsetName&#34;: self.backupset_name,
                        &#34;instanceName&#34;: self._instance_object.instance_name,
                        &#34;appName&#34;: self._agent_object.agent_name
                    }
                ]
            }
        }

        request_json[&#39;backupsetProperties&#39;].update(properties_dict)
        status, _, error_string = self._process_update_reponse(request_json)

        if not status:
            raise SDKException(
                &#39;Backupset&#39;,
                &#39;102&#39;,
                &#39;Failed to update backupset property\nError: &#34;{0}&#34;&#39;.format(error_string))

    @property
    def properties(self):
        &#34;&#34;&#34;Returns the backupset properties&#34;&#34;&#34;
        return copy.deepcopy(self._properties)

    @property
    def name(self):
        &#34;&#34;&#34;Returns the Backupset display name&#34;&#34;&#34;
        return self._properties[&#34;backupSetEntity&#34;][&#34;backupsetName&#34;]

    @property
    def backupset_id(self):
        &#34;&#34;&#34;Treats the backupset id as a read-only attribute.&#34;&#34;&#34;
        return self._backupset_id

    @property
    def backupset_name(self):
        &#34;&#34;&#34;Treats the backupset name as a property of the Backupset class.&#34;&#34;&#34;
        return self._backupset_name

    @property
    def description(self):
        &#34;&#34;&#34;Treats the backupset description as a property of the Backupset class.&#34;&#34;&#34;
        return self._description

    @property
    def is_default_backupset(self):
        &#34;&#34;&#34;Treats the is default backupset as a read-only attribute.&#34;&#34;&#34;
        return self._is_default

    @property
    def is_on_demand_backupset(self):
        &#34;&#34;&#34;Treats the is on demand backupset as a read-only attribute.&#34;&#34;&#34;
        return self._is_on_demand_backupset

    @property
    def plan(self):
        &#34;&#34;&#34;Treats the backupset plan as a property of the Backupset class.&#34;&#34;&#34;
        if self._plan_obj is not None:
            return self._plan_obj
        elif self._plan_name is not None:
            self._plan_obj = self._commcell_object.plans.get(self._plan_name)
            return self._plan_obj
        else:
            return None

    @property
    def guid(self):
        &#34;&#34;&#34;Treats the backupset GUID as a property of the Backupset class.&#34;&#34;&#34;
        if self._properties.get(&#39;backupSetEntity&#39;):
            if self._properties[&#39;backupSetEntity&#39;].get(&#39;backupsetGUID&#39;):
                return self._properties[&#39;backupSetEntity&#39;][&#39;backupsetGUID&#39;]
            raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, &#39;Backupset GUID not found&#39;)
        raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, &#39;Backupset entity not found&#39;)

    @backupset_name.setter
    def backupset_name(self, value):
        &#34;&#34;&#34;Sets the name of the backupset as the value provided as input.

            Raises:
                SDKException:
                    if failed to update the backupset name

                    if type of value input is not string
        &#34;&#34;&#34;
        if isinstance(value, str):
            output = self._update(
                backupset_name=value,
                backupset_description=self.description,
                default_backupset=self.is_default_backupset
            )

            if output[0]:
                return
            o_str = &#39;Failed to update the name of the backupset\nError: &#34;{0}&#34;&#39;
            raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, o_str.format(output[2]))

        raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, &#39;Backupset name should be a string value&#39;)

    @description.setter
    def description(self, value):
        &#34;&#34;&#34;Sets the description of the backupset as the value provided as input.

            Raises:
                SDKException:
                    if failed to update the backupset description

                    if type of value input is not string

                    if description cannot be modified for this backupset
        &#34;&#34;&#34;
        if self.description is not None:
            if isinstance(value, str):
                output = self._update(
                    backupset_name=self.backupset_name,
                    backupset_description=value,
                    default_backupset=self.is_default_backupset
                )

                if output[0]:
                    return

                o_str = &#39;Failed to update the description of the backupset\nError: &#34;{0}&#34;&#39;
                raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, o_str.format(output[2]))

            raise SDKException(
                &#39;Backupset&#39;, &#39;102&#39;, &#39;Backupset description should be a string value&#39;
            )

        raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, &#39;Description cannot be modified&#39;)

    @plan.setter
    def plan(self, value):
        &#34;&#34;&#34;Associates the plan to the backupset

            Args:
                value   (object)    --  the Plan object which is to be associated
                                        with the backupset

                value   (str)       --  name of the plan which is to be associated
                                        with the backupset

                value   (None)      --  set value to None to remove plan associations

            Raises:
                SDKException:

                    if plan does not exist

                    if plan association fails

                    if plan is not eligible to be associated
        &#34;&#34;&#34;
        from .plan import Plan
        if isinstance(value, Plan):
            self._plan_obj = value
        elif isinstance(value, str):
            self._plan_obj = self._commcell_object.plans.get(value)
        elif value is None:
            self._plan_obj = None
        else:
            raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, &#39;Input value is not of supported type&#39;)

        plans_obj = self._commcell_object.plans
        entity_dict = {
            &#39;clientId&#39;: int(self._client_object.client_id),
            &#39;appId&#39;: int(self._agent_object.agent_id),
            &#39;backupsetId&#39;: int(self.backupset_id)
        }
        if value is not None and self._plan_obj.plan_name in plans_obj.get_eligible_plans(entity_dict):
            request_json = {
                &#39;backupsetProperties&#39;: {
                    &#39;planEntity&#39;: {
                        &#39;planSubtype&#39;: int(self._plan_obj.subtype),
                        &#39;_type_&#39;: 158,
                        &#39;planType&#39;: int(self._plan_obj.plan_type),
                        &#39;planName&#39;: self._plan_obj.plan_name,
                        &#39;planId&#39;: int(self._plan_obj.plan_id)
                    }
                }
            }

            response = self._process_update_reponse(
                request_json
            )

            if response[0]:
                return
            else:
                o_str = &#39;Failed to asspciate plan to the backupset\nError: &#34;{0}&#34;&#39;
                raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, o_str.format(response[2]))
        elif value is None:
            request_json = {
                &#39;backupsetProperties&#39;: {
                    &#39;removePlanAssociation&#39;: True
                }
            }

            response = self._process_update_reponse(
                request_json
            )

            if response[0]:
                return
            else:
                o_str = &#39;Failed to dissociate plan from backupset\nError: &#34;{0}&#34;&#39;
                raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, o_str.format(response[2]))
        else:
            raise SDKException(
                &#39;Backupset&#39;,
                &#39;102&#39;,
                &#39;Plan not eligible to be associated with the backupset&#39;
            )

    def set_default_backupset(self):
        &#34;&#34;&#34;Sets the backupset represented by this Backupset class instance as the default backupset
            if it is not the default backupset.

            Raises:
                SDKException:
                    if failed to set this as the default backupset
        &#34;&#34;&#34;
        if self.is_default_backupset is False:
            output = self._update(
                backupset_name=self.backupset_name,
                backupset_description=self.description,
                default_backupset=True
            )

            if output[0]:
                return

            o_str = &#39;Failed to set this as the Default Backup Set\nError: &#34;{0}&#34;&#39;
            raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, o_str.format(output[2]))

    def backup(self, **kwargs):
        &#34;&#34;&#34;Runs backup job for all subclients in this backupset.

            kwargs:
                Please refer subclient.backup() for all the supported arguments. Commonly used arguments are,

                backup_level        (str)   --  level of backup the user wish to run
                        Full / Incremental / Differential / Synthetic_full
                    default: Incremental

                advanced_options   (dict)  --  advanced backup options to be included while
                                                    making the request

                common_backup_options      (dict)  --  advanced job options to be included while
                                                        making request

            Returns:
                list    -   list consisting of the job objects for the backup jobs started for
                the subclients in the backupset

        &#34;&#34;&#34;
        return_list = []
        thread_list = []

        if self.subclients.all_subclients:
            for subclient in self.subclients.all_subclients:
                thread = threading.Thread(
                    target=self._run_backup, args=(subclient, return_list), kwargs=kwargs
                )
                thread_list.append(thread)
                thread.start()

        for thread in thread_list:
            thread.join()

        return return_list

    def browse(self, *args, **kwargs):
        &#34;&#34;&#34;Browses the content of the Backupset.

            Args:
                Dictionary of browse options:
                    Example:

                        browse({
                            &#39;path&#39;: &#39;c:\\hello&#39;,

                            &#39;show_deleted&#39;: True,

                            &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,

                            &#39;to_time&#39;: &#39;2016-04-21 12:00:00&#39;
                        })

            Kwargs:
                Keyword argument of browse options:
                    Example:

                        browse(
                            path=&#39;c:\\hello&#39;,

                            show_deleted=True,

                            from_time=&#39;2014-04-20 12:00:00&#39;,

                            to_time=&#39;2016-04-21 12:00:00&#39;
                        )

            Returns:
                (list, dict)
                    list    -   List of only the file, folder paths from the browse response

                    dict    -   Dictionary of all the paths with additional metadata retrieved
                    from browse operation


            Refer `default_browse_options`_ for all the supported options.

            .. _default_browse_options: https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565

        &#34;&#34;&#34;
        if args and isinstance(args[0], dict):
            options = args[0]
        else:
            options = kwargs

        options[&#39;operation&#39;] = &#39;browse&#39;

        return self._do_browse(options)

    def find(self, *args, **kwargs):
        &#34;&#34;&#34;Searches a file/folder in the backed up content of the backupset,
            and returns all the files matching the filters given.

            Args:
                Dictionary of browse options:
                    Example:

                        find({
                            &#39;file_name&#39;: &#39;*.txt&#39;,

                            &#39;show_deleted&#39;: True,

                            &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,

                            &#39;to_time&#39;: &#39;2016-04-31 12:00:00&#39;
                        })

            Kwargs:
                Keyword argument of browse options:
                    Example:

                        find(
                            file_name=&#39;*.txt&#39;,

                            show_deleted=True,

                            &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,

                            to_time=&#39;2016-04-31 12:00:00&#39;
                        )

            Returns:
                (list, dict)
                    list    -   List of only the file, folder paths from the browse response

                    dict    -   Dictionary of all the paths with additional metadata retrieved
                    from browse operation


            Refer `default_browse_options`_ for all the supported options.

            Additional options supported:
                file_name       (str)   --  Find files with name

                file_size_gt    (int)   --  Find files with size greater than size

                file_size_lt    (int)   --  Find files with size lesser than size

                file_size_et    (int)   --  Find files with size equal to size

            .. _default_browse_options: https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565

        &#34;&#34;&#34;
        if args and isinstance(args[0], dict):
            options = args[0]
        else:
            options = kwargs

        if &#39;operation&#39; not in options:
            options[&#39;operation&#39;] = &#39;find&#39;

        if &#39;path&#39; not in options:
            options[&#39;path&#39;] = &#39;\\**\\*&#39;

        if &#39;filters&#39; not in options:
            options[&#39;filters&#39;] = []

        if &#39;file_name&#39; in options:
            options[&#39;filters&#39;].append((&#39;FileName&#39;, options[&#39;file_name&#39;]))

        if &#39;file_size_gt&#39; in options:
            options[&#39;filters&#39;].append((&#39;FileSize&#39;, options[&#39;file_size_gt&#39;], &#39;GTE&#39;))

        if &#39;file_size_lt&#39; in options:
            options[&#39;filters&#39;].append((&#39;FileSize&#39;, options[&#39;file_size_lt&#39;], &#39;LTE&#39;))

        if &#39;file_size_et&#39; in options:
            options[&#39;filters&#39;].append((&#39;FileSize&#39;, options[&#39;file_size_et&#39;], &#39;EQUALSBLAH&#39;))

        return self._do_browse(options)

    def delete_data(self, paths):
        &#34;&#34;&#34;Deletes items for the backupset in the Index and makes them unavailable for
        browsing and recovery

            Args:
                paths       (str/list)      --      The list of paths or single path to delete
                from the backupset

            Returns:
                None        --      If delete request is sent successfully

            Raises:
                Exception, if unable to prepare, response is invalid or send the
                delete data request

        &#34;&#34;&#34;

        options = {
            &#39;operation&#39;: &#39;delete_data&#39;,
            &#39;path&#39;: paths
        }

        files, _ = self._do_browse(options)

        # Delete operation does not return any result, hence consider the operation successful
        if files:
            raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, &#39;Delete data operation gave unexpected results&#39;)

    def list_media(self, *args, **kwargs):
        &#34;&#34;&#34;List media required to browse and restore backed up data from the backupset

            Args:
                Dictionary of options:
                    Example:

                        list_media({
                            &#39;path&#39;: &#39;c:\\hello&#39;,
                            &#39;show_deleted&#39;: True,
                            &#39;from_time&#39;: &#39;2020-04-20 12:00:00&#39;,
                            &#39;to_time&#39;: &#39;2021-04-19 12:00:00&#39;
                        })

            Kwargs:
                Keyword argument of options:
                    Example:

                        list_media(
                            path=&#39;c:\\hello&#39;,
                            show_deleted=True,
                            from_time=&#39;2020-04-20 12:00:00&#39;,
                            to_time=&#39;2021-04-19 12:00:00&#39;
                        )

            Note:
                Refer `_default_browse_options` for all the supported options.

            Returns:
                (List, Dict) -
                    List - List of all the media required for the given options

                    Dict - Total size of the media

            Raises:
                SDKException:
                    if failed to list media for content

                    if response is not success

        &#34;&#34;&#34;

        if args and isinstance(args[0], dict):
            options = args[0]
        else:
            options = kwargs

        options[&#39;operation&#39;] = &#39;list_media&#39;
        options[&#39;_raw_response&#39;] = True

        _, response = self._do_browse(options)

        if response and &#39;browseResponses&#39; in response:
            responses = response[&#39;browseResponses&#39;]
            list_media_response = responses[0]

            prediction_data = list_media_response.get(&#39;predictionData&#39;, [])
            browse_result = list_media_response.get(&#39;browseResult&#39;, {})

            return prediction_data, browse_result
        else:
            raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, &#39;List media operation gave unexpected results&#39;)

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the Backupset.&#34;&#34;&#34;
        self._get_backupset_properties()

        self.subclients = Subclients(self)
        self.schedules = Schedules(self)

    def backed_up_files_count(self, path=&#34;\\**\\*&#34;):
        &#34;&#34;&#34;Returns the count of the total number of files present in the backed up data
         of all the subclients of the given backupset and given path.

                Args:

                    path        (str)       --  Folder path to find no of backed up files
                                                    (Default: \\**\\*)

                Returns:

                    int --  No of backed up files count in given path

                Raises:

                    Exception:

                        if browse response is not proper
         &#34;&#34;&#34;
        options_dic = {&#34;operation&#34;: &#34;find&#34;, &#34;opType&#34;: 1, &#34;path&#34;: path,
                       &#34;_custom_queries&#34;: [{&#34;type&#34;: &#34;AGGREGATE&#34;, &#34;queryId&#34;: &#34;2&#34;,
                                            &#34;aggrParam&#34;: {&#34;aggrType&#34;: &#34;COUNT&#34;}, &#34;whereClause&#34;: [{
                                                &#34;criteria&#34;: {
                                                    &#34;field&#34;: &#34;Flags&#34;,
                                                    &#34;dataOperator&#34;: &#34;IN&#34;,
                                                    &#34;values&#34;: [&#34;file&#34;]
                                                }
                                            }]}], &#34;_raw_response&#34;: True}

        browse_response = self._do_browse(options_dic)
        if not len(browse_response) &gt; 1:
            raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, &#39;Browse response is not proper&#39;)
        browse_response = browse_response[1]
        if &#39;browseResponses&#39; not in browse_response or len(browse_response[&#39;browseResponses&#39;]) == 0:
            raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, &#39;Browse response is missing browseResponses&#39;)
        browse_response = browse_response[&#39;browseResponses&#39;][0]
        if &#39;browseResult&#39; not in browse_response:
            raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, &#39;Browse response is missing browseResult&#39;)
        browse_result = browse_response[&#39;browseResult&#39;]
        if &#39;aggrResultSet&#39; not in browse_result or len(browse_result[&#39;aggrResultSet&#39;]) == 0:
            raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, &#39;Browse response is missing aggrResultSet&#39;)
        return browse_result[&#39;aggrResultSet&#39;][0].get(&#39;count&#39;, 0)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.backupset.Backupset"><code class="flex name class">
<span>class <span class="ident">Backupset</span></span>
<span>(</span><span>instance_object, backupset_name, backupset_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for performing backupset operations for a specific backupset.</p>
<p>Initialise the backupset object.</p>
<h2 id="args">Args</h2>
<p>instance_object
(object)
&ndash;
instance of the Instance class</p>
<p>backupset_name
(str)
&ndash;
name of the backupset</p>
<p>backupset_id
(str)
&ndash;
id of the backupset
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Backupset class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupset.py#L1007-L2391" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Backupset(object):
    &#34;&#34;&#34;Class for performing backupset operations for a specific backupset.&#34;&#34;&#34;

    def __new__(cls, instance_object, backupset_name, backupset_id=None):
        &#34;&#34;&#34;Class composition for CV backupsets&#34;&#34;&#34;
        from .backupsets.fsbackupset import FSBackupset
        from .backupsets.nasbackupset import NASBackupset
        from .backupsets.hanabackupset import HANABackupset
        from .backupsets.cabackupset import CloudAppsBackupset
        from .backupsets.postgresbackupset import PostgresBackupset
        from .backupsets.adbackupset import ADBackupset
        from .backupsets.db2backupset import DB2Backupset
        from .backupsets.vsbackupset import VSBackupset
        from .backupsets.aadbackupset import AzureAdBackupset
        from .backupsets.sharepointbackupset import SharepointBackupset

        _backupsets_dict = {
            &#39;file system&#39;: FSBackupset,
            &#39;nas&#39;: NASBackupset,        # SP11 or lower CS honors NAS as the Agent Name
            &#39;ndmp&#39;: NASBackupset,       # SP12 and above honors NDMP as the Agent Name
            &#39;sap hana&#39;: HANABackupset,
            &#39;cloud apps&#39;: CloudAppsBackupset,
            &#39;postgresql&#39;: PostgresBackupset,
            &#34;active directory&#34;: ADBackupset,
            &#39;db2&#39;: DB2Backupset,
            &#39;virtual server&#39;: VSBackupset,
            &#34;azure ad&#34;: AzureAdBackupset,
            &#39;sharepoint server&#39;: SharepointBackupset
        }

        if instance_object._agent_object.agent_name in _backupsets_dict.keys():
            _class = _backupsets_dict.get(instance_object._agent_object.agent_name, cls)
            if _class.__new__ == cls.__new__:
                return object.__new__(_class)
            return _class.__new__(_class, instance_object, backupset_name, backupset_id)
        else:
            return object.__new__(cls)

    def __init__(self, instance_object, backupset_name, backupset_id=None):
        &#34;&#34;&#34;Initialise the backupset object.

            Args:
                instance_object     (object)  --  instance of the Instance class

                backupset_name      (str)     --  name of the backupset

                backupset_id        (str)     --  id of the backupset
                    default: None

            Returns:
                object - instance of the Backupset class
        &#34;&#34;&#34;
        self._instance_object = instance_object
        self._agent_object = self._instance_object._agent_object
        self._client_object = self._agent_object._client_object

        self._commcell_object = self._agent_object._commcell_object

        self._cvpysdk_object = self._commcell_object._cvpysdk_object
        self._services = self._commcell_object._services
        self._update_response_ = self._commcell_object._update_response_

        # self._restore_methods = [&#39;_process_restore_response&#39;, &#39;_filter_paths&#39;, &#39;_restore_json&#39;]
        self._restore_methods = [
            &#39;_process_restore_response&#39;,
            &#39;_filter_paths&#39;,
            &#39;_process_search_response&#39;,
            &#39;_restore_json&#39;,
            &#39;_impersonation_json&#39;,
            &#39;_restore_browse_option_json&#39;,
            &#39;_restore_common_options_json&#39;,
            &#39;_restore_destination_json&#39;,
            &#39;_restore_fileoption_json&#39;,
            &#39;_json_restore_subtask&#39;
        ]

        self._restore_options_json = [
            &#39;_impersonation_json_&#39;,
            &#39;_browse_restore_json&#39;,
            &#39;_destination_restore_json&#39;,
            &#39;_commonoption_restore_json&#39;,
            &#39;_fileoption_restore_json&#39;,
        ]

        self._backupset_name = backupset_name.split(&#39;\\&#39;)[-1].lower()
        self._description = None

        if backupset_id:
            # Use the backupset id provided in the arguments
            self._backupset_id = str(backupset_id)
        else:
            # Get the id associated with this backupset
            self._backupset_id = self._get_backupset_id()

        self._BACKUPSET = self._services[&#39;BACKUPSET&#39;] % (self.backupset_id)
        self._BROWSE = self._services[&#39;BROWSE&#39;]
        self._RESTORE = self._services[&#39;RESTORE&#39;]

        self._is_default = False
        self._is_on_demand_backupset = False
        self._properties = None
        self._backupset_association = {}
        self._plan_name = None
        self._plan_obj = None

        self.subclients = None
        self.schedules = None
        self._hidden_subclient = None
        self.refresh()

        self._default_browse_options = {
            &#39;operation&#39;: &#39;browse&#39;,
            &#39;show_deleted&#39;: False,
            &#39;from_time&#39;: 0,  # value should either be the Epoch time or the Timestamp
            &#39;to_time&#39;: 0,  # value should either be the Epoch time or the Timestamp
            &#39;path&#39;: &#39;\\&#39;,
            &#39;copy_precedence&#39;: 0,
            &#39;media_agent&#39;: &#39;&#39;,
            &#39;page_size&#39;: 100000,
            &#39;skip_node&#39;: 0,
            &#39;restore_index&#39;: True,
            &#39;vm_disk_browse&#39;: False,
            &#39;filters&#39;: [],
            &#39;job_id&#39;: 0,
            &#39;commcell_id&#39;: self._commcell_object.commcell_id,
            &#39;include_aged_data&#39;: False,
            &#39;include_meta_data&#39;:False,
            &#39;include_hidden&#39;: False,
            &#39;include_running_jobs&#39;: False,
            &#39;compute_folder_size&#39;: False,
            &#39;vs_volume_browse&#39;: False,
            &#39;browse_view_name&#39;: &#39;VOLUMEVIEW&#39;,
            &#39;compare_backups_req&#39;: 0,
            &#39;comparison_job_id&#39;: 0,

            &#39;_subclient_id&#39;: 0,
            &#39;_raw_response&#39;: False,
            &#39;_custom_queries&#39;: False
        }

    def __getattr__(self, attribute):
        &#34;&#34;&#34;Returns the persistent attributes&#34;&#34;&#34;
        if attribute in self._restore_methods:
            return getattr(self._instance_object, attribute)
        elif attribute in self._restore_options_json:
            return getattr(self._instance_object, attribute)

        return super(Backupset, self).__getattribute__(attribute)

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        representation_string = (&#39;Backupset class instance for Backupset: &#34;{0}&#34; &#39;
                                 &#39;for Instance: &#34;{1}&#34; of Agent: &#34;{2}&#34;&#39;)
        return representation_string.format(
            self.backupset_name,
            self._instance_object.instance_name,
            self._agent_object.agent_name
        )

    def _get_backupset_id(self):
        &#34;&#34;&#34;Gets the backupset id associated with this backupset.

            Returns:
                str - id associated with this backupset
        &#34;&#34;&#34;
        backupsets = Backupsets(self._instance_object)
        return backupsets.get(self.backupset_name).backupset_id

    def _get_backupset_properties(self):
        &#34;&#34;&#34;Gets the properties of this backupset.

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._BACKUPSET)

        if flag:
            if response.json() and &#34;backupsetProperties&#34; in response.json():
                self._properties = response.json()[&#34;backupsetProperties&#34;][0]

                backupset_name = self._properties[&#34;backupSetEntity&#34;][&#34;backupsetName&#34;]
                self._backupset_name = backupset_name.lower()

                self._backupset_association = self._properties[&#39;backupSetEntity&#39;]

                self._is_default = bool(self._properties[&#34;commonBackupSet&#34;][&#34;isDefaultBackupSet&#34;])

                if &#39;commonBackupSet&#39; in self._properties:
                    if &#39;onDemandBackupset&#39; in self._properties[&#39;commonBackupSet&#39;]:
                        self._is_on_demand_backupset = bool(
                            self._properties[&#39;commonBackupSet&#39;][&#39;onDemandBackupset&#39;]
                        )

                if &#34;userDescription&#34; in self._properties[&#34;commonBackupSet&#34;]:
                    self._description = self._properties[&#34;commonBackupSet&#34;][&#34;userDescription&#34;]

                if &#34;planName&#34; in self._properties[&#34;planEntity&#34;]:
                    self._plan_name = self._properties[&#34;planEntity&#34;][&#34;planName&#34;]
                else:
                    self._plan_name = None
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _run_backup(self, subclient_name, return_list, **kwargs):
        &#34;&#34;&#34;Triggers backup job for the given subclient, and appends its Job object to the list.
            Backup job is started only when backup activity is enabled and storage policy is set for it.

            The SDKException class instance is appended to the list,
            if any exception is raised while running the backup job for the Subclient.

            Args:
                subclient_name (str)   --  name of the subclient to trigger the backup for

                return_list    (list)  --  list to append the job object to

            Kwargs:
                All arguments used by subclient.backup() can be used here. Commonly used arguments are

                backup_level   (str)   --  The type of backup to run

                advanced_options   (dict)  --  advanced backup options to be included while
                                    making the request

            Returns:
                None

        &#34;&#34;&#34;
        try:
            subclient = self.subclients.get(subclient_name)
            if subclient.is_backup_enabled and subclient.storage_policy is not None:
                job = subclient.backup(**kwargs)
                return_list.append(job)
                time.sleep(2)  # Staggering the next backup job to be started
        except SDKException as excp:
            return_list.append(excp)

    def _process_update_reponse(self, request_json):
        &#34;&#34;&#34;Runs the Backupset update API with the request JSON provided,
            and returns the contents after parsing the response.

            Args:
                request_json    (dict)  --  JSON request to run for the API

            Returns:
                (bool, str, str):
                    bool -  flag specifies whether success / failure

                    str  -  error code received in the response

                    str  -  error message received

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._BACKUPSET, request_json)

        self._get_backupset_properties()

        if flag:
            if response.json() and &#34;response&#34; in response.json():
                error_code = str(response.json()[&#34;response&#34;][0][&#34;errorCode&#34;])

                if error_code == &#34;0&#34;:
                    return True, &#34;0&#34;, &#34;&#34;
                else:
                    error_string = &#34;&#34;

                    if &#34;errorString&#34; in response.json()[&#34;response&#34;][0]:
                        error_string = response.json()[&#34;response&#34;][0][&#34;errorString&#34;]

                    if error_string:
                        return False, error_code, error_string
                    else:
                        return False, error_code, &#34;&#34;
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _update(self, backupset_name, backupset_description, default_backupset):
        &#34;&#34;&#34;Updates the properties of the backupset.

            Args:
                backupset_name        (str)   --  new name of the backupset

                backupset_description (str)   --  description of the backupset

                default_backupset     (bool)  --  default backupset property

            Returns:
                (bool, str, str):
                    bool -  flag specifies whether success / failure

                    str  -  error code received in the response

                    str  -  error message received

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;

        request_json = {
            &#34;association&#34;: {
                &#34;entity&#34;: [{
                    &#34;clientName&#34;: self._client_object.client_name,
                    &#34;appName&#34;: self._agent_object.agent_name,
                    &#34;instanceName&#34;: self._instance_object.instance_name,
                    &#34;backupsetName&#34;: self.backupset_name
                }]
            },
            &#34;backupsetProperties&#34;: {
                &#34;commonBackupSet&#34;: {
                    &#34;newBackupSetName&#34;: backupset_name,
                    &#34;isDefaultBackupSet&#34;: default_backupset
                }
            }
        }

        if backupset_description is not None:
            request_json[&#34;backupsetProperties&#34;][&#34;commonBackupSet&#34;][
                &#34;userDescription&#34;] = backupset_description

        return self._process_update_reponse(request_json)

    @staticmethod
    def _get_epoch_time(timestamp):
        &#34;&#34;&#34;Returns the Epoch time given the input time is in format %Y-%m-%d %H:%M:%S.

            Args:
                timestamp   (int / str)     --  value should either be the Epoch time or, the
                                                    Timestamp of the format %Y-%m-%d %H:%M:%S

            Returns:
                int - epoch time converted from the input timestamp

            Raises:
                SDKException:
                    if the input timestamp is not of correct format
        &#34;&#34;&#34;
        if str(timestamp) == &#39;0&#39;:
            return 0

        try:
            # return the timestamp value in int type
            return int(timestamp)
        except ValueError:
            # if not convertible to int, then convert the timestamp input to Epoch time
            try:
                return int(time.mktime(time.strptime(timestamp, &#34;%Y-%m-%d %H:%M:%S&#34;)))
            except Exception:
                raise SDKException(&#39;Subclient&#39;, &#39;106&#39;)

    def _set_defaults(self, final_dict, defaults_dict):
        &#34;&#34;&#34;Iterates over the defaults_dict, and adds the default value to the final_dict,
            for the key which is not present in the final dict.

            Recursively sets default values on the final_dict dictionary.

            Args:
                final_dict      (dict)  --  the dictionary to be set with defaults, and to be used
                                                to generate the Browse / Find JSON

                defaults_dict   (dict)  --  the dictionary with default values

            Returns:
                None
        &#34;&#34;&#34;
        for key in defaults_dict:
            if key not in final_dict:
                final_dict[key] = defaults_dict[key]

            if isinstance(defaults_dict[key], dict):
                self._set_defaults(final_dict[key], defaults_dict[key])

    def _prepare_browse_options(self, options):
        &#34;&#34;&#34;Prepares the options for the Browse/find operation.

            Args:
                options     (dict)  --  a dictionary of browse options

            Returns:
                dict - The browse options with all the default options set
        &#34;&#34;&#34;
        self._set_defaults(options, self._default_browse_options)
        return options

    def _prepare_browse_json(self, options):
        &#34;&#34;&#34;Prepares the JSON object for the browse request.

            Args:
                options     (dict)  --  the browse options dictionary

            Returns:
                dict - A JSON object for the browse response
        &#34;&#34;&#34;
        operation_types = {
            &#39;browse&#39;: 0,
            &#39;find&#39;: 1,
            &#39;all_versions&#39;: 2,
            &#39;list_media&#39;: 3,
            &#39;delete_data&#39;: 7
        }

        options[&#39;operation&#39;] = options[&#39;operation&#39;].lower()

        if options[&#39;operation&#39;] not in operation_types:
            options[&#39;operation&#39;] = &#39;find&#39;

        # add the browse mode value here, if it is different for an agent
        # if agent is not added in the dict, default value 2 will be used
        browse_mode = {
            &#39;virtual server&#39;: 4,
            &#39;cloud apps&#39;: 3,
            &#39;azure ad&#39;: 3
        }

        mode = 2
        paths = []

        if isinstance(options[&#39;path&#39;], str):
            paths.append(options[&#39;path&#39;])
        elif isinstance(options[&#39;path&#39;], list):
            paths = options[&#39;path&#39;]
        else:
            paths = [&#39;\\&#39;]

        if self._agent_object.agent_name in browse_mode:
            mode = browse_mode[self._agent_object.agent_name]

        request_json = {
            &#34;opType&#34;: operation_types[options[&#39;operation&#39;]],
            &#34;mode&#34;: {
                &#34;mode&#34;: mode
            },
            &#34;paths&#34;: [{&#34;path&#34;: path} for path in paths],
            &#34;options&#34;: {
                &#34;showDeletedFiles&#34;: options.get(&#39;show_deleted&#39;, False),
                &#34;restoreIndex&#34;: options[&#39;restore_index&#39;],
                &#34;vsDiskBrowse&#34;: options[&#39;vm_disk_browse&#39;],
                &#34;vsFileBrowse&#34;: options.get(&#39;vs_file_browse&#39;, False),
                &#34;includeMetadata&#34;: options.get(&#39;include_meta_data&#39;, False),
                &#34;hideUserHidden&#34;: options.get(&#39;hide_user_hidden&#39;, False)
            },
            &#34;entity&#34;: {
                &#34;clientName&#34;: self._client_object.client_name,
                &#34;clientId&#34;: int(self._client_object.client_id),
                &#34;applicationId&#34;: int(self._agent_object.agent_id),
                &#34;instanceId&#34;: int(self._instance_object.instance_id),
                &#34;backupsetId&#34;: int(self.backupset_id),
                &#34;subclientId&#34;: int(options[&#39;_subclient_id&#39;])
            },
            &#34;timeRange&#34;: {
                &#34;fromTime&#34;: self._get_epoch_time(options[&#39;from_time&#39;]),
                &#34;toTime&#34;: self._get_epoch_time(options[&#39;to_time&#39;])
            },
            &#34;advOptions&#34;: {
                &#34;copyPrecedence&#34;: int(options[&#39;copy_precedence&#39;])
            },
            &#34;ma&#34;: {
                &#34;clientName&#34;: options[&#39;media_agent&#39;]
            },
            &#34;queries&#34;: [{
                &#34;type&#34;: 0,
                &#34;queryId&#34;: &#34;dataQuery&#34;,
                &#34;dataParam&#34;: {
                    &#34;sortParam&#34;: {
                        &#34;ascending&#34;: False,
                        &#34;sortBy&#34;: [0]
                    },
                    &#34;paging&#34;: {
                        &#34;pageSize&#34;: int(options[&#39;page_size&#39;]),
                        &#34;skipNode&#34;: int(options[&#39;skip_node&#39;]),
                        &#34;firstNode&#34;: 0
                    }
                }
            }]
        }

        if options[&#39;filters&#39;]:
            # [(&#39;FileName&#39;, &#39;*.txt&#39;), (&#39;FileSize&#39;,&#39;GT&#39;,&#39;100&#39;)]
            request_json[&#39;queries&#39;][0][&#39;whereClause&#39;] = []

            for browse_filter in options[&#39;filters&#39;]:
                if browse_filter[0] in (&#39;FileName&#39;, &#39;FileSize&#39;):
                    temp_dict = {
                        &#39;connector&#39;: 0,
                        &#39;criteria&#39;: {
                            &#39;field&#39;: browse_filter[0],
                            &#39;values&#39;: [browse_filter[1]]
                        }
                    }

                    if browse_filter[0] == &#39;FileSize&#39;:
                        temp_dict[&#39;criteria&#39;][&#39;dataOperator&#39;] = browse_filter[2]

                    request_json[&#39;queries&#39;][0][&#39;whereClause&#39;].append(temp_dict)

        if options[&#39;job_id&#39;] != 0:
            request_json[&#39;advOptions&#39;][&#39;advConfig&#39;] = {
                &#39;browseAdvancedConfigBrowseByJob&#39;: {
                    &#39;commcellId&#39;: options[&#39;commcell_id&#39;],
                    &#39;jobId&#39;: options[&#39;job_id&#39;]
                }
            }

        if options[&#39;include_aged_data&#39;]:
            request_json[&#39;options&#39;][&#39;includeAgedData&#39;] = True

        if options[&#39;include_meta_data&#39;]:
            request_json[&#39;options&#39;][&#39;includeMetadata&#39;] = True

        if options[&#39;include_hidden&#39;]:
            request_json[&#39;options&#39;][&#39;includeHidden&#39;] = True

        if options[&#39;include_running_jobs&#39;]:
            request_json[&#39;options&#39;][&#39;includeRunningJobs&#39;] = True

        if options[&#39;compute_folder_size&#39;]:
            request_json[&#39;options&#39;][&#39;computeFolderSizeForFilteredBrowse&#39;] = True

        if options[&#39;vs_volume_browse&#39;]:
            request_json[&#39;mode&#39;][&#39;mode&#39;] = 3
            request_json[&#39;options&#39;][&#39;vsVolumeBrowse&#39;] = True
            request_json[&#39;advOptions&#39;][&#39;browseViewName&#39;] = options[&#39;browse_view_name&#39;]

        if options[&#39;operation&#39;] == &#39;list_media&#39;:
            request_json[&#39;options&#39;][&#39;doPrediction&#39;] = True

        if options[&#39;_custom_queries&#39;]:
            request_json[&#39;queries&#39;] = options[&#39;_custom_queries&#39;]

        if options.get(&#39;live_browse&#39;, False):
            request_json[&#39;options&#39;][&#39;liveBrowse&#39;] = True

        if options.get(&#39;compare_backups_req&#39;, 0) != 0:
            request_json[&#39;mode&#39;][&#39;mode&#39;] = 3
            request_json[&#39;advOptions&#39;][&#39;advConfig&#39;] = {
                &#39;compareBackupsReqType&#39;: int(options.get(&#39;compare_backups_req&#39;))
            }
            if options.get(&#39;comparison_job_id&#39;, 0) != 0:
                request_json[&#39;applicationInfo&#39;] = {
                    &#39;indexingInfo&#39;: {
                        &#39;jobId&#39;: int(options.get(&#39;comparison_job_id&#39;))
                    }
                }

        return request_json

    def _process_browse_all_versions_response(self, result_set, options):
        &#34;&#34;&#34;Retrieves the items from browse response.

        Args:
            result_set  (list of dict)  --  browse response dict obtained from server
            options     (dict)          --  The browse options dictionary
                                                                                        {
                                                                                                &#34;show_deleted&#34;: True,
                                                                                        }

        Returns:
            dict - Dictionary of the specified file with list of all the file versions and
                    additional metadata retrieved from browse

        Raises:
            SDKException:
                if failed to browse/search for content

                if response is empty

                if response is not success
        &#34;&#34;&#34;
        path = None
        versions_list = []
        show_deleted = options.get(&#39;show_deleted&#39;, False)

        for result in result_set:
            name = result[&#39;displayName&#39;]
            path = result[&#39;path&#39;]

            if &#39;modificationTime&#39; in result:
                mod_time = time.localtime(int(result[&#39;modificationTime&#39;]))
                mod_time = time.strftime(&#39;%d/%m/%Y %H:%M:%S&#39;, mod_time)
            else:
                mod_time = None
            
            if &#39;backupTime&#39; in result[&#39;advancedData&#39;] and int(result[&#39;advancedData&#39;][&#39;backupTime&#39;]) &gt; 0:
                bkp_time = time.localtime(int(result[&#39;advancedData&#39;][&#39;backupTime&#39;]))
                bkp_time = time.strftime(&#39;%d/%m/%Y %H:%M:%S&#39;, bkp_time)
            else:
                bkp_time = None

            if &#39;file&#39; in result[&#39;flags&#39;]:
                if result[&#39;flags&#39;][&#39;file&#39;] in (True, &#39;1&#39;):
                    file_or_folder = &#39;File&#39;
                else:
                    file_or_folder = &#39;Folder&#39;
            else:
                file_or_folder = &#39;Folder&#39;

            if &#39;size&#39; in result:
                size = result[&#39;size&#39;]
            else:
                size = None

            if &#39;version&#39; in result:
                version = result[&#39;version&#39;]
            else:
                version = None

            if show_deleted and &#39;deleted&#39; in result.get(&#39;flags&#39;):
                deleted = True if result[&#39;flags&#39;].get(&#39;deleted&#39;) in (True, &#39;1&#39;) else False
            else:
                deleted = None
                    
            paths_dict = {
                &#39;name&#39;: name,
                &#39;version&#39;: version,
                &#39;size&#39;: size,
                &#39;modified_time&#39;: mod_time,
                &#39;type&#39;: file_or_folder,
                &#39;backup_time&#39;: bkp_time,
                &#39;advanced_data&#39;: result[&#39;advancedData&#39;],
                &#39;deleted&#39;: deleted
            }

            versions_list.append(paths_dict)

        all_versions_dict = dict()
        all_versions_dict[path] = versions_list

        return all_versions_dict

    def _process_browse_response(self, flag, response, options):
        &#34;&#34;&#34;Retrieves the items from browse response.

        Args:
            flag        (bool)  --  boolean, whether the response was success or not

            response    (dict)  --  JSON response received for the request from the Server

            options     (dict)  --  The browse options dictionary

        Returns:
            list - List of only the file / folder paths from the browse response

            dict - Dictionary of all the paths with additional metadata retrieved from browse

        Raises:
            SDKException:
                if failed to browse/search for content

                if response is empty

                if response is not success
        &#34;&#34;&#34;

        operation_types = {
            &#34;browse&#34;: (&#39;110&#39;, &#39;Failed to browse for subclient backup content\nError: &#34;{0}&#34;&#39;),
            &#34;find&#34;: (&#39;111&#39;, &#39;Failed to Search\nError: &#34;{0}&#34;&#39;),
            &#34;all_versions&#34;: (
                &#39;112&#39;, &#39;Failed to browse all version for specified content\nError: &#34;{0}&#34;&#39;
            ),
            &#34;delete_data&#34;: (
                &#39;113&#39;, &#39;Failed to perform delete data operation for given content\nError: &#34;{0}&#34;&#39;
            ),
            &#34;list_media&#34;: (
                &#39;113&#39;, &#39;Failed to perform list media operation for given content\nError: &#34;{0}&#34;&#39;
            )
        }

        exception_code = operation_types[options[&#39;operation&#39;]][0]
        exception_message = operation_types[options[&#39;operation&#39;]][1]
        
        show_deleted = options.get(&#39;show_deleted&#39;, False)

        if flag:

            response_json = response.json()
            paths_dict = {}
            paths = []
            result_set = None
            browse_result = None

            # Send raw result as browse response for advanced use cases
            if options[&#39;_raw_response&#39;]:
                return [], response_json

            if response_json and &#39;browseResponses&#39; in response_json:
                _browse_responses = response_json[&#39;browseResponses&#39;]
                for browse_response in _browse_responses:
                    if &#34;browseResult&#34; in browse_response:
                        browse_result = browse_response[&#39;browseResult&#39;]
                        if &#39;dataResultSet&#39; in browse_result:
                            result_set = browse_result[&#39;dataResultSet&#39;]
                            break

                if not browse_result:
                    try:
                        message = response_json[&#39;browseResponses&#39;][0][&#39;messages&#39;][0]
                        error_message = message[&#39;errorMessage&#39;]

                        o_str = exception_message
                        raise SDKException(&#39;Subclient&#39;, &#39;102&#39;, o_str.format(error_message))
                    except KeyError:
                        return [], {}

                if not result_set:
                    raise SDKException(&#39;Subclient&#39;, exception_code)

                if not isinstance(result_set, list):
                    result_set = [result_set]

                if &#39;all_versions&#39; in options[&#39;operation&#39;]:
                    return self._process_browse_all_versions_response(result_set, options)

                for result in result_set:
                    name = result.get(&#39;displayName&#39;)
                    snap_display_name = result.get(&#39;name&#39;)

                    if &#39;path&#39; in result:
                        path = result[&#39;path&#39;]
                    else:
                        path = &#39;\\&#39;.join([options[&#39;path&#39;], name])

                    if &#39;modificationTime&#39; in result and int(result[&#39;modificationTime&#39;]) &gt; 0:
                        mod_time = time.localtime(int(result[&#39;modificationTime&#39;]))
                        mod_time = time.strftime(&#39;%d/%m/%Y %H:%M:%S&#39;, mod_time)
                    else:
                        mod_time = None
                    
                    if &#39;backupTime&#39; in result[&#39;advancedData&#39;] and int(result[&#39;advancedData&#39;][&#39;backupTime&#39;]) &gt; 0:
                        bkp_time = time.localtime(int(result[&#39;advancedData&#39;][&#39;backupTime&#39;]))
                        bkp_time = time.strftime(&#39;%d/%m/%Y %H:%M:%S&#39;, bkp_time)
                    else:
                        bkp_time = None

                    if &#39;file&#39; in result[&#39;flags&#39;]:
                        if result[&#39;flags&#39;][&#39;file&#39;] in (True, &#39;1&#39;):
                            file_or_folder = &#39;File&#39;
                        else:
                            file_or_folder = &#39;Folder&#39;
                    else:
                        file_or_folder = &#39;Folder&#39;

                    if &#39;size&#39; in result:
                        size = result[&#39;size&#39;]
                    else:
                        size = None
                        
                    if show_deleted and &#39;deleted&#39; in result.get(&#39;flags&#39;):
                        deleted = True if result[&#39;flags&#39;].get(&#39;deleted&#39;) in (True, &#39;1&#39;) else False
                    else:
                        deleted = None

                    paths_dict[path] = {
                        &#39;name&#39;: name,
                        &#39;snap_display_name&#39;: snap_display_name,
                        &#39;size&#39;: size,
                        &#39;modified_time&#39;: mod_time,
                        &#39;type&#39;: file_or_folder,
                        &#39;backup_time&#39;: bkp_time,
                        &#39;advanced_data&#39;: result[&#39;advancedData&#39;],
                        &#39;deleted&#39;: deleted
                    }

                    paths.append(path)

                return paths, paths_dict
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def _do_browse(self, options=None, retry=10):
        &#34;&#34;&#34;Performs a browse operation with the given options.

        Args:
            options     (dict)  --  dictionary of browse options

            retry       (int)   --  Number of times to retry for browse

        Returns:
            list - List of only the file, folder paths from the browse response

            dict - Dictionary of all the paths with additional metadata retrieved from browse
        &#34;&#34;&#34;
        if options is None:
            options = {}

        options = self._prepare_browse_options(options)
        request_json = self._prepare_browse_json(options)

        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._BROWSE, request_json)

        attempt = 1
        while attempt &lt;= retry:
            if response.json() == {}:
                time.sleep(120)
                flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._BROWSE, request_json)
            else:
                break
            attempt += 1
        return self._process_browse_response(flag, response, options)

    def update_properties(self, properties_dict):
        &#34;&#34;&#34;Updates the backupset properties

            Args:
                properties_dict (dict)  --  Backupset property dict which is to be updated

            Returns:
                None

            Raises:
                SDKException:
                    if failed to add

                    if response is empty

                    if response code is not as expected

        **Note** self.properties can be used to get a deep copy of all the properties, modify the properties which you
        need to change and use the update_properties method to set the properties

        &#34;&#34;&#34;
        request_json = {
            &#34;backupsetProperties&#34;: {},
            &#34;association&#34;: {
                &#34;entity&#34;: [
                    {
                        &#34;clientName&#34;: self._client_object.client_name,
                        &#34;backupsetName&#34;: self.backupset_name,
                        &#34;instanceName&#34;: self._instance_object.instance_name,
                        &#34;appName&#34;: self._agent_object.agent_name
                    }
                ]
            }
        }

        request_json[&#39;backupsetProperties&#39;].update(properties_dict)
        status, _, error_string = self._process_update_reponse(request_json)

        if not status:
            raise SDKException(
                &#39;Backupset&#39;,
                &#39;102&#39;,
                &#39;Failed to update backupset property\nError: &#34;{0}&#34;&#39;.format(error_string))

    @property
    def properties(self):
        &#34;&#34;&#34;Returns the backupset properties&#34;&#34;&#34;
        return copy.deepcopy(self._properties)

    @property
    def name(self):
        &#34;&#34;&#34;Returns the Backupset display name&#34;&#34;&#34;
        return self._properties[&#34;backupSetEntity&#34;][&#34;backupsetName&#34;]

    @property
    def backupset_id(self):
        &#34;&#34;&#34;Treats the backupset id as a read-only attribute.&#34;&#34;&#34;
        return self._backupset_id

    @property
    def backupset_name(self):
        &#34;&#34;&#34;Treats the backupset name as a property of the Backupset class.&#34;&#34;&#34;
        return self._backupset_name

    @property
    def description(self):
        &#34;&#34;&#34;Treats the backupset description as a property of the Backupset class.&#34;&#34;&#34;
        return self._description

    @property
    def is_default_backupset(self):
        &#34;&#34;&#34;Treats the is default backupset as a read-only attribute.&#34;&#34;&#34;
        return self._is_default

    @property
    def is_on_demand_backupset(self):
        &#34;&#34;&#34;Treats the is on demand backupset as a read-only attribute.&#34;&#34;&#34;
        return self._is_on_demand_backupset

    @property
    def plan(self):
        &#34;&#34;&#34;Treats the backupset plan as a property of the Backupset class.&#34;&#34;&#34;
        if self._plan_obj is not None:
            return self._plan_obj
        elif self._plan_name is not None:
            self._plan_obj = self._commcell_object.plans.get(self._plan_name)
            return self._plan_obj
        else:
            return None

    @property
    def guid(self):
        &#34;&#34;&#34;Treats the backupset GUID as a property of the Backupset class.&#34;&#34;&#34;
        if self._properties.get(&#39;backupSetEntity&#39;):
            if self._properties[&#39;backupSetEntity&#39;].get(&#39;backupsetGUID&#39;):
                return self._properties[&#39;backupSetEntity&#39;][&#39;backupsetGUID&#39;]
            raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, &#39;Backupset GUID not found&#39;)
        raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, &#39;Backupset entity not found&#39;)

    @backupset_name.setter
    def backupset_name(self, value):
        &#34;&#34;&#34;Sets the name of the backupset as the value provided as input.

            Raises:
                SDKException:
                    if failed to update the backupset name

                    if type of value input is not string
        &#34;&#34;&#34;
        if isinstance(value, str):
            output = self._update(
                backupset_name=value,
                backupset_description=self.description,
                default_backupset=self.is_default_backupset
            )

            if output[0]:
                return
            o_str = &#39;Failed to update the name of the backupset\nError: &#34;{0}&#34;&#39;
            raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, o_str.format(output[2]))

        raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, &#39;Backupset name should be a string value&#39;)

    @description.setter
    def description(self, value):
        &#34;&#34;&#34;Sets the description of the backupset as the value provided as input.

            Raises:
                SDKException:
                    if failed to update the backupset description

                    if type of value input is not string

                    if description cannot be modified for this backupset
        &#34;&#34;&#34;
        if self.description is not None:
            if isinstance(value, str):
                output = self._update(
                    backupset_name=self.backupset_name,
                    backupset_description=value,
                    default_backupset=self.is_default_backupset
                )

                if output[0]:
                    return

                o_str = &#39;Failed to update the description of the backupset\nError: &#34;{0}&#34;&#39;
                raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, o_str.format(output[2]))

            raise SDKException(
                &#39;Backupset&#39;, &#39;102&#39;, &#39;Backupset description should be a string value&#39;
            )

        raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, &#39;Description cannot be modified&#39;)

    @plan.setter
    def plan(self, value):
        &#34;&#34;&#34;Associates the plan to the backupset

            Args:
                value   (object)    --  the Plan object which is to be associated
                                        with the backupset

                value   (str)       --  name of the plan which is to be associated
                                        with the backupset

                value   (None)      --  set value to None to remove plan associations

            Raises:
                SDKException:

                    if plan does not exist

                    if plan association fails

                    if plan is not eligible to be associated
        &#34;&#34;&#34;
        from .plan import Plan
        if isinstance(value, Plan):
            self._plan_obj = value
        elif isinstance(value, str):
            self._plan_obj = self._commcell_object.plans.get(value)
        elif value is None:
            self._plan_obj = None
        else:
            raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, &#39;Input value is not of supported type&#39;)

        plans_obj = self._commcell_object.plans
        entity_dict = {
            &#39;clientId&#39;: int(self._client_object.client_id),
            &#39;appId&#39;: int(self._agent_object.agent_id),
            &#39;backupsetId&#39;: int(self.backupset_id)
        }
        if value is not None and self._plan_obj.plan_name in plans_obj.get_eligible_plans(entity_dict):
            request_json = {
                &#39;backupsetProperties&#39;: {
                    &#39;planEntity&#39;: {
                        &#39;planSubtype&#39;: int(self._plan_obj.subtype),
                        &#39;_type_&#39;: 158,
                        &#39;planType&#39;: int(self._plan_obj.plan_type),
                        &#39;planName&#39;: self._plan_obj.plan_name,
                        &#39;planId&#39;: int(self._plan_obj.plan_id)
                    }
                }
            }

            response = self._process_update_reponse(
                request_json
            )

            if response[0]:
                return
            else:
                o_str = &#39;Failed to asspciate plan to the backupset\nError: &#34;{0}&#34;&#39;
                raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, o_str.format(response[2]))
        elif value is None:
            request_json = {
                &#39;backupsetProperties&#39;: {
                    &#39;removePlanAssociation&#39;: True
                }
            }

            response = self._process_update_reponse(
                request_json
            )

            if response[0]:
                return
            else:
                o_str = &#39;Failed to dissociate plan from backupset\nError: &#34;{0}&#34;&#39;
                raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, o_str.format(response[2]))
        else:
            raise SDKException(
                &#39;Backupset&#39;,
                &#39;102&#39;,
                &#39;Plan not eligible to be associated with the backupset&#39;
            )

    def set_default_backupset(self):
        &#34;&#34;&#34;Sets the backupset represented by this Backupset class instance as the default backupset
            if it is not the default backupset.

            Raises:
                SDKException:
                    if failed to set this as the default backupset
        &#34;&#34;&#34;
        if self.is_default_backupset is False:
            output = self._update(
                backupset_name=self.backupset_name,
                backupset_description=self.description,
                default_backupset=True
            )

            if output[0]:
                return

            o_str = &#39;Failed to set this as the Default Backup Set\nError: &#34;{0}&#34;&#39;
            raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, o_str.format(output[2]))

    def backup(self, **kwargs):
        &#34;&#34;&#34;Runs backup job for all subclients in this backupset.

            kwargs:
                Please refer subclient.backup() for all the supported arguments. Commonly used arguments are,

                backup_level        (str)   --  level of backup the user wish to run
                        Full / Incremental / Differential / Synthetic_full
                    default: Incremental

                advanced_options   (dict)  --  advanced backup options to be included while
                                                    making the request

                common_backup_options      (dict)  --  advanced job options to be included while
                                                        making request

            Returns:
                list    -   list consisting of the job objects for the backup jobs started for
                the subclients in the backupset

        &#34;&#34;&#34;
        return_list = []
        thread_list = []

        if self.subclients.all_subclients:
            for subclient in self.subclients.all_subclients:
                thread = threading.Thread(
                    target=self._run_backup, args=(subclient, return_list), kwargs=kwargs
                )
                thread_list.append(thread)
                thread.start()

        for thread in thread_list:
            thread.join()

        return return_list

    def browse(self, *args, **kwargs):
        &#34;&#34;&#34;Browses the content of the Backupset.

            Args:
                Dictionary of browse options:
                    Example:

                        browse({
                            &#39;path&#39;: &#39;c:\\hello&#39;,

                            &#39;show_deleted&#39;: True,

                            &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,

                            &#39;to_time&#39;: &#39;2016-04-21 12:00:00&#39;
                        })

            Kwargs:
                Keyword argument of browse options:
                    Example:

                        browse(
                            path=&#39;c:\\hello&#39;,

                            show_deleted=True,

                            from_time=&#39;2014-04-20 12:00:00&#39;,

                            to_time=&#39;2016-04-21 12:00:00&#39;
                        )

            Returns:
                (list, dict)
                    list    -   List of only the file, folder paths from the browse response

                    dict    -   Dictionary of all the paths with additional metadata retrieved
                    from browse operation


            Refer `default_browse_options`_ for all the supported options.

            .. _default_browse_options: https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565

        &#34;&#34;&#34;
        if args and isinstance(args[0], dict):
            options = args[0]
        else:
            options = kwargs

        options[&#39;operation&#39;] = &#39;browse&#39;

        return self._do_browse(options)

    def find(self, *args, **kwargs):
        &#34;&#34;&#34;Searches a file/folder in the backed up content of the backupset,
            and returns all the files matching the filters given.

            Args:
                Dictionary of browse options:
                    Example:

                        find({
                            &#39;file_name&#39;: &#39;*.txt&#39;,

                            &#39;show_deleted&#39;: True,

                            &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,

                            &#39;to_time&#39;: &#39;2016-04-31 12:00:00&#39;
                        })

            Kwargs:
                Keyword argument of browse options:
                    Example:

                        find(
                            file_name=&#39;*.txt&#39;,

                            show_deleted=True,

                            &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,

                            to_time=&#39;2016-04-31 12:00:00&#39;
                        )

            Returns:
                (list, dict)
                    list    -   List of only the file, folder paths from the browse response

                    dict    -   Dictionary of all the paths with additional metadata retrieved
                    from browse operation


            Refer `default_browse_options`_ for all the supported options.

            Additional options supported:
                file_name       (str)   --  Find files with name

                file_size_gt    (int)   --  Find files with size greater than size

                file_size_lt    (int)   --  Find files with size lesser than size

                file_size_et    (int)   --  Find files with size equal to size

            .. _default_browse_options: https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565

        &#34;&#34;&#34;
        if args and isinstance(args[0], dict):
            options = args[0]
        else:
            options = kwargs

        if &#39;operation&#39; not in options:
            options[&#39;operation&#39;] = &#39;find&#39;

        if &#39;path&#39; not in options:
            options[&#39;path&#39;] = &#39;\\**\\*&#39;

        if &#39;filters&#39; not in options:
            options[&#39;filters&#39;] = []

        if &#39;file_name&#39; in options:
            options[&#39;filters&#39;].append((&#39;FileName&#39;, options[&#39;file_name&#39;]))

        if &#39;file_size_gt&#39; in options:
            options[&#39;filters&#39;].append((&#39;FileSize&#39;, options[&#39;file_size_gt&#39;], &#39;GTE&#39;))

        if &#39;file_size_lt&#39; in options:
            options[&#39;filters&#39;].append((&#39;FileSize&#39;, options[&#39;file_size_lt&#39;], &#39;LTE&#39;))

        if &#39;file_size_et&#39; in options:
            options[&#39;filters&#39;].append((&#39;FileSize&#39;, options[&#39;file_size_et&#39;], &#39;EQUALSBLAH&#39;))

        return self._do_browse(options)

    def delete_data(self, paths):
        &#34;&#34;&#34;Deletes items for the backupset in the Index and makes them unavailable for
        browsing and recovery

            Args:
                paths       (str/list)      --      The list of paths or single path to delete
                from the backupset

            Returns:
                None        --      If delete request is sent successfully

            Raises:
                Exception, if unable to prepare, response is invalid or send the
                delete data request

        &#34;&#34;&#34;

        options = {
            &#39;operation&#39;: &#39;delete_data&#39;,
            &#39;path&#39;: paths
        }

        files, _ = self._do_browse(options)

        # Delete operation does not return any result, hence consider the operation successful
        if files:
            raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, &#39;Delete data operation gave unexpected results&#39;)

    def list_media(self, *args, **kwargs):
        &#34;&#34;&#34;List media required to browse and restore backed up data from the backupset

            Args:
                Dictionary of options:
                    Example:

                        list_media({
                            &#39;path&#39;: &#39;c:\\hello&#39;,
                            &#39;show_deleted&#39;: True,
                            &#39;from_time&#39;: &#39;2020-04-20 12:00:00&#39;,
                            &#39;to_time&#39;: &#39;2021-04-19 12:00:00&#39;
                        })

            Kwargs:
                Keyword argument of options:
                    Example:

                        list_media(
                            path=&#39;c:\\hello&#39;,
                            show_deleted=True,
                            from_time=&#39;2020-04-20 12:00:00&#39;,
                            to_time=&#39;2021-04-19 12:00:00&#39;
                        )

            Note:
                Refer `_default_browse_options` for all the supported options.

            Returns:
                (List, Dict) -
                    List - List of all the media required for the given options

                    Dict - Total size of the media

            Raises:
                SDKException:
                    if failed to list media for content

                    if response is not success

        &#34;&#34;&#34;

        if args and isinstance(args[0], dict):
            options = args[0]
        else:
            options = kwargs

        options[&#39;operation&#39;] = &#39;list_media&#39;
        options[&#39;_raw_response&#39;] = True

        _, response = self._do_browse(options)

        if response and &#39;browseResponses&#39; in response:
            responses = response[&#39;browseResponses&#39;]
            list_media_response = responses[0]

            prediction_data = list_media_response.get(&#39;predictionData&#39;, [])
            browse_result = list_media_response.get(&#39;browseResult&#39;, {})

            return prediction_data, browse_result
        else:
            raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, &#39;List media operation gave unexpected results&#39;)

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the Backupset.&#34;&#34;&#34;
        self._get_backupset_properties()

        self.subclients = Subclients(self)
        self.schedules = Schedules(self)

    def backed_up_files_count(self, path=&#34;\\**\\*&#34;):
        &#34;&#34;&#34;Returns the count of the total number of files present in the backed up data
         of all the subclients of the given backupset and given path.

                Args:

                    path        (str)       --  Folder path to find no of backed up files
                                                    (Default: \\**\\*)

                Returns:

                    int --  No of backed up files count in given path

                Raises:

                    Exception:

                        if browse response is not proper
         &#34;&#34;&#34;
        options_dic = {&#34;operation&#34;: &#34;find&#34;, &#34;opType&#34;: 1, &#34;path&#34;: path,
                       &#34;_custom_queries&#34;: [{&#34;type&#34;: &#34;AGGREGATE&#34;, &#34;queryId&#34;: &#34;2&#34;,
                                            &#34;aggrParam&#34;: {&#34;aggrType&#34;: &#34;COUNT&#34;}, &#34;whereClause&#34;: [{
                                                &#34;criteria&#34;: {
                                                    &#34;field&#34;: &#34;Flags&#34;,
                                                    &#34;dataOperator&#34;: &#34;IN&#34;,
                                                    &#34;values&#34;: [&#34;file&#34;]
                                                }
                                            }]}], &#34;_raw_response&#34;: True}

        browse_response = self._do_browse(options_dic)
        if not len(browse_response) &gt; 1:
            raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, &#39;Browse response is not proper&#39;)
        browse_response = browse_response[1]
        if &#39;browseResponses&#39; not in browse_response or len(browse_response[&#39;browseResponses&#39;]) == 0:
            raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, &#39;Browse response is missing browseResponses&#39;)
        browse_response = browse_response[&#39;browseResponses&#39;][0]
        if &#39;browseResult&#39; not in browse_response:
            raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, &#39;Browse response is missing browseResult&#39;)
        browse_result = browse_response[&#39;browseResult&#39;]
        if &#39;aggrResultSet&#39; not in browse_result or len(browse_result[&#39;aggrResultSet&#39;]) == 0:
            raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, &#39;Browse response is missing aggrResultSet&#39;)
        return browse_result[&#39;aggrResultSet&#39;][0].get(&#39;count&#39;, 0)</code></pre>
</details>
<h3>Subclasses</h3>
<ul class="hlist">
<li><a title="cvpysdk.backupsets.aadbackupset.AzureAdBackupset" href="backupsets/aadbackupset.html#cvpysdk.backupsets.aadbackupset.AzureAdBackupset">AzureAdBackupset</a></li>
<li><a title="cvpysdk.backupsets.adbackupset.ADBackupset" href="backupsets/adbackupset.html#cvpysdk.backupsets.adbackupset.ADBackupset">ADBackupset</a></li>
<li><a title="cvpysdk.backupsets.cabackupset.CloudAppsBackupset" href="backupsets/cabackupset.html#cvpysdk.backupsets.cabackupset.CloudAppsBackupset">CloudAppsBackupset</a></li>
<li><a title="cvpysdk.backupsets.db2backupset.DB2Backupset" href="backupsets/db2backupset.html#cvpysdk.backupsets.db2backupset.DB2Backupset">DB2Backupset</a></li>
<li><a title="cvpysdk.backupsets.fsbackupset.FSBackupset" href="backupsets/fsbackupset.html#cvpysdk.backupsets.fsbackupset.FSBackupset">FSBackupset</a></li>
<li><a title="cvpysdk.backupsets.hanabackupset.HANABackupset" href="backupsets/hanabackupset.html#cvpysdk.backupsets.hanabackupset.HANABackupset">HANABackupset</a></li>
<li><a title="cvpysdk.backupsets.postgresbackupset.PostgresBackupset" href="backupsets/postgresbackupset.html#cvpysdk.backupsets.postgresbackupset.PostgresBackupset">PostgresBackupset</a></li>
<li><a title="cvpysdk.backupsets.sharepointbackupset.SharepointBackupset" href="backupsets/sharepointbackupset.html#cvpysdk.backupsets.sharepointbackupset.SharepointBackupset">SharepointBackupset</a></li>
<li><a title="cvpysdk.backupsets.vsbackupset.VSBackupset" href="backupsets/vsbackupset.html#cvpysdk.backupsets.vsbackupset.VSBackupset">VSBackupset</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.backupset.Backupset.backupset_id"><code class="name">var <span class="ident">backupset_id</span></code></dt>
<dd>
<div class="desc"><p>Treats the backupset id as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupset.py#L1875-L1878" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def backupset_id(self):
    &#34;&#34;&#34;Treats the backupset id as a read-only attribute.&#34;&#34;&#34;
    return self._backupset_id</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupset.Backupset.backupset_name"><code class="name">var <span class="ident">backupset_name</span></code></dt>
<dd>
<div class="desc"><p>Treats the backupset name as a property of the Backupset class.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupset.py#L1880-L1883" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def backupset_name(self):
    &#34;&#34;&#34;Treats the backupset name as a property of the Backupset class.&#34;&#34;&#34;
    return self._backupset_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupset.Backupset.description"><code class="name">var <span class="ident">description</span></code></dt>
<dd>
<div class="desc"><p>Treats the backupset description as a property of the Backupset class.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupset.py#L1885-L1888" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def description(self):
    &#34;&#34;&#34;Treats the backupset description as a property of the Backupset class.&#34;&#34;&#34;
    return self._description</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupset.Backupset.guid"><code class="name">var <span class="ident">guid</span></code></dt>
<dd>
<div class="desc"><p>Treats the backupset GUID as a property of the Backupset class.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupset.py#L1911-L1918" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def guid(self):
    &#34;&#34;&#34;Treats the backupset GUID as a property of the Backupset class.&#34;&#34;&#34;
    if self._properties.get(&#39;backupSetEntity&#39;):
        if self._properties[&#39;backupSetEntity&#39;].get(&#39;backupsetGUID&#39;):
            return self._properties[&#39;backupSetEntity&#39;][&#39;backupsetGUID&#39;]
        raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, &#39;Backupset GUID not found&#39;)
    raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, &#39;Backupset entity not found&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupset.Backupset.is_default_backupset"><code class="name">var <span class="ident">is_default_backupset</span></code></dt>
<dd>
<div class="desc"><p>Treats the is default backupset as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupset.py#L1890-L1893" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_default_backupset(self):
    &#34;&#34;&#34;Treats the is default backupset as a read-only attribute.&#34;&#34;&#34;
    return self._is_default</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupset.Backupset.is_on_demand_backupset"><code class="name">var <span class="ident">is_on_demand_backupset</span></code></dt>
<dd>
<div class="desc"><p>Treats the is on demand backupset as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupset.py#L1895-L1898" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def is_on_demand_backupset(self):
    &#34;&#34;&#34;Treats the is on demand backupset as a read-only attribute.&#34;&#34;&#34;
    return self._is_on_demand_backupset</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupset.Backupset.name"><code class="name">var <span class="ident">name</span></code></dt>
<dd>
<div class="desc"><p>Returns the Backupset display name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupset.py#L1870-L1873" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def name(self):
    &#34;&#34;&#34;Returns the Backupset display name&#34;&#34;&#34;
    return self._properties[&#34;backupSetEntity&#34;][&#34;backupsetName&#34;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupset.Backupset.plan"><code class="name">var <span class="ident">plan</span></code></dt>
<dd>
<div class="desc"><p>Treats the backupset plan as a property of the Backupset class.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupset.py#L1900-L1909" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def plan(self):
    &#34;&#34;&#34;Treats the backupset plan as a property of the Backupset class.&#34;&#34;&#34;
    if self._plan_obj is not None:
        return self._plan_obj
    elif self._plan_name is not None:
        self._plan_obj = self._commcell_object.plans.get(self._plan_name)
        return self._plan_obj
    else:
        return None</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupset.Backupset.properties"><code class="name">var <span class="ident">properties</span></code></dt>
<dd>
<div class="desc"><p>Returns the backupset properties</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupset.py#L1865-L1868" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def properties(self):
    &#34;&#34;&#34;Returns the backupset properties&#34;&#34;&#34;
    return copy.deepcopy(self._properties)</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.backupset.Backupset.backed_up_files_count"><code class="name flex">
<span>def <span class="ident">backed_up_files_count</span></span>(<span>self, path='\\**\\*')</span>
</code></dt>
<dd>
<div class="desc"><p>Returns the count of the total number of files present in the backed up data
of all the subclients of the given backupset and given path.</p>
<pre><code>   Args:

       path        (str)       --  Folder path to find no of backed up files
                                       (Default: \**\*)

   Returns:

       int --  No of backed up files count in given path

   Raises:

       Exception:

           if browse response is not proper
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupset.py#L2350-L2391" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def backed_up_files_count(self, path=&#34;\\**\\*&#34;):
    &#34;&#34;&#34;Returns the count of the total number of files present in the backed up data
     of all the subclients of the given backupset and given path.

            Args:

                path        (str)       --  Folder path to find no of backed up files
                                                (Default: \\**\\*)

            Returns:

                int --  No of backed up files count in given path

            Raises:

                Exception:

                    if browse response is not proper
     &#34;&#34;&#34;
    options_dic = {&#34;operation&#34;: &#34;find&#34;, &#34;opType&#34;: 1, &#34;path&#34;: path,
                   &#34;_custom_queries&#34;: [{&#34;type&#34;: &#34;AGGREGATE&#34;, &#34;queryId&#34;: &#34;2&#34;,
                                        &#34;aggrParam&#34;: {&#34;aggrType&#34;: &#34;COUNT&#34;}, &#34;whereClause&#34;: [{
                                            &#34;criteria&#34;: {
                                                &#34;field&#34;: &#34;Flags&#34;,
                                                &#34;dataOperator&#34;: &#34;IN&#34;,
                                                &#34;values&#34;: [&#34;file&#34;]
                                            }
                                        }]}], &#34;_raw_response&#34;: True}

    browse_response = self._do_browse(options_dic)
    if not len(browse_response) &gt; 1:
        raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, &#39;Browse response is not proper&#39;)
    browse_response = browse_response[1]
    if &#39;browseResponses&#39; not in browse_response or len(browse_response[&#39;browseResponses&#39;]) == 0:
        raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, &#39;Browse response is missing browseResponses&#39;)
    browse_response = browse_response[&#39;browseResponses&#39;][0]
    if &#39;browseResult&#39; not in browse_response:
        raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, &#39;Browse response is missing browseResult&#39;)
    browse_result = browse_response[&#39;browseResult&#39;]
    if &#39;aggrResultSet&#39; not in browse_result or len(browse_result[&#39;aggrResultSet&#39;]) == 0:
        raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, &#39;Browse response is missing aggrResultSet&#39;)
    return browse_result[&#39;aggrResultSet&#39;][0].get(&#39;count&#39;, 0)</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupset.Backupset.backup"><code class="name flex">
<span>def <span class="ident">backup</span></span>(<span>self, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Runs backup job for all subclients in this backupset.</p>
<p>kwargs:
Please refer subclient.backup() for all the supported arguments. Commonly used arguments are,</p>
<pre><code>backup_level        (str)   --  level of backup the user wish to run
        Full / Incremental / Differential / Synthetic_full
    default: Incremental

advanced_options   (dict)  --  advanced backup options to be included while
                                    making the request

common_backup_options      (dict)  --  advanced job options to be included while
                                        making request
</code></pre>
<h2 id="returns">Returns</h2>
<p>list
-
list consisting of the job objects for the backup jobs started for
the subclients in the backupset</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupset.py#L2080-L2115" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def backup(self, **kwargs):
    &#34;&#34;&#34;Runs backup job for all subclients in this backupset.

        kwargs:
            Please refer subclient.backup() for all the supported arguments. Commonly used arguments are,

            backup_level        (str)   --  level of backup the user wish to run
                    Full / Incremental / Differential / Synthetic_full
                default: Incremental

            advanced_options   (dict)  --  advanced backup options to be included while
                                                making the request

            common_backup_options      (dict)  --  advanced job options to be included while
                                                    making request

        Returns:
            list    -   list consisting of the job objects for the backup jobs started for
            the subclients in the backupset

    &#34;&#34;&#34;
    return_list = []
    thread_list = []

    if self.subclients.all_subclients:
        for subclient in self.subclients.all_subclients:
            thread = threading.Thread(
                target=self._run_backup, args=(subclient, return_list), kwargs=kwargs
            )
            thread_list.append(thread)
            thread.start()

    for thread in thread_list:
        thread.join()

    return return_list</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupset.Backupset.browse"><code class="name flex">
<span>def <span class="ident">browse</span></span>(<span>self, *args, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Browses the content of the Backupset.</p>
<h2 id="args">Args</h2>
<p>Dictionary of browse options:
Example:</p>
<pre><code>    browse({
        'path': 'c:\hello',

        'show_deleted': True,

        'from_time': '2014-04-20 12:00:00',

        'to_time': '2016-04-21 12:00:00'
    })
</code></pre>
<h2 id="kwargs">Kwargs</h2>
<p>Keyword argument of browse options:
Example:</p>
<pre><code>    browse(
        path='c:\hello',

        show_deleted=True,

        from_time='2014-04-20 12:00:00',

        to_time='2016-04-21 12:00:00'
    )
</code></pre>
<h2 id="returns">Returns</h2>
<p>(list, dict)
list
-
List of only the file, folder paths from the browse response</p>
<pre><code>dict    -   Dictionary of all the paths with additional metadata retrieved
from browse operation
</code></pre>
<p>Refer <code>default_browse_options</code>_ for all the supported options.</p>
<p>.. _default_browse_options: <a href="https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565">https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565</a></p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupset.py#L2117-L2168" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def browse(self, *args, **kwargs):
    &#34;&#34;&#34;Browses the content of the Backupset.

        Args:
            Dictionary of browse options:
                Example:

                    browse({
                        &#39;path&#39;: &#39;c:\\hello&#39;,

                        &#39;show_deleted&#39;: True,

                        &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,

                        &#39;to_time&#39;: &#39;2016-04-21 12:00:00&#39;
                    })

        Kwargs:
            Keyword argument of browse options:
                Example:

                    browse(
                        path=&#39;c:\\hello&#39;,

                        show_deleted=True,

                        from_time=&#39;2014-04-20 12:00:00&#39;,

                        to_time=&#39;2016-04-21 12:00:00&#39;
                    )

        Returns:
            (list, dict)
                list    -   List of only the file, folder paths from the browse response

                dict    -   Dictionary of all the paths with additional metadata retrieved
                from browse operation


        Refer `default_browse_options`_ for all the supported options.

        .. _default_browse_options: https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565

    &#34;&#34;&#34;
    if args and isinstance(args[0], dict):
        options = args[0]
    else:
        options = kwargs

    options[&#39;operation&#39;] = &#39;browse&#39;

    return self._do_browse(options)</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupset.Backupset.delete_data"><code class="name flex">
<span>def <span class="ident">delete_data</span></span>(<span>self, paths)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes items for the backupset in the Index and makes them unavailable for
browsing and recovery</p>
<pre><code>Args:
    paths       (str/list)      --      The list of paths or single path to delete
    from the backupset

Returns:
    None        --      If delete request is sent successfully

Raises:
    Exception, if unable to prepare, response is invalid or send the
    delete data request
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupset.py#L2252-L2278" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete_data(self, paths):
    &#34;&#34;&#34;Deletes items for the backupset in the Index and makes them unavailable for
    browsing and recovery

        Args:
            paths       (str/list)      --      The list of paths or single path to delete
            from the backupset

        Returns:
            None        --      If delete request is sent successfully

        Raises:
            Exception, if unable to prepare, response is invalid or send the
            delete data request

    &#34;&#34;&#34;

    options = {
        &#39;operation&#39;: &#39;delete_data&#39;,
        &#39;path&#39;: paths
    }

    files, _ = self._do_browse(options)

    # Delete operation does not return any result, hence consider the operation successful
    if files:
        raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, &#39;Delete data operation gave unexpected results&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupset.Backupset.find"><code class="name flex">
<span>def <span class="ident">find</span></span>(<span>self, *args, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Searches a file/folder in the backed up content of the backupset,
and returns all the files matching the filters given.</p>
<h2 id="args">Args</h2>
<p>Dictionary of browse options:
Example:</p>
<pre><code>    find({
        'file_name': '*.txt',

        'show_deleted': True,

        'from_time': '2014-04-20 12:00:00',

        'to_time': '2016-04-31 12:00:00'
    })
</code></pre>
<h2 id="kwargs">Kwargs</h2>
<p>Keyword argument of browse options:
Example:</p>
<pre><code>    find(
        file_name='*.txt',

        show_deleted=True,

        'from_time': '2014-04-20 12:00:00',

        to_time='2016-04-31 12:00:00'
    )
</code></pre>
<h2 id="returns">Returns</h2>
<p>(list, dict)
list
-
List of only the file, folder paths from the browse response</p>
<pre><code>dict    -   Dictionary of all the paths with additional metadata retrieved
from browse operation
</code></pre>
<p>Refer <code>default_browse_options</code>_ for all the supported options.</p>
<p>Additional options supported:
file_name
(str)
&ndash;
Find files with name</p>
<pre><code>file_size_gt    (int)   --  Find files with size greater than size

file_size_lt    (int)   --  Find files with size lesser than size

file_size_et    (int)   --  Find files with size equal to size
</code></pre>
<p>.. _default_browse_options: <a href="https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565">https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565</a></p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupset.py#L2170-L2250" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def find(self, *args, **kwargs):
    &#34;&#34;&#34;Searches a file/folder in the backed up content of the backupset,
        and returns all the files matching the filters given.

        Args:
            Dictionary of browse options:
                Example:

                    find({
                        &#39;file_name&#39;: &#39;*.txt&#39;,

                        &#39;show_deleted&#39;: True,

                        &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,

                        &#39;to_time&#39;: &#39;2016-04-31 12:00:00&#39;
                    })

        Kwargs:
            Keyword argument of browse options:
                Example:

                    find(
                        file_name=&#39;*.txt&#39;,

                        show_deleted=True,

                        &#39;from_time&#39;: &#39;2014-04-20 12:00:00&#39;,

                        to_time=&#39;2016-04-31 12:00:00&#39;
                    )

        Returns:
            (list, dict)
                list    -   List of only the file, folder paths from the browse response

                dict    -   Dictionary of all the paths with additional metadata retrieved
                from browse operation


        Refer `default_browse_options`_ for all the supported options.

        Additional options supported:
            file_name       (str)   --  Find files with name

            file_size_gt    (int)   --  Find files with size greater than size

            file_size_lt    (int)   --  Find files with size lesser than size

            file_size_et    (int)   --  Find files with size equal to size

        .. _default_browse_options: https://github.com/CommvaultEngg/cvpysdk/blob/master/cvpysdk/backupset.py#L565

    &#34;&#34;&#34;
    if args and isinstance(args[0], dict):
        options = args[0]
    else:
        options = kwargs

    if &#39;operation&#39; not in options:
        options[&#39;operation&#39;] = &#39;find&#39;

    if &#39;path&#39; not in options:
        options[&#39;path&#39;] = &#39;\\**\\*&#39;

    if &#39;filters&#39; not in options:
        options[&#39;filters&#39;] = []

    if &#39;file_name&#39; in options:
        options[&#39;filters&#39;].append((&#39;FileName&#39;, options[&#39;file_name&#39;]))

    if &#39;file_size_gt&#39; in options:
        options[&#39;filters&#39;].append((&#39;FileSize&#39;, options[&#39;file_size_gt&#39;], &#39;GTE&#39;))

    if &#39;file_size_lt&#39; in options:
        options[&#39;filters&#39;].append((&#39;FileSize&#39;, options[&#39;file_size_lt&#39;], &#39;LTE&#39;))

    if &#39;file_size_et&#39; in options:
        options[&#39;filters&#39;].append((&#39;FileSize&#39;, options[&#39;file_size_et&#39;], &#39;EQUALSBLAH&#39;))

    return self._do_browse(options)</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupset.Backupset.list_media"><code class="name flex">
<span>def <span class="ident">list_media</span></span>(<span>self, *args, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>List media required to browse and restore backed up data from the backupset</p>
<h2 id="args">Args</h2>
<p>Dictionary of options:
Example:</p>
<pre><code>    list_media({
        'path': 'c:\hello',
        'show_deleted': True,
        'from_time': '2020-04-20 12:00:00',
        'to_time': '2021-04-19 12:00:00'
    })
</code></pre>
<h2 id="kwargs">Kwargs</h2>
<p>Keyword argument of options:
Example:</p>
<pre><code>    list_media(
        path='c:\hello',
        show_deleted=True,
        from_time='2020-04-20 12:00:00',
        to_time='2021-04-19 12:00:00'
    )
</code></pre>
<h2 id="note">Note</h2>
<p>Refer <code>_default_browse_options</code> for all the supported options.</p>
<h2 id="returns">Returns</h2>
<p>(List, Dict) -
List - List of all the media required for the given options</p>
<pre><code>Dict - Total size of the media
</code></pre>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to list media for content</p>
<pre><code>if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupset.py#L2280-L2341" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def list_media(self, *args, **kwargs):
    &#34;&#34;&#34;List media required to browse and restore backed up data from the backupset

        Args:
            Dictionary of options:
                Example:

                    list_media({
                        &#39;path&#39;: &#39;c:\\hello&#39;,
                        &#39;show_deleted&#39;: True,
                        &#39;from_time&#39;: &#39;2020-04-20 12:00:00&#39;,
                        &#39;to_time&#39;: &#39;2021-04-19 12:00:00&#39;
                    })

        Kwargs:
            Keyword argument of options:
                Example:

                    list_media(
                        path=&#39;c:\\hello&#39;,
                        show_deleted=True,
                        from_time=&#39;2020-04-20 12:00:00&#39;,
                        to_time=&#39;2021-04-19 12:00:00&#39;
                    )

        Note:
            Refer `_default_browse_options` for all the supported options.

        Returns:
            (List, Dict) -
                List - List of all the media required for the given options

                Dict - Total size of the media

        Raises:
            SDKException:
                if failed to list media for content

                if response is not success

    &#34;&#34;&#34;

    if args and isinstance(args[0], dict):
        options = args[0]
    else:
        options = kwargs

    options[&#39;operation&#39;] = &#39;list_media&#39;
    options[&#39;_raw_response&#39;] = True

    _, response = self._do_browse(options)

    if response and &#39;browseResponses&#39; in response:
        responses = response[&#39;browseResponses&#39;]
        list_media_response = responses[0]

        prediction_data = list_media_response.get(&#39;predictionData&#39;, [])
        browse_result = list_media_response.get(&#39;browseResult&#39;, {})

        return prediction_data, browse_result
    else:
        raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, &#39;List media operation gave unexpected results&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupset.Backupset.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the properties of the Backupset.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupset.py#L2343-L2348" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the properties of the Backupset.&#34;&#34;&#34;
    self._get_backupset_properties()

    self.subclients = Subclients(self)
    self.schedules = Schedules(self)</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupset.Backupset.set_default_backupset"><code class="name flex">
<span>def <span class="ident">set_default_backupset</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Sets the backupset represented by this Backupset class instance as the default backupset
if it is not the default backupset.</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to set this as the default backupset</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupset.py#L2059-L2078" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_default_backupset(self):
    &#34;&#34;&#34;Sets the backupset represented by this Backupset class instance as the default backupset
        if it is not the default backupset.

        Raises:
            SDKException:
                if failed to set this as the default backupset
    &#34;&#34;&#34;
    if self.is_default_backupset is False:
        output = self._update(
            backupset_name=self.backupset_name,
            backupset_description=self.description,
            default_backupset=True
        )

        if output[0]:
            return

        o_str = &#39;Failed to set this as the Default Backup Set\nError: &#34;{0}&#34;&#39;
        raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, o_str.format(output[2]))</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupset.Backupset.update_properties"><code class="name flex">
<span>def <span class="ident">update_properties</span></span>(<span>self, properties_dict)</span>
</code></dt>
<dd>
<div class="desc"><p>Updates the backupset properties</p>
<pre><code>Args:
    properties_dict (dict)  --  Backupset property dict which is to be updated

Returns:
    None

Raises:
    SDKException:
        if failed to add

        if response is empty

        if response code is not as expected
</code></pre>
<p><strong>Note</strong> self.properties can be used to get a deep copy of all the properties, modify the properties which you
need to change and use the update_properties method to set the properties</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupset.py#L1821-L1863" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def update_properties(self, properties_dict):
    &#34;&#34;&#34;Updates the backupset properties

        Args:
            properties_dict (dict)  --  Backupset property dict which is to be updated

        Returns:
            None

        Raises:
            SDKException:
                if failed to add

                if response is empty

                if response code is not as expected

    **Note** self.properties can be used to get a deep copy of all the properties, modify the properties which you
    need to change and use the update_properties method to set the properties

    &#34;&#34;&#34;
    request_json = {
        &#34;backupsetProperties&#34;: {},
        &#34;association&#34;: {
            &#34;entity&#34;: [
                {
                    &#34;clientName&#34;: self._client_object.client_name,
                    &#34;backupsetName&#34;: self.backupset_name,
                    &#34;instanceName&#34;: self._instance_object.instance_name,
                    &#34;appName&#34;: self._agent_object.agent_name
                }
            ]
        }
    }

    request_json[&#39;backupsetProperties&#39;].update(properties_dict)
    status, _, error_string = self._process_update_reponse(request_json)

    if not status:
        raise SDKException(
            &#39;Backupset&#39;,
            &#39;102&#39;,
            &#39;Failed to update backupset property\nError: &#34;{0}&#34;&#39;.format(error_string))</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.backupset.Backupsets"><code class="flex name class">
<span>class <span class="ident">Backupsets</span></span>
<span>(</span><span>class_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for getting all the backupsets associated with a client.</p>
<p>Initialize object of the Backupsets class.</p>
<h2 id="args">Args</h2>
<p>class_object
(object)
&ndash;
instance of the Agent / Instance class</p>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the Backupsets class</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if class object is not an instance of the Agent / Instance class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupset.py#L152-L1004" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Backupsets(object):
    &#34;&#34;&#34;Class for getting all the backupsets associated with a client.&#34;&#34;&#34;

    def __init__(self, class_object):
        &#34;&#34;&#34;Initialize object of the Backupsets class.

            Args:
                class_object    (object)    --  instance of the Agent / Instance class

            Returns:
                object  -   instance of the Backupsets class

            Raises:
                SDKException:
                    if class object is not an instance of the Agent / Instance class

        &#34;&#34;&#34;
        from .agent import Agent
        from .instance import Instance

        self._instance_object = None

        if isinstance(class_object, Agent):
            self._agent_object = class_object
        elif isinstance(class_object, Instance):
            self._instance_object = class_object
            self._agent_object = class_object._agent_object
        else:
            raise SDKException(&#39;Backupset&#39;, &#39;103&#39;)

        self._client_object = self._agent_object._client_object

        self._commcell_object = self._agent_object._commcell_object

        self._cvpysdk_object = self._commcell_object._cvpysdk_object
        self._services = self._commcell_object._services
        self._update_response_ = self._commcell_object._update_response_

        self._BACKUPSETS = self._services[&#39;GET_ALL_BACKUPSETS&#39;] % (self._client_object.client_id)
        if self._agent_object:
            self._BACKUPSETS += &#39;&amp;applicationId=&#39; + self._agent_object.agent_id

        if self._instance_object:
            self._BACKUPSETS += &#39;&amp;instanceId=&#39; + self._instance_object.instance_id

        if self._agent_object.agent_name in [&#39;cloud apps&#39;, &#39;sql server&#39;, &#39;sap hana&#39;]:
            self._BACKUPSETS += &#39;&amp;excludeHidden=0&#39;

        self._backupsets = None
        self._default_backup_set = None
        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all backupsets of the agent of a client.

            Returns:
                str - string of all the backupsets of an agent of a client
        &#34;&#34;&#34;
        representation_string = &#39;{:^5}\t{:^20}\t{:^20}\t{:^20}\t{:^20}\n\n&#39;.format(
            &#39;S. No.&#39;, &#39;Backupset&#39;, &#39;Instance&#39;, &#39;Agent&#39;, &#39;Client&#39;
        )

        for index, backupset in enumerate(self._backupsets):
            sub_str = &#39;{:^5}\t{:20}\t{:20}\t{:20}\t{:20}\n&#39;.format(
                index + 1,
                backupset.split(&#39;\\&#39;)[-1],
                self._backupsets[backupset][&#39;instance&#39;],
                self._agent_object.agent_name,
                self._client_object.client_name
            )
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the Backupsets class.&#34;&#34;&#34;
        return &#34;Backupsets class instance for Agent: &#39;{0}&#39;&#34;.format(self._agent_object.agent_name)

    def __len__(self):
        &#34;&#34;&#34;Returns the number of the backupsets for the selected Agent.&#34;&#34;&#34;
        return len(self.all_backupsets)

    def __getitem__(self, value):
        &#34;&#34;&#34;Returns the name of the backupset for the given backupset ID or
            the details of the backupset for given backupset Name.

            Args:
                value   (str / int)     --  Name or ID of the backupset

            Returns:
                str     -   name of the backupset, if the backupset id was given

                dict    -   dict of details of the backupset, if backupset name was given

            Raises:
                IndexError:
                    no backupset exists with the given Name / Id

        &#34;&#34;&#34;
        value = str(value)

        if value in self.all_backupsets:
            return self.all_backupsets[value]
        else:
            try:
                return list(
                    filter(lambda x: x[1][&#39;id&#39;] == value, self.all_backupsets.items())
                )[0][0]
            except IndexError:
                raise IndexError(&#39;No backupset exists with the given Name / Id&#39;)

    def _get_backupsets(self):
        &#34;&#34;&#34;Gets all the backupsets associated to the agent specified by agent_object.

            Returns:
                dict - consists of all backupsets of the agent
                    {
                         &#34;backupset1_name&#34;: {
                             &#34;id&#34;: backupset1_id,
                             &#34;instance&#34;: instance
                         },
                         &#34;backupset2_name&#34;: {
                             &#34;id&#34;: backupset2_id,
                             &#34;instance&#34;: instance
                         }
                    }

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._BACKUPSETS)

        if flag:
            if response.json() and &#39;backupsetProperties&#39; in response.json():
                return_dict = {}

                for dictionary in response.json()[&#39;backupsetProperties&#39;]:
                    agent = dictionary[&#39;backupSetEntity&#39;][&#39;appName&#39;].lower()
                    instance = dictionary[&#39;backupSetEntity&#39;][&#39;instanceName&#39;].lower()

                    if self._instance_object is not None:
                        if (self._instance_object.instance_name in instance and
                                self._agent_object.agent_name in agent):
                            temp_name = dictionary[&#39;backupSetEntity&#39;][&#39;backupsetName&#39;].lower()
                            temp_id = str(dictionary[&#39;backupSetEntity&#39;][&#39;backupsetId&#39;]).lower()
                            return_dict[temp_name] = {
                                &#34;id&#34;: temp_id,
                                &#34;instance&#34;: instance
                            }

                            if dictionary[&#39;commonBackupSet&#39;].get(&#39;isDefaultBackupSet&#39;):
                                self._default_backup_set = temp_name

                    elif self._agent_object.agent_name in agent:
                        temp_name = dictionary[&#39;backupSetEntity&#39;][&#39;backupsetName&#39;].lower()
                        temp_id = str(dictionary[&#39;backupSetEntity&#39;][&#39;backupsetId&#39;]).lower()

                        if len(self._agent_object.instances.all_instances) &gt; 1:
                            return_dict[&#34;{0}\\{1}&#34;.format(instance, temp_name)] = {
                                &#34;id&#34;: temp_id,
                                &#34;instance&#34;: instance
                            }

                            if dictionary[&#39;commonBackupSet&#39;].get(&#39;isDefaultBackupSet&#39;):
                                self._default_backup_set = &#34;{0}\\{1}&#34;.format(instance, temp_name)
                        else:
                            return_dict[temp_name] = {
                                &#34;id&#34;: temp_id,
                                &#34;instance&#34;: instance
                            }

                            if dictionary[&#39;commonBackupSet&#39;].get(&#39;isDefaultBackupSet&#39;):
                                self._default_backup_set = temp_name

                return return_dict
            else:
                return {}
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    @property
    def all_backupsets(self):
        &#34;&#34;&#34;Returns the dict of backupsets for the Agent / Instance of the selected Client

            dict - consists of all backupsets
                    {
                         &#34;backupset1_name&#34;: {
                             &#34;id&#34;: backupset1_id,
                             &#34;instance&#34;: instance
                         },
                         &#34;backupset2_name&#34;: {
                             &#34;id&#34;: backupset2_id,
                             &#34;instance&#34;: instance
                         }
                    }
        &#34;&#34;&#34;
        return self._backupsets

    def has_backupset(self, backupset_name):
        &#34;&#34;&#34;Checks if a backupset exists for the agent with the input backupset name.

            Args:
                backupset_name (str)  --  name of the backupset

            Returns:
                bool - boolean output whether the backupset exists for the agent or not

            Raises:
                SDKException:
                    if type of the backupset name argument is not string
        &#34;&#34;&#34;
        if not isinstance(backupset_name, str):
            raise SDKException(&#39;Backupset&#39;, &#39;101&#39;)

        return self._backupsets and backupset_name.lower() in self._backupsets

    def _process_add_response(self, backupset_name, request_json):
        &#34;&#34;&#34;Runs the Backupset Add API with the request JSON provided,
            and returns the contents after parsing the response.

            Args:
                backupset_name   (str)  --   backupset name
                request_json    (dict)  --  JSON request to run for the API

            Returns:
                (bool, str, str):
                    bool -  flag specifies whether success / failure

                    str  -  error code received in the response

                    str  -  error message received

            Raises:
                SDKException:
                    if response is empty

                    if response is not success
        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;POST&#39;, self._services[&#39;ADD_BACKUPSET&#39;], request_json)

        if flag:
            if response.json():
                if &#39;response&#39; in response.json():
                    error_code = response.json()[&#39;response&#39;][0][&#39;errorCode&#39;]

                    if error_code != 0:
                        error_string = response.json()[&#39;response&#39;][0][&#39;errorString&#39;]
                        o_str = &#39;Failed to create backupset\nError: &#34;{0}&#34;&#39;.format(error_string)
                        raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, o_str)
                    else:
                        # initialize the backupsets again
                        # so the backupset object has all the backupsets
                        self.refresh()
                        return self.get(backupset_name)
                elif &#39;errorMessage&#39; in response.json():
                    error_string = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Failed to create backuspet\nError: &#34;{0}&#34;&#39;.format(error_string)
                    raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, o_str)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def add(self, backupset_name, on_demand_backupset=False, **kwargs):
        &#34;&#34;&#34;Adds a new backup set to the agent.

            Args:
                backupset_name      (str)   --  name of the new backupset to add

                on_demand_backupset (bool)  --  flag to specify whether the backupset to be added
                is a simple backupset or an on-demand backupset

                    default: False

                **kwargs    --  dict of keyword arguments as follows:

                    storage_policy  (str)   --  name of the storage policy to associate to the
                    backupset

                    plan_name       (str)   -- name of the plan to associate to the backupset

                    is_nas_turbo_backupset  (bool)    --  True for NAS based client.


            Returns:
                object - instance of the Backupset class, if created successfully

            Raises:
                SDKException:
                    if type of the backupset name argument is not string

                    if failed to create a backupset

                    if response is empty

                    if response is not success

                    if backupset with same name already exists
        &#34;&#34;&#34;
        if not (isinstance(backupset_name, str) and isinstance(on_demand_backupset, bool)):
            raise SDKException(&#39;Backupset&#39;, &#39;101&#39;)

        if self.has_backupset(backupset_name):
            raise SDKException(
                &#39;Backupset&#39;, &#39;102&#39;, &#39;Backupset &#34;{0}&#34; already exists.&#39;.format(backupset_name)
            )

        if self._instance_object is None:
            if self._agent_object.instances.has_instance(&#39;DefaultInstanceName&#39;):
                self._instance_object = self._agent_object.instances.get(&#39;DefaultInstanceName&#39;)
            else:
                self._instance_object = self._agent_object.instances.get(
                    sorted(self._agent_object.instances.all_instances)[0]
                )

        request_json = {
            &#34;association&#34;: {
                &#34;entity&#34;: [{
                    &#34;clientName&#34;: self._client_object.client_name,
                    &#34;appName&#34;: self._agent_object.agent_name,
                    &#34;instanceName&#34;: self._instance_object.instance_name,
                    &#34;backupsetName&#34;: backupset_name
                }]
            },
            &#34;backupSetInfo&#34;: {
                &#34;commonBackupSet&#34;: {
                    &#34;onDemandBackupset&#34;: on_demand_backupset
                }
            }
        }

        if kwargs.get(&#39;is_nas_turbo_type&#39;):
            request_json[&#34;backupSetInfo&#34;][&#34;commonBackupSet&#34;][&#34;isNasTurboBackupSet&#34;] = kwargs.get(&#39;is_nas_turbo_type&#39;,
                                                                                                 False)

        agent_settings = {
            &#39;db2&#39;: &#34;&#34;&#34;
request_json[&#39;backupSetInfo&#39;].update({
    &#39;db2BackupSet&#39;: {
        &#39;dB2DefaultIndexSP&#39;: {
            &#39;storagePolicyName&#39;: kwargs.get(&#39;storage_policy&#39;, &#39;&#39;)
        }
    }
})
            &#34;&#34;&#34;
        }

        exec(agent_settings.get(self._agent_object.agent_name, &#39;&#39;))

        if kwargs.get(&#39;plan_name&#39;):
            plan_entity_dict = {
                &#34;planName&#34;: kwargs.get(&#39;plan_name&#39;)
            }
            request_json[&#39;backupSetInfo&#39;][&#39;planEntity&#39;] = plan_entity_dict

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;ADD_BACKUPSET&#39;], request_json
        )

        if flag:
            if response.json():
                if &#39;response&#39; in response.json():
                    response_value = response.json()[&#39;response&#39;][0]
                    error_code = str(response_value[&#39;errorCode&#39;])
                    error_message = None

                    if &#39;errorString&#39; in response_value:
                        error_message = response_value[&#39;errorString&#39;]

                    if error_message:
                        o_str = &#39;Failed to create new backupset\nError: &#34;{0}&#34;&#39;.format(
                            error_message
                        )
                        raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, o_str)
                    else:
                        if error_code == &#39;0&#39;:
                            # initialize the backupsets again
                            # so the backupsets object has all the backupsets
                            self.refresh()

                            return self.get(backupset_name)

                        else:
                            o_str = (&#39;Failed to create new backupset with error code: &#34;{0}&#34;\n&#39;
                                     &#39;Please check the documentation for &#39;
                                     &#39;more details on the error&#39;).format(error_code)

                            raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, o_str)
                else:
                    error_code = response.json()[&#39;errorCode&#39;]
                    error_message = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Failed to create new backupset\nError: &#34;{0}&#34;&#39;.format(
                        error_message
                    )
                    raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def add_archiveset(self, archiveset_name, is_nas_turbo_backupset=False):
        &#34;&#34;&#34; 
        Adds a new archiveset to the agent. It is just a backupset but is mainly used for archive only items

        Args:
            archiveset_name     (str) -- name of new archiveset to add

            is_nas_turbo_backupset  (bool) -- True for NAS based client.
                default -   False
            
        Returns:
        object - instance of the Backupset class, if created successfully

        Raises:
            SDKException:
                if type of the archiveset name argument is not string

                if failed to create a archiveset

                if response is empty

                if response is not success

                if archiveset with same name already exists
                

        &#34;&#34;&#34;        
        if not (isinstance(archiveset_name, str)):
            raise SDKException(&#39;Backupset&#39;, &#39;101&#39;)
        else:
            archiveset_name = archiveset_name.lower()

        if self.has_backupset(archiveset_name):
            raise SDKException(&#39;archiveset_name&#39;, &#39;102&#39;, &#39;Archiveset &#34;{0}&#34; already exists.&#39;.format(archiveset_name))

        if self._agent_object.agent_id not in [&#39;29&#39;, &#39;33&#39;]:
            raise SDKException(&#39;Backupset&#39;, &#39;101&#39;, &#34;Archiveset is not applicable to this application type.&#34;)



        request_json = {
            &#34;backupSetInfo&#34;: {
                &#34;useContentFromPlan&#34;: False,
                &#34;planEntity&#34;: {},
                &#34;commonBackupSet&#34;: {
                    &#34;isNasTurboBackupSet&#34;: is_nas_turbo_backupset,
                    &#34;isArchivingEnabled&#34;: True,
                    &#34;isDefaultBackupSet&#34;: False
                },
                &#34;backupSetEntity&#34;: {
                    &#34;_type_&#34;: 6,
                    &#34;clientId&#34;: int(self._client_object.client_id),
                    &#34;backupsetName&#34;: archiveset_name,
                    &#34;applicationId&#34;: int(self._agent_object.agent_id)
                },
                &#34;subClientList&#34;: [
                    {
                        &#34;contentOperationType&#34;: 1,
                        &#34;fsSubClientProp&#34;: {
                            &#34;useGlobalFilters&#34;: 2,
                            &#34;forcedArchiving&#34;: True,
                            &#34;diskCleanupRules&#34;: {
                                &#34;enableArchivingWithRules&#34;: True,
                                &#34;diskCleanupFileTypes&#34;: {}
                            }
                        },
                        &#34;content&#34;: [
                            {
                                &#34;path&#34;: &#34;&#34;
                            }
                        ]
                    }
                ]
            }
        }
        
        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;ADD_BACKUPSET&#39;], request_json
        )

        if flag:
            if response.json():
                if &#39;response&#39; in response.json():
                    response_value = response.json()[&#39;response&#39;][0]
                    error_code = str(response_value[&#39;errorCode&#39;])
                    error_message = None

                    if &#39;errorString&#39; in response_value:
                        error_message = response_value[&#39;errorString&#39;]

                    if error_message:
                        o_str = &#39;Failed to create new Archiveset\nError: &#34;{0}&#34;&#39;.format(
                            error_message
                        )
                        raise SDKException(&#39;Archiveset&#39;, &#39;102&#39;, o_str)
                    else:
                        if error_code == &#39;0&#39;:
                            # initialize the backupsets again
                            # so the backupsets object has all the backupsets
                            self.refresh()
                            return self.get(archiveset_name)
                        
                        else:
                            o_str = (&#39;Failed to create new Archiveset with error code: &#34;{0}&#34;\n&#39;
                                     &#39;Please check the documentation for &#39;
                                     &#39;more details on the error&#39;).format(error_code)

                            raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, o_str)
                else:
                    error_code = response.json()[&#39;errorCode&#39;]
                    error_message = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Failed to create new Archiveset\nError: &#34;{0}&#34;&#39;.format(
                        error_message
                    )
                    raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))

    def add_v1_sharepoint_client(
            self,
            backupset_name,
            server_plan,
            client_name,
            **kwargs):
        &#34;&#34;&#34;
        for sharepoint v1 client creation is a backupset
        Adds a new Office 365 V1 Share Point Pseudo Client to the Commcell.

                Args:
                    backupset_name                 (str)   --  name of the new Sharepoint Pseudo Client

                    server_plan                 (str)   --  server_plan to associate with the client

                    client_name                 (str) -- the access node for which Pseudo Client will be created


                Kwargs :

                    tenant_url                  (str)   --  url of sharepoint tenant

                    azure_username              (str)   --  username of azure app

                    azure_secret                (str)   --  secret key of azure app

                    user_username        (str)   --  username of Sharepoint admin

                    user_password           (str)  -- password of Sharepoint admin

                    azure_app_id            (str)       --  azure app id for sharepoint online

                    azure_app_key_id        (str)       --  app key for sharepoint online

                    azure_directory_id    (str)   --  azure directory id for sharepoint online


                Returns:
                    object  -   instance of the Client class for this new client

                Raises:
                    SDKException:
                        if client with given name already exists

                        if index_server is not found

                        if server_plan is not found

                        if failed to add the client

                        if response is empty

                        if response is not success

        &#34;&#34;&#34;
        if self.has_backupset(backupset_name):
            raise SDKException(
                &#39;Backupset&#39;, &#39;102&#39;, &#39;Backupset &#34;{0}&#34; already exists.&#39;.format(backupset_name))
        if self._commcell_object.plans.has_plan(server_plan):
            server_plan_object = self._commcell_object.plans.get(server_plan)
            server_plan_dict = {
                &#34;planId&#34;: int(server_plan_object.plan_id)
            }
        else:
            raise SDKException(&#39;Backupset&#39;, &#39;102&#39;)
        backup_set = {
            &#34;_type_&#34;: 6,
            &#34;applicationId&#34;: 78,
            &#34;backupsetName&#34;: backupset_name,
            &#34;clientId&#34;: int(self._client_object.client_id)
        }
        request_json = {
            &#34;backupSetInfo&#34;: {
                &#34;planEntity&#34;: server_plan_dict,
                &#34;backupSetEntity&#34;: backup_set,
                &#34;sharepointBackupSet&#34;: {
                    &#34;sharepointBackupSetType&#34;: 4
                }
            }
        }
        tenant_url = kwargs.get(&#39;tenant_url&#39;)
        user_username = kwargs.get(&#39;user_username&#39;)
        is_modern_auth_enabled = kwargs.get(&#39;is_modern_auth_enabled&#39;,False)
        azure_secret = b64encode(kwargs.get(&#39;azure_secret&#39;).encode()).decode()
        azure_app_key_id = b64encode(kwargs.get(&#39;azure_app_key_id&#39;).encode()).decode()
        user_password = b64encode(kwargs.get(&#39;user_password&#39;).encode()).decode()
        request_json[&#34;backupSetInfo&#34;][&#34;sharepointBackupSet&#34;][
            &#34;spOffice365BackupSetProp&#34;] = {
            &#34;azureUserAccount&#34;: kwargs.get(&#39;azure_username&#39;),
            &#34;azureAccountKey&#34;: azure_secret,
            &#34;tenantUrlItem&#34;: tenant_url,
            &#34;isModernAuthEnabled&#34;: is_modern_auth_enabled,
            &#34;office365Credentials&#34;: {
                &#34;userName&#34;: user_username,
                &#34;password&#34;: user_password
            },
        }
        if is_modern_auth_enabled:
            request_json[&#34;backupSetInfo&#34;][&#34;sharepointBackupSet&#34;][
                &#34;spOffice365BackupSetProp&#34;][&#34;azureAppList&#34;] = {
                &#34;azureApps&#34;: [
                    {
                        &#34;azureAppId&#34;: kwargs.get(&#39;azure_app_id&#39;),
                        &#34;azureAppKeyValue&#34;: azure_app_key_id,
                        &#34;azureDirectoryId&#34;: kwargs.get(&#39;azure_directory_id&#39;)
                    }
                ]
            }

        self._process_add_response(backupset_name, request_json)

    def add_salesforce_backupset(
            self,
            salesforce_options,
            db_options=None, **kwargs):
        &#34;&#34;&#34;Adds a new Salesforce Backupset to the Commcell.

            Args:

                salesforce_options         (dict)       --  salesforce options
                                                            {
                                                                &#34;salesforce_user_name&#34;: &#39;salesforce login user&#39;,
                                                                &#34;salesforce_user_password&#34;: &#39;salesforce user password&#39;,
                                                                &#34;salesforce_user_token&#34;: &#39;salesforce user token&#39;
                                                            }

                db_options                 (dict)       --  database options to configure sync db
                                                            {
                                                                &#34;db_enabled&#34;: &#39;True or False&#39;,
                                                                &#34;db_type&#34;: &#39;SQLSERVER or POSTGRESQL&#39;,
                                                                &#34;db_host_name&#34;: &#39;database hostname&#39;,
                                                                &#34;db_instance&#34;: &#39;database instance name&#39;,
                                                                &#34;db_name&#34;: &#39;database name&#39;,
                                                                &#34;db_port&#34;: &#39;port of the database&#39;,
                                                                &#34;db_user_name&#34;: &#39;database user name&#39;,
                                                                &#34;db_user_password&#34;: &#39;database user password&#39;
                                                            }

                **kwargs                   (dict)       --     dict of keyword arguments as follows

                                                            download_cache_path     (str)   -- download cache path
                                                            mutual_auth_path        (str)   -- mutual auth cert path
                                                            storage_policy          (str)   -- storage policy
                                                            streams                 (int)   -- number of streams

            Returns:
                object  -   instance of the Backupset class for this new backupset

            Raises:
                SDKException:
                    if backupset with given name already exists

                    if failed to add the backupset

                    if response is empty

                    if response is not success
        &#34;&#34;&#34;

        if db_options is None:
            db_options = {&#39;db_enabled&#39;: False}
        if self.has_backupset(salesforce_options.get(&#39;salesforce_user_name&#39;)):
            raise SDKException(&#39;Backupset&#39;, &#39;102&#39;,
                               &#39;Backupset &#34;{0}&#34; already exists.&#39;.format(salesforce_options.get(&#39;salesforce_user_name&#39;)))

        salesforce_password = b64encode(salesforce_options.get(&#39;salesforce_user_password&#39;).encode()).decode()
        salesforce_token = b64encode(salesforce_options.get(&#39;salesforce_user_token&#39;, &#39;&#39;).encode()).decode()
        db_user_password = &#34;&#34;
        if db_options.get(&#39;db_enabled&#39;, False):
            db_user_password = b64encode(db_options.get(&#39;db_user_password&#39;).encode()).decode()

        request_json = {
            &#34;backupSetInfo&#34;: {
                &#34;backupSetEntity&#34;: {
                    &#34;clientName&#34;: self._client_object.client_name,
                    &#34;instanceName&#34;: self._instance_object.instance_name,
                    &#34;backupsetName&#34;: salesforce_options.get(&#39;salesforce_user_name&#39;),
                    &#34;appName&#34;: self._agent_object.agent_name
                },
                &#34;cloudAppsBackupset&#34;: {
                    &#34;instanceType&#34;: 3,
                    &#34;salesforceBackupSet&#34;: {
                        &#34;enableREST&#34;: True,
                        &#34;downloadCachePath&#34;: kwargs.get(&#39;download_cache_path&#39;, &#39;/tmp&#39;),
                        &#34;mutualAuthPath&#34;: kwargs.get(&#39;mutual_auth_path&#39;, &#39;&#39;),
                        &#34;token&#34;: salesforce_token,
                        &#34;userPassword&#34;: {
                            &#34;userName&#34;: salesforce_options.get(&#39;salesforce_user_name&#39;),
                            &#34;password&#34;: salesforce_password,
                        },
                        &#34;syncDatabase&#34;: {
                            &#34;dbEnabled&#34;: db_options.get(&#39;db_enabled&#39;, False),
                            &#34;dbPort&#34;: db_options.get(&#39;db_port&#39;, &#39;1433&#39;),
                            &#34;dbInstance&#34;: db_options.get(&#39;db_instance&#39;, &#39;&#39;),
                            &#34;dbName&#34;: db_options.get(&#39;db_name&#39;, self._instance_object.instance_name),
                            &#34;dbType&#34;: db_options.get(&#39;db_type&#39;, &#34;SQLSERVER&#34;),
                            &#34;dbHost&#34;: db_options.get(&#39;db_host_name&#39;, &#39;&#39;),
                            &#34;dbUserPassword&#34;: {
                                &#34;userName&#34;: db_options.get(&#39;db_user_name&#39;, &#39;&#39;),
                                &#34;password&#34;: db_user_password,

                            },
                        },
                    },
                    &#34;generalCloudProperties&#34;: {
                        &#34;numberOfBackupStreams&#34;: kwargs.get(&#39;streams&#39;, 2),
                        &#34;storageDevice&#34;: {
                            &#34;dataBackupStoragePolicy&#34;: {
                                &#34;storagePolicyName&#34;: kwargs.get(&#39;storage_policy&#39;, &#39;&#39;)
                            },
                        },
                    },
                },
            },
        }

        self._process_add_response(salesforce_options.get(&#39;salesforce_user_name&#39;), request_json)

    def get(self, backupset_name):
        &#34;&#34;&#34;Returns a backupset object of the specified backupset name.

            Args:
                backupset_name (str)  --  name of the backupset

            Returns:
                object - instance of the Backupset class for the given backupset name

            Raises:
                SDKException:
                    if type of the backupset name argument is not string

                    if no backupset exists with the given name
        &#34;&#34;&#34;
        if not isinstance(backupset_name, str):
            raise SDKException(&#39;Backupset&#39;, &#39;101&#39;)
        else:
            backupset_name = backupset_name.lower()

            if self.has_backupset(backupset_name):
                if self._instance_object is None:
                    self._instance_object = self._agent_object.instances.get(
                        self._backupsets[backupset_name][&#39;instance&#39;]
                    )
                return Backupset(
                    self._instance_object,
                    backupset_name,
                    self._backupsets[backupset_name][&#34;id&#34;]
                )

            raise SDKException(
                &#39;Backupset&#39;, &#39;102&#39;, &#39;No backupset exists with name: &#34;{0}&#34;&#39;.format(backupset_name)
            )

    def delete(self, backupset_name):
        &#34;&#34;&#34;Deletes the backup set from the agent.

            Args:
                backupset_name (str)  --  name of the backupset

            Raises:
                SDKException:
                    if type of the backupset name argument is not string

                    if failed to delete the backupset

                    if response is empty

                    if response is not success

                    if no backupset exists with the given name
        &#34;&#34;&#34;
        if not isinstance(backupset_name, str):
            raise SDKException(&#39;Backupset&#39;, &#39;101&#39;)
        else:
            backupset_name = backupset_name.lower()

        if self.has_backupset(backupset_name):
            delete_backupset_service = self._services[&#39;BACKUPSET&#39;] % (
                self._backupsets[backupset_name][&#39;id&#39;]
            )

            flag, response = self._cvpysdk_object.make_request(&#39;DELETE&#39;, delete_backupset_service)

            if flag:
                if response.json():
                    if &#39;response&#39; in response.json():
                        response_value = response.json()[&#39;response&#39;][0]
                        error_code = str(response_value[&#39;errorCode&#39;])
                        error_message = None

                        if &#39;errorString&#39; in response_value:
                            error_message = response_value[&#39;errorString&#39;]

                        if error_message:
                            o_str = &#39;Failed to delete backupset\nError: &#34;{0}&#34;&#39;
                            raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, o_str.format(error_message))
                        else:
                            if error_code == &#39;0&#39;:
                                # initialize the backupsets again
                                # so the backupsets object has all the backupsets
                                self.refresh()
                            else:
                                o_str = (&#39;Failed to delete backupset with error code: &#34;{0}&#34;\n&#39;
                                         &#39;Please check the documentation for &#39;
                                         &#39;more details on the error&#39;).format(error_code)
                                raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, o_str)
                    else:
                        error_code = response.json()[&#39;errorCode&#39;]
                        error_message = response.json()[&#39;errorMessage&#39;]
                        o_str = &#39;Failed to delete backupset\nError: &#34;{0}&#34;&#39;.format(error_message)
                        raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, o_str)
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
        else:
            raise SDKException(
                &#39;Backupset&#39;, &#39;102&#39;, &#39;No backupset exists with name: &#34;{0}&#34;&#39;.format(backupset_name)
            )

    def refresh(self):
        &#34;&#34;&#34;Refresh the backupsets associated with the Agent / Instance.&#34;&#34;&#34;
        self._backupsets = self._get_backupsets()

    @property
    def default_backup_set(self):
        &#34;&#34;&#34;Returns the name of the default backup set for the selected Client and Agent.&#34;&#34;&#34;
        return self._default_backup_set</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.backupset.Backupsets.all_backupsets"><code class="name">var <span class="ident">all_backupsets</span></code></dt>
<dd>
<div class="desc"><p>Returns the dict of backupsets for the Agent / Instance of the selected Client</p>
<p>dict - consists of all backupsets
{
"backupset1_name": {
"id": backupset1_id,
"instance": instance
},
"backupset2_name": {
"id": backupset2_id,
"instance": instance
}
}</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupset.py#L335-L351" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def all_backupsets(self):
    &#34;&#34;&#34;Returns the dict of backupsets for the Agent / Instance of the selected Client

        dict - consists of all backupsets
                {
                     &#34;backupset1_name&#34;: {
                         &#34;id&#34;: backupset1_id,
                         &#34;instance&#34;: instance
                     },
                     &#34;backupset2_name&#34;: {
                         &#34;id&#34;: backupset2_id,
                         &#34;instance&#34;: instance
                     }
                }
    &#34;&#34;&#34;
    return self._backupsets</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupset.Backupsets.default_backup_set"><code class="name">var <span class="ident">default_backup_set</span></code></dt>
<dd>
<div class="desc"><p>Returns the name of the default backup set for the selected Client and Agent.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupset.py#L1001-L1004" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def default_backup_set(self):
    &#34;&#34;&#34;Returns the name of the default backup set for the selected Client and Agent.&#34;&#34;&#34;
    return self._default_backup_set</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.backupset.Backupsets.add"><code class="name flex">
<span>def <span class="ident">add</span></span>(<span>self, backupset_name, on_demand_backupset=False, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds a new backup set to the agent.</p>
<h2 id="args">Args</h2>
<p>backupset_name
(str)
&ndash;
name of the new backupset to add</p>
<p>on_demand_backupset (bool)
&ndash;
flag to specify whether the backupset to be added
is a simple backupset or an on-demand backupset</p>
<pre><code>default: False
</code></pre>
<p>**kwargs
&ndash;
dict of keyword arguments as follows:</p>
<pre><code>storage_policy  (str)   --  name of the storage policy to associate to the
backupset

plan_name       (str)   -- name of the plan to associate to the backupset

is_nas_turbo_backupset  (bool)    --  True for NAS based client.
</code></pre>
<h2 id="returns">Returns</h2>
<p>object - instance of the Backupset class, if created successfully</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the backupset name argument is not string</p>
<pre><code>if failed to create a backupset

if response is empty

if response is not success

if backupset with same name already exists
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupset.py#L420-L555" class="git-link">Browse git</a>
</summary>
<pre><code class="python">    def add(self, backupset_name, on_demand_backupset=False, **kwargs):
        &#34;&#34;&#34;Adds a new backup set to the agent.

            Args:
                backupset_name      (str)   --  name of the new backupset to add

                on_demand_backupset (bool)  --  flag to specify whether the backupset to be added
                is a simple backupset or an on-demand backupset

                    default: False

                **kwargs    --  dict of keyword arguments as follows:

                    storage_policy  (str)   --  name of the storage policy to associate to the
                    backupset

                    plan_name       (str)   -- name of the plan to associate to the backupset

                    is_nas_turbo_backupset  (bool)    --  True for NAS based client.


            Returns:
                object - instance of the Backupset class, if created successfully

            Raises:
                SDKException:
                    if type of the backupset name argument is not string

                    if failed to create a backupset

                    if response is empty

                    if response is not success

                    if backupset with same name already exists
        &#34;&#34;&#34;
        if not (isinstance(backupset_name, str) and isinstance(on_demand_backupset, bool)):
            raise SDKException(&#39;Backupset&#39;, &#39;101&#39;)

        if self.has_backupset(backupset_name):
            raise SDKException(
                &#39;Backupset&#39;, &#39;102&#39;, &#39;Backupset &#34;{0}&#34; already exists.&#39;.format(backupset_name)
            )

        if self._instance_object is None:
            if self._agent_object.instances.has_instance(&#39;DefaultInstanceName&#39;):
                self._instance_object = self._agent_object.instances.get(&#39;DefaultInstanceName&#39;)
            else:
                self._instance_object = self._agent_object.instances.get(
                    sorted(self._agent_object.instances.all_instances)[0]
                )

        request_json = {
            &#34;association&#34;: {
                &#34;entity&#34;: [{
                    &#34;clientName&#34;: self._client_object.client_name,
                    &#34;appName&#34;: self._agent_object.agent_name,
                    &#34;instanceName&#34;: self._instance_object.instance_name,
                    &#34;backupsetName&#34;: backupset_name
                }]
            },
            &#34;backupSetInfo&#34;: {
                &#34;commonBackupSet&#34;: {
                    &#34;onDemandBackupset&#34;: on_demand_backupset
                }
            }
        }

        if kwargs.get(&#39;is_nas_turbo_type&#39;):
            request_json[&#34;backupSetInfo&#34;][&#34;commonBackupSet&#34;][&#34;isNasTurboBackupSet&#34;] = kwargs.get(&#39;is_nas_turbo_type&#39;,
                                                                                                 False)

        agent_settings = {
            &#39;db2&#39;: &#34;&#34;&#34;
request_json[&#39;backupSetInfo&#39;].update({
    &#39;db2BackupSet&#39;: {
        &#39;dB2DefaultIndexSP&#39;: {
            &#39;storagePolicyName&#39;: kwargs.get(&#39;storage_policy&#39;, &#39;&#39;)
        }
    }
})
            &#34;&#34;&#34;
        }

        exec(agent_settings.get(self._agent_object.agent_name, &#39;&#39;))

        if kwargs.get(&#39;plan_name&#39;):
            plan_entity_dict = {
                &#34;planName&#34;: kwargs.get(&#39;plan_name&#39;)
            }
            request_json[&#39;backupSetInfo&#39;][&#39;planEntity&#39;] = plan_entity_dict

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._services[&#39;ADD_BACKUPSET&#39;], request_json
        )

        if flag:
            if response.json():
                if &#39;response&#39; in response.json():
                    response_value = response.json()[&#39;response&#39;][0]
                    error_code = str(response_value[&#39;errorCode&#39;])
                    error_message = None

                    if &#39;errorString&#39; in response_value:
                        error_message = response_value[&#39;errorString&#39;]

                    if error_message:
                        o_str = &#39;Failed to create new backupset\nError: &#34;{0}&#34;&#39;.format(
                            error_message
                        )
                        raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, o_str)
                    else:
                        if error_code == &#39;0&#39;:
                            # initialize the backupsets again
                            # so the backupsets object has all the backupsets
                            self.refresh()

                            return self.get(backupset_name)

                        else:
                            o_str = (&#39;Failed to create new backupset with error code: &#34;{0}&#34;\n&#39;
                                     &#39;Please check the documentation for &#39;
                                     &#39;more details on the error&#39;).format(error_code)

                            raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, o_str)
                else:
                    error_code = response.json()[&#39;errorCode&#39;]
                    error_message = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Failed to create new backupset\nError: &#34;{0}&#34;&#39;.format(
                        error_message
                    )
                    raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupset.Backupsets.add_archiveset"><code class="name flex">
<span>def <span class="ident">add_archiveset</span></span>(<span>self, archiveset_name, is_nas_turbo_backupset=False)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds a new archiveset to the agent. It is just a backupset but is mainly used for archive only items</p>
<h2 id="args">Args</h2>
<p>archiveset_name
(str) &ndash; name of new archiveset to add</p>
<p>is_nas_turbo_backupset
(bool) &ndash; True for NAS based client.
default -
False
Returns:
object - instance of the Backupset class, if created successfully</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the archiveset name argument is not string</p>
<pre><code>if failed to create a archiveset

if response is empty

if response is not success

if archiveset with same name already exists
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupset.py#L557-L675" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_archiveset(self, archiveset_name, is_nas_turbo_backupset=False):
    &#34;&#34;&#34; 
    Adds a new archiveset to the agent. It is just a backupset but is mainly used for archive only items

    Args:
        archiveset_name     (str) -- name of new archiveset to add

        is_nas_turbo_backupset  (bool) -- True for NAS based client.
            default -   False
        
    Returns:
    object - instance of the Backupset class, if created successfully

    Raises:
        SDKException:
            if type of the archiveset name argument is not string

            if failed to create a archiveset

            if response is empty

            if response is not success

            if archiveset with same name already exists
            

    &#34;&#34;&#34;        
    if not (isinstance(archiveset_name, str)):
        raise SDKException(&#39;Backupset&#39;, &#39;101&#39;)
    else:
        archiveset_name = archiveset_name.lower()

    if self.has_backupset(archiveset_name):
        raise SDKException(&#39;archiveset_name&#39;, &#39;102&#39;, &#39;Archiveset &#34;{0}&#34; already exists.&#39;.format(archiveset_name))

    if self._agent_object.agent_id not in [&#39;29&#39;, &#39;33&#39;]:
        raise SDKException(&#39;Backupset&#39;, &#39;101&#39;, &#34;Archiveset is not applicable to this application type.&#34;)



    request_json = {
        &#34;backupSetInfo&#34;: {
            &#34;useContentFromPlan&#34;: False,
            &#34;planEntity&#34;: {},
            &#34;commonBackupSet&#34;: {
                &#34;isNasTurboBackupSet&#34;: is_nas_turbo_backupset,
                &#34;isArchivingEnabled&#34;: True,
                &#34;isDefaultBackupSet&#34;: False
            },
            &#34;backupSetEntity&#34;: {
                &#34;_type_&#34;: 6,
                &#34;clientId&#34;: int(self._client_object.client_id),
                &#34;backupsetName&#34;: archiveset_name,
                &#34;applicationId&#34;: int(self._agent_object.agent_id)
            },
            &#34;subClientList&#34;: [
                {
                    &#34;contentOperationType&#34;: 1,
                    &#34;fsSubClientProp&#34;: {
                        &#34;useGlobalFilters&#34;: 2,
                        &#34;forcedArchiving&#34;: True,
                        &#34;diskCleanupRules&#34;: {
                            &#34;enableArchivingWithRules&#34;: True,
                            &#34;diskCleanupFileTypes&#34;: {}
                        }
                    },
                    &#34;content&#34;: [
                        {
                            &#34;path&#34;: &#34;&#34;
                        }
                    ]
                }
            ]
        }
    }
    
    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._services[&#39;ADD_BACKUPSET&#39;], request_json
    )

    if flag:
        if response.json():
            if &#39;response&#39; in response.json():
                response_value = response.json()[&#39;response&#39;][0]
                error_code = str(response_value[&#39;errorCode&#39;])
                error_message = None

                if &#39;errorString&#39; in response_value:
                    error_message = response_value[&#39;errorString&#39;]

                if error_message:
                    o_str = &#39;Failed to create new Archiveset\nError: &#34;{0}&#34;&#39;.format(
                        error_message
                    )
                    raise SDKException(&#39;Archiveset&#39;, &#39;102&#39;, o_str)
                else:
                    if error_code == &#39;0&#39;:
                        # initialize the backupsets again
                        # so the backupsets object has all the backupsets
                        self.refresh()
                        return self.get(archiveset_name)
                    
                    else:
                        o_str = (&#39;Failed to create new Archiveset with error code: &#34;{0}&#34;\n&#39;
                                 &#39;Please check the documentation for &#39;
                                 &#39;more details on the error&#39;).format(error_code)

                        raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, o_str)
            else:
                error_code = response.json()[&#39;errorCode&#39;]
                error_message = response.json()[&#39;errorMessage&#39;]
                o_str = &#39;Failed to create new Archiveset\nError: &#34;{0}&#34;&#39;.format(
                    error_message
                )
                raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, o_str)
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupset.Backupsets.add_salesforce_backupset"><code class="name flex">
<span>def <span class="ident">add_salesforce_backupset</span></span>(<span>self, salesforce_options, db_options=None, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds a new Salesforce Backupset to the Commcell.</p>
<h2 id="args">Args</h2>
<p>salesforce_options
(dict)
&ndash;
salesforce options
{
"salesforce_user_name": 'salesforce login user',
"salesforce_user_password": 'salesforce user password',
"salesforce_user_token": 'salesforce user token'
}</p>
<p>db_options
(dict)
&ndash;
database options to configure sync db
{
"db_enabled": 'True or False',
"db_type": 'SQLSERVER or POSTGRESQL',
"db_host_name": 'database hostname',
"db_instance": 'database instance name',
"db_name": 'database name',
"db_port": 'port of the database',
"db_user_name": 'database user name',
"db_user_password": 'database user password'
}</p>
<p>**kwargs
(dict)
&ndash;
dict of keyword arguments as follows</p>
<pre><code>                                        download_cache_path     (str)   -- download cache path
                                        mutual_auth_path        (str)   -- mutual auth cert path
                                        storage_policy          (str)   -- storage policy
                                        streams                 (int)   -- number of streams
</code></pre>
<h2 id="returns">Returns</h2>
<p>object
-
instance of the Backupset class for this new backupset</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if backupset with given name already exists</p>
<pre><code>if failed to add the backupset

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupset.py#L788-L893" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_salesforce_backupset(
        self,
        salesforce_options,
        db_options=None, **kwargs):
    &#34;&#34;&#34;Adds a new Salesforce Backupset to the Commcell.

        Args:

            salesforce_options         (dict)       --  salesforce options
                                                        {
                                                            &#34;salesforce_user_name&#34;: &#39;salesforce login user&#39;,
                                                            &#34;salesforce_user_password&#34;: &#39;salesforce user password&#39;,
                                                            &#34;salesforce_user_token&#34;: &#39;salesforce user token&#39;
                                                        }

            db_options                 (dict)       --  database options to configure sync db
                                                        {
                                                            &#34;db_enabled&#34;: &#39;True or False&#39;,
                                                            &#34;db_type&#34;: &#39;SQLSERVER or POSTGRESQL&#39;,
                                                            &#34;db_host_name&#34;: &#39;database hostname&#39;,
                                                            &#34;db_instance&#34;: &#39;database instance name&#39;,
                                                            &#34;db_name&#34;: &#39;database name&#39;,
                                                            &#34;db_port&#34;: &#39;port of the database&#39;,
                                                            &#34;db_user_name&#34;: &#39;database user name&#39;,
                                                            &#34;db_user_password&#34;: &#39;database user password&#39;
                                                        }

            **kwargs                   (dict)       --     dict of keyword arguments as follows

                                                        download_cache_path     (str)   -- download cache path
                                                        mutual_auth_path        (str)   -- mutual auth cert path
                                                        storage_policy          (str)   -- storage policy
                                                        streams                 (int)   -- number of streams

        Returns:
            object  -   instance of the Backupset class for this new backupset

        Raises:
            SDKException:
                if backupset with given name already exists

                if failed to add the backupset

                if response is empty

                if response is not success
    &#34;&#34;&#34;

    if db_options is None:
        db_options = {&#39;db_enabled&#39;: False}
    if self.has_backupset(salesforce_options.get(&#39;salesforce_user_name&#39;)):
        raise SDKException(&#39;Backupset&#39;, &#39;102&#39;,
                           &#39;Backupset &#34;{0}&#34; already exists.&#39;.format(salesforce_options.get(&#39;salesforce_user_name&#39;)))

    salesforce_password = b64encode(salesforce_options.get(&#39;salesforce_user_password&#39;).encode()).decode()
    salesforce_token = b64encode(salesforce_options.get(&#39;salesforce_user_token&#39;, &#39;&#39;).encode()).decode()
    db_user_password = &#34;&#34;
    if db_options.get(&#39;db_enabled&#39;, False):
        db_user_password = b64encode(db_options.get(&#39;db_user_password&#39;).encode()).decode()

    request_json = {
        &#34;backupSetInfo&#34;: {
            &#34;backupSetEntity&#34;: {
                &#34;clientName&#34;: self._client_object.client_name,
                &#34;instanceName&#34;: self._instance_object.instance_name,
                &#34;backupsetName&#34;: salesforce_options.get(&#39;salesforce_user_name&#39;),
                &#34;appName&#34;: self._agent_object.agent_name
            },
            &#34;cloudAppsBackupset&#34;: {
                &#34;instanceType&#34;: 3,
                &#34;salesforceBackupSet&#34;: {
                    &#34;enableREST&#34;: True,
                    &#34;downloadCachePath&#34;: kwargs.get(&#39;download_cache_path&#39;, &#39;/tmp&#39;),
                    &#34;mutualAuthPath&#34;: kwargs.get(&#39;mutual_auth_path&#39;, &#39;&#39;),
                    &#34;token&#34;: salesforce_token,
                    &#34;userPassword&#34;: {
                        &#34;userName&#34;: salesforce_options.get(&#39;salesforce_user_name&#39;),
                        &#34;password&#34;: salesforce_password,
                    },
                    &#34;syncDatabase&#34;: {
                        &#34;dbEnabled&#34;: db_options.get(&#39;db_enabled&#39;, False),
                        &#34;dbPort&#34;: db_options.get(&#39;db_port&#39;, &#39;1433&#39;),
                        &#34;dbInstance&#34;: db_options.get(&#39;db_instance&#39;, &#39;&#39;),
                        &#34;dbName&#34;: db_options.get(&#39;db_name&#39;, self._instance_object.instance_name),
                        &#34;dbType&#34;: db_options.get(&#39;db_type&#39;, &#34;SQLSERVER&#34;),
                        &#34;dbHost&#34;: db_options.get(&#39;db_host_name&#39;, &#39;&#39;),
                        &#34;dbUserPassword&#34;: {
                            &#34;userName&#34;: db_options.get(&#39;db_user_name&#39;, &#39;&#39;),
                            &#34;password&#34;: db_user_password,

                        },
                    },
                },
                &#34;generalCloudProperties&#34;: {
                    &#34;numberOfBackupStreams&#34;: kwargs.get(&#39;streams&#39;, 2),
                    &#34;storageDevice&#34;: {
                        &#34;dataBackupStoragePolicy&#34;: {
                            &#34;storagePolicyName&#34;: kwargs.get(&#39;storage_policy&#39;, &#39;&#39;)
                        },
                    },
                },
            },
        },
    }

    self._process_add_response(salesforce_options.get(&#39;salesforce_user_name&#39;), request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupset.Backupsets.add_v1_sharepoint_client"><code class="name flex">
<span>def <span class="ident">add_v1_sharepoint_client</span></span>(<span>self, backupset_name, server_plan, client_name, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>for sharepoint v1 client creation is a backupset
Adds a new Office 365 V1 Share Point Pseudo Client to the Commcell.</p>
<pre><code>    Args:
        backupset_name                 (str)   --  name of the new Sharepoint Pseudo Client

        server_plan                 (str)   --  server_plan to associate with the client

        client_name                 (str) -- the access node for which Pseudo Client will be created


    Kwargs :

        tenant_url                  (str)   --  url of sharepoint tenant

        azure_username              (str)   --  username of azure app

        azure_secret                (str)   --  secret key of azure app

        user_username        (str)   --  username of Sharepoint admin

        user_password           (str)  -- password of Sharepoint admin

        azure_app_id            (str)       --  azure app id for sharepoint online

        azure_app_key_id        (str)       --  app key for sharepoint online

        azure_directory_id    (str)   --  azure directory id for sharepoint online


    Returns:
        object  -   instance of the Client class for this new client

    Raises:
        SDKException:
            if client with given name already exists

            if index_server is not found

            if server_plan is not found

            if failed to add the client

            if response is empty

            if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupset.py#L677-L786" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add_v1_sharepoint_client(
        self,
        backupset_name,
        server_plan,
        client_name,
        **kwargs):
    &#34;&#34;&#34;
    for sharepoint v1 client creation is a backupset
    Adds a new Office 365 V1 Share Point Pseudo Client to the Commcell.

            Args:
                backupset_name                 (str)   --  name of the new Sharepoint Pseudo Client

                server_plan                 (str)   --  server_plan to associate with the client

                client_name                 (str) -- the access node for which Pseudo Client will be created


            Kwargs :

                tenant_url                  (str)   --  url of sharepoint tenant

                azure_username              (str)   --  username of azure app

                azure_secret                (str)   --  secret key of azure app

                user_username        (str)   --  username of Sharepoint admin

                user_password           (str)  -- password of Sharepoint admin

                azure_app_id            (str)       --  azure app id for sharepoint online

                azure_app_key_id        (str)       --  app key for sharepoint online

                azure_directory_id    (str)   --  azure directory id for sharepoint online


            Returns:
                object  -   instance of the Client class for this new client

            Raises:
                SDKException:
                    if client with given name already exists

                    if index_server is not found

                    if server_plan is not found

                    if failed to add the client

                    if response is empty

                    if response is not success

    &#34;&#34;&#34;
    if self.has_backupset(backupset_name):
        raise SDKException(
            &#39;Backupset&#39;, &#39;102&#39;, &#39;Backupset &#34;{0}&#34; already exists.&#39;.format(backupset_name))
    if self._commcell_object.plans.has_plan(server_plan):
        server_plan_object = self._commcell_object.plans.get(server_plan)
        server_plan_dict = {
            &#34;planId&#34;: int(server_plan_object.plan_id)
        }
    else:
        raise SDKException(&#39;Backupset&#39;, &#39;102&#39;)
    backup_set = {
        &#34;_type_&#34;: 6,
        &#34;applicationId&#34;: 78,
        &#34;backupsetName&#34;: backupset_name,
        &#34;clientId&#34;: int(self._client_object.client_id)
    }
    request_json = {
        &#34;backupSetInfo&#34;: {
            &#34;planEntity&#34;: server_plan_dict,
            &#34;backupSetEntity&#34;: backup_set,
            &#34;sharepointBackupSet&#34;: {
                &#34;sharepointBackupSetType&#34;: 4
            }
        }
    }
    tenant_url = kwargs.get(&#39;tenant_url&#39;)
    user_username = kwargs.get(&#39;user_username&#39;)
    is_modern_auth_enabled = kwargs.get(&#39;is_modern_auth_enabled&#39;,False)
    azure_secret = b64encode(kwargs.get(&#39;azure_secret&#39;).encode()).decode()
    azure_app_key_id = b64encode(kwargs.get(&#39;azure_app_key_id&#39;).encode()).decode()
    user_password = b64encode(kwargs.get(&#39;user_password&#39;).encode()).decode()
    request_json[&#34;backupSetInfo&#34;][&#34;sharepointBackupSet&#34;][
        &#34;spOffice365BackupSetProp&#34;] = {
        &#34;azureUserAccount&#34;: kwargs.get(&#39;azure_username&#39;),
        &#34;azureAccountKey&#34;: azure_secret,
        &#34;tenantUrlItem&#34;: tenant_url,
        &#34;isModernAuthEnabled&#34;: is_modern_auth_enabled,
        &#34;office365Credentials&#34;: {
            &#34;userName&#34;: user_username,
            &#34;password&#34;: user_password
        },
    }
    if is_modern_auth_enabled:
        request_json[&#34;backupSetInfo&#34;][&#34;sharepointBackupSet&#34;][
            &#34;spOffice365BackupSetProp&#34;][&#34;azureAppList&#34;] = {
            &#34;azureApps&#34;: [
                {
                    &#34;azureAppId&#34;: kwargs.get(&#39;azure_app_id&#39;),
                    &#34;azureAppKeyValue&#34;: azure_app_key_id,
                    &#34;azureDirectoryId&#34;: kwargs.get(&#39;azure_directory_id&#39;)
                }
            ]
        }

    self._process_add_response(backupset_name, request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupset.Backupsets.delete"><code class="name flex">
<span>def <span class="ident">delete</span></span>(<span>self, backupset_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes the backup set from the agent.</p>
<h2 id="args">Args</h2>
<p>backupset_name (str)
&ndash;
name of the backupset</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the backupset name argument is not string</p>
<pre><code>if failed to delete the backupset

if response is empty

if response is not success

if no backupset exists with the given name
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupset.py#L930-L995" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete(self, backupset_name):
    &#34;&#34;&#34;Deletes the backup set from the agent.

        Args:
            backupset_name (str)  --  name of the backupset

        Raises:
            SDKException:
                if type of the backupset name argument is not string

                if failed to delete the backupset

                if response is empty

                if response is not success

                if no backupset exists with the given name
    &#34;&#34;&#34;
    if not isinstance(backupset_name, str):
        raise SDKException(&#39;Backupset&#39;, &#39;101&#39;)
    else:
        backupset_name = backupset_name.lower()

    if self.has_backupset(backupset_name):
        delete_backupset_service = self._services[&#39;BACKUPSET&#39;] % (
            self._backupsets[backupset_name][&#39;id&#39;]
        )

        flag, response = self._cvpysdk_object.make_request(&#39;DELETE&#39;, delete_backupset_service)

        if flag:
            if response.json():
                if &#39;response&#39; in response.json():
                    response_value = response.json()[&#39;response&#39;][0]
                    error_code = str(response_value[&#39;errorCode&#39;])
                    error_message = None

                    if &#39;errorString&#39; in response_value:
                        error_message = response_value[&#39;errorString&#39;]

                    if error_message:
                        o_str = &#39;Failed to delete backupset\nError: &#34;{0}&#34;&#39;
                        raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, o_str.format(error_message))
                    else:
                        if error_code == &#39;0&#39;:
                            # initialize the backupsets again
                            # so the backupsets object has all the backupsets
                            self.refresh()
                        else:
                            o_str = (&#39;Failed to delete backupset with error code: &#34;{0}&#34;\n&#39;
                                     &#39;Please check the documentation for &#39;
                                     &#39;more details on the error&#39;).format(error_code)
                            raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, o_str)
                else:
                    error_code = response.json()[&#39;errorCode&#39;]
                    error_message = response.json()[&#39;errorMessage&#39;]
                    o_str = &#39;Failed to delete backupset\nError: &#34;{0}&#34;&#39;.format(error_message)
                    raise SDKException(&#39;Backupset&#39;, &#39;102&#39;, o_str)
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._update_response_(response.text))
    else:
        raise SDKException(
            &#39;Backupset&#39;, &#39;102&#39;, &#39;No backupset exists with name: &#34;{0}&#34;&#39;.format(backupset_name)
        )</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupset.Backupsets.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, backupset_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a backupset object of the specified backupset name.</p>
<h2 id="args">Args</h2>
<p>backupset_name (str)
&ndash;
name of the backupset</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Backupset class for the given backupset name</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the backupset name argument is not string</p>
<pre><code>if no backupset exists with the given name
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupset.py#L895-L928" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, backupset_name):
    &#34;&#34;&#34;Returns a backupset object of the specified backupset name.

        Args:
            backupset_name (str)  --  name of the backupset

        Returns:
            object - instance of the Backupset class for the given backupset name

        Raises:
            SDKException:
                if type of the backupset name argument is not string

                if no backupset exists with the given name
    &#34;&#34;&#34;
    if not isinstance(backupset_name, str):
        raise SDKException(&#39;Backupset&#39;, &#39;101&#39;)
    else:
        backupset_name = backupset_name.lower()

        if self.has_backupset(backupset_name):
            if self._instance_object is None:
                self._instance_object = self._agent_object.instances.get(
                    self._backupsets[backupset_name][&#39;instance&#39;]
                )
            return Backupset(
                self._instance_object,
                backupset_name,
                self._backupsets[backupset_name][&#34;id&#34;]
            )

        raise SDKException(
            &#39;Backupset&#39;, &#39;102&#39;, &#39;No backupset exists with name: &#34;{0}&#34;&#39;.format(backupset_name)
        )</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupset.Backupsets.has_backupset"><code class="name flex">
<span>def <span class="ident">has_backupset</span></span>(<span>self, backupset_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks if a backupset exists for the agent with the input backupset name.</p>
<h2 id="args">Args</h2>
<p>backupset_name (str)
&ndash;
name of the backupset</p>
<h2 id="returns">Returns</h2>
<p>bool - boolean output whether the backupset exists for the agent or not</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the backupset name argument is not string</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupset.py#L353-L369" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_backupset(self, backupset_name):
    &#34;&#34;&#34;Checks if a backupset exists for the agent with the input backupset name.

        Args:
            backupset_name (str)  --  name of the backupset

        Returns:
            bool - boolean output whether the backupset exists for the agent or not

        Raises:
            SDKException:
                if type of the backupset name argument is not string
    &#34;&#34;&#34;
    if not isinstance(backupset_name, str):
        raise SDKException(&#39;Backupset&#39;, &#39;101&#39;)

    return self._backupsets and backupset_name.lower() in self._backupsets</code></pre>
</details>
</dd>
<dt id="cvpysdk.backupset.Backupsets.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the backupsets associated with the Agent / Instance.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/backupset.py#L997-L999" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the backupsets associated with the Agent / Instance.&#34;&#34;&#34;
    self._backupsets = self._get_backupsets()</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul>
<li><a href="#backupsets">Backupsets:</a></li>
<li><a href="#backupset">Backupset:</a><ul>
<li><a href="#backupset-instance-attributes">Backupset instance Attributes</a></li>
</ul>
</li>
</ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.backupset.Backupset" href="#cvpysdk.backupset.Backupset">Backupset</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.backupset.Backupset.backed_up_files_count" href="#cvpysdk.backupset.Backupset.backed_up_files_count">backed_up_files_count</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.backup" href="#cvpysdk.backupset.Backupset.backup">backup</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.backupset_id" href="#cvpysdk.backupset.Backupset.backupset_id">backupset_id</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.backupset_name" href="#cvpysdk.backupset.Backupset.backupset_name">backupset_name</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.browse" href="#cvpysdk.backupset.Backupset.browse">browse</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.delete_data" href="#cvpysdk.backupset.Backupset.delete_data">delete_data</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.description" href="#cvpysdk.backupset.Backupset.description">description</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.find" href="#cvpysdk.backupset.Backupset.find">find</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.guid" href="#cvpysdk.backupset.Backupset.guid">guid</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.is_default_backupset" href="#cvpysdk.backupset.Backupset.is_default_backupset">is_default_backupset</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.is_on_demand_backupset" href="#cvpysdk.backupset.Backupset.is_on_demand_backupset">is_on_demand_backupset</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.list_media" href="#cvpysdk.backupset.Backupset.list_media">list_media</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.name" href="#cvpysdk.backupset.Backupset.name">name</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.plan" href="#cvpysdk.backupset.Backupset.plan">plan</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.properties" href="#cvpysdk.backupset.Backupset.properties">properties</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.refresh" href="#cvpysdk.backupset.Backupset.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.set_default_backupset" href="#cvpysdk.backupset.Backupset.set_default_backupset">set_default_backupset</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupset.update_properties" href="#cvpysdk.backupset.Backupset.update_properties">update_properties</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.backupset.Backupsets" href="#cvpysdk.backupset.Backupsets">Backupsets</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.backupset.Backupsets.add" href="#cvpysdk.backupset.Backupsets.add">add</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupsets.add_archiveset" href="#cvpysdk.backupset.Backupsets.add_archiveset">add_archiveset</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupsets.add_salesforce_backupset" href="#cvpysdk.backupset.Backupsets.add_salesforce_backupset">add_salesforce_backupset</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupsets.add_v1_sharepoint_client" href="#cvpysdk.backupset.Backupsets.add_v1_sharepoint_client">add_v1_sharepoint_client</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupsets.all_backupsets" href="#cvpysdk.backupset.Backupsets.all_backupsets">all_backupsets</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupsets.default_backup_set" href="#cvpysdk.backupset.Backupsets.default_backup_set">default_backup_set</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupsets.delete" href="#cvpysdk.backupset.Backupsets.delete">delete</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupsets.get" href="#cvpysdk.backupset.Backupsets.get">get</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupsets.has_backupset" href="#cvpysdk.backupset.Backupsets.has_backupset">has_backupset</a></code></li>
<li><code><a title="cvpysdk.backupset.Backupsets.refresh" href="#cvpysdk.backupset.Backupsets.refresh">refresh</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>