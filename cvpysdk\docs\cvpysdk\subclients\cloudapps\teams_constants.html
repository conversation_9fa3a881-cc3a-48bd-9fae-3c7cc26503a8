<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.subclients.cloudapps.teams_constants API documentation</title>
<meta name="description" content="Helper file to maintain all the constants for MS Teams subclient …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.subclients.cloudapps.teams_constants</code></h1>
</header>
<section id="section-intro">
<p>Helper file to maintain all the constants for MS Teams subclient.</p>
<p>TeamsConstants
-
Maintains constants for MS Teams subclient.</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/teams_constants.py#L1-L340" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;Helper file to maintain all the constants for MS Teams subclient.

TeamsConstants  -   Maintains constants for MS Teams subclient.

&#34;&#34;&#34;


class TeamsConstants:
    &#34;&#34;&#34;Class to maintain all the Teams subclient related constants.&#34;&#34;&#34;

    ADD_DISCOVER_TYPE = 12
    INDEX_APP_TYPE = 200128
    ADD_SUBCLIENT_ENTITY_JSON = {
        &#34;instanceId&#34;: None,
        &#34;subclientId&#34;: None,
        &#34;clientId&#34;: None,
        &#34;applicationId&#34;: None
    }

    ADD_USER_JSON = {
        &#34;_type_&#34;: None,
        &#34;userGUID&#34;: None
    }

    ADD_TEAM_JSON = {
        &#34;displayName&#34;: None,
        &#34;smtpAddress&#34;: None,
        &#34;associated&#34;: False,
        &#34;msTeamsInfo&#34;: {
            &#34;visibility&#34;: 1,
            &#34;teamsCreatedTime&#34;: None
        },
        &#34;user&#34;: None
    }

    ADD_GROUP_JSON = {
        &#34;associated&#34;: False,
        &#34;name&#34;: None,
        &#34;id&#34;: None
    }

    ADD_REQUEST_JSON = {
        &#34;LaunchAutoDiscovery&#34;: False,
        &#34;cloudAppAssociation&#34;: {
            &#34;accountStatus&#34;: 0,
            &#34;subclientEntity&#34;: None,
            &#34;cloudAppDiscoverinfo&#34;: {
                &#34;discoverByType&#34;: ADD_DISCOVER_TYPE,
                &#34;userAccounts&#34;: []
            },
            &#34;plan&#34;: {
                &#34;planId&#34;: None
            }
        }
    }

    BACKUP_TEAM_JSON = {
        &#34;aliasName&#34;: &#34;&#34;,
        &#34;displayName&#34;: None,
        &#34;smtpAddress&#34;: None,
        &#34;BackupSetId&#34;: 0,
        &#34;isAutoDiscoveredUser&#34;: False,
        &#34;accountSize&#34;: 0,
        &#34;ParentWebGuid&#34;: &#34;&#34;,
        &#34;commonFlags&#34;: 0,
        &#34;msTeamsInfo&#34;: {
            &#34;visibility&#34;: 1,
            &#34;teamsCreatedTime&#34;: None
        },
        &#34;lastBackupJobRanTime&#34;: {},
        &#34;IdxCollectionTime&#34;: {},
        &#34;user&#34;: None
    }

    BACKUP_USER_JSON = {
        &#34;user&#34;: None
    }

    ASSOCIATIONS = {
        &#34;subclientId&#34;: None,
        &#34;applicationId&#34;: None,
        &#34;clientName&#34;: None,
        &#34;displayName&#34;: None,
        &#34;backupsetId&#34;: None,
        &#34;instanceId&#34;: None,
        &#34;subclientGUID&#34;: None,
        &#34;clientId&#34;: None,
        &#34;clientGUID&#34;: None,
        &#34;subclientName&#34;: None,
        &#34;backupsetName&#34;: None,
        &#34;instanceName&#34;: None,
        &#34;_type_&#34;: None,
    }

    BACKUP_SUBTASK_JSON = {
        &#34;subTask&#34;: {
            &#34;subTaskType&#34;: 2,
            &#34;operationType&#34;: 2
        },
        &#34;options&#34;: {
            &#34;backupOpts&#34;: {
                &#34;backupLevel&#34;: 2,
                &#34;cloudAppOptions&#34;: {&#34;forceFullBackup&#34;: False, &#34;userAccounts&#34;: []}
            },
            &#34;commonOpts&#34;: {
                &#34;notifyUserOnJobCompletion&#34;: False,
                &#34;jobMetadata&#34;: [
                    {
                        &#34;jobOptionItems&#34;: [
                            {
                                &#34;value&#34;: &#34;Disabled&#34;,
                                &#34;option&#34;: &#34;Convert job to full&#34;
                            },
                            {
                                &#34;value&#34;: &#34;Disabled&#34;,
                                &#34;option&#34;: &#34;Total running time&#34;
                            }
                        ],
                        &#34;selectedItems&#34;: []
                    }
                ]
            }
        }
    }

    BACKUP_REQUEST_JSON = {
        &#34;processinginstructioninfo&#34;: {
            &#34;formatFlags&#34;: {&#34;skipIdToNameConversion&#34;: True}
        },
        &#34;taskInfo&#34;: {
            &#34;associations&#34;: [],
            &#34;task&#34;: {&#34;taskType&#34;: 1},
            &#34;subTasks&#34;: []
        }
    }

    RESTORE_TASK_JSON = {
        &#34;initiatedFrom&#34;: 2,
        &#34;taskType&#34;: 1,
        &#34;policyType&#34;: 0
    }

    RESTORE_SUBTASK_JSON = {
        &#34;subTaskType&#34;: 3,
        &#34;operationType&#34;: 1001
    }

    CUSTOM_CATEGORY_JSON = {
        &#34;subclientEntity&#34;: {
            &#34;subclientId&#34;: None
        },
        &#34;planEntity&#34;: {
            &#34;planId&#34;: None,
            &#34;planName&#34;: &#34;&#34;
        },
        &#34;status&#34;: 0,
        &#34;categoryName&#34;: None,
        &#34;categoryQuery&#34;: {
            &#34;conditions&#34;: []
        },
        &#34;office365V2AutoDiscover&#34;: {
            &#34;launchAutoDiscover&#34;: True,
            &#34;appType&#34;: 134,
            &#34;clientId&#34;: None,
            &#34;instanceId&#34;: None,
            &#34;instanceType&#34;: 36
        }
    }

    ClOUD_APP_EDISCOVER_TYPE = {
        &#34;Teams&#34;: 8,
        &#34;Users&#34;: 7,
        &#34;Groups&#34;: 22
    }

    DESTINATION_ONEDRIVE_INFO = {
        &#34;userSMTP&#34;: None,
        &#34;userGUID&#34;: None,
         &#34;folder&#34;: &#34;/&#34;
    }

    USER_ONEDRIVE_RESTORE_JSON = {
      &#34;taskInfo&#34;: {
        &#34;associations&#34;: None,
        &#34;task&#34;: {
            &#34;taskType&#34;: 1,
            &#34;initiatedFrom&#34;: 1
        },
        &#34;subTasks&#34;: [
          {
            &#34;subTask&#34;: {
              &#34;subTaskType&#34;: 3,
              &#34;operationType&#34;: 1001
            },
            &#34;options&#34;: {
              &#34;commonOpts&#34;: {
                &#34;notifyUserOnJobCompletion&#34;: False,
                &#34;jobMetadata&#34;: [
                  {
                    &#34;selectedItems&#34;: [],
                    &#34;jobOptionItems&#34;: []
                  }
                ]
              },
              &#34;restoreOptions&#34;: {
                &#34;browseOption&#34;: {
                  &#34;commCellId&#34;: 2
                },
                &#34;destination&#34;: {
                  &#34;inPlace&#34;: False,
                  &#34;destClient&#34;: {},
                  &#34;destPath&#34;: None,
                  &#34;destAppId&#34;: 134
                },
                &#34;commonOptions&#34;: {
                  &#34;unconditionalOverwrite&#34;: False,
                  &#34;overwriteFiles&#34;: False,
                  &#34;skip&#34;: True
                },
                &#34;cloudAppsRestoreOptions&#34;: {
                  &#34;instanceType&#34;: 36,
                  &#34;msTeamsRestoreOptions&#34;: {
                    &#34;restoreToTeams&#34;: False,
                    &#34;restoreToOneDrive&#34;: True,
                    &#34;destLocation&#34;: None,
                    &#34;overWriteItems&#34;: False,
                    &#34;restoreUsingFindQuery&#34;: False,
                    &#34;findQuery&#34;: {
                      &#34;mode&#34;: 4,
                      &#34;advSearchGrp&#34;: {
                        &#34;commonFilter&#34;: [
                          {
                            &#34;filter&#34;: {
                              &#34;interFilterOP&#34;: 2,
                              &#34;filters&#34;: [
                                {
                                  &#34;field&#34;: &#34;CISTATE&#34;,
                                  &#34;intraFieldOp&#34;: 0,
                                  &#34;fieldValues&#34;: {
                                    &#34;values&#34;: [
                                      &#34;1&#34;
                                    ]
                                  }
                                }
                              ]
                            }
                          }
                        ],
                        &#34;fileFilter&#34;: [
                          {
                            &#34;interGroupOP&#34;: 2,
                            &#34;filter&#34;: {
                              &#34;interFilterOP&#34;: 2,
                              &#34;filters&#34;: [
                                {
                                  &#34;field&#34;: &#34;TEAMS_ITEM_TYPE&#34;,
                                  &#34;intraFieldOp&#34;: 0,
                                  &#34;fieldValues&#34;: {
                                    &#34;values&#34;: [
                                      &#34;55&#34;
                                    ]
                                  }
                                },
                                {
                                  &#34;field&#34;: &#34;TEAMS_USER_ID&#34;,
                                  &#34;intraFieldOp&#34;: 0,
                                  &#34;fieldValues&#34;: {
                                    &#34;values&#34;: []
                                  }
                                }
                              ]
                            }
                          }
                        ],
                        &#34;emailFilter&#34;: [],
                        &#34;galaxyFilter&#34;: [
                          {
                            &#34;appIdList&#34;: None
                          }
                        ]
                      },
                      &#34;searchProcessingInfo&#34;: {
                        &#34;resultOffset&#34;: 0,
                        &#34;pageSize&#34;: 15,
                        &#34;queryParams&#34;: [
                          {
                            &#34;param&#34;: &#34;ENABLE_MIXEDVIEW&#34;,
                            &#34;value&#34;: &#34;true&#34;
                          },
                          {
                            &#34;param&#34;: &#34;RESPONSE_FIELD_LIST&#34;,
                            &#34;value&#34;: &#34;DATA_TYPE,CONTENTID,CV_OBJECT_GUID&#34;
                                     &#34;,PARENT_GUID,CV_TURBO_GUID,AFILEID&#34;
                                     &#34;,AFILEOFFSET,COMMCELLNO,MODIFIEDTIME&#34;
                                     &#34;,SIZEINKB,BACKUPTIME,CISTATE,DATE_DELETED&#34;
                                     &#34;,TEAMS_ITEM_ID,TEAMS_ITEM_NAME,TEAMS_NAME&#34;
                                     &#34;,TEAMS_SMTP,TEAMS_ITEM_TYPE,TEAMS_CHANNEL_TYPE&#34;
                                     &#34;,TEAMS_TAB_TYPE,TEAMS_GROUP_VISIBILITY&#34;
                                     &#34;,TEAMS_GUID,TEAMS_CONV_ITEM_TYPE,TEAMS_CONV_MESSAGE_TYPE&#34;
                                     &#34;,TEAMS_CONV_SUBJECT,TEAMS_CONV_IMPORTANCE,TEAMS_CONV_SENDER_TYPE&#34;
                                     &#34;,TEAMS_CONV_SENDER_NAME,TEAMS_CONV_HAS_REPLIES,CI_URL,TEAMS_DRIVE_FOLDER_TYPE&#34;
                          }
                        ],
                        &#34;sortParams&#34;: [
                          {
                            &#34;sortDirection&#34;: 0,
                            &#34;sortField&#34;: &#34;TEAMS_NAME&#34;
                          }
                        ]
                      }
                    },
                    &#34;selectedItemsToRestore&#34;: None,
                    &#34;destinationOneDriveInfo&#34;: None,
                    &#34;restorePostsAsHtml&#34;: True
                  }
                }
              }
            }
          }
        ]
      }
    }</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants"><code class="flex name class">
<span>class <span class="ident">TeamsConstants</span></span>
</code></dt>
<dd>
<div class="desc"><p>Class to maintain all the Teams subclient related constants.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/subclients/cloudapps/teams_constants.py#L26-L340" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class TeamsConstants:
    &#34;&#34;&#34;Class to maintain all the Teams subclient related constants.&#34;&#34;&#34;

    ADD_DISCOVER_TYPE = 12
    INDEX_APP_TYPE = 200128
    ADD_SUBCLIENT_ENTITY_JSON = {
        &#34;instanceId&#34;: None,
        &#34;subclientId&#34;: None,
        &#34;clientId&#34;: None,
        &#34;applicationId&#34;: None
    }

    ADD_USER_JSON = {
        &#34;_type_&#34;: None,
        &#34;userGUID&#34;: None
    }

    ADD_TEAM_JSON = {
        &#34;displayName&#34;: None,
        &#34;smtpAddress&#34;: None,
        &#34;associated&#34;: False,
        &#34;msTeamsInfo&#34;: {
            &#34;visibility&#34;: 1,
            &#34;teamsCreatedTime&#34;: None
        },
        &#34;user&#34;: None
    }

    ADD_GROUP_JSON = {
        &#34;associated&#34;: False,
        &#34;name&#34;: None,
        &#34;id&#34;: None
    }

    ADD_REQUEST_JSON = {
        &#34;LaunchAutoDiscovery&#34;: False,
        &#34;cloudAppAssociation&#34;: {
            &#34;accountStatus&#34;: 0,
            &#34;subclientEntity&#34;: None,
            &#34;cloudAppDiscoverinfo&#34;: {
                &#34;discoverByType&#34;: ADD_DISCOVER_TYPE,
                &#34;userAccounts&#34;: []
            },
            &#34;plan&#34;: {
                &#34;planId&#34;: None
            }
        }
    }

    BACKUP_TEAM_JSON = {
        &#34;aliasName&#34;: &#34;&#34;,
        &#34;displayName&#34;: None,
        &#34;smtpAddress&#34;: None,
        &#34;BackupSetId&#34;: 0,
        &#34;isAutoDiscoveredUser&#34;: False,
        &#34;accountSize&#34;: 0,
        &#34;ParentWebGuid&#34;: &#34;&#34;,
        &#34;commonFlags&#34;: 0,
        &#34;msTeamsInfo&#34;: {
            &#34;visibility&#34;: 1,
            &#34;teamsCreatedTime&#34;: None
        },
        &#34;lastBackupJobRanTime&#34;: {},
        &#34;IdxCollectionTime&#34;: {},
        &#34;user&#34;: None
    }

    BACKUP_USER_JSON = {
        &#34;user&#34;: None
    }

    ASSOCIATIONS = {
        &#34;subclientId&#34;: None,
        &#34;applicationId&#34;: None,
        &#34;clientName&#34;: None,
        &#34;displayName&#34;: None,
        &#34;backupsetId&#34;: None,
        &#34;instanceId&#34;: None,
        &#34;subclientGUID&#34;: None,
        &#34;clientId&#34;: None,
        &#34;clientGUID&#34;: None,
        &#34;subclientName&#34;: None,
        &#34;backupsetName&#34;: None,
        &#34;instanceName&#34;: None,
        &#34;_type_&#34;: None,
    }

    BACKUP_SUBTASK_JSON = {
        &#34;subTask&#34;: {
            &#34;subTaskType&#34;: 2,
            &#34;operationType&#34;: 2
        },
        &#34;options&#34;: {
            &#34;backupOpts&#34;: {
                &#34;backupLevel&#34;: 2,
                &#34;cloudAppOptions&#34;: {&#34;forceFullBackup&#34;: False, &#34;userAccounts&#34;: []}
            },
            &#34;commonOpts&#34;: {
                &#34;notifyUserOnJobCompletion&#34;: False,
                &#34;jobMetadata&#34;: [
                    {
                        &#34;jobOptionItems&#34;: [
                            {
                                &#34;value&#34;: &#34;Disabled&#34;,
                                &#34;option&#34;: &#34;Convert job to full&#34;
                            },
                            {
                                &#34;value&#34;: &#34;Disabled&#34;,
                                &#34;option&#34;: &#34;Total running time&#34;
                            }
                        ],
                        &#34;selectedItems&#34;: []
                    }
                ]
            }
        }
    }

    BACKUP_REQUEST_JSON = {
        &#34;processinginstructioninfo&#34;: {
            &#34;formatFlags&#34;: {&#34;skipIdToNameConversion&#34;: True}
        },
        &#34;taskInfo&#34;: {
            &#34;associations&#34;: [],
            &#34;task&#34;: {&#34;taskType&#34;: 1},
            &#34;subTasks&#34;: []
        }
    }

    RESTORE_TASK_JSON = {
        &#34;initiatedFrom&#34;: 2,
        &#34;taskType&#34;: 1,
        &#34;policyType&#34;: 0
    }

    RESTORE_SUBTASK_JSON = {
        &#34;subTaskType&#34;: 3,
        &#34;operationType&#34;: 1001
    }

    CUSTOM_CATEGORY_JSON = {
        &#34;subclientEntity&#34;: {
            &#34;subclientId&#34;: None
        },
        &#34;planEntity&#34;: {
            &#34;planId&#34;: None,
            &#34;planName&#34;: &#34;&#34;
        },
        &#34;status&#34;: 0,
        &#34;categoryName&#34;: None,
        &#34;categoryQuery&#34;: {
            &#34;conditions&#34;: []
        },
        &#34;office365V2AutoDiscover&#34;: {
            &#34;launchAutoDiscover&#34;: True,
            &#34;appType&#34;: 134,
            &#34;clientId&#34;: None,
            &#34;instanceId&#34;: None,
            &#34;instanceType&#34;: 36
        }
    }

    ClOUD_APP_EDISCOVER_TYPE = {
        &#34;Teams&#34;: 8,
        &#34;Users&#34;: 7,
        &#34;Groups&#34;: 22
    }

    DESTINATION_ONEDRIVE_INFO = {
        &#34;userSMTP&#34;: None,
        &#34;userGUID&#34;: None,
         &#34;folder&#34;: &#34;/&#34;
    }

    USER_ONEDRIVE_RESTORE_JSON = {
      &#34;taskInfo&#34;: {
        &#34;associations&#34;: None,
        &#34;task&#34;: {
            &#34;taskType&#34;: 1,
            &#34;initiatedFrom&#34;: 1
        },
        &#34;subTasks&#34;: [
          {
            &#34;subTask&#34;: {
              &#34;subTaskType&#34;: 3,
              &#34;operationType&#34;: 1001
            },
            &#34;options&#34;: {
              &#34;commonOpts&#34;: {
                &#34;notifyUserOnJobCompletion&#34;: False,
                &#34;jobMetadata&#34;: [
                  {
                    &#34;selectedItems&#34;: [],
                    &#34;jobOptionItems&#34;: []
                  }
                ]
              },
              &#34;restoreOptions&#34;: {
                &#34;browseOption&#34;: {
                  &#34;commCellId&#34;: 2
                },
                &#34;destination&#34;: {
                  &#34;inPlace&#34;: False,
                  &#34;destClient&#34;: {},
                  &#34;destPath&#34;: None,
                  &#34;destAppId&#34;: 134
                },
                &#34;commonOptions&#34;: {
                  &#34;unconditionalOverwrite&#34;: False,
                  &#34;overwriteFiles&#34;: False,
                  &#34;skip&#34;: True
                },
                &#34;cloudAppsRestoreOptions&#34;: {
                  &#34;instanceType&#34;: 36,
                  &#34;msTeamsRestoreOptions&#34;: {
                    &#34;restoreToTeams&#34;: False,
                    &#34;restoreToOneDrive&#34;: True,
                    &#34;destLocation&#34;: None,
                    &#34;overWriteItems&#34;: False,
                    &#34;restoreUsingFindQuery&#34;: False,
                    &#34;findQuery&#34;: {
                      &#34;mode&#34;: 4,
                      &#34;advSearchGrp&#34;: {
                        &#34;commonFilter&#34;: [
                          {
                            &#34;filter&#34;: {
                              &#34;interFilterOP&#34;: 2,
                              &#34;filters&#34;: [
                                {
                                  &#34;field&#34;: &#34;CISTATE&#34;,
                                  &#34;intraFieldOp&#34;: 0,
                                  &#34;fieldValues&#34;: {
                                    &#34;values&#34;: [
                                      &#34;1&#34;
                                    ]
                                  }
                                }
                              ]
                            }
                          }
                        ],
                        &#34;fileFilter&#34;: [
                          {
                            &#34;interGroupOP&#34;: 2,
                            &#34;filter&#34;: {
                              &#34;interFilterOP&#34;: 2,
                              &#34;filters&#34;: [
                                {
                                  &#34;field&#34;: &#34;TEAMS_ITEM_TYPE&#34;,
                                  &#34;intraFieldOp&#34;: 0,
                                  &#34;fieldValues&#34;: {
                                    &#34;values&#34;: [
                                      &#34;55&#34;
                                    ]
                                  }
                                },
                                {
                                  &#34;field&#34;: &#34;TEAMS_USER_ID&#34;,
                                  &#34;intraFieldOp&#34;: 0,
                                  &#34;fieldValues&#34;: {
                                    &#34;values&#34;: []
                                  }
                                }
                              ]
                            }
                          }
                        ],
                        &#34;emailFilter&#34;: [],
                        &#34;galaxyFilter&#34;: [
                          {
                            &#34;appIdList&#34;: None
                          }
                        ]
                      },
                      &#34;searchProcessingInfo&#34;: {
                        &#34;resultOffset&#34;: 0,
                        &#34;pageSize&#34;: 15,
                        &#34;queryParams&#34;: [
                          {
                            &#34;param&#34;: &#34;ENABLE_MIXEDVIEW&#34;,
                            &#34;value&#34;: &#34;true&#34;
                          },
                          {
                            &#34;param&#34;: &#34;RESPONSE_FIELD_LIST&#34;,
                            &#34;value&#34;: &#34;DATA_TYPE,CONTENTID,CV_OBJECT_GUID&#34;
                                     &#34;,PARENT_GUID,CV_TURBO_GUID,AFILEID&#34;
                                     &#34;,AFILEOFFSET,COMMCELLNO,MODIFIEDTIME&#34;
                                     &#34;,SIZEINKB,BACKUPTIME,CISTATE,DATE_DELETED&#34;
                                     &#34;,TEAMS_ITEM_ID,TEAMS_ITEM_NAME,TEAMS_NAME&#34;
                                     &#34;,TEAMS_SMTP,TEAMS_ITEM_TYPE,TEAMS_CHANNEL_TYPE&#34;
                                     &#34;,TEAMS_TAB_TYPE,TEAMS_GROUP_VISIBILITY&#34;
                                     &#34;,TEAMS_GUID,TEAMS_CONV_ITEM_TYPE,TEAMS_CONV_MESSAGE_TYPE&#34;
                                     &#34;,TEAMS_CONV_SUBJECT,TEAMS_CONV_IMPORTANCE,TEAMS_CONV_SENDER_TYPE&#34;
                                     &#34;,TEAMS_CONV_SENDER_NAME,TEAMS_CONV_HAS_REPLIES,CI_URL,TEAMS_DRIVE_FOLDER_TYPE&#34;
                          }
                        ],
                        &#34;sortParams&#34;: [
                          {
                            &#34;sortDirection&#34;: 0,
                            &#34;sortField&#34;: &#34;TEAMS_NAME&#34;
                          }
                        ]
                      }
                    },
                    &#34;selectedItemsToRestore&#34;: None,
                    &#34;destinationOneDriveInfo&#34;: None,
                    &#34;restorePostsAsHtml&#34;: True
                  }
                }
              }
            }
          }
        ]
      }
    }</code></pre>
</details>
<h3>Class variables</h3>
<dl>
<dt id="cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.ADD_DISCOVER_TYPE"><code class="name">var <span class="ident">ADD_DISCOVER_TYPE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.ADD_GROUP_JSON"><code class="name">var <span class="ident">ADD_GROUP_JSON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.ADD_REQUEST_JSON"><code class="name">var <span class="ident">ADD_REQUEST_JSON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.ADD_SUBCLIENT_ENTITY_JSON"><code class="name">var <span class="ident">ADD_SUBCLIENT_ENTITY_JSON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.ADD_TEAM_JSON"><code class="name">var <span class="ident">ADD_TEAM_JSON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.ADD_USER_JSON"><code class="name">var <span class="ident">ADD_USER_JSON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.ASSOCIATIONS"><code class="name">var <span class="ident">ASSOCIATIONS</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.BACKUP_REQUEST_JSON"><code class="name">var <span class="ident">BACKUP_REQUEST_JSON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.BACKUP_SUBTASK_JSON"><code class="name">var <span class="ident">BACKUP_SUBTASK_JSON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.BACKUP_TEAM_JSON"><code class="name">var <span class="ident">BACKUP_TEAM_JSON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.BACKUP_USER_JSON"><code class="name">var <span class="ident">BACKUP_USER_JSON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.CUSTOM_CATEGORY_JSON"><code class="name">var <span class="ident">CUSTOM_CATEGORY_JSON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.ClOUD_APP_EDISCOVER_TYPE"><code class="name">var <span class="ident">ClOUD_APP_EDISCOVER_TYPE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.DESTINATION_ONEDRIVE_INFO"><code class="name">var <span class="ident">DESTINATION_ONEDRIVE_INFO</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.INDEX_APP_TYPE"><code class="name">var <span class="ident">INDEX_APP_TYPE</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.RESTORE_SUBTASK_JSON"><code class="name">var <span class="ident">RESTORE_SUBTASK_JSON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.RESTORE_TASK_JSON"><code class="name">var <span class="ident">RESTORE_TASK_JSON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
<dt id="cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.USER_ONEDRIVE_RESTORE_JSON"><code class="name">var <span class="ident">USER_ONEDRIVE_RESTORE_JSON</span></code></dt>
<dd>
<div class="desc"></div>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.subclients.cloudapps" href="index.html">cvpysdk.subclients.cloudapps</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants" href="#cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants">TeamsConstants</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.ADD_DISCOVER_TYPE" href="#cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.ADD_DISCOVER_TYPE">ADD_DISCOVER_TYPE</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.ADD_GROUP_JSON" href="#cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.ADD_GROUP_JSON">ADD_GROUP_JSON</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.ADD_REQUEST_JSON" href="#cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.ADD_REQUEST_JSON">ADD_REQUEST_JSON</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.ADD_SUBCLIENT_ENTITY_JSON" href="#cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.ADD_SUBCLIENT_ENTITY_JSON">ADD_SUBCLIENT_ENTITY_JSON</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.ADD_TEAM_JSON" href="#cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.ADD_TEAM_JSON">ADD_TEAM_JSON</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.ADD_USER_JSON" href="#cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.ADD_USER_JSON">ADD_USER_JSON</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.ASSOCIATIONS" href="#cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.ASSOCIATIONS">ASSOCIATIONS</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.BACKUP_REQUEST_JSON" href="#cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.BACKUP_REQUEST_JSON">BACKUP_REQUEST_JSON</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.BACKUP_SUBTASK_JSON" href="#cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.BACKUP_SUBTASK_JSON">BACKUP_SUBTASK_JSON</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.BACKUP_TEAM_JSON" href="#cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.BACKUP_TEAM_JSON">BACKUP_TEAM_JSON</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.BACKUP_USER_JSON" href="#cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.BACKUP_USER_JSON">BACKUP_USER_JSON</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.CUSTOM_CATEGORY_JSON" href="#cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.CUSTOM_CATEGORY_JSON">CUSTOM_CATEGORY_JSON</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.ClOUD_APP_EDISCOVER_TYPE" href="#cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.ClOUD_APP_EDISCOVER_TYPE">ClOUD_APP_EDISCOVER_TYPE</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.DESTINATION_ONEDRIVE_INFO" href="#cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.DESTINATION_ONEDRIVE_INFO">DESTINATION_ONEDRIVE_INFO</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.INDEX_APP_TYPE" href="#cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.INDEX_APP_TYPE">INDEX_APP_TYPE</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.RESTORE_SUBTASK_JSON" href="#cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.RESTORE_SUBTASK_JSON">RESTORE_SUBTASK_JSON</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.RESTORE_TASK_JSON" href="#cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.RESTORE_TASK_JSON">RESTORE_TASK_JSON</a></code></li>
<li><code><a title="cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.USER_ONEDRIVE_RESTORE_JSON" href="#cvpysdk.subclients.cloudapps.teams_constants.TeamsConstants.USER_ONEDRIVE_RESTORE_JSON">USER_ONEDRIVE_RESTORE_JSON</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>