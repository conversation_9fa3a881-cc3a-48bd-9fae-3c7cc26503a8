<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.domains API documentation</title>
<meta name="description" content="File for performing domain related operations …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.domains</code></h1>
</header>
<section id="section-intro">
<p>File for performing domain related operations.</p>
<p>Domains: Class for representing all the associated domains with the commcell.</p>
<h2 id="domains">Domains</h2>
<p><strong>init</strong>(commcell_object)
&ndash;
initialize instance of the Domains associated with
the specified commcell</p>
<p><strong>str</strong>()
&ndash;
returns all the domains associated with the commcell</p>
<p><strong>repr</strong>()
&ndash;
returns the string for the instance of the Domains class</p>
<p><strong>len</strong>()
&ndash;
returns the number of domains associated with the Commcell</p>
<p><strong>getitem</strong>()
&ndash;
returns the name of the domain for the given domain Id
or the details for the given domain name</p>
<p>_get_domains()
&ndash;
gets all the domains associated with the commcell specified</p>
<p>all_domains()
&ndash;
returns the dict of all the domanin configured</p>
<p>has_domain()
&ndash;
checks if a domain exists with the given name or not</p>
<p>get(domain_name)
&ndash;
returns the instance of the Domain class,
for the the input domain name</p>
<p>delete(domain_name)
&ndash;
deletes the domain from the commcell</p>
<p>refresh()
&ndash;
refresh the domains associated with the commcell</p>
<h2 id="domain">Domain</h2>
<p><strong>init</strong>()
&ndash;
initializes instance of the Domain class for doing
operations on the selected Domain</p>
<p><strong>repr</strong>()
&ndash;
returns the string representation of an instance of this class</p>
<p>_get_domain_id()
&ndash;
Gets the domain id associated with this domain</p>
<p>_get_domain_properties
&ndash;
get the properties of the domain</p>
<p>set_sso
&ndash;
Enables/Disables single sign on a domain</p>
<p>set_properties
&ndash;
Sets/modifies the properties for domain</p>
<p>set_domain_status
&ndash;
Enables/Disables the domain</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/domains.py#L1-L690" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34;File for performing domain related operations.


Domains: Class for representing all the associated domains with the commcell.


Domains:

    __init__(commcell_object)   --  initialize instance of the Domains associated with
    the specified commcell

    __str__()                   --  returns all the domains associated with the commcell

    __repr__()                  --  returns the string for the instance of the Domains class

    __len__()                   --  returns the number of domains associated with the Commcell

    __getitem__()               --  returns the name of the domain for the given domain Id
    or the details for the given domain name

    _get_domains()              --  gets all the domains associated with the commcell specified

    all_domains()               --  returns the dict of all the domanin configured

    has_domain()                --  checks if a domain exists with the given name or not

    get(domain_name)            --  returns the instance of the Domain class,
    for the the input domain name

    delete(domain_name)         --  deletes the domain from the commcell

    refresh()                   --  refresh the domains associated with the commcell


Domain:

    __init__()                  --  initializes instance of the Domain class for doing
    operations on the selected Domain

    __repr__()                  --  returns the string representation of an instance of this class

    _get_domain_id()            --  Gets the domain id associated with this domain

    _get_domain_properties      --  get the properties of the domain

    set_sso                     --  Enables/Disables single sign on a domain

    set_properties              --  Sets/modifies the properties for domain

    set_domain_status           --  Enables/Disables the domain
&#34;&#34;&#34;

from __future__ import absolute_import
from __future__ import unicode_literals

from base64 import b64encode

from .exception import SDKException


class Domains(object):
    &#34;&#34;&#34;Class for getting all the domains associated with a commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the Domains class.

            Args:
                commcell_object     (object)    --  instance of the Commcell class

            Returns:
                object - instance of the Domains class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object

        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_

        self._DOMAIN_CONTROLER = self._services[&#39;DOMAIN_CONTROLER&#39;]

        self._domains = None
        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all domains of the Commcell.

            Returns:
                str - string of all the domains for a commcell

        &#34;&#34;&#34;
        representation_string = &#34;{:^5}\t{:^50}\n\n&#34;.format(&#39;S. No.&#39;, &#39;Domain&#39;)

        for index, domain_name in enumerate(self._domains):
            sub_str = &#39;{:^5}\t{:30}\n&#39;.format(index + 1, domain_name)
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the Domains class.&#34;&#34;&#34;
        return &#34;Domains class instance for Commcell&#34;

    def __len__(self):
        &#34;&#34;&#34;Returns the number of the domains associated to the Commcell.&#34;&#34;&#34;
        return len(self.all_domains)

    def __getitem__(self, value):
        &#34;&#34;&#34;Returns the name of the domain for the given domain ID or
            the details of the domain for given domain Name.

            Args:
                value   (str / int)     --  Name or ID of the domain

            Returns:
                str     -   name of the domain, if the domain id was given

                dict    -   dict of details of the domain, if domain name was given

            Raises:
                IndexError:
                    no domain exists with the given Name / Id

        &#34;&#34;&#34;
        value = str(value)

        if value in self.all_domains:
            return self.all_domains[value]
        else:
            try:
                return list(filter(lambda x: x[1][&#39;id&#39;] == value, self.all_domains.items()))[0][0]
            except IndexError:
                raise IndexError(&#39;No domain exists with the given Name / Id&#39;)

    def _get_domains(self):
        &#34;&#34;&#34;Gets all the domains associated with the commcell

            Returns:
                dict - consists of all domain in the commcell

                    {
                         &#34;domain1_name&#34;: domain_Details_dict1,

                         &#34;domain2_name&#34;: domain_Details_dict2
                    }

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._DOMAIN_CONTROLER)

        if flag:
            domains_dict = {}

            if response.json() and &#39;providers&#39; in response.json():
                response_value = response.json()[&#39;providers&#39;]

                for temp in response_value:
                    temp_name = temp[&#39;shortName&#39;][&#39;domainName&#39;].lower()
                    temp_details = temp
                    domains_dict[temp_name] = temp_details

            return domains_dict
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def all_domains(self):
        &#34;&#34;&#34;Returns the domains configured on this commcell

            dict - consists of all domain in the commcell

                    {
                         &#34;domain1_name&#34;: domain_Details_dict1,

                         &#34;domain2_name&#34;: domain_Details_dict2
                    }
        &#34;&#34;&#34;
        return self._domains

    def has_domain(self, domain_name):
        &#34;&#34;&#34;Checks if a domain exists in the commcell with the input domain name.

            Args:
                domain_name     (str)   --  name of the domain

            Returns:
                bool    -   boolean output whether the domain exists in the commcell or not

            Raises:
                SDKException:
                    if type of the domain name argument is not string

        &#34;&#34;&#34;
        if not isinstance(domain_name, str):
            raise SDKException(&#39;Domain&#39;, &#39;101&#39;)

        return self._domains and domain_name.lower() in self._domains

    def get(self, domain_name):
        &#34;&#34;&#34;Returns a domain object of the specified domain name.

            Args:
                domain_name (str)  --  name of the domain

            Returns:
                object of the domain

            Raises:
                SDKException:

                                        if domain doesn&#39;t exist with specified name

                                        if type of the domain name argument is not string

        &#34;&#34;&#34;
        if not isinstance(domain_name, str):
            raise SDKException(&#39;Domain&#39;, &#39;101&#39;)
        if not self.has_domain(domain_name):
            raise SDKException(
                &#39;Domain&#39;, &#39;102&#39;, &#34;Domain {0} doesn&#39;t exists on this commcell.&#34;.format(
                    domain_name)
            )

        return Domain(self._commcell_object, domain_name, self._domains[domain_name.lower()][&#39;shortName&#39;][&#39;id&#39;])

    def delete(self, domain_name):
        &#34;&#34;&#34;Deletes the domain from the commcell.

            Args:
                domain_name     (str)   --  name of the domain to remove from the commcell

            Raises:
                SDKException:
                    if type of the domain name argument is not string

                    if failed to delete domain

                    if response is empty

                    if response is not success

                    if no domain exists with the given name

        &#34;&#34;&#34;

        if not isinstance(domain_name, str):
            raise SDKException(&#39;Domain&#39;, &#39;101&#39;)
        else:
            domain_name = domain_name.lower()

            if self.has_domain(domain_name):
                domain_id = str(self._domains[domain_name][&#34;shortName&#34;][&#34;id&#34;])
                delete_domain = self._services[&#39;DELETE_DOMAIN_CONTROLER&#39;] % (domain_id)

                flag, response = self._cvpysdk_object.make_request(&#39;DELETE&#39;, delete_domain)

                if flag:
                    if response.json() and &#39;errorCode&#39; in response.json():
                        error_code = response.json()[&#34;errorCode&#34;]

                        if error_code == 0:
                            # initialize the domain again
                            # so the domains object has all the domains
                            self.refresh()
                        else:
                            o_str = (&#39;Failed to delete domain with error code: &#34;{0}&#34;&#39;
                                     &#39;\nPlease check the documentation for &#39;
                                     &#39;more details on the error&#39;)
                            raise SDKException(
                                &#39;Domain&#39;, &#39;102&#39;, o_str.format(error_code)
                            )
                    else:
                        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
                else:
                    response_string = self._update_response_(response.text)
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
            else:
                raise SDKException(
                    &#39;Domain&#39;, &#39;102&#39;, &#39;No domain exists with name: {0}&#39;.format(domain_name)
                )

    def refresh(self):
        &#34;&#34;&#34;Refresh the domains associated with the Commcell.&#34;&#34;&#34;
        self._domains = self._get_domains()

    def add(self,
            domain_name,
            netbios_name,
            user_name,
            password,
            company_id=&#34;&#34;,
            ad_proxy_list=None,
            enable_sso=True,
            type_of_server=&#34;active directory&#34;,
            **kwargs):
        &#34;&#34;&#34;Adds a new domain to the commcell.

            Args:
                domain_name     (str)   --  name of the domain

                netbios_name    (str)   --  netbios name of the domain

                user_name       (str)   --  user name of the domain

                password        (str)   --  password of the domain

                company_id      (int)   --  company id for which the domain needs to be added for

                ad_proxy_list     (list)  --  list of client objects to be used as proxy.

                    default: None

                    if no proxy required

                enable_sso      (bool)  --  enable sso for domain

                type_of_server  (str)   --  Type of server to be registered
                    values:
                    &#34;active directory&#34;
                    &#34;apple directory&#34;
                    &#34;oracle ldap&#34;
                    &#34;open ldap&#34;
                    &#34;ldap server&#34;

                **kwargs            --      required parameters for LDAP Server registration and other additional
                                            settings can be passed

                    group_filter    (str)   --  group filter for ldap server
                    user_filter     (str)   --  user filter for ldap server
                    unique_identifier   (str)   --  unique identifier for ldap server
                    base_dn              (str)  --  base dn for ldap server

                    additional_settings     (list)  --  additional settings for directory server.
                        eg:-    [
                                    {
                                        &#34;relativepath&#34;: &#34;CommServDB.Console&#34;,
                                        &#34;keyName&#34;: &#34;basedn&#34;,
                                        &#34;type&#34;: &#34;STRING&#34;,
                                        &#34;value&#34;: &#34;cn=automation_group2,dc=example,dc=com&#34;,
                                        &#34;enabled&#34;: 1
                                    }
                                ]

            Returns:
                dict    -   properties of domain

            Raises:
                SDKException:
                    if type of the domain name argument is not string

                    if no domain exists with the given name

        &#34;&#34;&#34;
        service_type_mapping = {&#34;active directory&#34;: 2, &#34;apple directory&#34;: 8, &#34;oracle ldap&#34;: 9, &#34;open ldap&#34;: 10,
                                &#34;ldap server&#34;: 14}
        service_type = service_type_mapping.get(type_of_server.lower())
        if not service_type:
            raise SDKException(&#39;Domain&#39;, &#34;102&#34;, &#34;please pass valid server type&#34;)
        if not (isinstance(domain_name, str) and
                isinstance(netbios_name, str) and
                isinstance(user_name, str) and
                isinstance(password, str)):
            raise SDKException(&#39;Domain&#39;, &#39;101&#39;)
        else:
            domain_name = domain_name.lower()

            if self.has_domain(domain_name):
                return self._domains[domain_name]

        proxy_information = {}

        if ad_proxy_list:
            if isinstance(ad_proxy_list, list):
                proxy_information = {
                    &#39;adProxyList&#39;: [{&#34;clientName&#34;: client} for client in ad_proxy_list]
                }
            else:
                raise SDKException(&#39;Domain&#39;, &#39;101&#39;)

        domain_create_request = {
            &#34;operation&#34;: 1,
            &#34;provider&#34;: {
                &#34;serviceType&#34;: service_type,
                &#34;flags&#34;: 1,
                &#34;bPassword&#34;: b64encode(password.encode()).decode(),
                &#34;login&#34;: user_name,
                &#34;enabled&#34;: 1,
                &#34;useSecureLdap&#34;: 0,
                &#34;connectName&#34;: domain_name,
                &#34;bLogin&#34;: user_name,
                &#34;ownerCompanyId&#34;: company_id,
                &#34;tppm&#34;: {
                    &#34;enable&#34;: True if ad_proxy_list else False,
                    &#34;tppmType&#34;: 4,
                    &#34;proxyInformation&#34;: proxy_information
                },
                &#34;shortName&#34;: {
                    &#34;domainName&#34;: netbios_name
                }
            }
        }

        if kwargs:
            custom_provider = {
                &#34;providerTypeId&#34;: 0,
                &#34;attributes&#34;: [
                    {
                        &#34;attrId&#34;: 6,
                        &#34;attributeName&#34;: &#34;User group filter&#34;,
                        &#34;staticAttributeString&#34;: &#34;(objectClass=group)&#34;,
                        &#34;customAttributeString&#34;: kwargs.get(&#39;group_filter&#39;, &#39;&#39;),
                        &#34;attrTypeFlags&#34;: 1
                    },
                    {
                        &#34;attrId&#34;: 7,
                        &#34;attributeName&#34;: &#34;User filter&#34;,
                        &#34;staticAttributeString&#34;: &#34;(&amp;(objectCategory=User)(sAMAccountName=*))&#34;,
                        &#34;customAttributeString&#34;: kwargs.get(&#39;user_filter&#39;, &#39;&#39;),
                        &#34;attrTypeFlags&#34;: 1
                    },
                    {
                        &#34;attrId&#34;: 9,
                        &#34;attributeName&#34;: &#34;Unique identifier&#34;,
                        &#34;staticAttributeString&#34;: &#34;sAMAccountName&#34;,
                        &#34;customAttributeString&#34;: kwargs.get(&#39;unique_identifier&#39;, &#39;&#39;),
                        &#34;attrTypeFlags&#34;: 1
                    },
                    {
                        &#34;attrId&#34;: 10,
                        &#34;attributeName&#34;: &#34;base DN&#34;,
                        &#34;staticAttributeString&#34;: &#34;baseDN&#34;,
                        &#34;customAttributeString&#34;: kwargs.get(&#39;base_dn&#39;, &#39;&#39;),
                        &#34;attrTypeFlags&#34;: 1
                    },
                    {
                        &#34;attrTypeFlags&#34;: 6,
                        &#34;customAttributeString&#34;: kwargs.get(&#39;email_attribute&#39;, &#39;mail&#39;),
                        &#34;attrId&#34;: 3,
                        &#34;attributeName&#34;: &#34;Email&#34;,
                        &#34;staticAttributeString&#34;: &#34;mail&#34;
                    },
                    {
                        &#34;attrTypeFlags&#34;: 6,
                        &#34;customAttributeString&#34;: kwargs.get(&#39;guid_attribute&#39;, &#39;objectGUID&#39;),
                        &#34;attrId&#34;: 4,
                        &#34;attributeName&#34;: &#34;GUID&#34;,
                        &#34;staticAttributeString&#34;: &#34;objectGUID&#34;
                    }
                ]
            }
            domain_create_request[&#34;provider&#34;][&#34;customProvider&#34;] = custom_provider

        if kwargs.get(&#39;additional_settings&#39;):
            domain_create_request[&#34;provider&#34;][&#39;additionalSettings&#39;] = kwargs.get(&#39;additional_settings&#39;)

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._DOMAIN_CONTROLER, domain_create_request
        )

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                error_code = response.json()[&#34;errorCode&#34;]

                if error_code == 0:
                    # initialize the domain again
                    # so the domains object has all the domains
                    self.refresh()
                else:
                    error_message = response.json()[&#34;errorMessage&#34;]
                    o_str = (&#39;Failed to add domain with error code: &#34;{0}&#34;&#39;
                             &#39;\nWith error message: &#34;{1}&#34;&#39;)
                    raise SDKException(
                        &#39;Domain&#39;, &#39;102&#39;, o_str.format(error_code, error_message)
                    )
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

class Domain(object):
    &#34;&#34;&#34;Class for representing a particular domain configured on a commcell&#34;&#34;&#34;

    def __init__(self, commcell_object, domain_name, domain_id=None):
        &#34;&#34;&#34;Initialize the domain class object for specified domain

            Args:
                commcell_object (object)  --  instance of the Commcell class

                domain_name         (str)     --  name of the domain

                domain_id           (str)     --  id of the domain


        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._domain_name = domain_name.lower()

        if domain_id is None:
            self._domain_id = self._get_domain_id(self._domain_name)
        else:
            self._domain_id = domain_id

        self._domain = self._commcell_object._services[&#39;DOMAIN_PROPERTIES&#39;] % (self._domain_id)
        self._DOMAIN_CONTROLER = self._commcell_object._services[&#39;DOMAIN_CONTROLER&#39;]
        self._properties = None
        self._get_domain_properties()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        representation_string = &#39;Domain class instance for Domain: &#34;{0}&#34;&#39;
        return representation_string.format(self.domain_name)


    def _get_domain_id(self, domain_name):
        &#34;&#34;&#34;Gets the domain id associated with this domain

            Args:
                domain_name         (str)     --     name of the domain

            Returns:
                int     -     id associated to the specified user
        &#34;&#34;&#34;
        domain = Domains(self._commcell_object)
        return domain.get(domain_name).domain_id

    def _get_domain_properties(self):
        &#34;&#34;&#34;Gets the properties of this domain

            Returns (dict):
                domain_name (str) - name of the domain
                domain_id   (str) - domain id

        &#34;&#34;&#34;

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._domain
        )

        if flag:
            if response.json() and &#39;providers&#39; in response.json():
                self._properties = response.json().get(&#39;providers&#39;, [{}])[0]
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the domain.&#34;&#34;&#34;
        self._get_domain_properties()

    def set_sso(self, flag=True, **kwargs):
        &#34;&#34;&#34;Enables/Disables single sign on the domain
            Args:
                flag(bool)      --  True    - enables SSO
                                    False   - disables SSO
                ** kwargs(dict) --  Key value pairs for supported arguments
                Supported argument values:
                username(str)       --  Username to be used
                password(str)       --  Password to be used

            Returns:
                None

            Raises:
                SDKException:
                    if arguments passed are of incorrect types
                    if failed to enable SSO
                    if response is empty
                    if response is not success
        &#34;&#34;&#34;

        if not isinstance(flag, bool):
            raise SDKException(&#39;Domain&#39;, &#39;101&#39;)
        request_json = {&#34;enableSSO&#34;: flag}
        username = kwargs.get(&#34;username&#34;, None)
        password = kwargs.get(&#34;password&#34;, None)
        if username and password:
            if not (isinstance(username, str) and isinstance(password, str)):
                raise SDKException(&#39;Domain&#39;, &#39;101&#39;)
            request_json[&#34;username&#34;] = username
            request_json[&#34;password&#34;] = b64encode(password.encode()).decode()
        url = self._commcell_object._services[&#39;DOMAIN_SSO&#39;] % self.domain_id
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;PUT&#39;, url, request_json
        )
        if flag:
            if response.json():
                error_code = response.json().get(&#39;errorCode&#39;, 0)
                if error_code != 0:
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))
                return
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    def set_properties(self, req_body):
        &#34;&#34;&#34;Modifies domain properties
            Args:
                req_body  (json)       domain properties in json format to pass as payload to API

            Raises:
                SDKException:
                    if failed to set properties
                    if request is not successful

        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;PUT&#39;, self._commcell_object._services[&#39;DOMAIN_SSO&#39;] % self.domain_id, req_body
        )
        if flag:
            if response.json():
                error_code = response.json().get(&#39;errorCode&#39;, 0)
                if error_code != 0:
                    raise SDKException(
                        &#39;Response&#39;, &#39;101&#39;,
                        self._commcell_object._update_response_(response.text)
                    )
                return
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        raise SDKException(
            &#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    def set_domain_status(self, enable: bool) -&gt; None:
        &#34;&#34;&#34;Enables/Disables the domain
            Args:
                enable(bool)      --  True    - enables domain
                                      False   - disables domain

            Returns:
                None

            Raises:
                SDKException:
                    if response is not success
        &#34;&#34;&#34;

        self._properties[&#39;enabled&#39;] = 1 if enable else 0
        req_json = {&#34;operation&#34;: 3,
                    &#34;provider&#34;: self._properties}
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;POST&#39;, self._DOMAIN_CONTROLER, req_json)
        if flag:
            if response.json():
                error_code = response.json().get(&#39;errorCode&#39;, 0)
                if error_code != 0:
                    raise SDKException(
                        &#39;Domain&#39;, &#39;102&#39;,
                        response.json().get(&#39;errorMessage&#39;, &#39;Unable to update domain status&#39;)
                    )
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(
                &#39;Response&#39;, &#39;101&#39;,
                response.json().get(&#39;errorMessage&#39;, &#39;Unable to update domain status&#39;))

    @property
    def domain_name(self):
        &#34;&#34;&#34;Returns the User display name&#34;&#34;&#34;
        return self._properties[&#39;shortName&#39;][&#39;domainName&#39;]

    @property
    def domain_id(self):
        &#34;&#34;&#34;Returns the user name of this commcell user&#34;&#34;&#34;
        return self._properties[&#39;shortName&#39;][&#39;id&#39;]</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.domains.Domain"><code class="flex name class">
<span>class <span class="ident">Domain</span></span>
<span>(</span><span>commcell_object, domain_name, domain_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing a particular domain configured on a commcell</p>
<p>Initialize the domain class object for specified domain</p>
<h2 id="args">Args</h2>
<p>commcell_object (object)
&ndash;
instance of the Commcell class</p>
<p>domain_name
(str)
&ndash;
name of the domain</p>
<p>domain_id
(str)
&ndash;
id of the domain</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/domains.py#L505-L689" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Domain(object):
    &#34;&#34;&#34;Class for representing a particular domain configured on a commcell&#34;&#34;&#34;

    def __init__(self, commcell_object, domain_name, domain_id=None):
        &#34;&#34;&#34;Initialize the domain class object for specified domain

            Args:
                commcell_object (object)  --  instance of the Commcell class

                domain_name         (str)     --  name of the domain

                domain_id           (str)     --  id of the domain


        &#34;&#34;&#34;
        self._commcell_object = commcell_object
        self._domain_name = domain_name.lower()

        if domain_id is None:
            self._domain_id = self._get_domain_id(self._domain_name)
        else:
            self._domain_id = domain_id

        self._domain = self._commcell_object._services[&#39;DOMAIN_PROPERTIES&#39;] % (self._domain_id)
        self._DOMAIN_CONTROLER = self._commcell_object._services[&#39;DOMAIN_CONTROLER&#39;]
        self._properties = None
        self._get_domain_properties()

    def __repr__(self):
        &#34;&#34;&#34;String representation of the instance of this class.&#34;&#34;&#34;
        representation_string = &#39;Domain class instance for Domain: &#34;{0}&#34;&#39;
        return representation_string.format(self.domain_name)


    def _get_domain_id(self, domain_name):
        &#34;&#34;&#34;Gets the domain id associated with this domain

            Args:
                domain_name         (str)     --     name of the domain

            Returns:
                int     -     id associated to the specified user
        &#34;&#34;&#34;
        domain = Domains(self._commcell_object)
        return domain.get(domain_name).domain_id

    def _get_domain_properties(self):
        &#34;&#34;&#34;Gets the properties of this domain

            Returns (dict):
                domain_name (str) - name of the domain
                domain_id   (str) - domain id

        &#34;&#34;&#34;

        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;GET&#39;, self._domain
        )

        if flag:
            if response.json() and &#39;providers&#39; in response.json():
                self._properties = response.json().get(&#39;providers&#39;, [{}])[0]
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._commcell_object._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    def refresh(self):
        &#34;&#34;&#34;Refresh the properties of the domain.&#34;&#34;&#34;
        self._get_domain_properties()

    def set_sso(self, flag=True, **kwargs):
        &#34;&#34;&#34;Enables/Disables single sign on the domain
            Args:
                flag(bool)      --  True    - enables SSO
                                    False   - disables SSO
                ** kwargs(dict) --  Key value pairs for supported arguments
                Supported argument values:
                username(str)       --  Username to be used
                password(str)       --  Password to be used

            Returns:
                None

            Raises:
                SDKException:
                    if arguments passed are of incorrect types
                    if failed to enable SSO
                    if response is empty
                    if response is not success
        &#34;&#34;&#34;

        if not isinstance(flag, bool):
            raise SDKException(&#39;Domain&#39;, &#39;101&#39;)
        request_json = {&#34;enableSSO&#34;: flag}
        username = kwargs.get(&#34;username&#34;, None)
        password = kwargs.get(&#34;password&#34;, None)
        if username and password:
            if not (isinstance(username, str) and isinstance(password, str)):
                raise SDKException(&#39;Domain&#39;, &#39;101&#39;)
            request_json[&#34;username&#34;] = username
            request_json[&#34;password&#34;] = b64encode(password.encode()).decode()
        url = self._commcell_object._services[&#39;DOMAIN_SSO&#39;] % self.domain_id
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;PUT&#39;, url, request_json
        )
        if flag:
            if response.json():
                error_code = response.json().get(&#39;errorCode&#39;, 0)
                if error_code != 0:
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))
                return
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    def set_properties(self, req_body):
        &#34;&#34;&#34;Modifies domain properties
            Args:
                req_body  (json)       domain properties in json format to pass as payload to API

            Raises:
                SDKException:
                    if failed to set properties
                    if request is not successful

        &#34;&#34;&#34;
        flag, response = self._commcell_object._cvpysdk_object.make_request(
            &#39;PUT&#39;, self._commcell_object._services[&#39;DOMAIN_SSO&#39;] % self.domain_id, req_body
        )
        if flag:
            if response.json():
                error_code = response.json().get(&#39;errorCode&#39;, 0)
                if error_code != 0:
                    raise SDKException(
                        &#39;Response&#39;, &#39;101&#39;,
                        self._commcell_object._update_response_(response.text)
                    )
                return
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        raise SDKException(
            &#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))

    def set_domain_status(self, enable: bool) -&gt; None:
        &#34;&#34;&#34;Enables/Disables the domain
            Args:
                enable(bool)      --  True    - enables domain
                                      False   - disables domain

            Returns:
                None

            Raises:
                SDKException:
                    if response is not success
        &#34;&#34;&#34;

        self._properties[&#39;enabled&#39;] = 1 if enable else 0
        req_json = {&#34;operation&#34;: 3,
                    &#34;provider&#34;: self._properties}
        flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;POST&#39;, self._DOMAIN_CONTROLER, req_json)
        if flag:
            if response.json():
                error_code = response.json().get(&#39;errorCode&#39;, 0)
                if error_code != 0:
                    raise SDKException(
                        &#39;Domain&#39;, &#39;102&#39;,
                        response.json().get(&#39;errorMessage&#39;, &#39;Unable to update domain status&#39;)
                    )
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            raise SDKException(
                &#39;Response&#39;, &#39;101&#39;,
                response.json().get(&#39;errorMessage&#39;, &#39;Unable to update domain status&#39;))

    @property
    def domain_name(self):
        &#34;&#34;&#34;Returns the User display name&#34;&#34;&#34;
        return self._properties[&#39;shortName&#39;][&#39;domainName&#39;]

    @property
    def domain_id(self):
        &#34;&#34;&#34;Returns the user name of this commcell user&#34;&#34;&#34;
        return self._properties[&#39;shortName&#39;][&#39;id&#39;]</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.domains.Domain.domain_id"><code class="name">var <span class="ident">domain_id</span></code></dt>
<dd>
<div class="desc"><p>Returns the user name of this commcell user</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/domains.py#L686-L689" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def domain_id(self):
    &#34;&#34;&#34;Returns the user name of this commcell user&#34;&#34;&#34;
    return self._properties[&#39;shortName&#39;][&#39;id&#39;]</code></pre>
</details>
</dd>
<dt id="cvpysdk.domains.Domain.domain_name"><code class="name">var <span class="ident">domain_name</span></code></dt>
<dd>
<div class="desc"><p>Returns the User display name</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/domains.py#L681-L684" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def domain_name(self):
    &#34;&#34;&#34;Returns the User display name&#34;&#34;&#34;
    return self._properties[&#39;shortName&#39;][&#39;domainName&#39;]</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.domains.Domain.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the properties of the domain.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/domains.py#L573-L575" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the properties of the domain.&#34;&#34;&#34;
    self._get_domain_properties()</code></pre>
</details>
</dd>
<dt id="cvpysdk.domains.Domain.set_domain_status"><code class="name flex">
<span>def <span class="ident">set_domain_status</span></span>(<span>self, enable: bool) ‑> None</span>
</code></dt>
<dd>
<div class="desc"><p>Enables/Disables the domain</p>
<h2 id="args">Args</h2>
<p>enable(bool)
&ndash;
True
- enables domain
False
- disables domain</p>
<h2 id="returns">Returns</h2>
<p>None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if response is not success</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/domains.py#L648-L679" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_domain_status(self, enable: bool) -&gt; None:
    &#34;&#34;&#34;Enables/Disables the domain
        Args:
            enable(bool)      --  True    - enables domain
                                  False   - disables domain

        Returns:
            None

        Raises:
            SDKException:
                if response is not success
    &#34;&#34;&#34;

    self._properties[&#39;enabled&#39;] = 1 if enable else 0
    req_json = {&#34;operation&#34;: 3,
                &#34;provider&#34;: self._properties}
    flag, response = self._commcell_object._cvpysdk_object.make_request(&#39;POST&#39;, self._DOMAIN_CONTROLER, req_json)
    if flag:
        if response.json():
            error_code = response.json().get(&#39;errorCode&#39;, 0)
            if error_code != 0:
                raise SDKException(
                    &#39;Domain&#39;, &#39;102&#39;,
                    response.json().get(&#39;errorMessage&#39;, &#39;Unable to update domain status&#39;)
                )
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        raise SDKException(
            &#39;Response&#39;, &#39;101&#39;,
            response.json().get(&#39;errorMessage&#39;, &#39;Unable to update domain status&#39;))</code></pre>
</details>
</dd>
<dt id="cvpysdk.domains.Domain.set_properties"><code class="name flex">
<span>def <span class="ident">set_properties</span></span>(<span>self, req_body)</span>
</code></dt>
<dd>
<div class="desc"><p>Modifies domain properties</p>
<h2 id="args">Args</h2>
<p>req_body
(json)
domain properties in json format to pass as payload to API</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if failed to set properties
if request is not successful</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/domains.py#L621-L646" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_properties(self, req_body):
    &#34;&#34;&#34;Modifies domain properties
        Args:
            req_body  (json)       domain properties in json format to pass as payload to API

        Raises:
            SDKException:
                if failed to set properties
                if request is not successful

    &#34;&#34;&#34;
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;PUT&#39;, self._commcell_object._services[&#39;DOMAIN_SSO&#39;] % self.domain_id, req_body
    )
    if flag:
        if response.json():
            error_code = response.json().get(&#39;errorCode&#39;, 0)
            if error_code != 0:
                raise SDKException(
                    &#39;Response&#39;, &#39;101&#39;,
                    self._commcell_object._update_response_(response.text)
                )
            return
        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    raise SDKException(
        &#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))</code></pre>
</details>
</dd>
<dt id="cvpysdk.domains.Domain.set_sso"><code class="name flex">
<span>def <span class="ident">set_sso</span></span>(<span>self, flag=True, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Enables/Disables single sign on the domain</p>
<h2 id="args">Args</h2>
<p>flag(bool)
&ndash;
True
- enables SSO
False
- disables SSO
** kwargs(dict) &ndash;
Key value pairs for supported arguments
Supported argument values:
username(str)
&ndash;
Username to be used
password(str)
&ndash;
Password to be used</p>
<h2 id="returns">Returns</h2>
<p>None</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if arguments passed are of incorrect types
if failed to enable SSO
if response is empty
if response is not success</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/domains.py#L577-L619" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def set_sso(self, flag=True, **kwargs):
    &#34;&#34;&#34;Enables/Disables single sign on the domain
        Args:
            flag(bool)      --  True    - enables SSO
                                False   - disables SSO
            ** kwargs(dict) --  Key value pairs for supported arguments
            Supported argument values:
            username(str)       --  Username to be used
            password(str)       --  Password to be used

        Returns:
            None

        Raises:
            SDKException:
                if arguments passed are of incorrect types
                if failed to enable SSO
                if response is empty
                if response is not success
    &#34;&#34;&#34;

    if not isinstance(flag, bool):
        raise SDKException(&#39;Domain&#39;, &#39;101&#39;)
    request_json = {&#34;enableSSO&#34;: flag}
    username = kwargs.get(&#34;username&#34;, None)
    password = kwargs.get(&#34;password&#34;, None)
    if username and password:
        if not (isinstance(username, str) and isinstance(password, str)):
            raise SDKException(&#39;Domain&#39;, &#39;101&#39;)
        request_json[&#34;username&#34;] = username
        request_json[&#34;password&#34;] = b64encode(password.encode()).decode()
    url = self._commcell_object._services[&#39;DOMAIN_SSO&#39;] % self.domain_id
    flag, response = self._commcell_object._cvpysdk_object.make_request(
        &#39;PUT&#39;, url, request_json
    )
    if flag:
        if response.json():
            error_code = response.json().get(&#39;errorCode&#39;, 0)
            if error_code != 0:
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))
            return
        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    raise SDKException(&#39;Response&#39;, &#39;101&#39;, self._commcell_object._update_response_(response.text))</code></pre>
</details>
</dd>
</dl>
</dd>
<dt id="cvpysdk.domains.Domains"><code class="flex name class">
<span>class <span class="ident">Domains</span></span>
<span>(</span><span>commcell_object)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for getting all the domains associated with a commcell.</p>
<p>Initialize object of the Domains class.</p>
<h2 id="args">Args</h2>
<p>commcell_object
(object)
&ndash;
instance of the Commcell class</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Domains class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/domains.py#L79-L503" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class Domains(object):
    &#34;&#34;&#34;Class for getting all the domains associated with a commcell.&#34;&#34;&#34;

    def __init__(self, commcell_object):
        &#34;&#34;&#34;Initialize object of the Domains class.

            Args:
                commcell_object     (object)    --  instance of the Commcell class

            Returns:
                object - instance of the Domains class

        &#34;&#34;&#34;
        self._commcell_object = commcell_object

        self._cvpysdk_object = commcell_object._cvpysdk_object
        self._services = commcell_object._services
        self._update_response_ = commcell_object._update_response_

        self._DOMAIN_CONTROLER = self._services[&#39;DOMAIN_CONTROLER&#39;]

        self._domains = None
        self.refresh()

    def __str__(self):
        &#34;&#34;&#34;Representation string consisting of all domains of the Commcell.

            Returns:
                str - string of all the domains for a commcell

        &#34;&#34;&#34;
        representation_string = &#34;{:^5}\t{:^50}\n\n&#34;.format(&#39;S. No.&#39;, &#39;Domain&#39;)

        for index, domain_name in enumerate(self._domains):
            sub_str = &#39;{:^5}\t{:30}\n&#39;.format(index + 1, domain_name)
            representation_string += sub_str

        return representation_string.strip()

    def __repr__(self):
        &#34;&#34;&#34;Representation string for the instance of the Domains class.&#34;&#34;&#34;
        return &#34;Domains class instance for Commcell&#34;

    def __len__(self):
        &#34;&#34;&#34;Returns the number of the domains associated to the Commcell.&#34;&#34;&#34;
        return len(self.all_domains)

    def __getitem__(self, value):
        &#34;&#34;&#34;Returns the name of the domain for the given domain ID or
            the details of the domain for given domain Name.

            Args:
                value   (str / int)     --  Name or ID of the domain

            Returns:
                str     -   name of the domain, if the domain id was given

                dict    -   dict of details of the domain, if domain name was given

            Raises:
                IndexError:
                    no domain exists with the given Name / Id

        &#34;&#34;&#34;
        value = str(value)

        if value in self.all_domains:
            return self.all_domains[value]
        else:
            try:
                return list(filter(lambda x: x[1][&#39;id&#39;] == value, self.all_domains.items()))[0][0]
            except IndexError:
                raise IndexError(&#39;No domain exists with the given Name / Id&#39;)

    def _get_domains(self):
        &#34;&#34;&#34;Gets all the domains associated with the commcell

            Returns:
                dict - consists of all domain in the commcell

                    {
                         &#34;domain1_name&#34;: domain_Details_dict1,

                         &#34;domain2_name&#34;: domain_Details_dict2
                    }

            Raises:
                SDKException:
                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        flag, response = self._cvpysdk_object.make_request(&#39;GET&#39;, self._DOMAIN_CONTROLER)

        if flag:
            domains_dict = {}

            if response.json() and &#39;providers&#39; in response.json():
                response_value = response.json()[&#39;providers&#39;]

                for temp in response_value:
                    temp_name = temp[&#39;shortName&#39;][&#39;domainName&#39;].lower()
                    temp_details = temp
                    domains_dict[temp_name] = temp_details

            return domains_dict
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)

    @property
    def all_domains(self):
        &#34;&#34;&#34;Returns the domains configured on this commcell

            dict - consists of all domain in the commcell

                    {
                         &#34;domain1_name&#34;: domain_Details_dict1,

                         &#34;domain2_name&#34;: domain_Details_dict2
                    }
        &#34;&#34;&#34;
        return self._domains

    def has_domain(self, domain_name):
        &#34;&#34;&#34;Checks if a domain exists in the commcell with the input domain name.

            Args:
                domain_name     (str)   --  name of the domain

            Returns:
                bool    -   boolean output whether the domain exists in the commcell or not

            Raises:
                SDKException:
                    if type of the domain name argument is not string

        &#34;&#34;&#34;
        if not isinstance(domain_name, str):
            raise SDKException(&#39;Domain&#39;, &#39;101&#39;)

        return self._domains and domain_name.lower() in self._domains

    def get(self, domain_name):
        &#34;&#34;&#34;Returns a domain object of the specified domain name.

            Args:
                domain_name (str)  --  name of the domain

            Returns:
                object of the domain

            Raises:
                SDKException:

                                        if domain doesn&#39;t exist with specified name

                                        if type of the domain name argument is not string

        &#34;&#34;&#34;
        if not isinstance(domain_name, str):
            raise SDKException(&#39;Domain&#39;, &#39;101&#39;)
        if not self.has_domain(domain_name):
            raise SDKException(
                &#39;Domain&#39;, &#39;102&#39;, &#34;Domain {0} doesn&#39;t exists on this commcell.&#34;.format(
                    domain_name)
            )

        return Domain(self._commcell_object, domain_name, self._domains[domain_name.lower()][&#39;shortName&#39;][&#39;id&#39;])

    def delete(self, domain_name):
        &#34;&#34;&#34;Deletes the domain from the commcell.

            Args:
                domain_name     (str)   --  name of the domain to remove from the commcell

            Raises:
                SDKException:
                    if type of the domain name argument is not string

                    if failed to delete domain

                    if response is empty

                    if response is not success

                    if no domain exists with the given name

        &#34;&#34;&#34;

        if not isinstance(domain_name, str):
            raise SDKException(&#39;Domain&#39;, &#39;101&#39;)
        else:
            domain_name = domain_name.lower()

            if self.has_domain(domain_name):
                domain_id = str(self._domains[domain_name][&#34;shortName&#34;][&#34;id&#34;])
                delete_domain = self._services[&#39;DELETE_DOMAIN_CONTROLER&#39;] % (domain_id)

                flag, response = self._cvpysdk_object.make_request(&#39;DELETE&#39;, delete_domain)

                if flag:
                    if response.json() and &#39;errorCode&#39; in response.json():
                        error_code = response.json()[&#34;errorCode&#34;]

                        if error_code == 0:
                            # initialize the domain again
                            # so the domains object has all the domains
                            self.refresh()
                        else:
                            o_str = (&#39;Failed to delete domain with error code: &#34;{0}&#34;&#39;
                                     &#39;\nPlease check the documentation for &#39;
                                     &#39;more details on the error&#39;)
                            raise SDKException(
                                &#39;Domain&#39;, &#39;102&#39;, o_str.format(error_code)
                            )
                    else:
                        raise SDKException(&#39;Response&#39;, &#39;102&#39;)
                else:
                    response_string = self._update_response_(response.text)
                    raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
            else:
                raise SDKException(
                    &#39;Domain&#39;, &#39;102&#39;, &#39;No domain exists with name: {0}&#39;.format(domain_name)
                )

    def refresh(self):
        &#34;&#34;&#34;Refresh the domains associated with the Commcell.&#34;&#34;&#34;
        self._domains = self._get_domains()

    def add(self,
            domain_name,
            netbios_name,
            user_name,
            password,
            company_id=&#34;&#34;,
            ad_proxy_list=None,
            enable_sso=True,
            type_of_server=&#34;active directory&#34;,
            **kwargs):
        &#34;&#34;&#34;Adds a new domain to the commcell.

            Args:
                domain_name     (str)   --  name of the domain

                netbios_name    (str)   --  netbios name of the domain

                user_name       (str)   --  user name of the domain

                password        (str)   --  password of the domain

                company_id      (int)   --  company id for which the domain needs to be added for

                ad_proxy_list     (list)  --  list of client objects to be used as proxy.

                    default: None

                    if no proxy required

                enable_sso      (bool)  --  enable sso for domain

                type_of_server  (str)   --  Type of server to be registered
                    values:
                    &#34;active directory&#34;
                    &#34;apple directory&#34;
                    &#34;oracle ldap&#34;
                    &#34;open ldap&#34;
                    &#34;ldap server&#34;

                **kwargs            --      required parameters for LDAP Server registration and other additional
                                            settings can be passed

                    group_filter    (str)   --  group filter for ldap server
                    user_filter     (str)   --  user filter for ldap server
                    unique_identifier   (str)   --  unique identifier for ldap server
                    base_dn              (str)  --  base dn for ldap server

                    additional_settings     (list)  --  additional settings for directory server.
                        eg:-    [
                                    {
                                        &#34;relativepath&#34;: &#34;CommServDB.Console&#34;,
                                        &#34;keyName&#34;: &#34;basedn&#34;,
                                        &#34;type&#34;: &#34;STRING&#34;,
                                        &#34;value&#34;: &#34;cn=automation_group2,dc=example,dc=com&#34;,
                                        &#34;enabled&#34;: 1
                                    }
                                ]

            Returns:
                dict    -   properties of domain

            Raises:
                SDKException:
                    if type of the domain name argument is not string

                    if no domain exists with the given name

        &#34;&#34;&#34;
        service_type_mapping = {&#34;active directory&#34;: 2, &#34;apple directory&#34;: 8, &#34;oracle ldap&#34;: 9, &#34;open ldap&#34;: 10,
                                &#34;ldap server&#34;: 14}
        service_type = service_type_mapping.get(type_of_server.lower())
        if not service_type:
            raise SDKException(&#39;Domain&#39;, &#34;102&#34;, &#34;please pass valid server type&#34;)
        if not (isinstance(domain_name, str) and
                isinstance(netbios_name, str) and
                isinstance(user_name, str) and
                isinstance(password, str)):
            raise SDKException(&#39;Domain&#39;, &#39;101&#39;)
        else:
            domain_name = domain_name.lower()

            if self.has_domain(domain_name):
                return self._domains[domain_name]

        proxy_information = {}

        if ad_proxy_list:
            if isinstance(ad_proxy_list, list):
                proxy_information = {
                    &#39;adProxyList&#39;: [{&#34;clientName&#34;: client} for client in ad_proxy_list]
                }
            else:
                raise SDKException(&#39;Domain&#39;, &#39;101&#39;)

        domain_create_request = {
            &#34;operation&#34;: 1,
            &#34;provider&#34;: {
                &#34;serviceType&#34;: service_type,
                &#34;flags&#34;: 1,
                &#34;bPassword&#34;: b64encode(password.encode()).decode(),
                &#34;login&#34;: user_name,
                &#34;enabled&#34;: 1,
                &#34;useSecureLdap&#34;: 0,
                &#34;connectName&#34;: domain_name,
                &#34;bLogin&#34;: user_name,
                &#34;ownerCompanyId&#34;: company_id,
                &#34;tppm&#34;: {
                    &#34;enable&#34;: True if ad_proxy_list else False,
                    &#34;tppmType&#34;: 4,
                    &#34;proxyInformation&#34;: proxy_information
                },
                &#34;shortName&#34;: {
                    &#34;domainName&#34;: netbios_name
                }
            }
        }

        if kwargs:
            custom_provider = {
                &#34;providerTypeId&#34;: 0,
                &#34;attributes&#34;: [
                    {
                        &#34;attrId&#34;: 6,
                        &#34;attributeName&#34;: &#34;User group filter&#34;,
                        &#34;staticAttributeString&#34;: &#34;(objectClass=group)&#34;,
                        &#34;customAttributeString&#34;: kwargs.get(&#39;group_filter&#39;, &#39;&#39;),
                        &#34;attrTypeFlags&#34;: 1
                    },
                    {
                        &#34;attrId&#34;: 7,
                        &#34;attributeName&#34;: &#34;User filter&#34;,
                        &#34;staticAttributeString&#34;: &#34;(&amp;(objectCategory=User)(sAMAccountName=*))&#34;,
                        &#34;customAttributeString&#34;: kwargs.get(&#39;user_filter&#39;, &#39;&#39;),
                        &#34;attrTypeFlags&#34;: 1
                    },
                    {
                        &#34;attrId&#34;: 9,
                        &#34;attributeName&#34;: &#34;Unique identifier&#34;,
                        &#34;staticAttributeString&#34;: &#34;sAMAccountName&#34;,
                        &#34;customAttributeString&#34;: kwargs.get(&#39;unique_identifier&#39;, &#39;&#39;),
                        &#34;attrTypeFlags&#34;: 1
                    },
                    {
                        &#34;attrId&#34;: 10,
                        &#34;attributeName&#34;: &#34;base DN&#34;,
                        &#34;staticAttributeString&#34;: &#34;baseDN&#34;,
                        &#34;customAttributeString&#34;: kwargs.get(&#39;base_dn&#39;, &#39;&#39;),
                        &#34;attrTypeFlags&#34;: 1
                    },
                    {
                        &#34;attrTypeFlags&#34;: 6,
                        &#34;customAttributeString&#34;: kwargs.get(&#39;email_attribute&#39;, &#39;mail&#39;),
                        &#34;attrId&#34;: 3,
                        &#34;attributeName&#34;: &#34;Email&#34;,
                        &#34;staticAttributeString&#34;: &#34;mail&#34;
                    },
                    {
                        &#34;attrTypeFlags&#34;: 6,
                        &#34;customAttributeString&#34;: kwargs.get(&#39;guid_attribute&#39;, &#39;objectGUID&#39;),
                        &#34;attrId&#34;: 4,
                        &#34;attributeName&#34;: &#34;GUID&#34;,
                        &#34;staticAttributeString&#34;: &#34;objectGUID&#34;
                    }
                ]
            }
            domain_create_request[&#34;provider&#34;][&#34;customProvider&#34;] = custom_provider

        if kwargs.get(&#39;additional_settings&#39;):
            domain_create_request[&#34;provider&#34;][&#39;additionalSettings&#39;] = kwargs.get(&#39;additional_settings&#39;)

        flag, response = self._cvpysdk_object.make_request(
            &#39;POST&#39;, self._DOMAIN_CONTROLER, domain_create_request
        )

        if flag:
            if response.json() and &#39;errorCode&#39; in response.json():
                error_code = response.json()[&#34;errorCode&#34;]

                if error_code == 0:
                    # initialize the domain again
                    # so the domains object has all the domains
                    self.refresh()
                else:
                    error_message = response.json()[&#34;errorMessage&#34;]
                    o_str = (&#39;Failed to add domain with error code: &#34;{0}&#34;&#39;
                             &#39;\nWith error message: &#34;{1}&#34;&#39;)
                    raise SDKException(
                        &#39;Domain&#39;, &#39;102&#39;, o_str.format(error_code, error_message)
                    )
            else:
                raise SDKException(&#39;Response&#39;, &#39;102&#39;)
        else:
            response_string = self._update_response_(response.text)
            raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.domains.Domains.all_domains"><code class="name">var <span class="ident">all_domains</span></code></dt>
<dd>
<div class="desc"><p>Returns the domains configured on this commcell</p>
<p>dict - consists of all domain in the commcell</p>
<pre><code>    {
         "domain1_name": domain_Details_dict1,

         "domain2_name": domain_Details_dict2
    }
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/domains.py#L190-L202" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def all_domains(self):
    &#34;&#34;&#34;Returns the domains configured on this commcell

        dict - consists of all domain in the commcell

                {
                     &#34;domain1_name&#34;: domain_Details_dict1,

                     &#34;domain2_name&#34;: domain_Details_dict2
                }
    &#34;&#34;&#34;
    return self._domains</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.domains.Domains.add"><code class="name flex">
<span>def <span class="ident">add</span></span>(<span>self, domain_name, netbios_name, user_name, password, company_id='', ad_proxy_list=None, enable_sso=True, type_of_server='active directory', **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Adds a new domain to the commcell.</p>
<h2 id="args">Args</h2>
<p>domain_name
(str)
&ndash;
name of the domain</p>
<p>netbios_name
(str)
&ndash;
netbios name of the domain</p>
<p>user_name
(str)
&ndash;
user name of the domain</p>
<p>password
(str)
&ndash;
password of the domain</p>
<p>company_id
(int)
&ndash;
company id for which the domain needs to be added for</p>
<p>ad_proxy_list
(list)
&ndash;
list of client objects to be used as proxy.</p>
<pre><code>default: None

if no proxy required
</code></pre>
<p>enable_sso
(bool)
&ndash;
enable sso for domain</p>
<p>type_of_server
(str)
&ndash;
Type of server to be registered
values:
"active directory"
"apple directory"
"oracle ldap"
"open ldap"
"ldap server"</p>
<p>**kwargs
&ndash;
required parameters for LDAP Server registration and other additional
settings can be passed</p>
<pre><code>group_filter    (str)   --  group filter for ldap server
user_filter     (str)   --  user filter for ldap server
unique_identifier   (str)   --  unique identifier for ldap server
base_dn              (str)  --  base dn for ldap server

additional_settings     (list)  --  additional settings for directory server.
    eg:-    [
                {
                    "relativepath": "CommServDB.Console",
                    "keyName": "basedn",
                    "type": "STRING",
                    "value": "cn=automation_group2,dc=example,dc=com",
                    "enabled": 1
                }
            ]
</code></pre>
<h2 id="returns">Returns</h2>
<p>dict
-
properties of domain</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the domain name argument is not string</p>
<pre><code>if no domain exists with the given name
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/domains.py#L310-L503" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def add(self,
        domain_name,
        netbios_name,
        user_name,
        password,
        company_id=&#34;&#34;,
        ad_proxy_list=None,
        enable_sso=True,
        type_of_server=&#34;active directory&#34;,
        **kwargs):
    &#34;&#34;&#34;Adds a new domain to the commcell.

        Args:
            domain_name     (str)   --  name of the domain

            netbios_name    (str)   --  netbios name of the domain

            user_name       (str)   --  user name of the domain

            password        (str)   --  password of the domain

            company_id      (int)   --  company id for which the domain needs to be added for

            ad_proxy_list     (list)  --  list of client objects to be used as proxy.

                default: None

                if no proxy required

            enable_sso      (bool)  --  enable sso for domain

            type_of_server  (str)   --  Type of server to be registered
                values:
                &#34;active directory&#34;
                &#34;apple directory&#34;
                &#34;oracle ldap&#34;
                &#34;open ldap&#34;
                &#34;ldap server&#34;

            **kwargs            --      required parameters for LDAP Server registration and other additional
                                        settings can be passed

                group_filter    (str)   --  group filter for ldap server
                user_filter     (str)   --  user filter for ldap server
                unique_identifier   (str)   --  unique identifier for ldap server
                base_dn              (str)  --  base dn for ldap server

                additional_settings     (list)  --  additional settings for directory server.
                    eg:-    [
                                {
                                    &#34;relativepath&#34;: &#34;CommServDB.Console&#34;,
                                    &#34;keyName&#34;: &#34;basedn&#34;,
                                    &#34;type&#34;: &#34;STRING&#34;,
                                    &#34;value&#34;: &#34;cn=automation_group2,dc=example,dc=com&#34;,
                                    &#34;enabled&#34;: 1
                                }
                            ]

        Returns:
            dict    -   properties of domain

        Raises:
            SDKException:
                if type of the domain name argument is not string

                if no domain exists with the given name

    &#34;&#34;&#34;
    service_type_mapping = {&#34;active directory&#34;: 2, &#34;apple directory&#34;: 8, &#34;oracle ldap&#34;: 9, &#34;open ldap&#34;: 10,
                            &#34;ldap server&#34;: 14}
    service_type = service_type_mapping.get(type_of_server.lower())
    if not service_type:
        raise SDKException(&#39;Domain&#39;, &#34;102&#34;, &#34;please pass valid server type&#34;)
    if not (isinstance(domain_name, str) and
            isinstance(netbios_name, str) and
            isinstance(user_name, str) and
            isinstance(password, str)):
        raise SDKException(&#39;Domain&#39;, &#39;101&#39;)
    else:
        domain_name = domain_name.lower()

        if self.has_domain(domain_name):
            return self._domains[domain_name]

    proxy_information = {}

    if ad_proxy_list:
        if isinstance(ad_proxy_list, list):
            proxy_information = {
                &#39;adProxyList&#39;: [{&#34;clientName&#34;: client} for client in ad_proxy_list]
            }
        else:
            raise SDKException(&#39;Domain&#39;, &#39;101&#39;)

    domain_create_request = {
        &#34;operation&#34;: 1,
        &#34;provider&#34;: {
            &#34;serviceType&#34;: service_type,
            &#34;flags&#34;: 1,
            &#34;bPassword&#34;: b64encode(password.encode()).decode(),
            &#34;login&#34;: user_name,
            &#34;enabled&#34;: 1,
            &#34;useSecureLdap&#34;: 0,
            &#34;connectName&#34;: domain_name,
            &#34;bLogin&#34;: user_name,
            &#34;ownerCompanyId&#34;: company_id,
            &#34;tppm&#34;: {
                &#34;enable&#34;: True if ad_proxy_list else False,
                &#34;tppmType&#34;: 4,
                &#34;proxyInformation&#34;: proxy_information
            },
            &#34;shortName&#34;: {
                &#34;domainName&#34;: netbios_name
            }
        }
    }

    if kwargs:
        custom_provider = {
            &#34;providerTypeId&#34;: 0,
            &#34;attributes&#34;: [
                {
                    &#34;attrId&#34;: 6,
                    &#34;attributeName&#34;: &#34;User group filter&#34;,
                    &#34;staticAttributeString&#34;: &#34;(objectClass=group)&#34;,
                    &#34;customAttributeString&#34;: kwargs.get(&#39;group_filter&#39;, &#39;&#39;),
                    &#34;attrTypeFlags&#34;: 1
                },
                {
                    &#34;attrId&#34;: 7,
                    &#34;attributeName&#34;: &#34;User filter&#34;,
                    &#34;staticAttributeString&#34;: &#34;(&amp;(objectCategory=User)(sAMAccountName=*))&#34;,
                    &#34;customAttributeString&#34;: kwargs.get(&#39;user_filter&#39;, &#39;&#39;),
                    &#34;attrTypeFlags&#34;: 1
                },
                {
                    &#34;attrId&#34;: 9,
                    &#34;attributeName&#34;: &#34;Unique identifier&#34;,
                    &#34;staticAttributeString&#34;: &#34;sAMAccountName&#34;,
                    &#34;customAttributeString&#34;: kwargs.get(&#39;unique_identifier&#39;, &#39;&#39;),
                    &#34;attrTypeFlags&#34;: 1
                },
                {
                    &#34;attrId&#34;: 10,
                    &#34;attributeName&#34;: &#34;base DN&#34;,
                    &#34;staticAttributeString&#34;: &#34;baseDN&#34;,
                    &#34;customAttributeString&#34;: kwargs.get(&#39;base_dn&#39;, &#39;&#39;),
                    &#34;attrTypeFlags&#34;: 1
                },
                {
                    &#34;attrTypeFlags&#34;: 6,
                    &#34;customAttributeString&#34;: kwargs.get(&#39;email_attribute&#39;, &#39;mail&#39;),
                    &#34;attrId&#34;: 3,
                    &#34;attributeName&#34;: &#34;Email&#34;,
                    &#34;staticAttributeString&#34;: &#34;mail&#34;
                },
                {
                    &#34;attrTypeFlags&#34;: 6,
                    &#34;customAttributeString&#34;: kwargs.get(&#39;guid_attribute&#39;, &#39;objectGUID&#39;),
                    &#34;attrId&#34;: 4,
                    &#34;attributeName&#34;: &#34;GUID&#34;,
                    &#34;staticAttributeString&#34;: &#34;objectGUID&#34;
                }
            ]
        }
        domain_create_request[&#34;provider&#34;][&#34;customProvider&#34;] = custom_provider

    if kwargs.get(&#39;additional_settings&#39;):
        domain_create_request[&#34;provider&#34;][&#39;additionalSettings&#39;] = kwargs.get(&#39;additional_settings&#39;)

    flag, response = self._cvpysdk_object.make_request(
        &#39;POST&#39;, self._DOMAIN_CONTROLER, domain_create_request
    )

    if flag:
        if response.json() and &#39;errorCode&#39; in response.json():
            error_code = response.json()[&#34;errorCode&#34;]

            if error_code == 0:
                # initialize the domain again
                # so the domains object has all the domains
                self.refresh()
            else:
                error_message = response.json()[&#34;errorMessage&#34;]
                o_str = (&#39;Failed to add domain with error code: &#34;{0}&#34;&#39;
                         &#39;\nWith error message: &#34;{1}&#34;&#39;)
                raise SDKException(
                    &#39;Domain&#39;, &#39;102&#39;, o_str.format(error_code, error_message)
                )
        else:
            raise SDKException(&#39;Response&#39;, &#39;102&#39;)
    else:
        response_string = self._update_response_(response.text)
        raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)</code></pre>
</details>
</dd>
<dt id="cvpysdk.domains.Domains.delete"><code class="name flex">
<span>def <span class="ident">delete</span></span>(<span>self, domain_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Deletes the domain from the commcell.</p>
<h2 id="args">Args</h2>
<p>domain_name
(str)
&ndash;
name of the domain to remove from the commcell</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the domain name argument is not string</p>
<pre><code>if failed to delete domain

if response is empty

if response is not success

if no domain exists with the given name
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/domains.py#L250-L304" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def delete(self, domain_name):
    &#34;&#34;&#34;Deletes the domain from the commcell.

        Args:
            domain_name     (str)   --  name of the domain to remove from the commcell

        Raises:
            SDKException:
                if type of the domain name argument is not string

                if failed to delete domain

                if response is empty

                if response is not success

                if no domain exists with the given name

    &#34;&#34;&#34;

    if not isinstance(domain_name, str):
        raise SDKException(&#39;Domain&#39;, &#39;101&#39;)
    else:
        domain_name = domain_name.lower()

        if self.has_domain(domain_name):
            domain_id = str(self._domains[domain_name][&#34;shortName&#34;][&#34;id&#34;])
            delete_domain = self._services[&#39;DELETE_DOMAIN_CONTROLER&#39;] % (domain_id)

            flag, response = self._cvpysdk_object.make_request(&#39;DELETE&#39;, delete_domain)

            if flag:
                if response.json() and &#39;errorCode&#39; in response.json():
                    error_code = response.json()[&#34;errorCode&#34;]

                    if error_code == 0:
                        # initialize the domain again
                        # so the domains object has all the domains
                        self.refresh()
                    else:
                        o_str = (&#39;Failed to delete domain with error code: &#34;{0}&#34;&#39;
                                 &#39;\nPlease check the documentation for &#39;
                                 &#39;more details on the error&#39;)
                        raise SDKException(
                            &#39;Domain&#39;, &#39;102&#39;, o_str.format(error_code)
                        )
                else:
                    raise SDKException(&#39;Response&#39;, &#39;102&#39;)
            else:
                response_string = self._update_response_(response.text)
                raise SDKException(&#39;Response&#39;, &#39;101&#39;, response_string)
        else:
            raise SDKException(
                &#39;Domain&#39;, &#39;102&#39;, &#39;No domain exists with name: {0}&#39;.format(domain_name)
            )</code></pre>
</details>
</dd>
<dt id="cvpysdk.domains.Domains.get"><code class="name flex">
<span>def <span class="ident">get</span></span>(<span>self, domain_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Returns a domain object of the specified domain name.</p>
<h2 id="args">Args</h2>
<p>domain_name (str)
&ndash;
name of the domain</p>
<h2 id="returns">Returns</h2>
<p>object of the domain</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>                    if domain doesn't exist with specified name

                    if type of the domain name argument is not string
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/domains.py#L223-L248" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def get(self, domain_name):
    &#34;&#34;&#34;Returns a domain object of the specified domain name.

        Args:
            domain_name (str)  --  name of the domain

        Returns:
            object of the domain

        Raises:
            SDKException:

                                    if domain doesn&#39;t exist with specified name

                                    if type of the domain name argument is not string

    &#34;&#34;&#34;
    if not isinstance(domain_name, str):
        raise SDKException(&#39;Domain&#39;, &#39;101&#39;)
    if not self.has_domain(domain_name):
        raise SDKException(
            &#39;Domain&#39;, &#39;102&#39;, &#34;Domain {0} doesn&#39;t exists on this commcell.&#34;.format(
                domain_name)
        )

    return Domain(self._commcell_object, domain_name, self._domains[domain_name.lower()][&#39;shortName&#39;][&#39;id&#39;])</code></pre>
</details>
</dd>
<dt id="cvpysdk.domains.Domains.has_domain"><code class="name flex">
<span>def <span class="ident">has_domain</span></span>(<span>self, domain_name)</span>
</code></dt>
<dd>
<div class="desc"><p>Checks if a domain exists in the commcell with the input domain name.</p>
<h2 id="args">Args</h2>
<p>domain_name
(str)
&ndash;
name of the domain</p>
<h2 id="returns">Returns</h2>
<p>bool
-
boolean output whether the domain exists in the commcell or not</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if type of the domain name argument is not string</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/domains.py#L204-L221" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def has_domain(self, domain_name):
    &#34;&#34;&#34;Checks if a domain exists in the commcell with the input domain name.

        Args:
            domain_name     (str)   --  name of the domain

        Returns:
            bool    -   boolean output whether the domain exists in the commcell or not

        Raises:
            SDKException:
                if type of the domain name argument is not string

    &#34;&#34;&#34;
    if not isinstance(domain_name, str):
        raise SDKException(&#39;Domain&#39;, &#39;101&#39;)

    return self._domains and domain_name.lower() in self._domains</code></pre>
</details>
</dd>
<dt id="cvpysdk.domains.Domains.refresh"><code class="name flex">
<span>def <span class="ident">refresh</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Refresh the domains associated with the Commcell.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/domains.py#L306-L308" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def refresh(self):
    &#34;&#34;&#34;Refresh the domains associated with the Commcell.&#34;&#34;&#34;
    self._domains = self._get_domains()</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk" href="index.html">cvpysdk</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.domains.Domain" href="#cvpysdk.domains.Domain">Domain</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.domains.Domain.domain_id" href="#cvpysdk.domains.Domain.domain_id">domain_id</a></code></li>
<li><code><a title="cvpysdk.domains.Domain.domain_name" href="#cvpysdk.domains.Domain.domain_name">domain_name</a></code></li>
<li><code><a title="cvpysdk.domains.Domain.refresh" href="#cvpysdk.domains.Domain.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.domains.Domain.set_domain_status" href="#cvpysdk.domains.Domain.set_domain_status">set_domain_status</a></code></li>
<li><code><a title="cvpysdk.domains.Domain.set_properties" href="#cvpysdk.domains.Domain.set_properties">set_properties</a></code></li>
<li><code><a title="cvpysdk.domains.Domain.set_sso" href="#cvpysdk.domains.Domain.set_sso">set_sso</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="cvpysdk.domains.Domains" href="#cvpysdk.domains.Domains">Domains</a></code></h4>
<ul class="two-column">
<li><code><a title="cvpysdk.domains.Domains.add" href="#cvpysdk.domains.Domains.add">add</a></code></li>
<li><code><a title="cvpysdk.domains.Domains.all_domains" href="#cvpysdk.domains.Domains.all_domains">all_domains</a></code></li>
<li><code><a title="cvpysdk.domains.Domains.delete" href="#cvpysdk.domains.Domains.delete">delete</a></code></li>
<li><code><a title="cvpysdk.domains.Domains.get" href="#cvpysdk.domains.Domains.get">get</a></code></li>
<li><code><a title="cvpysdk.domains.Domains.has_domain" href="#cvpysdk.domains.Domains.has_domain">has_domain</a></code></li>
<li><code><a title="cvpysdk.domains.Domains.refresh" href="#cvpysdk.domains.Domains.refresh">refresh</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>