<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.10.0" />
<title>cvpysdk.instances.cloudapps.cloud_storage_instance API documentation</title>
<meta name="description" content="File for operating on a cloud storage instance …" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em;background-color:#f8f9fa}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}#lunr-search{width:100%;font-size:1em;padding:6px 9px 5px 9px;border:1px solid silver}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#FE496A;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#FD0131}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
<link rel="shortcut icon" type="image/x-icon" href="https://commvault.github.io/cvpysdk/favicon.ico">
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>cvpysdk.instances.cloudapps.cloud_storage_instance</code></h1>
</header>
<section id="section-intro">
<p>File for operating on a cloud storage instance.</p>
<p>CloudStorageInstance is the only class defined in this file.</p>
<p>CloudStorageInstance:
Derived class from CloudAppsInstance Base class, representing a
Cloud Storage instance(S3,Azure,Oraclecloud and Openstack), and to
perform operations on that instance</p>
<h2 id="cloudstorageinstance">Cloudstorageinstance</h2>
<p>_get_instance_properties()
&ndash;
Instance class method overwritten to add cloud apps
instance properties as well</p>
<p>_generate_json()
&ndash;
Returns the JSON request to pass to the API as per
the options selected by the user</p>
<p>restore_in_place()
&ndash;
restores the files/folders specified in the
input paths list to the same location.</p>
<p>restore_out_of_place()
&ndash;
restores the files/folders specified in the input
paths list to the input client,
at the specified destination location.</p>
<p>restore_to_fs()
&ndash;
restores the files/folders specified in the input
paths list to the input fs client,
at the specified destination location.</p>
<p>_set_destination_options_json()
&ndash;
setter for cloud apps destination options in
restore JSON</p>
<p>_set_restore_options_json()
&ndash;
setter for cloudapps restore options in restore JSON</p>
<p>_set_proxy_credential_json()
&ndash;
Method to construct the proxy credentials
json for out of place restore</p>
<p>restore_using_proxy()
&ndash;
To perform restore to different cloud using
proxy passing explicit credentials of destination cloud</p>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/cloud_storage_instance.py#L1-L802" class="git-link">Browse git</a>
</summary>
<pre><code class="python"># -*- coding: utf-8 -*-

# --------------------------------------------------------------------------
# Copyright Commvault Systems, Inc.
#
# Licensed under the Apache License, Version 2.0 (the &#34;License&#34;);
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an &#34;AS IS&#34; BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# --------------------------------------------------------------------------

&#34;&#34;&#34; File for operating on a cloud storage instance.

CloudStorageInstance is the only class defined in this file.

CloudStorageInstance:   Derived class from CloudAppsInstance Base class, representing a
                        Cloud Storage instance(S3,Azure,Oraclecloud and Openstack), and to
                        perform operations on that instance

CloudStorageInstance:

    _get_instance_properties()              --  Instance class method overwritten to add cloud apps
    instance properties as well

    _generate_json()                        --  Returns the JSON request to pass to the API as per
    the options selected by the user

    restore_in_place()                      --  restores the files/folders specified in the
    input paths list to the same location.

    restore_out_of_place()                  --  restores the files/folders specified in the input
                                                paths list to the input client,
                                                at the specified destination location.

    restore_to_fs()                         --  restores the files/folders specified in the input
                                                paths list to the input fs client,
                                                at the specified destination location.

    _set_destination_options_json()         --  setter for cloud apps destination options in
    restore JSON

    _set_restore_options_json()             --  setter for cloudapps restore options in restore JSON

    _set_proxy_credential_json()            --  Method to construct the proxy credentials
                                                json for out of place restore

    restore_using_proxy()                   --  To perform restore to different cloud using
                                                proxy passing explicit credentials of destination cloud

&#34;&#34;&#34;
from cvpysdk.instances.cainstance import CloudAppsInstance
from cvpysdk.client import Client
from cvpysdk.exception import SDKException


class CloudStorageInstance(CloudAppsInstance):
    &#34;&#34;&#34;Class for representing an Instance of the cloud storage instance type.&#34;&#34;&#34;

    def __init__(self, agent_object, instance_name, instance_id=None):
        &#34;&#34;&#34;Initializes the object of the CloudStorageInstance class.

            Args:
                agent_object    (object)  --  instance of the Agent class

                instance_name   (str)     --  name of the instance

                instance_id     (str)     --  id of the instance
                    default: None

            Returns:
                object - instance of the Instance class

        &#34;&#34;&#34;
        # Common Properties
        self._ca_instance_type = None
        self._access_node = None

        # Google Cloud Properties
        self._google_host_url = None
        self._google_access_key = None
        self._google_secret_key = None

        self._host_url = None
        self._access_keyid = None
        self._secret_accesskey = None
        self._account_name = None
        self._access_key = None
        self._server_name = None
        self._username = None
        self._endpointurl = None

        self._set_cloud_destination_options_json = None
        self._set_cloud_restore_options_json = None

        super(
            CloudStorageInstance,
            self).__init__(
            agent_object,
            instance_name,
            instance_id)

    def _get_instance_properties(self):
        &#34;&#34;&#34;Gets the properties of this instance&#34;&#34;&#34;
        super(CloudStorageInstance, self)._get_instance_properties()

        if &#39;cloudAppsInstance&#39; in self._properties:
            cloud_apps_instance = self._properties.get(&#39;cloudAppsInstance&#39;, {})
            self._ca_instance_type = cloud_apps_instance[&#39;instanceType&#39;]

            if &#39;s3Instance&#39; in cloud_apps_instance:
                s3instance = cloud_apps_instance.get(&#39;s3Instance&#39;, {})

                self._host_url = s3instance.get(&#39;hostURL&#39;, &#39;&#39;)
                self._access_keyid = s3instance.get(&#39;accessKeyId&#39;, &#39;&#39;)

            if &#39;azureInstance&#39; in cloud_apps_instance:
                azureinstance = cloud_apps_instance.get(&#39;azureInstance&#39;, {})

                self._host_url = azureinstance.get(&#39;hostURL&#39;, &#39;&#39;)
                self._account_name = azureinstance.get(&#39;accountName&#39;, &#39;&#39;)
                self._access_key = azureinstance.get(&#39;accessKey&#39;, &#39;&#39;)

            if &#39;openStackInstance&#39; in cloud_apps_instance:
                openstackinstance = cloud_apps_instance.get(&#39;openStackInstance&#39;, {})

                self._server_name = openstackinstance.get(&#39;serverName&#39;, &#39;&#39;)
                self._username = openstackinstance.get(&#39;credentials&#39;, {}).get(&#39;userName&#39;, &#39;&#39;)

            if &#39;oraCloudInstance&#39; in cloud_apps_instance:
                oraclecloudinstance = cloud_apps_instance.get(&#39;oraCloudInstance&#39;)

                self._endpointurl = oraclecloudinstance.get(&#39;endpointURL&#39;, &#39;&#39;)
                self._username = oraclecloudinstance.get(&#39;user&#39;, {}).get(&#39;userName&#39;, &#39;&#39;)

            # Google Cloud Instance porperties
            if &#39;googleCloudInstance&#39; in cloud_apps_instance:
                googlecloudinstance = cloud_apps_instance.get(&#39;googleCloudInstance&#39;, {})

                self._google_host_url = googlecloudinstance.get(&#39;serverName&#39;, &#39;&#39;)
                self._google_access_key = googlecloudinstance.get(&#39;credentials&#39;, {}).get(&#39;userName&#39;, &#39;&#39;)

            # Ali Cloud
            if &#39;alibabaInstance&#39; in cloud_apps_instance:
                alibabainstance = cloud_apps_instance.get(&#39;alibabaInstance&#39;, {})

                self._host_url = alibabainstance.get(&#39;hostURL&#39;, &#39;&#39;)
                self._access_key = alibabainstance.get(&#39;accessKey&#39;, &#39;&#39;)

            # IBM Cloud
            if &#39;ibmCosInstance&#39; in cloud_apps_instance:
                ibminstance = cloud_apps_instance.get(&#39;ibmCosInstance&#39;, {})

                self._host_url = ibminstance.get(&#39;hostURL&#39;, &#39;&#39;)
                self._access_key = ibminstance.get(&#39;credentials&#39;, {}).get(&#39;username&#39;, &#39;&#39;)

            if &#39;generalCloudProperties&#39; in cloud_apps_instance:
                self._access_node = cloud_apps_instance.get(
                    &#39;generalCloudProperties&#39;, {}).get(
                    &#39;proxyServers&#39;, [{}])[0].get(&#39;clientName&#39;, cloud_apps_instance.get(
                        &#39;generalCloudProperties&#39;, {}).get(&#39;memberServers&#39;, [{}])[
                        0].get(&#39;client&#39;, {}).get(&#39;clientName&#39;))

    @property
    def google_host_url(self):
        &#34;&#34;&#34;
        Returns google cloud URL as read only attribute

        Returns:
            (str)     -     string representing host URL of goole cloud
        &#34;&#34;&#34;
        return self._google_host_url

    @property
    def google_access_key(self):
        &#34;&#34;&#34;
        Returns google cloud account access key as read only attribute

        Returns:
            (str)     -     string representing google cloud account access key
        &#34;&#34;&#34;
        return self._google_access_key

    @property
    def ca_instance_type(self):
        &#34;&#34;&#34;Returns the CloudApps instance type as a read-only attribute.&#34;&#34;&#34;
        return self._ca_instance_type

    @property
    def host_url(self):
        &#34;&#34;&#34;Returns the host URL property as a read-only attribute.&#34;&#34;&#34;
        return self._host_url

    @property
    def access_key(self):
        &#34;&#34;&#34;Returns the access key property as a read-only attribute.&#34;&#34;&#34;
        return self._access_key

    @property
    def account_name(self):
        &#34;&#34;&#34;Returns the account name as a read-only attribute.&#34;&#34;&#34;
        return self._account_name

    @property
    def access_keyid(self):
        &#34;&#34;&#34;Returns the access key ID property as a read-only attribute.&#34;&#34;&#34;
        return self._access_keyid

    @property
    def server_name(self):
        &#34;&#34;&#34;Returns the server name property as a read-only attribute.&#34;&#34;&#34;
        return self._server_name

    @property
    def username(self):
        &#34;&#34;&#34;Returns the username property as a read-only attribute.&#34;&#34;&#34;
        return self._username

    @property
    def endpointurl(self):
        &#34;&#34;&#34;Returns the endpoint URL property as a read-only attribute.&#34;&#34;&#34;
        return self._endpointurl

    @property
    def client_name(self):
        &#34;&#34;&#34;
        Returns client name of this instance

            Returns:
                (str) - client name as registered in the commcell

        &#34;&#34;&#34;
        return self._properties.get(&#39;instance&#39;, {}).get(&#39;clientName&#39;)

    @property
    def access_node(self):
        &#34;&#34;&#34;Returns the access node of this instance as a read-only attribute.&#34;&#34;&#34;
        return self._access_node

    def _generate_json(self, **kwargs):
        &#34;&#34;&#34;Returns the JSON request to pass to the API as per the options selected by the user.

            Args:
                kwargs   (list)  --  list of options need to be set for restore

            Returns:
                dict - JSON request to pass to the API

        &#34;&#34;&#34;
        cloud_restore_json = super(
            CloudStorageInstance,
            self)._restore_json(
            **kwargs)
        restore_options = {}
        if kwargs.get(&#34;restore_options&#34;):

            restore_options = kwargs[&#34;restore_options&#34;]
            for key in kwargs:

                if not key == &#34;restore_options&#34;:
                    restore_options[key] = kwargs[key]

        else:
            restore_options.update(kwargs)

        self._set_destination_options_json(restore_options)
        self._set_restore_options_json(restore_options)
        self._set_common_options_json(restore_options)

        cloud_restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;destination&#34;] = self._set_cloud_destination_options_json
        cloud_restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;cloudAppsRestoreOptions&#34;] = self._set_cloud_restore_options_json
        cloud_restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;commonOptions&#34;] = self._common_options_json
        cloud_restore_json[&#34;taskInfo&#34;][&#34;associations&#34;][0][&#34;backupsetId&#34;] = int(self._agent_object.backupsets.get(
            &#39;defaultBackupSet&#39;).backupset_id)

        return cloud_restore_json

    def restore_in_place(
            self,
            paths,
            overwrite=True,
            copy_precedence=None,
            no_of_streams=2):
        &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the same location.

            Args:
                paths                   (list)  --  list of full paths of files/folders to restore

                overwrite               (bool)  --  unconditional overwrite files during restore
                    default: True

                copy_precedence         (int)   --  copy precedence value of storage policy copy
                    default: None

                no_of_streams           (int)   --  number of streams for restore
                                                    default : 2

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;

        if not (isinstance(paths, list) and
                isinstance(overwrite, bool)):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        request_json = self._generate_json(
            paths=paths,
            destination_client=self.client_name,
            destination_instance_name=self.instance_name,
            overwrite=overwrite,
            in_place=True,
            copy_precedence=copy_precedence,
            restore_To_FileSystem=False,
            no_of_streams=no_of_streams)

        return self._process_restore_response(request_json)

    def restore_out_of_place(
            self,
            paths,
            destination_client,
            destination_instance_name,
            destination_path,
            overwrite=True,
            copy_precedence=None,
            no_of_streams=2,
            **kwargs):
        &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the input client,
            at the specified destination location.

            Args:
                paths                    (list)  --  list of full paths of files/folders to restore

                destination_client       (str)   --  name of the client to which the files
                    are to be restored.

                destination_instance_name(str)   --  name of the instance to which the files
                    are to be restored.

                destination_path         (str)   --  location where the files are to be restored
                    in the destination instance.

                overwrite                (bool)  --  unconditional overwrite files during restore
                    default: True

                copy_precedence          (int)   --  copy precedence value of storage policy copy
                    default: None

                no_of_streams           (int)   --  number of streams for restore
                                                    default : 2

               kwargs                  (dict)  -- dict of keyword arguments as follows

                    from_time           (str)   --  time to retore the contents after
                        format: YYYY-MM-DD HH:MM:SS
                        default: None

                    to_time             (str)   --  time to retore the contents before
                        format: YYYY-MM-DD HH:MM:SS
                        default: None

                    no_image            (bool)  --  restore deleted items
                        default: False

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if client is not a string or Client object

                    if destination_path is not a string

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;

        if not ((isinstance(destination_client, str) or
                 isinstance(destination_client, Client)) and
                isinstance(destination_instance_name, str) and
                isinstance(destination_path, str) and
                isinstance(paths, list) and
                isinstance(overwrite, bool)):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        request_json = self._generate_json(
            paths=paths,
            destination_client=destination_client,
            destination_instance_name=destination_instance_name,
            destination_path=destination_path,
            overwrite=overwrite,
            in_place=False,
            copy_precedence=copy_precedence,
            no_of_streams=no_of_streams,
            restore_To_FileSystem=False,
            **kwargs)

        return self._process_restore_response(request_json)

    def restore_to_fs(
            self,
            paths,
            destination_path,
            destination_client=None,
            overwrite=True,
            copy_precedence=None,
            no_of_streams=2):
        &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the input client,
            at the specified destination location.

            Args:
                paths                   (list)  --  list of full paths of files/folders to restore

                destination_path        (str)   --  location where the files are to be restored
                    in the destination instance.

                destination_client      (str)   --  name of the fs client to which the files
                    are to be restored.
                    default: None for restores to backup or proxy client.

                overwrite               (bool)  --  unconditional overwrite files during restore
                    default: True

                copy_precedence         (int)   --  copy precedence value of storage policy copy
                    default: None

                no_of_streams           (int)   --  number of streams for restore
                                                    default : 2

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if client is not a string or client object

                    if destination_path is not a string

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;

        if not ((isinstance(destination_client, str) or
                 isinstance(destination_client, Client)) and
                isinstance(destination_path, str) and
                isinstance(paths, list) and
                isinstance(overwrite, bool)):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        destination_appTypeId = int(
            self._commcell_object.clients.get(destination_client).agents.get(&#39;file system&#39;).agent_id)

        request_json = self._generate_json(
            paths=paths,
            destination_path=destination_path,
            destination_client=destination_client,
            overwrite=overwrite,
            in_place=False,
            copy_precedence=copy_precedence,
            restore_To_FileSystem=True,
            no_of_streams=no_of_streams,
            destination_appTypeId=destination_appTypeId)

        return self._process_restore_response(request_json)

    def _set_destination_options_json(self, value):
        &#34;&#34;&#34;setter for cloud apps destination options in restore JSON

        Args:
            value    (dict)    --    options needed to set the cloud apps destination parameters

        Example:
            value = {
                &#34;destination_proxy&#34;:False,
                &#34;restore_To_FileSystem&#34; : False
                &#34;in_place&#34; : False
                &#34;destination_path&#34; : &#34;/test/test1&#34;
                &#34;destination_client&#34; : &#34;test_client&#34;
                &#34;destination_instance_name&#34; : &#34;test_instance&#34;
            }

        &#34;&#34;&#34;
        proxy_option = value.get(&#39;destination_proxy&#39;, False)
        if value.get(&#34;restore_To_FileSystem&#34;):

            self._set_cloud_destination_options_json = {
                &#34;isLegalHold&#34;: False,
                &#34;noOfStreams&#34;: value.get(&#39;no_of_streams&#39;, 2),
                &#34;inPlace&#34;: value.get(&#34;in_place&#34;, &#34;&#34;),
                &#34;destPath&#34;: [value.get(&#34;destination_path&#34;, &#34;&#34;)],
                &#34;destClient&#34;: {
                    &#34;clientName&#34;: value.get(&#34;destination_client&#34;, &#34;&#34;)
                }
            }

        else:

            if value.get(&#34;destination_client&#34;):
                dest_client = value.get(&#34;destination_client&#34;, &#34;&#34;)

            else:
                dest_client = self._agent_object._client_object.client_name

            if value.get(&#34;destination_instance_name&#34;):
                dest_instance = value.get(&#34;destination_instance_name&#34;)

            else:
                dest_instance = self.instance_name

            regular_instance_restore_json = {
                &#34;isLegalHold&#34;: False,
                &#34;noOfStreams&#34;: value.get(&#39;no_of_streams&#39;, 2),
                &#34;inPlace&#34;: value.get(&#34;in_place&#34;),
                &#34;destPath&#34;: [value.get(&#34;destination_path&#34;)],
                &#34;destClient&#34;: {
                    &#34;clientName&#34;: value.get(&#34;destination_client&#34;)
                }
            }
            if not proxy_option:
                destination_client_object = self._commcell_object.clients.get(dest_client)
                destination_agent_object = destination_client_object.agents.get(&#39;cloud apps&#39;)
                destination_instance_object = destination_agent_object.instances.get(dest_instance)
                destination_instance_details = {
                    &#34;destinationInstance&#34;: {
                        &#34;instanceName&#34;: value.get(&#34;destination_instance_name&#34;),
                        &#34;instanceId&#34;: int(destination_instance_object.instance_id)}}
                regular_instance_restore_json.update(
                    destination_instance_details)
            self._set_cloud_destination_options_json = regular_instance_restore_json

    def _set_restore_options_json(self, value):
        &#34;&#34;&#34;setter for cloudapps restore options in restore JSON

        Args:
            value    (dict)    --    options needed to set the cloud apps restore parameters

        Example:
            value = {
                &#34;restore_To_FileSystem&#34;: True
                }

        &#34;&#34;&#34;

        self._set_cloud_restore_options_json = {
            &#34;instanceType&#34;: int(self.ca_instance_type),
            &#34;cloudStorageRestoreOptions&#34;: {
                &#34;restoreToFileSystem&#34;: value.get(&#34;restore_To_FileSystem&#34;),
                &#34;overrideCloudLogin&#34;: False,
                &#34;restoreDestination&#34;: {
                    &#34;instanceType&#34;: int(self.ca_instance_type)
                }
            }
        }

    def _set_common_options_json(self, value):
        &#34;&#34;&#34;
        Setter for the Common options in restore JSON

            Args:
                value   (dict)  --  dict of common options
                                    for restore json

        &#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        self._common_options_json = {
            &#34;overwriteFiles&#34;: True,
            &#34;unconditionalOverwrite&#34;: value.get(&#34;overwrite&#34;, False),
            &#34;stripLevelType&#34;: 1
        }

    def _set_proxy_credential_json(self, destination_cloud):
        &#34;&#34;&#34;
        Method to construct the proxy credentials json for out of place restore

        Args:
            destination_cloud        (dict(dict))  --     dict of dict representing cross cloud credentials

            Sample dict(dict) :

            destination_cloud = {
                                    &#39;google_cloud&#39;: {
                                                        &#39;google_host_url&#39;:&#39;storage.googleapis.com&#39;,
                                                        &#39;google_access_key&#39;:&#39;xxxxxx&#39;,
                                                        &#39;google_secret_key&#39;:&#39;yyyyyy&#39;
                                                    }
                                }

            destination_cloud = {
                                    &#39;amazon_s3&#39;:    {
                                                        &#39;s3_host_url&#39;:&#39;s3.amazonaws.com&#39;,
                                                        &#39;s3_access_key&#39;:&#39;xxxxxx&#39;,
                                                        &#39;s3_secret_key&#39;:&#39;yyyyyy&#39;
                                                    }
                                }
            destination_cloud = {
                                    &#39;azure_blob&#39;:   {
                                                        &#39;azure_host_url&#39;:&#39;blob.core.windows.net&#39;,
                                                        &#39;azure_account_name&#39;:&#39;xxxxxx&#39;,
                                                        &#39;azure_access_key&#39;:&#39;yyyyyy&#39;
                                                    }
                                }

        &#34;&#34;&#34;

        if &#39;amazon_s3&#39; in destination_cloud:
            self._proxy_credential_json = {
                &#34;instanceType&#34;: 5,
                &#34;s3Instance&#34;: {
                    &#34;hostURL&#34;: destination_cloud.get(&#39;amazon_s3&#39;, {}).get(&#39;s3_host_url&#39;, &#39;s3.amazonaws.com&#39;),
                    &#34;accessKeyId&#34;: destination_cloud.get(&#39;amazon_s3&#39;, {}).get(&#39;s3_access_key&#39;, &#34;&#34;),
                    &#34;secretAccessKey&#34;: destination_cloud.get(&#39;amazon_s3&#39;, {}).get(&#39;s3_secret_key&#39;, &#34;&#34;)
                }
            }

        elif &#39;google_cloud&#39; in destination_cloud:
            self._proxy_credential_json = {
                &#34;instanceType&#34;: 20,
                &#34;googleCloudInstance&#34;: {
                    &#34;serverName&#34;: destination_cloud.get(&#39;google_cloud&#39;, {}).get(&#39;google_host_url&#39;,
                                                                                &#39;storage.googleapis.com&#39;),
                    &#34;credentials&#34;: {
                        &#34;userName&#34;: destination_cloud.get(&#39;google_cloud&#39;, {}).get(&#39;google_access_key&#39;, &#34;&#34;),
                        &#34;password&#34;: destination_cloud.get(&#39;google_cloud&#39;, {}).get(&#39;google_secret_key&#39;, &#34;&#34;)
                    }
                }
            }

        elif &#39;azure_blob&#39; in destination_cloud:
            self._proxy_credential_json = {
                &#34;instanceType&#34;: 6,
                &#34;azureInstance&#34;: {
                    &#34;hostURL&#34;: destination_cloud.get(&#39;azure_blob&#39;, {}).get(&#39;azure_host_url&#39;, &#39;blob.core.windows.net&#39;),
                    &#34;accountName&#34;: destination_cloud.get(&#39;azure_blob&#39;, {}).get(&#39;azure_account_name&#39;, &#34;&#34;),
                    &#34;accessKey&#34;: destination_cloud.get(&#39;azure_blob&#39;, {}).get(&#39;azure_access_key&#39;, &#34;&#34;)
                }
            }

    def restore_using_proxy(self,
                            paths,
                            destination_client_proxy,
                            destination_path,
                            overwrite=True,
                            copy_precedence=None,
                            destination_cloud=None):
        &#34;&#34;&#34;
        To perform restore to different cloud using
        proxy passing explicit credentials of destination cloud

        Args:
            destination_client_proxy (str)          --  name of proxy machine having cloud connector package

            paths                    (list)         --  list of full paths of files/folders to restore

            destination_path         (str)          --  location where the files are to be restored
                                                        in the destination instance.

            overwrite                (bool)         --  unconditional overwrite files during restore
                                                        default: True

            copy_precedence          (int)          --  copy precedence value of storage policy copy
                                                        default: None


            destination_cloud        (dict(dict))  --     dict of dict representing cross cloud credentials

            Sample dict(dict) :

            destination_cloud = {
                                    &#39;google_cloud&#39;: {
                                                        &#39;google_host_url&#39;:&#39;storage.googleapis.com&#39;,
                                                        &#39;google_access_key&#39;:&#39;xxxxxx&#39;,
                                                        &#39;google_secret_key&#39;:&#39;yyyyyy&#39;
                                                    }
                                }

            destination_cloud = {
                                    &#39;amazon_s3&#39;:    {
                                                        &#39;s3_host_url&#39;:&#39;s3.amazonaws.com&#39;,
                                                        &#39;s3_access_key&#39;:&#39;xxxxxx&#39;,
                                                        &#39;s3_secret_key&#39;:&#39;yyyyyy&#39;
                                                    }
                                }
            destination_cloud = {
                                    &#39;azure_blob&#39;:   {
                                                        &#39;azure_host_url&#39;:&#39;blob.core.windows.net&#39;,
                                                        &#39;azure_account_name&#39;:&#39;xxxxxx&#39;,
                                                        &#39;azure_access_key&#39;:&#39;yyyyyy&#39;
                                                    }
                                }


        Returns:
                object - instance of the Job class for this restore job

        Raises:
            SDKException:

                    if destination cloud credentials empty

                    if destination cloud has more than one vendor details

                    if unsupported destination cloud for restore is chosen

                    if client is not a string or Client object

                    if destination_path is not a string

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        # Check if destination cloud credentials are empty
        if destination_cloud is None:
            raise SDKException(
                &#39;Instance&#39;,
                &#39;102&#39;,
                &#39;Destination Cloud Credentials empty&#39;)

        if len(destination_cloud) &gt; 1:
            raise SDKException(
                &#39;Instance&#39;, &#39;102&#39;, &#39;only one cloud vendor details can&#39;
                                   &#39;be passed.Multiple entries not allowed&#39;)

        cloud_vendors = [&#34;google_cloud&#34;, &#34;amazon_s3&#34;, &#34;azure_blob&#34;]
        # Check if destination cloud falls within supported cloud vendors
        cloud_vendors = [&#34;google_cloud&#34;, &#34;amazon_s3&#34;, &#34;azure_blob&#34;]
        # Check if destination cloud falls within supported cloud vendors
        dict_keys = list(destination_cloud.keys())
        if dict_keys[0] not in cloud_vendors:
            raise SDKException(
                &#39;Instance&#39;,
                &#39;102&#39;,
                &#39;Unsupported destination cloud for restore&#39;)

        if not ((isinstance(destination_client_proxy, str) or
                 isinstance(destination_client_proxy, Client)) and
                isinstance(destination_path, str) and
                isinstance(paths, list) and
                isinstance(overwrite, bool)):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        request_json = self._generate_json(
            paths=paths,
            destination_proxy=True,
            destination_client=destination_client_proxy,
            destination_instance_name=None,
            destination_path=destination_path,
            overwrite=overwrite,
            in_place=False,
            copy_precedence=copy_precedence,
            restore_To_FileSystem=False)
        self._set_proxy_credential_json(destination_cloud)
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;][&#34;cloudStorageRestoreOptions&#34;][
            &#34;restoreDestination&#34;] = self._proxy_credential_json
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;cloudAppsRestoreOptions&#34;][&#34;cloudStorageRestoreOptions&#34;][&#34;overrideCloudLogin&#34;] = True
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;browseOption&#34;][&#34;backupset&#34;].update({&#34;backupsetName&#34;: &#34;defaultBackupSet&#34;})
        request_json[&#34;taskInfo&#34;][&#34;associations&#34;][0][&#34;backupsetName&#34;] = &#34;defaultBackupSet&#34;

        return self._process_restore_response(request_json)</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance"><code class="flex name class">
<span>class <span class="ident">CloudStorageInstance</span></span>
<span>(</span><span>agent_object, instance_name, instance_id=None)</span>
</code></dt>
<dd>
<div class="desc"><p>Class for representing an Instance of the cloud storage instance type.</p>
<p>Initializes the object of the CloudStorageInstance class.</p>
<h2 id="args">Args</h2>
<p>agent_object
(object)
&ndash;
instance of the Agent class</p>
<p>instance_name
(str)
&ndash;
name of the instance</p>
<p>instance_id
(str)
&ndash;
id of the instance
default: None</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Instance class</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/cloud_storage_instance.py#L63-L802" class="git-link">Browse git</a>
</summary>
<pre><code class="python">class CloudStorageInstance(CloudAppsInstance):
    &#34;&#34;&#34;Class for representing an Instance of the cloud storage instance type.&#34;&#34;&#34;

    def __init__(self, agent_object, instance_name, instance_id=None):
        &#34;&#34;&#34;Initializes the object of the CloudStorageInstance class.

            Args:
                agent_object    (object)  --  instance of the Agent class

                instance_name   (str)     --  name of the instance

                instance_id     (str)     --  id of the instance
                    default: None

            Returns:
                object - instance of the Instance class

        &#34;&#34;&#34;
        # Common Properties
        self._ca_instance_type = None
        self._access_node = None

        # Google Cloud Properties
        self._google_host_url = None
        self._google_access_key = None
        self._google_secret_key = None

        self._host_url = None
        self._access_keyid = None
        self._secret_accesskey = None
        self._account_name = None
        self._access_key = None
        self._server_name = None
        self._username = None
        self._endpointurl = None

        self._set_cloud_destination_options_json = None
        self._set_cloud_restore_options_json = None

        super(
            CloudStorageInstance,
            self).__init__(
            agent_object,
            instance_name,
            instance_id)

    def _get_instance_properties(self):
        &#34;&#34;&#34;Gets the properties of this instance&#34;&#34;&#34;
        super(CloudStorageInstance, self)._get_instance_properties()

        if &#39;cloudAppsInstance&#39; in self._properties:
            cloud_apps_instance = self._properties.get(&#39;cloudAppsInstance&#39;, {})
            self._ca_instance_type = cloud_apps_instance[&#39;instanceType&#39;]

            if &#39;s3Instance&#39; in cloud_apps_instance:
                s3instance = cloud_apps_instance.get(&#39;s3Instance&#39;, {})

                self._host_url = s3instance.get(&#39;hostURL&#39;, &#39;&#39;)
                self._access_keyid = s3instance.get(&#39;accessKeyId&#39;, &#39;&#39;)

            if &#39;azureInstance&#39; in cloud_apps_instance:
                azureinstance = cloud_apps_instance.get(&#39;azureInstance&#39;, {})

                self._host_url = azureinstance.get(&#39;hostURL&#39;, &#39;&#39;)
                self._account_name = azureinstance.get(&#39;accountName&#39;, &#39;&#39;)
                self._access_key = azureinstance.get(&#39;accessKey&#39;, &#39;&#39;)

            if &#39;openStackInstance&#39; in cloud_apps_instance:
                openstackinstance = cloud_apps_instance.get(&#39;openStackInstance&#39;, {})

                self._server_name = openstackinstance.get(&#39;serverName&#39;, &#39;&#39;)
                self._username = openstackinstance.get(&#39;credentials&#39;, {}).get(&#39;userName&#39;, &#39;&#39;)

            if &#39;oraCloudInstance&#39; in cloud_apps_instance:
                oraclecloudinstance = cloud_apps_instance.get(&#39;oraCloudInstance&#39;)

                self._endpointurl = oraclecloudinstance.get(&#39;endpointURL&#39;, &#39;&#39;)
                self._username = oraclecloudinstance.get(&#39;user&#39;, {}).get(&#39;userName&#39;, &#39;&#39;)

            # Google Cloud Instance porperties
            if &#39;googleCloudInstance&#39; in cloud_apps_instance:
                googlecloudinstance = cloud_apps_instance.get(&#39;googleCloudInstance&#39;, {})

                self._google_host_url = googlecloudinstance.get(&#39;serverName&#39;, &#39;&#39;)
                self._google_access_key = googlecloudinstance.get(&#39;credentials&#39;, {}).get(&#39;userName&#39;, &#39;&#39;)

            # Ali Cloud
            if &#39;alibabaInstance&#39; in cloud_apps_instance:
                alibabainstance = cloud_apps_instance.get(&#39;alibabaInstance&#39;, {})

                self._host_url = alibabainstance.get(&#39;hostURL&#39;, &#39;&#39;)
                self._access_key = alibabainstance.get(&#39;accessKey&#39;, &#39;&#39;)

            # IBM Cloud
            if &#39;ibmCosInstance&#39; in cloud_apps_instance:
                ibminstance = cloud_apps_instance.get(&#39;ibmCosInstance&#39;, {})

                self._host_url = ibminstance.get(&#39;hostURL&#39;, &#39;&#39;)
                self._access_key = ibminstance.get(&#39;credentials&#39;, {}).get(&#39;username&#39;, &#39;&#39;)

            if &#39;generalCloudProperties&#39; in cloud_apps_instance:
                self._access_node = cloud_apps_instance.get(
                    &#39;generalCloudProperties&#39;, {}).get(
                    &#39;proxyServers&#39;, [{}])[0].get(&#39;clientName&#39;, cloud_apps_instance.get(
                        &#39;generalCloudProperties&#39;, {}).get(&#39;memberServers&#39;, [{}])[
                        0].get(&#39;client&#39;, {}).get(&#39;clientName&#39;))

    @property
    def google_host_url(self):
        &#34;&#34;&#34;
        Returns google cloud URL as read only attribute

        Returns:
            (str)     -     string representing host URL of goole cloud
        &#34;&#34;&#34;
        return self._google_host_url

    @property
    def google_access_key(self):
        &#34;&#34;&#34;
        Returns google cloud account access key as read only attribute

        Returns:
            (str)     -     string representing google cloud account access key
        &#34;&#34;&#34;
        return self._google_access_key

    @property
    def ca_instance_type(self):
        &#34;&#34;&#34;Returns the CloudApps instance type as a read-only attribute.&#34;&#34;&#34;
        return self._ca_instance_type

    @property
    def host_url(self):
        &#34;&#34;&#34;Returns the host URL property as a read-only attribute.&#34;&#34;&#34;
        return self._host_url

    @property
    def access_key(self):
        &#34;&#34;&#34;Returns the access key property as a read-only attribute.&#34;&#34;&#34;
        return self._access_key

    @property
    def account_name(self):
        &#34;&#34;&#34;Returns the account name as a read-only attribute.&#34;&#34;&#34;
        return self._account_name

    @property
    def access_keyid(self):
        &#34;&#34;&#34;Returns the access key ID property as a read-only attribute.&#34;&#34;&#34;
        return self._access_keyid

    @property
    def server_name(self):
        &#34;&#34;&#34;Returns the server name property as a read-only attribute.&#34;&#34;&#34;
        return self._server_name

    @property
    def username(self):
        &#34;&#34;&#34;Returns the username property as a read-only attribute.&#34;&#34;&#34;
        return self._username

    @property
    def endpointurl(self):
        &#34;&#34;&#34;Returns the endpoint URL property as a read-only attribute.&#34;&#34;&#34;
        return self._endpointurl

    @property
    def client_name(self):
        &#34;&#34;&#34;
        Returns client name of this instance

            Returns:
                (str) - client name as registered in the commcell

        &#34;&#34;&#34;
        return self._properties.get(&#39;instance&#39;, {}).get(&#39;clientName&#39;)

    @property
    def access_node(self):
        &#34;&#34;&#34;Returns the access node of this instance as a read-only attribute.&#34;&#34;&#34;
        return self._access_node

    def _generate_json(self, **kwargs):
        &#34;&#34;&#34;Returns the JSON request to pass to the API as per the options selected by the user.

            Args:
                kwargs   (list)  --  list of options need to be set for restore

            Returns:
                dict - JSON request to pass to the API

        &#34;&#34;&#34;
        cloud_restore_json = super(
            CloudStorageInstance,
            self)._restore_json(
            **kwargs)
        restore_options = {}
        if kwargs.get(&#34;restore_options&#34;):

            restore_options = kwargs[&#34;restore_options&#34;]
            for key in kwargs:

                if not key == &#34;restore_options&#34;:
                    restore_options[key] = kwargs[key]

        else:
            restore_options.update(kwargs)

        self._set_destination_options_json(restore_options)
        self._set_restore_options_json(restore_options)
        self._set_common_options_json(restore_options)

        cloud_restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;destination&#34;] = self._set_cloud_destination_options_json
        cloud_restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;cloudAppsRestoreOptions&#34;] = self._set_cloud_restore_options_json
        cloud_restore_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;commonOptions&#34;] = self._common_options_json
        cloud_restore_json[&#34;taskInfo&#34;][&#34;associations&#34;][0][&#34;backupsetId&#34;] = int(self._agent_object.backupsets.get(
            &#39;defaultBackupSet&#39;).backupset_id)

        return cloud_restore_json

    def restore_in_place(
            self,
            paths,
            overwrite=True,
            copy_precedence=None,
            no_of_streams=2):
        &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the same location.

            Args:
                paths                   (list)  --  list of full paths of files/folders to restore

                overwrite               (bool)  --  unconditional overwrite files during restore
                    default: True

                copy_precedence         (int)   --  copy precedence value of storage policy copy
                    default: None

                no_of_streams           (int)   --  number of streams for restore
                                                    default : 2

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;

        if not (isinstance(paths, list) and
                isinstance(overwrite, bool)):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        request_json = self._generate_json(
            paths=paths,
            destination_client=self.client_name,
            destination_instance_name=self.instance_name,
            overwrite=overwrite,
            in_place=True,
            copy_precedence=copy_precedence,
            restore_To_FileSystem=False,
            no_of_streams=no_of_streams)

        return self._process_restore_response(request_json)

    def restore_out_of_place(
            self,
            paths,
            destination_client,
            destination_instance_name,
            destination_path,
            overwrite=True,
            copy_precedence=None,
            no_of_streams=2,
            **kwargs):
        &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the input client,
            at the specified destination location.

            Args:
                paths                    (list)  --  list of full paths of files/folders to restore

                destination_client       (str)   --  name of the client to which the files
                    are to be restored.

                destination_instance_name(str)   --  name of the instance to which the files
                    are to be restored.

                destination_path         (str)   --  location where the files are to be restored
                    in the destination instance.

                overwrite                (bool)  --  unconditional overwrite files during restore
                    default: True

                copy_precedence          (int)   --  copy precedence value of storage policy copy
                    default: None

                no_of_streams           (int)   --  number of streams for restore
                                                    default : 2

               kwargs                  (dict)  -- dict of keyword arguments as follows

                    from_time           (str)   --  time to retore the contents after
                        format: YYYY-MM-DD HH:MM:SS
                        default: None

                    to_time             (str)   --  time to retore the contents before
                        format: YYYY-MM-DD HH:MM:SS
                        default: None

                    no_image            (bool)  --  restore deleted items
                        default: False

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if client is not a string or Client object

                    if destination_path is not a string

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;

        if not ((isinstance(destination_client, str) or
                 isinstance(destination_client, Client)) and
                isinstance(destination_instance_name, str) and
                isinstance(destination_path, str) and
                isinstance(paths, list) and
                isinstance(overwrite, bool)):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        request_json = self._generate_json(
            paths=paths,
            destination_client=destination_client,
            destination_instance_name=destination_instance_name,
            destination_path=destination_path,
            overwrite=overwrite,
            in_place=False,
            copy_precedence=copy_precedence,
            no_of_streams=no_of_streams,
            restore_To_FileSystem=False,
            **kwargs)

        return self._process_restore_response(request_json)

    def restore_to_fs(
            self,
            paths,
            destination_path,
            destination_client=None,
            overwrite=True,
            copy_precedence=None,
            no_of_streams=2):
        &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the input client,
            at the specified destination location.

            Args:
                paths                   (list)  --  list of full paths of files/folders to restore

                destination_path        (str)   --  location where the files are to be restored
                    in the destination instance.

                destination_client      (str)   --  name of the fs client to which the files
                    are to be restored.
                    default: None for restores to backup or proxy client.

                overwrite               (bool)  --  unconditional overwrite files during restore
                    default: True

                copy_precedence         (int)   --  copy precedence value of storage policy copy
                    default: None

                no_of_streams           (int)   --  number of streams for restore
                                                    default : 2

            Returns:
                object - instance of the Job class for this restore job

            Raises:
                SDKException:
                    if client is not a string or client object

                    if destination_path is not a string

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;

        if not ((isinstance(destination_client, str) or
                 isinstance(destination_client, Client)) and
                isinstance(destination_path, str) and
                isinstance(paths, list) and
                isinstance(overwrite, bool)):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        destination_appTypeId = int(
            self._commcell_object.clients.get(destination_client).agents.get(&#39;file system&#39;).agent_id)

        request_json = self._generate_json(
            paths=paths,
            destination_path=destination_path,
            destination_client=destination_client,
            overwrite=overwrite,
            in_place=False,
            copy_precedence=copy_precedence,
            restore_To_FileSystem=True,
            no_of_streams=no_of_streams,
            destination_appTypeId=destination_appTypeId)

        return self._process_restore_response(request_json)

    def _set_destination_options_json(self, value):
        &#34;&#34;&#34;setter for cloud apps destination options in restore JSON

        Args:
            value    (dict)    --    options needed to set the cloud apps destination parameters

        Example:
            value = {
                &#34;destination_proxy&#34;:False,
                &#34;restore_To_FileSystem&#34; : False
                &#34;in_place&#34; : False
                &#34;destination_path&#34; : &#34;/test/test1&#34;
                &#34;destination_client&#34; : &#34;test_client&#34;
                &#34;destination_instance_name&#34; : &#34;test_instance&#34;
            }

        &#34;&#34;&#34;
        proxy_option = value.get(&#39;destination_proxy&#39;, False)
        if value.get(&#34;restore_To_FileSystem&#34;):

            self._set_cloud_destination_options_json = {
                &#34;isLegalHold&#34;: False,
                &#34;noOfStreams&#34;: value.get(&#39;no_of_streams&#39;, 2),
                &#34;inPlace&#34;: value.get(&#34;in_place&#34;, &#34;&#34;),
                &#34;destPath&#34;: [value.get(&#34;destination_path&#34;, &#34;&#34;)],
                &#34;destClient&#34;: {
                    &#34;clientName&#34;: value.get(&#34;destination_client&#34;, &#34;&#34;)
                }
            }

        else:

            if value.get(&#34;destination_client&#34;):
                dest_client = value.get(&#34;destination_client&#34;, &#34;&#34;)

            else:
                dest_client = self._agent_object._client_object.client_name

            if value.get(&#34;destination_instance_name&#34;):
                dest_instance = value.get(&#34;destination_instance_name&#34;)

            else:
                dest_instance = self.instance_name

            regular_instance_restore_json = {
                &#34;isLegalHold&#34;: False,
                &#34;noOfStreams&#34;: value.get(&#39;no_of_streams&#39;, 2),
                &#34;inPlace&#34;: value.get(&#34;in_place&#34;),
                &#34;destPath&#34;: [value.get(&#34;destination_path&#34;)],
                &#34;destClient&#34;: {
                    &#34;clientName&#34;: value.get(&#34;destination_client&#34;)
                }
            }
            if not proxy_option:
                destination_client_object = self._commcell_object.clients.get(dest_client)
                destination_agent_object = destination_client_object.agents.get(&#39;cloud apps&#39;)
                destination_instance_object = destination_agent_object.instances.get(dest_instance)
                destination_instance_details = {
                    &#34;destinationInstance&#34;: {
                        &#34;instanceName&#34;: value.get(&#34;destination_instance_name&#34;),
                        &#34;instanceId&#34;: int(destination_instance_object.instance_id)}}
                regular_instance_restore_json.update(
                    destination_instance_details)
            self._set_cloud_destination_options_json = regular_instance_restore_json

    def _set_restore_options_json(self, value):
        &#34;&#34;&#34;setter for cloudapps restore options in restore JSON

        Args:
            value    (dict)    --    options needed to set the cloud apps restore parameters

        Example:
            value = {
                &#34;restore_To_FileSystem&#34;: True
                }

        &#34;&#34;&#34;

        self._set_cloud_restore_options_json = {
            &#34;instanceType&#34;: int(self.ca_instance_type),
            &#34;cloudStorageRestoreOptions&#34;: {
                &#34;restoreToFileSystem&#34;: value.get(&#34;restore_To_FileSystem&#34;),
                &#34;overrideCloudLogin&#34;: False,
                &#34;restoreDestination&#34;: {
                    &#34;instanceType&#34;: int(self.ca_instance_type)
                }
            }
        }

    def _set_common_options_json(self, value):
        &#34;&#34;&#34;
        Setter for the Common options in restore JSON

            Args:
                value   (dict)  --  dict of common options
                                    for restore json

        &#34;&#34;&#34;

        if not isinstance(value, dict):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        self._common_options_json = {
            &#34;overwriteFiles&#34;: True,
            &#34;unconditionalOverwrite&#34;: value.get(&#34;overwrite&#34;, False),
            &#34;stripLevelType&#34;: 1
        }

    def _set_proxy_credential_json(self, destination_cloud):
        &#34;&#34;&#34;
        Method to construct the proxy credentials json for out of place restore

        Args:
            destination_cloud        (dict(dict))  --     dict of dict representing cross cloud credentials

            Sample dict(dict) :

            destination_cloud = {
                                    &#39;google_cloud&#39;: {
                                                        &#39;google_host_url&#39;:&#39;storage.googleapis.com&#39;,
                                                        &#39;google_access_key&#39;:&#39;xxxxxx&#39;,
                                                        &#39;google_secret_key&#39;:&#39;yyyyyy&#39;
                                                    }
                                }

            destination_cloud = {
                                    &#39;amazon_s3&#39;:    {
                                                        &#39;s3_host_url&#39;:&#39;s3.amazonaws.com&#39;,
                                                        &#39;s3_access_key&#39;:&#39;xxxxxx&#39;,
                                                        &#39;s3_secret_key&#39;:&#39;yyyyyy&#39;
                                                    }
                                }
            destination_cloud = {
                                    &#39;azure_blob&#39;:   {
                                                        &#39;azure_host_url&#39;:&#39;blob.core.windows.net&#39;,
                                                        &#39;azure_account_name&#39;:&#39;xxxxxx&#39;,
                                                        &#39;azure_access_key&#39;:&#39;yyyyyy&#39;
                                                    }
                                }

        &#34;&#34;&#34;

        if &#39;amazon_s3&#39; in destination_cloud:
            self._proxy_credential_json = {
                &#34;instanceType&#34;: 5,
                &#34;s3Instance&#34;: {
                    &#34;hostURL&#34;: destination_cloud.get(&#39;amazon_s3&#39;, {}).get(&#39;s3_host_url&#39;, &#39;s3.amazonaws.com&#39;),
                    &#34;accessKeyId&#34;: destination_cloud.get(&#39;amazon_s3&#39;, {}).get(&#39;s3_access_key&#39;, &#34;&#34;),
                    &#34;secretAccessKey&#34;: destination_cloud.get(&#39;amazon_s3&#39;, {}).get(&#39;s3_secret_key&#39;, &#34;&#34;)
                }
            }

        elif &#39;google_cloud&#39; in destination_cloud:
            self._proxy_credential_json = {
                &#34;instanceType&#34;: 20,
                &#34;googleCloudInstance&#34;: {
                    &#34;serverName&#34;: destination_cloud.get(&#39;google_cloud&#39;, {}).get(&#39;google_host_url&#39;,
                                                                                &#39;storage.googleapis.com&#39;),
                    &#34;credentials&#34;: {
                        &#34;userName&#34;: destination_cloud.get(&#39;google_cloud&#39;, {}).get(&#39;google_access_key&#39;, &#34;&#34;),
                        &#34;password&#34;: destination_cloud.get(&#39;google_cloud&#39;, {}).get(&#39;google_secret_key&#39;, &#34;&#34;)
                    }
                }
            }

        elif &#39;azure_blob&#39; in destination_cloud:
            self._proxy_credential_json = {
                &#34;instanceType&#34;: 6,
                &#34;azureInstance&#34;: {
                    &#34;hostURL&#34;: destination_cloud.get(&#39;azure_blob&#39;, {}).get(&#39;azure_host_url&#39;, &#39;blob.core.windows.net&#39;),
                    &#34;accountName&#34;: destination_cloud.get(&#39;azure_blob&#39;, {}).get(&#39;azure_account_name&#39;, &#34;&#34;),
                    &#34;accessKey&#34;: destination_cloud.get(&#39;azure_blob&#39;, {}).get(&#39;azure_access_key&#39;, &#34;&#34;)
                }
            }

    def restore_using_proxy(self,
                            paths,
                            destination_client_proxy,
                            destination_path,
                            overwrite=True,
                            copy_precedence=None,
                            destination_cloud=None):
        &#34;&#34;&#34;
        To perform restore to different cloud using
        proxy passing explicit credentials of destination cloud

        Args:
            destination_client_proxy (str)          --  name of proxy machine having cloud connector package

            paths                    (list)         --  list of full paths of files/folders to restore

            destination_path         (str)          --  location where the files are to be restored
                                                        in the destination instance.

            overwrite                (bool)         --  unconditional overwrite files during restore
                                                        default: True

            copy_precedence          (int)          --  copy precedence value of storage policy copy
                                                        default: None


            destination_cloud        (dict(dict))  --     dict of dict representing cross cloud credentials

            Sample dict(dict) :

            destination_cloud = {
                                    &#39;google_cloud&#39;: {
                                                        &#39;google_host_url&#39;:&#39;storage.googleapis.com&#39;,
                                                        &#39;google_access_key&#39;:&#39;xxxxxx&#39;,
                                                        &#39;google_secret_key&#39;:&#39;yyyyyy&#39;
                                                    }
                                }

            destination_cloud = {
                                    &#39;amazon_s3&#39;:    {
                                                        &#39;s3_host_url&#39;:&#39;s3.amazonaws.com&#39;,
                                                        &#39;s3_access_key&#39;:&#39;xxxxxx&#39;,
                                                        &#39;s3_secret_key&#39;:&#39;yyyyyy&#39;
                                                    }
                                }
            destination_cloud = {
                                    &#39;azure_blob&#39;:   {
                                                        &#39;azure_host_url&#39;:&#39;blob.core.windows.net&#39;,
                                                        &#39;azure_account_name&#39;:&#39;xxxxxx&#39;,
                                                        &#39;azure_access_key&#39;:&#39;yyyyyy&#39;
                                                    }
                                }


        Returns:
                object - instance of the Job class for this restore job

        Raises:
            SDKException:

                    if destination cloud credentials empty

                    if destination cloud has more than one vendor details

                    if unsupported destination cloud for restore is chosen

                    if client is not a string or Client object

                    if destination_path is not a string

                    if paths is not a list

                    if failed to initialize job

                    if response is empty

                    if response is not success

        &#34;&#34;&#34;
        # Check if destination cloud credentials are empty
        if destination_cloud is None:
            raise SDKException(
                &#39;Instance&#39;,
                &#39;102&#39;,
                &#39;Destination Cloud Credentials empty&#39;)

        if len(destination_cloud) &gt; 1:
            raise SDKException(
                &#39;Instance&#39;, &#39;102&#39;, &#39;only one cloud vendor details can&#39;
                                   &#39;be passed.Multiple entries not allowed&#39;)

        cloud_vendors = [&#34;google_cloud&#34;, &#34;amazon_s3&#34;, &#34;azure_blob&#34;]
        # Check if destination cloud falls within supported cloud vendors
        cloud_vendors = [&#34;google_cloud&#34;, &#34;amazon_s3&#34;, &#34;azure_blob&#34;]
        # Check if destination cloud falls within supported cloud vendors
        dict_keys = list(destination_cloud.keys())
        if dict_keys[0] not in cloud_vendors:
            raise SDKException(
                &#39;Instance&#39;,
                &#39;102&#39;,
                &#39;Unsupported destination cloud for restore&#39;)

        if not ((isinstance(destination_client_proxy, str) or
                 isinstance(destination_client_proxy, Client)) and
                isinstance(destination_path, str) and
                isinstance(paths, list) and
                isinstance(overwrite, bool)):
            raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

        request_json = self._generate_json(
            paths=paths,
            destination_proxy=True,
            destination_client=destination_client_proxy,
            destination_instance_name=None,
            destination_path=destination_path,
            overwrite=overwrite,
            in_place=False,
            copy_precedence=copy_precedence,
            restore_To_FileSystem=False)
        self._set_proxy_credential_json(destination_cloud)
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
            &#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;][&#34;cloudStorageRestoreOptions&#34;][
            &#34;restoreDestination&#34;] = self._proxy_credential_json
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;cloudAppsRestoreOptions&#34;][&#34;cloudStorageRestoreOptions&#34;][&#34;overrideCloudLogin&#34;] = True
        request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
            &#34;browseOption&#34;][&#34;backupset&#34;].update({&#34;backupsetName&#34;: &#34;defaultBackupSet&#34;})
        request_json[&#34;taskInfo&#34;][&#34;associations&#34;][0][&#34;backupsetName&#34;] = &#34;defaultBackupSet&#34;

        return self._process_restore_response(request_json)</code></pre>
</details>
<h3>Ancestors</h3>
<ul class="hlist">
<li><a title="cvpysdk.instances.cainstance.CloudAppsInstance" href="../cainstance.html#cvpysdk.instances.cainstance.CloudAppsInstance">CloudAppsInstance</a></li>
<li><a title="cvpysdk.instance.Instance" href="../../instance.html#cvpysdk.instance.Instance">Instance</a></li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.access_key"><code class="name">var <span class="ident">access_key</span></code></dt>
<dd>
<div class="desc"><p>Returns the access key property as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/cloud_storage_instance.py#L200-L203" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def access_key(self):
    &#34;&#34;&#34;Returns the access key property as a read-only attribute.&#34;&#34;&#34;
    return self._access_key</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.access_keyid"><code class="name">var <span class="ident">access_keyid</span></code></dt>
<dd>
<div class="desc"><p>Returns the access key ID property as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/cloud_storage_instance.py#L210-L213" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def access_keyid(self):
    &#34;&#34;&#34;Returns the access key ID property as a read-only attribute.&#34;&#34;&#34;
    return self._access_keyid</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.access_node"><code class="name">var <span class="ident">access_node</span></code></dt>
<dd>
<div class="desc"><p>Returns the access node of this instance as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/cloud_storage_instance.py#L241-L244" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def access_node(self):
    &#34;&#34;&#34;Returns the access node of this instance as a read-only attribute.&#34;&#34;&#34;
    return self._access_node</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.account_name"><code class="name">var <span class="ident">account_name</span></code></dt>
<dd>
<div class="desc"><p>Returns the account name as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/cloud_storage_instance.py#L205-L208" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def account_name(self):
    &#34;&#34;&#34;Returns the account name as a read-only attribute.&#34;&#34;&#34;
    return self._account_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.ca_instance_type"><code class="name">var <span class="ident">ca_instance_type</span></code></dt>
<dd>
<div class="desc"><p>Returns the CloudApps instance type as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/cloud_storage_instance.py#L190-L193" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def ca_instance_type(self):
    &#34;&#34;&#34;Returns the CloudApps instance type as a read-only attribute.&#34;&#34;&#34;
    return self._ca_instance_type</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.client_name"><code class="name">var <span class="ident">client_name</span></code></dt>
<dd>
<div class="desc"><p>Returns client name of this instance</p>
<pre><code>Returns:
    (str) - client name as registered in the commcell
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/cloud_storage_instance.py#L230-L239" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def client_name(self):
    &#34;&#34;&#34;
    Returns client name of this instance

        Returns:
            (str) - client name as registered in the commcell

    &#34;&#34;&#34;
    return self._properties.get(&#39;instance&#39;, {}).get(&#39;clientName&#39;)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.endpointurl"><code class="name">var <span class="ident">endpointurl</span></code></dt>
<dd>
<div class="desc"><p>Returns the endpoint URL property as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/cloud_storage_instance.py#L225-L228" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def endpointurl(self):
    &#34;&#34;&#34;Returns the endpoint URL property as a read-only attribute.&#34;&#34;&#34;
    return self._endpointurl</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.google_access_key"><code class="name">var <span class="ident">google_access_key</span></code></dt>
<dd>
<div class="desc"><p>Returns google cloud account access key as read only attribute</p>
<h2 id="returns">Returns</h2>
<p>(str)
-
string representing google cloud account access key</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/cloud_storage_instance.py#L180-L188" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def google_access_key(self):
    &#34;&#34;&#34;
    Returns google cloud account access key as read only attribute

    Returns:
        (str)     -     string representing google cloud account access key
    &#34;&#34;&#34;
    return self._google_access_key</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.google_host_url"><code class="name">var <span class="ident">google_host_url</span></code></dt>
<dd>
<div class="desc"><p>Returns google cloud URL as read only attribute</p>
<h2 id="returns">Returns</h2>
<p>(str)
-
string representing host URL of goole cloud</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/cloud_storage_instance.py#L170-L178" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def google_host_url(self):
    &#34;&#34;&#34;
    Returns google cloud URL as read only attribute

    Returns:
        (str)     -     string representing host URL of goole cloud
    &#34;&#34;&#34;
    return self._google_host_url</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.host_url"><code class="name">var <span class="ident">host_url</span></code></dt>
<dd>
<div class="desc"><p>Returns the host URL property as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/cloud_storage_instance.py#L195-L198" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def host_url(self):
    &#34;&#34;&#34;Returns the host URL property as a read-only attribute.&#34;&#34;&#34;
    return self._host_url</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.server_name"><code class="name">var <span class="ident">server_name</span></code></dt>
<dd>
<div class="desc"><p>Returns the server name property as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/cloud_storage_instance.py#L215-L218" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def server_name(self):
    &#34;&#34;&#34;Returns the server name property as a read-only attribute.&#34;&#34;&#34;
    return self._server_name</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.username"><code class="name">var <span class="ident">username</span></code></dt>
<dd>
<div class="desc"><p>Returns the username property as a read-only attribute.</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/cloud_storage_instance.py#L220-L223" class="git-link">Browse git</a>
</summary>
<pre><code class="python">@property
def username(self):
    &#34;&#34;&#34;Returns the username property as a read-only attribute.&#34;&#34;&#34;
    return self._username</code></pre>
</details>
</dd>
</dl>
<h3>Methods</h3>
<dl>
<dt id="cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.restore_in_place"><code class="name flex">
<span>def <span class="ident">restore_in_place</span></span>(<span>self, paths, overwrite=True, copy_precedence=None, no_of_streams=2)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the files/folders specified in the input paths list to the same location.</p>
<h2 id="args">Args</h2>
<p>paths
(list)
&ndash;
list of full paths of files/folders to restore</p>
<p>overwrite
(bool)
&ndash;
unconditional overwrite files during restore
default: True</p>
<p>copy_precedence
(int)
&ndash;
copy precedence value of storage policy copy
default: None</p>
<p>no_of_streams
(int)
&ndash;
number of streams for restore
default : 2</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if paths is not a list</p>
<pre><code>if failed to initialize job

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/cloud_storage_instance.py#L287-L336" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_in_place(
        self,
        paths,
        overwrite=True,
        copy_precedence=None,
        no_of_streams=2):
    &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the same location.

        Args:
            paths                   (list)  --  list of full paths of files/folders to restore

            overwrite               (bool)  --  unconditional overwrite files during restore
                default: True

            copy_precedence         (int)   --  copy precedence value of storage policy copy
                default: None

            no_of_streams           (int)   --  number of streams for restore
                                                default : 2

        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:
                if paths is not a list

                if failed to initialize job

                if response is empty

                if response is not success

    &#34;&#34;&#34;

    if not (isinstance(paths, list) and
            isinstance(overwrite, bool)):
        raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

    request_json = self._generate_json(
        paths=paths,
        destination_client=self.client_name,
        destination_instance_name=self.instance_name,
        overwrite=overwrite,
        in_place=True,
        copy_precedence=copy_precedence,
        restore_To_FileSystem=False,
        no_of_streams=no_of_streams)

    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.restore_out_of_place"><code class="name flex">
<span>def <span class="ident">restore_out_of_place</span></span>(<span>self, paths, destination_client, destination_instance_name, destination_path, overwrite=True, copy_precedence=None, no_of_streams=2, **kwargs)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the files/folders specified in the input paths list to the input client,
at the specified destination location.</p>
<h2 id="args">Args</h2>
<p>paths
(list)
&ndash;
list of full paths of files/folders to restore</p>
<p>destination_client
(str)
&ndash;
name of the client to which the files
are to be restored.</p>
<p>destination_instance_name(str)
&ndash;
name of the instance to which the files
are to be restored.</p>
<p>destination_path
(str)
&ndash;
location where the files are to be restored
in the destination instance.</p>
<p>overwrite
(bool)
&ndash;
unconditional overwrite files during restore
default: True</p>
<p>copy_precedence
(int)
&ndash;
copy precedence value of storage policy copy
default: None</p>
<p>no_of_streams
(int)
&ndash;
number of streams for restore
default : 2</p>
<p>kwargs
(dict)
&ndash; dict of keyword arguments as follows</p>
<pre><code> from_time           (str)   --  time to retore the contents after
     format: YYYY-MM-DD HH:MM:SS
     default: None

 to_time             (str)   --  time to retore the contents before
     format: YYYY-MM-DD HH:MM:SS
     default: None

 no_image            (bool)  --  restore deleted items
     default: False
</code></pre>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if client is not a string or Client object</p>
<pre><code>if destination_path is not a string

if paths is not a list

if failed to initialize job

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/cloud_storage_instance.py#L338-L424" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_out_of_place(
        self,
        paths,
        destination_client,
        destination_instance_name,
        destination_path,
        overwrite=True,
        copy_precedence=None,
        no_of_streams=2,
        **kwargs):
    &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the input client,
        at the specified destination location.

        Args:
            paths                    (list)  --  list of full paths of files/folders to restore

            destination_client       (str)   --  name of the client to which the files
                are to be restored.

            destination_instance_name(str)   --  name of the instance to which the files
                are to be restored.

            destination_path         (str)   --  location where the files are to be restored
                in the destination instance.

            overwrite                (bool)  --  unconditional overwrite files during restore
                default: True

            copy_precedence          (int)   --  copy precedence value of storage policy copy
                default: None

            no_of_streams           (int)   --  number of streams for restore
                                                default : 2

           kwargs                  (dict)  -- dict of keyword arguments as follows

                from_time           (str)   --  time to retore the contents after
                    format: YYYY-MM-DD HH:MM:SS
                    default: None

                to_time             (str)   --  time to retore the contents before
                    format: YYYY-MM-DD HH:MM:SS
                    default: None

                no_image            (bool)  --  restore deleted items
                    default: False

        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:
                if client is not a string or Client object

                if destination_path is not a string

                if paths is not a list

                if failed to initialize job

                if response is empty

                if response is not success

    &#34;&#34;&#34;

    if not ((isinstance(destination_client, str) or
             isinstance(destination_client, Client)) and
            isinstance(destination_instance_name, str) and
            isinstance(destination_path, str) and
            isinstance(paths, list) and
            isinstance(overwrite, bool)):
        raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

    request_json = self._generate_json(
        paths=paths,
        destination_client=destination_client,
        destination_instance_name=destination_instance_name,
        destination_path=destination_path,
        overwrite=overwrite,
        in_place=False,
        copy_precedence=copy_precedence,
        no_of_streams=no_of_streams,
        restore_To_FileSystem=False,
        **kwargs)

    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.restore_to_fs"><code class="name flex">
<span>def <span class="ident">restore_to_fs</span></span>(<span>self, paths, destination_path, destination_client=None, overwrite=True, copy_precedence=None, no_of_streams=2)</span>
</code></dt>
<dd>
<div class="desc"><p>Restores the files/folders specified in the input paths list to the input client,
at the specified destination location.</p>
<h2 id="args">Args</h2>
<p>paths
(list)
&ndash;
list of full paths of files/folders to restore</p>
<p>destination_path
(str)
&ndash;
location where the files are to be restored
in the destination instance.</p>
<p>destination_client
(str)
&ndash;
name of the fs client to which the files
are to be restored.
default: None for restores to backup or proxy client.</p>
<p>overwrite
(bool)
&ndash;
unconditional overwrite files during restore
default: True</p>
<p>copy_precedence
(int)
&ndash;
copy precedence value of storage policy copy
default: None</p>
<p>no_of_streams
(int)
&ndash;
number of streams for restore
default : 2</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:
if client is not a string or client object</p>
<pre><code>if destination_path is not a string

if paths is not a list

if failed to initialize job

if response is empty

if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/cloud_storage_instance.py#L426-L496" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_to_fs(
        self,
        paths,
        destination_path,
        destination_client=None,
        overwrite=True,
        copy_precedence=None,
        no_of_streams=2):
    &#34;&#34;&#34;Restores the files/folders specified in the input paths list to the input client,
        at the specified destination location.

        Args:
            paths                   (list)  --  list of full paths of files/folders to restore

            destination_path        (str)   --  location where the files are to be restored
                in the destination instance.

            destination_client      (str)   --  name of the fs client to which the files
                are to be restored.
                default: None for restores to backup or proxy client.

            overwrite               (bool)  --  unconditional overwrite files during restore
                default: True

            copy_precedence         (int)   --  copy precedence value of storage policy copy
                default: None

            no_of_streams           (int)   --  number of streams for restore
                                                default : 2

        Returns:
            object - instance of the Job class for this restore job

        Raises:
            SDKException:
                if client is not a string or client object

                if destination_path is not a string

                if paths is not a list

                if failed to initialize job

                if response is empty

                if response is not success

    &#34;&#34;&#34;

    if not ((isinstance(destination_client, str) or
             isinstance(destination_client, Client)) and
            isinstance(destination_path, str) and
            isinstance(paths, list) and
            isinstance(overwrite, bool)):
        raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

    destination_appTypeId = int(
        self._commcell_object.clients.get(destination_client).agents.get(&#39;file system&#39;).agent_id)

    request_json = self._generate_json(
        paths=paths,
        destination_path=destination_path,
        destination_client=destination_client,
        overwrite=overwrite,
        in_place=False,
        copy_precedence=copy_precedence,
        restore_To_FileSystem=True,
        no_of_streams=no_of_streams,
        destination_appTypeId=destination_appTypeId)

    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
<dt id="cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.restore_using_proxy"><code class="name flex">
<span>def <span class="ident">restore_using_proxy</span></span>(<span>self, paths, destination_client_proxy, destination_path, overwrite=True, copy_precedence=None, destination_cloud=None)</span>
</code></dt>
<dd>
<div class="desc"><p>To perform restore to different cloud using
proxy passing explicit credentials of destination cloud</p>
<h2 id="args">Args</h2>
<p>destination_client_proxy (str)
&ndash;
name of proxy machine having cloud connector package</p>
<p>paths
(list)
&ndash;
list of full paths of files/folders to restore</p>
<p>destination_path
(str)
&ndash;
location where the files are to be restored
in the destination instance.</p>
<p>overwrite
(bool)
&ndash;
unconditional overwrite files during restore
default: True</p>
<p>copy_precedence
(int)
&ndash;
copy precedence value of storage policy copy
default: None</p>
<p>destination_cloud
(dict(dict))
&ndash;
dict of dict representing cross cloud credentials</p>
<p>Sample dict(dict) :</p>
<p>destination_cloud = {
'google_cloud': {
'google_host_url':'storage.googleapis.com',
'google_access_key':'xxxxxx',
'google_secret_key':'yyyyyy'
}
}</p>
<p>destination_cloud = {
'amazon_s3':
{
's3_host_url':'s3.amazonaws.com',
's3_access_key':'xxxxxx',
's3_secret_key':'yyyyyy'
}
}
destination_cloud = {
'azure_blob':
{
'azure_host_url':'blob.core.windows.net',
'azure_account_name':'xxxxxx',
'azure_access_key':'yyyyyy'
}
}</p>
<h2 id="returns">Returns</h2>
<p>object - instance of the Job class for this restore job</p>
<h2 id="raises">Raises</h2>
<p>SDKException:</p>
<pre><code>    if destination cloud credentials empty

    if destination cloud has more than one vendor details

    if unsupported destination cloud for restore is chosen

    if client is not a string or Client object

    if destination_path is not a string

    if paths is not a list

    if failed to initialize job

    if response is empty

    if response is not success
</code></pre></div>
<details class="source">
<summary>
<span>Expand source code</span>
<a href="https://github.com/Commvault/cvpysdk/blob/8dccc75ff7e6a20e27c0e0820ae8e40a8208806b/cvpysdk/instances/cloudapps/cloud_storage_instance.py#L673-L802" class="git-link">Browse git</a>
</summary>
<pre><code class="python">def restore_using_proxy(self,
                        paths,
                        destination_client_proxy,
                        destination_path,
                        overwrite=True,
                        copy_precedence=None,
                        destination_cloud=None):
    &#34;&#34;&#34;
    To perform restore to different cloud using
    proxy passing explicit credentials of destination cloud

    Args:
        destination_client_proxy (str)          --  name of proxy machine having cloud connector package

        paths                    (list)         --  list of full paths of files/folders to restore

        destination_path         (str)          --  location where the files are to be restored
                                                    in the destination instance.

        overwrite                (bool)         --  unconditional overwrite files during restore
                                                    default: True

        copy_precedence          (int)          --  copy precedence value of storage policy copy
                                                    default: None


        destination_cloud        (dict(dict))  --     dict of dict representing cross cloud credentials

        Sample dict(dict) :

        destination_cloud = {
                                &#39;google_cloud&#39;: {
                                                    &#39;google_host_url&#39;:&#39;storage.googleapis.com&#39;,
                                                    &#39;google_access_key&#39;:&#39;xxxxxx&#39;,
                                                    &#39;google_secret_key&#39;:&#39;yyyyyy&#39;
                                                }
                            }

        destination_cloud = {
                                &#39;amazon_s3&#39;:    {
                                                    &#39;s3_host_url&#39;:&#39;s3.amazonaws.com&#39;,
                                                    &#39;s3_access_key&#39;:&#39;xxxxxx&#39;,
                                                    &#39;s3_secret_key&#39;:&#39;yyyyyy&#39;
                                                }
                            }
        destination_cloud = {
                                &#39;azure_blob&#39;:   {
                                                    &#39;azure_host_url&#39;:&#39;blob.core.windows.net&#39;,
                                                    &#39;azure_account_name&#39;:&#39;xxxxxx&#39;,
                                                    &#39;azure_access_key&#39;:&#39;yyyyyy&#39;
                                                }
                            }


    Returns:
            object - instance of the Job class for this restore job

    Raises:
        SDKException:

                if destination cloud credentials empty

                if destination cloud has more than one vendor details

                if unsupported destination cloud for restore is chosen

                if client is not a string or Client object

                if destination_path is not a string

                if paths is not a list

                if failed to initialize job

                if response is empty

                if response is not success

    &#34;&#34;&#34;
    # Check if destination cloud credentials are empty
    if destination_cloud is None:
        raise SDKException(
            &#39;Instance&#39;,
            &#39;102&#39;,
            &#39;Destination Cloud Credentials empty&#39;)

    if len(destination_cloud) &gt; 1:
        raise SDKException(
            &#39;Instance&#39;, &#39;102&#39;, &#39;only one cloud vendor details can&#39;
                               &#39;be passed.Multiple entries not allowed&#39;)

    cloud_vendors = [&#34;google_cloud&#34;, &#34;amazon_s3&#34;, &#34;azure_blob&#34;]
    # Check if destination cloud falls within supported cloud vendors
    cloud_vendors = [&#34;google_cloud&#34;, &#34;amazon_s3&#34;, &#34;azure_blob&#34;]
    # Check if destination cloud falls within supported cloud vendors
    dict_keys = list(destination_cloud.keys())
    if dict_keys[0] not in cloud_vendors:
        raise SDKException(
            &#39;Instance&#39;,
            &#39;102&#39;,
            &#39;Unsupported destination cloud for restore&#39;)

    if not ((isinstance(destination_client_proxy, str) or
             isinstance(destination_client_proxy, Client)) and
            isinstance(destination_path, str) and
            isinstance(paths, list) and
            isinstance(overwrite, bool)):
        raise SDKException(&#39;Instance&#39;, &#39;101&#39;)

    request_json = self._generate_json(
        paths=paths,
        destination_proxy=True,
        destination_client=destination_client_proxy,
        destination_instance_name=None,
        destination_path=destination_path,
        overwrite=overwrite,
        in_place=False,
        copy_precedence=copy_precedence,
        restore_To_FileSystem=False)
    self._set_proxy_credential_json(destination_cloud)
    request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][
        &#34;restoreOptions&#34;][&#34;cloudAppsRestoreOptions&#34;][&#34;cloudStorageRestoreOptions&#34;][
        &#34;restoreDestination&#34;] = self._proxy_credential_json
    request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
        &#34;cloudAppsRestoreOptions&#34;][&#34;cloudStorageRestoreOptions&#34;][&#34;overrideCloudLogin&#34;] = True
    request_json[&#34;taskInfo&#34;][&#34;subTasks&#34;][0][&#34;options&#34;][&#34;restoreOptions&#34;][
        &#34;browseOption&#34;][&#34;backupset&#34;].update({&#34;backupsetName&#34;: &#34;defaultBackupSet&#34;})
    request_json[&#34;taskInfo&#34;][&#34;associations&#34;][0][&#34;backupsetName&#34;] = &#34;defaultBackupSet&#34;

    return self._process_restore_response(request_json)</code></pre>
</details>
</dd>
</dl>
<h3>Inherited members</h3>
<ul class="hlist">
<li><code><b><a title="cvpysdk.instances.cainstance.CloudAppsInstance" href="../cainstance.html#cvpysdk.instances.cainstance.CloudAppsInstance">CloudAppsInstance</a></b></code>:
<ul class="hlist">
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.backupsets" href="../../instance.html#cvpysdk.instance.Instance.backupsets">backupsets</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.browse" href="../../instance.html#cvpysdk.instance.Instance.browse">browse</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.credentials" href="../../instance.html#cvpysdk.instance.Instance.credentials">credentials</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.find" href="../../instance.html#cvpysdk.instance.Instance.find">find</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.instance_id" href="../../instance.html#cvpysdk.instance.Instance.instance_id">instance_id</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.instance_name" href="../../instance.html#cvpysdk.instance.Instance.instance_name">instance_name</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.name" href="../../instance.html#cvpysdk.instance.Instance.name">name</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.properties" href="../../instance.html#cvpysdk.instance.Instance.properties">properties</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.refresh" href="../../instance.html#cvpysdk.instance.Instance.refresh">refresh</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.subclients" href="../../instance.html#cvpysdk.instance.Instance.subclients">subclients</a></code></li>
<li><code><a title="cvpysdk.instances.cainstance.CloudAppsInstance.update_properties" href="../../instance.html#cvpysdk.instance.Instance.update_properties">update_properties</a></code></li>
</ul>
</li>
</ul>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<header>
<a class="homelink" rel="home" title="GitHub home" href="https://github.com/Commvault/cvpysdk">
<img src="https://commvault.github.io/cvpysdk/logo.png" alt="" width="100%"> Commvault Python SDK
</a> <br>
<a class="homelink" title="Go to Top Level" href="https://commvault.github.io/cvpysdk/cvpysdk/index.html"> Go to Top Level </a>
</header>
<form>
<input id="lunr-search" name="q" placeholder="🔎 Search ..." aria-label="Search"
disabled minlength="2">
</form>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.css" integrity="sha512-j1u8eUJ4f23xPPxwOrLUPQaCD2dwzNqqmDDcWS4deWsMv2ohLqmXXuP3hU7g8TyzbMSakP/mMqoNBYWj8AEIFg==" crossorigin>
<script src="https://cdnjs.cloudflare.com/ajax/libs/tingle/0.15.3/tingle.min.js" integrity="sha512-plGUER9JkeEWPPqQBE4sdLqBoQug5Ap+BCGMc7bJ8BXkm+VVj6QzkpBz5Yv2yPkkq+cqg9IpkBaGCas6uDbW8g==" crossorigin></script>
<style>
.modal-dialog iframe {
width: 100vw;
height: calc(100vh - 80px);
}
@media screen and (min-width: 700px) {
.modal-dialog iframe {
width: 70vw;
height: 80vh;
}
}
.modal-dialog .tingle-modal-box {width: auto;}
.modal-dialog .tingle-modal-box__content {padding: 0;}
</style>
<script>
const input = document.getElementById('lunr-search');
input.disabled = false;
input.form.addEventListener('submit', (ev) => {
ev.preventDefault();
const url = new URL(window.location);
url.searchParams.set('q', input.value);
history.replaceState({}, null, url.toString());
search(input.value);
});
const query = new URL(window.location).searchParams.get('q');
if (query)
search(query);
function search(query) {
const url = '../../../doc-search.html#' + encodeURIComponent(query);
new tingle.modal({
cssClass: ['modal-dialog'],
onClose: () => {
const url = new URL(window.location);
url.searchParams.delete('q');
history.replaceState({}, null, url.toString());
setTimeout(() => input.focus(), 100);
}
}).setContent('<iframe src="' + url + '"></iframe>').open();
}
</script>
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="cvpysdk.instances.cloudapps" href="index.html">cvpysdk.instances.cloudapps</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance" href="#cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance">CloudStorageInstance</a></code></h4>
<ul class="">
<li><code><a title="cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.access_key" href="#cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.access_key">access_key</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.access_keyid" href="#cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.access_keyid">access_keyid</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.access_node" href="#cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.access_node">access_node</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.account_name" href="#cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.account_name">account_name</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.ca_instance_type" href="#cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.ca_instance_type">ca_instance_type</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.client_name" href="#cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.client_name">client_name</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.endpointurl" href="#cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.endpointurl">endpointurl</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.google_access_key" href="#cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.google_access_key">google_access_key</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.google_host_url" href="#cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.google_host_url">google_host_url</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.host_url" href="#cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.host_url">host_url</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.restore_in_place" href="#cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.restore_in_place">restore_in_place</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.restore_out_of_place" href="#cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.restore_out_of_place">restore_out_of_place</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.restore_to_fs" href="#cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.restore_to_fs">restore_to_fs</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.restore_using_proxy" href="#cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.restore_using_proxy">restore_using_proxy</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.server_name" href="#cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.server_name">server_name</a></code></li>
<li><code><a title="cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.username" href="#cvpysdk.instances.cloudapps.cloud_storage_instance.CloudStorageInstance.username">username</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc" title="pdoc: Python API documentation generator"><cite>pdoc</cite> 0.10.0</a>.</p>
</footer>
</body>
</html>